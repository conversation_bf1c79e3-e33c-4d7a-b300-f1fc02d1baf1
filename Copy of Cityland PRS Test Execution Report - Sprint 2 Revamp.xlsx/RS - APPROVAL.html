<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1150944411C0" style="width:90px;" class="column-headers-background">A</th><th id="1150944411C1" style="width:219px;" class="column-headers-background">B</th><th id="1150944411C2" style="width:61px;" class="column-headers-background">C</th><th id="1150944411C3" style="width:252px;" class="column-headers-background">D</th><th id="1150944411C4" style="width:233px;" class="column-headers-background">E</th><th id="1150944411C5" style="width:330px;" class="column-headers-background">F</th><th id="1150944411C7" style="width:258px;" class="column-headers-background">H</th><th id="1150944411C8" style="width:100px;" class="column-headers-background">I</th><th id="1150944411C9" style="width:164px;" class="column-headers-background">J</th><th id="1150944411C10" style="width:245px;" class="column-headers-background">K</th><th id="1150944411C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="1150944411R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s2">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1150944411R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="11">RS APPROVAL - Allow deletion of Requested Item by the Approvers during Approval</td></tr><tr style="height: 19px"><th id="1150944411R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">PRS-1403-011</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Critical</td><td class="s7">Verify if removing an item doesn&#39;t repeat the Approval process</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Number with a Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot; </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 1 approver<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Number is </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table , then approve<br>4. Validate that the item is removed and it is approved<br>5. Login as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 2 approver<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">6. Validate that the approval process </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">continues</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">, and the removal of item </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">doesn&#39;t repeat </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">the Approval process, meaning it does not go back to Level 1 Approver<br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed and the process was approved<br>6. The approval process continued, and the removal of item did not repeat the Approval process, meaning it did not go back to Level 1 Approver<br></td><td class="s8" rowspan="9"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1J9APOX-Qmkql_aMON4rvopmG9rH4AesktSh26jUB1_U/edit?usp=sharing">Regel_Sprint1_Test Result</a></td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">PRS-1403-012</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Critical</td><td class="s7">Verify if the next Approver can approve the remaining items after an item is removed</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Number with a Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot; </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 1 approver<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Number is </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table , then approve<br>4. Validate that the item is removed and it is approved<br>5. Login as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 2 approver<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">6. Approve the remaining items<br>7. Validate that the Level 2 approver can approve the remaining items after an item is removed<br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed and the process was approved<br>7.  The Level 2 approver can approve the remaining items after an item is removed<br><br></td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">PRS-1403-013</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">High</td><td class="s7">Verify if changes are tracked in the RS History and Audit Logs after an item is removed</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><br>1. Navigate to the RS History and Audit Logs<br>2. Validate if the changes are tracked in the RS History and Audit Logs after an item is removed</td><td class="s7">2. The changes are tracked in the RS History and Audit Logs after an item is removed</td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">PRS-049-014</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Critical</td><td class="s7">Verify if all levels of RS Approvers (e.g., Lv1 to Lv5) can remove items as long as RS Items &gt; 1</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Number with a Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot; </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 1 approver<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Number is </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table , then approve<br>4. Validate that the item is removed and it is approved<br>5. Repeat steps 1 to 4 as the </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">next approvers (Level 2 to 5) respectively, </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">until one RS item remains<br>6. Validate that each level of Approver can remove items as long as more than 1 item remains<br><br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed and the process was approved<br>6. Each level of Approver can remove items as long as more than 1 item remains<br><br><br></td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-049-015</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Critical</td><td class="s10">Attempt to remove the only remaining item after series of removal as the next/last approver</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Number with a Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot; </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 1 approver<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Number is </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table , then approve<br>4. Validate that the item is removed and it is approved<br>5. Repeat steps 1 to 4 as the </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">next approvers (Level 2 to 4) respectively, </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">until one RS item remains<br>6. Login as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Level 5 approver, </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> attempt to remove </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">the only remaining item<br>7. Validate that the item cannot be removed as the remove icon is disabled when number of item is equal to 1<br><br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed and the process was approved<br>6. The item cannot be removed as the remove icon is disabled when number of item is equal to 1<br><br><br><br></td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4">PRS-049-016</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Critical</td><td class="s7">Verify if an added Approver during the RS Approval process can delete items</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Number with a Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot; </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">as </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Added Approver During Approval<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Number is </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table<br>4. Validate that the item is removed successfully as the added approver during approval<br><br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed successfully as the added approver during approval<br></td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4">PRS-049-017</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Critical</td><td class="s7">Verify if item removal is allowed for different types of Requests</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Numbers with Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot;</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> for all the</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> different types of requests<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Numbers are </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an Item for </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">each type of requests<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">4. Validate that the Item removal is </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">allowed </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">consistently across all Types of Requests and the functionality is not limited by request classification<br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. Items were removed successfully on different types of requests<br>6. The Item removal was allowed consistently across all Types of Requests and the functionality was not limited by request classification<br></td><td class="s9">Not Started</td><td class="s4">4/25: Blocked due to Unable to remove item</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547">4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547</a></td></tr><tr style="height: 19px"><th id="1150944411R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s4">PRS-049-018</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">Minor</td><td class="s7">Verify if the application displays a success message after an item is successfully removed</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Numbers with Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot;<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Numbers are </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table<br>4. Validate that the Item is removed successfully and the application displays a success message after an item is successfully removed<br></span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed successfully and the application displays a success message after an item is successfully removed<br><br></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s4">PRS-049-019</td><td class="s5">Allow deletion of Requested Item by the Approvers during Approval</td><td class="s6">High</td><td class="s7">Verify if the total number of item is automatically updated after an item is removed</td><td class="s7">1. Requisition Slip is submitted<br>2. Logged in User is an Approver for a Requisition Slip</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>1. Click RS Numbers with Status of </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;For RS Approval&quot;<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Validate if the RS Numbers are </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">and the user is redirected to the Requisition Slip Page<br>3. Remove an item from the Items Table<br>4. Validate that the Item is removed successfully and that the total number of items is automatically updated after an item is removed</span></td><td class="s7">2. The RS Number was clickable and the user was redirected to the Requisition Slip Page<br>4. The item was removed successfully and  the total number of items was automatically updated after an item was removed<br><br><br></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s3" colspan="11">RS APPROVAL - Allow Adding of Notes before Approval of RS</td></tr><tr style="height: 19px"><th id="1150944411R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s7">PRS-1396-001</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">Critical</td><td class="s7">Verify that a Requisition Slip can be submitted for Approval</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Create a new Requisition Slip or open a drafted one.<br>2. Click the &quot;Submit&quot; button.</td><td class="s7">2. Requisition Slip is successfully submitted and ready for approval.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4">4/25: Out of scope due to spillover to sprint2</td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s7">PRS-1396-002</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">Minor</td><td class="s7">Verify Requisition Slip No. redirection.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Navigate to For My Approval/Assigned Tab in Dashboard<br>2. Click the RS No. hyperlink of submitted Requisition Slip</td><td class="s7">2. Requisition Slip details page is displayed.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s7">PRS-1396-003</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">High</td><td class="s7">Verify that a floating Confirmation Message appears when initiating approval.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Click on the &quot;Approve&quot; button</td><td class="s7">1. A floating confirmation message for approval is displayed.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1150944411R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s7">PRS-1396-004</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">Crtitical</td><td class="s7">Verify that clicking Approve displays a Confirmation Modal with Notes field.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Click on the &quot;Approve&quot; button<br>2. Observe the modal</td><td class="s7">2. A confirmation modal is shown and displayed the following field and buttons:<br>     - Notes<br>     - Add Approver<br>     - Continue<br>     - Cancel</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4">4/25 No Notes appeared</td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s7">PRS-1396-005</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">High</td><td class="s7">Verify that Notes field accepts alphanumeric and special characters.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Type a valid note with alphanumeric and special characters.<br>2. Click &quot;Continue&quot; button.</td><td class="s7">2. Note is accepted and approval proceeds.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s7">PRS-1396-006</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">Minor</td><td class="s10">Verify that Notes field does not accept emojis.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Enter emojis in the Notes field.<br>2. Click &quot;Continue&quot; button.</td><td class="s7">2. Error message and approval does not proceed.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s7">PRS-1396-007</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">High</td><td class="s7">Verify that Notes field accepts input up to 100 characters.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Enter exactly 100 characters in the Notes field.<br>2. Click &quot;Continue&quot; button.</td><td class="s7">2. Note is accepted and approval proceeds.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s7">PRS-1396-008</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">Minor</td><td class="s10">Verify that Notes field does not accept more than 100 characters.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Enter more than 100 characters in the Notes field.<br>2. Attempt to click &quot;Continue&quot; button.</td><td class="s7">2. System prevents entry exceeding 100 characters.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s7">PRS-1396-009</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">High</td><td class="s7">Verify that Notes field is optional.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Leave the Notes field blank.<br>2. Click &quot;Continue&quot; button.</td><td class="s7">2. Approval proceeds without error.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s7">PRS-1396-010</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">High</td><td class="s7">Validate Cancel button functionality.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Click on &quot;Cancel&quot; button.</td><td class="s7">1. Modal closes and user is returned to Requisition Slip detail page.</td><td class="s11"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1150944411R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s7">PRS-1396-011</td><td class="s5"><br>Allow Adding of Notes before Approval of RS</td><td class="s7">High</td><td class="s7">Verify that entered Notes are displayed under the Check Notes button after Approval.</td><td class="s7">1. User is an RS Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s7">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Notes” of the approved Requisition Slip.<br>3. Verify visibility of approver.<br>4. Verify the badge is cleared.</td><td class="s7">1. “New Attachment” badge is displayed.<br>2. Entered Approval Notes are displayed correctly.<br>3. Approver name should be displayed correctly.<br>4. “New Attachment” badge is cleared when viewed.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s14" colspan="11">RS APPROVAL - Update Adding of Additional Approver before Approval of RS</td></tr><tr style="height: 19px"><th id="1150944411R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s4">1314-001</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">Critical</td><td class="s7">Verify Add Button in Approvers</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval</td><td class="s4">1. Select RS Number<br>2. Navigate to Approvers Sections<br>3. Click Add button<br>4. Search for a User or view User list<br>5. Select a user<br>6. Click Add Approver</td><td class="s4">1. Should display the Request Details, Requested Items, and Approver<br>2. Should display and click Add Button in the Approvers Section<br>3. Should display a Modal that will allow adding of an Approver<br>    a. Displays the message: &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>    b. Should display a Search User Field<br>4. Should display Users with a User Types of<br>    a. Supervisor<br>    b. Assistant Manager<br>    c. Department Head<br>    d. Division Head<br>    e. Area Staff/Department Secretary<br>5. Should be displayed in the field<br>6. Additional Approver should be displayed below the current Approver with a * as their Label</td><td class="s4" rowspan="4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s4">1314-002</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">Critical</td><td class="s7">Verify Behavior After Adding Additional Approver</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. Additional Approver has been added</td><td class="s4">1. Select RS Number<br>2. Validate Approvers section<br>3. Edit Additional Approver and Update Approver<br>4. Delete Approver</td><td class="s4">2. Additional Approver should be displayed below the current Approver with a * as their Label<br>    a. Added Approver should be reflected to the List of Approvers once added<br>        i. Will not need to wait for Approval for the User to be added as an Additional Approver<br>    b. Should notify the tagged Additional Approver through the Notification Bell<br>    c. Should allow the Default Approver to Edit or Delete the Added Approver until they haven&#39;t Approve the Requisition Slip<br>3. Additional Approver should update and display the name of the new Approver<br>4. Additional Approver should be removed from the Approvers section</td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s4">1314-003</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">High</td><td class="s7">Verify Additional Approver Notification</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. Additional Approver has been added<br>4. Login as Additional Approver</td><td class="s4">1. View Notification Bell<br>2. Click Notification Bell and observe notification</td><td class="s4">1. Notification bell count should update<br>2. Notification should be displayed following the template below:<br>&quot;Title: <br>Assigned as an Additional Approver<br><br>Content:<br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024&quot;</td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s4">1314-004</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">Critical</td><td class="s7">Verify if Additional Approver can Approve Before Current Approver</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. Additional Approver has been added<br>4. Current Approver has not approved yet<br>5. Login as Additional Approver</td><td class="s4">1. Select RS Number<br>2. Observe if approve sticky note is displayed</td><td class="s4">2. Should require the current Approver to Approve the Requisition Slip before the Additional Approver can Approve<br>    a. Should require the Additional Approver to Approve the Requisition Slip before allowing to continue on the next Level of Approve</td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s4">1314-005</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">Critical</td><td class="s7">Verify Add Approver Button in Approve Modal When No Additional Approvers have been Added</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. No Additional Approver has been added</td><td class="s4">1. Select RS Number<br>2. Click Approve button<br>3. Click Add Approver button<br>4. Search for a User or view User list<br>5. Select a user<br>6. Click Add &amp; Submit</td><td class="s4">2. If an Additional Approver is not yet Added, should allow adding of Approver when Approving the Requisition Slip<br>    a. Should only display the Add Approver Button in the Confirmation Message if the Additional Approver is not yet indicated<br>3. Should display a Modal that will allow adding of an Approver<br>    a. Displays the message: &quot;You are about to add an approver. Please select your designated approver and press “Add &amp; Submit” if you want to proceed with this action and submit your approval.&quot;<br>    b. Should display a Search User Field<br>4. Should display Users with a User Types of<br>    a. Supervisor<br>    b. Assistant Manager<br>    c. Department Head<br>    d. Division Head<br>    e. Area Staff/Department Secretary<br>5. Should be displayed in the field<br>6. Additional Approver should be displayed below the current Approver with a * as their Label and Current Approver status should be Approved</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s4">1314-006</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">High</td><td class="s10">Verify Adding Additional Approver After Deletion</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. Additional Approver has been added<br>4. Current Approver has not approved yet</td><td class="s4">1. Select RS Number<br>2. Validate Approvers section<br>3. Delete Approver<br>4. Click Add button<br>5. Select a different user as approver</td><td class="s4">3. Approver should be removed from Approvers section<br>4. Should be able to add a new additional approver<br>5. Should be added successfully into Approvers section</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s4">1314-007</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">High</td><td class="s10">Verify Adding Additional Approver After Deletion and RS already approved by the current approver</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. Additional Approver has been added<br>4. Current Approver has already approved the RS</td><td class="s4">1. Select RS Number<br>2. Validate Approvers section<br>3. Delete Approver<br>4.Check Add Button</td><td class="s4">3. Approver should be removed from Approvers section<br>4. Add button should no longer visible</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s4">1314-008</td><td class="s5">Update Adding of Additional Approver before Approval of RS</td><td class="s15">High</td><td class="s7">Verify Adding Additional Approver if Additional Approver has already been Added</td><td class="s4">1. User is an RS Approver and the current Approver<br>2. A Requisition Slip has been submitted for Approval<br>3. Additional Approver has been added<br>4. Current Approver has not approved yet</td><td class="s4">1. Select RS Number<br>2. Validate Approvers section</td><td class="s4">2. Add button should be removed from Approvers section if an additional approver has already been added</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s3" colspan="11">RS APPROVAL - Enabling RS Optional Approver</td></tr><tr style="height: 19px"><th id="1150944411R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s7">1492-001</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">Critical</td><td class="s7">Verify Optional Approver for Department Approvers and Project Approvers</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open assigned Requisition Slip<br>2. Navigate to Approval Setup<br>3. Verify Optional Approver for Department Approvers and Project Approvers</td><td class="s7">4. Optional Approver should be available for both Department and Project Approvers.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s7">1492-002</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Quantity Exceeds 80% of Remaining GFQ on Submission</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open assigned Requisition Slip<br>2. Observe item/s with Quantity is greater than 80% of the Item&#39;s Remaining GFQ</td><td class="s7">4. Item with Quantity is greater than 80% of the Item&#39;s Remaining GFQ is displayed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s7">1492-003</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Optional Approver for Department and Project Approvers as the Last Approver</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open assigned Requisition Slip<br>2. Observe list of Approvers</td><td class="s7">4. Optional Approver is the Last Approver</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1150944411R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s7">1492-004</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Optional Approver for Department and Project Approvers is Required</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open Requisition Slip<br>2. Remove or do not Assign Optional Approver</td><td class="s7">4. Error Message appears Optional Approver is Required</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s7">1492-005</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">Critical</td><td class="s7">Verify Cut off approval process if Optional Approver is not yet assigned</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open Requisition Slip<br>2. Observe Approval Process with no Optional Approval assigned</td><td class="s7">4. Approval process is cut off</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s7">1492-006</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">Critical</td><td class="s7">Verify Optional Approver Assigned is cascaded to all of the Requisition Slips that need Approval</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open assigned Requisition Slip<br>2. Observe Approval Process for Requisition Slips with assigned Optional Approval </td><td class="s7">4. Assigned Optional Approver is cascaded to Requisition Slips that need approval </td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s7">1492-007</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Optional Approver can Approve, Reject, Edit the Request, and Add an Additional Approver</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open assigned Requisition Slip<br>2. Verify if Optional Approver can:<br>-Approve<br>-Reject<br>-Edit the Request<br>-Add an Addtional Approver</td><td class="s7">4. Optional Approver should be able to:<br>-Approve<br>-Reject<br>-Edit the Request<br>-Add an Addtional Approver</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s7">1492-008</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Optional Approver is required once Item&#39;s Quantity updated to more than 80% of Remaining GFQ during Approval</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Verify Item&#39;s Quantity has been Updated by an Approver to more than 80% of Remaining GFQ</td><td class="s7">4. Optional Approver should be Required</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s7">1492-009</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify system display  &quot;---&quot; if Optional Approver is not yet Assigned</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Open Requisition Slip<br>2. Observe Approval Process with no Optional Approval assigned</td><td class="s7">4. System should display  &quot;---&quot; if Optional Approver is not yet Assigned</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s7">1492-010</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">Cirtical</td><td class="s7">Verify if system only allow Optional Approver removal before their approval if Item Quantity drops below 80% of Latest Remaining GFQ</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Navigate to the Item approval page.<br>2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ<br>3. Obvserve  Optional Approver while Quantity is above 80%.</td><td class="s7">4. Optional Approver should be available when current approver when Quantity is above 80% of the Latest Remaining GFQ.<br>5. Optional Approver should be automatically removed once current approver approved less than 80% of the Latest Remaining GFQ.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s7">1492-011</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">Critical</td><td class="s7">Verify If the one Optional Approver has already Approved and the Item&#39;s Quantity became less than 80% of the Item&#39;s GFQ, should not allow the removal of the Optional Approver<br></td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Navigate to the Item approval page.<br>2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ<br>3. Approve the Requisition Slip.<br>4. After approval, reduce Item Quantity further to 70 units (well below 80%).<br>5. Observe Optional Approver after they have approved and Quantity is below 80%.</td><td class="s7">4. Optional Approver should be automatically removed once current approver approved less than 80% of the Latest Remaining GFQ.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s7">1492-012</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Correct Optional Approver for Department is Added to RS</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Navigate to the Item approval page.<br>2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ<br>3. Obvserve Optional Approver for Department while Quantity is above 80%.</td><td class="s7">4. The optional approver must match the Department added in Requesition Slip.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s7">1492-013</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Correct Optional Approver for Project is Added to RS</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Navigate to the Item approval page.<br>2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ<br>3. Obvserve Optional Approver for Project while Quantity is above 80%.</td><td class="s7">4. The optional approver must match the Project added in Requesition Slip.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s7">1492-014</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Optional Approver from Department Only when Added as Category</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Log in as requester and create an RS under Department category, with Item assigned to Department. Quantity is set below 80% of remaining GFQ.<br>2. Log in as the assigned Approver and Edit Item&#39;s quantity during approval to exceed 80% of remaining GFQ.<br>3. Confirm that no optional Project approver is added.</td><td class="s7">4. System adds the optional Department approver to the approval flow.<br>5. Only Department optional approver is present in the routing.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s7">1492-015</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">High</td><td class="s7">Verify Optional Approver from Project Only when Added as Category</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Log in as requester and create an RS under Project category, with Item assigned to Project. Quantity is set below 80% of remaining GFQ.<br>2. Log in as the assigned Approver and Edit Item&#39;s quantity during approval to exceed 80% of remaining GFQ.<br>3. Confirm that no optional Department approver is added.</td><td class="s7">4. System adds the optional Department approver to the approval flow.<br>5. Only Project optional approver is present in the routing.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1150944411R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s7">1492-016</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS APPROVAL</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Enabling RS Optional Approver</span></td><td class="s7">Critical</td><td class="s7">Verify if Add Department or Project Optional Approvers When Both Are Used in RS</td><td class="s7">1. User is an Optional RS Approver<br>2. A Requisition Slip has been submitted for Approval<br>    a. At least one Item&#39;s Requested Quantity is greater than the 80% of the Item&#39;s GFQ<br>3. Type of Request is OFM and OFM Transfer of Materials</td><td class="s7">1. Log in as requester and create a new RS with:<br>- Item under Department – quantity below 80%<br>- Item under Project – quantity below 80%<br>2. Edit Item under Department  quantity to exceed 80% of its remaining GFQ.<br>Edit Item under Project quantity to also exceed 80% of its remaining GFQ.<br>3. Click Approve <br>4. Observe Approval Flow.</td><td class="s7">4. System should add Optional Approver for Department or Project to the RS approval flow.</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td></tr></tbody></table></div>