Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,STATUS,Remarks,Defects
RS APPROVAL - Allow deletion of Requested Item by the Approvers during Approval,,,,,,,,,,,
PRS-1403-001,Allow deletion of Requested Item by the Approvers during Approval,High,"Verify if the Requisition Slip Number with a Status of ""For RS Approval"" is clickable","1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval""
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
",,2. The RS Number was clickable and the user was redirected to the Requisition Slip Page,Regel_Sprint1_Test Result,Passed,,
PRS-1403-002,Allow deletion of Requested Item by the Approvers during Approval,High,Verify if the Remove Icon under the Actions Column is displayed for each item,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval""
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Scroll down to the Items Table
4. Validate if the Remove Icon is displayed for each item under Actions Column
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The Remove Icon was displayed for each item under Actions Column",,Passed,,
PRS-1403-003,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if the Remove Icon is enabled when there are more than one item in the Items Table,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" and with more than one item requested
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Scroll down to the Items Table
4. Validate if the Remove Icon is enabled when there are more than one item in the Items Table
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The Remove Icon is enabled when there are more than one item in the Items Table",,Passed,,
PRS-1403-004,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if the Remove Icon is disabled when there is only one item in the Items Table,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" and with only one item requested
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Scroll down to the Items Table
4. Validate if the Remove Icon is disabled when there is only one item in the Items Table
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The Remove Icon is disabled when there is only one item in the Items Table",,Failed,4/28: removed icon is enabled,4/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1631 
PRS-1403-005,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if the Remove Icon is removed when the Requisition Slip has been Fully Approved,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number that has been fully approved
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Validate that the Remove Icon is removed
",,"
2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
3. The Remove Icon is removed when the Requisition Slip has been fully approved
",,Passed,,
PRS-1403-006,Allow deletion of Requested Item by the Approvers during Approval,High,Verify if the Confirmation Modal displays when the Remove Icon is clicked,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval""
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Click the Remove Icon for a specific item
4. Validate that a Confirmation Modal appears upon clicking the Remove Icon button
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. A Confirmation Modal appeared upon clicking the Remove Icon button
",,Passed,,
PRS-1403-007,Allow deletion of Requested Item by the Approvers during Approval,High,Verify if Cancel Button in the Confirmation Modal closes the Modal and does not remove the item,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval""
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Click the Remove Icon for a specific item
4. Validate that a Confirmation Modal appears upon clicking the Remove Icon button
5. Click the Cancel Button
6. Validate that the Confirmation Modal closes and does not remove the item
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. A Confirmation Modal appeared upon clicking the Remove Icon button
6. The Confirmation Modal closed and did not remove the item

",,Passed,,
PRS-1403-008,Allow deletion of Requested Item by the Approvers during Approval,High,Verify if Continue Button in the Confirmation Modal removes the item and requires Submit to apply changes,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval""
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Click the Remove Icon for a specific item
4. Validate that a Confirmation Modal appears upon clicking the Remove Icon button
5. Click the Continue Button
6. Validate that the action removes the item and requires Submit to apply changes
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. A Confirmation Modal appeared upon clicking the Remove Icon button
6. The action removed the item and required Submit to apply changes
",,Failed,4/25 Cannot successfully Delete an Item,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-1403-009,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if only the current Approver can remove an item during their Approval process,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Current approver
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the items Table
4. Validate that the Item is removed successfully
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed successfully
",,Failed,4/25: Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-1403-010,Allow deletion of Requested Item by the Approvers during Approval,High,Attempt to remove an item even if you are not the Current Approver,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Not the Current Approver
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Attempt to remove an item from the items Table
4. Validate that the user that is Not the Current Approver cannot remove an item
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The user that is Not the Current Approver cannot remove an item
",,Passed,,
PRS-1403-011,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if removing an item doesn't repeat the Approval process,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Level 1 approver
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table , then approve
4. Validate that the item is removed and it is approved
5. Login as Level 2 approver
6. Validate that the approval process continues, and the removal of item doesn't repeat the Approval process, meaning it does not go back to Level 1 Approver
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed and the process was approved
6. The approval process continued, and the removal of item did not repeat the Approval process, meaning it did not go back to Level 1 Approver
",,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-1403-012,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if the next Approver can approve the remaining items after an item is removed,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Level 1 approver
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table , then approve
4. Validate that the item is removed and it is approved
5. Login as Level 2 approver
6. Approve the remaining items
7. Validate that the Level 2 approver can approve the remaining items after an item is removed
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed and the process was approved
7.  The Level 2 approver can approve the remaining items after an item is removed

",,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-1403-013,Allow deletion of Requested Item by the Approvers during Approval,High,Verify if changes are tracked in the RS History and Audit Logs after an item is removed,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Navigate to the RS History and Audit Logs
2. Validate if the changes are tracked in the RS History and Audit Logs after an item is removed",,2. The changes are tracked in the RS History and Audit Logs after an item is removed,,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-049-014,Allow deletion of Requested Item by the Approvers during Approval,Critical,"Verify if all levels of RS Approvers (e.g., Lv1 to Lv5) can remove items as long as RS Items > 1","1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Level 1 approver
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table , then approve
4. Validate that the item is removed and it is approved
5. Repeat steps 1 to 4 as the next approvers (Level 2 to 5) respectively, until one RS item remains
6. Validate that each level of Approver can remove items as long as more than 1 item remains

",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed and the process was approved
6. Each level of Approver can remove items as long as more than 1 item remains


",,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-049-015,Allow deletion of Requested Item by the Approvers during Approval,Critical,Attempt to remove the only remaining item after series of removal as the next/last approver,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Level 1 approver
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table , then approve
4. Validate that the item is removed and it is approved
5. Repeat steps 1 to 4 as the next approvers (Level 2 to 4) respectively, until one RS item remains
6. Login as Level 5 approver, and attempt to remove the only remaining item
7. Validate that the item cannot be removed as the remove icon is disabled when number of item is equal to 1

",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed and the process was approved
6. The item cannot be removed as the remove icon is disabled when number of item is equal to 1



",,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-049-016,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if an added Approver during the RS Approval process can delete items,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Number with a Status of ""For RS Approval"" as Added Approver During Approval
2. Validate if the RS Number is clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table
4. Validate that the item is removed successfully as the added approver during approval

",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed successfully as the added approver during approval
",,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-049-017,Allow deletion of Requested Item by the Approvers during Approval,Critical,Verify if item removal is allowed for different types of Requests,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Numbers with Status of ""For RS Approval"" for all the different types of requests
2. Validate if the RS Numbers are clickable and the user is redirected to the Requisition Slip Page
3. Remove an Item for each type of requests
4. Validate that the Item removal is allowed consistently across all Types of Requests and the functionality is not limited by request classification
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. Items were removed successfully on different types of requests
6. The Item removal was allowed consistently across all Types of Requests and the functionality was not limited by request classification
",,Not Started,4/25: Blocked due to Unable to remove item,4/25: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1547
PRS-049-018,Allow deletion of Requested Item by the Approvers during Approval,Minor,Verify if the application displays a success message after an item is successfully removed,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Numbers with Status of ""For RS Approval""
2. Validate if the RS Numbers are clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table
4. Validate that the Item is removed successfully and the application displays a success message after an item is successfully removed
",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed successfully and the application displays a success message after an item is successfully removed

",,Not Started,,
PRS-049-019,Allow deletion of Requested Item by the Approvers during Approval,High,Verify if the total number of item is automatically updated after an item is removed,"1. Requisition Slip is submitted
2. Logged in User is an Approver for a Requisition Slip","
1. Click RS Numbers with Status of ""For RS Approval""
2. Validate if the RS Numbers are clickable and the user is redirected to the Requisition Slip Page
3. Remove an item from the Items Table
4. Validate that the Item is removed successfully and that the total number of items is automatically updated after an item is removed",,"2. The RS Number was clickable and the user was redirected to the Requisition Slip Page
4. The item was removed successfully and  the total number of items was automatically updated after an item was removed


",,Not Started,,
RS APPROVAL - Allow Adding of Notes before Approval of RS,,,,,,,,,,,
PRS-1396-001,"
Allow Adding of Notes before Approval of RS",Critical,Verify that a Requisition Slip can be submitted for Approval,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Create a new Requisition Slip or open a drafted one.
2. Click the ""Submit"" button.",,2. Requisition Slip is successfully submitted and ready for approval.,,Not Started,4/25: Out of scope due to spillover to sprint2,
PRS-1396-002,"
Allow Adding of Notes before Approval of RS",Minor,Verify Requisition Slip No. redirection.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Navigate to For My Approval/Assigned Tab in Dashboard
2. Click the RS No. hyperlink of submitted Requisition Slip",,2. Requisition Slip details page is displayed.,,Not Started,,
PRS-1396-003,"
Allow Adding of Notes before Approval of RS",High,Verify that a floating Confirmation Message appears when initiating approval.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Click on the ""Approve"" button",,1. A floating confirmation message for approval is displayed.,,Not Started,,
PRS-1396-004,"
Allow Adding of Notes before Approval of RS",Crtitical,Verify that clicking Approve displays a Confirmation Modal with Notes field.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Click on the ""Approve"" button
2. Observe the modal",,"2. A confirmation modal is shown and displayed the following field and buttons:
     - Notes
     - Add Approver
     - Continue
     - Cancel",,Not Started,4/25 No Notes appeared,
PRS-1396-005,"
Allow Adding of Notes before Approval of RS",High,Verify that Notes field accepts alphanumeric and special characters.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Type a valid note with alphanumeric and special characters.
2. Click ""Continue"" button.",,2. Note is accepted and approval proceeds.,,Not Started,,
PRS-1396-006,"
Allow Adding of Notes before Approval of RS",Minor,Verify that Notes field does not accept emojis.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Enter emojis in the Notes field.
2. Click ""Continue"" button.",,2. Error message and approval does not proceed.,,Not Started,,
PRS-1396-007,"
Allow Adding of Notes before Approval of RS",High,Verify that Notes field accepts input up to 100 characters.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Enter exactly 100 characters in the Notes field.
2. Click ""Continue"" button.",,2. Note is accepted and approval proceeds.,,Not Started,,
PRS-1396-008,"
Allow Adding of Notes before Approval of RS",Minor,Verify that Notes field does not accept more than 100 characters.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Enter more than 100 characters in the Notes field.
2. Attempt to click ""Continue"" button.",,2. System prevents entry exceeding 100 characters.,,Not Started,,
PRS-1396-009,"
Allow Adding of Notes before Approval of RS",High,Verify that Notes field is optional.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Leave the Notes field blank.
2. Click ""Continue"" button.",,2. Approval proceeds without error.,,Not Started,,
PRS-1396-010,"
Allow Adding of Notes before Approval of RS",High,Validate Cancel button functionality.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Click on ""Cancel"" button.",,1. Modal closes and user is returned to Requisition Slip detail page.,,Not Started,,
PRS-1396-011,"
Allow Adding of Notes before Approval of RS",High,Verify that entered Notes are displayed under the Check Notes button after Approval.,"1. User is an RS Approver
2. A Requisition Slip has been submitted for Approval","1. Verify the presence of the “New Attachment” badge.
2. Click on “Check Notes” of the approved Requisition Slip.
3. Verify visibility of approver.
4. Verify the badge is cleared.",,"1. “New Attachment” badge is displayed.
2. Entered Approval Notes are displayed correctly.
3. Approver name should be displayed correctly.
4. “New Attachment” badge is cleared when viewed.",,Not Started,,
RS APPROVAL - Update Adding of Additional Approver before Approval of RS,,,,,,,,,,,
1314-001,Update Adding of Additional Approver before Approval of RS,Critical,Verify Add Button in Approvers,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval","1. Select RS Number
2. Navigate to Approvers Sections
3. Click Add button
4. Search for a User or view User list
5. Select a user
6. Click Add Approver",,"1. Should display the Request Details, Requested Items, and Approver
2. Should display and click Add Button in the Approvers Section
3. Should display a Modal that will allow adding of an Approver
    a. Displays the message: ""You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.""
    b. Should display a Search User Field
4. Should display Users with a User Types of
    a. Supervisor
    b. Assistant Manager
    c. Department Head
    d. Division Head
    e. Area Staff/Department Secretary
5. Should be displayed in the field
6. Additional Approver should be displayed below the current Approver with a * as their Label",,Not Started,,
1314-002,Update Adding of Additional Approver before Approval of RS,Critical,Verify Behavior After Adding Additional Approver,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. Additional Approver has been added","1. Select RS Number
2. Validate Approvers section
3. Edit Additional Approver and Update Approver
4. Delete Approver",,"2. Additional Approver should be displayed below the current Approver with a * as their Label
    a. Added Approver should be reflected to the List of Approvers once added
        i. Will not need to wait for Approval for the User to be added as an Additional Approver
    b. Should notify the tagged Additional Approver through the Notification Bell
    c. Should allow the Default Approver to Edit or Delete the Added Approver until they haven't Approve the Requisition Slip
3. Additional Approver should update and display the name of the new Approver
4. Additional Approver should be removed from the Approvers section",,Not Started,,
1314-003,Update Adding of Additional Approver before Approval of RS,High,Verify Additional Approver Notification,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. Additional Approver has been added
4. Login as Additional Approver","1. View Notification Bell
2. Click Notification Bell and observe notification",,"1. Notification bell count should update
2. Notification should be displayed following the template below:
""Title: 
Assigned as an Additional Approver

Content:
[DEFAULT APPROVER'S NAME] has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.

Date of the Notification: [MMM-DD-YYY]
Nov 08 2024""",,Not Started,,
1314-004,Update Adding of Additional Approver before Approval of RS,Critical,Verify if Additional Approver can Approve Before Current Approver,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. Additional Approver has been added
4. Current Approver has not approved yet
5. Login as Additional Approver","1. Select RS Number
2. Observe if approve sticky note is displayed",,"2. Should require the current Approver to Approve the Requisition Slip before the Additional Approver can Approve
    a. Should require the Additional Approver to Approve the Requisition Slip before allowing to continue on the next Level of Approve",,Not Started,,
1314-005,Update Adding of Additional Approver before Approval of RS,Critical,Verify Add Approver Button in Approve Modal When No Additional Approvers have been Added,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. No Additional Approver has been added","1. Select RS Number
2. Click Approve button
3. Click Add Approver button
4. Search for a User or view User list
5. Select a user
6. Click Add & Submit",,"2. If an Additional Approver is not yet Added, should allow adding of Approver when Approving the Requisition Slip
    a. Should only display the Add Approver Button in the Confirmation Message if the Additional Approver is not yet indicated
3. Should display a Modal that will allow adding of an Approver
    a. Displays the message: ""You are about to add an approver. Please select your designated approver and press “Add & Submit” if you want to proceed with this action and submit your approval.""
    b. Should display a Search User Field
4. Should display Users with a User Types of
    a. Supervisor
    b. Assistant Manager
    c. Department Head
    d. Division Head
    e. Area Staff/Department Secretary
5. Should be displayed in the field
6. Additional Approver should be displayed below the current Approver with a * as their Label and Current Approver status should be Approved",,Not Started,,
1314-006,Update Adding of Additional Approver before Approval of RS,High,Verify Adding Additional Approver After Deletion,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. Additional Approver has been added
4. Current Approver has not approved yet","1. Select RS Number
2. Validate Approvers section
3. Delete Approver
4. Click Add button
5. Select a different user as approver",,"3. Approver should be removed from Approvers section
4. Should be able to add a new additional approver
5. Should be added successfully into Approvers section",,Not Started,,
1314-007,Update Adding of Additional Approver before Approval of RS,High,Verify Adding Additional Approver After Deletion and RS already approved by the current approver,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. Additional Approver has been added
4. Current Approver has already approved the RS","1. Select RS Number
2. Validate Approvers section
3. Delete Approver
4.Check Add Button",,"3. Approver should be removed from Approvers section
4. Add button should no longer visible",,Not Started,,
1314-008,Update Adding of Additional Approver before Approval of RS,High,Verify Adding Additional Approver if Additional Approver has already been Added,"1. User is an RS Approver and the current Approver
2. A Requisition Slip has been submitted for Approval
3. Additional Approver has been added
4. Current Approver has not approved yet","1. Select RS Number
2. Validate Approvers section",,2. Add button should be removed from Approvers section if an additional approver has already been added,,Not Started,,
RS APPROVAL - Enabling RS Optional Approver,,,,,,,,,,,
1492-001,RS APPROVAL - Enabling RS Optional Approver,Critical,Verify Optional Approver for Department Approvers and Project Approvers,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open assigned Requisition Slip
2. Navigate to Approval Setup
3. Verify Optional Approver for Department Approvers and Project Approvers",,4. Optional Approver should be available for both Department and Project Approvers.,,Not Started,,
1492-002,RS APPROVAL - Enabling RS Optional Approver,High,Verify Quantity Exceeds 80% of Remaining GFQ on Submission,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open assigned Requisition Slip
2. Observe item/s with Quantity is greater than 80% of the Item's Remaining GFQ",,4. Item with Quantity is greater than 80% of the Item's Remaining GFQ is displayed,,Not Started,,
1492-003,RS APPROVAL - Enabling RS Optional Approver,High,Verify Optional Approver for Department and Project Approvers as the Last Approver,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open assigned Requisition Slip
2. Observe list of Approvers",,4. Optional Approver is the Last Approver,,Not Started,,
1492-004,RS APPROVAL - Enabling RS Optional Approver,High,Verify Optional Approver for Department and Project Approvers is Required,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open Requisition Slip
2. Remove or do not Assign Optional Approver",,4. Error Message appears Optional Approver is Required,,Not Started,,
1492-005,RS APPROVAL - Enabling RS Optional Approver,Critical,Verify Cut off approval process if Optional Approver is not yet assigned,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open Requisition Slip
2. Observe Approval Process with no Optional Approval assigned",,4. Approval process is cut off,,Not Started,,
1492-006,RS APPROVAL - Enabling RS Optional Approver,Critical,Verify Optional Approver Assigned is cascaded to all of the Requisition Slips that need Approval,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open assigned Requisition Slip
2. Observe Approval Process for Requisition Slips with assigned Optional Approval ",,4. Assigned Optional Approver is cascaded to Requisition Slips that need approval ,,Not Started,,
1492-007,RS APPROVAL - Enabling RS Optional Approver,High,"Verify Optional Approver can Approve, Reject, Edit the Request, and Add an Additional Approver","1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open assigned Requisition Slip
2. Verify if Optional Approver can:
-Approve
-Reject
-Edit the Request
-Add an Addtional Approver","1. Open assigned Requisition Slip
2. Verify if Optional Approver can:
-Approve
-Reject
-Edit the Request
-Add an Addtional Approver","4. Optional Approver should be able to:
-Approve
-Reject
-Edit the Request
-Add an Addtional Approver",,Not Started,,
1492-008,RS APPROVAL - Enabling RS Optional Approver,High,Verify Optional Approver is required once Item's Quantity updated to more than 80% of Remaining GFQ during Approval,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials",1. Verify Item's Quantity has been Updated by an Approver to more than 80% of Remaining GFQ,,4. Optional Approver should be Required,,Not Started,,
1492-009,RS APPROVAL - Enabling RS Optional Approver,High,"Verify system display  ""---"" if Optional Approver is not yet Assigned","1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Open Requisition Slip
2. Observe Approval Process with no Optional Approval assigned",,"4. System should display  ""---"" if Optional Approver is not yet Assigned",,Not Started,,
1492-010,RS APPROVAL - Enabling RS Optional Approver,Cirtical,Verify if system only allow Optional Approver removal before their approval if Item Quantity drops below 80% of Latest Remaining GFQ,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Navigate to the Item approval page.
2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ
3. Obvserve  Optional Approver while Quantity is above 80%.",,"4. Optional Approver should be available when current approver when Quantity is above 80% of the Latest Remaining GFQ.
5. Optional Approver should be automatically removed once current approver approved less than 80% of the Latest Remaining GFQ.",,Not Started,,
1492-011,RS APPROVAL - Enabling RS Optional Approver,Critical,"Verify If the one Optional Approver has already Approved and the Item's Quantity became less than 80% of the Item's GFQ, should not allow the removal of the Optional Approver
","1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Navigate to the Item approval page.
2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ
3. Approve the Requisition Slip.
4. After approval, reduce Item Quantity further to 70 units (well below 80%).
5. Observe Optional Approver after they have approved and Quantity is below 80%.",,4. Optional Approver should be automatically removed once current approver approved less than 80% of the Latest Remaining GFQ.,,Not Started,,
1492-012,RS APPROVAL - Enabling RS Optional Approver,High,Verify Correct Optional Approver for Department is Added to RS,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Navigate to the Item approval page.
2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ
3. Obvserve Optional Approver for Department while Quantity is above 80%.",,4. The optional approver must match the Department added in Requesition Slip.,,Not Started,,
1492-013,RS APPROVAL - Enabling RS Optional Approver,High,Verify Correct Optional Approver for Project is Added to RS,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Navigate to the Item approval page.
2. Check if Items Quantity is more than the 80% of the Latest Remaining GFQ
3. Obvserve Optional Approver for Project while Quantity is above 80%.",,4. The optional approver must match the Project added in Requesition Slip.,,Not Started,,
1492-014,RS APPROVAL - Enabling RS Optional Approver,High,Verify Optional Approver from Department Only when Added as Category,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Log in as requester and create an RS under Department category, with Item assigned to Department. Quantity is set below 80% of remaining GFQ.
2. Log in as the assigned Approver and Edit Item's quantity during approval to exceed 80% of remaining GFQ.
3. Confirm that no optional Project approver is added.",,"4. System adds the optional Department approver to the approval flow.
5. Only Department optional approver is present in the routing.",,Not Started,,
1492-015,RS APPROVAL - Enabling RS Optional Approver,High,Verify Optional Approver from Project Only when Added as Category,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Log in as requester and create an RS under Project category, with Item assigned to Project. Quantity is set below 80% of remaining GFQ.
2. Log in as the assigned Approver and Edit Item's quantity during approval to exceed 80% of remaining GFQ.
3. Confirm that no optional Department approver is added.",,"4. System adds the optional Department approver to the approval flow.
5. Only Project optional approver is present in the routing.",,Not Started,,
1492-016,RS APPROVAL - Enabling RS Optional Approver,Critical,Verify if Add Department or Project Optional Approvers When Both Are Used in RS,"1. User is an Optional RS Approver
2. A Requisition Slip has been submitted for Approval
    a. At least one Item's Requested Quantity is greater than the 80% of the Item's GFQ
3. Type of Request is OFM and OFM Transfer of Materials","1. Log in as requester and create a new RS with:
- Item under Department – quantity below 80%
- Item under Project – quantity below 80%
2. Edit Item under Department  quantity to exceed 80% of its remaining GFQ.
Edit Item under Project quantity to also exceed 80% of its remaining GFQ.
3. Click Approve 
4. Observe Approval Flow.",,4. System should add Optional Approver for Department or Project to the RS approval flow.,,Not Started,,