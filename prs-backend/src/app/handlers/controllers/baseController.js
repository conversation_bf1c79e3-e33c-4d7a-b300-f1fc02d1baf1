const { ZodError } = require('zod');
const { ErrorFactory, ErrorTranslator } = require('../../errors');

/**
 * BaseController class that provides standard response handling
 * for controller layer implementations.
 */
class BaseController {
  /**
   * Creates a new BaseController instance
   *
   * @param {Object} container - Dependency injection container
   */
  constructor(container) {
    const { utils, fastify } = container;

    this.utils = utils;
    this.fastify = fastify;
    this.ErrorFactory = ErrorFactory;
    this.ErrorTranslator = ErrorTranslator;

    // For backward compatibility
    const { clientErrors } = container;
    this.clientErrors = clientErrors;
  }

  /**
   * Handles successful response
   *
   * @param {Object} reply - Fastify reply object
   * @param {Object} data - Data to send in response
   * @param {number} statusCode - HTTP status code
   * @param {string} message - Success message
   * @returns {Object} - Fastify reply
   */
  sendSuccess(reply, data, statusCode = 200, message = 'Success') {
    return reply.status(statusCode).send({
      success: true,
      message,
      data
    });
  }

  /**
   * <PERSON><PERSON> created response
   *
   * @param {Object} reply - Fastify reply object
   * @param {Object} data - Data to send in response
   * @param {string} message - Success message
   * @returns {Object} - Fastify reply
   */
  sendCreated(reply, data, message = 'Resource created successfully') {
    return this.sendSuccess(reply, data, 201, message);
  }

  /**
   * Handles no content response
   *
   * @param {Object} reply - Fastify reply object
   * @returns {Object} - Fastify reply
   */
  sendNoContent(reply) {
    return reply.status(204).send();
  }

  /**
   * Handles error response
   *
   * @param {Error} error - Error object
   * @throws {Error} - Rethrows the error to be handled by global error handler
   */
  handleError(error) {
    // Log the error
    this.fastify?.log.error({
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });

    // Handle Zod validation errors
    if (error instanceof ZodError) {
      throw this.ErrorFactory.validation(
        error.issues[0]?.message || 'Validation failed',
        error.issues.reduce((issues, current) => {
          issues[current.path.join('.') || 'error'] = current.message;
          return issues;
        }, {}),
        error
      );
    }

    // If it's already an AppError, rethrow it
    if (error.name && error.name.endsWith('Error') && error.getHttpStatus) {
      throw error;
    }

    // Translate other errors
    throw this.ErrorTranslator.translate(error);
  }

  /**
   * Validates request data against a schema
   *
   * @param {Object} schema - Zod schema
   * @param {Object} data - Data to validate
   * @returns {Object} - Validated data
   */
  validate(schema, data) {
    try {
      // Try to use the utils.parseDomain if available
      if (this.utils && this.utils.parseDomain) {
        return this.utils.parseDomain(schema, data);
      }

      // Otherwise, parse directly with Zod
      const result = schema.safeParse(data);

      if (!result.success) {
        throw result.error;
      }

      return result.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Executes a controller action with error handling
   *
   * @param {Function} action - Controller action to execute
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   * @returns {Promise<Object>} - Fastify reply
   */
  async executeAction(action, request, reply) {
    try {
      return await action(request, reply);
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Creates a controller action with standard error handling
   *
   * @param {Function} handler - Handler function
   * @returns {Function} - Controller action
   */
  createAction(handler) {
    return async (request, reply) => {
      return this.executeAction(() => handler(request, reply), request, reply);
    };
  }
}

module.exports = BaseController;
