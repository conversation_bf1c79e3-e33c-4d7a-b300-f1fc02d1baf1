<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Purchase Request System</title>
    <style>
      @page {
        size: legal;
        margin: 0.5in;
      }

      body {
        font-family: 'Inter', sans-serif;
        font-size: 12px;
        line-height: 1.2;
        color: #000;
        margin: 0;
        padding: 0;
        width: 8.5in;
        height: 14in;
        box-sizing: border-box;
      }

      @supports (font-variation-settings: normal) {
        body {
          font-family: 'Inter var', sans-serif;
        }
      }

      .wrapper {
        max-width: 7.5in;
        margin: 0 auto;
        padding: 0;
        padding-bottom: 40px;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 15px;
        padding-top: 15px;
      }

      .logo-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .logo {
        font-size: 20px;
        line-height: 1;
      }

      .title {
        font-weight: bold;
        font-size: 12px;
        color: #450a0a;
      }

      .page-info {
        text-align: right;
        font-size: 12px;
        margin-top: 10px;
        margin-bottom: 10px;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 2px;
      }

      .page-number-input {
        width: 18px;
        text-align: center;
        background-color: #e6f0ff;
        border: 1px solid #b3d1ff;
        border-radius: 4px;
        padding: 1px;
        color: #0047b3;
        font-weight: bold;
        margin: 0;
      }

      .rs-number {
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .rs-label {
        font-size: 17px;
        font-weight: bold;
      }

      .rs-value {
        text-decoration: underline;
        font-weight: bold;
      }

      .status {
        font-weight: bold;
        text-align: center;
        width: 100%;
        font-size: 16px;
        margin-top: 10px;
        margin-bottom: 20px;
        resize: none;
        border: none;
        background: transparent;
        overflow: hidden;
        font-family: inherit;
      }

      .section {
        margin: 15px 0;
      }

      .section-title {
        font-weight: bold;
        margin-bottom: 5px;
        font-size: 12px;
      }

      .details-grid-outer {
        border: 1px solid #ccc;
        border-radius: 10px;
        padding: 10px;
      }

      .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        padding: 5px;
        font-size: 10px;
      }

      .details-row {
        display: flex;
        flex-direction: column;
      }

      .details-label {
        color: #323232;
        font-weight: normal;
        font-size: 8px;
        margin-bottom: 4px;
      }

      input,
      select {
        border: none;
        background: transparent;
        width: 100%;
        font-family: inherit;
        font-size: inherit;
        outline: none;
      }

      .items-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
        margin-bottom: 5px;
      }

      .items-title {
        font-weight: bold;
        color: #495057;
        font-size: 14px;
      }

      .items-counter {
        display: flex;
        align-items: center;
        gap: 3px;
      }

      .item-count-input {
        width: 20px;
        text-align: center;
        background-color: transparent;
        border: none;
        border-bottom: 1px solid #b3d1ff;
        color: #0047b3;
        font-weight: bold;
        margin: 0;
        padding: 1px;
      }

      .total-count {
        width: 25px;
      }

      .items-counter span {
        color: #6c757d;
      }

      table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 8px;
        overflow: hidden;
      }

      th,
      td {
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 4px;
      }

      th {
        color: #4f575e;
        vertical-align: top;
        text-align: left;
        font-weight: bold;
        font-size: 8px;
        border-bottom: 1px solid #ccc;
      }

      th:first-child,
      td:first-child {
        border-right: 1px solid #ccc;
      }

      th:last-child,
      td:last-child {
        border-left: none;
      }

      tbody tr {
        border-bottom: 1px solid #ccc;
      }

      td {
        font-size: 10px;
        height: 100px;
        vertical-align: center;
      }

      .num-col {
        vertical-align: middle;
        width: 20px;
        text-align: center;
      }

      .unit-col {
        width: 40px;
        vertical-align: middle;
        text-align: center;
      }

      .remaining-col,
      .qty-col {
        width: 90px;
        text-align: center;
        vertical-align: middle;
      }

      .item-name-cell {
        vertical-align: middle;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
        padding: 4px;
        padding-top: 25px;
      }

      .item-name-input {
        color: #4f575e;
        width: 100%;
        min-height: 40px;
        outline: none;
        border: none;
        background: transparent;
        font-family: inherit;
        font-size: inherit;
        text-decoration: underline;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
        text-align: center;
        resize: none;
        overflow: hidden;
        vertical-align: middle;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        padding-top: 20px;
      }

      input,
      textarea {
        text-align: center;
        vertical-align: middle;
      }

      .text-left {
        text-align: center;
      }

      .footer {
        margin-top: 20px;
        text-align: right;
        font-size: 10px;
        color: #666;
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="header">
        <div class="logo-title">
          <div class="logo">
            <svg
              width="19"
              height="20"
              viewBox="0 0 19 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <rect
                width="18.105"
                height="19.6138"
                fill="url(#pattern0_17325_42288)"
              />
              <defs>
                <pattern
                  id="pattern0_17325_42288"
                  patternContentUnits="objectBoundingBox"
                  width="1"
                  height="1"
                >
                  <use
                    xlink:href="#image0_17325_42288"
                    transform="scale(0.0166667 0.0153846)"
                  />
                </pattern>
                <image
                  id="image0_17325_42288"
                  width="60"
                  height="65"
                  preserveAspectRatio="none"
                  xlink:href="data:image/png;base64,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"
                />
              </defs>
            </svg>
          </div>
          <div class="title">Purchase Request System</div>
        </div>
        <div class="page-info">
          <span>Page</span>
          <input type="text" name="currentPage" class="page-number-input" />
          <span>of</span>
          <input type="text" name="totalPage" class="page-number-input" />
        </div>
      </div>

      <div class="rs-number">
        <div class="rs-label">
          R.S. #
          <input
            class="rs-value"
            type="text"
            name="rsNumber"
            style="width: 180px; font-weight: bold"
          />
        </div>
        <div><textarea name="status" class="status" rows="2"></textarea></div>
      </div>

      <div class="section">
        <div class="details-grid-outer">
          <div class="section-title">Request Details</div>
          <div class="details-grid">
            <div class="details-row">
              <div class="details-label">Company:</div>
              <div><input type="text" name="company" /></div>
            </div>
            <div class="details-row">
              <div class="details-label">Department:</div>
              <div><input type="text" name="department" /></div>
            </div>
            <div class="details-row">
              <div class="details-label">Project:</div>
              <div><input type="text" name="project" /></div>
            </div>
            <div class="details-row">
              <div class="details-label">Purpose:</div>
              <div><input type="text" name="purpose" /></div>
            </div>
          </div>
        </div>
      </div>

      <div class="items-header">
        <div class="items-title">Items</div>
        <div class="items-counter">
          <input type="text" name="firstItemCount" class="item-count-input" />
          <span>-</span>
          <input type="text" name="lastItemCount" class="item-count-input" />
          <span>of</span>
          <input
            type="text"
            name="total"
            class="item-count-input total-count"
          />
        </div>
      </div>

      <table>
        <thead>
          <tr>
            <th>#</th>
            <th>Item Name</th>
            <th>Unit</th>
            <th>Remaining GFQ</th>
            <th>Qty</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="num-col"><input type="text" name="itemNum1" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName1"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit1" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq1" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty1" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum2" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName2"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit2" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq2" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty2" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum3" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName3"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit3" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq3" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty3" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum4" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName4"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit4" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq4" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty4" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum5" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName5"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit5" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq5" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty5" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum6" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName6"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit6" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq6" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty6" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum7" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName7"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit7" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq7" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty7" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum8" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName8"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit8" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq8" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty8" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum9" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName9"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit9" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq9" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty9" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum10" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName10"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit10" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq10" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty10" class=".text-left" />
            </td>
          </tr>
          <tr>
            <td class="num-col"><input type="text" name="itemNum11" /></td>
            <td class="item-name-cell">
              <textarea class="item-name-input" name="itemName11"></textarea>
            </td>
            <td class="unit-col"><input type="text" name="unit11" /></td>
            <td class="remaining-col">
              <input type="text" name="remainingGfq11" class=".text-left" />
            </td>
            <td class="qty-col">
              <input type="text" name="qty11" class=".text-left" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>
</html>
