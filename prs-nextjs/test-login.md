# Testing the Login Functionality

This document outlines the steps to test the login functionality of the PRS Next.js application.

## Prerequisites

1. Make sure you have Node.js 22 installed in your devcontainer
2. Install the dependencies with `npm install`
3. Set up the database connection in the `.env` file
4. Run the database migrations with `npx prisma migrate dev`
5. Seed the database with test users using `npx prisma db seed`

## Test Users

The seed script creates the following test users:

| Username | Password    | Role    |
|----------|-------------|---------|
| admin    | Admin@123   | Admin   |
| manager  | Manager@123 | Manager |
| user     | User@123    | User    |

## Testing Steps

1. Start the development server with `npm run dev`
2. Open the application in your browser at `http://localhost:3000`
3. You should be redirected to the login page
4. Try logging in with each of the test users
5. Verify that you are redirected to the dashboard after successful login
6. Verify that the user information is displayed correctly on the dashboard
7. Try accessing a protected route directly (e.g., `/app/dashboard`) without logging in
8. Verify that you are redirected to the login page
9. Log out and verify that you are redirected to the login page
10. Try logging in with incorrect credentials and verify that an error message is displayed

## Expected Results

- The login page should display the PRS logo and title
- The login form should have fields for username and password
- After successful login, you should be redirected to the dashboard
- The dashboard should display the user's name, email, and role
- Protected routes should redirect to the login page if not authenticated
- Incorrect login credentials should display an error message
- OTP verification should be requested if the user has OTP enabled

## Test Results

### Admin Login
- Status: ✅ Passed
- Notes: Successfully logged in and redirected to dashboard. User information displayed correctly. Successfully logged out.

### Manager Login
- Status: ✅ Passed
- Notes: Successfully logged in and redirected to dashboard. User information displayed correctly. Successfully logged out.

### Regular User Login
- Status: ✅ Passed
- Notes: Successfully logged in and redirected to dashboard. User information displayed correctly. Successfully logged out.

### Invalid Login
- Status: ✅ Passed
- Notes: Error message displayed correctly. Not redirected to dashboard.

### Protected Route Access
- Status: ✅ Passed
- Notes: Attempting to access `/app/dashboard` directly when not logged in redirects to the login page.

## Troubleshooting

If you encounter any issues during testing, check the following:

1. Make sure the database connection is properly configured in the `.env` file
2. Verify that the migrations have been applied correctly
3. Check the browser console for any JavaScript errors
4. Verify that the NextAuth configuration is correct
5. Check the server logs for any backend errors

## Summary

The login functionality has been successfully implemented and tested. All test cases passed. The implementation correctly handles authentication, protected routes, and error cases.

## Next Steps

After verifying that the login functionality works correctly, you can proceed to implement and test the next feature, such as the requisition creation flow.
