/**
 * Example model for demonstrating base classes
 */
module.exports = (sequelize, Sequelize) => {
  const ExampleModel = sequelize.define(
    'examples',
    {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'pending'),
        defaultValue: 'pending',
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('type1', 'type2', 'type3'),
        allowNull: false,
      },
      priority: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 5,
        },
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: Sequelize.UUID,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      underscored: true,
      paranoid: true, // Soft delete
    }
  );

  ExampleModel.associate = (models) => {
    // Example of a relationship
    ExampleModel.belongsTo(models.userModel, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    ExampleModel.belongsTo(models.userModel, {
      foreignKey: 'updatedBy',
      as: 'updater',
    });
  };

  return ExampleModel;
};
