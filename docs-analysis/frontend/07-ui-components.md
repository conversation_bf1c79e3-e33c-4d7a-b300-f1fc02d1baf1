# UI Components

## Overview

The PRS-Frontend implements a comprehensive UI component system built on top of Tailwind CSS. This document provides a detailed analysis of the UI components, including their design principles, implementation patterns, and usage across the application.

## Design System

The UI components in PRS-Frontend follow a consistent design system with the following characteristics:

1. **Tailwind CSS**: The application uses Tailwind CSS for styling, providing a utility-first approach to CSS
2. **Custom Color Palette**: Defined in `tailwind.config.js` with primary, secondary, and base color schemes
3. **Component Variants**: Components like buttons and inputs have multiple variants for different use cases
4. **Consistent Spacing**: Components use consistent spacing and sizing based on Tailwind's scale
5. **Responsive Design**: Components are designed to work across different screen sizes

### Color System

The color system is defined in `tailwind.config.js`:

```javascript
colors: {
  base: {
    50: '#eff6ff',
    100: '#dbeafe',
    // ... other shades
    900: '#1e3a8a',
  },
  primary: {
    50: '#f5f3ff',
    100: '#ede9fe',
    // ... other shades
    900: '#4c1d95',
  },
  secondary: {
    50: '#fff1f2',
    100: '#ffe4e6',
    // ... other shades
    900: '#881337',
  },
}
```

This color system provides a consistent palette for the entire application, with different shades for different purposes.

## Core UI Components

### Button Component

The Button component is one of the most versatile components in the system, supporting multiple variants, sizes, and states:

```jsx
// From src/components/ui/Button/Button.jsx
const buttonVariants = cva(
  'w-full inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 transition duration-300 rounded-xl py-2 px-4',
  {
    variants: {
      variant: {
        submit: 'bg-[#754445] text-white',
        secondary: 'bg-[#445475] text-white hover:bg-[#4F5F8B]',
        outline: 'text-[#754445] border border-[#754445]',
        danger: 'text-[#EB5757] border border-[#EB5757]',
        back: 'flex w-10 h-10 rounded-lg bg-gray-100 shadow-sm hover:bg-gray-200',
        action: 'bg-gray-200 rounded-lg font-semibold',
        icon: 'w-auto bg-sky-500 text-white rounded-lg font-semibold',
        paginationSelected:
          'flex items-center justify-center rounded-xl bg-white text-[#306F4E] border border-[#306F4E]',
        paginationNormal:
          'flex items-center justify-center rounded-xl bg-white text-gray-500 border border-gray-200',
        noColor: 'bg-transparent text-black',
        file: 'text-gray-500 bg-white border border-gray-200 font-normal',
        link: 'text-blue-500 underline underline-offset-4 font-semibold p-0 m-0',
      },
      hover: {
        submit: 'hover:bg-[#8B4F4F]',
        action: 'hover:bg-slate-200 hover:text-gray-800',
        highlight: 'hover:bg-sky-400 hover:text-gray-100',
        danger: 'hover:text-[#F58A8A] hover:border-[#F58A8A]',
        pagination:
          'hover:border-1 hover:border-[#306F4E] hover:text-[#306F4E] hover:bg-[#f3fff8]',
        darkGlow: 'hover:text-gray-800',
        outline: 'hover:text-[#8B4F4F] hover:border-[#8B4F4F] hover:bg-rose-50',
        file: 'hover:border-gray-200 hover:bg-gray-50',
        link: 'hover:text-blue-600',
      },
    },
    defaultVariants: {
      variant: 'submit',
      hover: 'submit',
    },
  },
);
```

The Button component supports:
1. Multiple visual variants (submit, secondary, outline, danger, etc.)
2. Different hover states
3. Loading state with a spinner
4. Icon support (left or right position)
5. Disabled state

### Form Components

The form components are built on top of React Hook Form and provide a consistent interface for form inputs:

#### Input Component

```jsx
// From src/components/ui/Form/Input.jsx
const Input = forwardRef(
  (
    {
      className,
      type,
      label,
      error,
      registration,
      placeholder,
      hasIcon = false,
      renderIcon: RenderIcon = null,
      iconClassName = '',
      isSearch = false,
      beforeIcon: BeforeIcon = null,
      iconHandler = () => {},
      columnSpan = 1,
      minDate,
      maxDate,
      maxNumberLength,
      numberOnly,
      noNegative = false,
      isParent = false,
      ...props
    },
    ref,
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const inputType = type === 'password' && showPassword ? 'text' : type;

    const isRadioOrCheckbox = type === 'radio' || type === 'checkbox';

    return (
      <FieldWrapper
        label={isRadioOrCheckbox ? undefined : label}
        error={error}
        hasErrorBelow={false}
        className={cn(`col-span-${columnSpan}`)}
      >
        {/* Input implementation */}
      </FieldWrapper>
    );
  },
);
```

The Input component supports:
1. Different input types (text, password, number, date, etc.)
2. Error states with validation messages
3. Icons (before or after the input)
4. Password visibility toggle
5. Number input constraints (max length, no negative values)
6. Date range constraints

#### Select Component

```jsx
// From src/components/ui/Form/Select.jsx
const Select = forwardRef(
  (
    {
      name,
      control,
      options = [],
      defaultValue,
      className,
      cva,
      label,
      error,
      defaultLabel = '',
      disabled,
      includeBlankOption = false,
      searchable = false,
      clearable = false,
      onLoadMore,
      handleChange = () => {},
      hasMore = false,
      onSearch,
      onChange,
      ...props
    },
    ref,
  ) => {
    // Select implementation
  },
);
```

The Select component supports:
1. Dropdown options with key-value pairs
2. Searchable options
3. Clearable selection
4. Infinite scrolling with load more functionality
5. Disabled state
6. Error states with validation messages

#### TextArea Component

```jsx
// From src/components/ui/Form/TextArea.jsx
const TextArea = forwardRef(
  (
    {
      className,
      label,
      error,
      name = '',
      control: externalControl,
      maxCharacters = 100,
      defaultValue = '',
      counterPosition = 'right',
      textLeft = false,
      ...props
    },
    ref,
  ) => {
    // TextArea implementation
  },
);
```

The TextArea component supports:
1. Character counter with maximum limit
2. Error states with validation messages
3. Custom counter position
4. Text alignment options

#### RadioTab Component

```jsx
// From src/components/ui/Form/RadioTab.jsx
const RadioTab = forwardRef(
  ({ options = [], label, error, control, name, defaultValue, disabled = false }, ref) => {
    const { field } = useController({ name, control, defaultValue });

    return (
      <FieldWrapper label={label} error={error} hasErrorBelow={false}>
        <div
          className={cn(
            'inline-flex rounded-lg border border-gray-200 p-1 w-fit gap-x-1',
            { 'opacity-50 cursor-not-allowed': disabled },
          )}
        >
          {options.map(option => (
            <label
              key={option.value}
              className={cn(
                'rounded-md py-1 text-sm transition-colors min-w-28 text-center bg-white text-gray-700',
                {
                  'cursor-pointer': !disabled,
                  'cursor-not-allowed': disabled,
                },
                {
                  'bg-[#795C5F] text-white':
                    field.value === option.value && !disabled,
                  'bg-gray-300 text-gray-500':
                    field.value === option.value && disabled,
                },
                {
                  'hover:bg-gray-100': field.value !== option.value && !disabled,
                },
              )}
            >
              <input
                type="radio"
                className="hidden"
                {...field}
                value={option.value}
                checked={field.value === option.value}
                onChange={e => {
                  if (!disabled) {
                    field.onChange(e.target.value);
                  }
                }}
                disabled={disabled} 
              />
              {option.key}
            </label>
          ))}
        </div>
      </FieldWrapper>
    );
  },
);
```

The RadioTab component provides a stylized radio button group with:
1. Multiple options in a tab-like interface
2. Selected state styling
3. Disabled state
4. Error states with validation messages

#### Checkbox Component

```jsx
// From src/components/ui/Checkbox/Checkbox.jsx
const Checkbox = ({ text, checked, onCheckedChange, className, ...props }) => {
  const handleChange = (e) => {
    e.preventDefault();
    onCheckedChange?.(!checked);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <label className="inline-flex items-center cursor-pointer">
        <input 
          type="checkbox" 
          className="sr-only peer" 
          checked={checked}
          onChange={handleChange}
          {...props} 
        />
        <div 
          className={`w-5 h-5 border-2 border-[#584144] rounded-md bg-white flex items-center justify-center transition-all duration-300 ease-in-out ${
            checked ? 'bg-[#584144] border-[#584144]' : ''
          }`}
          onClick={handleChange}
        >
          <CheckIcon className={checked ? 'text-white' : 'hidden'} />
        </div>
      </label>
      {text && <span className="text-gray-600 text-sm">{text}</span>}
    </div>
  );
};
```

The Checkbox component provides:
1. Custom styled checkbox with animation
2. Label support
3. Controlled component pattern

#### FileUpload Component

```jsx
// From src/components/ui/Form/FileUpload.jsx
const FileUpload = forwardRef(
  (
    {
      name = 'attachments',
      label = 'Attachment/s',
      inputLabel = 'Select Attachment/s',
      maxFileSize = 25,
      maxFileUpload = 25,
      disabled = false,
      modelType,
      accept = '.png,.jpg,.jpeg,.pdf,.xls,.xlsx,.csv',
      setIsUploading,
      defaultValue,
      onAttachmentChange,
      ...props
    },
    ref,
  ) => {
    // FileUpload implementation
  },
);
```

The FileUpload component supports:
1. Multiple file uploads
2. File type restrictions
3. File size limits
4. Upload progress indicator
5. File preview and removal
6. Integration with backend upload API

### Table Component

The Table component is a complex component that supports various features:

```jsx
// From src/components/ui/Table/Table.jsx
const Table = ({
  headers,
  data,
  tdDesign = {},
  isLoading,
  onSort,
  currentSort = [],
  isBareHeader = true,
  hasMaximumHeight = false,
  expandedRowDesign = {},
  buttonData = {},
  expandedRowFunction,
  expandByRow = false,
  expandRowKey = 'id',
  page,
  limit,
  onExpand,
  alignment = 'left',
  POtableCustomStyle = false,
  infoHeader = 'No Items Found',
  infoDescription,
  iconDesign = NewDocument,
  hasHoverTitle = true,
  dynamicHeight = true,
  className,
  removeOverflowHidden = false
}) => {
  // Table implementation
};
```

The Table component supports:
1. Column sorting
2. Pagination
3. Expandable rows
4. Custom cell rendering
5. Loading state
6. Empty state with customizable message
7. Dynamic height based on content
8. Custom styling for specific use cases

### Modal Component

The Modal component provides a flexible dialog system:

```jsx
// From src/components/ui/Modal/Modal.jsx
const Modal = ({
  children,
  isOpen,
  onClose,
  title,
  size = 'default',
  visibility = 'medium',
  hasCloseButton = true,
  className,
  titleClassName,
  bodyClassName,
  closeOnClickOutside = true,
  ...props
}) => {
  // Modal implementation
};
```

The Modal component supports:
1. Different sizes (small, medium, default)
2. Different visibility levels (low, medium, high)
3. Custom title and content
4. Close button toggle
5. Click outside to close functionality
6. Custom styling for different parts (title, body)

### Spinner Component

The Spinner component provides a loading indicator:

```jsx
// From src/components/ui/Spinner/Spinner.jsx
export const Spinner = ({
  size = 'md',
  variant = 'primary',
  className = '',
}) => {
  return (
    <React.Fragment>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={cn(
          'animate-spin',
          sizes[size],
          variants[variant],
          className,
        )}
      >
        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
      </svg>
      <span className="sr-only">Loading</span>
    </React.Fragment>
  );
};
```

The Spinner component supports:
1. Different sizes (sm, md, lg, xl)
2. Different variants (light, primary)
3. Custom styling through className

## Form Validation

The application uses Zod for form validation, providing a type-safe way to validate form inputs:

```javascript
// From src/schema/payment-request-schema.js
const prFormSchema = z
  .object({
    poId: z
      .number({
        invalid_type_error: 'Purchase order ID is required',
      })
      .min(1, 'Purchase order ID is required'),

    payableDate: dateSchema.min(1, 'Payable Date is required.'),
    comment: z.string().optional().nullable(),
    terms: z.string().optional().nullable(),
    attachments: z.any().optional(),
    employee: z.any().optional().nullable(),
  })
  .superRefine((data, ctx) => {
    const terms = data?.terms;
    const employee = data?.employee;

    if (terms === 'Cash in Advance (CIA)' && !employee) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Employee is required.',
        path: ['employee'],
      });
    }
  });
```

The validation system supports:
1. Type validation
2. Required fields
3. Minimum/maximum values
4. Custom validation rules
5. Conditional validation based on other field values

## Component Composition Patterns

The UI components use several composition patterns:

### 1. Compound Components

The Form component uses a compound component pattern:

```jsx
// From src/components/ui/Form/Form.jsx
export {
  useFormField,
  Form,
  FormProvider,
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
  FormField,
};
```

This pattern allows for flexible form composition:

```jsx
<Form onSubmit={handleSubmit} schema={formSchema}>
  {({ register, formState }) => (
    <>
      <FormField
        control={control}
        name="username"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Username</FormLabel>
            <Input {...field} />
            <FormMessage />
          </FormItem>
        )}
      />
      {/* Other form fields */}
    </>
  )}
</Form>
```

### 2. Render Props

The Form component uses render props to provide form state and methods:

```jsx
<Form onSubmit={handleSubmit} schema={formSchema}>
  {({ register, formState, control }) => (
    // Form content
  )}
</Form>
```

### 3. Higher-Order Components

The FieldWrapper component wraps other form components to provide consistent layout and error handling:

```jsx
// From src/components/ui/Form/FieldWrapper.jsx
export const FieldWrapper = ({
  label,
  error,
  children,
  className,
  hasErrorBelow = true,
}) => {
  return (
    <div className={cn('mb-4', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      {children}
      {error && hasErrorBelow && (
        <p className="mt-1 text-sm text-red-500">
          {typeof error === 'object' ? error?.message : error}
        </p>
      )}
    </div>
  );
};
```

### 4. Forwarded Refs

Most components use `forwardRef` to allow parent components to access their DOM nodes:

```jsx
const Input = forwardRef(
  (
    {
      // props
    },
    ref,
  ) => {
    // component implementation
  },
);
```

## UI Component Issues

### 1. Inconsistent Color Usage

The application uses a mix of hardcoded color values and Tailwind classes:

```jsx
// Hardcoded color values
'bg-[#754445] text-white'
'text-[#EB5757] border border-[#EB5757]'

// Tailwind classes
'bg-gray-200 rounded-lg font-semibold'
'text-gray-500 bg-white border border-gray-200 font-normal'
```

This inconsistency makes it harder to maintain a consistent color scheme and adapt to theme changes.

### 2. Limited Accessibility

Some components lack proper accessibility features:

```jsx
// Missing aria-label
<button
  className="..."
  onClick={toggleDropdown}
>
  <span className="sr-only">Open user menu</span>
  <img
    className="w-8 h-8 rounded-full"
    src="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
    alt="user photo"
  />
</button>
```

### 3. Inconsistent Component APIs

Component APIs are inconsistent across the application:

```jsx
// Button component uses variant and hover
<Button variant="submit" hover="submit" />

// RadioTab uses options array
<RadioTab options={[{ key: 'Option 1', value: 'option1' }]} />

// Select uses different option format
<Select options={[{ label: 'Option 1', value: 'option1' }]} />
```

This inconsistency makes it harder for developers to use components without referring to their implementation.

### 4. Limited Component Documentation

Components lack comprehensive documentation, making it harder for developers to understand their capabilities and limitations.

## Recommendations

### 1. Implement a Design Token System

Replace hardcoded color values with a design token system:

```javascript
// Design tokens
const tokens = {
  colors: {
    primary: {
      main: '#754445',
      hover: '#8B4F4F',
      light: '#F3E5E5',
    },
    secondary: {
      main: '#445475',
      hover: '#4F5F8B',
      light: '#E5E8F3',
    },
    danger: {
      main: '#EB5757',
      hover: '#F58A8A',
      light: '#FDEEEE',
    },
    // ... other colors
  },
  // ... other tokens
};

// Usage in components
const buttonVariants = cva(
  'w-full inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 transition duration-300 rounded-xl py-2 px-4',
  {
    variants: {
      variant: {
        submit: `bg-[${tokens.colors.primary.main}] text-white`,
        secondary: `bg-[${tokens.colors.secondary.main}] text-white hover:bg-[${tokens.colors.secondary.hover}]`,
        // ... other variants
      },
      // ... other variant types
    },
  },
);
```

### 2. Improve Accessibility

Enhance components with proper accessibility attributes:

```jsx
// Improved button with accessibility
<Button
  aria-label="Open user menu"
  aria-expanded={isOpen}
  aria-controls="user-menu"
  onClick={toggleDropdown}
>
  <img
    className="w-8 h-8 rounded-full"
    src={userAvatar}
    alt={`${user.name}'s profile picture`}
  />
</Button>

// Accessible dropdown menu
<div
  id="user-menu"
  role="menu"
  aria-orientation="vertical"
  aria-labelledby="user-menu-button"
  className={cn(
    'absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
    { hidden: !isOpen }
  )}
>
  {/* Menu items */}
</div>
```

### 3. Standardize Component APIs

Create a consistent API across all components:

```jsx
// Consistent option format
type Option = {
  label: string;
  value: string | number;
};

// Consistent component props
interface SelectProps {
  options: Option[];
  value: string | number;
  onChange: (value: string | number) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  // ... other props
}

// Usage
<Select
  options={[{ label: 'Option 1', value: 'option1' }]}
  value={selectedValue}
  onChange={handleChange}
/>

<RadioGroup
  options={[{ label: 'Option 1', value: 'option1' }]}
  value={selectedValue}
  onChange={handleChange}
/>
```

### 4. Create a Component Documentation System

Implement a component documentation system using Storybook or a similar tool:

```jsx
// Button.stories.jsx
import { Button } from './Button';

export default {
  title: 'Components/Button',
  component: Button,
  argTypes: {
    variant: {
      control: 'select',
      options: ['submit', 'secondary', 'outline', 'danger', 'back', 'action', 'icon', 'noColor', 'file', 'link'],
    },
    hover: {
      control: 'select',
      options: ['submit', 'action', 'highlight', 'danger', 'pagination', 'darkGlow', 'outline', 'file', 'link'],
    },
    isLoading: {
      control: 'boolean',
    },
    // ... other props
  },
};

const Template = args => <Button {...args} />;

export const Primary = Template.bind({});
Primary.args = {
  variant: 'submit',
  hover: 'submit',
  children: 'Submit',
};

export const Secondary = Template.bind({});
Secondary.args = {
  variant: 'secondary',
  hover: 'action',
  children: 'Secondary',
};

// ... other variants
```

### 5. Implement Theme Support

Add support for different themes (light/dark mode, different color schemes):

```javascript
// Theme configuration
const themes = {
  light: {
    colors: {
      primary: {
        main: '#754445',
        hover: '#8B4F4F',
        light: '#F3E5E5',
      },
      // ... other colors
    },
    // ... other tokens
  },
  dark: {
    colors: {
      primary: {
        main: '#A05C5D',
        hover: '#B46A6B',
        light: '#2C1A1A',
      },
      // ... other colors
    },
    // ... other tokens
  },
};

// Theme context
const ThemeContext = createContext({
  theme: 'light',
  setTheme: () => {},
});

// Theme provider
const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <div className={theme === 'dark' ? 'dark' : ''}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
};

// Usage in components
const { theme } = useContext(ThemeContext);
const tokens = themes[theme];

const buttonVariants = cva(
  'w-full inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 transition duration-300 rounded-xl py-2 px-4',
  {
    variants: {
      variant: {
        submit: `bg-[${tokens.colors.primary.main}] text-white`,
        // ... other variants
      },
      // ... other variant types
    },
  },
);
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
