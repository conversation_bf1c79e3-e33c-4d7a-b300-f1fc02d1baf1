const AppError = require('./appError');

/**
 * Error class for resource not found errors
 */
class NotFoundError extends AppError {
  /**
   * Creates a new NotFoundError
   * 
   * @param {string} resource - Resource type that was not found
   * @param {string|number} identifier - Resource identifier
   * @param {string} message - Optional custom error message
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(
    resource = 'Resource',
    identifier = null,
    message = null,
    originalError = null
  ) {
    const errorMessage = message || `${resource} not found${identifier ? ` with identifier: ${identifier}` : ''}`;
    super(errorMessage, 'NOT_FOUND', { resource, identifier }, originalError);
    this.resource = resource;
    this.identifier = identifier;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 404; // Not Found
  }
}

module.exports = NotFoundError;
