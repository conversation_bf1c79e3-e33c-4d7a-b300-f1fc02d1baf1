/**
 * Theme utility for standardizing color usage across the application
 * This helps avoid hardcoded color values in components and ensures consistency
 */

/**
 * Primary color palette - maroon/burgundy tones used throughout the app
 */
export const PRIMARY_COLORS = {
  DEFAULT: '#754445', // Main primary color
  LIGHT: '#8B4F4F',   // Lighter variant for hover states
  DARK: '#614A4C',    // Darker variant for backgrounds
  DARKER: '#795C5F',  // Darker variant for headers
  LIGHTEST: '#f3e5e5', // Very light variant for backgrounds
};

/**
 * Secondary color palette - blue tones used as accent colors
 */
export const SECONDARY_COLORS = {
  DEFAULT: '#445475', // Main secondary color
  LIGHT: '#4F5F8B',   // Lighter variant for hover states
  DARK: '#394463',    // Darker variant
};

/**
 * Success color palette - green tones
 */
export const SUCCESS_COLORS = {
  DEFAULT: '#306F4E', // Main success color
  LIGHT: '#f3fff8',   // Light background for success states
};

/**
 * Error/danger color palette - red tones
 */
export const ERROR_COLORS = {
  DEFAULT: '#EB5757', // Main error color
  LIGHT: '#F58A8A',   // Lighter variant for hover states
  LIGHTEST: '#FFF7F7', // Very light background for error states
};

/**
 * Neutral color palette - grays
 */
export const NEUTRAL_COLORS = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY_50: '#F9FAFB',
  GRAY_100: '#F3F4F6',
  GRAY_200: '#E5E7EB',
  GRAY_300: '#D1D5DB',
  GRAY_400: '#9CA3AF',
  GRAY_500: '#6B7280',
  GRAY_600: '#4B5563',
  GRAY_700: '#374151',
  GRAY_800: '#1F2937',
  GRAY_900: '#111827',
};

/**
 * Returns the appropriate Tailwind class for a color
 * @param {string} colorType - The type of color (primary, secondary, etc.)
 * @param {string} variant - The variant of the color (default, light, dark, etc.)
 * @returns {string} The corresponding Tailwind class
 */
export const getColorClass = (colorType, variant = 'DEFAULT') => {
  const colorMap = {
    primary: {
      DEFAULT: 'bg-[#754445] text-white',
      light: 'bg-[#8B4F4F] text-white',
      dark: 'bg-[#614A4C] text-white',
      darker: 'bg-[#795C5F] text-white',
      lightest: 'bg-[#f3e5e5]',
      outline: 'text-[#754445] border border-[#754445]',
      'outline-hover': 'text-[#8B4F4F] border border-[#8B4F4F] bg-rose-50',
    },
    secondary: {
      DEFAULT: 'bg-[#445475] text-white',
      light: 'bg-[#4F5F8B] text-white',
      dark: 'bg-[#394463] text-white',
    },
    success: {
      DEFAULT: 'bg-[#306F4E] text-white',
      light: 'bg-[#f3fff8]',
    },
    error: {
      DEFAULT: 'bg-[#EB5757] text-white',
      light: 'bg-[#F58A8A] text-white',
      lightest: 'bg-[#FFF7F7]',
      outline: 'text-[#EB5757] border border-[#EB5757]',
      'outline-hover': 'text-[#F58A8A] border border-[#F58A8A]',
    },
    gray: {
      50: 'bg-gray-50',
      100: 'bg-gray-100',
      200: 'bg-gray-200',
      300: 'bg-gray-300',
      400: 'bg-gray-400',
      500: 'bg-gray-500',
      600: 'bg-gray-600',
      700: 'bg-gray-700',
      800: 'bg-gray-800',
      900: 'bg-gray-900',
    },
  };

  return colorMap[colorType]?.[variant] || '';
};

/**
 * Returns the appropriate text color class
 * @param {string} colorType - The type of color (primary, secondary, etc.)
 * @param {string} variant - The variant of the color (default, light, dark, etc.)
 * @returns {string} The corresponding Tailwind text color class
 */
export const getTextColorClass = (colorType, variant = 'DEFAULT') => {
  const colorMap = {
    primary: {
      DEFAULT: 'text-[#754445]',
      light: 'text-[#8B4F4F]',
      dark: 'text-[#614A4C]',
    },
    secondary: {
      DEFAULT: 'text-[#445475]',
      light: 'text-[#4F5F8B]',
    },
    success: {
      DEFAULT: 'text-[#306F4E]',
    },
    error: {
      DEFAULT: 'text-[#EB5757]',
      light: 'text-[#F58A8A]',
    },
    gray: {
      500: 'text-gray-500',
      600: 'text-gray-600',
      700: 'text-gray-700',
      800: 'text-gray-800',
      900: 'text-gray-900',
    },
    white: {
      DEFAULT: 'text-white',
    },
    black: {
      DEFAULT: 'text-black',
    },
  };

  return colorMap[colorType]?.[variant] || '';
};

/**
 * Returns the appropriate border color class
 * @param {string} colorType - The type of color (primary, secondary, etc.)
 * @param {string} variant - The variant of the color (default, light, dark, etc.)
 * @returns {string} The corresponding Tailwind border color class
 */
export const getBorderColorClass = (colorType, variant = 'DEFAULT') => {
  const colorMap = {
    primary: {
      DEFAULT: 'border-[#754445]',
      light: 'border-[#8B4F4F]',
    },
    secondary: {
      DEFAULT: 'border-[#445475]',
    },
    success: {
      DEFAULT: 'border-[#306F4E]',
    },
    error: {
      DEFAULT: 'border-[#EB5757]',
      light: 'border-[#F58A8A]',
    },
    gray: {
      200: 'border-gray-200',
      300: 'border-gray-300',
      400: 'border-gray-400',
    },
  };

  return colorMap[colorType]?.[variant] || '';
};
