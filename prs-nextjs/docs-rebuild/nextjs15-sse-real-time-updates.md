# Real-Time Updates with Server-Sent Events (SSE) in Next.js 15

This document provides a comprehensive guide on implementing real-time updates using Server-Sent Events (SSE) in the Next.js 15 PRS application.

## Table of Contents

1. [Introduction to SSE](#introduction-to-sse)
2. [SSE vs WebSockets](#sse-vs-websockets)
3. [Core SSE Implementation](#core-sse-implementation)
4. [SSE for Notifications](#sse-for-notifications)
5. [SSE for Approval Workflows](#sse-for-approval-workflows)
6. [SSE for Dashboard Updates](#sse-for-dashboard-updates)
7. [SSE for Delivery Tracking](#sse-for-delivery-tracking)
8. [Client-Side SSE Integration](#client-side-sse-integration)
9. [Error Handling and Reconnection](#error-handling-and-reconnection)
10. [Performance Considerations](#performance-considerations)

## Introduction to SSE

Server-Sent Events (SSE) is a standard that enables servers to push updates to clients over HTTP. Unlike WebSockets, SSE is a one-way communication channel from server to client, making it ideal for scenarios where clients need to receive real-time updates without sending data back to the server.

### Key Features of SSE

- **One-way communication**: Server pushes data to clients
- **Built on HTTP**: Uses standard HTTP protocol
- **Automatic reconnection**: Browsers automatically reconnect if the connection is lost
- **Event-based**: Supports named events and data
- **Text-based**: Data is sent as text, typically JSON

## SSE vs WebSockets

For the PRS application, SSE is preferred over WebSockets for the following reasons:

| Feature | SSE | WebSockets |
|---------|-----|------------|
| Protocol | HTTP | WebSocket protocol |
| Communication | One-way (server to client) | Two-way |
| Connection overhead | Lower | Higher |
| Automatic reconnection | Yes (built-in) | No (must be implemented) |
| Message types | Text only | Text and binary |
| Browser support | All modern browsers | All modern browsers |
| Implementation complexity | Simpler | More complex |
| Use case | Real-time updates, notifications | Chat, collaborative editing |

For the PRS system, most real-time updates are one-way (server to client), making SSE the more efficient choice.

## Core SSE Implementation

### 1. SSE Utility Functions

```typescript
// lib/sse/utils.ts
import { NextRequest } from 'next/server'

export function createSSEResponse(
  handler: (controller: ReadableStreamDefaultController) => void
) {
  const stream = new ReadableStream({
    start: handler
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}

export function sendSSEEvent(
  controller: ReadableStreamDefaultController,
  data: any,
  eventName?: string
) {
  let message = ''
  
  if (eventName) {
    message += `event: ${eventName}\n`
  }
  
  message += `data: ${JSON.stringify(data)}\n\n`
  controller.enqueue(message)
}
```

### 2. Base SSE Route Handler

```typescript
// lib/sse/baseHandler.ts
import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { createSSEResponse, sendSSEEvent } from './utils'

export function createSSEHandler(
  dataFetcher: (userId: number) => Promise<any>,
  interval: number = 5000,
  eventName?: string
) {
  return async function handler(req: NextRequest) {
    const session = await getServerSession(authOptions)
    if (!session) {
      return new Response('Unauthorized', { status: 401 })
    }
    
    const userId = parseInt(session.user.id)
    
    return createSSEResponse((controller) => {
      const sendUpdate = async () => {
        try {
          const data = await dataFetcher(userId)
          sendSSEEvent(controller, data, eventName)
          
          // Schedule next update
          setTimeout(sendUpdate, interval)
        } catch (error) {
          console.error('SSE error:', error)
          controller.error(error)
        }
      }
      
      // Start sending updates
      sendUpdate()
    })
  }
}
```

## SSE for Notifications

### 1. Notification SSE Route

```typescript
// app/api/sse/notifications/route.ts
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/db'
import { createSSEHandler } from '@/lib/sse/baseHandler'

async function fetchNotifications(userId: number) {
  return prisma.notification.findMany({
    where: { 
      userId,
      read: false 
    },
    orderBy: { createdAt: 'desc' },
    take: 10
  })
}

export const GET = createSSEHandler(
  fetchNotifications,
  5000,
  'notifications'
)
```

### 2. Notification Service

```typescript
// lib/notifications.ts
import { prisma } from '@/lib/db'

type NotificationType = 
  | 'REQUISITION_CREATED'
  | 'REQUISITION_APPROVED'
  | 'REQUISITION_REJECTED'
  | 'APPROVAL_REQUESTED'
  | 'PO_CREATED'
  | 'DELIVERY_UPDATED'
  | 'PAYMENT_REQUESTED'

interface NotificationData {
  type: NotificationType
  title: string
  message: string
  referenceId: number
  referenceType: string
  metadata?: Record<string, any>
}

export async function notifyUsers(userIds: number[], data: NotificationData) {
  // Create notifications for each user
  await prisma.notification.createMany({
    data: userIds.map(userId => ({
      userId,
      type: data.type,
      title: data.title,
      message: data.message,
      referenceId: data.referenceId,
      referenceType: data.referenceType,
      metadata: data.metadata || {},
      read: false
    }))
  })
  
  return true
}
```

## SSE for Approval Workflows

### 1. Approval Status SSE Route

```typescript
// app/api/sse/approvals/[id]/route.ts
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { createSSEResponse, sendSSEEvent } from '@/lib/sse/utils'

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions)
  if (!session) {
    return new Response('Unauthorized', { status: 401 })
  }
  
  const requisitionId = parseInt(params.id)
  
  return createSSEResponse((controller) => {
    const checkApprovals = async () => {
      try {
        // Get current approval status
        const approvals = await prisma.approval.findMany({
          where: { requisitionId },
          include: { user: true },
          orderBy: { level: 'asc' }
        })
        
        sendSSEEvent(controller, approvals, 'approvals')
        
        // Schedule next check
        setTimeout(checkApprovals, 5000)
      } catch (error) {
        console.error('SSE error:', error)
        controller.error(error)
      }
    }
    
    // Start checking
    checkApprovals()
  })
}
```

### 2. Approval Action with SSE Update

```typescript
// app/requisitions/[id]/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'

export async function approveRequisition(
  requisitionId: number,
  comment: string
) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')
  
  const userId = parseInt(session.user.id)
  
  // Get the requisition
  const requisition = await prisma.requisition.findUnique({
    where: { id: requisitionId },
    include: { approvals: true }
  })
  
  if (!requisition) throw new Error('Requisition not found')
  
  // Find the current user's approval
  const approval = requisition.approvals.find(
    a => a.userId === userId && a.status === 'pending'
  )
  
  if (!approval) throw new Error('No pending approval found for this user')
  
  // Update the approval
  await prisma.approval.update({
    where: { id: approval.id },
    data: { 
      status: 'approved',
      comment,
      approvedAt: new Date()
    }
  })
  
  // Check if all approvals are complete
  const pendingApprovals = await prisma.approval.findMany({
    where: { 
      requisitionId,
      status: 'pending'
    }
  })
  
  // If all approved, update requisition status
  if (pendingApprovals.length === 0) {
    await prisma.requisition.update({
      where: { id: requisitionId },
      data: { status: 'approved' }
    })
    
    // Notify the requisition creator
    await notifyUsers([requisition.createdById], {
      type: 'REQUISITION_APPROVED',
      title: 'Requisition Approved',
      message: `Requisition #${requisition.rsNumber} has been fully approved`,
      referenceId: requisitionId,
      referenceType: 'requisition'
    })
  }
  
  // Revalidate the page
  revalidatePath(`/requisitions/${requisitionId}`)
  
  return { success: true }
}
```

## SSE for Dashboard Updates

### 1. Dashboard SSE Route

```typescript
// app/api/sse/dashboard/route.ts
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/db'
import { createSSEHandler } from '@/lib/sse/baseHandler'

async function fetchDashboardData(userId: number) {
  // Get user role
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { role: true }
  })
  
  // Get pending approvals count
  const pendingApprovals = await prisma.approval.count({
    where: { 
      userId,
      status: 'pending' 
    }
  })
  
  // Get my requisitions count
  const myRequisitions = await prisma.requisition.count({
    where: { 
      createdById: userId,
      status: { not: 'closed' } 
    }
  })
  
  // Get assigned requisitions count (for purchasing staff)
  const assignedRequisitions = user.role.name.includes('Purchasing')
    ? await prisma.requisition.count({
        where: { 
          assignedToId: userId,
          status: { not: 'closed' } 
        }
      })
    : 0
  
  return {
    pendingApprovals,
    myRequisitions,
    assignedRequisitions,
    timestamp: new Date().toISOString()
  }
}

export const GET = createSSEHandler(
  fetchDashboardData,
  10000,
  'dashboard'
)
```
