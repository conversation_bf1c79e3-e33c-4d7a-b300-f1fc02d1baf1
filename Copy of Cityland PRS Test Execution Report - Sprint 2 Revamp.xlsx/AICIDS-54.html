<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:line-through;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{background-color:#cc0000;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1527566062C0" style="width:170px;" class="column-headers-background">A</th><th id="1527566062C1" style="width:208px;" class="column-headers-background">B</th><th id="1527566062C2" style="width:320px;" class="column-headers-background">C</th><th id="1527566062C3" style="width:270px;" class="column-headers-background">D</th><th id="1527566062C4" style="width:335px;" class="column-headers-background">E</th><th id="1527566062C5" style="width:313px;" class="column-headers-background">F</th><th id="1527566062C6" style="width:313px;" class="column-headers-background">G</th><th id="1527566062C7" style="width:154px;" class="column-headers-background">H</th><th id="1527566062C8" style="width:310px;" class="column-headers-background">I</th><th id="1527566062C9" style="width:196px;" class="column-headers-background">J</th></tr></thead><tbody><tr style="height: 20px"><th id="1527566062R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">Status</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1527566062R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="10"></td></tr><tr style="height: 20px"><th id="1527566062R7" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">8</div></th><td class="s3">TC-54-001</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verify GIB Verifier Portal landing page</td><td class="s3"></td><td class="s4"><a target="_blank" href="https://dev.identityscape.ph/login">1. User go to https://dev.identityscape.ph/login</a></td><td class="s3">1. User should see the mockup login page<br>2. User should see the ff:<br>a. username and password field (no functionality)<br>b. login botton (no functionality)<br>c. create account</td><td class="s3"></td><td class="s5"></td><td class="s4"><a target="_blank" href="https://aboitiz.monday.com/boards/**********/pulses/**********?asset_id=**********">https://aboitiz.monday.com/boards/**********/pulses/**********?asset_id=**********</a></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R8" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">9</div></th><td class="s3">TC-54-002</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verify redirection of Create Account</td><td class="s3"></td><td class="s3">1. Click Create Account</td><td class="s3">1. user should be redirected to new Interaction under GIB Service under ProofSpace</td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R9" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">10</div></th><td class="s3">TC-54-003</td><td class="s3">GIB Verifier Portal</td><td class="s6">Verifry Proofspace credentials - new customer (no app)</td><td class="s7">1. user don&#39;t have a proofspace app</td><td class="s6">1. Click Create Account</td><td class="s6">1. user will be redirected to appstore/playstore</td><td class="s7"></td><td class="s5"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 20px"><th id="1527566062R10" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">11</div></th><td class="s3">TC-54-004</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verifry Proofspace credentials - new customer</td><td class="s3"></td><td class="s3">1. Click Create Account</td><td class="s3">1. User should fill out all the requirements first<br>a. Email<br>b. Mobile Number<br>c. Philippine ID<br>d. Biometrics<br>e. User ID</td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R11" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">12</div></th><td class="s3">TC-54-005</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verifry Proofspace credentials - old/existing customer</td><td class="s3"></td><td class="s3">1. Click Create Account</td><td class="s3">1. user should see his existing verified requirements:<br>a. Email<br>b. Mobile Number<br>c. Philippine ID<br>d. Biometrics<br>e. User ID<br></td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R12" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">13</div></th><td class="s3">TC-54-006</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verify redirection of Proceed button</td><td class="s3"></td><td class="s3">1. Click Proceed button</td><td class="s3">1.  Should be able to forward payload from ProofSpace to GIB Create Account Portal<br></td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R13" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">14</div></th><td class="s3">TC-54-007</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verify GIB Create Account Portal</td><td class="s3"></td><td class="s3"></td><td class="s3">1. User should see all the details from proofspace to GIB portal</td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R14" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">15</div></th><td class="s3">TC-54-008</td><td class="s3">GIB Verifier Portal</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3">2. If there are no data found on the filed, user should see blank</td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R15" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">16</div></th><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3">3. All details from Verified ID<br>a. Including Email and Mobile Number<br>b. Full Name, Birth Date, Address, Gender and other details</td><td class="s3"></td><td class="s5"></td><td class="s8">Font color minor issues<br>Button Sizes</td><td class="s3"></td></tr><tr style="height: 236px"><th id="1527566062R16" style="height: 236px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 236px">17</div></th><td class="s3"></td><td class="s3"></td><td class="s3">Verify mask fields</td><td class="s3"></td><td class="s3"></td><td class="s9">1. User should see some of the fields are mask:<br>Partial Mask:<br>a. <br>Mobile Number<br>b. <br>Email<br><br>c. Document Number<br><br>d. Full Mask<br>e. Date of Birth<br>f. Place of Birth</td><td class="s3"></td><td class="s5"></td><td class="s3">Tooltip was applied to all text even text is not long</td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R17" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">18</div></th><td class="s3">TC-54-009</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verify Submit button</td><td class="s3"></td><td class="s3">1. Click submit button</td><td class="s3">1. User should see:<br>a. &quot;You just have created a GIB basic saver account&quot;<br>b. Return to login</td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R18" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">19</div></th><td class="s3">TC-54-010</td><td class="s3">GIB Verifier Portal</td><td class="s3">Verify redirection of login</td><td class="s3"></td><td class="s3">1. Click Login</td><td class="s3">1. User should be redirected to login</td><td class="s3"></td><td class="s5"></td><td class="s8">Some instances where after you click Submit button then you will be redirected to login page but when you switch app, it will go back to the Create Account Portal Page</td><td class="s3"></td></tr><tr style="height: 20px"><th id="1527566062R19" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">20</div></th><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s10"></td></tr></tbody></table></div>