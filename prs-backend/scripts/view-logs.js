#!/usr/bin/env node
/**
 * Log Viewer Utility
 * 
 * This script provides a human-readable view of the structured JSON logs.
 * It reads logs from stdin or a file and formats them for easier reading.
 * 
 * Usage:
 *   cat logs/app.log | node scripts/view-logs.js
 *   node scripts/view-logs.js logs/app.log
 *   docker compose logs backend | node scripts/view-logs.js
 */

const fs = require('fs');
const readline = require('readline');
const path = require('path');
const chalk = require('chalk');

// Try to load chalk, but don't fail if it's not available
let colorize = text => text;
let red = text => text;
let yellow = text => text;
let green = text => text;
let blue = text => text;
let gray = text => text;
let bold = text => text;

try {
  const chalk = require('chalk');
  colorize = chalk;
  red = chalk.red;
  yellow = chalk.yellow;
  green = chalk.green;
  blue = chalk.blue;
  gray = chalk.gray;
  bold = chalk.bold;
} catch (e) {
  console.log('Chalk not installed. Run "npm install chalk" for colored output.');
}

// Level colors
const LEVEL_COLORS = {
  10: gray,    // trace
  20: gray,    // debug
  30: green,   // info
  40: yellow,  // warn
  50: red,     // error
  60: red,     // fatal
};

// Level names
const LEVEL_NAMES = {
  10: 'TRACE',
  20: 'DEBUG',
  30: 'INFO ',
  40: 'WARN ',
  50: 'ERROR',
  60: 'FATAL',
};

/**
 * Formats a timestamp
 */
function formatTimestamp(timestamp) {
  if (!timestamp) return '';
  
  let date;
  if (typeof timestamp === 'number') {
    date = new Date(timestamp);
  } else if (typeof timestamp === 'string') {
    date = new Date(timestamp);
  } else {
    return timestamp;
  }
  
  return date.toISOString().replace('T', ' ').substr(0, 19);
}

/**
 * Formats a log entry for human readability
 */
function formatLogEntry(entry) {
  try {
    // Parse the entry if it's a string
    const log = typeof entry === 'string' ? JSON.parse(entry) : entry;
    
    // Extract basic fields
    const level = log.level || 30;
    const timestamp = formatTimestamp(log.time || log.timestamp);
    const message = log.msg || log.message || '';
    const requestId = log.requestId || log['x-request-id'] || (log.request && log.request.id) || '';
    
    // Format the level
    const levelColor = LEVEL_COLORS[level] || (text => text);
    const levelName = LEVEL_NAMES[level] || 'INFO ';
    
    // Build the basic log line
    let logLine = `${gray(timestamp)} ${levelColor(levelName)} ${bold(message)}`;
    
    // Add request context if available
    if (requestId) {
      logLine += ` ${blue(`[${requestId}]`)}`;
    }
    
    // Add user context if available
    if (log.user && log.user.id) {
      logLine += ` ${blue(`User: ${log.user.id}`)}`;
    }
    
    // Add URL if it's an API request
    if (log.request && log.request.url) {
      logLine += ` ${blue(`${log.request.method || 'GET'} ${log.request.url}`)}`;
    }
    
    // Add response info if available
    if (log.response && log.response.statusCode) {
      const statusColor = log.response.statusCode >= 400 ? red : green;
      logLine += ` ${statusColor(`Status: ${log.response.statusCode}`)}`;
      
      if (log.response.responseTime) {
        const timeColor = log.response.responseTime > 500 ? yellow : green;
        logLine += ` ${timeColor(`Time: ${log.response.responseTime.toFixed(2)}ms`)}`;
      }
    }
    
    // Add error info if available
    if (log.error) {
      logLine += `\n  ${red('Error:')} ${log.error.message || log.error}`;
      
      if (log.error.stack && !log.stack) {
        const stackLines = log.error.stack.split('\n').slice(0, 3);
        logLine += `\n  ${red('Stack:')} ${stackLines.join('\n    ')}`;
      }
    }
    
    // Add stack trace if available
    if (log.stack) {
      const stackLines = log.stack.split('\n').slice(0, 3);
      logLine += `\n  ${red('Stack:')} ${stackLines.join('\n    ')}`;
    }
    
    // Add business context if available
    if (log.business) {
      const businessContext = Object.entries(log.business)
        .map(([key, value]) => `${key}=${value}`)
        .join(', ');
      
      logLine += `\n  ${blue('Business:')} ${businessContext}`;
    }
    
    // Add performance info if available
    if (log.performance && (log.performance.slow || log.performance.verySlow)) {
      const perfColor = log.performance.verySlow ? red : yellow;
      logLine += `\n  ${perfColor('Performance:')} Slow response detected`;
      
      if (log.performance.threshold) {
        logLine += ` (threshold: ${log.performance.threshold}ms)`;
      }
    }
    
    return logLine;
  } catch (error) {
    // If we can't parse the entry, return it as-is
    return entry;
  }
}

/**
 * Processes log entries from a readable stream
 */
function processLogs(inputStream) {
  const rl = readline.createInterface({
    input: inputStream,
    output: process.stdout,
    terminal: false
  });
  
  rl.on('line', (line) => {
    // Skip empty lines
    if (!line.trim()) return;
    
    try {
      // Try to parse as JSON
      const formatted = formatLogEntry(line);
      console.log(formatted);
    } catch (error) {
      // If it's not valid JSON, output as-is
      console.log(line);
    }
  });
  
  rl.on('close', () => {
    // End of input
  });
}

/**
 * Main function
 */
function main() {
  // Check if a file path was provided
  const filePath = process.argv[2];
  
  if (filePath) {
    // Read from file
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.error(`File not found: ${fullPath}`);
      process.exit(1);
    }
    
    const inputStream = fs.createReadStream(fullPath);
    processLogs(inputStream);
  } else {
    // Read from stdin
    processLogs(process.stdin);
  }
}

// Run the main function
main();
