'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeIndex(
      'canvass_item_suppliers',
      'unique_canvass_item_order',
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addIndex(
      'canvass_item_suppliers',
      ['canvass_item_id', 'order'],
      {
        unique: true,
        name: 'unique_canvass_item_order',
      },
    );
  },
};
