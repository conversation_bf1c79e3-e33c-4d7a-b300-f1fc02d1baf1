const { z } = require('zod');

// Schema for creating a new example
const createExampleSchema = z.object({
  name: z.string({
    required_error: 'Name is required',
  }).min(3, 'Name must be at least 3 characters'),
  description: z.string({
    required_error: 'Description is required',
  }).min(10, 'Description must be at least 10 characters'),
  status: z.enum(['active', 'inactive', 'pending'], {
    required_error: 'Status is required',
    invalid_type_error: 'Status must be one of: active, inactive, pending',
  }),
  type: z.enum(['type1', 'type2', 'type3'], {
    required_error: 'Type is required',
    invalid_type_error: 'Type must be one of: type1, type2, type3',
  }),
  priority: z.number({
    required_error: 'Priority is required',
  }).int().min(1).max(5),
  relatedItems: z.array(
    z.object({
      name: z.string(),
      value: z.number(),
    })
  ).optional(),
});

// Schema for updating an existing example
const updateExampleSchema = createExampleSchema.partial();

// Schema for example ID parameter
const getExampleParams = z.object({
  id: z.string({
    required_error: 'Example ID is required',
  }),
});

// Schema for sorting examples
const exampleSortSchema = z.array(
  z.array(z.string())
).default([['createdAt', 'DESC']]);

// Schema for filtering examples
const exampleFilterSchema = z.array(
  z.object({
    field: z.string(),
    operator: z.string(),
    value: z.any(),
  })
).default([]);

module.exports = {
  createExampleSchema,
  updateExampleSchema,
  getExampleParams,
  exampleSortSchema,
  exampleFilterSchema,
};
