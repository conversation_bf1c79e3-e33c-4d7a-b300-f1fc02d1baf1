import z from 'zod';
import { emojiRegex } from '@utils/regexUtils';

const submitNoteSchema = z.object({
  comment: z
    .string()
    .max(100, 'Note must be at most 100 characters')
    .refine(
      val => {
        for (const match of val.matchAll(emojiRegex)) {
          return !match;
        }

        return z.NEVER;
      },
      {
        message:
          'Only Letters, Numbers, and Special Characters are allowed. Kindly check entered Notes.',
      },
    ),
});

export { submitNoteSchema };
