<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s5{background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{background-color:#b6d7a8;text-align:center;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#f9cb9c;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{background-color:#ffff00;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{background-color:#9fc5e8;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#9fc5e8;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{background-color:#b6d7a8;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1295657322C0" style="width:124px;" class="column-headers-background">A</th><th id="1295657322C1" style="width:444px;" class="column-headers-background">B</th><th id="1295657322C2" style="width:166px;" class="column-headers-background">C</th><th id="1295657322C3" style="width:82px;" class="column-headers-background">D</th><th id="1295657322C4" style="width:144px;" class="column-headers-background">E</th><th id="1295657322C5" style="width:144px;" class="column-headers-background">F</th><th id="1295657322C6" style="width:82px;" class="column-headers-background">G</th><th id="1295657322C7" style="width:160px;" class="column-headers-background">H</th><th id="1295657322C8" style="width:113px;" class="column-headers-background">I</th><th id="1295657322C9" style="width:111px;" class="column-headers-background">J</th><th id="1295657322C10" style="width:100px;" class="column-headers-background">K</th></tr></thead><tbody><tr style="height: 19px"><th id="1295657322R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td class="s0">Youtrack ID</td><td class="s0">REVAMP FEATURES</td><td class="s1">Tab Name</td><td class="s1">TOTAL TCs</td><td class="s1">INTERNS</td><td class="s1">QA</td><td class="s1">TC Completion %</td><td class="s1">TC Review Status</td><td class="s1">TC Exectution -1 </td><td class="s1">TC Exectution - 2</td><td class="s1">Remarks</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1295657322R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="11">ITEM MANAGEMENT</td></tr><tr style="height: 19px"><th id="1295657322R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3">CITYLANDPRS-1480</td><td class="s3"> [ITEM MANAGEMENT] Allow Decimal to OFM Quanity</td><td class="s3">ITEM MANAGEMENT</td><td class="s4">6</td><td class="s5">Aira</td><td class="s4">Gela</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s5">Aira</td><td class="s5">Gela</td><td class="s7">5/02: for QA</td></tr><tr style="height: 19px"><th id="1295657322R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3">CITYLANDPRS-1481 </td><td class="s3">[ITEM MANAGEMENT] Allow Decimal to Non-OFM Quanity</td><td class="s3">ITEM MANAGEMENT</td><td class="s4">6</td><td class="s5">Aira</td><td class="s4">Gela</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4">Aira</td><td class="s4">Gela</td><td class="s7">5/02: for QA</td></tr><tr style="height: 19px"><th id="1295657322R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s2" colspan="11">REQUISITION SLIP - VIEWING</td></tr><tr style="height: 19px"><th id="1295657322R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3">CITYLANDPRS-1482</td><td class="s3">[REQUISITION SLIP - VIEWING] Requisition Slip Download to PDF</td><td class="s3">RS - VIEWING</td><td class="s4">10</td><td class="s4">Verna</td><td class="s4">Gela</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s2" colspan="11">REQUISITION SLIP - APPROVAL</td></tr><tr style="height: 19px"><th id="1295657322R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s3">CITYLANDPRS-1313</td><td class="s3">[REQUISITION SLIP - APPROVAL] Allow Adding of Notes before Approval of RS</td><td class="s3">RS - APPROVAL</td><td class="s4">11</td><td class="s4">Justine</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s3">CITYLANDPRS-1314</td><td class="s3">[REQUISITION SLIP - APPROVAL] Update Adding of Approver behavior for RS Approval</td><td class="s3">RS - APPROVAL</td><td class="s4">8</td><td class="s4">Aira</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s3">CITYLANDPRS-1492</td><td class="s3">[REQUISITION SLIP - APPROVAL] Enabling RS Optional Approver</td><td class="s3">RS - APPROVAL</td><td class="s4">11</td><td class="s4">Ghienel</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s3">CITYLANDPRS-1299</td><td class="s3">[REQUISITION SLIP - APPROVAL] Allow deletion of Requested Item by the Approvers</td><td class="s3">RS - APPROVAL</td><td class="s4">19</td><td class="s4">Justine</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"><span style="font-family:Calibri,Arial;color:#000000;">Spill over from sp1 w/ blocked TCs<br></span><span style="font-family:Calibri,Arial;color:#00b800;">4/29: TC Recheck done</span></td></tr><tr style="height: 19px"><th id="1295657322R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s2" colspan="11">REQUISITION SLIP - ADDING OF ITEMS</td></tr><tr style="height: 19px"><th id="1295657322R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s3">CITYLANDPRS-1493</td><td class="s3">[REQUISITION SLIP - ADDING OF ITEMS] Adding of Non-OFM Items for existing Non-OFM Items</td><td class="s3">RS - ADDING OF ITEMS</td><td class="s4">21</td><td class="s4">Ghienel</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"><span style="font-family:Calibri,Arial;color:#000000;">For recheck for new update in AC<br></span><span style="font-family:Calibri,Arial;color:#00b800;">4/29: TC Recheck done</span></td></tr><tr style="height: 19px"><th id="1295657322R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s2" colspan="11">CANVASS - CREATION</td></tr><tr style="height: 19px"><th id="1295657322R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s3">CITYLANDPRS-1301</td><td class="s3">[CANVASS - CREATION] Entering of OFM and Non-OFM Quantity per Supplier</td><td class="s3">CANVASS CREATION</td><td class="s4">42</td><td class="s4">Ghienel</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s3">CITYLANDPRS-1495</td><td class="s3">[CANVASS - CREATION] Canvass Status updates</td><td class="s3">CANVASS CREATION</td><td class="s4">6</td><td class="s4">Verna</td><td class="s4">Me-Ann</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4">Verna</td><td class="s4">Cherry</td><td class="s7">4/30: for QA</td></tr><tr style="height: 19px"><th id="1295657322R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s3">CITYLANDPRS-1496</td><td class="s3">[CANVASS - CREATION] Item Group</td><td class="s3">CANVASS CREATION</td><td class="s4">50</td><td class="s4">Justine</td><td class="s4">Me-Ann</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3">4/30: Updated Test Cases</td></tr><tr style="height: 19px"><th id="1295657322R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s3">CITYLANDPRS-1315</td><td class="s3">[CANVASS - CREATION] Update Table Behavior for Canvass Sheet (for Create and Viewing)</td><td class="s3">CANVASS CREATION</td><td class="s4">28</td><td class="s4">Justine</td><td class="s4">Me-Ann</td><td class="s6">100%</td><td class="s8">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s2" colspan="11">PURCHASE ORDER - REVIEW</td></tr><tr style="height: 19px"><th id="1295657322R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s3">CITYLANDPRS-1497</td><td class="s3">[PURCHASE ORDER - REVIEW] Add Discounts Field and Computation if the Purchase Order</td><td class="s3">PURCHASE ORDER - VIEWING</td><td class="s4">17</td><td class="s4">Verna</td><td class="s4">Me-Ann</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s2" colspan="11">DELIVERY REPORT</td></tr><tr style="height: 19px"><th id="1295657322R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s3">CITYLANDPRS-1498</td><td class="s3">[DELIVERY REPORT] Allow the Purchasing Staff to create the Delivery Receipt on behalf of the Requester</td><td class="s3">DELIVERY RECEIPT </td><td class="s4">2</td><td class="s4">Aira</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s8">Completed</td><td class="s4">Justine</td><td class="s4">Gela</td><td class="s7">5/02: for QA</td></tr><tr style="height: 19px"><th id="1295657322R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s3">CITYLANDPRS-1499</td><td class="s3">[DELIVERY REPORT] Multiple DR per PO</td><td class="s3">DELIVERY RECEIPT</td><td class="s4"></td><td class="s4">Regel</td><td class="s4">Cherry</td><td class="s9">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s3">CITYLANDPRS-1472</td><td class="s3">[DELIVERY REPORT] Update Delivery Receipt Form(for Create and Viewing])</td><td class="s3"> DELIVERY REPORT</td><td class="s4"></td><td class="s4">Regel</td><td class="s4">Gela</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4">Cherry</td><td class="s4">Me-Ann</td><td class="s7">5/02: for QA</td></tr><tr style="height: 19px"><th id="1295657322R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s3">CITYLANDPRS-1458</td><td class="s3">[DELIVERY REPORT] Update Delivery Receipt Items Table (Create and Viewing)</td><td class="s3">DELIVERY REPORT</td><td class="s4">13</td><td class="s4">Verna</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4">Verna</td><td class="s4">Cherry</td><td class="s7">5/02: for QA</td></tr><tr style="height: 19px"><th id="1295657322R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s2" colspan="11">INVOICE</td></tr><tr style="height: 19px"><th id="1295657322R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s3">CITYLANDPRS-1304</td><td class="s3">[INVOICE] Invoice Entry Points and Invoice Creation</td><td class="s3">INVOICE </td><td class="s4"></td><td class="s4">Cherry</td><td class="s4">Me-Ann</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s3">CITYLANDPRS-1500 </td><td class="s3">[INVOICE] Adding of Invoice Tab in Related Documents </td><td class="s3">INVOICE </td><td class="s4">17</td><td class="s4">Justine</td><td class="s4">Me-Ann</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"><span style="font-family:Calibri,Arial;color:#000000;">For recheck for new update in AC <br><br></span><span style="font-family:Calibri,Arial;color:#00b800;">05/02: TC Recheck done</span></td></tr><tr style="height: 19px"><th id="1295657322R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s3">CITYLANDPRS-1501 </td><td class="s3">[INVOICE] Adding of Invoice Tab in Request History <br></td><td class="s3">INVOICE </td><td class="s4">14</td><td class="s4">Aira</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s2" colspan="11">NON - RS</td></tr><tr style="height: 19px"><th id="1295657322R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s3">CITYLANDPRS-1309 </td><td class="s3">[NON-RS] Update Adding of Approver behavior for Non-RS Approval <br></td><td class="s3">NON-RS</td><td class="s4">16</td><td class="s4">Ghienel</td><td class="s4">Cherry</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4">Ghienel</td><td class="s4">Gela</td><td class="s7">4/30: FE and BE for QA</td></tr><tr style="height: 19px"><th id="1295657322R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s3">CITYLANDPRS-1310 </td><td class="s3">[NON-RS] Allow Adding of Notes before Approval of Non-RS </td><td class="s3">NON-RS</td><td class="s4"></td><td class="s8">Regel</td><td class="s8">Me-Ann</td><td class="s9">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s3"> CITYLANDPRS-1502 </td><td class="s3"> [NON-RS] Non-RS Payment Request Download to PDF<br></td><td class="s3">NON-RS</td><td class="s4">19</td><td class="s4">Justine</td><td class="s4">Gela</td><td class="s6">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1295657322R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s3"> CITYLANDPRS-1306</td><td class="s10">[NON-RS] Update Non-RS Form</td><td class="s3">NON-RS</td><td class="s4">64</td><td class="s4">Aira</td><td class="s4">Cherry</td><td class="s9">100%</td><td class="s4">Completed</td><td class="s4"></td><td class="s4"></td><td class="s3"><span style="font-family:Calibri,Arial;color:#000000;">Spill over from sp1 w/ blocked TCs<br></span><span style="font-family:Calibri,Arial;color:#00b800;">4/29: TC Recheck done<br></span><span style="font-family:Calibri,Arial;color:#000000;">4/30: TC Update</span></td></tr></tbody></table></div>