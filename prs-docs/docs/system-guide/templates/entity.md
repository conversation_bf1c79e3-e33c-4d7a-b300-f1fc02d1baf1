# Entity Template

This template provides a standard structure for creating new domain entities in the PRS system using Zod for validation.

## Usage Instructions

1. Replace `EntityName` with your entity name
2. Define validation schemas
3. Export the schemas

## Template Code

```javascript
/**
 * Template for creating a new domain entity
 * 
 * Instructions:
 * 1. Replace EntityName with your entity name
 * 2. Define validation schemas
 * 3. Export the schemas
 */
const { z } = require('zod');
const { createNumberSchema, stringFieldError } = require('../../utils/validationUtils');

/**
 * Schema for creating a new entity
 */
const createEntityNameSchema = z
  .object({
    // Define fields with validation rules
    name: z
      .string(stringFieldError('Name'))
      .trim()
      .min(1, 'Name is required')
      .max(100, 'Name must not exceed 100 characters')
      .regex(
        /^[A-Za-z0-9'\s.-Ññ]+$/,
        'Name can only contain letters, numbers, spaces and specific special characters: -\'."',
      ),
    
    description: z
      .string(stringFieldError('Description'))
      .trim()
      .max(500, 'Description must not exceed 500 characters')
      .optional(),
    
    type: z
      .enum(['TYPE_A', 'TYPE_B', 'TYPE_C'], {
        invalid_type_error: 'Invalid type',
        required_error: 'Type is required',
      }),
    
    status: z
      .enum(['ACTIVE', 'INACTIVE', 'PENDING'], {
        invalid_type_error: 'Invalid status',
        required_error: 'Status is required',
      })
      .default('ACTIVE'),
    
    // Additional fields...
  })
  .strict();

/**
 * Schema for updating an existing entity
 */
const updateEntityNameSchema = z
  .object({
    // Define fields with validation rules (similar to create schema but with most fields optional)
    name: z
      .string(stringFieldError('Name'))
      .trim()
      .min(1, 'Name is required')
      .max(100, 'Name must not exceed 100 characters')
      .optional(),
    
    // Additional fields...
  })
  .strict();

/**
 * Schema for getting an entity by ID
 */
const getEntityNameByIdSchema = z
  .object({
    id: createNumberSchema('Entity ID'),
  })
  .strict();

module.exports = {
  createEntityNameSchema,
  updateEntityNameSchema,
  getEntityNameByIdSchema,
};
```

## Key Features

1. **Zod Validation**: Uses Zod for schema validation
2. **Strict Mode**: Enforces strict validation (no unknown fields)
3. **Detailed Error Messages**: Provides detailed error messages for validation failures
4. **Type Safety**: Ensures type safety for all fields
5. **Default Values**: Provides default values for certain fields

## Common Schema Types

1. **createEntityNameSchema**: Schema for creating a new entity
2. **updateEntityNameSchema**: Schema for updating an existing entity
3. **getEntityNameByIdSchema**: Schema for getting an entity by ID
4. **customActionSchema**: Schema for custom actions on an entity

## Validation Features

1. **String Validation**: Length, regex patterns, trimming
2. **Number Validation**: Min/max values, integer validation
3. **Enum Validation**: Restricted set of values
4. **Boolean Validation**: Type checking
5. **Array Validation**: Item validation, length restrictions
6. **Object Validation**: Nested object validation
7. **Optional Fields**: Fields that can be omitted

## Best Practices

1. Use strict mode to prevent unknown fields
2. Provide detailed error messages
3. Use regex patterns for complex string validation
4. Make update schema fields optional
5. Use utility functions for common validation patterns
