# Master Data Management Workflow

This document outlines the workflow for managing master data in the PRS system, including companies, departments, projects, and other reference data.

## Master Data Types

| Data Type | Description |
|-----------|-------------|
| Companies | Legal entities withtin Cityland that can create requisitions and purchase orders |
| Departments | Organizational units within Cityland |
| Projects | Projects under companies with specific objectives and budgets |

## Detailed Workflow Steps

### 1. Master Data Creation

**Actor**: Admin or Master Data Manager

**Actions**:
- Create new master data record
- Fill in required information
- Set status (active/inactive)
- Define relationships to other master data

**Business Rules**:
- Codes must be unique within their type
- Required fields must be filled
- Hierarchical relationships must be valid
- Some master data may require approval before activation

### 2. Master Data Validation

**Actor**: System or Master Data Manager

**Actions**:
- Validate data against business rules
- Check for duplicates
- Verify relationships
- Ensure data integrity

**Business Rules**:
- Validation rules vary by data type
- Some validations may be automated
- Critical master data may require additional verification
- Validation errors must be resolved before activation

### 3. Master Data Activation

**Actor**: Admin or Master Data Manager

**Actions**:
- Review validated data
- Activate the master data record
- Notify relevant users

**Business Rules**:
- Only validated data can be activated
- Some master data may require approval before activation
- Activation may trigger system updates
- Activation history is maintained for audit purposes

### 4. Master Data Maintenance

**Actor**: Admin or Master Data Manager

**Actions**:
- Update existing master data
- Change status (active/inactive)
- Manage relationships
- Archive obsolete data

**Business Rules**:
- Some fields may be immutable after creation
- Changes to active master data may require approval
- Changes may affect existing transactions
- Historical data must be preserved for audit purposes

### 5. Master Data Deactivation

**Actor**: Admin or Master Data Manager

**Actions**:
- Deactivate master data record
- Provide reason for deactivation
- Handle dependencies

**Business Rules**:
- Deactivation may be restricted if data is in use
- Dependencies must be resolved before deactivation
- Deactivation history is maintained for audit purposes
- Deactivated data may still be visible in historical transactions

## Example Scenarios

### Scenario 1: New Department Creation

1. Admin creates a new department record
2. Admin fills in department code, name, and company
3. System validates the data (unique code, valid company)
4. Admin activates the department
5. Department is now available for selection in requisitions

### Scenario 2: Project Deactivation

1. Project Manager requests project deactivation
2. Admin checks for dependencies (active requisitions, purchase orders)
3. Admin resolves dependencies (reassign or close)
4. Admin deactivates the project with reason
5. Project is no longer available for new transactions but remains visible in existing ones

### Scenario 3: Company Structure Update

1. Admin updates company organizational structure
2. Admin modifies department relationships
3. System validates changes against existing data
4. Admin approves changes
5. System updates all affected relationships
6. Users see updated structure in selection lists

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/masterDataController.js`
- **Service**: `src/app/services/masterDataService.js`
- **Repository**: `src/infra/repositories/masterDataRepository.js`
- **Entity**: `src/domain/entities/masterDataEntity.js`
- **Constants**: `src/domain/constants/masterDataConstants.js`

### Master Data Management Implementation

```javascript
// Example master data management logic
async function createMasterData(type, data, userId, transaction) {
  // Validate data based on type
  validateMasterData(type, data);
  
  // Check for duplicates
  await checkDuplicates(type, data);
  
  // Create master data record
  const masterData = await getMasterDataRepository(type).create({
    ...data,
    createdBy: userId,
    status: data.status || 'ACTIVE',
  }, { transaction });
  
  // Handle relationships
  if (data.relationships) {
    await createMasterDataRelationships(type, masterData.id, data.relationships, transaction);
  }
  
  // Log creation event
  await logMasterDataEvent(type, masterData.id, 'CREATED', userId, transaction);
  
  return masterData;
}

// Get appropriate repository based on master data type
function getMasterDataRepository(type) {
  switch (type) {
    case 'COMPANY':
      return companyRepository;
    case 'DEPARTMENT':
      return departmentRepository;
    case 'PROJECT':
      return projectRepository;
    case 'UNIT':
      return unitRepository;
    case 'CATEGORY':
      return categoryRepository;
    case 'LOCATION':
      return locationRepository;
    case 'CURRENCY':
      return currencyRepository;
    case 'TAX_CODE':
      return taxCodeRepository;
    default:
      throw new Error(`Unsupported master data type: ${type}`);
  }
}

// Update master data
async function updateMasterData(type, id, data, userId, transaction) {
  // Get existing record
  const existingData = await getMasterDataRepository(type).findOne({
    where: { id },
    transaction,
  });
  
  if (!existingData) {
    throw new Error(`${type} with ID ${id} not found`);
  }
  
  // Check if update is allowed
  checkUpdatePermissions(type, existingData, data);
  
  // Validate updated data
  validateMasterData(type, { ...existingData, ...data });
  
  // Check for duplicates if key fields changed
  if (hasKeyFieldChanges(type, existingData, data)) {
    await checkDuplicates(type, data, id);
  }
  
  // Update master data record
  const updatedData = await getMasterDataRepository(type).update(
    { id },
    {
      ...data,
      updatedBy: userId,
      updatedAt: new Date(),
    },
    { transaction, returning: true }
  );
  
  // Update relationships if needed
  if (data.relationships) {
    await updateMasterDataRelationships(type, id, data.relationships, transaction);
  }
  
  // Log update event
  await logMasterDataEvent(type, id, 'UPDATED', userId, transaction);
  
  return updatedData[1][0];
}
```

## Common Issues and Solutions

### Issue 1: Duplicate Master Data

**Cause**: Attempt to create master data with non-unique identifiers.

**Solution**:
- Implement strict validation for unique fields
- Provide search functionality before creation
- Consider fuzzy matching for similar names
- Implement merge functionality for duplicates

### Issue 2: Dependency Conflicts

**Cause**: Attempt to deactivate master data that is in use.

**Solution**:
- Perform dependency check before deactivation
- Provide tools to resolve dependencies
- Allow soft deactivation (no new usage but existing preserved)
- Implement replacement functionality

### Issue 3: Hierarchical Data Integrity

**Cause**: Invalid parent-child relationships or circular references.

**Solution**:
- Validate hierarchical relationships during creation/update
- Prevent circular references
- Ensure parent exists before creating child
- Handle cascading updates carefully
