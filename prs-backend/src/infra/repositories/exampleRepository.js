const BaseRepository = require('./baseRepository');

/**
 * Example repository that extends BaseRepository
 */
class ExampleRepository extends BaseRepository {
  /**
   * Creates a new ExampleRepository instance
   * 
   * @param {Object} container - Dependency injection container
   */
  constructor({ db }) {
    super(db.exampleModel);
    
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  /**
   * Gets examples by status
   * 
   * @param {string} status - Status to filter by
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Found examples
   */
  async getByStatus(status, options = {}) {
    return await this.findAll({
      ...options,
      where: {
        ...options.where,
        status
      }
    });
  }

  /**
   * Gets examples by user ID
   * 
   * @param {string|number} userId - User ID
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Found examples
   */
  async getByUserId(userId, options = {}) {
    return await this.findAll({
      ...options,
      where: {
        ...options.where,
        createdBy: userId
      }
    });
  }

  /**
   * Searches examples by name
   * 
   * @param {string} searchTerm - Search term
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Found examples
   */
  async searchByName(searchTerm, options = {}) {
    return await this.findAll({
      ...options,
      where: {
        ...options.where,
        name: {
          [this.Sequelize.Op.iLike]: `%${searchTerm}%`
        }
      }
    });
  }
}

module.exports = ExampleRepository;
