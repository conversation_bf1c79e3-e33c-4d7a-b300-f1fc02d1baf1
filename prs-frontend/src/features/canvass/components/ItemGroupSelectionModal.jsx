import React, { useState, useEffect, useRef, createRef, useMemo } from 'react';
import { ScreenModal } from '@src/components/ui/Modal';
import { Table, Pagination } from '@components/ui/Table';
import { FieldWrapper } from '@src/components/ui/Form';
import { Dropdown } from '@src/components/ui/Dropdown';
import { useGetActiveSuppliers } from '@features/supplier/api';
import { useNotification } from '@hooks/useNotification';
import { ConfirmModal, CancelModal } from '@components/ui/Modal';
import { Input } from '@components/ui/Form';
import { cva } from 'class-variance-authority';
import { RadioButtonGroup } from '@components/ui/RadioButtonGroup';
import { useGetCompanies } from '@features/company/api';
import { useGetProjects } from '@features/project/api';
import PesoIcon from '@assets/icons/peso.svg?react';
import PercentIcon from '@assets/icons/percent.svg?react';
import { editCanvassItemSupplierSchema } from '@schema/canvass.schema';
import { useCanvassItemsStore } from '@store/canvassItemsStore';
import { z } from 'zod';
import { sortTableItems } from '@src/utils/query';
import { NumericalInput } from '@src/components/ui/Form/NumericalInput';

const MODAL_TYPES = {
    CONFIRM: 'CONFIRM',
    CANCEL: 'CANCEL',
    NONE: 'NONE',
}

const dropDownStyle = cva(
  'flex justify-between items-center text-gray-800 disabled:bg-gray-200 hover:bg-transparent bg-white bg-white border border-gray-200 w-full px-2 rounded-md disabled:border-gray-300',
  {
    variants: {},
    defaultVariants: {
      key: 'default',
    },
  },
);

const sortTypeConfig = {
  item: 'string',
  unit: 'string',
  quantity: 'number',
  qty: 'number',
  unitPrice: 'number',
  weight: 'number',
};

const discountTypeOptions = [
    { key: 'Fixed Amount', value: 'fixed' },
    { key: 'Percentage', value: 'percent' },
  ];
  
const termOptions = [
    { key: 'NET 15', value: 'NET 15' },
    { key: 'NET 30', value: 'NET 30' },
    { key: 'Cash in Advance (CIA)', value: 'Cash in Advance (CIA)' },
    { key: 'Cash on Delivery (COD)', value: 'Cash on Delivery (COD)' },
    {
      key: '10% DP, Balance upon delivery',
      value: '10% DP, Balance upon delivery',
    },
    {
      key: '20% DP, Balance upon delivery',
      value: '20% DP, Balance upon delivery',
    },
    {
      key: '30% DP, Balance upon delivery',
      value: '30% DP, Balance upon delivery',
    },
    {
      key: '50% DP, Balance upon delivery',
      value: '50% DP, Balance upon delivery',
    },
    {
      key: '80% DP, Balance upon delivery',
      value: '80% DP, Balance upon delivery',
    },
    { key: '10% DP, PB, 10% RETENTION', value: '10% DP, PB, 10% RETENTION' },
    { key: '20% DP, PB, 10% RETENTION', value: '20% DP, PB, 10% RETENTION' },
    { key: '30% DP, PB, 10% RETENTION', value: '30% DP, PB, 10% RETENTION' },
  ];

const defaultSupplierData = {
    supplierId: undefined,
    supplierName: '',
    term: '',
    quantity: '',
    qty: '',
    unitPrice: '',
    discountType: 'fixed',
    discountValue: '',
    unitPriceDiscounted: '',
  };

const HEADERS = [
  { key: 'item', value: 'Item' },
  { key: 'unit', value: 'Unit', css: 'w-[10%]'},
  { key: 'qty', value: 'Qty', css: 'w-[7%]'},
  { key: 'weight', value: 'Weight (kg)', css: 'w-[9%]'},
  { key: 'unitPrice', value: 'Unit Price', css: 'w-[12%]'},
];

const ItemGroupSelectionModal = ({
    screenModalProps,
    tableProps,
    isTransferOfMaterials,
    isOFM,
    data, 
}) => {
    /* Variables & States here */
    const [supplierData, setSupplierData] = useState([defaultSupplierData]);
    const [lastSortedItems, setLastSortedItems] = useState([]);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [modalState, setModalState] = useState({
        type: MODAL_TYPES.NONE,
        data: null,
    })
    const noteRefs = useRef([]);
    const fileUploadRefs = useRef([]);
    const identifier = 'id';

    useEffect(() => {
      if (screenModalProps?.isOpen) {
        tableProps?.resetPage();
      }
    }, [screenModalProps?.isOpen])

    useEffect(() => { // handle sorting
      if (screenModalProps?.isOpen) {
        if (!tableProps?.currentSort?.length) {
          setLastSortedItems([]);
          return;
        }
  
        const sorted = sortTableItems(data, tableProps?.currentSort, sortTypeConfig);
        setLastSortedItems(sorted);
      }
    }, [tableProps?.currentSort, data, screenModalProps?.isOpen])

    useEffect(() => { //sync data
      if (screenModalProps?.isOpen && !hasUnsavedChanges) {
        setSupplierData(data.map(item => {
          return {
            ...defaultSupplierData,
            _id: item?.[identifier],
            isOFM: isOFM,
          }
        }));
      } 
    }, [data, hasUnsavedChanges, screenModalProps?.isOpen])

    const { updateItemSuppliers } = useCanvassItemsStore();

    const { showNotification } = useNotification();

    const { data: supplierOptions, isFetching: isFetchingSuppliers } = useGetActiveSuppliers(
        {
        paginate: false,
        },
        {
        enabled: screenModalProps?.isOpen,
        },
    );

    const { data: companiesData = [] } = useGetCompanies(
        {
          paginate: false,
        },
        {
          select: response => {
            const { data } = response;
    
            return data?.map(company => ({
              key: `${company.name}`,
              value: `company??${company.id}`,
            }));
          },
        },
      );

    const { data: projectsData = [] } = useGetProjects(
        {
          paginate: false,
        },
        {
          select: response => {
            const { data } = response;
    
            return data?.map(project => ({
              key: `${project.name}`,
              value: `project??${project.id}`,
            }));
          },
        },
      );
    
    const supplierOptionsTOM = useMemo(() => {
        return [...companiesData, ...projectsData];
      }, [companiesData, projectsData]);

    /* Functions & Logic here */
    const renderDesign = (handleInputChange, supplierData, identifier) => {
      return {
        item: {
          render: data => {
            return (
              <p className="flex text-blue-500 underline text-wrap break-all">
                {data?.item ?? '---'}
              </p>
            );
          },
        },
        unit: {
          render: data => {
            return (<p className='text-wrap break-all'>{data?.unit ?? '---'}</p>)
          }
        },
        qty: {
          render: data => {
            const idx = supplierData.indexOf(supplierData.find(item => item?._id === data?.[identifier]));
            return (
              <NumericalInput
                name={`tabs.${idx}.qty`}
                decimalPlaces={3}
                maxLimit={6}
                value={supplierData[idx]?.qty ?? ''}
                onChange={e => handleInputChange(idx, 'qty', e.target.value)}
                inputProps={{
                  step: '0.001',
                  min: 0.000,
                  max: 999.999,
              }}
              />
            )
          }
        },
        weight: {
          render: data => (
            <p>{data?.weight ? Number(data?.weight).toFixed(3) : '---'}</p>
          )
        },
        unitPrice: {
          render: data => {
            const idx = supplierData.indexOf(supplierData.find(item => item?._id === data?.[identifier]));
            return (
              <NumericalInput
                name={`tabs.${idx}.unitPrice`}
                value={supplierData[idx]?.unitPrice ?? ''}
                onChange={e => handleInputChange(idx, 'unitPrice', e.target.value)}
                beforeIcon={PesoIcon}
                inputProps={{
                  min: 0.00,
                  max: 99999999.99
              }}
              />
            )}
        },
      }
    }

    const handleFormChange = () => {
      setHasUnsavedChanges(true);
    };

    const calculateDiscountedPrice = (unitPrice, discountType, discountValue) => {
        if (!unitPrice) return 0;
    
        if (discountType === 'percent') {
          const discounted = unitPrice - (unitPrice * discountValue) / 100;
          return discounted.toFixed(2);
        }
    
        const discounted = unitPrice - discountValue;
        return discounted.toFixed(2);
    };

    const handleInputChange = (index, field, value) => {
        setSupplierData(prev => {
          const updated = [...prev];
          updated[index] = {
            ...updated[index],
            quantity: (field === 'qty') ? Number(value) : updated[index].quantity,
            [field]: value,
          };
          
          if (['unitPrice', 'discountType', 'discountValue'].includes(field)) {
            updated[index].unitPriceDiscounted = calculateDiscountedPrice(
              updated[index].unitPrice,
              updated[index].discountType,
              updated[index].discountValue,
            );
          }

          if (['unitPrice', 'qty'].includes(field)) {
            data.find(item => item?.[identifier] === updated[index]?._id)[field] = value;
          }

          return updated;
        });
        handleFormChange();
      };

    const syncData = (dataToSync) => {
      return dataToSync.map((item) => {
        const qty = item?.qty;

        return {
          ...item,
          // pre-fill values
          discountType: dataToSync[0]?.discountType,
          discountValue: dataToSync[0]?.discountValue,
          supplierId: dataToSync[0]?.supplierId,
          supplierName: dataToSync[0]?.supplierName,
          term: dataToSync[0]?.term,
          quantity: qty,
        }
            
      })
    }

    const handleSubmit = async () => {
      const sharedData = ['discountValue', 'discountType', 'supplierId', 'term']
      let row = 0;
      try {
        const { data: modalData } = modalState;

        // ensure matching data
        const updatedData = syncData(modalData);
        data.forEach((item, index) => {
          row = item?.item ?? index + 1;
          const fullData = updatedData.find((value) => value?._id === item?.[identifier]);

          if (!fullData) {
            throw new Error('Missing data');
          }
          
          delete fullData._id; // remove identifier

          const parsedData = editCanvassItemSupplierSchema.parse([{
            ...fullData,
            maxQuantity: Number(item?.quantity),
            isSteelbars: item?.isSteelbars
          }]);

          const updatePayload = {
            id: item?.id,
            canvassItemId: item?.canvassItemId,
            suppliers: parsedData,
          }

          updateItemSuppliers({
            ...updatePayload,
            forUpdate: !!item?.canvassItemId,
          })
        });

        screenModalProps?.baseOnConfirm();
        onCloseModal();
      } catch (error) {
        if (error instanceof z.ZodError) {
          const zodError = error.errors[0];
          const atrribute = zodError.path[1];

          if (sharedData.includes(atrribute)) {
            return showNotification({
              type: 'error',
              message: `${zodError.message}`,
            });
          }
          return showNotification({
              type: 'error',
              message: `${typeof row === 'string' ? row : 'Row'+row}: ${zodError.message}`,
            });
          }

        const errorMessage = error?.response?.data?.message || error?.message;
        showNotification({
          type: 'error',
          message: errorMessage,
        });
        onCloseModal();
      }
    }

    /* Component functions */
    const openModal = (type, data = null) => {
      setModalState({type, data});
    }

    const confirmEdit = () => {
      openModal(MODAL_TYPES.CONFIRM, supplierData);
    }

    const closeModal = () => {
        setModalState({ type: MODAL_TYPES.NONE, data: null });
    }

    const confirmCancel = () => {
        if (hasUnsavedChanges) { 
          openModal(MODAL_TYPES.CANCEL);
        } else {
          onCloseModal();
        }
    }

    const onCloseModal = () => {
        tableProps?.resetPage();
        setHasUnsavedChanges(false);
        closeModal();
        screenModalProps?.baseOnClose();
    }

    return (
        <ScreenModal
            isOpen={screenModalProps?.isOpen}
            header={screenModalProps?.header}
            portal={screenModalProps?.portal}
            hasBackButton={screenModalProps?.hasBackButton}
            onClose={onCloseModal}
            onCancel={confirmCancel}
            onConfirm={confirmEdit}
        >
          <div className='bg-white rounded-lg p-4'>
          <div className='flex flex-col gap-y-4'>
                {supplierData?.map((tab, i) => {
                    if (i > 0) return null;

                    if (!fileUploadRefs.current[i]) {
                        fileUploadRefs.current[i] = createRef();
                    }
                    if (!noteRefs.current[i]) {
                        noteRefs.current[i] = createRef();
                    }

                    return (
                        <div className='grid grid-cols-2 gap-x-2' key={i}>
                            <FieldWrapper label="Supplier">
                                <Dropdown
                                    label="Supplier"
                                    name={`tabs.${i + 1}.supplierId`}
                                    options={
                                    isTransferOfMaterials
                                        ? supplierOptionsTOM
                                        : supplierOptions?.data.map(supplier => ({
                                            key: supplier.name,
                                            value: supplier.id,
                                        }))
                                    }
                                    defaultLabel="Select Supplier"
                                    cvaStyles={dropDownStyle}
                                     
                                    searchable
                                    defaultValue={
                                    !isTransferOfMaterials
                                        ? tab.supplierId
                                        : Number.isInteger(tab.supplierId)
                                        ? `${tab.supplierType}??${tab.supplierId}`
                                        : tab.supplierId
                                    }
                                    onChange={val => {
                                    const string_val = val?.toString();
                                    if (isTransferOfMaterials) {
                                        handleInputChange(
                                        i,
                                        'supplierType',
                                        string_val.split('??')[0],
                                        );
                                    }
            
                                    handleInputChange(i, 'supplierId', val);
            
                                    const supplierName = isTransferOfMaterials
                                        ? supplierOptionsTOM?.find(
                                            supplier => supplier.value === string_val,
                                        )?.key
                                        : supplierOptions?.data?.find(
                                            supplier => supplier.id === val,
                                        )?.name;
            
                                    handleInputChange(i, 'supplierName', supplierName);
                                    }}
                                />
                            </FieldWrapper>

                            <div className="hidden">
                                <Input
                                    type="text"
                                    label="supplierType"
                                    name={`tabs.${i}.supplierType`}
                                    disabled={true}
                                    value={tab.supplierType || 'supplier'}
                                />
                            </div>
        
                            <FieldWrapper label="Terms">
                                <Dropdown
                                    label="Terms"
                                    name={`tabs.${i + 1}.term`}
                                    cvaStyles={dropDownStyle}
                                    options={termOptions}
                                    defaultLabel="Select Terms"

                                    searchable
                                    onChange={val => handleInputChange(i, 'term', val)}
                                    defaultValue={tab.term || ''}
                                />
                            </FieldWrapper>
                                
                            <RadioButtonGroup
                                name={`tabs.${i}.discountType`}
                                label="Discount (in price or percentage):"
                                options={discountTypeOptions}
                                fluid={true}
                                defaultValue={tab.discountType || 'fixed'}
                                onChange={val =>
                                    handleInputChange(i, 'discountType', val)
                                }
                            />
                            <NumericalInput
                                noNegative={false}
                                name={`tabs.${i}.discountValue`}
                                label={<span>&nbsp;</span>}
                                placeholder="Input Discount"
                                beforeIcon={
                                  tab.discountType === 'percent' ? PercentIcon : PesoIcon
                                  }
                                  onChange={e => {
                                      handleInputChange(i, 'discountValue', e.target.value);
                                  }}
                                value={tab.discountValue ?? ''}
                            />
                        </div>
                    )
                })
                }
            </div>
            <div className="text-xl font-bold m-4">Item/s</div>
            <Pagination
                total={data?.length || 0}
                setPage={tableProps?.setPage}
                asHOC={true}
                maxPerPage={tableProps?.currentLimit}
            >
                {({ currentPage, limit }) => (
                    <Table
                        headers={HEADERS}
                        data={lastSortedItems.length > 0 ? lastSortedItems : data}
                        tdDesign={renderDesign(handleInputChange, supplierData, identifier)}
                        isLoading={false}
                        onSort={tableProps?.onSort}
                        currentSort={tableProps?.currentSort}
                        page={currentPage}
                        limit={limit}
                        alignment='left'
                    />                        
                )}
            </Pagination>
          </div>

          <ConfirmModal
            isOpen={modalState.type === MODAL_TYPES.CONFIRM}
            onClose={closeModal}
            onConfirm={() => handleSubmit()}
            size="small"
            header={'Confirm Canvass'}
          />

          <CancelModal
            isOpen={modalState.type === MODAL_TYPES.CANCEL}
            onClose={closeModal}
            onContinue={onCloseModal}
            header={'Cancel Canvass'}
          />

        </ScreenModal>
    );
};

export { ItemGroupSelectionModal };