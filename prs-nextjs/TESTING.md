# Testing the Login Functionality

This document outlines the steps to test the login functionality of the PRS Next.js application.

## Prerequisites

1. Make sure you have Node.js 22 installed in your devcontainer
2. Install the dependencies with `npm install`
3. Set up the database connection in the `.env` file
4. Run the database migrations with `npx prisma migrate dev`
5. Seed the database with test users using `npx prisma db seed`

## Test Users

The seed script creates the following test users:

| Username | Password    | Role    |
|----------|-------------|---------|
| admin    | Admin@123   | Admin   |
| manager  | Manager@123 | Manager |
| user     | User@123    | User    |

## Testing Steps

1. Start the development server with `npm run dev`
2. Open the application in your browser at `http://localhost:3000`
3. You should be redirected to the login page
4. Try logging in with each of the test users
5. Verify that you are redirected to the dashboard after successful login
6. Verify that the user information is displayed correctly on the dashboard
7. Try accessing a protected route directly (e.g., `/app/dashboard`) without logging in
8. Verify that you are redirected to the login page
9. Log out and verify that you are redirected to the login page
10. Try logging in with incorrect credentials and verify that an error message is displayed

## Expected Results

- The login page should display the PRS logo and title
- The login form should have fields for username and password
- After successful login, you should be redirected to the dashboard
- The dashboard should display the user's name, email, and role
- Protected routes should redirect to the login page if not authenticated
- Incorrect login credentials should display an error message
- OTP verification should be requested if the user has OTP enabled

## Troubleshooting

If you encounter any issues during testing, check the following:

1. Make sure the database connection is properly configured in the `.env` file
2. Verify that the migrations have been applied correctly
3. Check the browser console for any JavaScript errors
4. Verify that the NextAuth configuration is correct
5. Check the server logs for any backend errors

## Testing in the Devcontainer

Since we're using a devcontainer approach, you'll need to:

1. Build and start the devcontainer:
   ```bash
   docker compose -f .devcontainer/docker compose.yml up -d
   ```

2. Connect to the devcontainer:
   ```bash
   docker exec -it prs-nextjs-devcontainer bash
   ```

3. Inside the devcontainer, install dependencies and run the development server:
   ```bash
   npm install
   npx prisma migrate dev
   npx prisma db seed
   npm run dev
   ```

4. Access the application at `http://localhost:3000` in your browser

## Manual Testing Checklist

- [ ] Login page loads correctly
- [ ] Login form validation works (required fields)
- [ ] Login with admin user succeeds
- [ ] Login with manager user succeeds
- [ ] Login with regular user succeeds
- [ ] Login with incorrect credentials fails with appropriate error message
- [ ] Protected routes redirect to login when not authenticated
- [ ] User information is displayed correctly on the dashboard
- [ ] Logout functionality works correctly

## Next Steps

After verifying that the login functionality works correctly, we can proceed to implement the next feature: Requisition creation.
