import { useState, useCallback, useEffect } from 'react';

/**
 * useViewMode
 * 
 * Tracks "view" vs "edit" mode based on focus, blur, or disabled state.
 * @param {boolean} enforceFormat
 * @param {boolean} disabled
 * @returns {{ isViewMode: boolean, onFocus: function, onBlur: function }}
 */
export function useViewMode(enforceFormat = true, disabled = false) {
    const [isViewMode, setIsViewMode] = useState(false);

    // update when disabled toggles
    useEffect(() => {
        if (enforceFormat && disabled) {
            setIsViewMode(true);
        }
    }, [disabled, enforceFormat]);

    const onFocus = useCallback(() => {
        if (enforceFormat) setIsViewMode(false);
    }, [enforceFormat]);

    const onBlur = useCallback(() => {
        if (enforceFormat) setIsViewMode(true);
    }, [enforceFormat]);

    return { isViewMode, onFocus, onBlur };
}