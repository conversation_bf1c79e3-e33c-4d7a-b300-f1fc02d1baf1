# PRS Implementation Strategy: RBAC, Approval Rules, and Data Preservation

This document outlines the implementation strategy for three critical requirements of the Purchase Requisition System (PRS):

1. Role-Based Access Control (RBAC)
2. Complex Approval Rules
3. No Deletion & Data Preservation

## Role-Based Access Control (RBAC)

RBAC is a fundamental security model that affects every aspect of the application, from UI rendering to API access.

### Centralized Permission System

```typescript
// /lib/permissions/types.ts
export type Permission = 
  | 'requisition:create'
  | 'requisition:view'
  | 'requisition:edit'
  | 'requisition:approve:level1'
  | 'requisition:approve:level2'
  | 'requisition:approve:final'
  | 'user:manage'
  | 'role:manage';

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

export interface User {
  id: string;
  roles: Role[];
  // other user properties
}
```

```typescript
// /lib/permissions/index.ts
export function hasPermission(user: User, permission: Permission): boolean {
  if (!user || !user.roles) return false;
  
  return user.roles.some(role => 
    role.permissions.includes(permission)
  );
}

export function hasAnyPermission(user: User, permissions: Permission[]): boolean {
  if (!user || !user.roles) return false;
  
  return permissions.some(permission => hasPermission(user, permission));
}

export function hasAllPermissions(user: User, permissions: Permission[]): boolean {
  if (!user || !user.roles) return false;
  
  return permissions.every(permission => hasPermission(user, permission));
}
```

### API-Level Authorization

```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { hasPermission } from '@/lib/permissions';

export function withAuthorization(requiredPermission: Permission) {
  return async function middleware(req) {
    const token = await getToken({ req });
    
    if (!token || !hasPermission(token.user, requiredPermission)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }
    
    return NextResponse.next();
  };
}
```

```typescript
// /app/api/requisitions/route.ts
import { withAuthorization } from '@/middleware';

export const GET = withAuthorization('requisition:view')(
  async (req) => {
    // Handle fetching requisitions
    return NextResponse.json({ requisitions: [] });
  }
);

export const POST = withAuthorization('requisition:create')(
  async (req) => {
    // Handle creating requisition
    return NextResponse.json({ success: true });
  }
);
```

### UI-Level Authorization

```tsx
// components/PermissionGate.tsx
import { useSession } from 'next-auth/react';
import { hasPermission } from '@/lib/permissions';
import type { Permission } from '@/lib/permissions/types';

interface PermissionGateProps {
  permission: Permission;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PermissionGate({ 
  permission, 
  children, 
  fallback = null 
}: PermissionGateProps) {
  const { data: session } = useSession();
  
  if (!session?.user || !hasPermission(session.user, permission)) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}
```

Usage in components:

```tsx
// components/RequisitionActions.tsx
import { PermissionGate } from '@/components/PermissionGate';

export function RequisitionActions({ requisition }) {
  return (
    <div className="flex gap-2">
      <PermissionGate permission="requisition:edit">
        <Button variant="outline">Edit</Button>
      </PermissionGate>
      
      <PermissionGate permission="requisition:approve:level1">
        <Button variant="primary">Approve</Button>
      </PermissionGate>
    </div>
  );
}
```

### Role Management UI

The system should include an admin interface for role management:

- Create, edit, and view roles
- Assign permissions to roles
- Assign roles to users
- Audit logging for role changes

## Approval Rules Engine

### Workflow Definition System

```typescript
// /lib/workflows/types.ts
export type RequisitionStatus = 
  | 'DRAFT'
  | 'SUBMITTED'
  | 'APPROVED_L1'
  | 'APPROVED_L2'
  | 'APPROVED_FINAL'
  | 'REJECTED'
  | 'CANCELLED';

export interface ApprovalLevel {
  level: number;
  requiredRole: string;
  threshold?: number; // Optional monetary threshold
  approverCount?: number; // Number of approvers required
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  departmentId?: string; // Optional department-specific workflow
  approvalLevels: ApprovalLevel[];
  conditionalRouting?: ConditionalRoute[];
}

export interface ConditionalRoute {
  condition: {
    field: string;
    operator: 'eq' | 'gt' | 'lt' | 'gte' | 'lte';
    value: any;
  };
  workflowId: string; // Route to a different workflow based on condition
}
```

### State Machine Implementation

```typescript
// /lib/workflows/stateMachine.ts
import type { RequisitionStatus } from './types';

interface Transition {
  requiredRole: string;
  conditions?: Array<(requisition: any) => boolean>;
}

export const stateMachine: Record<RequisitionStatus, Record<RequisitionStatus, Transition>> = {
  DRAFT: {
    SUBMITTED: { requiredRole: 'REQUESTER' }
  },
  SUBMITTED: {
    APPROVED_L1: { requiredRole: 'APPROVER_L1' },
    REJECTED: { requiredRole: 'APPROVER_L1' }
  },
  APPROVED_L1: {
    APPROVED_L2: { 
      requiredRole: 'APPROVER_L2',
      conditions: [
        // Only require L2 approval if amount > 10000
        (req) => req.totalAmount > 10000
      ]
    },
    APPROVED_FINAL: { 
      requiredRole: 'APPROVER_FINAL',
      conditions: [
        // Skip to final approval if amount <= 10000
        (req) => req.totalAmount <= 10000
      ]
    },
    REJECTED: { requiredRole: 'APPROVER_L2' }
  },
  APPROVED_L2: {
    APPROVED_FINAL: { requiredRole: 'APPROVER_FINAL' },
    REJECTED: { requiredRole: 'APPROVER_FINAL' }
  },
  APPROVED_FINAL: {
    CANCELLED: { requiredRole: 'APPROVER_FINAL' }
  },
  REJECTED: {
    DRAFT: { requiredRole: 'REQUESTER' }
  },
  CANCELLED: {
    DRAFT: { requiredRole: 'REQUESTER' }
  }
};

export function canTransition(
  currentStatus: RequisitionStatus,
  targetStatus: RequisitionStatus,
  userRole: string,
  requisition: any
): boolean {
  const transition = stateMachine[currentStatus]?.[targetStatus];
  
  if (!transition) return false;
  if (transition.requiredRole !== userRole) return false;
  
  // Check additional conditions if they exist
  if (transition.conditions && 
      !transition.conditions.every(condition => condition(requisition))) {
    return false;
  }
  
  return true;
}
```

### Notification System

```typescript
// /lib/notifications/index.ts
export async function sendApprovalNotification(requisition, approvers) {
  // Send email notifications
  for (const approver of approvers) {
    await sendEmail({
      to: approver.email,
      subject: `Requisition #${requisition.number} Requires Your Approval`,
      body: `Please review and approve requisition #${requisition.number}`
    });
    
    // Create in-app notification
    await prisma.notification.create({
      data: {
        userId: approver.id,
        type: 'APPROVAL_REQUIRED',
        title: 'Approval Required',
        message: `Requisition #${requisition.number} requires your approval`,
        link: `/requisitions/${requisition.id}`,
        isRead: false
      }
    });
  }
}

export async function sendEscalationNotification(requisition, escalationLevel) {
  // Implementation for escalation notifications
}
```

## Data Preservation Strategy

### Soft Delete Implementation

```typescript
// /prisma/schema.prisma
model BaseEntity {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  isDeleted Boolean  @default(false)
  deletedAt DateTime?
}

model Requisition extends BaseEntity {
  // Requisition fields
}
```

```typescript
// /lib/db/softDelete.ts
export async function softDelete(model, id) {
  return prisma[model].update({
    where: { id },
    data: {
      isDeleted: true,
      deletedAt: new Date()
    }
  });
}

// Usage
await softDelete('requisition', '123');
```

### Audit Trail System

```typescript
// /prisma/schema.prisma
model AuditLog {
  id            String   @id @default(uuid())
  entityType    String   // e.g., 'requisition', 'user'
  entityId      String
  action        String   // e.g., 'create', 'update', 'delete'
  userId        String
  timestamp     DateTime @default(now())
  previousValue Json?    // Previous state for updates
  newValue      Json?    // New state for updates
  metadata      Json?    // Additional context
  
  user          User     @relation(fields: [userId], references: [id])
}
```

```typescript
// /lib/audit/index.ts
export async function logAuditEvent({
  entityType,
  entityId,
  action,
  userId,
  previousValue,
  newValue,
  metadata
}) {
  return prisma.auditLog.create({
    data: {
      entityType,
      entityId,
      action,
      userId,
      previousValue,
      newValue,
      metadata
    }
  });
}

// Middleware to automatically log changes
export const withAuditLogging = (handler) => async (req, res) => {
  // Capture the state before changes
  const entityId = req.query.id;
  const entityType = req.url.split('/')[1]; // e.g., /requisitions/123 -> 'requisitions'
  
  let previousValue = null;
  if (entityId && (req.method === 'PUT' || req.method === 'PATCH')) {
    previousValue = await prisma[entityType].findUnique({
      where: { id: entityId }
    });
  }
  
  // Process the request
  const result = await handler(req, res);
  
  // Log the audit event
  if (req.method !== 'GET') {
    await logAuditEvent({
      entityType,
      entityId: entityId || result.id,
      action: getActionFromMethod(req.method),
      userId: req.user.id,
      previousValue,
      newValue: req.method === 'DELETE' ? null : (result || req.body),
      metadata: {
        ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        userAgent: req.headers['user-agent']
      }
    });
  }
  
  return result;
};
```

### Archiving Strategy

```typescript
// /lib/archiving/index.ts
export async function archiveOldRecords(entityType, cutoffDate) {
  // Move records to archive table but keep them accessible
  const records = await prisma[entityType].findMany({
    where: {
      updatedAt: {
        lt: cutoffDate
      },
      isArchived: false
    }
  });
  
  for (const record of records) {
    // Copy to archive table
    await prisma[`${entityType}Archive`].create({
      data: {
        ...record,
        originalId: record.id
      }
    });
    
    // Mark as archived in main table
    await prisma[entityType].update({
      where: { id: record.id },
      data: { isArchived: true }
    });
  }
}
```

## Conclusion

This implementation strategy addresses the core requirements of the PRS system:

1. **RBAC**: A comprehensive permission system that secures both the UI and API
2. **Approval Rules**: A flexible workflow engine with conditional routing and multi-level approvals
3. **Data Preservation**: A robust strategy for maintaining all data without deletion

By implementing these features in a Next.js fullstack application, we can create a secure, auditable, and compliant purchase requisition system that meets the specific needs of the organization.
