/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
    },
    extend: {
      boxShadow: {
        custom:
          'rgba(14, 30, 37, 0.12) 0px 2px 4px 0px, rgba(14, 30, 37, 0.32) 0px 2px 16px 0px',
      },
      colors: {
        // Exact colors from the original project
        primary: {
          50: '#f5f3f2',
          100: '#e6e0de',
          200: '#d1c5c1',
          300: '#b9a6a0',
          400: '#a48c85',
          500: '#8f726a',
          600: '#775c55',
          650: '#7a5c5f', // Exact maroon shade from the original modal
          700: '#754445', // Main primary color from original project
          800: '#8B4F4F', // Hover state for primary.700
          900: '#231a1c',
        },
        modal: {
          header: '#7a5c5f', // Exact header color for modals
          bg: '#797a7b',     // Exact background color for modals
        },
        secondary: {
          50: '#f0f2f5',
          100: '#e1e5eb',
          200: '#c3ccd8',
          300: '#a5b2c4',
          400: '#8799b1',
          500: '#697f9d',
          600: '#445475', // Main secondary color from original project
          700: '#3a4761',
          800: '#2f3a4e',
          900: '#252d3a',
        },
        danger: {
          500: '#EB5757', // Danger color from original project
          600: '#F58A8A', // Hover state for danger
        },
        success: {
          500: '#219653', // Success color from original project
        },
        warning: {
          500: '#F2994A', // Warning color from original project
        },
        red: {
          950: '#420001', // Used in the login page for "Welcome User" text
        },
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: '100ch',
            color: 'inherit',
            a: {
              color: 'inherit',
              opacity: 0.8,
              '&:hover': {
                opacity: 1,
                color: '#754445',
              },
              textDecoration: 'underline',
              fontWeight: '500',
            },
            b: { color: 'inherit' },
            strong: { color: 'inherit' },
            em: { color: 'inherit' },
            h1: { color: 'inherit' },
            h2: { color: 'inherit' },
            h3: { color: 'inherit' },
            h4: { color: 'inherit' },
            code: { color: 'inherit' },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    function ({ addVariant }) {
      addVariant('child', '& > *');
      addVariant('child-hover', '& > *:hover');
    },
  ],
}
