# Data Modeling Analysis

## Overview

This document analyzes the data modeling approach in the PRS-Backend, examining the database schema design, relationships, and implementation. The backend uses Sequelize ORM with a relational database (PostgreSQL inferred from the code patterns).

## Database Schema Design

The PRS-Backend implements a comprehensive database schema that models the complex domain of a purchase requisition system. The schema includes entities such as:

- Users and Roles
- Companies and Departments
- Projects
- Requisitions and Requisition Items
- Purchase Orders
- Suppliers
- Approvals and Approval Workflows
- Delivery Receipts
- Payment Requests
- Attachments and Notes

## Model Implementation

Models are implemented using Sequelize with clear definitions of fields, relationships, and constraints:

```javascript
// Example from infra/database/models/projectApprovalModel.js
module.exports = (sequelize, Sequelize) => {
  const ProjectApprovalModel = sequelize.define(
    'project_approvals',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'project_id',
      },
      approvalTypeCode: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'approval_types',
          key: 'code',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'approval_type_code',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isOptional: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_optional',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  ProjectApprovalModel.associate = (models) => {
    ProjectApprovalModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
      targetKey: 'id',
    });

    ProjectApprovalModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      as: 'project',
    });
  };

  return ProjectApprovalModel;
};
```

## Naming Conventions

The data models follow consistent naming conventions:

- **Table Names**: Plural, snake_case (e.g., `project_approvals`)
- **Column Names**: Snake_case (e.g., `approval_type_code`)
- **Model Names**: PascalCase with "Model" suffix (e.g., `ProjectApprovalModel`)
- **Foreign Keys**: camelCase with "Id" suffix in JavaScript, snake_case with "_id" suffix in database (e.g., `approverId` in code, `approver_id` in database)

## Relationships

The models implement various types of relationships:

### One-to-Many Relationships

```javascript
// Example: Project has many ProjectApprovals
ProjectModel.associate = (models) => {
  ProjectModel.hasMany(models.projectApprovalModel, {
    foreignKey: 'projectId',
    as: 'approvals',
  });
};
```

### Many-to-One Relationships

```javascript
// Example: ProjectApproval belongs to User
ProjectApprovalModel.associate = (models) => {
  ProjectApprovalModel.belongsTo(models.userModel, {
    foreignKey: 'approverId',
    as: 'approver',
    targetKey: 'id',
  });
};
```

### Many-to-Many Relationships

```javascript
// Example: User has many Roles through UserRoles
UserModel.associate = (models) => {
  UserModel.belongsToMany(models.roleModel, {
    through: models.userRoleModel,
    foreignKey: 'userId',
    otherKey: 'roleId',
    as: 'roles',
  });
};
```

## Data Types

The models use a variety of Sequelize data types:

- **INTEGER** - For IDs and numeric values
- **STRING** - For text with specified length
- **TEXT** - For longer text content
- **BOOLEAN** - For true/false flags
- **DATE** - For timestamps
- **FLOAT** - For decimal numbers
- **ENUM** - For predefined values

Example:

```javascript
// Example data types
{
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: Sequelize.STRING(100),
    allowNull: false,
  },
  description: {
    type: Sequelize.TEXT,
    allowNull: true,
  },
  isActive: {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    field: 'is_active',
  },
  status: {
    type: Sequelize.ENUM('pending', 'approved', 'rejected'),
    allowNull: false,
    defaultValue: 'pending',
  },
  amount: {
    type: Sequelize.FLOAT,
    allowNull: false,
    defaultValue: 0,
  },
  createdAt: {
    type: Sequelize.DATE,
    allowNull: false,
    field: 'created_at',
    defaultValue: Sequelize.fn('now'),
  },
}
```

## Constraints and Validations

The models implement various constraints and validations:

- **Primary Keys** - Defined for all tables
- **Foreign Keys** - With referential actions (CASCADE, SET NULL)
- **Not Null Constraints** - Using `allowNull: false`
- **Default Values** - Using `defaultValue`
- **Unique Constraints** - Using `unique: true`

Example:

```javascript
// Example constraints
{
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  email: {
    type: Sequelize.STRING(100),
    allowNull: false,
    unique: true,
  },
  status: {
    type: Sequelize.STRING(20),
    allowNull: false,
    defaultValue: 'active',
  },
  deletedAt: {
    type: Sequelize.DATE,
    allowNull: true,
    field: 'deleted_at',
  },
}
```

## Timestamps and Soft Deletes

The models implement timestamps and some implement soft deletes:

```javascript
// Example timestamps configuration
{
  timestamps: true,
  underscored: true,
  paranoid: true, // Enables soft delete
}
```

This creates:
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp
- `deleted_at` - Soft delete timestamp (when paranoid is true)

## Complex Model Example

The `RequisitionCanvassHistoryModel` demonstrates a complex model with multiple relationships:

```javascript
// From infra/database/models/requisitionCanvassHistoryModel.js
module.exports = (sequelize, Sequelize) => {
  const RequisitionCanvassHistoryModel = sequelize.define(
    'requisition_canvass_histories',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisition_item_lists',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_item_list_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'suppliers',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'supplier_id',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      canvassNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'canvass_number',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      item: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'item',
      },
      price: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'price',
      },
      discount: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'discount',
      },
      canvassDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'canvass_date',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
  );

  RequisitionCanvassHistoryModel.associate = (models) => {};

  return RequisitionCanvassHistoryModel;
};
```

## Repository Pattern

The data access is abstracted through repositories that extend a base repository:

```javascript
// Example from infra/repositories/purchaseOrderItemRepository.js
class PurchaseOrderItemRepository extends BaseRepository {
  constructor({ db, steelbarsRepository }) {
    super(db.purchaseOrderItemModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.steelbarsRepository = steelbarsRepository;
  }

  async getPOItemsById(payload) {
    // Implementation details...
  }

  async getPOItemsSummary(payload) {
    // Implementation details...
  }
}
```

The repositories implement complex queries using Sequelize's query builder:

```javascript
// Example complex query
const poItems = await this.findAll({
  where: {
    purchaseOrderId,
    [this.Sequelize.Op.or]: [
      { '$requisitionItemList.item.id$': { [this.Sequelize.Op.ne]: null } },
      {
        '$requisitionItemList.nonOfmItem.id$': {
          [this.Sequelize.Op.ne]: null,
        },
      },
    ],
  },
  attributes: [
    'id',
    'quantityPurchased',
    'canvassItemId',
    'requisitionItemListId',
    [
      this.Sequelize.col('canvassItemSupplier.quantity'),
      'quantityRequested',
    ],
    [this.Sequelize.col('canvassItemSupplier.unit_price'), 'originalPrice'],
    [
      this.Sequelize.literal(
        'CASE ' +
          'WHEN "canvassItemSupplier"."discount_type" = \'fixed\' THEN ' +
          '"canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value" ' +
          'WHEN "canvassItemSupplier"."discount_type" = \'percent\' THEN ' +
          '("canvassItemSupplier"."unit_price" * (1 - "canvassItemSupplier"."discount_value" / 100)) ' +
          'ELSE "canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value" ' +
          'END',
      ),
      'canvassedPrice',
    ],
    // Additional attributes...
  ],
  include: [
    {
      association: 'canvassItemSupplier',
      attributes: [
        'quantity',
        'unit_price',
        'discount_value',
        'supplier_name',
      ],
      required: false,
    },
    {
      association: 'requisitionItemList',
      attributes: ['notes', 'itemType'],
      required: true,
      include: [
        // Nested includes...
      ],
    },
  ],
  paginate,
  page,
  limit,
  order,
});
```

Some repositories also use raw SQL queries for complex calculations:

```javascript
// Example raw SQL query
const sqlQuery = `
  SELECT
    MAX(purchase_order_items.id) AS id,
    SUM("canvass_item_suppliers"."unit_price" * "purchase_order_items"."quantity_purchased") AS amount,
    SUM(CASE
        WHEN "canvass_item_suppliers"."discount_type" = 'fixed' THEN ("canvass_item_suppliers"."discount_value") * "purchase_order_items"."quantity_purchased"
        WHEN "canvass_item_suppliers"."discount_type" = 'percent' THEN ("canvass_item_suppliers"."unit_price" * ("canvass_item_suppliers"."discount_value" / 100)) * "purchase_order_items"."quantity_purchased"
        ELSE 0
    END)
    AS discount,
   SUM("canvass_item_suppliers"."unit_price" * "purchase_order_items"."quantity_purchased") - SUM(CASE
      WHEN "canvass_item_suppliers"."discount_type" = 'fixed' THEN ("canvass_item_suppliers"."discount_value") * "purchase_order_items"."quantity_purchased"
      WHEN "canvass_item_suppliers"."discount_type" = 'percent' THEN ("canvass_item_suppliers"."unit_price" * ("canvass_item_suppliers"."discount_value" / 100)) * "purchase_order_items"."quantity_purchased"
      ELSE 0
  END)
  AS total_amount
  FROM purchase_order_items
  LEFT JOIN canvass_item_suppliers
    ON canvass_item_suppliers.id = purchase_order_items.canvass_item_supplier_id
  WHERE purchase_order_items.purchase_order_id = :purchaseOrderId
  GROUP BY purchase_order_items.purchase_order_id;
`;

const [data] = await this.db.sequelize.query(sqlQuery, {
  replacements: { purchaseOrderId },
  type: this.db.Sequelize.QueryTypes.SELECT,
});
```

## Data Modeling Strengths

1. **Comprehensive Schema** - Models the complex domain completely
2. **Clear Relationships** - Well-defined associations between models
3. **Consistent Naming** - Follows consistent naming conventions
4. **Proper Constraints** - Implements appropriate constraints and validations
5. **Repository Abstraction** - Abstracts data access through repositories

## Data Modeling Challenges

1. **Complex Queries** - Some repositories contain very complex queries
2. **Mixed Query Approaches** - Uses both Sequelize ORM and raw SQL
3. **Limited Documentation** - Minimal documentation of the data model
4. **Some Redundancy** - Some duplication in model attributes

## Recommendations

1. **Document Data Model** - Create comprehensive documentation of the data model
2. **Refactor Complex Queries** - Break down complex queries into smaller, reusable parts
3. **Standardize Query Approach** - Adopt a consistent approach to queries
4. **Implement Data Validation Layer** - Add a domain validation layer above the ORM
5. **Add Database Indexes** - Review and optimize database indexes for performance

## Conclusion

The PRS-Backend implements a comprehensive data model that effectively represents the complex domain of a purchase requisition system. The use of Sequelize ORM with well-defined models, relationships, and repositories demonstrates a mature data modeling approach.

The data model supports the complex business requirements while maintaining consistency and integrity. Areas for improvement include documenting the data model, refactoring complex queries, and standardizing the query approach.
