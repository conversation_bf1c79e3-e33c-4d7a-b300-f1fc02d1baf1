# PRS Backend Architecture Overview

This document provides an overview of the PRS Backend architecture, including its components, interactions, and design principles. For information about the frontend architecture, see [Frontend Architecture Overview](frontend-overview.md).

## System Architecture

The PRS Backend follows a layered architecture pattern with the following layers:

### 1. API Layer (Controllers)

- Handles HTTP requests and responses
- Performs input validation
- Manages authentication and authorization
- Delegates business logic to the service layer

**Key Components:**
- `src/app/handlers/controllers/`: Contains all controller classes
- `src/interfaces/router/`: Contains route definitions
- `src/app/handlers/middlewares/`: Contains middleware functions

### 2. Interface Layers

The interface layers provide boundaries between different parts of the system, enabling loose coupling and better separation of concerns.

#### API Interfaces

- Define contracts between controllers and services
- Specify input/output data structures
- Enable mocking for testing

**Key Components:**
- `src/interfaces/http/`: HTTP interface definitions
- `src/interfaces/api/`: API interface contracts
- `src/interfaces/dto/`: Data Transfer Objects

#### Repository Interfaces

- Define contracts for data access
- Abstract database operations
- Enable swapping implementations

**Key Components:**
- `src/interfaces/repositories/`: Repository interface definitions
- `src/interfaces/models/`: Model interface definitions

### 3. Service Layer

- Implements business logic
- Orchestrates operations across multiple repositories
- Manages transactions
- Performs additional validation

**Key Components:**
- `src/app/services/`: Contains all service classes
- `src/app/services/baseService.js`: Base class for services

### 4. Repository Layer

- Handles data access
- Abstracts database operations
- Performs data mapping

**Key Components:**
- `src/infra/repositories/`: Contains all repository classes
- `src/infra/repositories/baseRepository.js`: Base class for repositories

### 5. Domain Layer

- Defines domain entities and validation schemas
- Contains business constants and enums
- Defines domain-specific errors

**Key Components:**
- `src/domain/entities/`: Contains domain entities and validation schemas
- `src/domain/constants/`: Contains domain constants and enums

### 6. Infrastructure Layer

- Manages database connections
- Handles logging
- Provides utility functions

**Key Components:**
- `src/infra/database/`: Database configuration and models
- `src/infra/logs/`: Logging configuration
- `src/utils/`: Utility functions

## Dependency Injection

The PRS Backend uses Awilix for dependency injection:

- `src/container.js`: Defines the dependency injection container
- Services and repositories are registered in the container
- Dependencies are injected into controllers and services

## Database

- Uses Sequelize ORM for database access
- Models are defined in `src/infra/database/models/`
- Migrations are in `src/infra/database/migrations/`
- Database configuration is in `src/infra/database/config/`

## Authentication and Authorization

- JWT-based authentication
- Role-based authorization
- Middleware for protecting routes

## Error Handling

- Custom error classes in `src/app/errors/`
- Centralized error handling middleware
- Standardized error responses

## Logging

- Uses Pino for logging
- Request logging middleware
- Structured logging format

## API Documentation

- Uses Swagger/OpenAPI for API documentation
- Available at `/api-docs` endpoint

## Design Principles

1. **Separation of Concerns**: Each layer has a specific responsibility
2. **Dependency Injection**: Dependencies are injected rather than created
3. **Repository Pattern**: Data access is abstracted behind repositories
4. **Single Responsibility**: Each class has a single responsibility
5. **Don't Repeat Yourself (DRY)**: Common functionality is extracted into base classes

## Known Architectural Issues

1. **Business Logic Scattered**: Some business logic is scattered across controllers and services
2. **Transaction Management**: Transaction management is inconsistent
3. **Error Handling**: Error handling is not fully standardized
4. **Tight Coupling**: Some components are tightly coupled to infrastructure

## Recommendations for Improvement

1. **Centralize Business Logic**: Move business logic to domain services
2. **Standardize Transaction Management**: Use a consistent pattern for transactions
3. **Improve Error Handling**: Standardize error handling across the application
4. **Reduce Coupling**: Introduce interfaces to reduce coupling
