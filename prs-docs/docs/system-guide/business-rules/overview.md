# Business Rules Overview

This document provides an overview of the business rules in the PRS system. It serves as a guide to understanding the core business processes and rules that govern the system's behavior.

## Core Business Processes

The PRS system manages the following core business processes:

1. **Requisition Process**: Creating and approving requests for goods or materials
2. **Canvassing Process**: Collecting and comparing supplier quotations
3. **Purchase Order Process**: Generating, reviewing, and approving purchase orders
4. **Delivery Report Process**: Creating of delivery report once item has been delivered
5. **Invoice Report Process**: Creating of invoice report of invoices issued by the supplier
6. **Payment Request Process**: Creating of payment or voucher requests for suppliers

## Process Flow

The overall process flow in the PRS system is as follows:

```mermaid
---
config:
  look: handDrawn
  layout: elk
---
flowchart LR
    RS["Create RS"] --> RA["RS Approval"]
    RA --> CAN["Canvassing"]
    CAN --> CANA["Canvassing Approval"]
    CANA --> PO["Purchase Order"]
    PO --> POA["Purchase Order Approval"]
    POA --> DEL["Delivery"] & INV["Invoice"]
    DEL <--> INV
    DEL --> PAY["Payment"]
    INV --> PAY
    PAY --> PAYA["Payment Approval"]
     RS:::processStep
     RA:::approvalStep
     CAN:::processStep
     CANA:::approvalStep
     PO:::processStep
     POA:::approvalStep
     DEL:::noApprovalStep
     INV:::noApprovalStep
     PAY:::processStep
     PAYA:::approvalStep
    classDef approvalStep fill:#f9d6d6,stroke:#333,stroke-width:1px
    classDef processStep fill:#d6e9f9,stroke:#333,stroke-width:1px
    classDef noApprovalStep fill:#d6f9e0,stroke:#333,stroke-width:1px
```

Each process has its own status flow and business rules, which are documented in detail in the respective business rules documents.

## Key Business Entities

### 1. Requisition

A requisition is a formal request for goods or materials. It is the starting point of the procurement process.

[Detailed Requisition Business Rules](./requisition.md)

### 2. Canvass

A canvass is the process of collecting and comparing supplier quotations for items in a requisition.

[Detailed Canvass Business Rules](./canvass.md)

### 3. Purchase Order

A purchase order is an official document issued to suppliers to order goods or services.

[Detailed Purchase Order Business Rules](./purchase-order.md)

### 4. Delivery Report

A delivery report is a document confirming the delivery of goods.

[Detailed Delivery Business Rules](./delivery.md)

### 5. Invoice Report

An invoice report is a document to input invoice details issued by supplier.

[Detailed Invoice Report Business Rules](./invoice.md)

### 6. Payment Request

A payment request is a request for payment for delivered goods.

[Detailed Payment Business Rules](./payment.md)

## Cross-Cutting Business Rules

### 1. Approval Workflows

Approval workflows are a key part of the PRS system. They govern how different entities (requisitions, canvasses, purchase orders, etc.) are approved.

Key aspects of approval workflows:

- **Approver Assignment**: Approvers are assigned based on project, department, role, and quantity (GFQs)
- **Approval Levels**: Approvers have different levels, and approval proceeds in order of level
- **Adhoc Approvers**: Additional approvers can be added to the approval workflow
- **Approval Status**: Approvers can approve or reject, and the entity status changes accordingly

### 2. User Roles and Permissions

The PRS system has different user roles with different permissions:

- **Requester**: Can create and submit requisitions
- **Approver**: Can approve or reject requisitions, canvasses, and purchase orders
- **Purchasing Staff**: Can create canvasses and purchase orders
- **Purchasing Head**: Can assign requisitions and manage the procurement process
- **Management**: Can approve high-value requisitions and purchase orders

### 3. Status Management

Each entity in the PRS system has a status that changes as it moves through the process:

- **Requisition**: DRAFT → FOR_RS_APPROVAL → ASSIGNING → ASSIGNED → RS_IN_PROGRESS → CLOSED_RS; RS_CANCELLED; RS_REJECTED
- **Canvass**: DRAFT → FOR_CS_APPROVAL → CS_APPROVED; CS_REJECTED; CS_CANCELLED
- **Purchase Order**: FOR_PO_REVIEW → FOR_PO_APPROVAL → FOR SENDING → FOR_DELIVERY → CLOSED_PO; PO_CANCELLED; PO_REJECTED
- **Delivery Report**: DRAFT → DELIVERED
- **Invoice Report**: DRAFT → INVOICE RECEIVED
- **Payment Request**: DRAFT → FOR_PR_APPROVAL → CLOSED_PR

### 4. Item History, RS History, and Related Documents

History management is a critical aspect of the PRS system, allowing users to track and view historical information across various entities and processes.

#### Workflow

The history management functionality provides access to various history elements in the system:

```mermaid
flowchart TD
    Start([History Management]) --> S1{User Role}
    S1 -->|Purchasing Admin, Staff, Head/IT Admin/Engineer| S2[OFM Item History]
    S1 -->|Purchasing Admin, Staff, Head/IT Admin/Engineer| S3[Non-OFM Item History]
    S1 -->|All Users except Root User| S4[RS Related Documents]
    S1 -->|Purchasing Admin/IT Admin| S6[RS History]
    
    S2 -->|View Item| S21[Item Details & History]
    S21 -->|Search or Sort| S22[Filtered or Sorted History]
    
    S3 -->|View Item| S31[Item Details & History]
    S31 -->|Search or Sort| S32[Filtered & Sorted History]
    
    S4 -->|Select Tab| S41[View Document Type]
    S41 --> S42{Select Tab}
    S42 -->|Canvasses| S43[View Canvass Data]
    S42 -->|Orders| S44[View Orders Data]
    S42 -->|Deliveries| S45[View Delivery Data]
    S42 -->|Payments| S46[View Payment Data]
    S42 -->|Returns| S47[View Returns Data]
    
    S6 -->|View RS| S61[View RS History]
    S61 --> S62{Select Tab}
    S62 -->|Canvass| S63[View Canvass History]
    S62 -->|Orders| S64[View Orders History]
    S62 -->|Deliveries| S65[View Delivery History]
    S62 -->|Payments| S66[View Payment History]
    S62 -->|Returns| S67[View Returns History]
```

#### Key History Types and Access Control

History viewing functions are restricted to specific user roles:

| History Type | Engineer | IT Admin | Purchasing Staff | Purchasing Admin | Purchasing Head |
|--------------|-----------|----------|------------------|-----------------|------------|
| OFM Item History | w/ Access | w/ Access | w/ Access | w/ Access | w/ Access |
| Non-OFM Item History | w/ Access | w/ Access | w/ Access | w/ Access | w/ Access |
| RS Related Documents | w/ Access | w/ Access | w/ Access | w/ Access | w/ Access (All except Root) |
| RS History | -- | w/ Access | -- | w/ Access | -- |

#### History Types Details

##### OFM Item History
- **Definition**: List of requisition slip involving the selected ofm item - from requisition until delivery 
- **Information Displayed**: RS Number, Date Requested, Quantity Requested, Price, Date Delivered, Quantity Delivered
- **Features**: Search by RS Number, sort by various columns, paginated view (10 rows/page)
- **Business Rules**: Default sorting by most recent Date Requested, "---" displayed for unavailable data

##### Non-OFM Item History
- **Definition**: List of requisition slip involving the selected non ofm item - from requisition until delivery 
- **Information Displayed**: RS Number, Company, Project, Department, Date Requested, Quantity Requested, Date Delivered, Quantity Delivered
- **Features**: Search by RS Number, sort by various columns, paginated view (10 rows/page)
- **Business Rules**: Default sorting by most recent Date Requested, "---" displayed for unavailable data

##### RS Related Documents
- **Definition**: List of child documents of a Requisition Slip grouped by document type
- **Document Types**: Canvasses, Orders, Deliveries, Payments, Returns
- **Features**: Tab navigation between document types, search by document numbers, sort by various columns, paginated view
- **Business Rules**: "No Data" message displayed when no data is available, color-coded statuses

##### RS History
- **Definition**: List of child documents of a Requisition Slip grouped by document type
- **Information Types**: Canvass, Orders, Deliveries, Payments, Returns
- **Features**: Tab navigation between history types, sort by various columns, paginated view
- **Business Rules**: Displays 10 rows per page, each tab shows relevant historical data, "No Data" message displayed when no data is available, new row for every update

#### Common Business Rules Across History Management

- Default sorting is typically by the most recent date
- Search functionality allows filtering by keywords or document numbers
- Unavailable data is represented by "---" placeholders
- Color-coded statuses provide visual indicators of document states
- Pagination is implemented with 10 rows per page by default
- Each history view allows sorting by various columns with options for ascending/descending order

### 5. Audit Logs

The PRS system maintains a comprehensive audit trail of system changes and user actions for accountability and troubleshooting purposes.

Key aspects of audit logging:

- **Logging Scope**: All data modifications (insert, update, delete), authentication events (login, logout), and system configuration changes are automatically logged
- **Log Details**: Each entry includes record ID, module, action type, description, user information, timestamp, and old/new values for updates
- **Access Control**: Only IT Administrators and Purchasing Administrators can view audit logs through the Admin Menu
- **Search and Export**: Logs can be searched by any field and exported to Excel or CSV formats
- **Immutability**: Audit logs cannot be modified or deleted by any user, ensuring a reliable system change history

The audit log system provides transparency and accountability throughout all system processes, supports troubleshooting efforts, and ensures compliance with business policies by creating an immutable record of all system changes and user actions.

### 6. Validation Rules

Validation rules ensure that data entered into the system is valid and consistent:

- **Required Fields**: Certain fields are required based on the entity and its status
- **Format Validation**: Fields must adhere to specific formats (e.g., dates, numbers, text)
- **Business Validation**: Data must comply with business rules (e.g., a requisition must have at least one item)

### 7. Transaction Management

The PRS system uses transactions to ensure data consistency:

- **Atomic Operations**: Multiple database operations are grouped into transactions
- **Rollback on Error**: If an error occurs, all operations in the transaction are rolled back
- **Commit on Success**: If all operations succeed, the transaction is committed

## Implementation Notes

The business rules are implemented in various parts of the codebase:

- **Domain Entities**: Define validation schemas and basic business rules
- **Services**: Implement complex business logic and orchestrate operations
- **Controllers**: Handle HTTP requests and delegate to services
- **Repositories**: Handle data access and persistence

When implementing new features or modifying existing ones, it's important to understand and respect these business rules to maintain the integrity of the system.
