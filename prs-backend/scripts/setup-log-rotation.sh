#!/bin/bash
# Setup Log Rotation Cron Job
# This script sets up a daily cron job to rotate logs

# Get the absolute path to the rotate-logs.sh script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROTATE_SCRIPT="$SCRIPT_DIR/rotate-logs.sh"

# Check if the rotation script exists
if [ ! -f "$ROTATE_SCRIPT" ]; then
  echo "Error: Log rotation script not found at $ROTATE_SCRIPT"
  exit 1
fi

# Make sure the script is executable
chmod +x "$ROTATE_SCRIPT"

# Create a temporary file for the crontab
TEMP_CRON=$(mktemp)

# Export current crontab
crontab -l > "$TEMP_CRON" 2>/dev/null || echo "# PRS Backend Log Rotation" > "$TEMP_CRON"

# Check if the cron job already exists
if grep -q "$ROTATE_SCRIPT" "$TEMP_CRON"; then
  echo "Log rotation cron job already exists."
else
  # Add the cron job to run daily at 1:00 AM
  echo "# Run log rotation daily at 1:00 AM" >> "$TEMP_CRON"
  echo "0 1 * * * $ROTATE_SCRIPT >> $SCRIPT_DIR/../logs/rotation.log 2>&1" >> "$TEMP_CRON"
  
  # Install the new crontab
  crontab "$TEMP_CRON"
  echo "Log rotation cron job installed. Will run daily at 1:00 AM."
fi

# Clean up
rm "$TEMP_CRON"

echo "Log rotation setup complete."
