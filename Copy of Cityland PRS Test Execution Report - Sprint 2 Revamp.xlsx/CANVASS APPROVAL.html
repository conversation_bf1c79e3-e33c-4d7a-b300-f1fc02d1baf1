<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1835743769C0" style="width:103px;" class="column-headers-background">A</th><th id="1835743769C1" style="width:219px;" class="column-headers-background">B</th><th id="1835743769C2" style="width:100px;" class="column-headers-background">C</th><th id="1835743769C3" style="width:252px;" class="column-headers-background">D</th><th id="1835743769C4" style="width:233px;" class="column-headers-background">E</th><th id="1835743769C5" style="width:330px;" class="column-headers-background">F</th><th id="1835743769C7" style="width:258px;" class="column-headers-background">H</th><th id="1835743769C8" style="width:100px;" class="column-headers-background">I</th><th id="1835743769C9" style="width:164px;" class="column-headers-background">J</th><th id="1835743769C10" style="width:245px;" class="column-headers-background">K</th><th id="1835743769C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="1835743769R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s2">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1835743769R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="11">CANVASS APPROVAL - Allow Adding of Notes before Approval of Canvass Sheet</td></tr><tr style="height: 19px"><th id="1835743769R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-001</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">Critical</td><td class="s4">Verify that a Canvass Sheet can be submitted for Approval</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Create a new Canvass Sheet or open a drafted one.<br>2. Click the &quot;Submit&quot; button.</td><td class="s4">2. Canvass Sheet is successfully submitted and ready for approval.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-002</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">Minor</td><td class="s4">Verify Canvass Sheet No. redirection.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Navigate to For My Approval/Assigned Tab in Dashboard<br>2. Click the CS No. hyperlink of submitted Canvass Sheet</td><td class="s4">2. Canvass Sheet details page is displayed.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-003</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">High</td><td class="s4">Verify that a floating Confirmation Message appears when initiating approval.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Click on the &quot;Approve&quot; button</td><td class="s4">1. A floating confirmation message for approval is displayed.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="1835743769R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">PRS-004</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">Crtitical</td><td class="s4">Verify that clicking Approve displays a Confirmation Modal with Notes field.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Click on the &quot;Approve&quot; button<br>2. Observe the modal</td><td class="s4">2. A confirmation modal is shown and displayed the following field and buttons:<br>     - Notes<br>     - Add Approver<br>     - Continue<br>     - Cancel</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-005</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">High</td><td class="s4">Verify that Notes field accepts alphanumeric and special characters.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Type a valid note with alphanumeric and special characters.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Note is accepted and approval proceeds.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-006</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">Minor</td><td class="s10">Verify that Notes field does not accept emojis.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Enter emojis in the Notes field.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Error message and approval does not proceed.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-007</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">High</td><td class="s4">Verify that Notes field accepts input up to 100 characters.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Enter exactly 100 characters in the Notes field.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Note is accepted and approval proceeds.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">PRS-008</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">Minor</td><td class="s10">Verify that Notes field does not accept more than 100 characters.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Enter more than 100 characters in the Notes field.<br>2. Attempt to click &quot;Continue&quot; button.</td><td class="s4">2. System prevents entry exceeding 100 characters.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-009</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">High</td><td class="s4">Verify that Notes field is optional.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Leave the Notes field blank.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Approval proceeds without error.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1835743769R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">PRS-010</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">High</td><td class="s4">Validate Cancel button functionality.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Click on &quot;Cancel&quot; button.</td><td class="s4">1. Modal closes and user is returned to Canvass Sheet detail page.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1835743769R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">PRS-011</td><td class="s5"><br>Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s4">High</td><td class="s4">Verify that entered Notes are displayed under the Check Notes button after Approval.</td><td class="s4">1. User is a Canvass Sheet Approver<br>2. A Canvass Sheet has been submitted for Approval</td><td class="s4">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Notes” of the approved Canvass Sheet.<br>3. Verify visibility of approver.<br>4. Verify the badge is cleared.</td><td class="s4">1. “New Attachment” badge is displayed.<br>2. Entered Approval Notes are displayed correctly.<br>3. Approver name should be displayed correctly.<br>4. “New Attachment” badge is cleared when viewed.</td><td class="s8"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr></tbody></table></div>