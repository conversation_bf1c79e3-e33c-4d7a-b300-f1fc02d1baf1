/**
 * Utility functions to convert Zod schemas to OpenAPI schemas
 */
const { z } = require('zod');

/**
 * Converts a Zod schema to an OpenAPI schema
 * 
 * @param {z.ZodType} zodSchema - Zod schema to convert
 * @returns {Object} - OpenAPI schema
 */
function zodToOpenApi(zodSchema) {
  if (!zodSchema) return {};
  
  // Handle primitive types
  if (zodSchema instanceof z.ZodString) {
    const schema = { type: 'string' };
    
    // Add format for email
    if (zodSchema._def.checks) {
      const emailCheck = zodSchema._def.checks.find(check => check.kind === 'email');
      if (emailCheck) {
        schema.format = 'email';
      }
      
      // Add min/max length
      zodSchema._def.checks.forEach(check => {
        if (check.kind === 'min') {
          schema.minLength = check.value;
        } else if (check.kind === 'max') {
          schema.maxLength = check.value;
        } else if (check.kind === 'regex') {
          schema.pattern = check.regex.source;
        } else if (check.kind === 'length') {
          schema.minLength = check.value;
          schema.maxLength = check.value;
        }
      });
    }
    
    return schema;
  }
  
  if (zodSchema instanceof z.ZodNumber) {
    const schema = { type: 'number' };
    
    // Add min/max
    if (zodSchema._def.checks) {
      zodSchema._def.checks.forEach(check => {
        if (check.kind === 'min') {
          schema.minimum = check.value;
        } else if (check.kind === 'max') {
          schema.maximum = check.value;
        } else if (check.kind === 'int') {
          schema.type = 'integer';
        }
      });
    }
    
    return schema;
  }
  
  if (zodSchema instanceof z.ZodBoolean) {
    return { type: 'boolean' };
  }
  
  if (zodSchema instanceof z.ZodNull) {
    return { type: 'null' };
  }
  
  if (zodSchema instanceof z.ZodUndefined) {
    return {};
  }
  
  if (zodSchema instanceof z.ZodArray) {
    return {
      type: 'array',
      items: zodToOpenApi(zodSchema._def.type),
    };
  }
  
  if (zodSchema instanceof z.ZodEnum) {
    return {
      type: 'string',
      enum: zodSchema._def.values,
    };
  }
  
  if (zodSchema instanceof z.ZodNativeEnum) {
    return {
      type: 'string',
      enum: Object.values(zodSchema._def.values),
    };
  }
  
  if (zodSchema instanceof z.ZodObject) {
    const properties = {};
    const required = [];
    
    Object.entries(zodSchema.shape).forEach(([key, value]) => {
      properties[key] = zodToOpenApi(value);
      
      // Check if property is required
      if (!(value instanceof z.ZodOptional) && 
          !(value instanceof z.ZodDefault) && 
          !(value instanceof z.ZodNullable)) {
        required.push(key);
      }
    });
    
    return {
      type: 'object',
      properties,
      ...(required.length > 0 && { required }),
    };
  }
  
  if (zodSchema instanceof z.ZodOptional || zodSchema instanceof z.ZodNullable) {
    return zodToOpenApi(zodSchema._def.innerType);
  }
  
  if (zodSchema instanceof z.ZodDefault) {
    const schema = zodToOpenApi(zodSchema._def.innerType);
    schema.default = zodSchema._def.defaultValue();
    return schema;
  }
  
  if (zodSchema instanceof z.ZodUnion) {
    return {
      oneOf: zodSchema._def.options.map(option => zodToOpenApi(option)),
    };
  }
  
  if (zodSchema instanceof z.ZodIntersection) {
    const left = zodToOpenApi(zodSchema._def.left);
    const right = zodToOpenApi(zodSchema._def.right);
    
    return {
      allOf: [left, right],
    };
  }
  
  if (zodSchema instanceof z.ZodRecord) {
    return {
      type: 'object',
      additionalProperties: zodToOpenApi(zodSchema._def.valueType),
    };
  }
  
  if (zodSchema instanceof z.ZodLiteral) {
    const value = zodSchema._def.value;
    const type = typeof value;
    
    return {
      type,
      enum: [value],
    };
  }
  
  // Default case
  return {};
}

/**
 * Generates OpenAPI schema for a route
 * 
 * @param {Object} options - Schema options
 * @param {Object} options.body - Body schema
 * @param {Object} options.params - Path parameters schema
 * @param {Object} options.querystring - Query parameters schema
 * @param {Object} options.response - Response schema
 * @param {Object} options.headers - Headers schema
 * @returns {Object} - OpenAPI schema
 */
function generateRouteSchema(options = {}) {
  const schema = {};
  
  if (options.body) {
    schema.body = zodToOpenApi(options.body);
  }
  
  if (options.params) {
    schema.params = zodToOpenApi(options.params);
  }
  
  if (options.querystring) {
    schema.querystring = zodToOpenApi(options.querystring);
  }
  
  if (options.response) {
    schema.response = {};
    
    Object.entries(options.response).forEach(([statusCode, responseSchema]) => {
      schema.response[statusCode] = {
        description: getResponseDescription(statusCode),
        content: {
          'application/json': {
            schema: zodToOpenApi(responseSchema),
          },
        },
      };
    });
  }
  
  if (options.headers) {
    schema.headers = zodToOpenApi(options.headers);
  }
  
  return schema;
}

/**
 * Gets a description for a response status code
 * 
 * @param {string|number} statusCode - HTTP status code
 * @returns {string} - Response description
 */
function getResponseDescription(statusCode) {
  const descriptions = {
    '200': 'Successful response',
    '201': 'Resource created successfully',
    '204': 'No content',
    '400': 'Bad request',
    '401': 'Unauthorized',
    '403': 'Forbidden',
    '404': 'Resource not found',
    '409': 'Conflict',
    '422': 'Validation error',
    '500': 'Internal server error',
  };
  
  return descriptions[statusCode] || 'Response';
}

module.exports = {
  zodToOpenApi,
  generateRouteSchema,
};
