<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s20{background-color:#ffff00;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s36{background-color:#ffe599;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{background-color:#a4c2f4;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{background-color:#f6b26b;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s44{background-color:#00ffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s39{background-color:#00ffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{background-color:#ffe599;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#9fc5e8;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{background-color:#f6b26b;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{background-color:#ffe599;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s42{background-color:#00ffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s40{background-color:#e06666;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s43{background-color:#00ffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{background-color:#d9d2e9;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{background-color:#9fc5e8;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{background-color:#ffe599;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{background-color:#d9d2e9;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:12pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s37{background-color:#00ffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{background-color:#ffe599;text-align:center;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s41{background-color:#e06666;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{background-color:#d9d2e9;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-left:none;border-right:none;background-color:#ffe599;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s38{background-color:#00ffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-left:none;background-color:#ffe599;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{background-color:#b6d7a8;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{background-color:#a4c2f4;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-left:none;border-right:none;background-color:#ffe599;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{background-color:#d9d2e9;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s35{background-color:#b6d7a8;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-right:none;background-color:#ffe599;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{background-color:#9fc5e8;text-align:center;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{background-color:#ffe599;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{background-color:#ffe599;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s45{background-color:#b6d7a8;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="2108093799C0" style="width:687px;" class="column-headers-background">A</th><th id="2108093799C1" style="width:100px;" class="column-headers-background">B</th><th id="2108093799C3" style="width:132px;" class="column-headers-background">D</th><th id="2108093799C4" style="width:106px;" class="column-headers-background">E</th><th id="2108093799C5" style="width:119px;" class="column-headers-background">F</th><th id="2108093799C6" style="width:106px;" class="column-headers-background">G</th><th id="2108093799C7" style="width:230px;" class="column-headers-background">H</th></tr></thead><tbody><tr style="height: 31px"><th id="2108093799R0" style="height: 31px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 31px">1</div></th><td class="s0" colspan="2">Stories to be Tested</td><td class="s1"></td><td class="s1"></td><td class="s1">new</td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R40" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">41</div></th><td class="s1"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 22px"><th id="2108093799R41" style="height: 22px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 22px">42</div></th><td class="s3">REVAMP FEATURES</td><td class="s4">TOTAL TCs</td><td class="s3">INTERNS</td><td class="s3">QA</td><td class="s5">TC Completion %</td><td class="s5">TC Review Status</td><td class="s5"></td></tr><tr style="height: 20px"><th id="2108093799R42" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">43</div></th><td class="s6 softmerge"><div class="softmerge-inner" style="width:1023px;left:-1px"><a target="_blank" href="https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=112765653#gid=112765653">https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=112765653#gid=112765653</a></div></td><td class="s7"></td><td class="s8"></td><td class="s9"></td><td class="s9"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 20px"><th id="2108093799R126" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">127</div></th><td class="s11">SPRINT 1 STRETCH GOALS</td><td class="s12"></td><td class="s12"></td><td class="s11"></td><td class="s12"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 20px"><th id="2108093799R127" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">128</div></th><td class="s10">RS APPROVAL</td><td class="s13"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 20px"><th id="2108093799R128" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">129</div></th><td class="s15">Update Adding of Additional Approver before Approval of RS</td><td class="s2">8</td><td class="s2">aira</td><td class="s1">cherry</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R129" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">130</div></th><td class="s10">RS DASHBOARD</td><td class="s17"></td><td class="s17"></td><td class="s10"></td><td class="s17"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 73px"><th id="2108093799R130" style="height: 73px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 73px">131</div></th><td class="s15">RS Dashboard Updates<br>1. Tab Updates<br>2. Update Create New Button<br>3. Dashboard Table Contents</td><td class="s2"></td><td class="s2">regel</td><td class="s1">gela</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R131" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">132</div></th><td class="s10">CANVASS CREATION</td><td class="s17"></td><td class="s17"></td><td class="s10"></td><td class="s17"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 105px"><th id="2108093799R132" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">133</div></th><td class="s15"><span style="font-size:11pt;font-family:Arial;color:#000000;">Update Table Behavior for Canvass Sheet[for Create and Viewing]<br>1. Updating of Supplier Columns<br>2. Removing of Account Code<br>3. Update Actions Buttons<br>4. Update Canvass Cards Names<br></span><span style="font-size:11pt;font-family:Arial;font-weight:bold;color:#000000;">5. Updating behavior of Item Name </span></td><td class="s2">28</td><td class="s2">justine</td><td class="s1">cherry</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s18"><span style="font-family:Calibri,Arial;color:#000000;">Added on Sprint 2 base on V2 </span><span style="font-family:Calibri,Arial;font-weight:bold;color:#000000;">with additonal item #5</span></td></tr><tr style="height: 20px"><th id="2108093799R135" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">136</div></th><td class="s10">USER MANAGEMENT</td><td class="s17"></td><td class="s17"></td><td class="s10"></td><td class="s17"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 20px"><th id="2108093799R136" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">137</div></th><td class="s15">Renaming of Area Staff to Area Staff/Dept Secretary</td><td class="s2">6</td><td class="s19">ghienel</td><td class="s1">gela</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s20" rowspan="3">Sprint 1</td></tr><tr style="height: 20px"><th id="2108093799R137" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">138</div></th><td class="s15">Allow editing of Username by the Root User and IT Admin</td><td class="s2">22</td><td class="s19">ghienel</td><td class="s1">gela</td><td class="s16">100%</td><td class="s1">Completed</td></tr><tr style="height: 20px"><th id="2108093799R138" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">139</div></th><td class="s21">Create User Type for Purchasing Admin</td><td class="s22">4</td><td class="s23">ghienel</td><td class="s24">gela</td><td class="s16">100%</td><td class="s24">Completed</td></tr><tr style="height: 20px"><th id="2108093799R139" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">140</div></th><td class="s25">Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s2">11</td><td class="s2">verna</td><td class="s1">cherry</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R140" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">141</div></th><td class="s25">Adding of Notes during Purchase Order Approval</td><td class="s2">11</td><td class="s2">verna</td><td class="s1">gela</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 19px"><th id="2108093799R141" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">142</div></th><td></td><td class="s26"></td><td></td><td class="s27"></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="2108093799R142" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">143</div></th><td class="s10">PURCHASE ORDER - VIEWING</td><td class="s17"></td><td class="s17"></td><td class="s10"></td><td class="s17"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 97px"><th id="2108093799R143" style="height: 97px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 97px">144</div></th><td class="s18">Update Purchase Order Items Table<br>[for Create and Viewing]<br><br>1. Remove Account Code<br>2. Display Full Item Name</td><td class="s2">18</td><td class="s2">verna</td><td class="s1">gela</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R144" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">145</div></th><td class="s28">PURCHASE ORDER - APPROVAL</td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 29px"><th id="2108093799R145" style="height: 29px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 29px">146</div></th><td class="s29">Adding of Notes during Purchase Order Approval</td><td class="s30">11</td><td class="s2">verna</td><td class="s29"></td><td class="s16">100%</td><td class="s29"></td><td class="s20">added as sprint 1 stretch goals in YT</td></tr><tr style="height: 20px"><th id="2108093799R146" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">147</div></th><td class="s31">SPRINT 2</td><td class="s32"></td><td class="s32"></td><td class="s31"></td><td class="s32"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="2108093799R147" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">148</div></th><td class="s28">ITEM MANAGEMENT</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R148" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">149</div></th><td class="s29">Allow Decimal to OFM Quanity</td><td class="s34"></td><td class="s34">aira</td><td class="s29"></td><td class="s35">100%</td><td class="s29"></td><td class="s29"></td></tr><tr style="height: 20px"><th id="2108093799R149" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">150</div></th><td class="s29">Allow Decimal to Non-OFM Quanity</td><td class="s34"></td><td class="s34">aira</td><td class="s29"></td><td class="s35">100%</td><td class="s29"></td><td class="s29"></td></tr><tr style="height: 20px"><th id="2108093799R150" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">151</div></th><td class="s28">REQUISITION SLIP - VIEWING</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R151" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">152</div></th><td class="s29">Requisition Slip Download to PDF</td><td class="s34"></td><td class="s34"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R152" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">153</div></th><td class="s36">RS DASHBOARD</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 68px"><th id="2108093799R153" style="height: 68px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 68px">154</div></th><td class="s37">RS Dashboard Updates<br><br>1. Sorting of Closed RS and related Documents</td><td class="s38"></td><td class="s38">Ghienel</td><td class="s39">Cherry</td><td class="s38">100%</td><td class="s39">Completed</td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R154" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">155</div></th><td class="s36">RS APPROVAL</td><td class="s17"></td><td class="s17"></td><td class="s10"></td><td class="s17"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 20px"><th id="2108093799R155" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">156</div></th><td class="s18">Enabling RS Optional Approver</td><td class="s2"></td><td class="s2">Ghienel</td><td class="s1">Cherry</td><td class="s16">100%</td><td class="s1">In Progress</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R156" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">157</div></th><td class="s18">Allow Adding of Notes before Approval of RS</td><td class="s2"></td><td class="s2">Justine</td><td class="s1"></td><td class="s2">100%</td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R157" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">158</div></th><td class="s28">REQUISITION SLIP - ADDING OF ITEMS</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R158" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">159</div></th><td class="s18">Adding of Non-OFM Items for existing Non-OFM Items</td><td class="s2"></td><td class="s2">Ghienel</td><td class="s1">Cherry</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R159" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">160</div></th><td class="s28">CANVASS - CREATION</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R160" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">161</div></th><td class="s18">Entering of OFM and Non-OFM Quantity per Supplier</td><td class="s2"></td><td class="s2">Ghienel</td><td class="s1">Cherry</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R161" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">162</div></th><td class="s29">Canvass Status Updates</td><td class="s2"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R162" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">163</div></th><td class="s29">Item Group</td><td class="s2"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R163" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">164</div></th><td class="s28">CANVASS - VIEWING</td><td class="s33"></td><td class="s33"></td><td class="s28"></td><td class="s33"></td><td class="s33"></td><td class="s33"></td></tr><tr style="height: 20px"><th id="2108093799R164" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">165</div></th><td class="s40">Update Canvass Sheet Related Documents /  Update Related Documents Table</td><td class="s38">14</td><td class="s38">verna</td><td class="s39"></td><td class="s38">100%</td><td class="s39"></td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R165" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">166</div></th><td class="s28">CANVASS - APPROVAL</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R166" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">167</div></th><td class="s41">Allow Adding of Notes before Approval of Canvass Sheet</td><td class="s38">11</td><td class="s38">justine</td><td class="s39">Cherry</td><td class="s38">100%</td><td class="s39">Completed</td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R167" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">168</div></th><td class="s28">PURCHASE ORDER - VIEWING</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R168" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">169</div></th><td class="s42">Cancellation of Purchase Order before Delivery Receipt is Fully Delivered</td><td class="s38">13</td><td class="s38">verna</td><td class="s39"></td><td class="s38">100%</td><td class="s39"></td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R169" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">170</div></th><td class="s42">Purchase Order Download to PDF</td><td class="s38"></td><td class="s38">aira</td><td class="s39"></td><td class="s39"></td><td class="s39"></td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R170" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">171</div></th><td class="s29">Add New Field for New Delivery Address</td><td class="s2"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R171" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">172</div></th><td class="s29">Add Discounts Field and Computation if the Purchase Order</td><td class="s2"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R172" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">173</div></th><td class="s28">PURCHASE ORDER - APPROVAL</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 19px"><th id="2108093799R173" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">174</div></th><td class="s37">Update Adding of Additional Approver for Purchase Order Approval /Update Adding of Approver behavior for Purchase Order Approval</td><td class="s43">21</td><td class="s43">verna</td><td class="s44"></td><td class="s38">100%</td><td class="s44"></td><td class="s44">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R174" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">175</div></th><td class="s42">Purchase Order Status Updates</td><td class="s38"></td><td class="s38">verna</td><td class="s39"></td><td class="s38">100%</td><td class="s39"></td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R175" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">176</div></th><td class="s28" colspan="7">DELIVERY RECEIPT</td></tr><tr style="height: 19px"><th id="2108093799R176" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">177</div></th><td class="s29">Allow the Purchasing Staff to create the Delivery Receipt on behalf of the Requester</td><td class="s2"></td><td class="s2">aira</td><td class="s1">Cherry</td><td class="s45">100%</td><td class="s1">In Progress</td><td class="s1"></td></tr><tr style="height: 19px"><th id="2108093799R177" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">178</div></th><td class="s29">Update Delivery Receipt Form<br>[for Create and Viewing]</td><td class="s2"></td><td class="s2">regel</td><td class="s1"></td><td class="s16">100%</td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R178" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">179</div></th><td class="s29">Multiple DR per PO</td><td class="s2"></td><td class="s2">regel</td><td class="s1"></td><td class="s2">60%</td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2108093799R179" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">180</div></th><td class="s29">Update Delivery Receipt Items Table<br>[for Create and Viewing]<br><br>1. Remove Account Code<br>2. Display Full Item Name</td><td class="s2"></td><td class="s2">regel</td><td class="s1"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R180" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">181</div></th><td class="s42">Delivery Receipt Status Updates</td><td class="s38"></td><td class="s38">aira</td><td class="s39"></td><td class="s39"></td><td class="s39"></td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R181" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">182</div></th><td class="s28">INVOICE</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R182" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">183</div></th><td class="s18">Invoice Entry Points and Invoice Creation</td><td class="s2"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R183" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">184</div></th><td class="s18">Adding of Invoice Tab in Related Documents</td><td class="s2">17</td><td class="s2">justine</td><td class="s1">MeAnn</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1"></td></tr><tr style="height: 20px"><th id="2108093799R184" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">185</div></th><td class="s18">Adding of Invoice Tab in Request History</td><td class="s2"></td><td class="s2"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R185" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">186</div></th><td class="s28">PAYMENT REQUEST - APPROVAL</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R186" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">187</div></th><td class="s42">Update Adding of Additional Approver</td><td class="s38">14</td><td class="s38">justine</td><td class="s39">MeAnn</td><td class="s38">100%</td><td class="s39"></td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R187" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">188</div></th><td class="s42">Adding of Notes during Approval</td><td class="s38">11</td><td class="s38">justine</td><td class="s39">Cherry</td><td class="s38">100%</td><td class="s39">Completed</td><td class="s39">Not Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R188" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">189</div></th><td class="s28">Non-RS</td><td class="s33"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td><td class="s28"></td></tr><tr style="height: 20px"><th id="2108093799R189" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">190</div></th><td class="s29">Allow Adding of Notes before Approval of Non-RS</td><td class="s2"></td><td class="s2">regel</td><td class="s1">MeAnn</td><td class="s16">100%</td><td class="s1">Completed</td><td class="s1">Added on Sprint 2 base on V2</td></tr><tr style="height: 20px"><th id="2108093799R190" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">191</div></th><td class="s29">Non-RS Payment Request Download to PDF</td><td class="s2">13</td><td class="s2">justine</td><td class="s1">MeAnn</td><td class="s16">100%</td><td class="s1"></td><td class="s1">Added on Sprint 2 base on V2</td></tr></tbody></table></div>