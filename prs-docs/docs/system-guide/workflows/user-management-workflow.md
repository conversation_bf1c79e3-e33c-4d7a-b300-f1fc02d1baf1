# User Management Workflow

This document outlines the complete workflow for managing users in the PRS system, including user creation, role assignment, and account management.

## Workflow Diagram

```mermaid
flowchart TD
  Start([Create User]) -->S2[Active]
  S2 -->|Deactivate| S3[Inactive]
  S3 -->|Reactivate| S2
```

## Status Definitions

| Status | Description |
|--------|-------------|
| ACTIVE | Default status upon creation, user account is active and can access the system |
| INACTIVE | User account is temporarily deactivated but can be reactivated |

## Detailed Workflow Steps

### 1. User Creation

**Actor**: IT Admin and Root User

**Actions**:

- Create a new user account
- Fill in required information (name, email, username, etc.)
- Assign role(s)
- Assign department

**Status Transitions**:

- New → ACTIVE

**Business Rules**:

- Only Root User can create Admin users only
- Admin users can create other types of users
- Username must be unique
- Email is optional
- User must be assigned one user type
- User must be assigned to a department
- User supervisor must be filled out
- Temporary password will be auto-generated
- Initial password must meet complexity requirements


### 2. User Deactivation

**Actor**: IT Admin and Root User

**Actions**:

- Deactivate a user account
- Provide deactivation reason

**Status Transitions**:

- ACTIVE → INACTIVE

**Business Rules**:

- Deactivation requires a reason
- Deactivated users cannot log in
- Deactivated users' pending approvals may need reassignment
- Deactivation doesn't delete the user account

### 3. User Re-activation

**Actor**: IT Admin and Root User

**Actions**:

- Verify user information
- Activate the user account

**Status Transitions**:

- INACTIVE → ACTIVE

**Business Rules**:

- All required fields must be filled

### 6. Password Management

**Actor**: IT Admin and Root User

**Actions**:

- Receive notification on password reset request
- Reset user password

**Status Transitions**:

- No status change (password changes don't affect account status)

**Business Rules**:

- Upon resetting a password, the system will regenerate new temporary password
- Resetting a password will set user's password to new temporary password

## Example Scenarios

### Scenario 1: New Employee Onboarding

1. Admin creates a new user account with employee information
2. Admin assigns appropriate role based on job function
3. Employee receives credentials offline from IT Admin
4. Employee activates account and sets password
5. Employee can now access the system based on assigned role

### Scenario 2: Employee Role Change

1. Employee changes department or position
2. Admin updates user's department and role assignment
3. System updates permissions based on new roles
4. Employee now has access to different functions in the system

### Scenario 3: Employee Departure

1. Employee leaves the organization
2. Admin deactivates the user account
3. System to notify admin of pending approvals
4. Admin reassigns any pending approvals to alternates -_for confirmation_
5. Employee can no longer access the system 

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/userController.js`
- **Service**: `src/app/services/userService.js`
- **Repository**: `src/infra/repositories/userRepository.js`
- **Entity**: `src/domain/entities/userEntity.js`
- **Constants**: `src/domain/constants/userConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic
async function updateUserStatus(userId, newStatus, reason, transaction) {
  const user = await userRepository.findOne({
    where: { id: userId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'PENDING': ['ACTIVE', 'INACTIVE'],
    'ACTIVE': ['INACTIVE', 'SUSPENDED'],
    'INACTIVE': ['ACTIVE', 'SUSPENDED'],
    'SUSPENDED': ['ACTIVE', 'INACTIVE'],
  };
  
  if (!validTransitions[user.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${user.status} to ${newStatus}`);
  }
  
  // Additional validation for suspension or deactivation
  if ((newStatus === 'SUSPENDED' || newStatus === 'INACTIVE') && !reason) {
    throw new Error(`Reason is required for ${newStatus} status`);
  }
  
  // Update status
  await user.update({ 
    status: newStatus,
    statusReason: ['SUSPENDED', 'INACTIVE'].includes(newStatus) ? reason : null,
    statusChangedAt: new Date(),
  }, { transaction });
  
  // Handle side effects of status change
  if (newStatus === 'INACTIVE' || newStatus === 'SUSPENDED') {
    await handleUserDeactivation(userId, transaction);
  }
  
  return user;
}

// Handle side effects of user deactivation
async function handleUserDeactivation(userId, transaction) {
  // Reassign pending approvals
  await reassignPendingApprovals(userId, transaction);
  
  // Revoke active sessions
  await revokeUserSessions(userId, transaction);
  
  // Log deactivation event
  await logUserEvent(userId, 'DEACTIVATED', transaction);
}

// Reassign pending approvals to alternates
async function reassignPendingApprovals(userId, transaction) {
  // Reassign requisition approvals
  const requisitionApprovers = await requisitionApproverRepository.findAll({
    where: { 
      userId,
      status: 'PENDING',
    },
    transaction,
  });
  
  for (const approver of requisitionApprovers) {
    // Find alternate approver
    const alternateApprover = await findAlternateApprover(approver.roleId, transaction);
    
    if (alternateApprover) {
      await approver.update({ 
        altApproverId: alternateApprover.id,
      }, { transaction });
    }
  }
  
  // Similar logic for canvass approvers, purchase order approvers, etc.
}
```

## Common Issues and Solutions

### Issue 1: User Cannot Log In

**Cause**: Account status issues or password problems.

**Solution**:

- Check account status (should be ACTIVE)
- Reset password if needed

### Issue 2: User Missing Permissions

**Cause**: Incorrect role assignment or permission configuration.

**Solution**:

- Verify user's role assignment
- Check role permissions configuration
- Ensure user's department matches required permissions

### Issue 3: Cannot Deactivate User

**Cause**: User has critical pending actions or is the only user with certain permissions.

**Solution**:

- Reassign pending approvals manually
- Ensure another user has the same critical permissions
- Complete or cancel user's pending transactions
