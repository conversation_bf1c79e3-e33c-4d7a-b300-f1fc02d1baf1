# Leveraging PostgreSQL Partitioning in the PRS System

This document outlines strategies for implementing PostgreSQL partitioning in the Purchase Requisition System (PRS) to support the requirements for data preservation and no deletion.

## Benefits of PostgreSQL Partitioning for PRS

1. **Improved Query Performance**
   - Queries that filter on the partition key will only scan relevant partitions
   - Critical for a system where historical data must be preserved indefinitely

2. **Efficient Data Management**
   - Easier maintenance of older, less frequently accessed data
   - Ability to apply different storage parameters to different partitions

3. **Scalability**
   - Better handling of very large tables without performance degradation
   - Supports continued growth of your system over years of operation

4. **Archiving Strategy**
   - Natural fit for implementing a tiered storage approach without deletion
   - Older partitions can be moved to slower, cheaper storage

## Partitioning Strategies for PRS

### 1. Time-Based Partitioning

Ideal for requisition and transaction tables:

```sql
CREATE TABLE requisitions (
    id SERIAL,
    requisition_number VARCHAR(50),
    requester_id INTEGER,
    department_id INTEGER,
    status VARCHAR(50),
    total_amount DECIMAL(15,2),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    -- other fields
) PARTITION BY RANGE (created_at);

-- Create partitions by quarter
CREATE TABLE requisitions_q1_2023 PARTITION OF requisitions
    FOR VALUES FROM ('2023-01-01') TO ('2023-04-01');
    
CREATE TABLE requisitions_q2_2023 PARTITION OF requisitions
    FOR VALUES FROM ('2023-04-01') TO ('2023-07-01');
    
-- And so on...
```

Benefits:
- Natural organization by time periods
- Easy archiving of older data
- Efficient queries for date ranges (common in reporting)

### 2. Status-Based Partitioning

For systems with distinct lifecycle stages:

```sql
CREATE TABLE requisitions (
    -- fields as above
) PARTITION BY LIST (status);

-- Active requisitions (frequently accessed)
CREATE TABLE requisitions_active PARTITION OF requisitions
    FOR VALUES IN ('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'PENDING_APPROVAL');
    
-- Completed requisitions (less frequently accessed)
CREATE TABLE requisitions_completed PARTITION OF requisitions
    FOR VALUES IN ('APPROVED', 'FULFILLED', 'CLOSED');
    
-- Rejected requisitions (rarely accessed)
CREATE TABLE requisitions_rejected PARTITION OF requisitions
    FOR VALUES IN ('REJECTED', 'CANCELLED');
```

Benefits:
- Optimizes for workflow-based access patterns
- Active requisitions stay in high-performance storage
- Completed/rejected requisitions can use cheaper storage

### 3. Composite Partitioning

For more complex scenarios:

```sql
-- First partition by year, then by status
CREATE TABLE requisitions (
    -- fields
) PARTITION BY RANGE (created_at);

CREATE TABLE requisitions_2023 PARTITION OF requisitions
    FOR VALUES FROM ('2023-01-01') TO ('2024-01-01')
    PARTITION BY LIST (status);
    
CREATE TABLE requisitions_2023_active PARTITION OF requisitions_2023
    FOR VALUES IN ('DRAFT', 'SUBMITTED', 'UNDER_REVIEW');
    
-- And so on...
```

Benefits:
- Combines advantages of both time and status partitioning
- Highly granular control over data placement
- Most efficient for complex query patterns

## Implementation in Next.js with Prisma

Prisma now supports PostgreSQL partitioning. Here's how to implement it:

### 1. Define Your Schema

```prisma
// schema.prisma
model Requisition {
  id                Int      @id @default(autoincrement())
  requisitionNumber String   @unique
  requesterId       Int
  departmentId      Int
  status            String
  totalAmount       Decimal  @db.Decimal(15, 2)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  requester         User     @relation(fields: [requesterId], references: [id])
  department        Department @relation(fields: [departmentId], references: [id])
  lineItems         LineItem[]
  approvals         Approval[]
  
  @@index([createdAt])
  @@index([status])
}
```

### 2. Create Migration Scripts

Since Prisma doesn't directly support defining partitions, you'll need to use raw SQL in migrations:

```typescript
// prisma/migrations/[timestamp]_add_partitioning/migration.sql
-- First create the table through Prisma's normal migration
-- Then modify it for partitioning

-- Convert to partitioned table
ALTER TABLE "Requisition" SET UNLOGGED;
ALTER TABLE "Requisition" RENAME TO "Requisition_old";

-- Create new partitioned table
CREATE TABLE "Requisition" (
  -- copy all column definitions
) PARTITION BY RANGE (created_at);

-- Create partitions
CREATE TABLE "Requisition_2023_q1" PARTITION OF "Requisition"
  FOR VALUES FROM ('2023-01-01') TO ('2023-04-01');
  
-- Copy data
INSERT INTO "Requisition" SELECT * FROM "Requisition_old";

-- Drop old table
DROP TABLE "Requisition_old";
```

### 3. Automated Partition Management

Create a scheduled job to create new partitions in advance:

```typescript
// lib/db/partitionManager.ts
export async function createNextQuarterPartition() {
  const nextQuarter = getNextQuarterDate();
  const endDate = getQuarterEndDate(nextQuarter);
  
  const partitionName = `Requisition_${nextQuarter.getFullYear()}_q${getQuarter(nextQuarter)}`;
  
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "${partitionName}" PARTITION OF "Requisition"
    FOR VALUES FROM (${nextQuarter.toISOString()}) TO (${endDate.toISOString()});
  `;
}

function getNextQuarterDate() {
  const now = new Date();
  const currentQuarter = Math.floor(now.getMonth() / 3);
  const nextQuarter = (currentQuarter + 1) % 4;
  const year = nextQuarter === 0 ? now.getFullYear() + 1 : now.getFullYear();
  
  return new Date(year, nextQuarter * 3, 1);
}

function getQuarterEndDate(startDate) {
  const year = startDate.getFullYear();
  const month = startDate.getMonth();
  const nextQuarterMonth = month + 3;
  
  return new Date(
    nextQuarterMonth >= 12 ? year + 1 : year,
    nextQuarterMonth % 12,
    1
  );
}

function getQuarter(date) {
  return Math.floor(date.getMonth() / 3) + 1;
}
```

## Query Optimization with Partitioning

### 1. Ensure Queries Use Partition Keys

```typescript
// This will scan only relevant partitions
const recentRequisitions = await prisma.requisition.findMany({
  where: {
    createdAt: {
      gte: new Date('2023-01-01'),
      lt: new Date('2023-04-01')
    }
  }
});
```

### 2. Partition Pruning Awareness

Make your application aware of the partitioning scheme to optimize queries:

```typescript
function getRequisitionsByDateRange(startDate: Date, endDate: Date) {
  // Ensure date ranges align with partition boundaries when possible
  const alignedStartDate = alignToPartitionBoundary(startDate);
  const alignedEndDate = alignToPartitionBoundary(endDate, true);
  
  return prisma.requisition.findMany({
    where: {
      createdAt: {
        gte: alignedStartDate,
        lt: alignedEndDate
      }
    }
  });
}

function alignToPartitionBoundary(date: Date, isEndDate = false) {
  const year = date.getFullYear();
  const month = date.getMonth();
  const quarter = Math.floor(month / 3);
  
  if (isEndDate) {
    // Align to end of quarter
    return new Date(year, (quarter + 1) * 3, 1);
  } else {
    // Align to start of quarter
    return new Date(year, quarter * 3, 1);
  }
}
```

## Archiving Strategy with Partitions

For your "no deletion" requirement:

### 1. Tiered Storage Approach

```sql
-- Move older partitions to a tablespace on cheaper storage
ALTER TABLE requisitions_2020 SET TABLESPACE archive_storage;
```

### 2. Detach Instead of Drop

```sql
-- Instead of dropping, detach the partition
ALTER TABLE requisitions DETACH PARTITION requisitions_2020;

-- The data remains accessible but in a separate table
-- You can still query it directly when needed
SELECT * FROM requisitions_2020 WHERE id = 12345;
```

## Performance Monitoring

Implement monitoring to ensure partitioning is working effectively:

```sql
-- Check partition usage
SELECT
    schemaname, 
    tablename, 
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables
WHERE tablename LIKE 'requisitions%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## Implementation Considerations for Next.js

1. **Database Connection Pool**
   - Configure connection pooling appropriately for partitioned tables
   - Consider using PgBouncer for connection management

2. **Query Timeouts**
   - Set appropriate query timeouts for operations that span multiple partitions
   - Implement retry logic for long-running queries

3. **Pagination Strategies**
   - Use keyset pagination instead of offset pagination for better performance
   - Consider partition-aware pagination for large datasets

4. **Backup and Recovery**
   - Implement partition-aware backup strategies
   - Consider backing up active partitions more frequently than archived ones

## Conclusion

PostgreSQL partitioning is an excellent fit for the PRS system, particularly with the requirements for data preservation and no deletion. It provides:

1. **Performance at Scale**: Maintains query performance even as your data grows indefinitely
2. **Compliance Support**: Facilitates data retention without deletion
3. **Cost Efficiency**: Allows tiered storage strategies for older data
4. **Query Optimization**: Improves performance for date-range and status-based queries

When implementing this with Next.js, you'll need to handle some of the partitioning logic through raw SQL migrations, but the performance benefits make this well worth the effort, especially for a system that must maintain all historical data indefinitely.
