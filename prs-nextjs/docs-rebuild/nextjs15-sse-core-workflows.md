# Core Business Workflows Implementation with Next.js 15 and SSE

This document outlines how to implement the core business workflows of the PRS system using Next.js 15 with Server-Sent Events (SSE) for real-time updates.

## Table of Contents

1. [Requisition Slip (RS) Workflow](#requisition-slip-rs-workflow)
2. [Non-Requisition Slip (Non-RS) Workflow](#non-requisition-slip-non-rs-workflow)
3. [Purchase Order Workflow](#purchase-order-workflow)
4. [Delivery Receipt Workflow](#delivery-receipt-workflow)
5. [Payment Request Workflow](#payment-request-workflow)
6. [Approval Workflows](#approval-workflows)
7. [Real-Time Updates with SSE](#real-time-updates-with-sse)

## Requisition Slip (RS) Workflow

The Requisition Slip workflow is the primary workflow in the application:

```
Create RS → Submit for Approval → Approval Process → Canvassing → Canvassing Approval → Purchase Order → Purchase Order Approval → Delivery & Invoice → Payment → Payment Approval
```

The complete workflow can be visualized as:

```mermaid
flowchart LR
    RS["Create RS"] --> SFA["Submit for Approval"]
    SFA --> AP["Approval Process"]
    AP --> CAN["Canvassing"]
    CAN --> CANA["Canvassing Approval"]
    CANA --> PO["Purchase Order"]
    PO --> POA["Purchase Order Approval"]
    POA --> DEL["Delivery"] & INV["Invoice"]
    DEL <--> INV
    DEL --> PAY["Payment"]
    INV --> PAY
    PAY --> PAYA["Payment Approval"]
```

This workflow includes multiple approval steps (highlighted in the diagram) that ensure proper oversight throughout the procurement process.

### Implementation with Next.js 15

#### 1. Requisition Creation

```typescript
// app/requisitions/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { RequisitionSchema } from '@/models/requisition'

export async function createRequisition(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')

  const userId = parseInt(session.user.id)

  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = RequisitionSchema.parse(rawData)

  // Create requisition
  const requisition = await prisma.requisition.create({
    data: {
      type: validatedData.type,
      status: validatedData.isDraft ? 'draft' : 'submitted',
      departmentId: validatedData.departmentId,
      projectId: validatedData.projectId,
      companyId: validatedData.companyId,
      createdById: userId,
      dateRequired: validatedData.dateRequired,
      deliverTo: validatedData.deliverTo,
      purpose: validatedData.purpose,
      category: validatedData.category, // Category (company, association, project)
      chargeTo: validatedData.chargeTo,
      chargeToId: validatedData.chargeToId,
      // Generate RS number if not draft
      ...(validatedData.isDraft
        ? { draftRsNumber: `DRAFT-${Date.now()}` }
        : { rsNumber: `RS-${Date.now()}` })
    }
  })

  // Create requisition items
  if (validatedData.items && validatedData.items.length > 0) {
    await prisma.requisitionItem.createMany({
      data: validatedData.items.map(item => ({
        requisitionId: requisition.id,
        itemId: item.itemId,
        quantity: item.quantity,
        notes: item.notes
      }))
    })
  }

  // If submitted, create approvals
  if (!validatedData.isDraft) {
    // Get approvers based on department and project
    const approvers = await getApprovers(
      validatedData.departmentId,
      validatedData.projectId
    )

    // Create approval records
    await prisma.approval.createMany({
      data: approvers.map((approver, index) => ({
        requisitionId: requisition.id,
        userId: approver.userId,
        level: index + 1,
        status: 'pending'
      }))
    })

    // Notify approvers
    await notifyUsers(
      approvers.map(a => a.userId),
      {
        type: 'APPROVAL_REQUESTED',
        title: 'New Approval Request',
        message: `A new requisition (${requisition.rsNumber}) requires your approval`,
        referenceId: requisition.id,
        referenceType: 'requisition'
      }
    )
  }

  // Revalidate paths
  revalidatePath('/requisitions')
  revalidatePath(`/requisitions/${requisition.id}`)

  return { success: true, id: requisition.id }
}

// Helper function to get approvers
async function getApprovers(departmentId: number, projectId?: number) {
  // Get department approvers
  const departmentApprovers = await prisma.departmentApproval.findMany({
    where: { departmentId },
    orderBy: { level: 'asc' }
  })

  // Get project approvers if project is specified
  const projectApprovers = projectId
    ? await prisma.projectApproval.findMany({
        where: { projectId },
        orderBy: { level: 'asc' }
      })
    : []

  // Combine and sort approvers by level
  const allApprovers = [...departmentApprovers, ...projectApprovers]
    .sort((a, b) => a.level - b.level)

  return allApprovers
}
```

#### 2. Requisition Submission Page

```tsx
// app/requisitions/new/page.tsx
import { RequisitionForm } from '@/components/requisitions/RequisitionForm'
import { getCompanies, getDepartments, getProjects } from '@/lib/data'

export default async function NewRequisitionPage() {
  // Fetch data for form dropdowns
  const companies = await getCompanies()
  const departments = await getDepartments()
  const projects = await getProjects()

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Create New Requisition</h1>
      <RequisitionForm
        companies={companies}
        departments={departments}
        projects={projects}
      />
    </div>
  )
}
```

#### 3. Requisition Form Component

```tsx
// components/requisitions/RequisitionForm.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createRequisition } from '@/app/requisitions/actions'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ItemSelector } from '@/components/items/ItemSelector'

export function RequisitionForm({ companies, departments, projects }) {
  const router = useRouter()
  const [items, setItems] = useState([])
  const [isDraft, setIsDraft] = useState(true)
  const [categoryOptions] = useState([
    { key: 'Company', value: 'company' },
    { key: 'Association', value: 'association' },
    { key: 'Project', value: 'project' }
  ])

  const handleSubmit = async (e) => {
    e.preventDefault()

    const form = e.target
    const formData = new FormData(form)

    // Add items to form data
    formData.append('items', JSON.stringify(items))

    // Add draft status
    formData.append('isDraft', isDraft.toString())

    // Submit form
    const result = await createRequisition(formData)

    if (result.success) {
      router.push(`/requisitions/${result.id}`)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Form fields for requisition details */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="category">Category</label>
          <Select id="category" name="category" required>
            {categoryOptions.map(option => (
              <option key={option.value} value={option.value}>{option.key}</option>
            ))}
          </Select>
        </div>

        <div>
          <label htmlFor="type">Type</label>
          <Select id="type" name="type" required>
            <option value="ofm">OFM</option>
            <option value="non-ofm">Non-OFM</option>
            <option value="ofm-tom">OFM-TOM</option>
            <option value="non-ofm-tom">Non-OFM-TOM</option>
          </Select>
        </div>

        <div>
          <label htmlFor="departmentId">Department</label>
          <Select id="departmentId" name="departmentId" required>
            {departments.map(dept => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </Select>
        </div>

        {/* Additional form fields */}
      </div>

      {/* Item selection */}
      <div>
        <h3 className="text-lg font-medium mb-2">Items</h3>
        <ItemSelector
          items={items}
          onChange={setItems}
        />
      </div>

      {/* Submit buttons */}
      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          variant="outline"
          onClick={() => setIsDraft(true)}
        >
          Save as Draft
        </Button>
        <Button
          type="submit"
          onClick={() => setIsDraft(false)}
        >
          Submit for Approval
        </Button>
      </div>
    </form>
  )
}
```

## Non-Requisition Slip (Non-RS) Workflow

The Non-Requisition Slip workflow allows users to create and manage purchase requisitions that don't follow the standard process:

```
Create Non-RS → Submit for Approval → Approval Process → Payment
```

### Implementation with Next.js 15

#### 1. Non-RS Creation Action

```typescript
// app/non-rs/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { NonRSSchema } from '@/models/non-rs'

export async function createNonRS(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')

  const userId = parseInt(session.user.id)

  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = NonRSSchema.parse(rawData)

  // Create non-RS record
  const nonRS = await prisma.nonRequisition.create({
    data: {
      type: validatedData.type,
      status: validatedData.isDraft ? 'draft' : 'submitted',
      departmentId: validatedData.departmentId,
      projectId: validatedData.projectId,
      companyId: validatedData.companyId,
      createdById: userId,
      supplierName: validatedData.supplierName,
      supplierAddress: validatedData.supplierAddress,
      invoiceNumber: validatedData.invoiceNumber,
      invoiceDate: validatedData.invoiceDate,
      totalAmount: validatedData.totalAmount,
      purpose: validatedData.purpose,
      // Generate Non-RS number if not draft
      ...(validatedData.isDraft
        ? { draftNonRsNumber: `DRAFT-NRS-${Date.now()}` }
        : { nonRsNumber: `NRS-${Date.now()}` })
    }
  })

  // Create non-RS items
  if (validatedData.items && validatedData.items.length > 0) {
    await prisma.nonRequisitionItem.createMany({
      data: validatedData.items.map(item => ({
        nonRequisitionId: nonRS.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        amount: item.amount
      }))
    })
  }

  // If submitted, create approvals
  if (!validatedData.isDraft) {
    // Similar approval logic as RS
    // ...
  }

  // Revalidate paths
  revalidatePath('/non-rs')
  revalidatePath(`/non-rs/${nonRS.id}`)

  return { success: true, id: nonRS.id }
}
```

## Purchase Order Workflow

The purchase order workflow allows users to create and manage purchase orders based on approved canvassing:

```
Purchase Order → Purchase Order Approval → Delivery & Invoice
```

This workflow follows the canvassing approval and is a critical step in the procurement process:

### Implementation with Next.js 15

#### 1. Purchase Order Creation

```typescript
// app/purchase-orders/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { PurchaseOrderSchema } from '@/models/purchase-order'

export async function createPurchaseOrder(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')

  const userId = parseInt(session.user.id)

  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = PurchaseOrderSchema.parse(rawData)

  // Get the requisition
  const requisition = await prisma.requisition.findUnique({
    where: { id: validatedData.requisitionId },
    include: { items: true }
  })

  if (!requisition) throw new Error('Requisition not found')

  // Create purchase order
  const purchaseOrder = await prisma.purchaseOrder.create({
    data: {
      requisitionId: requisition.id,
      supplierId: validatedData.supplierId,
      status: validatedData.isDraft ? 'draft' : 'submitted',
      poNumber: validatedData.isDraft ? null : `PO-${Date.now()}`,
      draftPoNumber: validatedData.isDraft ? `DRAFT-PO-${Date.now()}` : null,
      deliveryDate: validatedData.deliveryDate,
      terms: validatedData.terms,
      createdById: userId,
      totalAmount: validatedData.totalAmount
    }
  })

  // Create purchase order items
  if (validatedData.items && validatedData.items.length > 0) {
    await prisma.purchaseOrderItem.createMany({
      data: validatedData.items.map(item => ({
        purchaseOrderId: purchaseOrder.id,
        requisitionItemId: item.requisitionItemId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        amount: item.amount
      }))
    })
  }

  // If submitted, update requisition status and create approvals
  if (!validatedData.isDraft) {
    await prisma.requisition.update({
      where: { id: requisition.id },
      data: { status: 'for_po_review' }
    })

    // Create approvals
    // ...
  }

  // Revalidate paths
  revalidatePath('/purchase-orders')
  revalidatePath(`/purchase-orders/${purchaseOrder.id}`)
  revalidatePath(`/requisitions/${requisition.id}`)

  return { success: true, id: purchaseOrder.id }
}
```
