<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#538ed5;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1365549434C0" style="width:103px;" class="column-headers-background">A</th><th id="1365549434C1" style="width:98px;" class="column-headers-background">B</th><th id="1365549434C2" style="width:77px;" class="column-headers-background">C</th><th id="1365549434C3" style="width:252px;" class="column-headers-background">D</th><th id="1365549434C4" style="width:233px;" class="column-headers-background">E</th><th id="1365549434C5" style="width:277px;" class="column-headers-background">F</th><th id="1365549434C6" style="width:78px;" class="column-headers-background">G</th><th id="1365549434C7" style="width:595px;" class="column-headers-background">H</th><th id="1365549434C8" style="width:100px;" class="column-headers-background">I</th><th id="1365549434C9" style="width:88px;" class="column-headers-background">J</th><th id="1365549434C10" style="width:148px;" class="column-headers-background">K</th><th id="1365549434C11" style="width:148px;" class="column-headers-background">L</th><th id="1365549434C12" style="width:148px;" class="column-headers-background">M</th><th id="1365549434C13" style="width:148px;" class="column-headers-background">N</th><th id="1365549434C14" style="width:78px;" class="column-headers-background">O</th><th id="1365549434C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1365549434R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s3">Actual Results</td><td class="s3">STG</td><td class="s4">Status<br>(E2E_Run1)</td><td class="s4">Actual Results<br>(E2E_Run1)</td><td class="s4">Status<br>(E2E_Run2)</td><td class="s4">Actual Result<br>(E2E_Run2)</td><td class="s3">Remarks</td><td class="s3">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1365549434R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5" colspan="16">PRS-005 - Manage Company - Create, View, Update and Delete of Association</td></tr><tr style="height: 19px"><th id="1365549434R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s6"></td><td class="s7">[MANAGE COMPANY] Company Landing Page</td><td class="s6"></td><td class="s6">Validate Manage Company Landing Page</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Check the Company/Association Managementt page Display<br>4. Validate &quot;Search&quot; functionality<br>5. Validate &quot;Clear&quot; button<br>6. Validate sorting of Table Columns</td><td class="s9"></td><td class="s6"><span style="font-family:Poppins,Arial;color:#000000;">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display a Search Field for the Company Name<br>    a. Should be triggered by Search Button<br>    b. Should be cleared by clicking Clear Button<br>4. Should have a Create New Association Button<br>5. Should have a Table List of the synced Companies and created Associations<br>6. Should have the following Columns<br>    a. Co./Assoc. Name<br>        i. Pulled Company Name<br>        ii. Created Association Name<br></span><span style="font-family:Poppins,Arial;color:#ff0000;">  </span><span style="font-family:Poppins,Arial;color:#000000;">  b. Company Code<br>        i. Pulled Company Code<br>        ii. Created Association Code<br>    c. Company Initials<br>        i. Pulled Company Initials<br>        ii. Created Association Initials<br>    d. TIN<br>        i. Pulled Company TIN<br>        ii. Created Association TIN<br>    e. Address<br>        i. Pulled Company Address<br>        ii. Created Association Address<br>    f. Contact Number<br>        i. Pulled Company Contact Number<br>        ii. Created Association Contact Number<br>    g. Co./Assoc.<br>        i. If Synced should be tagged as Company<br>        ii. If created manually, should be tagged as Association<br>    h. Actions<br>        i. Should be applied only to Associations<br>        ii. Edit<br>7. Should sort the Table by Company/Association Name (A-Z)<br>8. Should display 10 Rows per Page<br>9. Should have a Sync Button with last Updated Date and Time</span></td><td class="s10" rowspan="16"></td><td class="s11">Passed</td><td class="s12">Failed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-982">https://youtrack.stratpoint.com/issue/CITYLANDPRS-982<br>CITYLANDPRS-1093<br>CITYLANDPRS-1094</a></td></tr><tr style="height: 19px"><th id="1365549434R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s6"></td><td class="s7">[MANAGE COMPANY] Pull Company Data</td><td class="s6"></td><td class="s6">Validate Manage Company Pull Company data when company is not on the list</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. Company Master File integration has been setup</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Sync&quot; button on Company/Association Managementt page </td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should initiate syncing of Companies from Company Master File and add the Company in the List.<br>4. Should sync the Company List for Filters and Forms<br>5. Should display the Date and Time of the last triger of the Sync Button<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s6"></td><td class="s7">[MANAGE COMPANY] Pull Company Data</td><td class="s6"></td><td class="s6">Validate Manage Company Pull Company data when company has already on the file</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. Company Master File integration has been setup<br>6. An update of existing company has been made</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Sync&quot; button on Company/Association Managementt page </td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should initiate syncing of Companies from Company Master File and update the Company Details if an update on the existing Company has been made in the Company Master File<br>4. Should sync the Company List for Filters and Forms<br>5. Should display the Date and Time of the last triger of the Sync Button<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10">1</td></tr><tr style="height: 19px"><th id="1365549434R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s6"></td><td class="s7">[MANAGE COMPANY] View Company</td><td class="s6"></td><td class="s6">Validate Manage Company View Company  when have a list of project</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click any &quot;Company&quot;  Text Link on the table list with tagged as &quot;Company&quot; in Co./ Assoc column <br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display Company Details - View page<br>4. Should display a non-editable Fields for<br>    a. Company Name<br>    b. Company Code<br>    c. Company Initials<br>    d. TIN<br>    e. Company Address<br>    f. Contact Number<br>5. Should be able to see a Table consisiting of<br>     a. Projects linked to the Company<br>     b. Departments linked to the Projects<br>6. Should be sorted by Project Name<br>     a. 0-9, A-Z<br>7. Should display 10 Rows per Page</td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s6"></td><td class="s7">[MANAGE COMPANY] View Company</td><td class="s6"></td><td class="s6">Validate Manage Company View Company when ther is no list of projects</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click any &quot;Company&quot;  Text Link on the table list with tagged as &quot;Company&quot; in Co./ Assoc column <br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display Company Details - View page<br>4. Should display a non-editable Fields for<br>    a. Company Name<br>    b. Company Code<br>    c. Company Initials<br>    d. TIN<br>    e. Company Address<br>    f. Contact Number<br>5. Should not be able to see a Table in Project List and Table should displayed as:<br>&quot; No Items Found. <br>No items can be displayed for this list&quot;<br></td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6">CITYLAND ELITE PROPERTIES INC</td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Manual Creation of Association</td><td class="s15"></td><td class="s15">Validate Manage Company Manual Creation of Association</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Create New Association&quot; button on Company/Association Management Page<br>4. Validate all fields and buttons on the form</td><td class="s18"></td><td class="s19">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display &quot;Create New Association&quot; Form with the following Fields:<br>    a. Association Name<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    b. Company Code<br>        i. Numeric only<br>        ii. Should have a maximum of 10 Characters<br>    c. Association Initials<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    d. TIN<br>        i. Numbers only<br>        ii. Should have a maximum of 20 Characters<br>    e. Address<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    f. Contact Number<br>        i. Numbers only<br>        ii. Should have a +63 placeholder that is included when saving<br>        iii. Should have a maximum of 13 Characters including the +63 Placeholder<br>    g. Area<br>        i. Drop-down values of Areas<br>4. Should have a &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>    a. If Cancel Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should not create the Association<br>    b. If Submit Button is clicked, should check Field Validations to proceed<br>        i. If with Error, should highlight the Field, display an Error Message, and do not allow the User to proceed<br>        ii. If without an Error, should display a Confirmation Modal<br>            i) Once Confirmed, should create the Association with a tagging of Association in the Landing Page</td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Manual Creation of Association</td><td class="s15"></td><td class="s15">Validate Manage Company Manual Creation of Association when clicked Cancel button in the Creation form</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Create New Association&quot; button on Company/Association Management Page<br>4. Populate all fields<br>5. Click &quot;Cancel&quot; button on the Create new Association form<br>6. Click &quot;Continue&quot; on the cancel Changes modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display &quot;Create New Association&quot; Form with the following Fields and buttons:<br>    a. Association Name<br>    b. Association Code<br>    c. Association Initials\<br>    d. TIN<br>    e. Address<br>    f. Contact Number<br>    g. Area<br>    h. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>4. Fields should be populated<br>5. Should displayed a &quot;Cancel Changes&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should not be able to Create a New Association</td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Manual Creation of Association</td><td class="s15"></td><td class="s15">Validate Manage Company Manual Creation of Association when clicked Confirm button in the Creation form</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Create New Association&quot; button on Company/Association Management Page<br>4. Populate all fields<br>5. Click &quot;Confirm&quot; button on the Create New Association form<br>6. Click &quot;Continue&quot; on the Create New Association modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display &quot;Create New Association&quot; Form with the following Fields and buttons:<br>    a. Association Name<br>    b. Association Code<br>    c. Association Initials\<br>    d. TIN<br>    e. Address<br>    f. Contact Number<br>    g. Area<br>    h. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>4. Fields should be populated<br>5. Should displayed a &quot;Create New Association&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to create a new association. Make sure all items are correct. Press continue if you want to proceed with this action..&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should be able to Create a New Association with a tagging of Association in the Company/Association Management Landing Page</td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Manual Creation of Association</td><td class="s15"></td><td class="s15">Validate Manage Company Manual Creation of Association when not populated all fields</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Create New Association&quot; button on Company/Association Management Page<br>4. Populate all fields except Association Name field<br>5. Click &quot;Confirm&quot; button on the Create New Association form<br>6. Click &quot;Continue&quot; on the Create New Association modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display &quot;Create New Association&quot; Form with the following Fields and buttons:<br>    a. Association Name<br>    b. Association Code<br>    c. Association Initials\<br>    d. TIN<br>    e. Address<br>    f. Contact Number<br>    g. Area<br>    h. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>4. Fields should be populated except the Association Name field<br>5. Should displayed a &quot;Create New Association&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to create a new association. Make sure all items are correct. Press continue if you want to proceed with this action..&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should not be able to Create New Association, should highlight the Field that has an error and, display an Error Message, </td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Updating Association Details</td><td class="s15"></td><td class="s15">Validate Manage Company Updating Association Details thru action column</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Edit&quot; icon button on Actions column list with tagged as &quot;Association&quot; in Co./ Assoc column and validate all fields<br>4. Update any of the fields <br>5. Click &quot;Confirm&quot; button on the  Association Details - Edit form<br>6. Click &quot;Continue&quot; on the Confirm Changes modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display the &quot;Association Details - Edit&quot; Form with the following Fields and buttons<br>    a. Association Name<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    b. Association Code<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    c. Association Initials<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    d. TIN<br>        i. Numbers only<br>        ii. Should have a maximum of 20 Characters<br>    e. Address<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    f. Contact Number<br>        i. Numbers only<br>        ii. Should have a +63 placeholder that is included when saving<br>        iii. Should have a maximum of 13 Characters including the +63 Placeholder<br>    g. Area<br>        i. Drop-down values of Areas<br>    h. A &quot;Cancel&quot; and &quot;Submit&quot; buttons<br>4. Fields should be updated <br>5. Should displayed a &quot; Confirm Changes&quot; Modal that contains the ff:<br>     a. A description You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should be able to Update Association, and displayed a success toast message </td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Updating Association Details</td><td class="s15"></td><td class="s15">Validate Manage Company Updating Association Details thru edit button in View Association page </td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3 Click &quot;Company/Association&quot; text Link on the column list table with tagged as &quot;Association&quot; in Co./ Assoc column<br>4. Click &quot;Edit&quot; button on Association Details - View page<br>5. Update any of the fields <br>6. Click &quot;Confirm&quot; button on the  Association Details - Edit form<br>7. Click &quot;Continue&quot; on the Confirm Changes modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display the &quot;Association Details - View&quot; page<br>4. Should display the &quot;Association Details - Edit&quot; Form with the following Fields and buttons<br>    a. Association Name<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    b. Association Code<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    c. Association Initials<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    d. TIN<br>        i. Numbers only<br>        ii. Should have a maximum of 20 Characters<br>    e. Address<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    f. Contact Number<br>        i. Numbers only<br>        ii. Should have a +63 placeholder that is included when saving<br>        iii. Should have a maximum of 13 Characters including the +63 Placeholder<br>    g. Area<br>        i. Drop-down values of Areas<br>    h. A &quot;Cancel&quot; and &quot;Submit&quot; buttons<br>5. Fields should be updated <br>6. Should displayed a &quot; Confirm Changes&quot; Modal that contains the ff:<br>     a. A description You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should be able to Update Association, and displayed a success toast message <br></td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Updating Association Details</td><td class="s15"></td><td class="s15">Validate Manage Company Updating Association Details when Cancel changes is clicked</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3 Click &quot;Company/Association&quot; text Link on the column list table with tagged as &quot;Association&quot; in Co./ Assoc column<br>4. Click &quot;Edit&quot; button on Association Details - View page<br>5. Update any of the fields <br>6. Click &quot;Cancel&quot; button on the  Association Details - Edit form<br>7. Click &quot;Continue&quot; on the Cancel Changes modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display the &quot;Association Details - View&quot; page<br>4. Should display the &quot;Association Details - Edit&quot; Form with the following Fields and buttons<br>    a. Association Name<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    b. Association Code<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    c. Association Initials<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    d. TIN<br>        i. Numbers only<br>        ii. Should have a maximum of 20 Characters<br>    e. Address<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    f. Contact Number<br>        i. Numbers only<br>        ii. Should have a +63 placeholder that is included when saving<br>        iii. Should have a maximum of 13 Characters including the +63 Placeholder<br>    g. Area<br>        i. Drop-down values of Areas<br>    h. A &quot;Cancel&quot; and &quot;Submit&quot; buttons<br>5. Fields should be updated <br>6. Should displayed a &quot; Cancel Changes&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should not be able to Update Association, <br>    b. If Submit Button is clicked, should check Field Validations to proceed<br>        i. If with Error, should highlight the Field, display an Error Message, and do not allow the User to proceed<br></td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1365549434R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s15"></td><td class="s16">[MANAGE COMPANY] Updating Association Details</td><td class="s15"></td><td class="s15">Validate Manage Company Updating Association Details when there are error fields </td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3 Click &quot;Company/Association&quot; text Link on the column list table with tagged as &quot;Association&quot; in Co./ Assoc column<br>4. Click &quot;Edit&quot; button on Association Details - View page<br>5. Update TIN field with Alphanumeric characters<br>6. Click &quot;Confirm&quot; button on the  Association Details - Edit form<br>7. Click &quot;Continue&quot; on the Confirm Changes modal</td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display the &quot;Association Details - View&quot; page<br>4. Should display the &quot;Association Details - Edit&quot; Form with the following Fields and buttons<br>    a. Association Name<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    b. Association Code<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    c. Association Initials<br>        i. Alphanumeric only<br>        ii. Should have a maximum of 20 Characters<br>    d. TIN<br>        i. Numbers only<br>        ii. Should have a maximum of 20 Characters<br>    e. Address<br>        i. Alphanumeric and Special Characters are accepted except Emojis<br>        ii. Should have a maximum of 100 Characters<br>    f. Contact Number<br>        i. Numbers only<br>        ii. Should have a +63 placeholder that is included when saving<br>        iii. Should have a maximum of 13 Characters including the +63 Placeholder<br>    g. Area<br>        i. Drop-down values of Areas<br>    h. A &quot;Cancel&quot; and &quot;Submit&quot; buttons<br>5. TIN field should be updated <br>6. Should displayed a &quot; Confirm Changes&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should not be able to Update Association, should highlight the Field, and display an Error Message, <br></td><td class="s11">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s20"></td><td class="s20"></td></tr><tr style="height: 19px"><th id="1365549434R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s18"></td><td class="s16">[MANAGE COMPANY] Viewing an Association</td><td class="s15"></td><td class="s15">Validate Manage Company Viewing an Association</td><td class="s17"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s15">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click any &quot;Company/Association&quot;  Text Link on the table list with tagged as &quot;Association&quot; in Co./ Assoc column </td><td class="s18"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display Association Details - View page<br>4. Should display Assocaition Details<br>    a. Association Name<br>    b. Association Code<br>    c. Association Initials<br>    d. TIN<br>    e. Address<br>    f. Contact Number<br>    g. Area<br>    h. Delete Assoc. Button<br>5. Should have an Edit Button that will allow editing of the Association Details<br>6. Should have an Go Back Button that will allow user to redirected back to Company/Association Management page<br>7.  Should have an Delete Button that will allow deleting of the Association</td><td class="s21">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s20"></td><td class="s20"></td></tr><tr style="height: 19px"><th id="1365549434R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s9"></td><td class="s7">[MANAGE COMPANY] Deleting an Association</td><td class="s6"></td><td class="s6">Validate Manage Company Deleting an Association when there are Requisition Slip linked to the Association</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin<br>6. Association has linked RS</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click any &quot;Company/Association&quot;  Text Link on the table list with tagged as &quot;Association&quot; in Co./ Assoc column <br>4. Click &quot;Delete&quot; button on Association Details - View page<br>5. Click &quot;Close Window&quot; on the modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display Association Details - View page<br>4. Should displayed a &quot; Delete Association&quot; Modal that contains the ff:<br>     a. A description &quot;It seems that there are still pending requests under this association. Please settle all pending items and requests before proceeding with the deletion.&quot;<br>     b. A &quot;Close Window&quot; button<br>5. Should close the modal and not able to delete association</td><td class="s21">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s20"></td><td class="s20"></td></tr><tr style="height: 19px"><th id="1365549434R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s9"></td><td class="s7">[MANAGE COMPANY] Deleting an Association</td><td class="s6"></td><td class="s6">Validate Manage Company Deleting an Association when there are no Requisition Slip linked to the Association</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Company page<br>5. User should be an IT admin</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click any &quot;Company/Association&quot;  Text Link on the table list with tagged as &quot;Association&quot; in Co./ Assoc column <br>4. Click &quot;Delete&quot; button on Association Details - View page<br>5. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should display Association Details - View page<br>4. Should displayed a &quot; Delete Association&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to delete this association. This action is irreversible and all information will be deleted. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>5. Should remove the Association to the Company List View</td><td class="s21">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s13">Not Started</td><td class="s6"></td><td class="s20"></td><td class="s20"></td></tr></tbody></table></div>