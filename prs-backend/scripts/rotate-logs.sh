#!/bin/bash
# Log Rotation Script for PRS Backend
# This script should be run daily via cron to rotate log files

# Configuration
LOG_DIR="/home/<USER>/prs-prod/prs-backend/logs"
MAX_SIZE_MB=10
MAX_APP_LOGS=14  # Keep 2 weeks of app logs
MAX_ERROR_LOGS=30  # Keep 1 month of error logs
DATE_SUFFIX=$(date +"%Y%m%d-%H%M%S")

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Function to rotate a log file if it exceeds the maximum size
rotate_log() {
  local log_file="$1"
  local max_logs="$2"
  
  # Check if file exists
  if [ ! -f "$log_file" ]; then
    echo "Log file $log_file does not exist. Skipping."
    return
  fi
  
  # Get file size in KB
  local size_kb=$(du -k "$log_file" | cut -f1)
  local size_mb=$((size_kb / 1024))
  
  # Rotate if file exceeds max size
  if [ $size_mb -ge $MAX_SIZE_MB ]; then
    echo "Rotating $log_file (size: ${size_mb}MB)"
    
    # Create rotated log file with date suffix
    local rotated_file="${log_file}.${DATE_SUFFIX}"
    cp "$log_file" "$rotated_file"
    
    # Compress rotated log
    gzip "$rotated_file"
    echo "Created compressed log: ${rotated_file}.gz"
    
    # Clear original log file
    echo "" > "$log_file"
    
    # Delete old logs if we have too many
    local log_count=$(ls -1 "${log_file}".*.gz 2>/dev/null | wc -l)
    if [ $log_count -gt $max_logs ]; then
      echo "Removing old logs (keeping $max_logs most recent)"
      ls -1t "${log_file}".*.gz | tail -n +$((max_logs+1)) | xargs rm -f
    fi
  else
    echo "Log file $log_file is ${size_mb}MB, below rotation threshold of ${MAX_SIZE_MB}MB"
  fi
}

# Main execution
echo "Starting log rotation at $(date)"

# Rotate application logs
rotate_log "$LOG_DIR/app.log" $MAX_APP_LOGS

# Rotate error logs
rotate_log "$LOG_DIR/error.log" $MAX_ERROR_LOGS

echo "Log rotation completed at $(date)"
