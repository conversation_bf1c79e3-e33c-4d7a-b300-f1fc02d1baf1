"use client";

import React, { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { cn } from "@/lib/utils";
import { Button } from "./Button";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  header?: string | React.ReactNode;
  footer?: React.ReactNode;
  size?: "small" | "medium" | "large" | "full";
  className?: string;
  closeOnClickOutside?: boolean;
  showCloseButton?: boolean;
}

export function Modal({
  isOpen,
  onClose,
  children,
  header,
  footer,
  size = "medium",
  className,
  closeOnClickOutside = true,
  showCloseButton = true,
}: ModalProps) {
  const sizeClasses = {
    small: "max-w-md w-[512px]",
    medium: "max-w-2xl",
    large: "max-w-4xl",
    full: "max-w-full",
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-50"
        onClose={closeOnClickOutside ? onClose : () => { }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={cn(
                  "w-full transform overflow-hidden rounded-md bg-white p-0 text-left align-middle shadow-xl transition-all",
                  sizeClasses[size],
                  className
                )}
              >
                {header && (
                  <Dialog.Title
                    as="div"
                    className="flex items-center justify-between bg-[#7a5c5f] text-white p-4 mb-4 rounded-t-md"
                  >
                    <h3 className="text-base font-medium text-white mx-auto">
                      {header}
                    </h3>
                    {showCloseButton && (
                      <button
                        type="button"
                        className="text-white hover:text-gray-200 absolute right-4"
                        onClick={onClose}
                      >
                        <span className="sr-only">Close</span>
                        <svg
                          className="h-6 w-6"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    )}
                  </Dialog.Title>
                )}

                <div className="px-6 pb-6">{children}</div>

                {footer && <div className="mt-4 pt-4">{footer}</div>}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

interface ActionModalProps extends Omit<ModalProps, "footer"> {
  primaryAction?: {
    label: string;
    onClick: () => void;
    variant?: "submit" | "secondary" | "outline" | "danger";
    hover?: string;
    disabled?: boolean;
    isLoading?: boolean;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: "submit" | "secondary" | "outline" | "danger";
    hover?: string;
    disabled?: boolean;
    isLoading?: boolean;
  };
  showActions?: boolean;
}

export function ActionModal({
  primaryAction,
  secondaryAction,
  showActions = false,
  ...props
}: ActionModalProps) {
  const footer = showActions && (primaryAction || secondaryAction) && (
    <div className="flex justify-end space-x-2">
      {secondaryAction && (
        <Button
          variant={secondaryAction.variant || "outline"}
          hover={secondaryAction.hover || "outline"}
          onClick={secondaryAction.onClick}
          disabled={secondaryAction.disabled}
          isLoading={secondaryAction.isLoading}
        >
          {secondaryAction.label}
        </Button>
      )}
      {primaryAction && (
        <Button
          variant={primaryAction.variant || "submit"}
          hover={primaryAction.hover || "submit"}
          onClick={primaryAction.onClick}
          disabled={primaryAction.disabled}
          isLoading={primaryAction.isLoading}
        >
          {primaryAction.label}
        </Button>
      )}
    </div>
  );

  return <Modal {...props} footer={footer} />;
}
