# PRS Workflow Overview in Next.js 15 with SSE

This document provides a comprehensive overview of the complete Purchase Requisition System (PRS) workflow as implemented in Next.js 15 with Server-Sent Events (SSE) for real-time updates.

## Table of Contents

1. [Complete Workflow Diagram](#complete-workflow-diagram)
2. [Workflow Stages](#workflow-stages)
3. [Approval Processes](#approval-processes)
4. [Real-Time Updates with SSE](#real-time-updates-with-sse)
5. [Implementation Files](#implementation-files)

## Complete Workflow Diagram

The complete PRS workflow can be visualized as follows:

```mermaid
flowchart LR
    RS["Create RS"] --> SFA["Submit for Approval"]
    SFA --> AP["Approval Process"]
    AP --> CAN["Canvassing"]
    CAN --> CANA["Canvassing Approval"]
    CANA --> PO["Purchase Order"]
    PO --> POA["Purchase Order Approval"]
    POA --> DEL["Delivery"] & INV["Invoice"]
    DEL <--> INV
    DEL --> PAY["Payment"]
    INV --> PAY
    PAY --> PAYA["Payment Approval"]
```

This workflow includes multiple approval steps that ensure proper oversight throughout the procurement process.

## Workflow Stages

### 1. Requisition Slip (RS) Creation

The workflow begins with the creation of a Requisition Slip (RS) by a user who needs to purchase items or services. The RS includes details such as:

- Category (company, association, project)
- Department and project information
- Items requested with quantities
- Purpose of the requisition
- Delivery information
- Charge-to information

**Implementation Files:**
- `app/requisitions/actions.ts` - Server actions for RS creation
- `app/requisitions/new/page.tsx` - RS creation page
- `components/requisitions/RequisitionForm.tsx` - RS form component

### 2. Approval Process

Once submitted, the RS goes through an approval process where designated approvers review and approve or reject the requisition. The approval routing is determined dynamically based on:

- Department hierarchy
- Project requirements
- Amount thresholds

**Implementation Files:**
- `app/requisitions/[id]/actions.ts` - Approval actions
- `app/requisitions/[id]/page.tsx` - Requisition detail page
- `components/approvals/ApprovalForm.tsx` - Approval form component

### 3. Canvassing

After approval, the requisition enters the canvassing stage where purchasing staff select potential suppliers and compare prices. This stage includes:

- Supplier selection
- Price comparison
- Quantity adjustment if needed
- Selection of the best supplier for each item

**Implementation Files:**
- `app/canvassing/actions.ts` - Canvassing actions
- `app/canvassing/new/page.tsx` - Canvassing creation page
- `components/canvassing/CanvassForm.tsx` - Canvassing form component

### 4. Canvassing Approval

The canvass sheet is then submitted for approval to ensure that the supplier selection process was fair and followed organizational policies.

**Implementation Files:**
- `app/canvassing/[id]/actions.ts` - Canvassing approval actions
- `app/canvassing/[id]/page.tsx` - Canvass detail page
- `components/canvassing/ApprovalForm.tsx` - Canvassing approval form

### 5. Purchase Order (PO) Creation

Once the canvass is approved, a Purchase Order is created based on the selected suppliers and prices. The PO includes:

- Supplier information
- Items with quantities and prices
- Delivery terms
- Payment terms

**Implementation Files:**
- `app/purchase-orders/actions.ts` - PO creation actions
- `app/purchase-orders/new/page.tsx` - PO creation page
- `components/purchase-orders/POForm.tsx` - PO form component

### 6. Purchase Order Approval

The Purchase Order goes through its own approval process to ensure that it accurately reflects the approved canvass and follows organizational policies.

**Implementation Files:**
- `app/purchase-orders/[id]/actions.ts` - PO approval actions
- `app/purchase-orders/[id]/page.tsx` - PO detail page
- `components/purchase-orders/ApprovalForm.tsx` - PO approval form

### 7. Delivery and Invoice

After the PO is approved, the supplier delivers the items and provides an invoice. The delivery receipt and invoice are recorded in the system.

**Implementation Files:**
- `app/delivery-receipts/actions.ts` - Delivery receipt actions
- `app/invoices/actions.ts` - Invoice actions
- `components/delivery-receipts/DeliveryReceiptForm.tsx` - Delivery form
- `components/invoices/InvoiceForm.tsx` - Invoice form

### 8. Payment Request

Based on the delivery receipt and invoice, a payment request is created to initiate the payment process.

**Implementation Files:**
- `app/payments/actions.ts` - Payment request actions
- `app/payments/new/page.tsx` - Payment request creation page
- `components/payments/PaymentRequestForm.tsx` - Payment request form

### 9. Payment Approval

The payment request goes through an approval process before the actual payment is made.

**Implementation Files:**
- `app/payments/[id]/actions.ts` - Payment approval actions
- `app/payments/[id]/page.tsx` - Payment detail page
- `components/payments/ApprovalForm.tsx` - Payment approval form

## Approval Processes

The PRS system includes multiple approval processes throughout the workflow:

### 1. Requisition Approval

- **Approvers**: Department heads, project managers, and higher management based on amount thresholds
- **Actions**: Approve, reject, or return for revision
- **Notifications**: Real-time notifications to approvers and requisition creator

### 2. Canvassing Approval

- **Approvers**: Purchasing head and department head
- **Actions**: Approve, reject, or return for revision
- **Notifications**: Real-time notifications to approvers and canvass creator

### 3. Purchase Order Approval

- **Approvers**: Purchasing head and finance officers based on amount thresholds
- **Actions**: Approve, reject, or return for revision
- **Notifications**: Real-time notifications to approvers and PO creator

### 4. Payment Approval

- **Approvers**: Department head, finance officers, and higher management based on amount thresholds
- **Actions**: Approve, reject, or return for revision
- **Notifications**: Real-time notifications to approvers and payment request creator

## Real-Time Updates with SSE

Server-Sent Events (SSE) are used throughout the application to provide real-time updates to users:

### 1. Notification System

- Real-time notifications for approval requests, approvals, rejections, and other important events
- Notification counter in the UI that updates in real-time
- Notification list that refreshes automatically

**Implementation Files:**
- `app/api/sse/notifications/route.ts` - SSE endpoint for notifications
- `components/notifications/NotificationListener.tsx` - Client-side SSE listener

### 2. Approval Status Updates

- Real-time updates of approval status for requisitions, canvasses, purchase orders, and payment requests
- Approval progress indicators that update automatically
- Current approval level indicators

**Implementation Files:**
- `app/api/sse/approvals/[id]/route.ts` - SSE endpoint for approval updates
- `components/approvals/ApprovalStatusListener.tsx` - Client-side SSE listener

### 3. Dashboard Updates

- Real-time updates of dashboard counters and statistics
- Pending approvals counter that updates automatically
- Recent activity feed that refreshes in real-time

**Implementation Files:**
- `app/api/sse/dashboard/route.ts` - SSE endpoint for dashboard updates
- `components/dashboard/DashboardListener.tsx` - Client-side SSE listener

## Implementation Files

The complete PRS workflow is implemented across multiple files in the Next.js 15 application:

### Server-Side Files

- **API Routes**: Handle SSE connections and data streaming
- **Server Actions**: Implement business logic for each workflow stage
- **Database Access**: Prisma ORM for database operations

### Client-Side Files

- **Page Components**: Render the UI for each workflow stage
- **Form Components**: Handle user input and form submission
- **SSE Listeners**: Connect to SSE endpoints and update UI in real-time

### Shared Files

- **Models**: Define data structures and validation schemas
- **Utilities**: Provide helper functions for common operations
- **Types**: Define TypeScript types for type safety

For detailed implementation of each workflow stage, refer to the specific documentation files:

1. [Requisition Workflow](./nextjs15-sse-core-workflows.md)
2. [Canvassing Workflow](./nextjs15-sse-canvassing.md)
3. [Delivery and Payment Workflows](./nextjs15-sse-delivery-payment.md)
4. [Payment Approval Workflow](./nextjs15-sse-payment-approval.md)
5. [Real-Time Updates with SSE](./nextjs15-sse-real-time-updates.md)
6. [Client-Side SSE Integration](./nextjs15-sse-client-integration.md)
