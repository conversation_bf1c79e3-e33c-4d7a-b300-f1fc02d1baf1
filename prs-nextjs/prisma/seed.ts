import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../src/lib/auth/password';

const prisma = new PrismaClient();

async function main() {
  // Create roles
  const adminRole = await prisma.role.upsert({
    where: { name: 'Admin' },
    update: {},
    create: {
      name: 'Admin',
      description: 'Administrator with full access',
    },
  });

  const managerRole = await prisma.role.upsert({
    where: { name: 'Manager' },
    update: {},
    create: {
      name: 'Manager',
      description: 'Department manager with approval rights',
    },
  });

  const userRole = await prisma.role.upsert({
    where: { name: 'User' },
    update: {},
    create: {
      name: 'User',
      description: 'Regular user with basic access',
    },
  });

  // Create permissions
  const createRequisitionPermission = await prisma.permission.upsert({
    where: { name: 'create:requisition' },
    update: {},
    create: {
      name: 'create:requisition',
      description: 'Create requisitions',
    },
  });

  const approveRequisitionPermission = await prisma.permission.upsert({
    where: { name: 'approve:requisition' },
    update: {},
    create: {
      name: 'approve:requisition',
      description: 'Approve requisitions',
    },
  });

  const manageUsersPermission = await prisma.permission.upsert({
    where: { name: 'manage:users' },
    update: {},
    create: {
      name: 'manage:users',
      description: 'Manage users',
    },
  });

  // Assign permissions to roles
  await prisma.rolePermission.upsert({
    where: {
      roleId_permissionId: {
        roleId: adminRole.id,
        permissionId: createRequisitionPermission.id,
      },
    },
    update: {},
    create: {
      roleId: adminRole.id,
      permissionId: createRequisitionPermission.id,
    },
  });

  await prisma.rolePermission.upsert({
    where: {
      roleId_permissionId: {
        roleId: adminRole.id,
        permissionId: approveRequisitionPermission.id,
      },
    },
    update: {},
    create: {
      roleId: adminRole.id,
      permissionId: approveRequisitionPermission.id,
    },
  });

  await prisma.rolePermission.upsert({
    where: {
      roleId_permissionId: {
        roleId: adminRole.id,
        permissionId: manageUsersPermission.id,
      },
    },
    update: {},
    create: {
      roleId: adminRole.id,
      permissionId: manageUsersPermission.id,
    },
  });

  await prisma.rolePermission.upsert({
    where: {
      roleId_permissionId: {
        roleId: managerRole.id,
        permissionId: createRequisitionPermission.id,
      },
    },
    update: {},
    create: {
      roleId: managerRole.id,
      permissionId: createRequisitionPermission.id,
    },
  });

  await prisma.rolePermission.upsert({
    where: {
      roleId_permissionId: {
        roleId: managerRole.id,
        permissionId: approveRequisitionPermission.id,
      },
    },
    update: {},
    create: {
      roleId: managerRole.id,
      permissionId: approveRequisitionPermission.id,
    },
  });

  await prisma.rolePermission.upsert({
    where: {
      roleId_permissionId: {
        roleId: userRole.id,
        permissionId: createRequisitionPermission.id,
      },
    },
    update: {},
    create: {
      roleId: userRole.id,
      permissionId: createRequisitionPermission.id,
    },
  });

  // Create company
  const company = await prisma.company.upsert({
    where: { code: 'CL' },
    update: {},
    create: {
      name: 'Cityland',
      code: 'CL',
      address: '123 Main St, Manila',
    },
  });

  // Create departments
  const financeDept = await prisma.department.upsert({
    where: { code: 'FIN' },
    update: {},
    create: {
      name: 'Finance',
      code: 'FIN',
      companyId: company.id,
    },
  });

  const itDept = await prisma.department.upsert({
    where: { code: 'IT' },
    update: {},
    create: {
      name: 'Information Technology',
      code: 'IT',
      companyId: company.id,
    },
  });

  // Create admin user
  const adminPassword = await hashPassword('Admin@123');
  const admin = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      status: 'ACTIVE',
      isPasswordTemporary: false,
      roleId: adminRole.id,
      departmentId: itDept.id,
    },
  });

  // Create manager user
  const managerPassword = await hashPassword('Manager@123');
  const manager = await prisma.user.upsert({
    where: { username: 'manager' },
    update: {},
    create: {
      username: 'manager',
      email: '<EMAIL>',
      password: managerPassword,
      firstName: 'Department',
      lastName: 'Manager',
      status: 'ACTIVE',
      isPasswordTemporary: false,
      roleId: managerRole.id,
      departmentId: financeDept.id,
    },
  });

  // Create regular user
  const userPassword = await hashPassword('User@123');
  const user = await prisma.user.upsert({
    where: { username: 'user' },
    update: {},
    create: {
      username: 'user',
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'Regular',
      lastName: 'User',
      status: 'ACTIVE',
      isPasswordTemporary: false,
      roleId: userRole.id,
      departmentId: financeDept.id,
      supervisorId: manager.id,
    },
  });

  console.log({ admin, manager, user });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
