import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/apiClient';
import { formatSortParams } from '@utils/query';

export const getInvoiceDocument = async ({
  id,
  page = 1,
  limit = 10,
  sortBy,
  search,
}) => {
  return await api.get(`/v1/requisitions/${id}/invoice-reports`, {
    params: {
      page,
      limit,
      ...formatSortParams(sortBy),
      search,
    },
  });
};

export const useGetInvoiceDocument = (
  { id, page, limit, sortBy, search },
  config = {},
) => {
  return useQuery({
    queryKey: ['rs_invoice_documents', { id, page, limit, sortBy, search }],
    queryFn: () => getInvoiceDocument({ id, page, limit, sortBy, search }),
    keepPreviousData: true,
    enabled: !!id,
    ...config,
  });
};
