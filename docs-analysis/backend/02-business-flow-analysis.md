# Business Flow Analysis

## Overview

The PRS-Backend implements a complex business process for managing purchase requisitions from creation to payment. This document provides a detailed analysis of each business flow, including state transitions, validation rules, and implementation details.

## Core Business Flows

### 1. Requisition Flow

The requisition process is the foundation of the entire system and follows these steps:

#### 1.1 Creation Phase

**Process:**
1. User creates a requisition with basic details (department, project, company)
2. User adds items to the requisition (OFM or non-OFM items)
3. User can save as draft or submit the requisition
4. Submitted requisitions enter the approval workflow

**Implementation:**
```javascript
// From src/app/handlers/controllers/requisitionController.js
async createRequisition(request, reply) {
  this.fastify.log.info(`Initiating Creation of Requisition Slip`);
  const { body, userFromToken, params, counter = false } = request;
  const {
    departmentId,
    chargeTo,
    chargeToId,
    comment,
    isDraft = true,
    projectId,
    companyId,
    category,
  } = body;
  const { REQUISITION_STATUS } = this.constants.requisition;
  const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

  const numberField = isDraft === true ? 'draftRsNumber' : 'rsNumber';

  if (!(await this.departmentRepository.getById(body.departmentId))) {
    throw this.clientErrors.NOT_FOUND({ message: 'Department not found' });
  }
  
  // ... more validation and processing
  
  try {
    requisition = await this.requisitionRepository.create(body, {
      transaction,
    });
    
    const rsApprovers = await this.requisitionService.rsApproversV2({
      category,
      projectId,
      departmentId,
      userFromToken,
      transaction,
      companyId,
    });

    await this.requisitionService.assignRSApprovers({
      rsApprovers,
      category,
      requisitionId: requisition.id,
      transaction,
    });

    await transaction.commit();
    
    // ... more processing
  } catch (error) {
    await transaction.rollback();
    throw this.clientErrors.BAD_REQUEST({
      message: 'Requisition creation failed',
    });
  }
}
```

**State Transitions:**
- New → Draft (when saved as draft)
- New → Submitted (when submitted directly)
- Draft → Submitted (when a draft is submitted)

**Issues:**
- The requisition number generation logic is complex and could be simplified
- Error handling is generic and doesn't provide specific error details
- The transaction management is incomplete in some error scenarios

#### 1.2 Approval Phase

**Process:**
1. Approvers are determined based on department, project, and requisition category
2. Approvers review the requisition in sequence based on their level
3. Approvers can approve, reject, or add comments
4. All approvers must approve for the requisition to proceed to the next phase

**Implementation:**
```javascript
// From src/app/services/requisitionApproverService.js
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  if (!requisitionId) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Requisition ID is required in setting RS status to assigning',
    });
  }

  // skip RS assigning flow
  // since there is a new approver
  if (approverId) {
    return false;
  }

  const [rsForApprovalCount, approvedRsCount] = await Promise.all([
    this.requisitionApproverRepository.count({
      where: { requisitionId },
      transaction,
    }),
    this.requisitionApproverRepository.count({
      where: { requisitionId, status: 'approved' },
      transaction,
    }),
  ]);

  if (rsForApprovalCount === approvedRsCount) {
    this.fastify.log.info(
      'All requisition approvers have approved the requisition, updating requisition status to assigning',
    );

    // TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error

    await this.requisitionRepository.update(
      { id: requisitionId },
      { status: 'assigning' },
      { transaction },
    );

    return true;
  }

  return false;
}
```

**State Transitions:**
- Submitted → Approved (when all approvers approve)
- Submitted → Rejected (when any approver rejects)

**Issues:**
- Missing validation of requisition status before updating (noted in TODO comment)
- No clear mechanism to handle approver unavailability (e.g., on leave)
- The approval level determination is not clearly documented

#### 1.3 Assignment Phase

**Process:**
1. Approved requisitions are assigned to purchasing staff
2. Assignment triggers notifications to the assignee
3. Assignee is responsible for the canvassing process

**Implementation:**
```javascript
// From src/app/services/requisitionService.js
async assignRequisition(requisitionId, assignedTo, type, userFromToken) {
  this.fastify.log.info(`Assigning Requisition`);

  const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
    this.constants.notification;

  await this.requisitionRepository.assignRequisition(
    requisitionId,
    assignedTo,
    type,
    userFromToken,
  );

  await this.notificationService.sendNotification({
    title: NOTIFICATION_DETAILS.ASSIGNING_RS.title,
    message: NOTIFICATION_DETAILS.ASSIGNING_RS.message,
    type: NOTIFICATION_TYPES.REQUISITION_SLIP,
    recipientUserIds: [assignedTo],
    metaData: {
      requisitionId: requisitionId,
    },
    senderId: userFromToken,
  });

  return this.fastify.log.info(`Successfully assigned Requisition`);
}
```

**State Transitions:**
- Approved → Assigning → Assigned

**Issues:**
- No validation that the assignee has the appropriate role/permissions
- No handling for reassignment scenarios
- No validation of the current requisition status before assignment

#### 1.4 Canvassing Phase

**Process:**
1. Assigned staff create canvass sheets for requisition items
2. Multiple suppliers can be added with pricing information
3. Canvass sheets can be saved as drafts or submitted
4. Submitted canvass sheets go through their own approval workflow

**Implementation:**
```javascript
// From src/app/services/canvassService.js
async createCanvass(payload = {}) {
  const {
    isDraft,
    assignedTo,
    transaction,
    requisitionId,
    addItems = [],
    updateItems = [],
    deleteItems = [],
    id: canvassId,
    userFromToken,
    canvassType,
  } = payload;

  const { CANVASS_STATUS, CANVASS_ITEM_STATUS } = this.constants.canvass;
  const numberField = isDraft ? 'draftCsNumber' : 'csNumber';
  let canvassData =
    await this.canvassRequisitionRepository.getExistingCanvass({
      canvassId,
      requisitionId,
    });

  const { csLetter, csNumber } = await this.#generateCSNumberCode(
    isDraft,
    canvassData?.id,
  );

  let isPartial = !isDraft;
  if (!isDraft) {
    isPartial = await this.#checkPartialCanvass({
      addItems,
      updateItems,
      deleteItems,
      requisitionId,
      canvassId: canvassData?.id,
    });
  }

  if (isPartial) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Every canvassed item must have at least one supplier.',
    });
  }

  const nonDraftStatus = isPartial
    ? CANVASS_STATUS.PARTIAL
    : CANVASS_STATUS.FOR_APPROVAL;

  const csStatus = isDraft ? CANVASS_STATUS.DRAFT : nonDraftStatus;
  const isCanvassNew = !canvassData;

  // ... more processing
}
```

**State Transitions:**
- Assigned → Partially_Canvassed (when some items are canvassed)
- Assigned → Canvass_For_Approval (when all items are canvassed and submitted)

**Issues:**
- Complex conditional logic for determining canvass status
- The supplier selection validation is complex and could be simplified
- The partial canvassing logic is complex and error-prone

#### 1.5 Purchase Order Phase

**Process:**
1. Approved canvass sheets automatically generate purchase orders
2. Purchase orders are grouped by supplier
3. POs have their own approval workflow
4. Approved POs move to delivery status

**Implementation:**
```javascript
// From src/app/services/purchaseOrderService.js
async createPurchaseOrder(payload = {}) {
  const { existingCanvass, transaction } = payload;
  const { PO_STATUS } = this.constants.purchaseOrder;
  const canvassItems = await this.canvassItemRepository.findAll({
    where: { canvassRequisitionId: existingCanvass.id },
    attributes: ['id'],
    paginate: false,
  });

  const selectedSuppliers =
    await this.canvassItemSupplierRepository.getSelectedSupplierByCanvassId(
      canvassItems?.data?.map((item) => item.id) || [],
    );

  if (!selectedSuppliers.total) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'No selected suppliers found for this canvass requisition',
    });
  }

  const supplierGroups = this.#groupBySupplier(selectedSuppliers.data);
  for (const supplierGroup of supplierGroups) {
    try {
      await this.#createPOForSupplier({
        transaction,
        supplierGroup,
        requisition: existingCanvass.requisition,
        canvassRequisitionId: existingCanvass.id,
      });
    } catch (error) {
      this.fastify.log.error('[ERROR] Creating PO for supplier');
      this.fastify.log.error(error);
      throw error;
    }
  }

  await this.requisitionRepository.update(
    { id: existingCanvass.requisition.id },
    { status: PO_STATUS.FOR_PO_REVIEW },
    { transaction },
  );
}
```

**State Transitions:**
- Canvass_For_Approval → For_PO_Review (when POs are created)
- For_PO_Review → For_PO_Approval (when POs are submitted for approval)
- For_PO_Approval → For_Delivery (when POs are approved)

**Issues:**
- The PO number generation logic is complex
- The PO approval workflow is not clearly separated from the PO creation
- There's incomplete validation when submitting POs for approval:
  ```javascript
  // TODO: do now allow if status is not for (status before for_po_approval)
  ```

#### 1.6 Delivery Phase

**Process:**
1. Delivery receipts are created for approved POs
2. Partial deliveries are supported
3. Returns can be processed
4. Attachments (like invoices) can be added to delivery receipts

**Implementation:**
```javascript
// From src/app/services/deliveryReceiptService.js
async createDeliveryReceipt(data, userFromToken) {
  const checkIfPoNumberAlreadyTaken =
    await this.deliveryReceiptRepository.findOne({
      where: {
        poId: data.poId,
      },
    });

  if (checkIfPoNumberAlreadyTaken) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Delivery receipt with PO number already exists.',
    });
  }

  const isUserCreatorOrAssignee = await this.requisitionService.isUserCreatorOrAssignee(data.requisitionId, userFromToken.id);

  if (!isUserCreatorOrAssignee) {
    throw this.clientErrors.UNAUTHORIZED({
      message: 'You are not authorized to create a delivery receipt.',
    });
  }

  const transaction = await this.db.sequelize.transaction();
  try {
    // create delivery receipt
    const deliveryReceipt = await this.deliveryReceiptRepository.create(
      {
        ...data,
        attachmentIds: undefined,
      },
      {
        transaction,
        userId: userFromToken.id,
      },
    );

    // ... more processing
    
    await transaction.commit();
    
    // ... more processing
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

**State Transitions:**
- For_Delivery → (no status change, but delivery receipts are created)

**Issues:**
- The delivery item status management is complex
- The return process is not clearly separated from the delivery process
- The validation of delivered quantities against ordered quantities is complex

#### 1.7 Payment Phase

**Process:**
1. Payment requests are created for delivered items
2. Payment terms are applied based on PO terms
3. Payment requests have their own approval workflow
4. Approved payments close the requisition cycle

**Implementation:**
```javascript
// From src/app/services/rsPaymentRequestService.js
async createRsPaymentRequest(request) {
  const { transaction, userFromToken, ...payload } = request;
  const numberField = payload.isDraft === true ? 'prDraftNumber' : 'prNumber';
  const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

  const { prLetter, prNumber } = await this.generatePRNumberCode(
    payload.isDraft,
  );

  // comment for testing
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });

  const result = await this.rsPaymentRequestRepository.create(
    {
      ...payload,
      prLetter,
      //comment for testing
      termsData,
      [numberField]: prNumber,
      status:
        payload.isDraft === true
          ? RS_PAYMENT_REQUEST_STATUS.DRAFT
          : RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
    },
    transaction,
  );

  if (payload.isDraft === false) {
    await this.requisitionRepository.update(
      { id: payload.requisitionId },
      { status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL },
      { transaction },
    );
  }

  return result;
}
```

**State Transitions:**
- For_Delivery → For_PR_Approval (when payment request is submitted)
- For_PR_Approval → Closed (when payment request is approved)

**Issues:**
- The payment request approval workflow is not clearly separated from the creation
- The integration with the accounting system is not well-defined
- The requisition closure logic is not consistently applied

### 2. Approval Workflows

The system implements multiple approval workflows for different entities:

#### 2.1 Requisition Approval

**Process:**
1. Approvers are determined based on department and project settings
2. Approvers are assigned levels to determine the approval sequence
3. Optional approvers can be added to the workflow
4. Rejection by any approver stops the workflow

**Implementation:**
```javascript
// From src/app/services/requisitionService.js
async rsApproversV2({
  category,
  projectId,
  departmentId,
  userFromToken,
  transaction,
  companyId,
}) {
  const { APPROVAL_TYPES } = this.constants.approval;
  const { REQUISITION_CATEGORIES } = this.constants.requisition;
  const approvalTypeCode = APPROVAL_TYPES.REQUISITION_SLIP.code;

  let approvers = [];
  let optionalApprovers = [];

  if (category === REQUISITION_CATEGORIES[0]) {
    // Association
    const departmentAssociationApprovals =
      await this.departmentAssociationApprovalRepository.findAll({
        where: {
          departmentId,
          approvalTypeCode,
          companyId,
        },
        paginate: false,
        transaction,
      });

    if (departmentAssociationApprovals.total) {
      approvers = departmentAssociationApprovals.data
        .filter((approver) => !approver.isOptional)
        .map((approver) => ({
          approverId: approver.approverId,
          level: approver.level,
        }));

      optionalApprovers = departmentAssociationApprovals.data
        .filter((approver) => approver.isOptional)
        .map((approver) => ({
          approverId: approver.approverId,
          level: approver.level,
        }));
    }
  } else if (category === REQUISITION_CATEGORIES[1]) {
    // Company
    const departmentApprovals =
      await this.departmentApprovalRepository.findAll({
        where: {
          departmentId,
          approvalTypeCode,
        },
        paginate: false,
        transaction,
      });

    if (departmentApprovals.total) {
      approvers = departmentApprovals.data
        .filter((approver) => !approver.isOptional)
        .map((approver) => ({
          approverId: approver.approverId,
          level: approver.level,
        }));

      optionalApprovers = departmentApprovals.data
        .filter((approver) => approver.isOptional)
        .map((approver) => ({
          approverId: approver.approverId,
          level: approver.level,
        }));
    }
  } else if (category === REQUISITION_CATEGORIES[2]) {
    // Project
    const projectApprovals = await this.projectApprovalRepository.findAll({
      where: {
        projectId,
        approvalTypeCode,
      },
      paginate: false,
      transaction,
    });

    if (projectApprovals.total) {
      approvers = projectApprovals.data
        .filter((approver) => !approver.isOptional)
        .map((approver) => ({
          approverId: approver.approverId,
          level: approver.level,
        }));

      optionalApprovers = projectApprovals.data
        .filter((approver) => approver.isOptional)
        .map((approver) => ({
          approverId: approver.approverId,
          level: approver.level,
        }));
    }
  }

  return {
    approvers,
    optionalApprovers,
  };
}
```

**Issues:**
- Complex conditional logic based on requisition category
- Duplicate code for processing approvers and optional approvers
- No clear error handling for missing approvers

#### 2.2 Canvass Approval

**Process:**
1. Purchasing supervisors and heads approve canvass sheets
2. Supplier selection is validated
3. Approval triggers purchase order creation

**Implementation:**
```javascript
// From src/app/services/canvassService.js
async approveCanvass(payload = {}) {
  const { existingCanvass, approver, transaction, canvassSuppliers = [] } =
    payload;

  const { CANVASS_STATUS, CANVASS_APPROVER_STATUS } = this.constants.canvass;

  const isReadyForApproval =
    existingCanvass.status !== CANVASS_STATUS.FOR_APPROVAL;

  if (isReadyForApproval) {
    throw this.clientErrors.BAD_REQUEST({
      message: `Canvass sheet is not for approval`,
    });
  }

  const approvers = await this.canvassApproverRepository.findAll({
    where: { canvassRequisitionId: existingCanvass.id },
    paginate: false,
    order: [
      ['level', 'ASC'],
      ['isAdhoc', 'ASC'],
    ],
  });

  if (!approvers.total) {
    throw this.clientErrors.BAD_REQUEST({
      message: `No approvers found for this canvass`,
    });
  }

  // ... more processing
}
```

**Issues:**
- No clear mechanism for handling approver delegation
- The supplier selection validation is complex
- No clear error handling for missing approvers

#### 2.3 Purchase Order Approval

**Process:**
1. PO approvers validate terms, pricing, and delivery details
2. Multi-level approval similar to requisition approval
3. Approval changes PO status to For_Delivery

**Implementation:**
```javascript
// From src/app/services/purchaseOrderService.js
async approvePurchaseOrder(payload = {}) {
  const { existingPurchaseOrder, approver, transaction } = payload;

  const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

  const forApprovalStatuses = [
    PO_STATUS.FOR_PO_APPROVAL,
    PO_STATUS.REJECT_PO,
  ];

  const isReadyForApproval = !forApprovalStatuses.includes(
    existingPurchaseOrder.status,
  );

  if (isReadyForApproval) {
    throw this.clientErrors.BAD_REQUEST({
      message: `Purchase order sheet is not for approval`,
    });
  }

  const approvers = await this.purchaseOrderApproverRepository.findAll({
    where: { purchaseOrderId: existingPurchaseOrder.id },
    paginate: false,
    order: [
      ['level', 'ASC'],
      ['isAdhoc', 'ASC'],
    ],
  });

  // ... more processing
}
```

**Issues:**
- Confusing logic for checking if PO is ready for approval
- No clear mechanism for handling approver delegation
- No validation of the PO details before approval

#### 2.4 Payment Request Approval

**Process:**
1. Finance department approvers validate payment details
2. Approval triggers integration with accounting system
3. Approved payments close the requisition cycle

**Implementation:**
```javascript
// From src/app/services/rsPaymentRequestService.js
async approvePurchaseRequest(payload = {}) {
  const { existingPaymentRequest, userFromToken, transaction } = payload;
  const { PR_APPROVER_STATUS, RS_PAYMENT_REQUEST_STATUS } =
    this.constants.rsPaymentRequest;

  if (existingPaymentRequest.status === RS_PAYMENT_REQUEST_STATUS.DRAFT) {
    throw this.clientErrors.BAD_REQUEST({
      message: `Payment Request is in draft status`,
    });
  }

  const approvers = await this.rsPaymentRequestApproverRepository.findAll({
    where: { paymentRequestId: existingPaymentRequest.id },
    paginate: false,
    order: [
      ['level', 'ASC'],
      ['isAdhoc', 'ASC'],
    ],
  });

  // ... more processing
}
```

**Issues:**
- The integration with the accounting system is not well-defined
- No clear mechanism for handling approver delegation
- The requisition closure logic is not consistently applied

## State Transition Analysis

The following diagram illustrates the complete state transition flow for requisitions:

```
DRAFT ──────────────┐
                    ▼
                 SUBMITTED ───────┐
                    │             │
                    ▼             ▼
                 APPROVED      REJECTED
                    │
                    ▼
                 ASSIGNING
                    │
                    ▼
                 ASSIGNED
                    │
          ┌─────────┴─────────┐
          ▼                   ▼
PARTIALLY_CANVASSED    CANVASS_FOR_APPROVAL
          │                   │
          └─────────┬─────────┘
                    ▼
               FOR_PO_REVIEW
                    │
                    ▼
              FOR_PO_APPROVAL
                    │
                    ▼
               FOR_DELIVERY
                    │
                    ▼
             FOR_PR_APPROVAL
                    │
                    ▼
                 CLOSED
```

## Critical Issues in Business Flows

### 1. Inconsistent Status Management

Status transitions are not consistently enforced across the system. Many status updates lack proper validation of the current status before updating.

**Example:**
```javascript
// From src/app/services/requisitionApproverService.js
// TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error
await this.requisitionRepository.update(
  { id: requisitionId },
  { status: 'assigning' },
  { transaction },
);
```

### 2. Authorization Bypass

The authorization system is completely disabled in the middleware:

```javascript
// From src/app/handlers/middlewares/authorize.js
return async function (request, _reply) {
  // disable permission checking, kasi may bug
  return true;
  // ... actual authorization code never runs
};
```

This is a critical security issue that allows any authenticated user to access any endpoint regardless of their permissions.

### 3. Incomplete Transaction Management

Many operations that should be atomic lack proper transaction handling, which can lead to data inconsistency if operations fail partially.

**Example:**
```javascript
// Missing transaction rollback in some error scenarios
try {
  // Multiple database operations
} catch (error) {
  // No transaction rollback
  throw error;
}
```

### 4. Complex Business Logic

Business logic is often complex and difficult to understand, with many nested conditions and special cases.

**Example:**
```javascript
// Complex conditional logic for determining canvass status
const nonDraftStatus = isPartial
  ? CANVASS_STATUS.PARTIAL
  : CANVASS_STATUS.FOR_APPROVAL;

const csStatus = isDraft ? CANVASS_STATUS.DRAFT : nonDraftStatus;
```

### 5. Hardcoded Business Rules

Many business rules are hardcoded throughout the codebase rather than being configurable.

**Example:**
```javascript
// Hardcoded approval workflow
if (category === REQUISITION_CATEGORIES[0]) {
  // Association approval logic
} else if (category === REQUISITION_CATEGORIES[1]) {
  // Company approval logic
} else if (category === REQUISITION_CATEGORIES[2]) {
  // Project approval logic
}
```

## Recommendations

Detailed recommendations for addressing these issues are provided in the [Recommendations and Improvements](./09-recommendations.md) document.
