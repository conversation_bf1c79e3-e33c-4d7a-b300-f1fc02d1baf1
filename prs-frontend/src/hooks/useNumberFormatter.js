import { useCallback } from "react";

/**
 * useNumberFormatter
 *
 * 
 * Returns a stable formatter function that will:
 *   - in “view” mode, coerce a truthy value into a fixed‑decimal string
 *   - if switching between modes, the value will be cast to a string that doesn't enforce decimals, useful for forcing integers to have a decimal suffix
 *   - otherwise, return the raw value (or a fallback)
 *
 * @returns {function(isViewMode: boolean, value: string|number, decimal?: number, fallBack?: string): string|number}
 *   `formatNumber`:
 *   - @param {boolean} isViewMode
 *       If `true`, and `value` is non‑empty, returns `Number(value).toFixed(decimal)`.
 *       Otherwise, returns `value` (if defined) or `fallBack`.
 *   - @param {string|number} value
 *       The value to format; if “view” mode and truthy, will be cast to Number and fixed.
 *   - @param {number} [decimals=2]
 *       Number of decimals when in “view” mode.
 *   - @param {string} [fallBack='']
 *       Value to return when `value == null` or `undefined` and not in “view” mode.
 *
 * @example
 * ```jsx
 * function PriceDisplay({ price, editMode }) {
 *   const formatNumber = useNumberFormatter();
 *   // if editMode===true, show raw “price” so user can type;
 *   // otherwise, show exactly two decimals
 *   const displayValue = formatNumber(!editMode, price, 2, "0.00");
 *
 *   return <span>{displayValue}</span>;
 * }
 * ```
 */
export const useNumberFormatter = () => {
    return useCallback(
        (isViewMode, value, decimals = 2, fallback = '') =>
            isViewMode && (value !== null && value !== undefined && value !== '')
                ? Number(value).toFixed(decimals)
                : (value ?? fallback),
        []
    );
};
