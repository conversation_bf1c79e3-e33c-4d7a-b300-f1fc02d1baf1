# Delivery and Payment Workflows in Next.js 15 with SSE

This document outlines the implementation of the Delivery and Payment workflows in the Next.js 15 PRS application with Server-Sent Events (SSE) for real-time updates.

## Table of Contents

1. [Delivery Receipt Workflow](#delivery-receipt-workflow)
2. [Invoice Management](#invoice-management)
3. [Payment Request Workflow](#payment-request-workflow)
4. [Payment Approval Process](#payment-approval-process)
5. [Real-Time Updates with SSE](#real-time-updates-with-sse)

## Delivery Receipt Workflow

The Delivery Receipt workflow follows the Purchase Order Approval step in the procurement process:

```
Purchase Order Approval → Delivery & Invoice → Payment → Payment Approval
```

### Implementation with Next.js 15

#### 1. Delivery Receipt Creation

```typescript
// app/delivery-receipts/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { DeliveryReceiptSchema } from '@/models/delivery-receipt'

export async function createDeliveryReceipt(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')
  
  const userId = parseInt(session.user.id)
  
  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = DeliveryReceiptSchema.parse(rawData)
  
  // Get the purchase order
  const purchaseOrder = await prisma.purchaseOrder.findUnique({
    where: { id: validatedData.purchaseOrderId },
    include: { 
      requisition: true,
      items: true
    }
  })
  
  if (!purchaseOrder) throw new Error('Purchase Order not found')
  
  // Create delivery receipt
  const deliveryReceipt = await prisma.deliveryReceipt.create({
    data: {
      purchaseOrderId: purchaseOrder.id,
      requisitionId: purchaseOrder.requisition.id,
      companyCode: validatedData.companyCode,
      drNumber: validatedData.isDraft ? null : `DR-${Date.now()}`,
      draftDrNumber: validatedData.isDraft ? `DRAFT-DR-${Date.now()}` : null,
      supplier: purchaseOrder.supplier,
      isDraft: validatedData.isDraft,
      deliveryDate: validatedData.deliveryDate,
      receivedBy: validatedData.receivedBy,
      note: validatedData.note,
      createdById: userId
    }
  })
  
  // Create delivery receipt items
  if (validatedData.items && validatedData.items.length > 0) {
    await prisma.deliveryReceiptItem.createMany({
      data: validatedData.items.map(item => ({
        deliveryReceiptId: deliveryReceipt.id,
        purchaseOrderItemId: item.purchaseOrderItemId,
        quantity: item.quantity,
        remarks: item.remarks
      }))
    })
  }
  
  // If not draft, update purchase order status
  if (!validatedData.isDraft) {
    await prisma.purchaseOrder.update({
      where: { id: purchaseOrder.id },
      data: { status: 'delivered' }
    })
    
    // Notify the purchase order creator
    await notifyUsers([purchaseOrder.createdById], {
      type: 'DELIVERY_CREATED',
      title: 'Delivery Receipt Created',
      message: `Delivery Receipt #${deliveryReceipt.drNumber} has been created for PO #${purchaseOrder.poNumber}`,
      referenceId: deliveryReceipt.id,
      referenceType: 'delivery_receipt'
    })
  }
  
  // Revalidate paths
  revalidatePath('/delivery-receipts')
  revalidatePath(`/delivery-receipts/${deliveryReceipt.id}`)
  revalidatePath(`/purchase-orders/${purchaseOrder.id}`)
  
  return { success: true, id: deliveryReceipt.id }
}
```

#### 2. Delivery Receipt Form Component

```tsx
// components/delivery-receipts/DeliveryReceiptForm.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createDeliveryReceipt } from '@/app/delivery-receipts/actions'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { DatePicker } from '@/components/ui/date-picker'
import { POItemSelector } from '@/components/purchase-orders/POItemSelector'

export function DeliveryReceiptForm({ purchaseOrder }) {
  const router = useRouter()
  const [items, setItems] = useState([])
  const [isDraft, setIsDraft] = useState(true)
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    const form = e.target
    const formData = new FormData(form)
    
    // Add items to form data
    formData.append('items', JSON.stringify(items))
    
    // Add draft status
    formData.append('isDraft', isDraft.toString())
    
    // Add purchase order ID
    formData.append('purchaseOrderId', purchaseOrder.id.toString())
    
    // Submit form
    const result = await createDeliveryReceipt(formData)
    
    if (result.success) {
      router.push(`/delivery-receipts/${result.id}`)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Form fields for delivery receipt details */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="companyCode">Company Code</label>
          <Input 
            id="companyCode" 
            name="companyCode" 
            defaultValue={purchaseOrder.requisition.companyCode}
            readOnly
          />
        </div>
        
        <div>
          <label htmlFor="supplier">Supplier</label>
          <Input 
            id="supplier" 
            name="supplier" 
            defaultValue={purchaseOrder.supplier}
            readOnly
          />
        </div>
        
        <div>
          <label htmlFor="deliveryDate">Delivery Date</label>
          <DatePicker 
            id="deliveryDate" 
            name="deliveryDate" 
            required
          />
        </div>
        
        <div>
          <label htmlFor="receivedBy">Received By</label>
          <Input 
            id="receivedBy" 
            name="receivedBy" 
            required
          />
        </div>
        
        <div className="col-span-2">
          <label htmlFor="note">Note</label>
          <Textarea 
            id="note" 
            name="note" 
            rows={3}
          />
        </div>
      </div>
      
      {/* Item selection */}
      <div>
        <h3 className="text-lg font-medium mb-2">Items</h3>
        <POItemSelector 
          purchaseOrderItems={purchaseOrder.items} 
          items={items} 
          onChange={setItems} 
        />
      </div>
      
      {/* Submit buttons */}
      <div className="flex justify-end space-x-4">
        <Button 
          type="submit" 
          variant="outline" 
          onClick={() => setIsDraft(true)}
        >
          Save as Draft
        </Button>
        <Button 
          type="submit" 
          onClick={() => setIsDraft(false)}
        >
          Submit Delivery Receipt
        </Button>
      </div>
    </form>
  )
}
```

## Invoice Management

Invoices are typically received alongside deliveries and are managed in parallel:

### Implementation with Next.js 15

#### 1. Invoice Creation

```typescript
// app/invoices/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { InvoiceSchema } from '@/models/invoice'

export async function createInvoice(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')
  
  const userId = parseInt(session.user.id)
  
  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = InvoiceSchema.parse(rawData)
  
  // Get the purchase order
  const purchaseOrder = await prisma.purchaseOrder.findUnique({
    where: { id: validatedData.purchaseOrderId },
    include: { requisition: true }
  })
  
  if (!purchaseOrder) throw new Error('Purchase Order not found')
  
  // Create invoice
  const invoice = await prisma.invoice.create({
    data: {
      purchaseOrderId: purchaseOrder.id,
      requisitionId: purchaseOrder.requisition.id,
      invoiceNumber: validatedData.invoiceNumber,
      invoiceDate: validatedData.invoiceDate,
      amount: validatedData.amount,
      supplierId: purchaseOrder.supplierId,
      createdById: userId,
      status: 'active'
    }
  })
  
  // Notify the purchase order creator
  await notifyUsers([purchaseOrder.createdById], {
    type: 'INVOICE_CREATED',
    title: 'Invoice Created',
    message: `Invoice #${invoice.invoiceNumber} has been created for PO #${purchaseOrder.poNumber}`,
    referenceId: invoice.id,
    referenceType: 'invoice'
  })
  
  // Revalidate paths
  revalidatePath('/invoices')
  revalidatePath(`/invoices/${invoice.id}`)
  revalidatePath(`/purchase-orders/${purchaseOrder.id}`)
  
  return { success: true, id: invoice.id }
}
```
