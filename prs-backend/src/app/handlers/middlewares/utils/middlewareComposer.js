/**
 * Middleware composer utility
 * 
 * This module provides functions for composing middleware
 */

/**
 * Composes multiple middleware functions into a single middleware
 * 
 * @param {...Function} middlewares - Middleware functions to compose
 * @returns {Function} - Composed middleware function
 */
function compose(...middlewares) {
  return async function composedMiddleware(request, reply) {
    // Store the original fastify instance context
    const fastifyInstance = this;
    
    // Execute each middleware in sequence
    for (const middleware of middlewares) {
      await middleware.call(fastifyInstance, request, reply);
    }
  };
}

/**
 * Creates a conditional middleware that only executes if the condition is met
 * 
 * @param {Function} condition - Function that returns boolean
 * @param {Function} middleware - Middleware to execute if condition is true
 * @returns {Function} - Conditional middleware function
 */
function conditional(condition, middleware) {
  return async function conditionalMiddleware(request, reply) {
    if (await condition.call(this, request, reply)) {
      await middleware.call(this, request, reply);
    }
  };
}

/**
 * Creates a middleware that catches errors and handles them
 * 
 * @param {Function} middleware - Middleware function
 * @param {Function} errorHandler - Error handler function
 * @returns {Function} - Error-handling middleware function
 */
function with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(middleware, errorHandler) {
  return async function errorHandlingMiddleware(request, reply) {
    try {
      await middleware.call(this, request, reply);
    } catch (error) {
      await errorHandler.call(this, error, request, reply);
    }
  };
}

/**
 * Creates a middleware that logs before and after execution
 * 
 * @param {Function} middleware - Middleware function
 * @param {Object} options - Logging options
 * @param {string} options.name - Middleware name for logging
 * @returns {Function} - Logging middleware function
 */
function withLogging(middleware, options = {}) {
  const { name = middleware.name || 'anonymous' } = options;
  
  return async function loggingMiddleware(request, reply) {
    const logger = this.diScope.resolve('logger') || this.log;
    const startTime = Date.now();
    
    logger.debug(`Starting middleware: ${name}`, {
      middleware: name,
      requestId: request.id,
    });
    
    try {
      await middleware.call(this, request, reply);
      
      const duration = Date.now() - startTime;
      logger.debug(`Completed middleware: ${name}`, {
        middleware: name,
        requestId: request.id,
        duration,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Error in middleware: ${name}`, {
        middleware: name,
        requestId: request.id,
        duration,
        error: {
          message: error.message,
          stack: error.stack,
        },
      });
      
      throw error;
    }
  };
}

/**
 * Creates a middleware that caches its result
 * 
 * @param {Function} middleware - Middleware function
 * @param {Function} cacheKeyGenerator - Function to generate cache key
 * @param {Object} options - Cache options
 * @param {number} options.ttl - Time to live in milliseconds
 * @returns {Function} - Caching middleware function
 */
function withCache(middleware, cacheKeyGenerator, options = {}) {
  const { ttl = 60000 } = options; // Default 1 minute
  const cache = new Map();
  
  // Clean up expired cache entries periodically
  setInterval(() => {
    const now = Date.now();
    for (const [key, entry] of cache.entries()) {
      if (now > entry.expiresAt) {
        cache.delete(key);
      }
    }
  }, ttl);
  
  return async function cachingMiddleware(request, reply) {
    const cacheKey = await cacheKeyGenerator.call(this, request);
    const now = Date.now();
    
    // Check if we have a valid cached result
    const cached = cache.get(cacheKey);
    if (cached && now < cached.expiresAt) {
      // Apply cached side effects to the request
      Object.assign(request, cached.requestChanges);
      return;
    }
    
    // Clone request to track changes
    const requestBefore = { ...request };
    
    // Execute middleware
    await middleware.call(this, request, reply);
    
    // Determine what changed in the request
    const requestChanges = {};
    for (const key in request) {
      if (request[key] !== requestBefore[key]) {
        requestChanges[key] = request[key];
      }
    }
    
    // Cache the result
    cache.set(cacheKey, {
      requestChanges,
      expiresAt: now + ttl,
    });
  };
}

module.exports = {
  compose,
  conditional,
  withErrorHandler,
  withLogging,
  withCache,
};
