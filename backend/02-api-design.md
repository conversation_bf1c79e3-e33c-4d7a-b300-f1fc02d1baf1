# API Design Analysis

## Overview

This document analyzes the API design of the PRS-Backend, examining its structure, patterns, and implementation. The API is built using Fastify and follows RESTful principles with a clear organization of endpoints.

## API Structure

The PRS-Backend API is organized into two main categories:

1. **Public Routes** - Accessible without authentication
2. **Private Routes** - Require authentication

The routes are further organized by resource type and versioned with a `v1` or `v2` prefix:

```
/v1/users
/v1/suppliers
/v1/companies
/v1/projects
/v1/departments
/v1/items
/v1/notifications
/v1/auditlogs
/v1/requisitions
/v1/approvals
/v1/canvass
/v1/delivery-receipts
/v1/mock-purchase-orders
/v1/attachments
/v1/delivery-receipt-items
/v1/notes
/v1/leaves
/v1/steelbars
/v1/purchase-orders
/v1/rs-payment-request
/v1/download
/v1/request-history
/v1/non-requisitions
/v1/invoice-reports
/v2/requisitions
```

## Route Implementation

Routes are implemented using Fastify's plugin system, with each resource having its own route file:

```javascript
// Example from interfaces/router/private/userRoute.js
async function userRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const userController = fastify.diScope.resolve('userController');

  const { permission: permissionConstants, user: userConstants } =
    fastify.diScope.resolve('constants');
  const { USER_TYPES, APPROVERS } = userConstants;
  const { PERMISSIONS } = permissionConstants;

  fastify.route({
    method: 'GET',
    url: '/',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
    handler: userController.getUserList.bind(userController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.user.updateUserParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
    handler: userController.getUserDetails.bind(userController),
  });

  // Additional routes...
}
```

## RESTful Design

The API follows RESTful principles with standard HTTP methods:

- **GET** - Retrieve resources
- **POST** - Create resources
- **PUT** - Update resources
- **PATCH** - Partially update resources

Example of RESTful endpoint design:

```javascript
// GET collection
fastify.route({
  method: 'GET',
  url: '/',
  handler: controller.getList.bind(controller),
});

// GET single resource
fastify.route({
  method: 'GET',
  url: '/:id',
  schema: {
    params: entities.resource.getParams,
  },
  handler: controller.getDetails.bind(controller),
});

// POST create resource
fastify.route({
  method: 'POST',
  url: '/',
  schema: {
    body: entities.resource.createRequest,
  },
  handler: controller.create.bind(controller),
});

// PUT update resource
fastify.route({
  method: 'PUT',
  url: '/:id',
  schema: {
    params: entities.resource.updateParams,
    body: entities.resource.updateRequest,
  },
  handler: controller.update.bind(controller),
});
```

## Request Validation

The API uses Zod for request validation through a schema compiler:

```javascript
// Example from interfaces/router/index.js
serverContext.addHook('onRoute', (routeOptions) => {
  routeOptions.validatorCompiler = (data) =>
    utils.validatorCompiler(data.schema);
});
```

Validation schemas are defined using Zod:

```javascript
// Example validation schema
const createUserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(8),
  roleId: z.number().int().positive(),
});
```

## Authentication and Authorization

The API implements authentication using JWT tokens:

```javascript
// Example from app/handlers/middlewares/authenticate.js
const authenticate = async function (request, reply) {
  try {
    const authHeader = request.headers.authorization;
    
    if (!authHeader) {
      throw clientErrors.UNAUTHORIZED();
    }
    
    const token = authHeader.split(' ')[1];
    const decoded = await this.diScope.resolve('authService').verifyToken(token);
    
    request.user = decoded;
  } catch (error) {
    throw clientErrors.UNAUTHORIZED();
  }
};
```

And permission-based authorization:

```javascript
// Example from interfaces/router/private/userRoute.js
fastify.route({
  method: 'GET',
  url: '/',
  preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
  handler: userController.getUserList.bind(userController),
});
```

## Error Handling

The API implements consistent error handling with standardized error responses:

```javascript
// Example error response
{
  "status": 400,
  "message": "Validation failed",
  "description": "{\"field\":\"Field is required\"}",
  "errorCode": "VALIDATION_ERROR",
  "timestamp": "2023-05-03T12:58:00.000Z"
}
```

Error types include:

- **UNAUTHORIZED** (401) - Authentication failure
- **FORBIDDEN** (403) - Authorization failure
- **BAD_REQUEST** (400) - Invalid request
- **VALIDATION_ERROR** (422) - Request validation failure
- **NOT_FOUND** (404) - Resource not found
- **UNPROCESSABLE_ENTITY** (422) - Business rule violation
- **INTERNAL_SERVER_ERROR** (500) - Server error

## Query Parameters

The API supports various query parameters for filtering, pagination, and sorting:

```
GET /v1/requisitions?page=1&limit=10&sortBy[createdAt]=desc&filterBy[status]=pending
```

Query parameter handling:

```javascript
// Example from infra/repositories/purchaseOrderItemRepository.js
async getPOItemsById(payload) {
  const {
    purchaseOrderId,
    search,
    paginate,
    page,
    limit,
    sortBy = '{"createdAt": "desc"}',
  } = payload;

  // Convert the parsedSortBy object into an array format for Sequelize ordering
  const order = Object.entries(JSON.parse(sortBy)).map(([key, value]) => [
    key,
    value.toUpperCase(),
  ]);

  // Implementation details...
}
```

## Response Format

The API returns consistent response formats:

```javascript
// Success response
{
  "result": {
    // Resource data
  },
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalPages": 5,
      "totalRecords": 50
    }
  }
}

// Error response
{
  "status": 400,
  "message": "Validation failed",
  "description": "{\"field\":\"Field is required\"}",
  "errorCode": "VALIDATION_ERROR",
  "timestamp": "2023-05-03T12:58:00.000Z"
}
```

## File Uploads

The API supports file uploads for attachments:

```javascript
// Example from app/utils/index.js
const newAttachmentSchema = z.object({
  buffer: z.instanceof(Buffer),
  encoding: z.string(),
  fieldname: z.string(),
  filePath: z.string(),
  mimetype: z.string(),
  originalname: z.string(),
  size: z.number(),
});
```

## API Versioning

The API implements versioning through URL prefixes:

```javascript
// Example from interfaces/router/private/index.js
serverContext.register(requisitionRoutes, {
  prefix: 'v1/requisitions',
});

serverContext.register(requisitionRoutesV2, {
  prefix: 'v2/requisitions',
});
```

## API Strengths

1. **Clear Organization** - Routes are organized by resource type
2. **Consistent Validation** - Zod validation for all requests
3. **Standardized Error Handling** - Consistent error responses
4. **Permission-Based Authorization** - Granular access control
5. **Support for Pagination, Sorting, and Filtering** - Flexible querying
6. **API Versioning** - Support for evolving API

## API Challenges

1. **Mixed Versioning Approach** - Some resources have v1 and v2 versions, others only v1
2. **Complex Query Parameters** - Some endpoints have complex query parameter handling
3. **Inconsistent Parameter Naming** - Some variation in parameter naming conventions
4. **Limited API Documentation** - No observed API documentation generation

## Recommendations

1. **Standardize API Versioning** - Adopt a consistent approach to API versioning
2. **Implement API Documentation** - Add OpenAPI/Swagger documentation
3. **Standardize Query Parameters** - Adopt consistent parameter naming conventions
4. **Implement Rate Limiting** - Add rate limiting for API protection
5. **Add Response Compression** - Implement response compression for large payloads

## Conclusion

The PRS-Backend API follows RESTful principles with a clear organization of endpoints. The use of Fastify's plugin system, Zod validation, and standardized error handling demonstrates a well-designed API architecture. The API supports the complex business requirements of the purchase requisition system while maintaining consistency and security.

Areas for improvement include standardizing the API versioning approach, implementing comprehensive API documentation, and adopting consistent parameter naming conventions.
