'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('canvass_item_suppliers', 'quantity', {
      type: Sequelize.DECIMAL(13, 3),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('canvass_item_suppliers', 'quantity', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
  },
};
