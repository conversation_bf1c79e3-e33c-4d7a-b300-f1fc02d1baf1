#!/bin/bash

# This is a simple wrapper script for pretty-logs.sh

# Define colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
RESET='\033[0m'

# Function to display usage
display_usage() {
  echo -e "${BLUE}PRS Logs Viewer${RESET}"
  echo -e "A beautiful log viewer for PRS containers"
  echo
  echo -e "Usage: $0 [options] [container]"
  echo
  echo -e "Options:"
  echo -e "  -h, --help     Display this help message"
  echo -e "  -f, --follow   Follow log output"
  echo -e "  -n, --lines N  Display the last N lines"
  echo -e "  -a, --all      Display all containers"
  echo
  echo -e "Containers:"
  echo -e "  backend        Backend container (default)"
  echo -e "  frontend       Frontend container"
  echo -e "  db             Database container"
  echo -e "  nginx          Nginx container"
  echo
  echo -e "Examples:"
  echo -e "  $0                  Display backend logs"
  echo -e "  $0 -f               Follow backend logs"
  echo -e "  $0 -n 50            Display last 50 lines of backend logs"
  echo -e "  $0 frontend         Display frontend logs"
  echo -e "  $0 -f frontend      Follow frontend logs"
  echo -e "  $0 -a               Display all container logs"
}

# Default values
container="backend"
lines="20"
follow=false
all=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -h|--help)
      display_usage
      exit 0
      ;;
    -f|--follow)
      follow=true
      shift
      ;;
    -n|--lines)
      lines="$2"
      shift 2
      ;;
    -a|--all)
      all=true
      shift
      ;;
    backend|frontend|db|nginx)
      container="$1"
      shift
      ;;
    *)
      echo -e "${RED}Unknown option: $1${RESET}"
      display_usage
      exit 1
      ;;
  esac
done

# Map container names to actual container names
case "$container" in
  backend)
    container_name="deployment-backend-1"
    ;;
  frontend)
    container_name="deployment-frontend-1"
    ;;
  db)
    container_name="deployment-postgres-1"
    ;;
  nginx)
    container_name="deployment-nginx-1"
    ;;
  *)
    container_name="$container"
    ;;
esac

# Display logs
if [ "$all" = true ]; then
  echo -e "${GREEN}Displaying logs for all containers${RESET}"
  echo -e "${YELLOW}Backend logs:${RESET}"
  /home/<USER>/prs-prod/deployment/tools/logs/pretty-logs.sh deployment-backend-1 "$lines"
  echo -e "\n${YELLOW}Frontend logs:${RESET}"
  /home/<USER>/prs-prod/deployment/tools/logs/pretty-logs.sh deployment-frontend-1 "$lines"
  echo -e "\n${YELLOW}Database logs:${RESET}"
  /home/<USER>/prs-prod/deployment/tools/logs/pretty-logs.sh deployment-postgres-1 "$lines"
  echo -e "\n${YELLOW}Nginx logs:${RESET}"
  /home/<USER>/prs-prod/deployment/tools/logs/pretty-logs.sh deployment-nginx-1 "$lines"
else
  if [ "$follow" = true ]; then
    echo -e "${GREEN}Following logs for $container container${RESET}"
    /home/<USER>/prs-prod/deployment/tools/logs/pretty-logs.sh "$container_name" "$lines" --follow
  else
    echo -e "${GREEN}Displaying logs for $container container${RESET}"
    /home/<USER>/prs-prod/deployment/tools/logs/pretty-logs.sh "$container_name" "$lines"
  fi
fi
