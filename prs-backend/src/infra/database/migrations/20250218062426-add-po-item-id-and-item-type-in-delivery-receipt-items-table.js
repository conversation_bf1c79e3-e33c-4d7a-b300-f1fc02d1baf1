'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('delivery_receipt_items', 'po_item_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'purchase_order_items',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addColumn('delivery_receipt_items', 'item_type', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('delivery_receipt_items', 'po_item_id');
    await queryInterface.removeColumn('delivery_receipt_items', 'item_type');
  }
};

