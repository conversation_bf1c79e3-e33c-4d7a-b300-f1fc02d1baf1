# PRS Developer Documentation

This repository contains the developer documentation for the PRS (Purchase Requisition System) project, built with MkDocs and the Material for MkDocs theme.

## Getting Started

### Prerequisites

Choose one of the following options:

#### Option 1: Using Docker (Recommended)

- Docker
- Docker Compose

#### Option 2: Using Python

- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. Clone this repository:
   ```bash
   git clone https://gitlab.stratpoint.dev/cityland/documentation.git
   cd documentation
   ```

2. Choose your installation method:

   **Option 1: Using Docker (Recommended)**

   No additional installation steps required! Docker will handle everything.

   **Option 2: Using Python**

   a. Create a virtual environment (optional but recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

   b. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

### Local Development

#### Using Python

If you have Python installed, you can run the development server directly:

```bash
mkdocs serve
```

This will start a local server at http://127.0.0.1:8000/. The site will automatically reload when you make changes to the documentation.

#### Using Docker

If you prefer to use Docker (recommended for consistent environments), you can use the official MkDocs Docker image:

```bash
# Run the development server
docker run --rm -it -p 8000:8000 -v ${PWD}:/docs squidfunk/mkdocs-material serve -a 0.0.0.0:8000

# Build the documentation
docker run --rm -it -v ${PWD}:/docs squidfunk/mkdocs-material build
```

#### Using Docker Compose (Recommended)

For the easiest development experience, use the included Docker Compose configuration:

```bash
# Start the development server
docker compose up

# Build the documentation
docker compose run --rm mkdocs build
```

To stop the server when using Docker Compose:

```bash
docker compose down
```

#### Using the Helper Script (Easiest)

We've included a helper script that makes working with the documentation even easier:

```bash
# Make the script executable (first time only)
chmod +x docs.sh

# Start the development server
./docs.sh serve

# Build the documentation
./docs.sh build

# Stop the development server
./docs.sh stop

# Deploy to Cloudflare Pages
./docs.sh deploy

# Deploy with custom domain
./docs.sh deploy custom prs-docs.stratpoint.io

# Show help
./docs.sh help
```

The Docker approach has several advantages:
- No need to install Python or dependencies locally
- Consistent environment across different machines
- Includes all Material for MkDocs plugins and extensions
- Works on any operating system with Docker installed

### Building the Site

To build the static site:

```bash
mkdocs build
```

This will create a `site` directory with the built HTML files.

## Documentation Structure

- `docs/`: Contains all the documentation files
  - `index.md`: Home page
  - `system-guide/`: System architecture, business rules, etc.
  - `how-to/`: How-to guides for common tasks
  - `refactoring-guide/`: Guidelines for refactoring
  - `enhancement/`: Planned enhancements
  - `stylesheets/`: Custom CSS files
  - `javascripts/`: Custom JavaScript files
  - `overrides/`: Custom HTML templates

- `mkdocs.yml`: MkDocs configuration file

## Project Files

- **Core Files:**
  - `mkdocs.yml`: Main configuration file for MkDocs
  - `docs.sh`: Helper script for building, serving, and deploying documentation
  - `docker-compose.yml`: Docker Compose configuration for local development
  - `Dockerfile.mkdocs`: Custom Docker image definition with required plugins

- **Configuration Files:**
  - `requirements.txt`: Python dependencies for non-Docker installation
  - `cloudflare-pages.toml`: Configuration for Cloudflare Pages deployments
  - `.gitignore`: Specifies intentionally untracked files to ignore
  - `.gitattributes`: Defines attributes for paths in the repository

- **Documentation:**
  - `README.md`: This file - project overview and instructions
  - `cloudflare-token-guide.md`: Guide for creating Cloudflare API tokens

- **Generated Directories:**
  - `site/`: Generated static site (created by `mkdocs build`)
  - `.wrangler/`: Wrangler CLI configuration for Cloudflare Pages

## Features

- **Dark Mode by Default**: The documentation uses a dark theme by default for reduced eye strain
- **Responsive Design**: Works well on desktop, tablet, and mobile devices
- **Search Functionality**: Built-in search to quickly find documentation
- **Code Highlighting**: Syntax highlighting for code blocks
- **Mermaid Diagrams**: Support for creating diagrams using Mermaid syntax
- **Protected Access**: Secured with Cloudflare Zero Trust for controlled access

## Contributing

1. Create a new branch for your changes
2. Make your changes to the documentation
3. Test your changes locally using `mkdocs serve`
4. Submit a pull request

## Testing and Deployment

### Local Testing

You can test the documentation locally before deploying:

1. Start the development server:
   ```bash
   ./docs.sh serve
   ```

2. Build the documentation:
   ```bash
   ./docs.sh build
   ```

This allows you to preview the documentation before deploying it to Cloudflare Pages.

### Cloudflare Pages with Zero Trust (Recommended)

Cloudflare Pages with Zero Trust provides a fast, secure, and access-controlled way to deploy your documentation:

1. Install Wrangler (Cloudflare's CLI tool):
   ```bash
   npm install -g wrangler
   ```

2. Log in to your Cloudflare account:
   ```bash
   wrangler login
   ```

3. Deploy to Cloudflare Pages with your custom domain:
   ```bash
   ./docs.sh deploy custom prs-docs.stratpoint.io
   ```

4. Set up Zero Trust access control:
   - Go to Cloudflare Zero Trust dashboard
   - Navigate to Access → Applications
   - Add an application for your custom domain with your desired access policies

Once deployed and configured, your documentation will be available at:
```
https://prs-docs.stratpoint.io
```

And protected by Cloudflare Zero Trust, ensuring only authorized users can access it.

### Manual Deployment

If you want to deploy the documentation manually:

1. Build the static site:
   ```bash
   mkdocs build
   ```

2. The built site will be in the `site` directory, which you can deploy to any web hosting service.
