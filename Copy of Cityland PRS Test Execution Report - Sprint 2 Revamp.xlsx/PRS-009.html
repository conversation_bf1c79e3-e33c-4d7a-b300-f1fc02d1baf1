<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1743619478C0" style="width:103px;" class="column-headers-background">A</th><th id="1743619478C1" style="width:135px;" class="column-headers-background">B</th><th id="1743619478C2" style="width:59px;" class="column-headers-background">C</th><th id="1743619478C3" style="width:252px;" class="column-headers-background">D</th><th id="1743619478C4" style="width:233px;" class="column-headers-background">E</th><th id="1743619478C5" style="width:330px;" class="column-headers-background">F</th><th id="1743619478C6" style="width:104px;" class="column-headers-background">G</th><th id="1743619478C7" style="width:346px;" class="column-headers-background">H</th><th id="1743619478C8" style="width:100px;" class="column-headers-background">I</th><th id="1743619478C9" style="width:88px;" class="column-headers-background">J</th><th id="1743619478C10" style="width:144px;" class="column-headers-background">K</th><th id="1743619478C11" style="width:144px;" class="column-headers-background">L</th><th id="1743619478C12" style="width:144px;" class="column-headers-background">M</th><th id="1743619478C13" style="width:144px;" class="column-headers-background">N</th><th id="1743619478C14" style="width:138px;" class="column-headers-background">O</th><th id="1743619478C15" style="width:225px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1743619478R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1743619478R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-009 - [Manage Requisition Slip] - Create Requistion Slip for Non-OFM, Transfer of Materials, OFM</td></tr><tr style="height: 19px"><th id="1743619478R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-009-001</td><td class="s6">Non-OFM Item RS</td><td class="s6">Critical</td><td class="s6">Verify Creation of Requisition Slip for Non-OFM Items</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- <br>- http://*************/login<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>5. Initial RS Details has been setup</a></td><td class="s6">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Non-OFM on Type of Request dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Check filter items<br>6. Select items in the modal<br>7. Click &quot;x&quot; icon on one of selected items<br>8. Click &quot;Cancel&quot; button on the add items modal<br>9.  Click &quot;Add Items&quot;  button in Items Section<br>10. Select items in the modal<br>11. Click &quot;Add Items&quot; button on the modal<br>12. Validate Quantity field in the table<br>13. Validate Notes field in the table<br>14. Click &quot;x&quot; button in the table<br>15. Click Save draft<br>16 Click Submit button</td><td class="s6"></td><td class="s6">1. Should display RS Creation Form<br>2. Should be able to select Non-OFM Type of Request<br>3. Should be able to populate all fields<br>4. Should display a Modal for adding of Items<br>5. Should filter the Items to be shown and display Non-OFM Items <br>6. Items should be selected<br>7. should be able to removed selected items<br>8. Should redirected back to RS creaqtion form and not able to add previous selected items<br>9. Should display a Modal for adding of Items<br>10. Items should be selected<br>11. Should display and added the selected items in the Table list<br>12. Should allow entering of Quantity per Item <br>    a. Numeric Values only<br>    b. Max Length of 3<br>13. Should allow entering of Notes per Item<br>    a. Text area of Notes with a Maximum Character of 100 Characters, Alphanumeric, and Special Characters except Emojis<br>    b. Field is Optional<br>14. Should allow removing an Item<br>15. Should be able to save a draft and create a temporary RS number in the list with draft status<br>16. Should be able to submit successfully with success toast message and added the new Non OFM Type of Request Requisition Slip in the list with &quot;Submitted&quot; status</td><td class="s8" rowspan="6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1ug3uLcS5U98nqLNkK3K42dEfDT0LlmF6KJFpflfxnno/edit?gid=583752595#gid=583752595&amp;range=A1">https://docs.google.com/spreadsheets/d/1ug3uLcS5U98nqLNkK3K42dEfDT0LlmF6KJFpflfxnno/edit?gid=583752595#gid=583752595&amp;range=A1</a></td><td class="s9">Failed</td><td class="s10">Failed</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-806">same issue on month 3<br><br>RS creation notes displays success message after saving notes with emoji input<br><br>Quantity of items in RS creation is able to input more than 3 digits<br><br>Able to input emoji in items notes<br><br>Item Notes is not optional<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-797<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-798<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-805<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-806</a></td><td class="s11">Not Started</td><td class="s5"></td><td class="s5">Distorted Add Items of non ofm<br><br>RS creation notes displays success message after saving notes with emoji input<br><br>Quantity of items in RS creation is able to input more than 3 digits<br><br>Able to input emoji in items notes<br><br>Item Notes is not optional</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-741">https://youtrack.stratpoint.com/issue/CITYLANDPRS-741<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-797<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-798<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-805<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-806<br><br></a></td></tr><tr style="height: 19px"><th id="1743619478R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-009-002</td><td class="s6">Non-OFM Item RS</td><td class="s6">Critical</td><td class="s6">Verify Creation of Requisition Slip for Non-OFM Items when clicked cancel</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br><br>- http://*************/login<br><br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>5. Initial RS Details has been setup</a></td><td class="s6">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Non-OFM on Type of Request dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Check filter items<br>6. Select items in the modal<br>7. Click &quot;x&quot; icon on one of selected items<br>8. Click &quot;Cancel&quot; button on the add items modal<br>9.  Click &quot;Add Items&quot;  button in Items Section<br>10. Select items in the modal<br>11. Click &quot;Add Items&quot; button on the modal<br>12. Validate Quantity field in the table<br>13. Validate Notes field in the table<br>14. Click &quot;x&quot; button in the table<br>15. Click &quot;Cancel&quot; button on RS Creation form<br>16. Clck &quot;Cancel&quot; on the Confirmation Modal<br>17. Click  &quot;Cancel&quot; button again on RS Creation form<br>18. Click &quot;Continue&quot; on the Confirmation Modal</td><td class="s6"><br></td><td class="s6">1. Should display RS Creation Form<br>2. Should be able to select Non-OFM Type of Request<br>3. Should be able to populate all fields<br>4. Should display a Modal for adding of Items<br>5. Should filter the Items to be shown and display Non-OFM Items <br>6. Items should be selected<br>7. should be able to removed selected items<br>8. Should redirected back to RS creaqtion form and not able to add previous selected items<br>9. Should display a Modal for adding of Items<br>10. Items should be selected<br>11. Should display and added the selected items in the Table list<br>12. Should allow entering of Quantity per Item <br>    a. Numeric Values only<br>    b. Max Length of 3<br>13. Should allow entering of Notes per Item<br>    a. Text area of Notes with a Maximum Character of 100 Characters, Alphanumeric, and Special Characters except Emojis<br>    b. Field is Optional<br>14. Should allow removing an Item<br>15. Should display a &quot;Cancel Requisition Slip&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot; <br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>16. Should retained the previous populated RS Creation form and able to continue on creating RS<br>17. Should display a &quot;Cancel Requisition Slip&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot; <br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>18. Should redirected back to RS Dashboard list and not able to continue on Creation of RS</td><td class="s13">Passed</td><td class="s14">Passed</td><td class="s5"></td><td class="s11">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1743619478R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">PRS-009-003</td><td class="s6">Transfer of Materials RS</td><td class="s6">Critical</td><td class="s6">Verify Creation of Requisition Slip for Transfer of Materials for OFM</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- <br>- http://*************/login<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Initial RS Details has been setup</a></td><td class="s6">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click OFM Transfer of materials on Type of Request dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Check filter items<br>6. Select items in the modal<br>7. Click &quot;x&quot; icon on one of selected items<br>8. Click &quot;Cancel&quot; button on the add items modal<br>9.  Click &quot;Add Items&quot;  button in Items Section<br>10. Select items in the modal<br>11. Click &quot;Add Items&quot; button on the modal<br>12. Validate Quantity field in the table<br>13. Validate Notes field in the table<br>14. Click &quot;x&quot; button in the table<br>15. Click Save draft<br>16 Click Submit button</td><td class="s15"></td><td class="s6">1. Should display RS Creation Form<br>2. Should be able to select OFM Transfer of MaterialsType of Request<br>3. Should be able to populate all fields<br>4. Should allow adding of Items from the OFM Lists categorized for the Requesting user<br>5. Should filter the Items to be shown and display OFM Items <br>6. Items should be selected<br>7. should be able to removed selected items<br>8. Should redirected back to RS creaation form and not able to add previous selected items<br>9. Should display a Modal for adding of Items<br>10. Items should be selected<br>11. Should display and added the selected items in the Table list<br>12. Should allow entering of Quantity per Item <br>    a. Numeric Values only<br>    b. Max Length of 3<br>    c. Should refer to the Remaining GFQ of the Item<br>13. Should allow entering of Notes per Item<br>    a. Text area of Notes with a Maximum Character of 100 Characters, Alphanumeric, and Special Characters except Emojis<br>    b. Field is Optional<br>14. Should allow removing an Item<br>15. Should be able to save a draft and create a temporary RS number in the list with draft status<br>16. Should be able to submit successfully with success toast message and added the new Non OFM Type of Request Requisition Slip in the list with &quot;Submitted&quot; status</td><td class="s9">Failed</td><td class="s10">Failed</td><td class="s5">same issue on month 3<br><br>RS creation notes displays success message after saving notes with emoji input<br><br>Quantity of items in RS creation is able to input more than 3 digits<br><br>Able to input emoji in items notes<br><br>Item Notes is not optional</td><td class="s11">Not Started</td><td class="s5"></td><td class="s5">[Manage RS] Search Item not working properly for OFM Transfer of Materials<br><br>RS creation notes displays success message after saving notes with emoji input<br><br>Able to input emoji in items notes<br><br>Item Notes is not optional</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-804">https://youtrack.stratpoint.com/issue/CITYLANDPRS-804<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-797<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-798<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-805<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-806</a></td></tr><tr style="height: 19px"><th id="1743619478R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">PRS-009-004</td><td class="s6">Transfer of Materials RS</td><td class="s6">Critical</td><td class="s6">Verify Creation of Requisition Slip for Transfer of Materials for OFM when clicked cancel</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- <br>- http://*************/login<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Initial RS Details has been setup</a></td><td class="s6">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick OFM Transfer of materials on Type of Request dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Check filter items<br>6. Select items in the modal<br>7. Click &quot;x&quot; icon on one of selected items<br>8. Click &quot;Cancel&quot; button on the add items modal<br>9.  Click &quot;Add Items&quot;  button in Items Section<br>10. Select items in the modal<br>11. Click &quot;Add Items&quot; button on the modal<br>12. Validate Quantity field in the table<br>13. Validate Notes field in the table<br>14. Click &quot;x&quot; button in the table<br>15. Click &quot;Cancel&quot; button on RS Creation form<br>16. Clck &quot;Cancel&quot; on the Confirmation Modal<br>17. Click  &quot;Cancel&quot; button again on RS Creation form<br>18. Click &quot;Continue&quot; on the Confirmation Modal</td><td class="s15"></td><td class="s6">1. Should display RS Creation Form<br>2. Should be able to select OFM Transfer of MaterialsType of Request<br>3. Should be able to populate all fields<br>4. Should allow adding of Items from the OFM Lists categorized for the Requesting user<br>5. Should filter the Items to be shown and display OFM Items <br>6. Items should be selected<br>7. should be able to removed selected items<br>8. Should redirected back to RS creaation form and not able to add previous selected items<br>9. Should display a Modal for adding of Items<br>10. Items should be selected<br>11. Should display and added the selected items in the Table list<br>12. Should allow entering of Quantity per Item <br>    a. Numeric Values only<br>    b. Max Length of 3<br>    c. Should refer to the Remaining GFQ of the Item<br>13. Should allow entering of Notes per Item<br>    a. Text area of Notes with a Maximum Character of 100 Characters, Alphanumeric, and Special Characters except Emojis<br>    b. Field is Optional<br>14. Should allow removing an Item<br>15. Should display a &quot;Cancel Requisition Slip&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot; <br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>16. Should retained the previous populated RS Creation form and able to continue on creating RS<br>17. Should display a &quot;Cancel Requisition Slip&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot; <br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>18. Should redirected back to RS Dashboard list and not able to continue on Creation of RS</td><td class="s13">Passed</td><td class="s14">Passed</td><td class="s5"></td><td class="s11">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1743619478R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">PRS-009-005</td><td class="s6">Transfer of Materials RS</td><td class="s6">Critical</td><td class="s6">Verify Creation of Requisition Slip for Transfer of Materials for Non-OFM</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- <br>- http://*************/login<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Initial RS Details has been setup</a></td><td class="s6">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Non-OFM Transfer of materials on Type of Request dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Check filter items<br>6. Select items in the modal<br>7. Click &quot;x&quot; icon on one of selected items<br>8. Click &quot;Cancel&quot; button on the add items modal<br>9.  Click &quot;Add Items&quot;  button in Items Section<br>10. Select items in the modal<br>11. Click &quot;Add Items&quot; button on the modal<br>12. Validate Quantity field in the table<br>13. Validate Notes field in the table<br>14. Click &quot;x&quot; button in the table<br>15. Click Save draft<br>16 Click Submit button</td><td class="s15"></td><td class="s6">1. Should display RS Creation Form<br>2. Should be able to select Non-OFM Transfer of MaterialsType of Request<br>3. Should be able to populate all fields<br>4. Should allow adding of Items from the created Non-OFM Items in PRS<br>5. Should filter the Items to be shown and display Non-OFM Items <br>6. Items should be selected<br>7. should be able to removed selected items<br>8. Should redirected back to RS creation form and not able to add previous selected items<br>9. Should display a Modal for adding of Items<br>10. Items should be selected<br>11. Should display and added the selected items in the Table list<br>12. Should allow entering of Quantity per Item <br>    a. Numeric Values only<br>    b. Max Length of 3<br>    c. Should refer to the Remaining GFQ of the Item<br>13. Should allow entering of Notes per Item<br>    a. Text area of Notes with a Maximum Character of 100 Characters, Alphanumeric, and Special Characters except Emojis<br>    b. Field is Optional<br>14. Should allow removing an Item<br>15. Should be able to save a draft and create a temporary RS number in the list with draft status<br>16. Should be able to submit successfully with success toast message and added the new Non OFM Type of Request Requisition Slip in the list with &quot;Submitted&quot; status</td><td class="s9">Failed</td><td class="s10">Failed</td><td class="s5">same issue on month 3<br><br>RS creation notes displays success message after saving notes with emoji input<br><br>Quantity of items in RS creation is able to input more than 3 digits<br><br>Able to input emoji in items notes<br><br>Item Notes is not optional</td><td class="s11">Not Started</td><td class="s5"></td><td class="s5">Distorted Add Items of non ofm<br><br>RS creation notes displays success message after saving notes with emoji input<br><br>Quantity of items in RS creation is able to input more than 3 digits<br><br>Able to input emoji in items notes<br><br>Item Notes is not optional</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-741">https://youtrack.stratpoint.com/issue/CITYLANDPRS-741<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-797<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-798<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-805<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-806<br><br></a></td></tr><tr style="height: 19px"><th id="1743619478R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">PRS-009-006</td><td class="s6">Transfer of Materials RS</td><td class="s6">Critical</td><td class="s6">Verify Creation of Requisition Slip for Transfer of Materials for Non-OFM when clicked cancel</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- <br>- http://*************/login<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Initial RS Details has been setup</a></td><td class="s6">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Non-OFM Transfer of materials on Type of Request dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Check filter items<br>6. Select items in the modal<br>7. Click &quot;x&quot; icon on one of selected items<br>8. Click &quot;Cancel&quot; button on the add items modal<br>9.  Click &quot;Add Items&quot;  button in Items Section<br>10. Select items in the modal<br>11. Click &quot;Add Items&quot; button on the modal<br>12. Validate Quantity field in the table<br>13. Validate Notes field in the table<br>14. Click &quot;x&quot; button in the table<br>15. Click &quot;Cancel&quot; button on RS Creation form<br>16. Clck &quot;Cancel&quot; on the Confirmation Modal<br>17. Click  &quot;Cancel&quot; button again on RS Creation form<br>18. Click &quot;Continue&quot; on the Confirmation Modal</td><td class="s15"></td><td class="s6">1. Should display RS Creation Form<br>2. Should be able to select Non-OFM Transfer of MaterialsType of Request<br>3. Should be able to populate all fields<br>4. Should allow adding of Items from the created Non-OFM Items in PRS<br>5. Should filter the Items to be shown and display Non-OFM Items <br>6. Items should be selected<br>7. should be able to removed selected items<br>8. Should redirected back to RS creation form and not able to add previous selected items<br>9. Should display a Modal for adding of Items<br>10. Items should be selected<br>11. Should display and added the selected items in the Table list<br>12. Should allow entering of Quantity per Item <br>    a. Numeric Values only<br>    b. Max Length of 3<br>    c. Should refer to the Remaining GFQ of the Item<br>13. Should allow entering of Notes per Item<br>    a. Text area of Notes with a Maximum Character of 100 Characters, Alphanumeric, and Special Characters except Emojis<br>    b. Field is Optional<br>14. Should allow removing an Item<br>15. Should display a &quot;Cancel Requisition Slip&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot; <br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>16. Should retained the previous populated RS Creation form and able to continue on creating RS<br>17. Should display a &quot;Cancel Requisition Slip&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot; <br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>18. Should redirected back to RS Dashboard list and not able to continue on Creation of RS</td><td class="s13">Passed</td><td class="s14">Passed</td><td class="s5"></td><td class="s11">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr></tbody></table></div>