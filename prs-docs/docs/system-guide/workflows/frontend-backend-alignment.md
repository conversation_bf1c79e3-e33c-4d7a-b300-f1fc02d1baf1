# Frontend-Backend Workflow Alignment

This document provides guidance on aligning the frontend and backend workflow implementations in the PRS system. It includes visual diagrams, synchronization points, and recommendations for maintaining consistency between the two layers.

## System Architecture Overview

The PRS system uses a layered architecture with distinct frontend and backend components:

- **Frontend**: React application with Zustand for state management and React Query for API integration
- **Backend**: Node.js application with Fastify framework and Sequelize ORM for database access

## Workflow Visualization

The following diagram illustrates the overall workflow architecture, showing how user actions flow through the system:

```mermaid
graph TD
    User([User]) --> UI[Frontend UI]
    UI --> Actions[Action Handlers]
    Actions --> ZustandStores[Zustand Stores]
    Actions --> ReactQuery[React Query]
    ReactQuery --> Backend[Backend API]
    Backend --> Database[(Database)]
    ReactQuery --> UI
    ZustandStores --> UI

    Draft[Draft] --> Submitted[Submitted]
    Submitted --> Approved[Approved]
    Submitted --> Rejected[Rejected]

    CSDraft[CS Draft] --> CSForApproval[For CS Approval]
    CSForApproval --> CSApproved[CS Approved]

    ForPOReview[PO Review] --> ForPOApproval[PO Approval]
    ForPOApproval --> ForDelivery[For Delivery]
```

## Workflow Sequence Diagram

The following sequence diagram shows the interaction between frontend and backend components during a typical workflow:

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant ZS as Zustand
    participant RQ as ReactQuery
    participant API
    participant DB as Database

    User->>UI: Create Requisition
    UI->>ZS: Initialize store
    User->>UI: Add items
    UI->>ZS: Update store
    User->>UI: Submit

    UI->>RQ: Submit mutation
    RQ->>API: PUT request
    API->>DB: Update status
    API->>RQ: Return response
    RQ->>UI: Update UI
```

## Key Synchronization Points

The diagrams highlight several critical synchronization points where frontend and backend must align:

1. **Status Transitions**: When a workflow status changes, both frontend and backend must agree on the new status
2. **Permission Checks**: Actions allowed in the UI must match permissions enforced by the backend
3. **Data Validation**: Validation rules must be consistent between frontend and backend
4. **Error Handling**: Both layers must handle errors consistently

## Common Alignment Issues

The following issues can cause misalignment between frontend and backend workflows:

1. **Inconsistent Status Representation**: Frontend and backend use different status values
2. **Missing Validation**: Frontend allows actions that backend will reject
3. **Stale Data**: Frontend displays outdated status information
4. **Incomplete Error Handling**: Frontend doesn't properly handle backend errors
5. **Optimistic Updates Without Rollback**: Frontend updates UI before backend confirms

## Recommendations for Maintaining Alignment

To maintain alignment between frontend and backend workflows:

1. **Use Consistent Status Constants**: Share status constants between frontend and backend
2. **Implement Comprehensive Validation**: Validate actions in both frontend and backend
3. **Refresh Data After Status Changes**: Invalidate relevant queries after mutations
4. **Handle Errors Gracefully**: Provide clear error messages and recovery options
5. **Test Edge Cases**: Test concurrent updates, network failures, and permission edge cases

## Status Mapping

The following table maps backend status values to frontend display values:

| Workflow | Backend Status | Frontend Display | CSS Class |
|----------|---------------|------------------|-----------|
| Requisition | draft | Draft | draft |
| Requisition | submitted | For Approval | for_approval |
| Requisition | approved | Approved | approved |
| Requisition | rejected | Rejected | rejected |
| Canvass | draft | CS Draft | draft |
| Canvass | for_approval | For CS Approval | for_approval |
| Canvass | approved | CS Approved | approved |
| Canvass | rejected | CS Rejected | rejected |
| Purchase Order | for_po_review | For PO Review | for_po_review |
| Purchase Order | for_po_approval | For PO Approval | for_po_approval |
| Purchase Order | for_delivery | For Delivery | for_delivery |
| Purchase Order | reject_po | Rejected PO | rejected |
