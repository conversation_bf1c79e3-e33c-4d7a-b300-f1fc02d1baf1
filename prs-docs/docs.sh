#!/bin/bash

# Script to help with common MkDocs tasks

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to display help
show_help() {
    echo -e "${YELLOW}PRS Developer Documentation Helper${NC}"
    echo "==============================="
    echo ""
    echo "Usage: ./docs.sh [command]"
    echo ""
    echo "Commands:"
    echo "  serve                Start the development server using Docker"
    echo "  build                Build the documentation using Docker"
    echo "  stop                 Stop the development server"
    echo "  deploy               Deploy to Cloudflare Pages (requires Wrangler)"
    echo "  deploy custom <domain>  Deploy with a custom domain"
    echo "  help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./docs.sh serve                    # Start the development server"
    echo "  ./docs.sh build                    # Build the documentation"
    echo "  ./docs.sh deploy                   # Deploy to Cloudflare Pages"
    echo "  ./docs.sh deploy custom prs-docs.stratpoint.io  # Deploy with custom domain"
    echo ""
    echo "Zero Trust Setup:"
    echo "  After deploying with a custom domain, you can configure Cloudflare Zero Trust"
    echo "  to restrict access to your documentation using your custom domain."
    echo "  See the deployment output for detailed instructions."
    echo ""
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Error: Docker is not installed. Please install Docker first.${NC}"
        exit 1
    fi
}

# Check if Docker Compose is installed
check_docker_compose() {
    if ! command -v docker compose &> /dev/null; then
        echo -e "${RED}Error: Docker Compose is not installed. Please install Docker Compose first.${NC}"
        exit 1
    fi
}

# Start the development server
serve() {
    check_docker_compose
    echo -e "${YELLOW}Starting development server...${NC}"
    docker compose up
}

# Build the documentation
build() {
    check_docker_compose
    echo -e "${YELLOW}Building documentation...${NC}"
    docker compose run --rm mkdocs build
    echo -e "${GREEN}Documentation built successfully in the 'site' directory.${NC}"
}

# Stop the development server
stop() {
    check_docker_compose
    echo -e "${YELLOW}Stopping development server...${NC}"
    docker compose down
    echo -e "${GREEN}Development server stopped.${NC}"
}

# Deploy to Cloudflare Pages
deploy() {
    # Check if npm is installed (needed for Wrangler)
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}Error: npm is not installed. Please install Node.js and npm first.${NC}"
        exit 1
    fi

    # Check if Wrangler is installed
    if ! command -v wrangler &> /dev/null; then
        echo -e "${YELLOW}Wrangler is not installed. Installing Wrangler...${NC}"
        npm install -g wrangler
    fi

    # Build the documentation
    echo -e "${YELLOW}Building documentation...${NC}"
    build

    # Check if a custom domain was provided
    DOMAIN=""

    # Check if this is a custom domain deployment
    if [ "$1" = "deploy" ] && [ "$2" = "custom" ] && [ -n "$3" ]; then
        DOMAIN="$3"
        echo -e "${YELLOW}Using custom domain: ${GREEN}$DOMAIN${NC}"
    fi

    # Deploy to Cloudflare Pages
    echo -e "${YELLOW}Deploying to Cloudflare Pages...${NC}"
    wrangler pages deploy site --project-name=prs-docs

    # Check if the deployment was successful
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Deployment successful!${NC}"
        echo ""
        echo "Your documentation has been deployed."
        echo ""
        if [ -n "$DOMAIN" ]; then
            echo "To set up your custom domain ($DOMAIN) with Zero Trust:"
            echo "1. Go to the Cloudflare Pages dashboard"
            echo "2. Select your 'prs-docs' project"
            echo "3. Go to 'Custom domains' tab and add: $DOMAIN"
            echo ""
            echo "4. Go to Cloudflare Zero Trust dashboard"
            echo "5. Navigate to Access → Applications"
            echo "6. Add an application for $DOMAIN with your desired access policies"
            echo ""
            echo "Your documentation will be accessible at:"
            echo -e "${GREEN}https://$DOMAIN${NC}"
            echo "And protected by Cloudflare Zero Trust"
        else
            echo "To use a custom domain with Zero Trust, run:"
            echo -e "${YELLOW}./docs.sh deploy custom prs-docs.stratpoint.io${NC}"
            echo "Then follow the instructions to set it up with Zero Trust."
        fi
    else
        echo -e "${RED}Deployment failed!${NC}"
        echo ""
        echo "Check the error messages above for more information."
    fi
}

# Main script logic
case "$1" in
    serve)
        serve
        ;;
    build)
        build
        ;;
    stop)
        stop
        ;;
    deploy)
        deploy "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        ;;
esac

# Make the script executable
chmod +x docs.sh
