<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f6b26b;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-right:1px SOLID #000000;background-color:#f6b26b;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f6b26b;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f6b26b;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1465565150C0" style="width:103px;" class="column-headers-background">A</th><th id="1465565150C1" style="width:167px;" class="column-headers-background">B</th><th id="1465565150C2" style="width:252px;" class="column-headers-background">C</th><th id="1465565150C3" style="width:84px;" class="column-headers-background">D</th><th id="1465565150C4" style="width:233px;" class="column-headers-background">E</th><th id="1465565150C5" style="width:330px;" class="column-headers-background">F</th><th id="1465565150C6" style="width:346px;" class="column-headers-background">G</th><th id="1465565150C7" style="width:511px;" class="column-headers-background">H</th><th id="1465565150C8" style="width:63px;" class="column-headers-background">I</th><th id="1465565150C9" style="width:114px;" class="column-headers-background">J</th><th id="1465565150C10" style="width:136px;" class="column-headers-background">K</th><th id="1465565150C11" style="width:136px;" class="column-headers-background">L</th><th id="1465565150C12" style="width:136px;" class="column-headers-background">M</th><th id="1465565150C13" style="width:136px;" class="column-headers-background">N</th><th id="1465565150C14" style="width:78px;" class="column-headers-background">O</th><th id="1465565150C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1465565150R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(STG_wk4&amp;5)</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1465565150R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="16">PRS-032 - [M2 Feedback] - Mockup Data Cleanup for Company                                                <br>Update the behavior of Charge To, Company, Project and DepartmentInclude Company Intials in the Drop-down Values for Company when creating a Requisition Slip                                                <br>Update the functionality of the Drop-down Fields in Requisition Slip Creation<br>Updating of Draft Reference Number when updating the Company in the Requisition Slip<br>Updating of Draft Requisition Slip should only be done by the Requestor        </td></tr><tr style="height: 19px"><th id="1465565150R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4"></td><td class="s4">Mockup Data Cleanup for Company</td><td class="s4">Verify Company data should sync to Cityland  Mock Data</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account  as Admin</a></td><td class="s4">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Click &quot;Sync&quot; button<br>4. Check List of Company Displayed</td><td class="s5"></td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should initiate syncing of Companies from Company Master File and update the Company Details if an update on the existing Company has been made in the Company Master File <br>4. Should display the correct company list from Cityland Mock data<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">COMPCD - CONAME - COINT<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1 - CITYLAND DEVELOPMENT CORPORATION - CDC<br>2 - CITYLAND, INC. - CI<br>3 - CITYMERGE HOLDINGS INC. - CMHI /<br>4 - CITYLOTS HOLDINGS, INC. - CHI<br>5 - CITYADS, INCORPORATED - CAI<br>6 - CREDIT &amp; LAND HOLDINGS, INC. - CLHI<br>8 - MAKATI GOSPEL CHURCH - MGC<br>9 - CITYRISE HOLDINGS INC. - CRI /<br>10 - BUILD &amp; YIELD HOLDINGS INC. - BYHI<br>11 - BUILDINVEST HOLDING INC. - BHI / <br>12 - CITY &amp; LAND DEVELOPERS, INCORPORATED - CLDI / <br>13 - CITYPLANS, INCORPORATED - CPI<br></span></td><td class="s4" rowspan="18"></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1465565150R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4"></td><td class="s4">Mockup Data Cleanup for Company</td><td class="s4">Verify A-Z Prefix on the Company is removed</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account  as Admin</a></td><td class="s4">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Company&quot; sub menu<br>3. Check A-Z Prefix in the Company Names</td><td class="s5"></td><td class="s4">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Company/Association Managementt page <br>3. Should remove A-Z Prefix on the Company Names<br></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1465565150R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4"></td><td class="s4">Mockup Data Cleanup for Company</td><td class="s4">Verify the company code should be sync when creating a Requisition Slip and other Documents and clicked &quot;save draft&quot;</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account  as Admin</a></td><td class="s4">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select &quot;Company&quot; in Charge To (Category) field <br>3. Populate all fields<br>4. Click Save Draft<br>5. Check &quot;Company Code&quot; of Temporary RS Number</td><td class="s5"></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TJDA02NlZPAOBw8hmG-3FbjHKqfGA51mOAqsMkI2JMc/edit?gid=**********#gid=**********"><br>1. Should display RS Creation Form<br>2. Charge To (Category) field is populated<br>3. All fields should be populated<br>4. Successfully Save as draft and Temporary RS Number should be displayed<br>5. Should follow the Company Code when creating a Requisition Slip and other Documents.<br>                Format: RS-TMP-[CompanyCD][AA-ZZ][********-********]<br>                Sample: RS-TMP-01AA00000003<br>Please refer to:<br>DB STRUCTURE:<br>FINAL Cityland database file structure and sample dataset</a></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1465565150R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4"></td><td class="s4">Mockup Data Cleanup for Company</td><td class="s4">Verify the company code should be sync when creating a Requisition Slip and other Documents and clicked submit</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account  as Admin</a></td><td class="s4">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select &quot;Company&quot; in Charge To (Category) field <br>3. Populate all fields<br>4. Click Save Draft<br>5. Click &quot;Submit&quot; button&quot; <br>6. Check &quot;Company Code&quot; of RS Number</td><td class="s5"></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TJDA02NlZPAOBw8hmG-3FbjHKqfGA51mOAqsMkI2JMc/edit?gid=**********#gid=**********"><br>1. Should display RS Creation Form<br>2. Charge To (Category) field is populated<br>3. All fields should be populated<br>4. Successfully Save as draft and Temporary RS Number should be displayed<br>5. Should follow the Company Code when creating a Requisition Slip and other Documents.<br>6. Should still displayed the correct Company Code of the RS number<br>                Format: RS-[CompanyCD][AA-ZZ][********-********]<br>                Sample: RS-01AA00000003<br><br>Please refer to:<br>DB STRUCTURE:<br>FINAL Cityland database file structure and sample dataset<br></a></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1465565150R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s11"></td><td class="s11">Update the behavior of Charge To, Company, and Project </td><td class="s11">Verify behavior of  Charge To (Client) field when Charge to (Category) selected is Company in creating RS</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account  as Admin</a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select &quot;Company&quot; in Charge To (Category) field <br>3. Select Company in Charge To  [Client] field<br>4. Check if Company field is still editable</td><td class="s12"></td><td class="s11"><br>1. Should display RS Creation Form<br>2. Company should be selected in Charge To (Category) field<br>3.  Selected Company in Charge To  (Client) field should automatically fill in the &quot;Company&quot; field below the &quot;Type of Request&quot; Field<br>4. Should still allow the User to update the autofilled Company Field</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1465565150R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s11"></td><td class="s11">Update the behavior of Charge To, Company, and Project </td><td class="s11">Verify behavior of  Charge To (Client) field when Charge to (Category) selected is Project in creating RS</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select &quot;Project&quot; in Charge To (Category) field <br>3. Select Project in Charge To  [Client] field<br>4. Check if Project and Company fields are still editable</td><td class="s12"></td><td class="s11"><br><br>1. Should display RS Creation Form<br>2. Project should be selected in Charge To (Category) field<br>3.  Selected Project in Charge To  (Client) field ishould automatically fill in the Project below and the linked Company to the Project<br>    a. Company Field below the Type of Request Field<br>4. Should still allow the User to update the autofilled Project and Company Fields</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1465565150R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s11"></td><td class="s11">Update the behavior of Charge To, Company, and Project </td><td class="s11">Verify behavior of  Charge To (Client) field when Charge to (Category) selected is Company in Editing of RS Draft</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click RS number with Draft Status<br>2. Update &quot;Company&quot; in Charge To (Category) field <br>3. Update Company in Charge To  [Client] field<br>4. Check if Company field is still editable<br>5. Click &quot;Save Draft&quot;</td><td class="s12"></td><td class="s11"><br>1. Should display the RS form with draft status<br>2. Company should be selected in Charge To (Category) field<br>3.  Selected Company in Charge To  (Client) field should automatically fill in the &quot;Company&quot; field below the &quot;Type of Request&quot; Field<br>4. Should still allow the User to update the autofilled Company Field<br>5. Should successfully save changes with correct comapny code of selected company in temporary RS Number</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1465565150R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s11"></td><td class="s11">Update the behavior of Charge To, Company, and Project </td><td class="s11">Verify behavior of  Charge To (Client) field when Charge to (Category) selected is Project in Editing of RS Draft</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click RS number with Draft Status<br>2. Update &quot;Project&quot; in Charge To (Category) field <br>3. Update Project in Charge To  [Client] field<br>4. Check if Project and Company fields are still editable<br>5. Click &quot;Save Draft&quot;</td><td class="s12"></td><td class="s11"><br>1. Should display the RS form with draft status<br>2. Project should be selected in Charge To (Category) field<br>3. Selected Project in Charge To  (Client) field ishould automatically fill in the Project below and the linked Company to the Project<br>    a. Company Field below the Type of Request Field<br>4. Should still allow the User to update the autofilled Project and Company Fields<br>5. Should successfully save changes with correct comapny code of selected company in temporary RS Number</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1465565150R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4"></td><td class="s4">Update the behavior of Charge To, Company, and Project </td><td class="s4">Verify Company Code in RS Number Should still base on the Company selected below the Type of Request Field when Draft</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account <br>4. RS Already Created with Draft status</a></td><td class="s4">1. On RS dashboard, click RS number with Draft Status<br>2. Check Company code on Temporary RS number<br><br></td><td class="s15"><div style="width:346px;height:19px;"><img src="https://lh7-rt.googleusercontent.com/sheetsz/AHOq17Flt_GsTo6NpgTRnCS6KAG8875yJFWrfDPU2nLKmvkyDz8w96imUw_G-Mm6mRAIqLMTgxMMdPQmOTocrxIqR6O7eHUl31CEAPj4d_Uz6PfIC6C8nsLOcZllHd3opHJIgoseikfCtq4P__1DexILU_E=w346-h19?key=NN19urqwamtM8J1xTnhjCeBh" style="width:inherit;height:inherit;object-fit:scale-down;object-position:left center;"/></div></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TJDA02NlZPAOBw8hmG-3FbjHKqfGA51mOAqsMkI2JMc/edit?gid=**********#gid=**********">1. Should display the RS form with draft status<br>2. Should still base the Company Code in the RS Number on the Company selected below the &quot;Type of Request&quot; Field<br>Format: RS-TMP-[CompanyCD][AA-ZZ][********-********]<br>Sample: RS-TMP-01AA00000003<br><br>DB STRUCTURE:<br>FINAL Cityland database file structure and sample dataset <br><br></a></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4"></td><td class="s4">Update the behavior of Charge To, Company, and Project </td><td class="s4">Verify Company Code in RS Number Should still base on the Company selected below the Type of Request Field when Submitted</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account <br>4. RS Already Created with Submitted status</a></td><td class="s4"><br>1. On RS dashboard, click RS number with Submitted Status<br>2. Check Company code on Submitted RS number<br></td><td class="s5"></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TJDA02NlZPAOBw8hmG-3FbjHKqfGA51mOAqsMkI2JMc/edit?gid=**********#gid=**********">1. Should display the RS form with submitted status<br>2. Should still base the Company Code in the RS Number on the Company selected below the &quot;Type of Request&quot; Field<br>Format: RS-[CompanyCD][AA-ZZ][********-********]<br>Sample: RS-01AA00000003<br><br>DB STRUCTURE:<br>FINAL Cityland database file structure and sample dataset</a></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 113px"><th id="1465565150R12" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">13</div></th><td class="s17"></td><td class="s17">Include Company Intials in the Drop-down Values for Company when creating a Requisition Slip</td><td class="s17">Verify Company Initials is displayed in Charge To (Client) when Charge to (Category) selected is Company in creating RS</td><td class="s18"></td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s17">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select &quot;Company&quot; in Charge To (Category) field <br>3. Check list of Companies in Charge To  [Client] field<br>4. Check list of Companies in Company field</td><td class="s18"></td><td class="s17"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br><br>1. Should display RS Creation Form<br>2. Company should be selected in Charge To (Category) field<br>3. Should include the Company Initials for the Charge To(Client) if Company Charge To is selected<br>4.  Should include the Company Initials for the Company Drop-down below the Type of Request Field<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    Format of Values: COMPINT + COMPNAME<br>    Sample: CDC - CITYLAND DEVELOPMENT CORPORATED</span></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s20"></td><td class="s20"></td></tr><tr style="height: 19px"><th id="1465565150R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s17"></td><td class="s17">Include Company Intials in the Drop-down Values for Company when creating a Requisition Slip</td><td class="s17">Verify Company Initials is displayed in Charge To (Client) when Charge to (Category) selected is Company in editing RS Draft</td><td class="s18"></td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s17">1. On RS dashboard, click RS number with Draft Status<br>2. Select &quot;Company&quot; in Charge To (Category) field <br>3. Check list of Companies in Charge To  [Client] field<br>4. Check list of Companies in Company field<br></td><td class="s20"></td><td class="s17"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display the RS form with draft status<br>2. Company should be selected in Charge To (Category) field<br>3. Should include the Company Initials for the Charge To(Client) if Company Charge To is selected<br>4.  Should include the Company Initials for the Company Drop-down below the Type of Request Field<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    Format of Values: COMPINT + COMPNAME<br>    Sample: CDC - CITYLAND DEVELOPMENT CORPORATED</span></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s20"></td><td class="s20"></td></tr><tr style="height: 19px"><th id="1465565150R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4"></td><td class="s4">Include Company Intials in the Drop-down Values for Company when creating a Requisition Slip</td><td class="s4">Verify Company Initials is displayed in Company field when viewing Submittd RS</td><td class="s5"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s4">1. On RS dashboard, click RS number with Submitted Status<br>2. Check Company field value displayed<br></td><td class="s16"></td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display the RS viewing form <br>2. Company Initials should include in the company field<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    Format of Values: COMPINT + COMPNAME<br>    Sample: CDC - CITYLAND DEVELOPMENT CORPORATED</span></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s11"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Charge To (Category) field if combobox is applied</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Charge To (Category) dropdown field <br>3. Check list of values displayed<br>4. Search a keyword in Charge To (Category) dropdown field <br>5. Search a keyword that is not existing on the list<br><br></td><td class="s12"></td><td class="s11">1. Should display RS Creation Form<br>2..Charge To (Category) field should implement a Combobox<br>3. Should sort the Values of the Charge To (Category) Drop-down Alphabetically [A-Z]<br>4. Should display lists of matched Data upon typing of Keyword<br>5. Should display No Data if no Value has matched the entered Keyword</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s11"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Charge To (Client)  field if combobox is applied</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select value on Charge To (Category) dropdown field <br>3. Click Charge To (Client) dropdown field <br>4. Check list of values displayed<br>5. Search a keyword in Charge To (Client) dropdown field <br>6. Search a keyword that is not existing on the list<br></td><td class="s12"></td><td class="s11">1. Should display RS Creation Form<br>2. Charge To (Category) dropdown field is populated<br>3..Charge To (Client) field should implement a Combobox<br>4. Should sort the Values of the Charge To (Client)  Drop-down Alphabetically [A-Z]<br>5.  Should display lists of matched Data upon typing of Keyword<br>6. Should display No Data if no Value has matched the entered Keyword<br></td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s11"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Company Field if combobox is applied</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Company dropdown field <br>3. Check list of values displayed<br>4. Search a keyword in Company dropdown field <br>5. Search a keyword that is not existing on the list<br><br></td><td class="s12"></td><td class="s11">1. Should display RS Creation Form<br>2..Company field should implement a Combobox<br>3. Should sort the Values of the Company Drop-down Alphabetically [A-Z]<br>4. Should display lists of matched Data upon typing of Keyword<br>5. Should display No Data if no Value has matched the entered Keyword</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s11"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Project Field if combobox is applied</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Project dropdown field <br>3. Check list of values displayed<br>4. Search a keyword in Project dropdown field <br>5. Search a keyword that is not existing on the list<br><br></td><td class="s12"></td><td class="s11">1. Should display RS Creation Form<br>2..Project field should implement a Combobox<br>3. Should sort the Values of the Project Drop-down Alphabetically [A-Z]<br>4. Should display lists of matched Data upon typing of Keyword<br>5. Should display No Data if no Value has matched the entered Keyword</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s11"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Department Field if combobox is applied</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Department dropdown field <br>3. Check list of values displayed<br>4. Search a keyword in Department dropdown field <br>5. Search a keyword that is not existing on the list<br><br></td><td class="s12"></td><td class="s11">1. Should display RS Creation Form<br>2..Department field should implement a Combobox<br>3. Should sort the Values of the Department Drop-down Alphabetically [A-Z]<br>4. Should display lists of matched Data upon typing of Keyword<br>5. Should display No Data if no Value has matched the entered Keyword</td><td class="s7"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Deliver To Field if combobox is applied</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Deliver To dropdown field <br>3. Check list of values displayed<br>4. Search a keyword in Deliver To dropdown field <br>5. Search a keyword that is not existing on the list<br><br></td><td class="s12"></td><td class="s11">1. Should display RS Creation Form<br>2..Deliver To field should implement a Combobox<br>3. Should sort the Values of the Deliver To Drop-down Alphabetically [A-Z]<br>4. Should display lists of matched Data upon typing of Keyword<br>5. Should display No Data if no Value has matched the entered Keyword</td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Charge To (Category) field if keyboard navigation keys are working as expected</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Charge To (Category) dropdown field <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click Charge To (Category) dropdown field <br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s12"></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display RS Creation Form<br>2..Charge To (Category) field should implement a Combobox<br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Charge To (Category) field should implement a Combobox<br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br></span></td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Charge To (Client) field if keyboard navigation keys are working as expected</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. On RS dashboard, click &quot;New Request&quot; <br>2. Select value on Charge To (Category) dropdown field <br>3. Click Charge To (Client) dropdown field <br>4. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>5. Click Enter Key in Keyboard<br>6. Click Charge To (Client) dropdown field <br>7. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>8. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 7:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s12"></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display RS Creation Form<br>2. Charge To (Category) dropdown field is populated<br>3..Charge To (Client) field should implement a Combobox<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>5. Should be able to select value in drop down field<br>6. Charge To (Client) field should implement a Combobox<br>7. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> <br>8. Should be able to select values in drop down field</span></td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Company Field if keyboard navigation keys are working as expected</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Company dropdown field <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click Company dropdown field <br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">  <br>7. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s12"></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display RS Creation Form<br>2..Company field should implement a Combobox<br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Company field should implement a Combobox<br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Should be able to select values in drop down field</span></td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Project Field if keyboard navigation keys are working as expected</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Project dropdown field <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click Project dropdown field <br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s12"></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display RS Creation Form<br>2..Project field should implement a Combobox<br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Project field should implement a Combobox<br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> on both when Searching or Not</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Should be able to select values in drop down field</span></td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Department Field if keyboard navigation keys are working as expected</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Department dropdown field <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click Department dropdown field <br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard  </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">  <br>7. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s12"></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display RS Creation Form<br>2..Department field should implement a Combobox<br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Department field should implement a Combobox<br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard  </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Should be able to select values in drop down field</span></td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s14"></td><td class="s11">Update the functionality of the Drop-down Fields in Requisition Slip Creation</td><td class="s11">Verify Deliver To Field if keyboard navigation keys are working as expected</td><td class="s12"></td><td class="s13"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account </a></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. On RS dashboard, click &quot;New Request&quot; <br>2. Click Deliver To dropdown field <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> <br>4. Click Enter Key in Keyboard<br>5. Click Deliver To dropdown field <br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s12"></td><td class="s11"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display RS Creation Form<br>2..Deliver To field should implement a Combobox<br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Deliver To field should implement a Combobox<br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">  <br>7. Should be able to select values in drop down field</span></td><td class="s14"></td><td class="s14"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="1465565150R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s16"></td><td class="s4">Updating of Draft Reference Number when updating the Company in the Requisition Slip</td><td class="s4">Verify Company code of RS number is updated when edit Company field during Save Draft</td><td class="s21"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as Admin (RS Requestor)<br>4. Should have a Requisition Slip created by the Admin User and with Draft Status</a></td><td class="s4"><br>1. On RS Dashboard, Click RS number link with Draft Status<br>2. Update Company in Company dropdown Field <br>3. Click &quot;Save Draft&quot; button<br>4. Check Temporary RS Number</td><td class="s22"></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TJDA02NlZPAOBw8hmG-3FbjHKqfGA51mOAqsMkI2JMc/edit?gid=**********#gid=**********">1. Requestor of the Requisition Slip should be allowed to edit the Draft<br>2. Company field should be updated<br>3. Should successfully saved the changes of Draft RS <br>4. Should update the Company Code used in the RS Number to the Company Code of newly selected Company in the dropdown selection<br><br>Format: RS-TMP-[CompanyCD][AA-ZZ][********-********]<br>Sample: RS-TMP-01AA00000003<br><br>DB STRUCTURE:<br>FINAL Cityland database file structure and sample dataset</a></td><td class="s16"></td><td class="s16"></td><td class="s23">Failed</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1110">CITYLANDPRS-1110</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s16"></td><td class="s4">Updating of Draft Reference Number when updating the Company in the Requisition Slip</td><td class="s4">Verify Company code of RS number is updated when edit Company field during Submission</td><td class="s25"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as Admin (RS Requestor)<br>4. Should have a Requisition Slip created by the Admin User and with Draft Status</a></td><td class="s4"><br>1. On RS Dashboard, Click RS number link with Draft Status<br>2. Update Company in Company dropdown Field <br>3. Click &quot;Save Draft&quot; button<br>4. Click &quot;Submit&quot; button<br>5. Check Submitted RS Number</td><td class="s25"></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TJDA02NlZPAOBw8hmG-3FbjHKqfGA51mOAqsMkI2JMc/edit?gid=**********#gid=**********">1. Requestor of the Requisition Slip should be allowed to edit the Draft<br>2. Company field should be updated<br>3. Should successfully saved the changes of Draft RS <br>4. Should successfully submitted the RS<br>5. Should update the Company Code used in the RS Number to the Company Code of newly selected Company in the dropdown selection<br><br>Format: RS-[CompanyCD][AA-ZZ][********-********]<br>Sample: RS-01AA00000003<br><br>DB STRUCTURE:<br>FINAL Cityland database file structure and sample dataset</a></td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s16"></td><td class="s4">Updating of Draft Requisition Slip should only be done by the Requestor</td><td class="s4">Verify if the Requestor is able to update the created RS with Draft Status</td><td class="s25"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as Admin (RS Requestor)<br>4. Should have a Requisition Slip created by the Admin User and with Draft Status</a></td><td class="s4"><br>1. On RS Dashboard, Click RS number link with Draft Status<br>2. Update fields<br>3. Click &quot;Save Draft&quot; button</td><td class="s25"></td><td class="s4">1. Requestor of the Requisition Slip should be allowed to edit the Draft<br>2. Field should be updated<br>3. Should successfully saved the changes of Draft RS </td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1110">CITYLANDPRS-1110</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s16"></td><td class="s4" rowspan="9">Updating of Draft Requisition Slip should only be done by the Requestor</td><td class="s4" rowspan="9">Verify  if the Other user types aside from requestor are able to update RS with Draft Status</td><td class="s25"></td><td class="s6" rowspan="9"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as the following User types:<br>     a. Engineers<br>     b. Supervisor<br>     c. Assistant Manager<br>     d. Department Head<br>     e. Department Secretary<br>     f. Division Head<br>     g. Area Staff<br>     h. Purchasing Staff<br>     i. Purchasing Head<br>     j. Management<br>4. Should have a Requisition Slip created by the Admin User and with Draft Status </a></td><td class="s4">1. Logged as Engineer<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Engineer should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s23">Failed</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Supervisor<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Supervisor should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Assistant Manager<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Assistant Manager should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Department Head<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Department Head should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Department Secretary<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Department Secretary should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Division Head<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Division Head should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Area Staff<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Area Staff should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Purchasing Staff<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Purchasing Staff should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Purchasing Head<br>2. On RS Dashboard, Click RS number link with Draft Status</td><td class="s25"></td><td class="s4">1. Purchasing Head should only View the RS and not be allowed to edit the Draft RS</td><td class="s16"></td><td class="s16"></td><td class="s26">Blocked</td><td class="s24"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1112">CITYLANDPRS-1112</a></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s16"></td><td class="s4">Updating of Draft Requisition Slip should only be done by the Requestor</td><td class="s4">Verify  if the Requestor  is able to update the created RS with Submitted Status</td><td class="s25"></td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as Admin (RS Requestor)<br>4. Should have a Requisition Slip created by the Admin User and with Submited Status</a></td><td class="s4"><br>1. On RS Dashboard, Click RS number link with Submitted Status</td><td class="s25"></td><td class="s4">1. Requestor of the Requisition Slip should only be able to view the Requisition Slip</td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s16"></td><td class="s4" rowspan="9">Updating of Draft Requisition Slip should only be done by the Requestor</td><td class="s4" rowspan="9">Verify  if the Other user types aside from requestor are able to update RS with Submitted Status</td><td class="s25"></td><td class="s6" rowspan="9"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as the following User types:<br>     a. Engineers<br>     b. Supervisor<br>     c. Assistant Manager<br>     d. Department Head<br>     e. Department Secretary<br>     f. Division Head<br>     g. Area Staff<br>     h. Purchasing Staff<br>     i. Purchasing Head<br>     j. Management<br>4. Should have a Requisition Slip created by the Admin User and with Submitted Status </a></td><td class="s4">1. Logged as Engineer<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Engineer should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Supervisor<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Supervisor should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Assistant Manager<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Assistant Manager should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Department Head<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Department Head should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Department Secretary<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Department Secretary should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Division Head<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Division Head should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Area Staff<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Area Staff should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Purchasing Staff<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Purchasing Staff should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="1465565150R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s16"></td><td class="s25"></td><td class="s4">1. Logged as Purchasing Head<br>2. On RS Dashboard, Click RS number link with Submiited Status</td><td class="s25"></td><td class="s4">1. Purchasing Head should only be able to view the Requisition Slip </td><td class="s16"></td><td class="s16"></td><td class="s8">Passed</td><td class="s4"></td><td class="s9">Not Started</td><td class="s4"></td><td class="s16"></td><td class="s16"></td></tr></tbody></table></div>