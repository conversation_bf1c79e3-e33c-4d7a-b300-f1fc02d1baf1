"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface FieldWrapperProps {
  label?: string | React.ReactNode;
  className?: string;
  children: React.ReactNode;
  error?: string;
  description?: string;
  columnSpan?: number;
  required?: boolean;
}

export const FieldWrapper = ({
  label,
  className,
  error,
  children,
  description,
  columnSpan = 1,
  required = false,
}: FieldWrapperProps) => {
  const columnClasses = {
    1: 'col-span-1',
    2: 'col-span-2',
    3: 'col-span-3',
    4: 'col-span-4',
    5: 'col-span-5',
    6: 'col-span-6',
    7: 'col-span-7',
    8: 'col-span-8',
    9: 'col-span-9',
    10: 'col-span-10',
    11: 'col-span-11',
    12: 'col-span-12',
  };

  return (
    <div className={cn('mb-4', columnClasses[columnSpan as keyof typeof columnClasses], className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {children}
      {description && <p className="mt-1 text-sm text-gray-500">{description}</p>}
      {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
    </div>
  );
};
