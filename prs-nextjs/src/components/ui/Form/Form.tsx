"use client";

import { zodResolver } from '@hookform/resolvers/zod';
import React, {
  useEffect,
  createContext,
  useContext,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react';
import {
  Controller,
  FormProvider,
  useForm,
  useFormContext,
  type ControllerProps,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form';
import { cn } from '@/lib/utils';
import { z } from 'zod';

const FormFieldContext = createContext<{ name: string }>({ name: '' });

const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name as string }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};

const useFormField = () => {
  const fieldContext = useContext(FormFieldContext);
  const itemContext = useContext(FormItemContext);
  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error('useFormField should be used within <FormField>');
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

type FormItemContextValue = {
  id: string;
};

const FormItemContext = createContext<FormItemContextValue>({
  id: '',
});

const FormItem = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const id = React.useId();

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn('space-y-2', className)} {...props} />
    </FormItemContext.Provider>
  );
});
FormItem.displayName = 'FormItem';

const FormLabel = forwardRef<
  HTMLLabelElement,
  React.LabelHTMLAttributes<HTMLLabelElement>
>(({ className, ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <label
      ref={ref}
      className={cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        error && 'text-red-500',
        className,
      )}
      htmlFor={formItemId}
      {...props}
    />
  );
});
FormLabel.displayName = 'FormLabel';

const FormDescription = forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn('text-sm text-gray-500', className)}
      {...props}
    />
  );
});
FormDescription.displayName = 'FormDescription';

const FormMessage = forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField();
  const body = error ? String(error?.message) : children;

  if (!body) {
    return null;
  }

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn('text-sm font-medium text-red-500', className)}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessage.displayName = 'FormMessage';

interface FormProps<TFormValues extends FieldValues> {
  onSubmit: (values: TFormValues) => void;
  children: React.ReactNode;
  className?: string;
  options?: Parameters<typeof useForm>[0];
  id?: string;
  schema?: z.ZodSchema<any>;
  hasErrorSpace?: boolean;
  resetOnSubmit?: boolean;
  watchFields?: string[];
  onChange?: (values: any) => void;
  removeNonDirtyFields?: boolean;
  includeDefaultValuesOnSubmit?: boolean;
}

const Form = forwardRef<
  { requestSubmit: (props?: any) => void; resetForm: () => void; ref: HTMLFormElement | null },
  FormProps<any>
>(
  (
    {
      onSubmit,
      children,
      className,
      options,
      id,
      schema,
      hasErrorSpace = true,
      resetOnSubmit = false,
      watchFields = [],
      onChange,
      removeNonDirtyFields,
      includeDefaultValuesOnSubmit = false,
    },
    ref,
  ) => {
    const form = useForm({
      ...options,
      resolver: schema ? zodResolver(schema) : undefined,
    });

    useEffect(() => {
      if (onChange) {
        const subscription = form.watch((value, { name, type }) => {
          if (type === 'change') {
            onChange(value);
          }
        });

        return () => subscription.unsubscribe();
      }
    }, [form, onChange]);

    const formRef = useRef<HTMLFormElement>(null);
    const submitPropsRef = useRef<any>();

    useImperativeHandle(ref, () => ({
      requestSubmit: (props = {}) => {
        submitPropsRef.current = props;
        formRef.current?.requestSubmit();
      },
      resetForm: form.reset,
      ref: formRef?.current,
    }));

    const handleSubmit = form.handleSubmit(async (values) => {
      let submitValues = { ...values };

      if (removeNonDirtyFields) {
        const dirtyFields = form.formState.dirtyFields;
        submitValues = Object.keys(dirtyFields).reduce((acc, key) => {
          acc[key] = values[key];
          return acc;
        }, {} as any);
      }

      if (includeDefaultValuesOnSubmit) {
        submitValues = {
          ...form.formState.defaultValues,
          ...submitValues,
        };
      }

      await onSubmit({ ...submitValues, ...submitPropsRef.current });

      if (resetOnSubmit) {
        form.reset();
      }
    });

    // Calculate disabled state based on watched fields
    const toWatchFields = form.watch(
      watchFields.filter(item => typeof item === 'string'),
    );

    const disabled = toWatchFields.some(item => {
      if (Array.isArray(item)) {
        return item.length === 0 || item.some(i => !i);
      }

      if (typeof item === 'undefined') {
        return true;
      }

      return !item;
    });

    // Get error messages
    const serverError = form.formState.errors?.root?.serverError?.message;
    const inputError = Object.values(form.formState.errors).find(
      error => error?.message,
    )?.message;

    const errorMessage = serverError || inputError;

    return (
      <FormProvider {...form}>
        <form
          ref={formRef}
          id={id}
          onSubmit={handleSubmit}
          className={className}
          noValidate
        >
          {hasErrorSpace ? (
            <div className="my-2">
              {errorMessage ? (
                <div className="flex justify-between items-center text-sm bg-red-100 border border-red-400 text-red-600 px-4 py-1 rounded-md">
                  <span className="flex-1 text-center">{errorMessage}</span>
                  <button
                    type="button"
                    className="cursor-pointer"
                    onClick={() => form.clearErrors()}
                  >
                    <svg
                      className="h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              ) : (
                <span className="invisible">&nbsp;</span>
              )}
            </div>
          ) : null}
          {typeof children === 'function' ? children({ ...form, disabled }) : children}
        </form>
      </FormProvider>
    );
  },
);

Form.displayName = 'Form';

export {
  useFormField,
  Form,
  FormProvider,
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
  FormField,
};
