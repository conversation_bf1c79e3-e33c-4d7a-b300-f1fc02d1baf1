<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s15{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="859059453C0" style="width:71px;" class="column-headers-background">A</th><th id="859059453C1" style="width:179px;" class="column-headers-background">B</th><th id="859059453C2" style="width:74px;" class="column-headers-background">C</th><th id="859059453C3" style="width:252px;" class="column-headers-background">D</th><th id="859059453C4" style="width:233px;" class="column-headers-background">E</th><th id="859059453C5" style="width:369px;" class="column-headers-background">F</th><th id="859059453C6" style="width:101px;" class="column-headers-background">G</th><th id="859059453C7" style="width:366px;" class="column-headers-background">H</th><th id="859059453C8" style="width:122px;" class="column-headers-background">I</th><th id="859059453C9" style="width:173px;" class="column-headers-background">J</th><th id="859059453C10" style="width:245px;" class="column-headers-background">K</th><th id="859059453C11" style="width:133px;" class="column-headers-background">L</th><th id="859059453C12" style="width:133px;" class="column-headers-background">M</th><th id="859059453C13" style="width:133px;" class="column-headers-background">N</th><th id="859059453C14" style="width:133px;" class="column-headers-background">O</th><th id="859059453C15" style="width:133px;" class="column-headers-background">P</th><th id="859059453C16" style="width:133px;" class="column-headers-background">Q</th><th id="859059453C17" style="width:133px;" class="column-headers-background">R</th><th id="859059453C18" style="width:133px;" class="column-headers-background">S</th><th id="859059453C19" style="width:133px;" class="column-headers-background">T</th><th id="859059453C20" style="width:133px;" class="column-headers-background">U</th><th id="859059453C21" style="width:133px;" class="column-headers-background">V</th></tr></thead><tbody><tr style="height: 42px"><th id="859059453R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Parameters</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="859059453R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="12">Update Non-RS Form</td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="859059453R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">1306-001</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Non-RS Form Page</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Go to Dashboard<br>2. Click Create New Non-RS button<br>3. Validate if page loads</td><td class="s8"></td><td class="s5">3. Page should load without any errors</td><td class="s9" rowspan="64"><a target="_blank" href="https://docs.google.com/spreadsheets/d/18RFWRs6Cig6nDW1IBOGHW_IMyYinVOF2prAmAzcOw3E/edit?gid=1821272722#gid=1821272722">Ghienel_Sprint1_Test Result</a></td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">1306-002</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Section Sequence</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate sections displayed</td><td class="s8"></td><td class="s5">2. Section sequence should be displayed as:<br>    a. Non-RS Details<br>    b. Charge To<br>    c. Items Table<br>    d. Attachment and Notes Section</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">1306-003</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Category field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Category field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should be a Drop-down Field with the following Options<br>           i) Company<br>           ii) Association<br>           iii) Project<br>    b. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">1306-004</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Company field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Company field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. List of Companies Synced in Company Management if Company is selected in the Category<br>    b. List of Associations created in Company Management if Association is selected in the Category<br>    c. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s14"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="859059453R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">1306-005</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Project field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Project field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. List of Projects Synced in Project Management<br>          i) Should be related to the Company selected in the Company Field<br>             a) If selected before the Company Field, should autofill the Company Field once the Project has been selected<br>    b. Field is Required if the selected Category is Project</td><td class="s10">Passed</td><td class="s5"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1484">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1484</a></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">1306-006</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Department field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Department field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. List of Departments Synced in Department Management<br>          i) Should be autofilled by the User&#39;s Department<br>          ii) Should allow the User to change the select Department<br>    b. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">1306-007</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Supplier field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Supplier field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. List of Active Suppliers from Supplier Management<br>    b. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5">1306-008</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Payable To field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Payable To field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should be Alphanumeric and Special Characters except Emojis<br>    b. Should allow maximum of 100 Characters<br>    c. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5">1306-009</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Invoice No field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Invoice No field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should be Alphanumeric and Special Characters except Emojis<br>    b. Should allow maximum of 100 Characters<br>    c. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1486">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1486</a></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 67px"><th id="859059453R11" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">12</div></th><td class="s5">1306-010</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Supplier Invoice Date field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Supplier Invoice Date  field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should be a Date Picker that will allow Dates for the Current and Previous Dates<br>    b. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">1306-011</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Supplier Invoice Amount field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Supplier Invoice Amount field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should have a Peso Sign Placeholder<br>    b. Should allow numeric values of 2-Decimal Places<br>    c. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5">1306-012</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Group Discount field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Group Discount field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should have an Option if it is a Fixed Amount or Percentage<br>    b. Should deduct to the Total of the Non-RS Payment Form<br>    c. Field is Optional</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5">1306-013</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Invoice Attachment field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Invoice Attachment field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should allow to upload a maximum of 25MB per File<br>    b. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV<br>    c. Field is Required</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">1306-014</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Invoice Note field in Non-RS Details Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Notes field displayed in the Non-RS Details Section</td><td class="s8"></td><td class="s5">2. a. Should allow Alphanumeric Characters and Special Characters except Emojis<br>    b. Should have a Maximum of 100 Characters<br>    c. Field is Optional</td><td class="s10">Passed</td><td class="s5"></td><td class="s12"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="859059453R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">1306-015</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Charge To Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate fields displayed in the Charge To Section</td><td class="s8"></td><td class="s5">2. Should display the following:<br>    a. Should set the Charge To Fields as Optional<br>    b. Should allow the User to select a different Charge To depending on the situation<br>    c. Should update the behavior of the Charge To(Category)<br>       i. If Charge To(Category) is Company<br>          i) Should get the selected Company from Request Details<br>       ii. If Charge To(Category) is Association<br>          i) Should get the selected Company-Association from Request Details<br>       iii. If Charge To(Category) is Project<br>          i) Should get the selected Project from Request Details<br>       iv. If Charge To(Category) is Supplier<br>          i) Should require the Requester to select a specific Supplier<br>    d. Should retain the filtering of Options of the Charge To based on the Charge To(Category)<br>    e. Should rename the Charge To(Client) to Charge To</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s5">1306-016</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Search field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Search field displayed in the Item/s Section</td><td class="s8"></td><td class="s5">2. a. Allow searching for added Item Name in the Items Table<br>    b. Allow searching for a Keyword<br>    c. Triggered by clicking Search Button<br>    d. Should clear the Search Field and Filtering of Table by clicking Clear Button</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s5">1306-017</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Item field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Item field displayed in the Item/s Section</td><td class="s8"></td><td class="s5">2. a. Should be Alphanumeric and Special Character except Emojis<br>    b. Maximum of 100 Characters</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s5">1306-018</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Unit field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Unit field displayed in the Item/s Section</td><td class="s8"></td><td class="s5">2. a. Should display the default Options for the Unit<br>               a) Should allow Searching for the Options<br>               b) pc<br>               c) lot<br>               d) pack<br>               e) unit<br>               f) set<br>               g) m<br>               h) gal<br>               i) liter<br>               j) bundle<br>               k) kilo<br>               l) yard<br>               m) ream<br>               n) box<br>               o) bottle<br>               p) pair<br>               q) roll<br>               r) dozen<br>               s) can<br>               t) unit<br>               u) tin</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s5">1306-019</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Qty field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Qty field displayed in the Item/s Section</td><td class="s8"></td><td class="s5">2. a. Numbers Only<br>    b. Maximum of 5 Characters</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">1306-020</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Amount field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Amount field displayed in the Item/s Section</td><td class="s8"></td><td class="s5">2. a. Numbers only with 2 Decimal Places<br>    b. Maximum of 10 Characters</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">1306-021</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Discount field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Discount field displayed in the Item/s Section</td><td class="s8"></td><td class="s5">2. a. Can only choose between fixed or percentage button<br>    b. Text field beside button</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">1306-022</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Add Item Button in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Populate fields<br>3. Click Add Item button</td><td class="s8"></td><td class="s5">3. Should add item upon clicking</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">1306-023</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Item field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Item field displayed in the Item Table Section</td><td class="s8"></td><td class="s5">2. a. Should display Item name<br>    b. Should sort the &quot;Items&quot; to - A-Z, Z-A when sorting is clicked</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">1306-024</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Unit field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Unit field displayed in the Item Table Section</td><td class="s8"></td><td class="s5">2. Should display chosen unit in Item/s section</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">1306-025</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Qty field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Qty field displayed in the Item Table Section</td><td class="s8"></td><td class="s5">2. a. Should display the correct quantity of the item<br>    b. Should sort in ascending or descending order based on Quantity</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">1306-026</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Amount field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Amountfield displayed in the Item Table Section</td><td class="s8"></td><td class="s5">2. a. Should display the correct price of the item<br>    b. Should sort in ascending or descending order based on Amount</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s5">1306-027</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Discount field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Discount field displayed in the Item Table Section</td><td class="s8"></td><td class="s5">2. a. Should display the correct discount of the item<br>    b. Should sort in ascending or descending order based on Discount</td><td class="s10">Passed</td><td class="s5">CITYLANDPRS-1508</td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s5">1306-028</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Discounted Price field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Discounted Price field displayed in the Item Table Section</td><td class="s8"></td><td class="s5">2. a. Should display the correct discounted price of the item computed depending on the Amount and the entered Discount<br>    b. Should sort in ascending or descending order based on Price</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5">1306-029</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Actions field in Item Table Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. An item has been added</td><td class="s5">1. Navigate to Item Table<br>2. Click trash button</td><td class="s8"></td><td class="s5">2. Should be able to remove item upon clicking button</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5">1306-030</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Item Table Pagination</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form<br>4. More than 10 items have been added</td><td class="s5">1. Navigate to Item Table<br>2. Validate Item Table Section<br>3. Click Page numbers</td><td class="s8"></td><td class="s5">2. Max number of items display per page is 10<br>3. a. Should be able to click page numbers<br>    b. Displays different items per different page numbers</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s5">1306-031</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Additional Attachment field in Attachment and Notes Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Additional Attachment field displayed in the Attachment and Notes Section</td><td class="s8"></td><td class="s5">2. a. Should allow to upload a maximum of 25MB per File<br>    b. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV<br>    c. Field is Optional</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s5">1306-032</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Additional Notes field in Attachment and Notes Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Validate Additional Notes field displayed in the Attachment and Notes Section</td><td class="s8"></td><td class="s5">2. a. Should allow Alphanumeric Characters and Special Characters except Emojis<br>    b. Should have a Maximum of 100 Characters<br>    c. Field is Optional</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s5">1306-033</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Non-RS Payment Request Computation</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Populate fields<br>3. Add items (more than 1 item) to Item Table<br>4. Add Group Discount<br>5. Validate Computed Amount<br>6. Click Submit</td><td class="s8"></td><td class="s5">5. Should update the Non-RS Payment Request Computation<br>    a. Amount (+Other Charges) - Sum of the (Price per Unit x Quantity)<br>    b. Discounts - Sum of all of the Discounts and the Group Discount if available<br>    c. Total Amount - Amount (+Other Charge) - Discounts(Item and Group Discount)<br><br>6. Should validate if the Total Amount Computation is equal with the Supplier Invoice Amount before Submission of the Non-RS, may it be Draft or not<br>     a. If not equal, should not allow the User to proceed and have them prompted of the discrepancy</td><td class="s10">Passed</td><td class="s5">CITYLANDPRS-1490</td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s5">1306-034</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Invoice No Accepts Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Invoice No<br>2. Enter emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Emojis should not be allowed as input<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s5">1306-035</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify Invoice No maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Invoice No<br>2. Enter more than 100 characters into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow maximum of 100 characters<br>3. Should not be allowed to submit form</td><td class="s17">Failed</td><td class="s5">CITYLANDPRS-1686</td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s5">1306-036</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Payable To Accepts Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Payable To<br>2. Enter emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Emojis should not be allowed as input<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s18"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td></tr><tr style="height: 19px"><th id="859059453R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s5">1306-037</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify Payable To maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Payable To<br>2. Enter more than 100 characters into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow maximum of 100 characters<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s5">1306-038</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify if Future Dates can be Selected</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Supplier Invoice Date<br>2. Click on Date Picker<br>3. Attempt to select date later than current date</td><td class="s8"></td><td class="s5">2. Date Picker should display<br>3. Should be a Date Picker that will allow Dates for the Current and Previous Dates</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s5">1306-039</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify if Supplier Invoice Amount Accepts Letters and Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Supplier Invoice Amount<br>2. Enter letters and emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow numbers<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s5">1306-040</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify Decimals in Supplier Invoice Amount</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Supplier Invoice Amount<br>2. Enter more than 2 decimal places<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow 2 decimal places<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s5">1306-041</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify if Group Discount Field in Item/s Section Accepts Letters and Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Group Discount<br>2. Enter letters and emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow numbers<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s5">1306-042</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify Decimals in Group Discount field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Group Discount<br>2. Select Fixed Discount<br>3. Enter more than 2 decimal places<br>4. Attempt to submit the form</td><td class="s8"></td><td class="s5">3. Should only allow 2 decimal places<br>4. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s5">1306-043</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Invoice Attachment field Accepts more than 25MB per file</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Invoice Attachment<br>2. Click Select Attachments<br>3. Select a file larger than 25MB</td><td class="s8"></td><td class="s5">3. Should allow to upload a maximum of 25MB per File</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s5">1306-044</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Invoice Attachment field Accepts Other File Formats</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Invoice Attachment<br>2. Click Select Attachments<br>3. Select a file that is not a PNG, JPG, JPEG, PDF, Excel, CSV</td><td class="s8"></td><td class="s5">3. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s5">1306-045</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Invoice Note Accepts Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Invoice Note<br>2. Enter emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Emojis should not be allowed as input<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s5">1306-046</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify Invoice Note maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Invoice Note<br>2. Enter more than 100 characters into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow maximum of 100 characters<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s5">1306-047</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Item field in Item/s Section Accepts Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Item<br>2. Enter emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Emojis should not be allowed as input<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s5">1306-048</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify Item field in Item/s Section maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Item<br>2. Enter more than 100 characters into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow maximum of 100 characters<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s5">1306-049</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify if Qty Field in Item/s Section Accepts Letters and Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Qty<br>2. Enter letters and emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow numbers<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s5">1306-050</td><td class="s6">Update Non-RS Form</td><td class="s7">MInor</td><td class="s16">Verify Qty field in Item/s Section maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Qty<br>2. Enter more than 5 characters into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow maximum of 5 characters<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s5">1306-051</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify if Amount Field in Item/s Section Accepts Letters and Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Amount<br>2. Enter letters and emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow numbers<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s5">1306-052</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify Amount field in Item/s Section maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Amount<br>2. Enter more than 10 characters into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow maximum of 10 characters<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s5">1306-053</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify Decimals in Amount field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Amount<br>2. Enter more than 2 decimal places<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow 2 decimal places<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s5">1306-054</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify if Discount Field in Item/s Section Accepts Letters and Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Discount<br>2. Enter letters and emojis into the field<br>3. Attempt to submit the form</td><td class="s8"></td><td class="s5">2. Should only allow numbers<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s5">1306-055</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s16">Verify Decimals in Discount field in Item/s Section</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Item/s Section &gt; Discount<br>2. Select Fixed Discount<br>3. Enter more than 2 decimal places<br>4. Attempt to submit the form</td><td class="s8"></td><td class="s5">3. Should only allow 2 decimal places<br>4. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s5">1306-056</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Additional Attachments field Accepts more than 25MB per file</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Attachment and Notes Section &gt; Additional Attachments<br>2. Click Select Attachments<br>3. Select a file larger than 25MB</td><td class="s18"></td><td class="s5">3. Should allow to upload a maximum of 25MB per File</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s5">1306-057</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Additional Attachments field Accepts Other File Formats</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Attachment and Notes Section &gt; Additional Attachments<br>2. Click Select Attachments<br>3. Select a file that is not a PNG, JPG, JPEG, PDF, Excel, CSV</td><td class="s18"></td><td class="s5">3. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s5">1306-058</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify if Additional Notes Accepts Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Attachment and Notes Section &gt; Additional Notes<br>2. Enter emojis into the field<br>3. Attempt to submit the form</td><td class="s18"></td><td class="s5">2. Emojis should not be allowed as input<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s5">1306-059</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s16">Verify Additional Notes maximum characters</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Attachment and Notes Section &gt; Additional Notes<br>2. Enter more than 100 characters into the field<br>3. Attempt to submit the form</td><td class="s18"></td><td class="s5">2. Should only allow maximum of 100 characters<br>3. Should not be allowed to submit form</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s5">1306-060</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Adding of New Unit</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Item/s Section &gt; Unit<br>2. Enter unit not included in options<br>3. Verify if unit is added to options</td><td class="s8"></td><td class="s5">2. Should be allowed to add new unit<br>3. Unit should be added to options</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s5">1306-061</td><td class="s6">Update Non-RS Form</td><td class="s7">Minor</td><td class="s5">Verify if Unit field Accepts Numbers, Special Characters, and Emojis</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Navigate to Non-RS Details &gt; Item/s Section &gt; Unit<br>2. Enter a number<br>3. Attempt to add the item<br>4. Enter a special character<br>5. Attempt to add the item<br>6. Enter a emoji<br>7. Attempt to add the item</td><td class="s8"></td><td class="s5">3. Should only accept letters</td><td class="s17">Failed</td><td class="s5"></td><td class="s5">CITYLANDPRS-1505</td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s5">1306-062</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify if Charge To is Optional</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS Details button<br>2. Populate all fields but leave Charge To blank<br>3. Click Submit button</td><td class="s8"></td><td class="s5">3. Should be submitted successfully with empty Charge To field</td><td class="s10">Passed</td><td class="s5"></td><td class="s5"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s5">1306-063</td><td class="s6">Update Non-RS Form</td><td class="s7">Critical</td><td class="s5">Verify Non-RS Submission when Supplier Invoice Amount and Total are not Equal</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department</td><td class="s5">1. Click New Non-RS button<br>2. Populate fields<br>3. Add items to Item Table to make up a higher total than Supplier Invoice Amount <br>4. Click Submit</td><td class="s8"></td><td class="s5">4. If not equal, should not allow the User to proceed and have them prompted of the discrepancy</td><td class="s10">Passed</td><td class="s5"></td><td class="s20">CITYLANDPRS-1490</td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s5">1306-064</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s5">Verify if Attachment and Notes section is Optional</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Populate all required fields<br>2. Leave attachments and notes section blank<br>3. Attempt to submit</td><td class="s8"></td><td class="s5">3. Should be able to submit successfully</td><td class="s10">Passed</td><td class="s5"></td><td class="s20">CITYLANDPRS-1488</td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s5">1306-065</td><td class="s6">Update Non-RS Form</td><td class="s7">High</td><td class="s5">Verify  text label of Attachment and Notes section i</td><td class="s5">1. User is logged in as any user except Root User<br>2. Should have set-up the Company, Association, Project, and Department<br>3. Must be in Non-RS Form</td><td class="s5">1. Populate all required fields<br>2. Check text label of attachment and notes</td><td class="s8"></td><td class="s5">3. Should display as &quot;Additional Attachment&quot; and Additional Notes&quot;</td><td class="s21 softmerge"><div class="softmerge-inner" style="width:119px;left:-1px"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1FyKHlVSw7jtjfYy2psQXvx-22pFuGEgmrTH_5dsJLQ4/edit?gid=0#gid=0">Cherry Sprint 2 Test Results</a></div></td><td class="s10">Passed</td><td class="s5"></td><td class="s20">CITYLANDPRS-1488</td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="859059453R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s3" colspan="12">Update Adding of Approver behavior for Non-RS Approval</td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="859059453R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s22">PRS-1309-001</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify clicking through Dashboard page to access the Non-RS Payment Request.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.</td><td class="s22">1. Navigate to Dashboard.<br>2. Click on Non-RS</td><td class="s23"></td><td class="s22">2. Non-RS Payment Request page is displayed.</td><td class="s24" rowspan="16"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1GxZe1_U3hJmTO1fPOpylkD2H0kny6HwXi2md_8LBNwM/edit?gid=0#gid=0">Test results</a></td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s22">PRS-1309-002</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify display of Non-Rs Details, Items, and Approvers section.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.</td><td class="s22">1. Check the layout of the page.<br>2. Navigate to Approvers section.</td><td class="s23"></td><td class="s22">2. Section for Non-RS details, items, and approvers are displayed.</td><td class="s10">Passed</td><td class="s22"></td><td class="s26"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td><td class="s27"></td></tr><tr style="height: 19px"><th id="859059453R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s22">PRS-1309-003</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify visibility and Functionality of Add button in the Approvers section.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Approvers section is visible.</td><td class="s22">1. Locate and click Add button.</td><td class="s23"></td><td class="s22">1. Add Approver modal is displayed.</td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R76" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">77</div></th><td class="s22">PRS-1309-004</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify Modal displays Search User Field and valid User Types.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Add Approver Modal is displayed.</td><td class="s22">1. Locate the Search User field.<br>2. Fill in the field.<br>3. Observe the search results.</td><td class="s23"></td><td class="s22">3. Only user with these following roles are shown:<br>     - Supervisor<br>     - Assistant Manager<br>     - Department Head<br>     - Division Head<br>     -  Area Staff/Department Secretary</td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R77" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">78</div></th><td class="s22">PRS-1309-005</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">Minor</td><td class="s22">Verify Additional Approvers appears below current Approver with asterist (*).</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Approver is successfully added.</td><td class="s22">1. Observe the Approver list.<br>2. Confirm new entry placement and asterisk label.</td><td class="s23"></td><td class="s22">2. New Approver is listed below current Approver with a &quot;*&quot; label.</td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R78" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">79</div></th><td class="s22">PRS-1309-006</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">Critical</td><td class="s22">Verify Additional Approver is reflected in the list of Approvers immediately.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Approver was just added.</td><td class="s22">1. Check the Approver list after adding.</td><td class="s23"></td><td class="s22">1. Added Approver appears immediately.</td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s22">PRS-1309-007</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">Critical</td><td class="s22">Verify if adding additional Approver after deletion is allowed.</td><td class="s22">1. User is on Non-RS page.<br>2. Logged in as current Approver.<br>3. An existing Additonal Approver has been removed.</td><td class="s22">1. Click Add approver.<br>2. Add a new user as an approver.</td><td class="s28"></td><td class="s22">2. New Additional Approver is successfully added and displayed on the Approvers list.</td><td class="s10">Passed</td><td class="s28"></td><td class="s28"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td></tr><tr style="height: 19px"><th id="859059453R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td class="s22">PRS-1309-008</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify Additional Approver can still be added after Non-RS has already been approved by current approver.</td><td class="s22">1. User is on Non-RS page.<br>2. Logged in as current Approver.<br>3. Non-RS has been approved.</td><td class="s22">1. Observe the Approvers section.<br>2. Verify if Add button is displayed.</td><td class="s28"></td><td class="s22">2. Add button should no longer be visible.</td><td class="s10">Passed</td><td class="s28"></td><td class="s28"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td></tr><tr style="height: 19px"><th id="859059453R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s22">PRS-1309-009</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify Add Approver button is removed if same Additional Approver is already added.</td><td class="s22">1. User is on Non-RS page.<br>2. Logged in as current Approver.<br>3. Additional Approver already exist on the list.</td><td class="s22">1. Click Add approver.<br>2. Search the same user that is already on the list.</td><td class="s28"></td><td class="s22">2. Add button should be removed to prevent duplicate addition.</td><td class="s10">Passed</td><td class="s28"></td><td class="s28"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td><td class="s29"></td></tr><tr style="height: 19px"><th id="859059453R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s22">PRS-1309-010</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s22">Verify Default Approver can Edit or Delete Added Approver before approval.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Added Approver has not approved the request.</td><td class="s22">1. Click ellipsis  to display Edit/Delete icon.<br>2. Perform edit or delete action.</td><td class="s23"></td><td class="s22">2. Added Approver details can be edited or deleted successfully.</td><td class="s17">Failed</td><td class="s22">CITYLANDPRS-1685</td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s22">PRS-1309-011</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">Critical</td><td class="s22">Verify Non-RS cannot proceed to Additional Approver until current Approver approves.</td><td class="s5">1. User is a Non-RS Approver and logged in as Added Approver.<br>2. User is on Non-RS page.<br>3. Both Approvers added.</td><td class="s22">1. Attempt to approves as Added Approver before Default Approver approves.</td><td class="s23"></td><td class="s22">1. System blocks approval.</td><td class="s10">Passed</td><td class="s22"></td><td class="s30"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 19px"><th id="859059453R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s22">PRS-1309-012</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">Critical</td><td class="s22">Verify next level of approval is blocked until Additional Approver Approves.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Default Approver has approved, Added Approver has not.</td><td class="s22">1. Attempt to proceed to next level approval.</td><td class="s23"></td><td class="s22">1. Approval to next level is blocked until Added Approver approves.</td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s22">PRS-1309-013</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">Minor</td><td class="s22">Verify Add Approver button appears in confirmation message if none is added.</td><td class="s5">1. User is a Non-RS Approver and the current Approver.<br>2. User is on Non-RS page.<br>3. Ready to approve, no Additional Approver added.</td><td class="s22">1. Click Approve.<br>2. Observe confirmation modal.</td><td class="s23"></td><td class="s22">1. Add Approver button is displayed in confirmation message.</td><td class="s10">Passed</td><td class="s22"></td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s22">PRS-1309-014</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s5">Verify Notification is sent to the Added Approver.</td><td class="s5">1. User is on Non-RS page.<br>2. Approver is added successfully.<br>3. Logged in as Added Approver.</td><td class="s22">1. Click on Notification Bell.</td><td class="s23"></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Additional Approver should receive the notificaiton with the ff format:<br><br>1. Notification with title &quot;Assigned as an Additional Approver&quot; is shown with accurate content and date.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Title: </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Assigned as an Additional Approver<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Content:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Non-RS Payment Request and have it Approved. Click here or access the Dashboard to proceed in reviewing the Purchase Order.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date of the Notification: [MMM-DD-YYY]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Nov 08 2024</span></td><td class="s17">Failed</td><td class="s22">CITYLANDPRS-1682</td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s22">PRS-1309-015</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s5">Verify correct notification when Non-RS is rejected.</td><td class="s5">1.User is on Non-RS page.<br>2. A Non-RS Payment Request is rejected by an Approver.<br>3. Logged in as Requestor.</td><td class="s22">1. Check Notifications.</td><td class="s23"></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Notification with title &quot;Non-RS Payment Request Rejected&quot; and correct content is shown.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Title: </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Non-RS Payment Request Rejected<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Content:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Non-RS Payment Request has been Rejected by one of the Approvers. Click here to proceed in reviewing the Non-RS Payment Request.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date of the Notification: [MMM-DD-YYY]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Nov 08 2024</span></td><td class="s17">Failed</td><td class="s22">CITYLANDPRS-1682</td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s22">PRS-1309-016</td><td class="s6">Update Adding of Approver behavior for Non-RS Approval</td><td class="s7">High</td><td class="s5">Verify correct notification when Non-RS is updated by Approver.</td><td class="s5">1.User is on Non-RS page.<br>2. A Non-RS Payment Request is updated by an Approver.<br>3. Logged in as Requestor.</td><td class="s22">1. Check Notifications.</td><td class="s23"></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Notification with title &quot;Non-RS Payment Request updated by an Approver&quot; is shown.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Title: </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Non-RS Payment Request updated by an Approver<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Content:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>[NON-RS NUMBER] has been updated by one of the Approvers. Click here to view the updates on the Non-RS Payment Request.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date of the Notification: [MMM-DD-YYY]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Nov 08 2024</span></td><td class="s17">Failed</td><td class="s22">CITYLANDPRS-1682</td><td class="s22"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td><td class="s25"></td></tr><tr style="height: 19px"><th id="859059453R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s3" colspan="12">Allow Adding of Notes before Approval of Non-RS</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R90" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">91</div></th><td class="s22">1310-001</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">Minor</td><td class="s22">Verify click Non-RS Payment Request Number</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;</td><td class="s23"></td><td class="s22">Non-RS Payment Request Number should be clickable</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R91" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">92</div></th><td class="s22">1310-002</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify sticky Confirmation Message for Approval</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Verify Sticky Approval Message</td><td class="s23"></td><td class="s22">User should see a Sticky Confirmation Message for Approval</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s26"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R92" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">93</div></th><td class="s22">1310-003</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">Critical</td><td class="s22">Verify if Approve Button is clickable</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Verify Approve Button is clickable</td><td class="s23"></td><td class="s22">Approve Button should be clickable</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R93" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">94</div></th><td class="s22">1310-004</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify if Confirmation Modal is displayed </td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select between Approve or Reject</td><td class="s23"></td><td class="s22">Confirmation Modal should be displayed</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R94" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">95</div></th><td class="s22">1310-005</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">Critical</td><td class="s22">Verify if the Approver is allowed to enter a Notes before they are allowed to Approve</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select Approve Button</td><td class="s23"></td><td class="s22">Approver should be allowed to enter a Notes before they are allowed to Approve</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R95" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">96</div></th><td class="s22">1310-006</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify if Alphanumeric and Special Characters except Emojis are accepted in Notes</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select Approve</td><td class="s23"></td><td class="s22">Alphanumeric and Special Characters except Emojis should be accepted in Notes</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s22">1310-007</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify if Notes is Maximum of 100 Characters</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select Approve</td><td class="s23"></td><td class="s22">Maximum characters Notes should be 100 Characters</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s22">1310-008</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify if Note before Approval is not Required</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select Approve</td><td class="s23"></td><td class="s22">Note before Approval requirement should not be required</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s22">1310-009</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify click Approve Button to proceed with their Approval</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select Approve</td><td class="s23"></td><td class="s22">User should click Approve button to proceed approval</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s30"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s22">1310-010</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">Minor</td><td class="s22">Verify If Cancel Button is clicked, should close the Modal and return back to Non-RS Payment Request Form</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval</td><td class="s22">1.  Select &quot;Non-RS&quot; to the Nav bar<br>2. Click &quot;Non-RS Payment Request Number&quot;<br>3. Select Approve<br>4. Click Cancel Button</td><td class="s23"></td><td class="s22">Should close the Modal and return back to Non-RS Payment Request Form when cancel button is clicked</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s22">1310-011</td><td class="s6">Allow Adding of Notes before Approval of Non-RS</td><td class="s7">High</td><td class="s22">Verify if the Approval Note is displayed to the Non-RS&#39;s Check Notes Button</td><td class="s5">1. User is a Non-RS Approver and the current Approver<br>2. A Non-RS Payment Request has been submitted for Approval with notes</td><td class="s22">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Notes” of the approved Canvass Sheet.<br>3. Verify visibility of approver.<br>4. Verify the badge is cleared.</td><td class="s23"></td><td class="s22">1. “New Attachment” badge is displayed.<br>2. Entered Approval Notes are displayed correctly.<br>3. Approver name should be displayed correctly.<br>4. “New Attachment” badge is cleared when viewed.</td><td class="s22"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s3" colspan="12">Non-RS Payment Request Download to PDF</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s5">PRS-066-001</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Critical</td><td class="s5">Verify if the Download button is displayed on the Non-RS Payment Request Page</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Click a Non-RS Payment Request Number<br>2. Validate if a Download Button is visible on the page <br></td><td class="s23"></td><td class="s22"><br>2. Should have a Download Button when viewing a Non-RS Payment Request</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s5">PRS-066-001</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Critical</td><td class="s5">Verify if the Download button is not visible on the Non-RS Payment Request Page when the Request is in Draft</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Click a Non-RS Payment Request Number which status is in Draft<br>2. Validate if the Download Button is not visible on the page <br></td><td class="s23"></td><td class="s22"><br>2. Should not have a Download Button when viewing a Non-RS Payment Request Draft</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s5">PRS-066-002</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Critical</td><td class="s5">Verify if the Print Preview of the Non-RS Payment appears upon clicking the Download Button</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Click the Download Button<br>2. The Print Preview of the Non-RS Payment Request should appear after clicking<br></td><td class="s23"></td><td class="s22"><br>2. The Print Preview of the Non-RS Payment Request appeared after clicking</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s26"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s5">PRS-066-003</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the P.R.# Number is visible on the upper left corner of the page</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Print Preview<br>2. Validate if the correct PR Number is visible on the upper left corner of the page<br></td><td class="s23"></td><td class="s22"><br>2. The correct PR Number was visible on the upper left corner of the page</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R106" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">107</div></th><td class="s5">PRS-066-004</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if a NOTE is visible below the PR Number, indicating processing days of the request </td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Print Preview<br>2. Validate if there is a note below the PR Number indicating processing days of the request<br></td><td class="s23"></td><td class="s22"><br>2.  A note below the PR Number, indicating processing days of the request was visible<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R107" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">108</div></th><td class="s5">PRS-066-005</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the Date Prepared, Date Needed, and Time Needed is visible on the upper right corner of the page</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Print Preview<br>2. Validate if the correct Date Prepared, Date Needed, and Time Needed is visible on the upper right corner of the page<br></td><td class="s23"></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>2. The correct Date Prepared, Date Needed, and Time Needed was visible on the upper right corner of the page<br>    Date Prepared: </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">XX Mmm YYYY<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">    Date Needed: </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">XX Mmm YYYY<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">    Time Needed: <br></span></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R108" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">109</div></th><td class="s5">PRS-066-006</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if all the Request Details Labels and Value Fields are visible on the Print Preview</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Print Preview<br>2. Validate if all the labels and fields are displayed on the Print Preview inside Request Details<br><br></td><td class="s23"></td><td class="s22"><br>2. The following labels and fields were displayed on the Print Preview under Request Details<br>    a. Non-RS Number<br>    b. Non-RS Status<br>    c. Category - Category indicated in the Non-RS Payment Request<br>    d. Date Required - Date indicated in the Non-RS Payment Request<br>    e.Company - Company indicated in the Non-RS Payment Request<br>    f.Project - Project indicated in the Non-RS Payment Request<br>       i. Should display as &quot;---&quot; if no Data is indicated<br>    g. Department - Depatment indicated in the Non-RS Payment Request<br>    h. Invoice Number - Invoice Number indicated in the Non-RS Payment Request<br>    h. Payable To<br>        i. Payable To indicated in the Non-RS Payment Request<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R109" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">110</div></th><td class="s5">PRS-066-007</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the Items Table is visible on the Print Preview with all the necessary columns</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Print Preview<br>2. Validate if the Items Table is visible on the Print Preview under the &#39;Items&#39; label with all the necessary columns<br></td><td class="s23"></td><td class="s22"><br>2. The Items Table was displayed under the &#39;Items&#39; label with the following Columns<br>    a. Sequence Number - Incremental depending on the Number of Items Added<br>    b. Item Name - Item Name added in the Non-RS Payment Request<br>    c. Quantity- Requested Quantity of the Item<br>    d. Unit - Unit of the Item<br>    e. Unit Price - Unit Price indicated for the Item<br>    f. Discount - Discount indicated for the Item<br>    g. Total Price - Discounted Unit Price x Requested Quantity<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R110" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">111</div></th><td class="s5">PRS-066-008</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the items table creates another page when the content exceeds the limit</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Items Table<br>2.  Validate if another page/s is created and the items table continue on another page<br></td><td class="s23"></td><td class="s22"><br>2. Another page/s was created and the items table continued on another page</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R111" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">112</div></th><td class="s5">PRS-066-009</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Minor</td><td class="s5">Verify if the items table pagination number is visible with the format &#39;X - XX of XXXX&#39; and the correct item number is displayed on documents with multiple items</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Items Table<br>2. Validate that the items table pagination number is visible on the Print review and the correct item number is displayed with the proper format <br>(eg., &quot;1 - 10  of 9999&quot;)<br></td><td class="s23"></td><td class="s22"><br>2. The items table pagination number was visible on the Print review and the correct item number was displayed with the proper format <br>(eg., &quot;1 - 10  of 9999&quot;)<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R112" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">113</div></th><td class="s5">PRS-066-010</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the order of items displayed have the Non-Steel bar Items first to be followed by the Steelbar Items if the Request is a mix of Steelbars and other OFM Items</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Items Table<br>2. Validate if the order of items in the Items Table have the Non-Steel bar Items first to be followed by the Steelbar Items if the Request is a mix of Steelbars and other OFM Items<br></td><td class="s23"></td><td class="s22"><br>2. The order of items in the Items Table  have the Non-Steel bar Items first to be followed by the Steelbar Items if the Request is a mix of Steelbars and other OFM Items</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R113" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">114</div></th><td class="s5">PRS-066-011</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Medium</td><td class="s5">Verify if the order of items with only the steel bar items is displayed properly</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Items Table<br>2. Validate if the order of steel bars items in the Items Table is sorted properly<br></td><td class="s23"></td><td class="s22"><br>2. The order of steel bars items in the Items Table is sorted properly</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R114" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">115</div></th><td class="s5">PRS-066-012</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Medium</td><td class="s5">Verify if the order of items with only the non-steel bar items is displayed properly</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Items Table<br>2. Validate if the order of non-steel bars items in the Items Table is sorted properly<br></td><td class="s23"></td><td class="s22"><br>2. The order of non-steel bars items in the Items Table is sorted properly</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R115" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">116</div></th><td class="s5">PRS-066-013</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the Sum of all of the Items is at the last Row of the Items Table</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Items Table<br>2. Validate if the correct Sum of all of the Items is at the last Row of the Items Table</td><td class="s23"></td><td class="s22"><br>2. Should display the correct Sum of all of the Items at the last Row of the Items Table</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R116" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">117</div></th><td class="s5">PRS-066-014</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if the Word Format of the Total Amount is correct and corresponds with the Total amount in Number format</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Items Table<br>2. Validate if the word Format of the Total Amount is correct and corresponds with the Total amount in Number format</td><td class="s23"></td><td class="s22"><br>2. Should indicate the correct and corresponding Word Format of the Total Amount</td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R117" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">118</div></th><td class="s5">PRS-066-015</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if there is a section for Checkboxes with all the necessary options</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Print Preview<br>2. Validate that a section for Checkboxes with all the necessary options is visible on the page below the Items Table</td><td class="s23"></td><td class="s22"><br>2. Should have a section for Checkboxes with Options for<br>    a. Pay to Cash<br>    b. For Encashment<br>    c. Uncrossed Check<br>    d. Manager&#39;s Check<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R118" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">119</div></th><td class="s5">PRS-066-016</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if there is a section for Supporting Documents with all the necessary options</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Print Preview<br>2. Validate that a section for Supporting Documents with all the necessary options is visible on the page</td><td class="s23"></td><td class="s22"><br>2. Should have a section for Supporting Documents with Options for<br>    a. Attached<br>    b. To Follow<br>    c. None Available<br>    d. Orig RS/OS/CS<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R119" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">120</div></th><td class="s5">PRS-066-017</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">High</td><td class="s5">Verify if there is a section for Signatures</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1. Observe the Print Preview<br>2. Validate that a section for Signatures is visible on the page</td><td class="s23"></td><td class="s22"><br>2. Should have a section for Signatures<br>    a. Requested By<br>    b. Endorsed By<br>    c. Approved By<br>    d. Countersigned By<br></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R120" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">121</div></th><td class="s5">PRS-066-018</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Minor</td><td class="s5">Verify if the page number is visible with the format &#39;Page X of X&#39; and the correct page number is displayed on documents with multiple pages</td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><br>1.  Observe the Print Preview<br>2. Validate that the page number is visible on the Print review and the correct page number is displayed<br></td><td class="s23"></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>2.  Should have a Page indicator<br>    e.g. No. of printing made: </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">2</span></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="859059453R121" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">122</div></th><td class="s5">PRS-066-019</td><td class="s6">Non-RS Payment Request Download to PDF</td><td class="s7">Medium</td><td class="s5">Verify the format of the File Name </td><td class="s5"><br>1. User is logged in as any user type except Root User<br>2. Non-RS Payment Request has been created and submitted<br></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br><br>1. Download the file<br>2. Validate if the File Name combination is correct</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br></span></td><td class="s23"></td><td class="s22"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>2. Should have a File Name combination of Document Prefix+Date Extracted[YYYYMMDD]+Time Extracted[HHMMSS]<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">SAMPLE: NRS-20240728-102039</span></td><td class="s33"></td><td class="s32">Not Started</td><td class="s22"></td><td class="s22"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></tbody></table></div>