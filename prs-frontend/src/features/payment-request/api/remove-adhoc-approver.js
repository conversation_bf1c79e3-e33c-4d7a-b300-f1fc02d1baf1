import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@lib/apiClient';

export const removeAdhocApprover = ({ id }) => {
  return api.delete(`/v1/rs-payment-request/${id}/remove-adhoc-approver`);
};

export const useRemoveAdhocApprover = (config = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: removeAdhocApprover,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentRequest'] });
      queryClient.invalidateQueries({ queryKey: ['paymentRequestDetails'] });
      queryClient.invalidateQueries({ queryKey: ['paymentRequestApprovers'] });
    },
    ...config,
  });
};
