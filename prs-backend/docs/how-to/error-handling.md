# Error Handling System

This document provides an overview of the standardized error handling system implemented for the PRS Backend project.

## Overview

The error handling system consists of:

1. **Error Hierarchy**: A consistent hierarchy of error classes
2. **Error Translation**: A centralized error translation mechanism
3. **Error Factory**: A utility for creating errors
4. **Global Error Handler**: A handler that processes all errors

## Error Hierarchy

All errors extend the base `AppError` class, which provides common functionality:

```
AppError
├── ValidationError
├── NotFoundError
├── AuthenticationError
├── AuthorizationError
├── BadRequestError
├── ConflictError
└── DatabaseError
```

### AppError

Base class for all application errors.

```javascript
const { AppError } = require('../app/errors');

throw new AppError(
  'Something went wrong',
  'CUSTOM_ERROR_CODE',
  { additionalInfo: 'Some details' },
  originalError
);
```

### ValidationError

Used for validation errors, particularly with Zod schemas.

```javascript
const { ValidationError } = require('../app/errors');

throw new ValidationError(
  'Validation failed',
  { field1: 'Field is required', field2: 'Must be a number' },
  originalError
);
```

### NotFoundError

Used when a resource is not found.

```javascript
const { NotFoundError } = require('../app/errors');

throw new NotFoundError(
  'User',
  userId,
  'User not found',
  originalError
);
```

### AuthenticationError

Used for authentication failures.

```javascript
const { AuthenticationError } = require('../app/errors');

throw new AuthenticationError(
  'Invalid credentials',
  'INVALID_PASSWORD',
  originalError
);
```

### AuthorizationError

Used when a user lacks permission for an action.

```javascript
const { AuthorizationError } = require('../app/errors');

throw new AuthorizationError(
  'You do not have permission to delete this resource',
  'DELETE_RESOURCE',
  originalError
);
```

### BadRequestError

Used for invalid client requests.

```javascript
const { BadRequestError } = require('../app/errors');

throw new BadRequestError(
  'Invalid request parameters',
  { details: 'Some details about the error' },
  originalError
);
```

### ConflictError

Used for resource conflicts, such as duplicate entries.

```javascript
const { ConflictError } = require('../app/errors');

throw new ConflictError(
  'User with this email already exists',
  'User',
  'email',
  email,
  originalError
);
```

### DatabaseError

Used for database operation failures.

```javascript
const { DatabaseError } = require('../app/errors');

throw new DatabaseError(
  'Failed to create user',
  'create',
  originalError
);
```

## Error Factory

The `ErrorFactory` provides convenient methods for creating errors:

```javascript
const { ErrorFactory } = require('../app/errors');

// Create a validation error
throw ErrorFactory.validation('Validation failed', { field: 'Invalid value' });

// Create a not found error
throw ErrorFactory.notFound('User', userId);

// Create an authentication error
throw ErrorFactory.authentication('Invalid credentials');

// Create an authorization error
throw ErrorFactory.authorization('Permission denied', 'DELETE_USER');

// Create a bad request error
throw ErrorFactory.badRequest('Invalid parameters', { details: 'Some details' });

// Create a conflict error
throw ErrorFactory.conflict('User already exists', 'User', 'email', email);

// Create a database error
throw ErrorFactory.database('Database operation failed', 'create');

// Create a generic error
throw ErrorFactory.generic('Something went wrong', 'CUSTOM_ERROR');
```

## Error Translation

The `ErrorTranslator` converts various error types to standardized `AppError` instances:

```javascript
const { ErrorTranslator } = require('../app/errors');

try {
  // Some operation that might throw different types of errors
} catch (error) {
  // Translate the error to an AppError
  const appError = ErrorTranslator.translate(error, {
    operation: 'create',
    resource: 'User'
  });
  
  // Handle the translated error
  throw appError;
}
```

## Global Error Handler

The global error handler processes all errors and returns standardized responses:

```javascript
// This is automatically registered in the application
const { errorHandler } = require('../app/errors');

// All errors thrown in route handlers will be processed by the error handler
fastify.setErrorHandler(errorHandler);
```

## Response Format

All errors are returned in a consistent format:

```json
{
  "status": 400,
  "errorCode": "BAD_REQUEST",
  "message": "Invalid request parameters",
  "timestamp": "2023-06-01T12:34:56.789Z",
  "metadata": {
    "details": "Some details about the error"
  }
}
```

## Migration from Legacy Error System

The legacy error system (`clientErrors`, `serverErrors`, etc.) is still available for backward compatibility, but new code should use the new error system.

### Legacy:

```javascript
const { clientErrors } = require('../app/errors');

throw clientErrors.BAD_REQUEST({
  message: 'Invalid request',
  description: 'Some details'
});
```

### New System:

```javascript
const { ErrorFactory } = require('../app/errors');

throw ErrorFactory.badRequest('Invalid request', { details: 'Some details' });
```

## Best Practices

1. **Use Specific Error Types**: Use the most specific error type for the situation.
2. **Include Meaningful Messages**: Error messages should be clear and descriptive.
3. **Add Context**: Include relevant context in error metadata.
4. **Catch and Translate**: Catch and translate errors from external libraries.
5. **Don't Expose Sensitive Information**: Be careful not to include sensitive information in error responses.
6. **Log Errors**: Ensure errors are properly logged for debugging.
7. **Use the Error Factory**: Use the `ErrorFactory` for creating errors when possible.
