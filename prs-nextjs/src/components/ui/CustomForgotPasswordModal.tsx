"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

interface CustomForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { username: string }) => void;
}

const forgotPasswordSchema = z.object({
  username: z.string().min(1, 'Username is required'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export function CustomForgotPasswordModal({ isOpen, onClose, onSubmit }: CustomForgotPasswordModalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose}></div>

      {/* Modal */}
      <div className="relative w-[550px] rounded-md bg-white shadow-xl overflow-hidden">
        {/* Header */}
        <div className="bg-[#7a5c5f] text-white py-3 px-4 text-center">
          <h3 className="text-base font-medium">Forgot Password</h3>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-sm text-gray-700 mb-4">
            You are about to reset your password. If you want to proceed with
            this action please enter your username and press continue.
          </p>

          <form onSubmit={handleSubmit((data) => onSubmit(data))}>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
              <input
                type="text"
                className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 text-sm"
                {...register('username')}
              />
              {errors.username && (
                <p className="mt-1 text-sm text-red-500">{errors.username.message}</p>
              )}
            </div>

            <div className="flex gap-3 justify-end">
              <button
                type="button"
                onClick={onClose}
                className="w-40 bg-white border border-gray-300 text-gray-700 rounded-md py-2 px-4 text-sm font-medium hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-40 rounded-md py-2 px-4 bg-[#7a5c5f] hover:bg-[#797a7b] text-white text-sm font-medium"
              >
                Continue
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
