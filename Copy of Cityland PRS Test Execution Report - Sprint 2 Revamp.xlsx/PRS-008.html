<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d0e0e3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="196631778C0" style="width:103px;" class="column-headers-background">A</th><th id="196631778C1" style="width:98px;" class="column-headers-background">B</th><th id="196631778C2" style="width:252px;" class="column-headers-background">C</th><th id="196631778C3" style="width:252px;" class="column-headers-background">D</th><th id="196631778C4" style="width:233px;" class="column-headers-background">E</th><th id="196631778C5" style="width:330px;" class="column-headers-background">F</th><th id="196631778C6" style="width:184px;" class="column-headers-background">G</th><th id="196631778C7" style="width:320px;" class="column-headers-background">H</th><th id="196631778C8" style="width:100px;" class="column-headers-background">I</th><th id="196631778C9" style="width:88px;" class="column-headers-background">J</th><th id="196631778C10" style="width:116px;" class="column-headers-background">K</th><th id="196631778C11" style="width:116px;" class="column-headers-background">L</th><th id="196631778C12" style="width:116px;" class="column-headers-background">M</th><th id="196631778C13" style="width:116px;" class="column-headers-background">N</th><th id="196631778C14" style="width:78px;" class="column-headers-background">O</th><th id="196631778C15" style="width:72px;" class="column-headers-background">P</th><th id="196631778C16" style="width:100px;" class="column-headers-background">Q</th></tr></thead><tbody><tr style="height: 42px"><th id="196631778R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td><td class="s4"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="196631778R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5" colspan="16">PRS-008 - [Manage Items] - View, Create, Update OFM Items, OFM List and Non-OFM Items, Search, Filter</td><td class="s4"></td></tr><tr style="height: 19px"><th id="196631778R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s6">PRS-008-001</td><td class="s7">[OFM Items] OFM Items Landing Page</td><td class="s6"></td><td class="s6">Validate OFM Items Landing Page</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM&quot; sub menu<br>3. Check display of the OFM Items page<br>4. Check Sorting of Item Name</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Should display the following:<br>    .a. Search Field and Search button<br>     b. Sync Button<br>        i. Once clicked, should be disabled until Data is fully retrieved<br>        ii.. Should display the Date and Time of the Last Synced Data<br>    c. Clear button<br>    d. Table with the following Columns:<br>       i. Item Code<br>       ii. Item Description<br>       iii. Unit<br>       iv. Account Code<br>       v. GFQ<br>       vi. Trade<br>    e. Should display 10 Rows per Page<br>4. Should sort the Table alphabetically by Item Name (0-9, A-Z)<br></td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1ug3uLcS5U98nqLNkK3K42dEfDT0LlmF6KJFpflfxnno/edit?gid=0#gid=0&amp;range=A1">https://docs.google.com/spreadsheets/d/1ug3uLcS5U98nqLNkK3K42dEfDT0LlmF6KJFpflfxnno/edit?gid=0#gid=0&amp;range=A1</a></td><td class="s11">Failed</td><td class="s12">Passed</td><td class="s10"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=0#gid=0">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=0#gid=0</a></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Default Sorting is not in alphabetical order</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-729">https://youtrack.stratpoint.com/issue/CITYLANDPRS-729</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s6">PRS-008-002</td><td class="s7">[OFM Items] Search and Filter of Items</td><td class="s6"></td><td class="s6">Validate OFM Items Search and Filter of Items is working as expected</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM&quot; sub menu<br>3.  Populate a keyword for Item Description in search field <br>4. Click Search button<br>5. Click Clear button<br>6. Click filter icon<br>7. Populate each dropdown fields and click search<br>8. Click Clear button<br>9. Populate a keyword in search field that has no match to Item Description column list<br>10. Click Search button<br>11. Click Clear button<br>12. Populate a keyword  that has no match in each filter dropdown fields and click search</td><td class="s6"><span style="font-family:Poppins,Arial;color:#000000;">keyword for step 3: </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Pipes</span><span style="font-family:Poppins,Arial;color:#000000;">, </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items</span><span style="font-family:Poppins,Arial;color:#000000;"><br>keyword for step 7: <br>    a. Item Code - </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">bi001su5</span><span style="font-family:Poppins,Arial;color:#000000;"><br>    b. Unit - </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">kilo</span><span style="font-family:Poppins,Arial;color:#000000;"><br>    c. Account Code - </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">2018C650015308</span><span style="font-family:Poppins,Arial;color:#000000;"><br>    d. Trade - </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Fire protection Works<br><br></span><span style="font-family:Poppins,Arial;color:#000000;">keyword for step 9: </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">bulb</span><span style="font-family:Poppins,Arial;color:#000000;"><br>keyword for step 12: <br>    a. Item Code - 89907uj<br>    b. Unit - group<br>    c. Account Code - 2048C650015308<br>    d. Trade - time<br> </span></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Keyword for Item Description should be populated<br>4. Should be able to display all related keyword for OFM Items and should be applied for all of the Data and not for the Page only<br>5. Should be clear entered keyword, able to refresh the page and displayed all OFM items list<br>6. Should displayed a dropdown fields for the ff:<br>    -Item Code<br>    -Unit<br>    -Account Code<br>    -Trade<br>7. Should display a correct item of selected filter per ff fields:<br>    -Item Code<br>    -Unit<br>    -Account Code<br>    -Trade<br>8. Should be clear filtered fields, able to refresh the page and displayed all OFM items list<br>9.  Keyword  should be populated in the search field<br>10. No Data View should be displayed<br>11. Should be clear entered keyword, able to refresh the page and displayed all OFM items list<br>12. No Data View should be displayed<br></td><td class="s16"></td><td class="s17">Passed</td><td class="s18">Failed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6"></td><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1084">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1084</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R4" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">5</div></th><td class="s6">PRS-008-003</td><td class="s7">[OFM Items] Pull Items Data</td><td class="s6"></td><td class="s6">Validate OFM Items Pulling of Items Data when OFM Item is not yet added on the list</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM&quot; sub menu<br>3.  Ciick &quot;Sync&quot; button<br></td><td class="s9">Step 3.</td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Should initiate syncing of OFM Items from OFM Items Master File and add the OFM item/s in the List.<br>4. Should sync the Department List for Filters and Forms<br>5. Should display the Date and Time of the last triger of the Sync Button<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s16"></td><td class="s11">Failed</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Sync button is showing an error after some changes in the backend</td><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-738">https://youtrack.stratpoint.com/issue/CITYLANDPRS-738</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R5" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">6</div></th><td class="s6">PRS-008-004</td><td class="s7">[OFM Items] Pull Items Data</td><td class="s6"></td><td class="s6">Validate OFM Items Pulling of Items Data when existing OFM item data has an update</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM&quot; sub menu<br>3.  Click &quot;Sync&quot; button<br></td><td class="s9">Step 3.</td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Should initiate syncing of OFM Items from OFM Items Master File and a new update has been done on existing OFM item/s in the List.<br>4. Should sync the Department List for Filters and Forms<br>5. Should display the Date and Time of the last triger of the Sync Button<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s16"></td><td class="s11">Failed</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Sync button is showing an error after some changes in the backend</td><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-738">https://youtrack.stratpoint.com/issue/CITYLANDPRS-738</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R6" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">7</div></th><td class="s6">PRS-008-005</td><td class="s7">[OFM Items] View Item and History</td><td class="s6"></td><td class="s6">Validate OFM Items Viewing of  Item and History</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM&quot; sub menu<br>3. Ciick &quot;Item Code&quot; Text Link <br>4. Check Display of Item Code View page</td><td class="s9">Step 3:<br>Item Code - BI002SU5</td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Should displayed the Item Code View page<br>4. Should displayed the ff:<br>    a. Item Code<br>    b. Item Description<br>    c. Unit<br>    d. Account Code<br>    e. GFQ<br>        i. Implemented a Mock Data for the GFQ of the Items that will be shown in the OFM Item View<br>    f. Remaining GFQ<br>       i. Should follow the Formula of:<br>FOR FIRST REQUEST OF THE ITEM<br>GFQ - Request Quantity = Remaining GFQ<br><br>AFTER FIRST REQUEST OF ITEM<br>Last Remaining GFQ - Request Quantity = New Remaining GFQ<br>    g. Company<br>    h. Project<br>    i. Trade<br>5. Should display Table of History with the following::<br>    a. Search field and search button<br>    b. Clear button</td><td class="s16"></td><td class="s11">Failed</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Remaining GFQ doesn&#39;t decrease after approving an RS</td><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-764">https://youtrack.stratpoint.com/issue/CITYLANDPRS-764</a></td><td class="s15">align with madam irene</td></tr><tr style="height: 211px"><th id="196631778R7" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">8</div></th><td class="s6">PRS-008-006</td><td class="s20">[OFM List] Pre-created OFM List after Syncing of OFM Items</td><td class="s6"></td><td class="s6">Validate OFM List Pre-created OFM List after Syncing of OFM Items</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head<br>5. OFM Items were synced</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM&quot; sub menu<br>3. Ciick &quot;Item Code&quot; Text Link <br>4. Check Display of Item Code View page</td><td class="s9"></td><td class="s21">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Should displayed the Item Code View page<br>4. Should displayed the ff:<br>    a. Item Code<br>    b. Item Description<br>    c. Unit<br>    d. Account Code<br>    e. GFQ<br>        i. Implemented a Mock Data for the GFQ of the Items that will be shown in the OFM Item View<br>    f. Remaining GFQ<br>       i. Should follow the Formula of:<br>FOR FIRST REQUEST OF THE ITEM<br>GFQ - Request Quantity = Remaining GFQ<br><br>AFTER FIRST REQUEST OF ITEM<br>Last Remaining GFQ - Request Quantity = New Remaining GFQ<br>    g. Company<br>    h. Project<br>    i. Trade<br>5. Should display Table of History with the following::<br>    a. Search field and search button<br>    b. Clear button</td><td class="s16"></td><td class="s11">Failed</td><td class="s11">Failed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Remaining GFQ doesn&#39;t decrease after approving an RS</td><td class="s6">https://youtrack.stratpoint.com/issue/CITYLANDPRS-765</td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R8" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">9</div></th><td class="s6">PRS-008-007</td><td class="s20">[OFM List] OFM List Landing Page</td><td class="s6"></td><td class="s6">Validate the OFM List Landing Page</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head<br>5. OFM Items were synced</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM List&quot; sub menu<br>3. Check display of the OFM List page<br>4. Check Sorting of the following columns:<br>    a. List Name<br>    b. Company <br>    c. Project <br>    d. Trade<br><br></td><td class="s9"></td><td class="s6"><span style="font-family:Poppins,Arial;color:#000000;">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM List page <br>3. Should display the following:<br>    .a. Search Field and Search button<br>     b. Clear button<br>    </span><span style="font-family:Poppins,Arial;text-decoration:line-through;color:#000000;">c. Download Button and Print Button<br>          i. Once Download Button is clicked, should be able to download an Excel File of the OFM List  and OFM Items in the List<br>          ii. Once Print Button is clicked, should be able to Print an Excel File of the OFM List  and OFM Items in the List</span><span style="font-family:Poppins,Arial;color:#000000;"><br>    d. Table with the following Columns:<br>       i. List Name<br>      ii. Company <br>      iii. Project <br>      iv. Trade<br>    e. Should display 10 Rows per Page<br>4. Should allow sorting per Column<br>    a. List Name<br>       i. Default sorting: A-Z<br>       ii. Can be sorted by A-Z, Z-A<br>    b. Company <br>       i. Can be sorted by: 0-9, A-Z || 9-0, Z-A<br>    c. Project <br>       i. Can be sorted by A-Z, Z-A<br>    d. Trade<br>       i. Can be sorted by A-Z, Z-A<br></span></td><td class="s16"></td><td class="s17">Not Run</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Update the test case, Download and Print Button is removed.<br><br>Task ticket for adding data to test pagination and max display of 10 items per page.<br></td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-757">https://youtrack.stratpoint.com/issue/CITYLANDPRS-757 - Sorting Trade column doesn&#39;t work<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-760 - Task Ticket for Adding Test Item in OFM List</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R9" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">10</div></th><td class="s6">PRS-008-008</td><td class="s20">[OFM List] Search and Filter OFM List</td><td class="s6"></td><td class="s6">Validate the OFM List Search and Filter is working as expected</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head<br>5. OFM Items were synced</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM List&quot; sub menu<br>3.  Populate a keyword for List Name, Company, Department, and Project in search field <br>4. Click Search button<br>5. Click Clear button<br>6. Click filter icon<br>7. Populate each dropdown fields and click search<br>8. Click Clear button<br>9. Populate a keyword in search field that has no match to List Name, Company, Department, and Project names in column list<br>10. Click Search button<br>11. Click Clear button<br>12. Populate a keyword  that has no match in each filter dropdown fields and click search</td><td class="s6"><span style="font-family:Poppins,Arial;color:#000000;">keyword for step 3: <br>keyword for step 7: <br>        i. List Name<br>        ii. Company<br>        iii. Project<br>        iv. Trade</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br><br></span><span style="font-family:Poppins,Arial;color:#000000;">keyword for step 9: </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">bulb</span><span style="font-family:Poppins,Arial;color:#000000;"><br>keyword for step 12: <br>        i. List Name<br>        ii. Company<br>        iii. Project<br>        iv. Trade   <br></span></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM Items page <br>3. Keyword for List Name, Company, Department, and Project should be populated<br>4. Should be able to display all related keyword for List Name, Company, Department, and Project in OFM List and should be applied for all of the Data and not for the Page only<br>5. Should be clear entered keyword, able to refresh the page and displayed all OFM list<br>6. Should displayed a dropdown fields for the ff:<br>        i. List Name<br>        ii. Company<br>        iii. Project<br>        iv. Trade<br>7. Should display a correct item of selected filter per ff fields:<br>        i. List Name<br>        ii. Company<br>        iii. Project<br>        iv. Trade<br>8. Should be clear filtered fields, able to refresh the page and displayed all OFM List <br>9.  Keyword  should be populated in the search field<br>10. No Data View should be displayed<br>11. Should be clear entered keyword, able to refresh the page and displayed all OFM items list<br>12. No Data View should be displayed<br></td><td class="s16"></td><td class="s17">Passed</td><td class="s22">Blocked</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6"></td><td class="s23"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1084">CITYLANDPRS-1084</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R10" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">11</div></th><td class="s6">PRS-008-009</td><td class="s20">[OFM List] Viewing of OFM Items</td><td class="s6"></td><td class="s6">Validate the OFM List Viewing of OFM Items</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head<br>5. OFM Items were synced</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;OFM List&quot; sub menu<br>3. Ciick &quot;List Name&quot; Text Link <br>4. Check Display of  OFM List View page</td><td class="s9 softmerge"><div class="softmerge-inner" style="width:181px;left:-1px">Step 3:<br>List Name - <br>ONE TAFT RESIDENCES-Civil and Architecture Works</div></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the OFM List page <br>3. Should displayed the List Name View page<br>4. Should displayed the ff:<br>    a. List Name<br>    b. Company<br>    c. Department<br>    d. Project<br>    e. Trade<br>5.Should display a Table of the Items added in the OFM List<br>    a. Should have Columns for:<br>        i. Item<br>        ii. Account Code<br>        iii. Unit<br>        iv. GFQ<br>            i) Implemented a Mock Data for the GFQ of the Items that will be shown in the OFM Item View<br>6. Should have Search for the Table Items<br>    a. Can search Item Name</td><td class="s16"></td><td class="s11">Failed</td><td class="s18">Failed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Missing Search bar, submit and clear button</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-775">CITYLANDPRS-1147<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-775</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R11" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">12</div></th><td class="s24">PRS-008-010</td><td class="s24">[Non-OFM Items] Non-OFM Items Landing Page</td><td class="s24"></td><td class="s24">Validate the Non-OFM Items Landing Page</td><td class="s25"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head</a></td><td class="s24">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM&quot; sub menu<br>3. Check display of the Non-OFM Items page<br>4. Check Sorting per column of the ff:<br>   a. Item Name<br>    b. Item Type<br>    c. Trade<br>    d. Unit</td><td class="s26"></td><td class="s24">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display the following:<br>    a. Search Field and Search button<br>    b. Filter button<br>    c. Clear button<br>    d. Table with the following Columns:<br>       i. Item Name<br>       ii. Item Type<br>       iii. Trade<br>      iv. Unit<br>      v.. Actions<br>        i.) Edit<br>    e. Should display 10 Rows per Page<br>4. Should sort the ff columns correctly<br>    a. Item Name<br>       i. Default sorting: A-Z<br>       ii. Can be sorted by A-Z, Z-A<br>    b. Item Type<br>       i. Can be sorted by: A-Z, Z-A<br>    c. Trade<br>       i. Can be sorted by A-Z, Z-A<br>    d. Unit<br>       i. Can be sorted by 0-9, 9-0</td><td class="s16"></td><td class="s11">Failed</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Still Validating with Ms Irene about Copy, Print and Download Button</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-788">https://youtrack.stratpoint.com/issue/CITYLANDPRS-788</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R12" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">13</div></th><td class="s6">PRS-008-011</td><td class="s27">[Non-OFM Items] Search and Filter Non-OFM Item</td><td class="s6"></td><td class="s6">Validate the Non-OFM Items Search and Filter are working as expected</td><td class="s6">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head<br></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3.  Populate a keyword for Item Name, and Trade in search field <br>4. Click Search button<br>5. Click Clear button<br>6. Click filter icon<br>7. Populate each fields and click search<br>8. Click Clear button<br>9. Populate a keyword in search field that has no match to Item Name, and Trade in column list<br>10. Click Search button<br>11. Click Clear button<br>12. Populate a keyword  in each filter fields and click search</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Keyword for Item Name, and Trade should be populated<br>4. Should be able to display all related keyword for  Item Name, and Trade in Non-OFM Items and should be applied for all of the Data and not for the Page only<br>5. Should be clear entered keyword, able to refresh the page and displayed all Non-OFM Items<br>6. Should displayed a  fields for the ff:<br>        i. Type<br>        ii. Trade<br>        iii. Unit<br>7. Should display a correct item of selected filter per ff fields:<br>        i. Type<br>        ii. Trade<br>        iii. Unit<br>8. Should be clear filtered fields, able to refresh the page and displayed all Non-OFM Items<br>9.  Keyword  should be populated in the search field<br>10. No Data View should be displayed<br>11. Should be clear entered keyword, able to refresh the page and displayed all Non-OFM Items<br>12. No Data View should be displayed<br></td><td class="s16"></td><td class="s11">Failed</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Still Validating with Ms Irene about Filter button. If updated only to Item Type and Unit</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-789">https://youtrack.stratpoint.com/issue/CITYLANDPRS-789</a></td><td class="s15"></td></tr><tr style="height: 211px"><th id="196631778R13" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">14</div></th><td class="s6">PRS-008-012</td><td class="s27">[Non-OFM Items]  Creation of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Creation of Non-OFM Items</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Purchasing Staff<br>    c. Purchasing Head</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3. Click &quot;New Item&quot; Button<br>4. Validate field for Item Name<br>5. Validate field for Item Type<br>6. Validate field for Unit<br>7. Validate field for Description<br>8. Populate all fields<br>9. Click &quot;Cancel&quot; button<br>10. Click &quot;Cancel&quot; on &quot;Cancel Non-OFM item&quot; confirmation modal<br>11. Click &quot;Submit&#39; button<br>12. Click &quot;Continue&quot; on &quot;Submit Non-OF Item&quot; confirmation modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display Non-OFM Item Creation Form with the ff fields and buttons<br>    a. Item Name field<br>    b. Item Type field<br>    c Unit Drop-down field<br>    d. Description Text Area field<br>    e. Cancel button<br>    f. Submit button<br>4. Should meet the ff validations for Item name;<br>        a. Alphanumeric and Special Characters except Emojis<br>        b. Maximum of 100 Characters<br>5. Item type should have a dropdown field with the ff values:<br>        a. Goods<br>        b. Services<br>6. Unit should have a dropdown field withe ff values:<br>        i. pc<br>        ii. lot<br>        iii. pack<br>        iv. unit<br>        v. set<br>        vi. m<br>        vii. gal<br>        viii. liter<br>        ix. bundle<br>        x. kilo<br>        xi. yard<br>        xii. ream<br>        xiii. box<br>        xiv. bottle<br>        xv. pair<br>        xvi. roll<br>        xvii. dozen<br>        xviii. can<br>        xix. unit<br>        xx. tin<br>7. Description field should meet the ff validations:<br>        a. Letters and Special Characters except Emojis<br>        b. Should have a maximum of 100 Characters<br>8. All fields should be populated<br>9. Should display a &quot;Cancel Non-OFM Item&quot; confirmation modal with contains of the ff;<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot; <br>    b. Cancel and Continue buttons<br>10. should redirected back to Item Creation Non-OFM page and retained the previously populated details<br>11. Should displayed a &quot;Submit Non-OFM Item&quot; Confirmation modal w ithe contains of the ff;<br>    a. a description &quot; You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. A Cancel and Continue buttons<br>12. Should display a Success Toast Message and should be automatically closed after 3 seconds.And displayed the newly created Non-OFM item in the table list</td><td class="s16"></td><td class="s28">Blocked</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Cannot Create Item</td><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-786">https://youtrack.stratpoint.com/issue/CITYLANDPRS-786</a></td><td class="s15"></td></tr><tr style="height: 207px"><th id="196631778R14" style="height: 207px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 207px">15</div></th><td class="s6">PRS-008-013</td><td class="s27">[Non-OFM Items]  Creation of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Creation of Non-OFM Items when clicked cancel</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Purchasing Staff<br>    c. Purchasing Head</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3. Click &quot;New Item&quot; Button<br>4. Validate field for Item Name<br>5. Validate field for Item Type<br>6. Validate field for Unit<br>7. Validate field for Description<br>8. Populate all fields<br>9. Click &quot;Cancel&quot; button<br>10. Click &quot;Continue&quot; on &quot;Cancel Non-OFM item&quot; confirmation modal<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display Non-OFM Item Creation Form with the ff fields and buttons<br>    a. Item Name field<br>    b. Item Type field<br>    c Unit Drop-down field<br>    d. Description Text Area field<br>    e. Cancel button<br>    f. Submit button<br>4. Should meet the ff validations for Item name;<br>        a. Alphanumeric and Special Characters except Emojis<br>        b. Maximum of 100 Characters<br>5. Item type should have a dropdown field with the ff values:<br>        a. Goods<br>        b. Services<br>6. Unit should have a dropdown field withe ff values:<br>        i. pc<br>        ii. lot<br>        iii. pack<br>        iv. unit<br>        v. set<br>        vi. m<br>        vii. gal<br>        viii. liter<br>        ix. bundle<br>        x. kilo<br>        xi. yard<br>        xii. ream<br>        xiii. box<br>        xiv. bottle<br>        xv. pair<br>        xvi. roll<br>        xvii. dozen<br>        xviii. can<br>        xix. unit<br>        xx. tin<br>7. Description field should meet the ff validations:<br>        a. Letters and Special Characters except Emojis<br>        b. Should have a maximum of 100 Characters<br>8. All fields should be populated<br>9. Should display a &quot;Cancel Non-OFM Item&quot; confirmation modal with contains of the ff;<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot; <br>    b. Cancel and Continue buttons<br>10. Should not create the Non-OFM Item for the Project and redirect back to Non-OFM Items list Page</td><td class="s16"></td><td class="s17">Passed</td><td class="s12">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s9"></td><td class="s9"></td><td class="s29"></td></tr><tr style="height: 211px"><th id="196631778R15" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">16</div></th><td class="s6">PRS-008-014</td><td class="s6">[Non-OFM Items]  Viewing of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Viewing of Non-OFM Items</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Engineers<br>    c. Purchasing Staff<br>    d. Purchasing Head<br>5. Should have a Non-OFM Item created per Project</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non- OFM List&quot; sub menu<br>3. Ciick &quot;Item Name&quot; Text Link <br>4. Check Display of  OFM List View page</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM List page <br>3. Should displayed the Non-OFM Item View page<br>4. Should displayed the ff Non-OFM List details:<br>    a. Item Name<br>    b. Account Code<br>    c. Item Type<br>    d. Unit<br>    e. Description<br>5. Should have Edit Button<br>6. Should display Table of History</td><td class="s16"></td><td class="s17">Passed</td><td class="s17">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Confirmed with Ms Cherry about the Edit function as engineer user type, should be no edit function according to user type bingo card.</td><td class="s9"></td><td class="s29"></td></tr><tr style="height: 211px"><th id="196631778R16" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">17</div></th><td class="s6">PRS-008-015</td><td class="s27">[Non-OFM Items]  Editing of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Editing of Non-OFM Items thru actions column in the table list</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Purchasing Staff<br>    c. Purchasing Head<br>5. Should have a Non-OFM Item created per Project</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3. Click &quot;Edit&quot; Button in actions column on the table<br>4. Validate field for Item Name<br>5. Validate fiield for Account Code<br>6. Validate field for Item Type<br>7. Validate field for Unit<br>8. Validate field for Description<br>9. Update all fields<br>10. Click &quot;Cancel&quot; button<br>11. Click &quot;Cancel&quot; on &quot;Cancel Changes&quot; confirmation modal<br>12. Click &quot;Save&#39; button<br>13. Click &quot;Continue&quot; on &quot;Confirm Changes&quot; confirmation modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display Non-OFM Item Edit Form with the ff fields and buttons<br>    a. Item Name field<br>    b. Item Type field<br>    c Unit Drop-down field<br>    d. Description Text Area field<br>    e. Cancel button<br>    f. Save button<br>4. Should meet the ff validations for Item name;<br>        a. Alphanumeric and Special Characters except Emojis<br>        b. Maximum of 100 Characters<br>5. Account code shold be non-editable field<br>6. Item type should have a dropdown field with the ff values:<br>        a. Goods<br>        b. Services<br>7. Unit should have a dropdown field withe ff values:<br>        i. pc<br>        ii. lot<br>        iii. pack<br>        iv. unit<br>        v. set<br>        vi. m<br>        vii. gal<br>        viii. liter<br>        ix. bundle<br>        x. kilo<br>        xi. yard<br>        xii. ream<br>        xiii. box<br>        xiv. bottle<br>        xv. pair<br>        xvi. roll<br>        xvii. dozen<br>        xviii. can<br>        xix. unit<br>        xx. tin<br>8. Description field should meet the ff validations:<br>        a. Letters and Special Characters except Emojis<br>        b. Should have a maximum of 100 Characters<br>9. All fields should be updated<br>10. Should display a &quot;Cancel Changes&quot; confirmation modal with contains of the ff;<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot; <br>    b. Cancel and Continue buttons<br>11. should redirected back to Item Edit Non-OFM page and retained the previously populated details<br>12. Should displayed a &quot;Confirm Changes&quot; Confirmation modal w ithe contains of the ff;<br>    a. a description &quot; You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. A Cancel and Continue buttons<br>13. Should display a Success Toast Message and should be automatically closed after 3 seconds.And displayed the newly updated Non-OFM item in edit form</td><td class="s16"></td><td class="s11">Failed</td><td class="s17">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Description not being saved.<br><br><br>Confirmed with Ms Cherry about the Edit function as engineer user type.</td><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-790">https://youtrack.stratpoint.com/issue/CITYLANDPRS-790</a></td><td class="s29"></td></tr><tr style="height: 244px"><th id="196631778R17" style="height: 244px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 244px">18</div></th><td class="s6">PRS-008-016</td><td class="s27">[Non-OFM Items]  Editing of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Editing of Non-OFM Items thru actions column in the table list when clicked cancel</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Purchasing Staff<br>    c. Purchasing Head<br>5. Should have a Non-OFM Item created per Project</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3. Click &quot;Edit&quot; Button in actions column on the table<br>4. Validate field for Item Name<br>5. Validate field for Account code<br>6. Validate field for Item Type<br>7. Validate field for Unit<br>8. Validate field for Description<br>9. Update all fields<br>10. Click &quot;Cancel&quot; button<br>11. Click &quot;Continue&quot; on &quot;Cancel Changes&quot; confirmation modal<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display Non-OFM Item Edit Form with the ff fields and buttons<br>    a. Item Name field<br>    b. Item Type field<br>    c Unit Drop-down field<br>    d. Description Text Area field<br>    e. Cancel button<br>    f. Save button<br>4. Should meet the ff validations for Item name;<br>        a. Alphanumeric and Special Characters except Emojis<br>        b. Maximum of 100 Characters<br>5. Account code shold be non-editable field<br>6. Item type should have a dropdown field with the ff values:<br>        a. Goods<br>        b. Services<br>7. Unit should have a dropdown field withe ff values:<br>        i. pc<br>        ii. lot<br>        iii. pack<br>        iv. unit<br>        v. set<br>        vi. m<br>        vii. gal<br>        viii. liter<br>        ix. bundle<br>        x. kilo<br>        xi. yard<br>        xii. ream<br>        xiii. box<br>        xiv. bottle<br>        xv. pair<br>        xvi. roll<br>        xvii. dozen<br>        xviii. can<br>        xix. unit<br>        xx. tin<br>8. Description field should meet the ff validations:<br>        a. Letters and Special Characters except Emojis<br>        b. Should have a maximum of 100 Characters<br>9. All fields should be updated<br>10. Should display a &quot;Cancel Changes&quot; confirmation modal with contains of the ff;<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot; <br>    b. Cancel and Continue buttons<br>11. should redirected back to Non-OFM Item list page and not able to save the updated fields</td><td class="s30"></td><td class="s17">Passed</td><td class="s17">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Confirmed with Ms Cherry about the Edit function as engineer user type, should be no edit function according to user type bingo card.</td><td class="s6"></td><td class="s29"></td></tr><tr style="height: 241px"><th id="196631778R18" style="height: 241px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 241px">19</div></th><td class="s6">PRS-008-017</td><td class="s27">[Non-OFM Items]  Editing of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Editing of Non-OFM Items thru  Edit Button when viewing a Non-OFM Item<br></td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Purchasing Staff<br>    c. Purchasing Head<br>5. Should have a Non-OFM Item created per Project</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3. Click Item name text link to view Non ofm item<br>4. Click &quot;Edit&quot; Button in Non-OFM items view page<br>5. Validate field for Item Name<br>6. Validate field for Account Code<br>7. Validate field for Item Type<br>8. Validate field for Unit<br>9. Validate field for Description<br>10. Update all fields<br>11. Click &quot;Cancel&quot; button<br>12. Click &quot;Cancel&quot; on &quot;Cancel Changes&quot; confirmation modal<br>13. Click &quot;Save&#39; button<br>14. Click &quot;Continue&quot; on &quot;Confirm Changes&quot; confirmation modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display Non-OFM item view page<br>4. Should display Non-OFM Item Edit Form with the ff fields and buttons<br>    a. Item Name field<br>    b. Item Type field<br>    c Unit Drop-down field<br>    d. Description Text Area field<br>    e. Cancel button<br>    f. Save button<br>5. Should meet the ff validations for Item name;<br>        a. Alphanumeric and Special Characters except Emojis<br>        b. Maximum of 100 Characters<br>6. Account code shold be non-editable field<br>7. Item type should have a dropdown field with the ff values:<br>        a. Goods<br>        b. Services<br>8. Unit should have a dropdown field withe ff values:<br>        i. pc<br>        ii. lot<br>        iii. pack<br>        iv. unit<br>        v. set<br>        vi. m<br>        vii. gal<br>        viii. liter<br>        ix. bundle<br>        x. kilo<br>        xi. yard<br>        xii. ream<br>        xiii. box<br>        xiv. bottle<br>        xv. pair<br>        xvi. roll<br>        xvii. dozen<br>        xviii. can<br>        xix. unit<br>        xx. tin<br>9. Description field should meet the ff validations:<br>        a. Letters and Special Characters except Emojis<br>        b. Should have a maximum of 100 Characters<br>10. All fields should be updated<br>11. Should display a &quot;Cancel Changes&quot; confirmation modal with contains of the ff;<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot; <br>    b. Cancel and Continue buttons<br>12. should redirected back to Item Edit Non-OFM page and retained the previously populated details<br>13. Should displayed a &quot;Confirm Changes&quot; Confirmation modal w ithe contains of the ff;<br>    a. a description &quot; You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. A Cancel and Continue buttons<br>14. Should display a Success Toast Message and should be automatically closed after 3 seconds.And displayed the newly updated Non-OFM item in edit form</td><td class="s31"></td><td class="s11">Failed</td><td class="s17">Passed</td><td class="s16"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Description not being saved.</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-790">https://youtrack.stratpoint.com/issue/CITYLANDPRS-790</a></td><td class="s29"></td></tr><tr style="height: 238px"><th id="196631778R19" style="height: 238px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 238px">20</div></th><td class="s6">PRS-008-018</td><td class="s27">[Non-OFM Items]  Editing of Non-OFM Items</td><td class="s6"></td><td class="s6">Validate Editing of Non-OFM Items thru  Edit Button when viewing a Non-OFM Item<br>when clcked cancel</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User Logged in as the ff:<br>    a. IT Admin<br>    b. Purchasing Staff<br>    c. Purchasing Head<br>5. Should have a Non-OFM Item created per Project</a></td><td class="s6">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non-OFM Items&quot; sub menu<br>3. Click Item name text link to view Non ofm item<br>4. Click &quot;Edit&quot; Button in Non-OFM Item view page<br>5. Validate field for Item Name<br>6. Validate field for Account code<br>7. Validate field for Item Type<br>8. Validate field for Unit<br>9. Validate field for Description<br>10. Update all fields<br>11. Click &quot;Cancel&quot; button<br>12. Click &quot;Continue&quot; on &quot;Cancel Changes&quot; confirmation modal<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM Items page <br>3. Should display the Non-OFM Item view page<br>4. Should display Non-OFM Item Edit Form with the ff fields and buttons<br>    a. Item Name field<br>    b. Item Type field<br>    c Unit Drop-down field<br>    d. Description Text Area field<br>    e. Cancel button<br>    f. Save button<br>5. Should meet the ff validations for Item name;<br>        a. Alphanumeric and Special Characters except Emojis<br>        b. Maximum of 100 Characters<br>6. Account code shold be non-editable field<br>7. Item type should have a dropdown field with the ff values:<br>        a. Goods<br>        b. Services<br>8. Unit should have a dropdown field withe ff values:<br>        i. pc<br>        ii. lot<br>        iii. pack<br>        iv. unit<br>        v. set<br>        vi. m<br>        vii. gal<br>        viii. liter<br>        ix. bundle<br>        x. kilo<br>        xi. yard<br>        xii. ream<br>        xiii. box<br>        xiv. bottle<br>        xv. pair<br>        xvi. roll<br>        xvii. dozen<br>        xviii. can<br>        xix. unit<br>        xx. tin<br>9. Description field should meet the ff validations:<br>        a. Letters and Special Characters except Emojis<br>        b. Should have a maximum of 100 Characters<br>10. All fields should be updated<br>11. Should display a &quot;Cancel Changes&quot; confirmation modal with contains of the ff;<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot; <br>    b. Cancel and Continue buttons<br>12. Should redirected back to Non-OFM Item list page and not able to save the updated fields</td><td class="s30"></td><td class="s17">Passed</td><td class="s17">Passed</td><td class="s30"></td><td class="s13">Not Started</td><td class="s14"></td><td class="s6">Confirmed with Ms Cherry, Cancel button is initially disabled when there is no input yet.</td><td class="s6"></td><td class="s29"></td></tr></tbody></table></div>