import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/apiClient';
import { formatSortParams } from '@utils/query';

export const getPurchaseHistory = async ({ type, id, page, limit, sortBy }) => {
    return await api.get(`/v1/items/${type}/${id}/purchase-history`, {
        params: {
            page,
            limit,
            ...formatSortParams(sortBy),
        }
    });
}

export const useGetPurchaseHistory = ({ id, isOfm = true, page = 1, limit = 10, sortBy = {}}, config = {}) => {
    const type = isOfm ? 'ofm' : 'non-ofm';

    const query = useQuery({
        queryKey: ['purchase_history', { id, isOfm, page, limit, sortBy }], 
        queryFn: () => getPurchaseHistory({ id, type, page, limit, sortBy }),
        enabled: !!id,
        ...config,
    });

    return query;
}