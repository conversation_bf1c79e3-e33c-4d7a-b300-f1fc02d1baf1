# Notification System

This document outlines the notification system in the PRS backend, which handles the creation and management of system notifications.

## Notification Types

The system defines several notification types in `notificationConstants.js`:

| Notification Type | Description |
|-------------------|-------------|
| SUPPLIER_SUSPENDED | Notification when a supplier used in a requisition is suspended |
| SUPPLIER_SYNC | Notification when a supplier's information is updated |
| COMPANY_SYNC | Notification when a company's information is updated |
| ITEM_SYNC | Notification when an OFM item's information is updated |
| APPROVE_NON_RS | Notification when a non-RS payment request is approved |
| REJECT_NON_RS | Notification when a non-RS payment request is rejected |
| CANCEL_RS | Notification when a requisition slip is cancelled |
| ASSIGNING_RS | Notification when a new requisition slip is assigned |

## Notification Structure

Based on the database model (`notificationModel.js`), notifications have the following structure:

- **Title**: Short description of the notification
- **Message**: Detailed notification content
- **Type**: Type of notification (from NOTIFICATION_TYPES)
- **Recipient Role ID**: Role that should receive the notification
- **Recipient User IDs**: Specific users who should receive the notification
- **Sender ID**: User who triggered the notification
- **Viewed By**: Array of user IDs who have viewed the notification
- **Meta Data**: Additional JSON data related to the notification

## Event-Triggered Notifications

### Approval Notifications

**Trigger**: Entity approval (requisition, canvass, purchase order, payment)

**Recipients**:
- Entity creator
- Entity assignee
- Department members (configurable)

**Content**:
- Approval status
- Approver information
- Next steps

### Rejection Notifications

**Trigger**: Entity rejection (requisition, canvass, purchase order, payment)

**Recipients**:
- Entity creator
- Entity assignee
- Department members (configurable)

**Content**:
- Rejection reason
- Approver information
- Instructions for resubmission

### Data Sync Notifications

**Trigger**: Master data updates (supplier, company, item)

**Recipients**:
- Users with active entities referencing the updated data

**Content**:
- What data was updated
- Affected entities
- Action required (if any)

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/notificationController.js`
- **Service**: `src/app/services/notificationService.js`
- **Repository**: `src/infra/repositories/notificationRepository.js`
- **Model**: `src/infra/database/models/notificationModel.js`
- **Constants**: `src/domain/constants/notificationConstants.js`

### Notification Generation Implementation

```javascript
// Example notification generation based on actual code
async function generateNotification({
  title,
  message,
  recipientUserIds,
  entityId,
  entityType,
  transaction,
}) {
  // Create notification
  const notification = await notificationRepository.create({
    title,
    message,
    recipientUserIds,
    entityId,
    entityType,
    isRead: false,
    createdAt: new Date(),
  }, { transaction });

  // Log notification creation
  await logNotificationEvent(notification.id, 'CREATED', transaction);

  return notification;
}

// Example of generating an approval notification
async function generateApprovalNotification(entityId, entityType, approverId, transaction) {
  // Get entity details
  const entity = await getEntityById(entityId, entityType, transaction);

  // Get approver details
  const approver = await userRepository.findOne({
    where: { id: approverId },
    transaction,
  });

  // Determine recipients
  const recipientUserIds = [entity.createdBy];
  if (entity.assignedTo && entity.assignedTo !== entity.createdBy) {
    recipientUserIds.push(entity.assignedTo);
  }

  // Generate notification content
  const { title, message } = generateApprovalNotificationContent(
    entityType,
    approver.firstName,
    approver.lastName
  );

  // Create notification
  return generateNotification({
    title,
    message,
    recipientUserIds,
    entityId,
    entityType,
    transaction,
  });
}
```

### Notification Delivery Implementation

```javascript
// Example notification delivery based on actual code
async function deliverNotifications(notification, transaction) {
  // Get recipient preferences
  const recipients = await userRepository.findAll({
    where: {
      id: {
        [Sequelize.Op.in]: notification.recipientUserIds,
      },
    },
    transaction,
  });

  // Deliver in-app notifications (already done by creating the notification)

  // Deliver email notifications if configured
  for (const recipient of recipients) {
    if (recipient.emailNotificationsEnabled) {
      await sendEmailNotification(
        recipient.email,
        notification.title,
        notification.message
      );
    }
  }

  // Update delivery status
  await notification.update({
    deliveredAt: new Date(),
  }, { transaction });

  return notification;
}
```

## Common Issues and Solutions

### Issue 1: Missing Notifications

**Cause**: User not included in recipient list or notification generation failed.

**Solution**:
- Check user's role and relationship to the entity
- Verify notification generation logic for the specific event
- Check for errors in the notification service logs
- Ensure user has not disabled notifications

### Issue 2: Notification Overload

**Cause**: Too many notifications generated for similar events.

**Solution**:
- Consider grouping similar notifications
- Implement notification preferences to allow users to filter notifications
- Set up notification digests instead of individual notifications
- Review notification generation rules to reduce unnecessary notifications

### Issue 3: Notification Links Not Working

**Cause**: Entity referenced by notification no longer exists or has changed status.

**Solution**:
- Implement link validation when notification is clicked
- Show appropriate error message if entity is not accessible
- Consider updating or removing notifications when entities change status
- Include entity status in notification to provide context
