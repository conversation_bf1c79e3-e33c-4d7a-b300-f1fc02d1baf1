# Authentication and Authorization

## Overview

The PRS-Frontend implements a comprehensive authentication and authorization system that includes user login, OTP verification, token management, and role-based access control. This document provides a detailed analysis of the authentication and authorization mechanisms, including the authentication flow, token management, and permission handling.

## Authentication Architecture

The authentication system is built around several key components:

1. **Auth Library**: Configured in `src/lib/auth.jsx` using the `react-query-auth` library
2. **Token Store**: Implemented in `src/store/authStore.js` using Zustand
3. **User Store**: Implemented in `src/store/userStore.js` using Zustand
4. **Permission Store**: Implemented in `src/store/permissionStore.js` using Zustand
5. **API Client**: Configured in `src/lib/apiClient.js` with authentication interceptors
6. **Protected Routes**: Implemented using the `ProtectedRoute` component

## Authentication Flow

The authentication flow includes several steps:

### 1. Login

The login process is implemented in `src/features/auth/components/LoginForm.jsx`:

```jsx
// From src/features/auth/components/LoginForm.jsx
const handleLoginSubmit = async values => {
  try {
    const routeMap = {
      requireUpdatePassword: '/auth/create-password',
      requireOTPSetup: '/auth/setup-otp',
      requireOTPVerification: '/auth/verify-otp',
    };

    const response = await login.mutateAsync(values);
    const nextRoute = Object.keys(routeMap).find(key => response[key]);
    const { accessToken } = response;

    setToken(accessToken);
    setCurrentRoute(nextRoute);

    if (accessToken && !nextRoute) {
      const { refreshToken, expiredAt } = response;
      setRefreshToken({ refreshToken, expiredAt });
      setType('auth');
      navigate('/', { replace: true });
    }

    if (nextRoute === 'requireOTPSetup') {
      const { otpAuthUrl, otpSecret, accessToken } = response;
      setOTP({ otp: otpAuthUrl, secret: otpSecret });
      setToken(accessToken);
    }

    if (nextRoute) {
      navigate(routeMap[nextRoute], { replace: true });
    }
  } catch (error) {
    console.error(error);
  }
};
```

This process:
1. Sends login credentials to the API
2. Handles different response scenarios (normal login, OTP setup, OTP verification, password update)
3. Stores tokens and user information
4. Redirects to the appropriate page

### 2. OTP Setup and Verification

The OTP setup and verification process is implemented in `src/features/auth/components/OTPForm.jsx`:

```jsx
// OTP setup
const handleOTPSetup = async values => {
  try {
    const { otp } = values;
    const { secret } = useUserStore.getState();

    const response = await setupOTP.mutateAsync({
      otp,
      otpSecret: secret,
    });

    const { accessToken, refreshToken, expiredAt } = response;

    setToken(accessToken, 'auth');
    setRefreshToken({ refreshToken, expiredAt });
    removeOtp();

    navigate('/', { replace: true });
  } catch (error) {
    console.error(error);
  }
};

// OTP verification
const handleOTPVerify = async values => {
  try {
    const { otp } = values;

    const response = await verifyOTP.mutateAsync({
      otp,
    });

    const { accessToken, refreshToken, expiredAt } = response;

    setToken(accessToken, 'auth');
    setRefreshToken({ refreshToken, expiredAt });

    navigate('/', { replace: true });
  } catch (error) {
    console.error(error);
  }
};
```

This process:
1. Sends OTP code to the API for setup or verification
2. Stores tokens and user information
3. Redirects to the home page

### 3. Password Update

The password update process is implemented in `src/features/auth/components/PasswordForm.jsx`:

```jsx
// Password update
const handlePasswordUpdate = async values => {
  try {
    const response = await updatePassword.mutateAsync(values);
    const { accessToken, refreshToken, expiredAt } = response;

    setToken(accessToken, 'auth');
    setRefreshToken({ refreshToken, expiredAt });

    navigate('/', { replace: true });
  } catch (error) {
    console.error(error);
  }
};
```

This process:
1. Sends new password to the API
2. Stores tokens and user information
3. Redirects to the home page

### 4. Token Refresh

The token refresh process is implemented in `src/features/auth/api/refresh-token.js`:

```javascript
// From src/features/auth/api/refresh-token.js
export const refreshToken = async () => {
  const { getState, setState } = useTokenStore;
  const { refreshToken } = getState();

  const response = await api.post(
    '/v1/auth/token',
    {},
    {
      headers: {
        Authorization: `Bearer ${refreshToken}`,
      },
    },
  );

  const { accessToken, refreshToken: newRefreshToken, expiredAt } = response;

  setState({
    token: accessToken,
    refreshToken: newRefreshToken,
    expiredAt,
  });

  return response;
};
```

This process:
1. Sends the refresh token to the API
2. Receives new access and refresh tokens
3. Updates the token store

### 5. Logout

The logout process is implemented in `src/lib/auth.jsx`:

```javascript
// From src/lib/auth.jsx
const logout = async () => {
  const { setState } = useTokenStore;
  const { setState: setUserState } = useUserStore;
  sessionStorage.removeItem('timeoutState');
  setState({ token: null, type: null, refreshToken: null, expiredAt: null });
  setUserState({ user: null, otp: null, secret: null });
};
```

This process:
1. Clears session storage
2. Clears token and user stores

## Token Management

Tokens are managed using the Zustand store in `src/store/authStore.js`:

```javascript
// From src/store/authStore.js
const useTokenStore = create(
  persist(
    (set, get) => ({
      token: null,
      type: null,
      refreshToken: null,
      expiredAt: null,
      setToken: (token, type = null) => set({ token, type }),
      setRefreshToken: ({ accessToken, refreshToken, expiredAt }) => {
        const currentAccessToken = get().token;

        set({
          token: accessToken || currentAccessToken,
          refreshToken,
          expiredAt,
        });
      },
      setType: type => set({ type }),
      removeToken: () =>
        set({ token: null, type: null, refreshToken: null, expiredAt: null }),
    }),
    {
      name: 'auth-storage', // name of the item in the storage (must be unique)
    },
  ),
);
```

This store:
1. Stores access token, refresh token, and expiration time
2. Provides actions to set and remove tokens
3. Persists tokens to localStorage using Zustand's persist middleware

## User Management

User information is managed using the Zustand store in `src/store/userStore.js`:

```javascript
// From src/store/userStore.js
const useUserStore = create(
  persist(
    set => ({
      user: null,
      otp: null,
      secret: null,
      currentRoute: null,
      setUser: user => set({ user }),
      setOTP: ({ otp, secret }) => set({ otp, secret }),
      setCurrentRoute: route => set({ currentRoute: route }),
      removeUser: () => set({ user: null }),
      removeOtp: () => set({ secret: null, otp: null }),
      removeAll: () =>
        set({ user: null, otp: null, secret: null, currentRoute: null }),
    }),
    {
      name: 'user-storage',
    },
  ),
);
```

This store:
1. Stores user information, OTP details, and current route
2. Provides actions to set and remove user information
3. Persists user information to localStorage using Zustand's persist middleware

## Permission Management

Permissions are managed using the Zustand store in `src/store/permissionStore.js`:

```javascript
// From src/store/permissionStore.js
const usePermissionStore = create(
  set => ({
    permissions: null,
    setPermissions: permissions => set({ permissions }),
    removeAll: () => set({ permissions: null }),
  }),
  {
    name: 'permission-storage',
  },
);
```

This store:
1. Stores user permissions
2. Provides actions to set and remove permissions
3. Persists permissions to localStorage using Zustand's persist middleware

## Protected Routes

Protected routes are implemented using the `ProtectedRoute` component in `src/lib/auth.jsx`:

```jsx
// From src/lib/auth.jsx
export const ProtectedRoute = ({ children }) => {
  const { token, type } = useTokenStore();

  if (!token && type !== 'auth') {
    return <Navigate to="/login" replace />;
  }

  return children;
};
```

This component:
1. Checks if the user is authenticated
2. Redirects to the login page if not
3. Renders the protected content if authenticated

## Role-Based Access Control

Role-based access control is implemented using the `hasPermission` function in `src/utils/permissions.js` and the navigation configuration in `src/config/navigation.js`:

```javascript
// From src/utils/permissions.js
export const hasPermission = (route, permission) => {
  if (!permission) {
    return null;
  }

  return route;
};

// From src/config/navigation.js
const navigationConfig = {
  mainNavigation: [
    dashboardView && {
      label: 'Dashboard',
      hasDropdown: false,
      path: '/app/dashboard',
    },
    dashboardView && {
      label: 'Non-RS',
      hasDropdown: false,
      path: '/app/non-requisition-slip/dashboard',
    },
    (ofmItemsView || nonOfmItemsView || ofmListsView) && {
      label: 'Items',
      hasDropdown: true,
      dropdownItems: [
        ofmItemsView && { label: 'OFM', path: '/app/items/ofm' },
        ofmListsView && { label: 'OFM List', path: '/app/items/ofm-list' },
        nonOfmItemsView && { label: 'Non-OFM', path: '/app/items/non-ofm' },
      ].filter(Boolean),
    },
    // ... other navigation items
  ].filter(Boolean),
};
```

This approach:
1. Conditionally renders navigation items based on user permissions
2. Filters out navigation items for which the user doesn't have permission
3. Provides a clean way to define permission-based navigation

## API Authentication

API requests are authenticated using the token stored in the token store:

```javascript
// From src/lib/apiClient.js
function authRequestInterceptor(config) {
  const { getState } = useTokenStore;

  const token = getState().token;
  if (!config.headers.has('Authorization') && token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  config.headers.Accept = 'application/json';
  return config;
}

api.interceptors.request.use(authRequestInterceptor);
```

This interceptor:
1. Gets the token from the token store
2. Adds it to the request headers
3. Ensures all API requests are authenticated

## Authentication Issues

### 1. Limited Token Expiration Handling

The application doesn't proactively handle token expiration, relying instead on the API to return 401 errors:

```javascript
// From src/lib/apiClient.js
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    const { setState } = useTokenStore;
    const { setState: setUserState } = useUserStore;
    const { setState: setPermissionState } = usePermissionStore;

    if (error.response && error.response.status === 401) {
      setState({
        token: null,
        type: null,
        refreshToken: null,
        expiredAt: null,
      });
      setUserState({ user: null, otp: null, secret: null, currentRoute: null });
      setPermissionState({ permissions: null });
      sessionStorage.removeItem('timeoutState');
    } else {
      console.error('API Error:', error);
    }

    return Promise.reject(error);
  },
);
```

This approach:
1. Waits for a 401 error from the API
2. Clears authentication state
3. Doesn't attempt to refresh the token

### 2. Inconsistent Error Handling

Error handling in authentication flows is inconsistent, with some components handling errors differently than others:

```jsx
// Example of inconsistent error handling
try {
  const response = await login.mutateAsync(values);
  // ... handle success
} catch (error) {
  console.error(error);
  // No user feedback
}
```

### 3. Limited Permission Checking

The permission checking mechanism is limited, with no fine-grained control over component-level permissions:

```javascript
// From src/utils/permissions.js
export const hasPermission = (route, permission) => {
  if (!permission) {
    return null;
  }

  return route;
};
```

This approach:
1. Only checks if a permission exists
2. Doesn't support complex permission rules
3. Doesn't provide a way to check permissions at the component level

### 4. OTP Bypass in Development

The backend has an OTP bypass in development mode:

```javascript
// From prs-backend/src/app/handlers/controllers/authController.js
async login(request, reply) {
  const existingUser = await this.authService.verifyUser(request.body);

  //TODO: Remove after development
  const isByPassOTP =
    process.env.BYPASS_OTP === 'true' || process.env.BYPASS_OTP === true;

  if (isByPassOTP) {
    return this.authService.sendAccessToken(existingUser.id, reply);
  }

  // ... normal authentication flow
}
```

This bypass:
1. Skips OTP verification in development mode
2. Could lead to security issues if accidentally enabled in production
3. Creates inconsistency between development and production environments

## Recommendations

### 1. Implement Proactive Token Refresh

Implement a proactive token refresh mechanism that refreshes the token before it expires:

```javascript
// Token refresh utility
export const setupTokenRefresh = () => {
  const { getState } = useTokenStore;
  
  const checkTokenExpiration = () => {
    const { expiredAt, refreshToken } = getState();
    
    if (!expiredAt || !refreshToken) {
      return;
    }
    
    const expirationTime = new Date(expiredAt).getTime();
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;
    
    // Refresh token 5 minutes before expiration
    if (timeUntilExpiration > 0 && timeUntilExpiration < 5 * 60 * 1000) {
      refreshToken();
    }
  };
  
  // Check token expiration every minute
  const intervalId = setInterval(checkTokenExpiration, 60 * 1000);
  
  return () => clearInterval(intervalId);
};

// Usage in app initialization
useEffect(() => {
  const cleanup = setupTokenRefresh();
  return cleanup;
}, []);
```

### 2. Standardize Error Handling

Implement a consistent error handling approach for authentication flows:

```jsx
// Authentication error handling utility
export const handleAuthError = (error, options = {}) => {
  const { showNotification, defaultMessage = 'Authentication failed' } = options;
  
  const message = error.response?.data?.message || defaultMessage;
  
  if (showNotification) {
    showNotification({
      type: 'error',
      message,
    });
  }
  
  return {
    message,
    status: error.response?.status,
    data: error.response?.data,
  };
};

// Usage in login form
try {
  const response = await login.mutateAsync(values);
  // ... handle success
} catch (error) {
  handleAuthError(error, { showNotification });
}
```

### 3. Implement Fine-Grained Permission Checking

Implement a more sophisticated permission checking mechanism:

```javascript
// Permission checking utility
export const checkPermission = (permission, options = {}) => {
  const { permissions } = usePermissionStore.getState();
  const { requireAll = false, fallback = null } = options;
  
  if (!permissions) {
    return fallback;
  }
  
  if (Array.isArray(permission)) {
    const checkFn = requireAll ? 'every' : 'some';
    return permission[checkFn](p => checkPermission(p)) || fallback;
  }
  
  const [module, action] = permission.split('.');
  
  return (
    permissions[module]?.[action] ||
    fallback
  );
};

// Permission-based component rendering
export const PermissionGuard = ({ permission, fallback = null, children }) => {
  const hasPermission = checkPermission(permission);
  
  if (!hasPermission) {
    return fallback;
  }
  
  return children;
};

// Usage in components
<PermissionGuard
  permission="dashboard.create"
  fallback={<p>You don't have permission to create dashboards</p>}
>
  <CreateDashboardButton />
</PermissionGuard>
```

### 4. Remove OTP Bypass in Production

Ensure the OTP bypass is only enabled in development environments:

```javascript
// Environment-specific configuration
if (process.env.NODE_ENV === 'production') {
  // Ensure OTP bypass is disabled in production
  process.env.BYPASS_OTP = 'false';
}
```

### 5. Implement Session Timeout

Implement a session timeout mechanism that logs out inactive users:

```javascript
// Session timeout utility
export const setupSessionTimeout = () => {
  const { getState, setState } = useTokenStore;
  const { setState: setUserState } = useUserStore;
  const { setState: setPermissionState } = usePermissionStore;
  
  const resetTimeout = () => {
    const timeoutState = {
      lastActivity: Date.now(),
    };
    sessionStorage.setItem('timeoutState', JSON.stringify(timeoutState));
  };
  
  const checkTimeout = () => {
    const timeoutStateStr = sessionStorage.getItem('timeoutState');
    
    if (!timeoutStateStr) {
      resetTimeout();
      return;
    }
    
    const timeoutState = JSON.parse(timeoutStateStr);
    const currentTime = Date.now();
    const inactiveTime = currentTime - timeoutState.lastActivity;
    
    // Log out after 30 minutes of inactivity
    if (inactiveTime > 30 * 60 * 1000) {
      setState({
        token: null,
        type: null,
        refreshToken: null,
        expiredAt: null,
      });
      setUserState({ user: null, otp: null, secret: null, currentRoute: null });
      setPermissionState({ permissions: null });
      sessionStorage.removeItem('timeoutState');
      window.location.href = '/login';
    }
  };
  
  // Reset timeout on user activity
  const activityEvents = ['mousedown', 'keydown', 'touchstart', 'scroll'];
  activityEvents.forEach(event => {
    window.addEventListener(event, resetTimeout);
  });
  
  // Check timeout every minute
  const intervalId = setInterval(checkTimeout, 60 * 1000);
  
  return () => {
    activityEvents.forEach(event => {
      window.removeEventListener(event, resetTimeout);
    });
    clearInterval(intervalId);
  };
};

// Usage in app initialization
useEffect(() => {
  const cleanup = setupSessionTimeout();
  return cleanup;
}, []);
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
