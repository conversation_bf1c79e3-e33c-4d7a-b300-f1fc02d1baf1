Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,Status,Remarks,Defects
 [RS Creation] - Creation of RS,,,,,,,,,,,
1273-001,[RS CREATION] Creation of RS,Critical,Validate Requisition Slip page,"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Dashboard > Click New Request
2. Should display the following Fields:
    a. Category - Required
        i. Drop-down with values of Company, Project, Association
    b. Type of Request  - Required
        i. OFM
        ii. Non-OFM
        iii. OFM Transfer of Materials
        iv.  Non-OFM Transfer of Materials
    c. Company  - Required
        i. List of Companies Synced in Company Management or created Associations
    d. Project  - Required
        i.  List of Projects Synced in Project Management
    e. Department  - Required
        i. List of Departments Synced in Department Management
    f. Deliver To
        i. List of Company Addresses and Project Addresses 
    g. Date Required   - Required
        i. Date Picker
        ii. Should allow selecting of Dates equal or greater than the current Date
    h. Purpose - Required
        i. Should allow Alphanumeric and Special Characters except Emojis
       ii. Maximum of 100 Characters
    i.  Charge To (Category)  - Optional
        i. Drop-down with values of Company, Project, Association
    j.  Charge To - Optional
        i. Drop-down of values depending on the selected Category
    k. Attachments
       i. Uploading of Files such as Images (JPG,JPEG,PNG) and PDF
       ii. Maximum of 25MB per File
       iii. Note should display ""The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.""
    l. Add Note
       i. Text area of Notes with a Maximum Character of 100 Characters
    m. Items Table List
       i. Should be blank
       ii. Should have search that will allow searching by Item Name entered within the Request
       iii. Should have Add Item Button

3. Validate Save Draft button",,All fields should be present upon clicking 'New Request',https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********,Failed,"4/24 Placements of Fields Do Not Match the Figma Design

4/24 Items Table Sorting Not Working

4/28: Placements of Fields Do Not Match the Figma Design
- Retest passed","4/24:

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1523

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1513

"
1273-002,[RS CREATION] Creation of RS - Creating RS with category selected is company,Critical,Validate user should be able to create a Requisition Slip with category selected is Company,"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Login. Check pre-requisite for roles with access
2. Click Dashboard
3. Click 'New Request'
4. Populate the below fields. Check parameters.
   a. Category - Required
        i. Company
    b. Type of Request  - Required
    c. Company  - Required
    d. Project  - Required
    e. Department  - Required
    f. Date Required   - Required
    g. Deliver To
    h. Purpose - Required
    i. Add Note with 100 characters
5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom
6. Click Submit
7. User will be redirected to Dashboard page
8. RS number will be automatically generated and details will be displayed on top of the table","    a. Category - Company
    b. Type of Request  - <any>
    c. Company  - <any>
    d. Project  - <any>
    e. Department  - <any>
    f. Date  - <any>
    g. Deliver To
    h. Purpose - 50 characters with  alpanumeric such as ,.-'
    i. Attachments - Upload 1 file with 25 MB in size
    j. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! ""', @+=","1. Requisition slip will be sucessfully submitted and will be visible on top of the table
2. Should be autofilled by the User's Department in User Management
         ii. Options must be the List of Departments Synced in Department Management
3. By Default is the Project Address if selected
        i) If Company is also selected, Drop-down Options should be the Company Address and project Address
.4. Project
       i. List of Projects related to the Association
       ii. Optional",,Passed,"4/24: Project field is not optional
4/28: Rtest passed",4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1527
1273-003,[RS CREATION] Creation of RS - Creating RS with category selected is Association,Critical,Validate user should be able to create a Requisition Slip with Category selected is Company ,"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Login. Check pre-requisite for roles with access
2. Click Dashboard
3. Click 'New Request'
4. Populate the below fields. Check parameters.
  a. Category - Required
        i. Association
    b. Type of Request  - Required
    c. Company  - Required
    d. Project  - Required
    e. Department  - Required
    f. Date Required   - Required
    g. Deliver To
    h. Purpose - Required
    i. Add Note with 100 characters
    j. Attachments - Upload 1 file with 25 MB in size
    k. Add Note with 100 characters
5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom
6. Click Submit
7. User will be redirected to Dashboard page
8. RS number will be automatically generated and details will be displayed on top of the table","   a. Category - Association
    b. Type of Request  - <any>
    c. Company  - <any>
    d. Project  - <any>
    e. Department  - <any>
    f. Date  - <any>
    g. Deliver To
    h. Purpose - 50 characters with  alpanumeric such as ,.-'
    i. Attachments - Upload 1 file with 25 MB in size
    j. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! ""', @+=","1. Requisition slip will be sucessfully submitted and will be visible on top of the table
2. Will be reflected on Dashboard with RS number automatically generated 
3. Should be autofilled by the User's Department in User Management
         ii. Options must be the List of Departments Synced in Department Management
4.i. By Default is the Project Address if selected
       i) If Association is also selected, Drop-down Options should be the Association Address and project Address
5.Project
       i. List of Projects related to the Association
       ii. Optional",,Passed,"2/28: Able to create but w/ Bug raised

4/24: Project field is not optional","2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-973

4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1527"
1273-004,[RS CREATION] Creation of RS - Creating RS with category selected is project,Critical,Validate user should be able to create a Requisition Slip with category selected is Project,"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Login. Check pre-requisite for roles with access
2. Click Dashboard
3. Click 'New Request'
4. Populate the below fields. Check parameters.
   a. Category - Required
        i. Project
    b. Type of Request  - Required    
    c. Company  - Required
    d. Project  - Required
    e. Department  - Required
    f. Date Required   - Required
    g. Deliver To
    h. Purpose - Required
    i. Add Note with 100 characters
    j. Attachments - Upload 1 file with 25 MB in size
    k. Add Note with 100 characters including alphanumeric 01-$ $% ()-! ""', @+=
5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom
6. Click Submit
7. User will be redirected to Dashboard page
8. RS number will be automatically generated and details will be displayed on top of the table","     a. Category - Project 
    b. Type of Request  - <any>
    c. Company  - <any>
    d. Project  - <any>
    e. Department  - <any>
    f. Date  - <any>
    g. Deliver To
    h. Purpose - 50 characters with  alpanumeric such as ,.-'
    i. Attachments - Upload 1 file with 25 MB in size
    j. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! ""', @+=","1. Requisition slip will be sucessfully submitted and will be visible on top of the table
2. Will be reflected on Dashboard with RS number automatically generated 
3. By Default is the Project Address if selected
         i) If Company is also selected, Drop-down Options should be the Company Address and project Address",,Failed,"2/28: Able to create but w/ Bug raised
4/24: Bug has been raised","2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-891
4/23: CITYLANDPRS-1537
CITYLANDPRS-1538"
1273-005,[RS CREATION] Creation of RS - Creating RS with category selected is project,Critical,Validate user should be able to create a Requisition Slip with multiple items (more than 10 items),"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Login. Check pre-requisite for roles with access
2. Click Dashboard
3. Click 'New Request'
4. Populate the the required fields 
5. Add multiple or more than 10 items
6. Click Submit",,1.Should be able to create RS with multiple items,,Passed,,
1273-006,[RS CREATION] Creation of RS - Creating RS with category selected is project,Critical,Validate user should be able to create a Requisition Slip with multiple items with steelbars,"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Login. Check pre-requisite for roles with access
2. Click Dashboard
3. Click 'New Request'
4. Populate the the required fields 
5. Add multiple or more than 10 items with steelbars
6. Click Submit",,1.Should be able to create RS with steelbar items,,Passed,,
1273-007,[RS CREATION] Validating error message for empty fields,High,Validate error message returns when required fields are empty,"1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items
2. The following role will have access to create RS or create New Request from Dashboard:-
IT Admin
Engineers
Supervisor
Assistant Manager
Department Head
Division Head
Area Staff
Purchasing Staff
Purchasing Head
Management","1. Login
2. Click Dashboard
3. Click 'New Request'
4. Leave the required fields empty.
   a. Category - Required
    b. Type of Request  - Required
        i. OFM
    c. Company  - Required
    d. Project  - Required
    e. Department  - Required
    f. Date Required   - Required
    g. Deliver To
    h. Purpose - Required
    i. Add Note with 100 characters
5. Click Save Draft
6. Validate error message returns ""Please input ONLY: ofm, non-ofm or transfer""
7. Populate 'Type of Request'
8. Click Save Draft. Validate error message returns ""Invalid date""
9. Populate Date  Required
10. Click Save Draft. Validate error message returns ""Create RS Purpose must not be Empty""
11. Populate Purpose with 50 characters and alpanumeric such as ,.-'
12. Click Save Draft. Validate error message returns ""Invalid charge to (category)""
13. Populate Charge To (Category)
14. Click Save Draft. Validate error message returns ""Charge to (client) is required""
15. Populate Charge To (Client)
16. Click Save Draft. Validate error message returns ""Department not found""
17. Populate Department
18.  Click Save Draft. Validate error message returns ""Project not found""
19. Populate Project
20. Click Save Draft. Validate error message returns ""Company not found""
21. Populate Company
22. Click Save Draft. Validate green pop-up message returns ""Draft created successfully""
23. Click Submit
24. Validate green pop-up message returns ""Requisition Submitted successfully""","   a. Category - <empty>
   b. Type of Request  - <empty>
    c. Company  - <empty>
    d. Project  - <empty>
    e. Department  - <empty>
    f. Date  - <empty>
    g. Purpose - <empty>",Error message with associated field should return on every field in question,,Failed,"2/28: Able to create but w/ Bug raised
4/24: Purpose text field allow only 50 characters","2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-980

4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1535"
1273-008,[RS CREATION] Validating error message for invalid input,High,Validate error message returns when input is invalid,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Login
2. Click Dashboard
3. Click 'New Request'
4.  Try to input 101 or more Characters in ""Purpose""
5. Validate error message returns ""Create RS Purpose maximum 100 characters""
6. Validate you cannot input more than 100 characters in notes",,Error message will return for purpose and input has limitation for Notes,,Failed,"4/24 Error message displays ""Create RS Purpose maximum of  50 characters""", 4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1535
1273-009,[RS CREATION] Validating past date is not allowed,Critical,Validate past date is not allowed,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Login
2. Click Dashboard
3. Click 'New Request'
4. Click past dates on Date Required
5. User should not be able to select past dates and past months/years",,User should not be able to select past dates  and past months/years,,Passed,,
1273-010,[RS CREATION]  Uploading additional attachment works in Draft RS,Critical,Validate uploading additional attachment works in Draft RS,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Login
2. Click Dashboard
3. Upload 2 files
4. Save as Draft
5. Draft should be successful with 2 files
6. Click same RS
7. Upload another 2 files
8. Draft should be successful and display the additional 2 files
9. Re-open same RS and Validate there are total of 4 files",,Successfully saved as Draft and additional attachment is reflected with scroll bar,,Passed,3/3: Issue has been logged,3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989
1273-011,[RS CREATION] Scroll bar for multiple attachments,,Validate scroll bar for multiple attachments,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Login
2. Click Dashboard
3. Click New Request
4. Upload 15 files at the same time
5. Validate scroll bar
6. Click Save Draft
7. Open saved RS
8. Validate scroll bar",,Scroll bar should be present when multiple files were uploaded,,Passed,,
1273-012,[RS CREATION] Removing attachment works in draft RS,High,Validate remove attachment works in draft RS,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Login
2. Click Dashboard
3. Click New Request
4. Upload 5 files at the same time
5. Delete 2 files by clicking 'x'
6. Click Save Draft
7. Open same RS
8. Validate only 2 files were uploaded
9. Delete 1 file again
10. Click Save Draft
11. Validate only 1 file is present on attachment",,Delete file should work and reflect on draft RS,,Passed,3/3: Issue has been logged,3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989
1273-013,[RS CREATION]  Removing Attachment in Requisition Slip,High,Validate user should be able to remove an Attachment made to the Requisition Slip,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft
4. Attachment has been uploaded","1. Open RS
2. Click Check Attachments button
3. In attachment modal view, remove a file by clicking 'X' icon
4.  Should display a Confirmation Modal once clicked
5.Once Confirmed, should remove the Attachment from the Requisition Slip
6. Should Log the Action in the System Audit Logs and Request History",,"Remove attachment should work for RS with the following status:

submitted
for-approval
assigning
assigned
partially-canvassed
canvass-approval
purchase-order
partially-ordered
transfer
pending
on-hold
returned
paying",,Passed,"unable to remove attachemnts
om/issue/CITYLANDPRS-808",
1273-014,[RS CREATION]  Attachment cannot be removed for Cancelled and Closed RS status ,Critical,Validate attachment cannot be removed for Cancelled and Closed RS status ,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft
4. Attachment has been uploaded","1. Create 1 Requisition Slip with 3 attachments till you reach each status below or find RS with below status:-

closed
cancelled
2. Open RS
3. Click Check Attachments button
4. In attachment modal view, remove a file by clicking 'X' icon
5. Validate user should not be able to remove an attachment",,"User should not be able to remove an attachment for RS with status as:
closed
cancelled",,Passed,3/3: able to delete attachment,3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1008
1273-015,[RS CREATION]  Delete attachment on Draft RS ,High,Validate delete attachment on Draft RS works,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft
4. Attachment has been uploaded","1. Click Dashboard > New Request
2. Add 3 attachments
3. Remove 1 attached file or Click 'x''
4. Validate attachment is removed
5. Click Save Draft
6. Click Go back to dashboard
7. Open the draft RS created, verify there are only 2 attached files",,Attachment is deleted for Draft RS,,Passed,3/3: Issue has been logged,3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989
1273-016,[RS CREATION]  Draft Requisition Slip created,High,Validate draft Requisition Slip is created,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
","1. Login
2. Click Dashboard
3. Click New Request
4. Populate required fields
5. Click Save Draft
6. Go back to Dashboard
7. Validate the RS you created has draft status
8. Validate the RS Number format is in TMP-************ or ex: TMP-12AA00000011",,"1. Should display RS Creation Form
2. Should be able to select any Type of Request
3. Should be able to populate all fields
4. Should display a Modal for adding of Items
5. Should be able to add items in the item list
6. Quantity and Notes should be populated
7. Should be able to submit RS
8. RS Created should be the ff:
           a. Requisition Slip can be edited by the Approver
           b.  Requisition Slip is For Approval
           c.  Requisition Slip should have an RS Number
                i. Format - RS-[AA-ZZ]-[********]",,Passed,,
1273-017,[RS CREATION] Updating Draft RS is successful,High,Validate updating Draft RS is successful,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Click an existing RS with Draft status
2. Update the values of the following fields
Charge to (Category)
Charge to (Client)
Date Required
Type of Request
Company
Project
Department
Deliver to
Purpose
Attachment/s
Notes
3. Click Save Draft
4. Validate table has updated value for all columns
5. Open created Draft RS
6. Validate all fields are updated with new value",,Update is successful and reflects on the table and on the Requisition Slip form/page,,Passed,,
1273-018,[RS CREATION]  Uneditable fields on Submitted RS,Minor,Validate uneditable fields on Submitted RS,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Click Dashboard > New Request
2. Populate all required fields > Save as Draft > Submit
3. Open submitted RS
4. Validate fields are disabled and cannot be updated:
Type of Request
Date Required
Company
Project
Department
Purpose
Deliver To
Charge To

5. Validate buttons are enabled and can view:
attachments
notes",,"Following Fields should not be editable: 
-Type of Request
-Date Required
-Company
-Project
-Department
-Purpose
-Deliver To
-Charge To",,Passed,,
1273-019,[RS CREATION] No duplicate Requisition Slip or Ref. Number ,Critical,Validate there are no duplicate Requisition Slip or Ref. Number created and displayed on the table,1. User has successfully logged in to their Account,"1. Click dashboard
2. Validate table that there are no duplicates on Ref. Number column",,No duplicate Requisition Slip or Ref. Number created,,Passed,,3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-951
1273-020,[RS CREATION] Validating Dropdown Search,Critical,Dropdown search should work,1. User has successfully logged in to their Account,"NOT IMPLEMENTED

1. Validate Dropdown search should work for Category, Type of Request, Company, Project, Department, Deliver to",,Dropdown search should work,,Passed,"4/24: Dropdown serqch for typeof request is not exist
4/28: Retest passed in staging","4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1539
"
1273-021,[RS CREATION] Attachment and Notes added on Draft RS,Critical,Validate attachment and notes added should be available on Draft RS,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. On Dashboard, click New Request
2. Populate all required fields
3. Upload 1 file on attachment
4. Add notes
5. Click Save draft
6. Go back to dashbard
7. Open created drafted RS
8. Validate that attachment is retained
9. Validate that notes is retained",,Attachment and Notes initially added should retain when user re-opens draft RS,,Failed,"CITYLAND-807
3/3: Current behavior only attachments should retained in draft RS. Notes will displayed upon submit RS
4;24: Notes not saved in draft",4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1532
1273-022,[RS CREATION] Notes accepts emoji,Minor,Validate notes accepts emoji,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Create a RS
2. Populate all required fields
3. Add emoji on notes
4. Save as Draft
5. Click Submit
6. Open RS
7. Click Check Notes
8. Open notes and validate emoji is added",,Emoji should not added on Notes,,Passed,"able to accept emoji
CITYLANDPRS-738

3/3: still accepts emoji",3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-582
1273-023,[RS CREATION]  Timestamp gets updated every modification on Draft RS,High,Validate timestamp gets updated every modification on Draft RS,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. On Dashboard, click New Request
2. Populate all required fields. Save as Draft
3. Open draft RS
4. Upload attachment
5. Click Save
6. Timestamp at the bottom should change based on the time of your latest update


Example: Draft Saved:15 January 2025 | 9:01:44 PM",,Timestamp should get updated based on the time of your latest update,,Passed,"unable to update timestamp
CITYLANDPRS-810

3/3: Still encountered the error",
1273-024,[RS CREATION] Cancelling Requisition Slip,High,Cancel requisition slip should work,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page
3. RS is saved as Draft","1. Dashboard > Click New Request
2. Click Cancel button
3. Validate pop-up msg ""You are about to cancel this request. Press continue if you want to proceed with this action.""
4. Click ''Continue"" and you will be redirected to Dashboard page
5. Click New Request again
6. Populate all required fields
7. Save as draft. Draft saved successfully
8. Update purpose
9. Click Cancel
10. Validate pop-up msg ""You are about to cancel this request. Press continue if you want to proceed with this action.""
11. Re-open draft RS
12. Validate updated purpose do not reflect",,"1. RS will not be created when user cancel the request upon creation
2. Changes do not reflect when user modify draft RS and clicks cancel",,Passed,,
1273-025,[RS CREATION] Creating RS even when 1 RS Approver in Project has been deleted,High,Validate that requestor can still create RS even when 1 of RS approver in Project has been deleted,1. User has successfully logged in to their Account,"1. Login as admin
2. Click Manage > Project 
3. Delete 1 approver from ALABANG HEIGHTS or any project
4. Create and Submit RS using ALABANG HEIGHTS as Charge to (Client) and Project
5. Validate admin/requestor will be able to create RS successfully
6. Validate that the latest approvers appears on submitted RS",,Admin/requestor will be able to create RS successfully with the latest project approvers,,Passed,,
1273-026,[RS CREATION] Audit Log Entry is Created in the Database for RS Creation,High,Verify Audit Log Entry is Created in the Database for RS Creation,1. User has successfully logged in to their Account,"1. Log in as a valid user (e.g., Requester).
2. Navigate to the Requisition Slip creation page.
3. Fill out the form with valid data (e.g., department/project, items, quantities).
4. Submit the RS.
5. Query the audit logs table (audit_logs or equivalent) for the specific RS ID.
Example: SELECT * FROM audit_logs WHERE entity_id = '<RS_ID>' AND action = 'CREATE';",,"A new row exists in the audit table with:
-Action = ""CREATE""
-Entity = ""RS"" or ""RequisitionSlip""
-Entity ID = ID of the RS just created
-Created by = User ID who created the RS
-Timestamp = Time of creation",,Passed,4/30: tested audit logs in RS create,
[RS Creation] - Filtering of RS Fields,,,,,,,,,,,
1273-024,[RS Creation] - Filtering of RS Fields - Project Filter based on Selected Company,High,Validate Filter Projects Based on Selected Company,"1. Multiple Companies and Projects exist. 
2. Projects are associated with Companies.","1. Select a Company from the Company dropdown.
2. Open the Project dropdown.",,Only Projects associated with the selected Company are displayed.,https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********,Passed,,
1273-025,[RS Creation] - Filtering of RS Fields - Project Filter based on Selected Association,High,Validate Filter Projects Based on Created Association,1. Associations exist between users and Projects.,"1. Do not select a Company.
2. Open the Project dropdown.",,Only Projects that have an Association with the current user are displayed.,,Failed,,4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1537
1273-026,[RS Creation] - Filtering of RS Fields - Project Filter based on Selected Project,High,Validate Change Company After Project Selection,1. A Project and Company are selected.,"1. Select a Project from the dropdown.
2. Change the selected Company.",,"The selected Project is cleared.

Project dropdown options are re-filtered based on the new Company.",,Passed,,
1273-027,[RS Creation] - Filtering of RS Fields - Select Project and Auto-fill Company,Critical,"Validate Select Project First, Auto-Fill Company",1. Projects are associated with specific Companies.,1. Select a Project from the dropdown.,,The corresponding Company is automatically filled in the Company field.,,Failed,,4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 
1273-028,[RS Creation] - Filtering of RS Fields - Replacing Company after Project Auto-fill,Critical,Validate  Replace Company After Project Auto-Fill,1. Projects are associated with specific Companies.,"1. Select a Project (auto-fills Company).
2. Replace the auto-filled Company with a different one.",,"The selected Project is cleared.

Project options are re-filtered based on the new Company.",,Failed,,4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 
1273-029,[RS Creation] - Filtering of RS Fields - Editable Company Field,High,Validate Editable Company Field,1. Projects are associated with specific Companies.,1. Click on the Company field.,,All Companies (including Associations) are searchable and selectable.,,Passed,,
1273-030,[RS Creation] - Filtering of RS Fields - Search Functionality in Project Dropdown,High,Validate Search Functionality in Project Dropdown,1. Company is selected.,"1. Open the Project dropdown.
2. Type a keyword in the search box.",,Matching Projects (based on the keyword and current filter) are displayed in real time.,,Passed,,
1273-031,[RS Creation] - Filtering of RS Fields - Select Invalid Project,High,Validate Select Invalid Project (Not Associated with Selected Company),1. Company is selected.,"1. Select a Company (e.g., CDC - CITYLAND DEVELOPMENT CORPORATION).
2. Attempt to manually force-select a Project related to a different Company (e.g., Project from BGC RESIDENCES, via dropdown manipulation).",,"Project selection is blocked or cleared automatically.

Error or warning shown: ""Selected Project is not associated with this Company.""",,Passed,,
1273-032,[RS Creation] - Filtering of RS Fields - Change Company After Selecting Valid Project,Critical, Validate Change Company After Selecting Valid Project,1. Project is selected.,"1. Select a valid Project.
2. Change the Company to another unrelated one.
3. Try to proceed without the Project being cleared.",,"The system should automatically clear the Project selection.

Validation error: ""Selected Project is not valid for this Company.""",,Passed,,
1273-033,[RS Creation] - Filtering of RS Fields - Auto-Filled Company Does Not Match Selected Project,Critical,Validate Auto-Filled Company Does Not Match Selected Project,1. Project is selected.,"1. Select a Project first (auto-fills the Company).
2. Manually override the auto-filled Company with a non-matching one.
3. Attempt to submit the form.",,"Project field is cleared.

Validation prevents submission until a matching Project is selected.",,Failed,,4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 
1273-034,[RS Creation] - Filtering of RS Fields - Search for Non-Existent Project,High,Search for Non-Existent Project,1. Company is selected.,"1. Select a Company.
2. Open the Project dropdown.
3. Enter a keyword that matches no associated Projects.",,"Dropdown displays “No results found.”

Prevents selecting an invalid Project.",,Passed,,
1273-035,[RS Creation] - Filtering of RS Fields - Remove Auto-Filled Company Manually,High,Remove Auto-Filled Company Manually,1. Project is selected.,"1. Select a Project first (auto-fills Company).
2. Manually remove the Company field value.
3. Attempt to proceed without selecting another Company.",,"Project is cleared or form cannot be submitted.

Error: ""Company is required when a Project is selected.""
",,Failed,,4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 
1273-036,[RS Creation] - Filtering of RS Fields - Selected Request Type ,Minor,Validate Selected Request Type – OFM or OFM Transfer of Materials,"1. Multiple Companies and Projects exist. 
2. Projects are associated with Companies.","1. Open Requisition Slip Form.

2. Select ""OFM"" or ""OFM Transfer of Materials"" from the Type of Request dropdown.",,The correct request type is selected and form dynamically updates if needed.,,Passed,,
1273-037,[RS Creation] - Filtering of RS Fields - Add Item Button – Launch Item Modal,High,Validate Add Item Button – Launch Item Modal,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Click the ""Add Item"" button.",,Modal for adding items appears.,,Passed,,
1273-038,[RS Creation] - Filtering of RS Fields - Filter Items by Selected Trade and Project,High,Validate Filter Items by Selected Trade and Project,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select Trade and Project in the form.
2. Open the Item modal.",,"Only OFM Items related to the selected Trade and Project are shown in the modal.

Unrelated items are not displayed.",,Passed,"4/25: all items displayed and not only items related to project and trade 
4/29: Retest passed",4/25: CITYLANDPRS-1543
1273-039,[RS Creation] - Filtering of RS Fields - Select Item List and Individual Items,High,Validate Select Item List and Individual Items,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Inside the modal, select an Item List.
2. Select specific items from the list.
3. Click ""Add Items"".",,Selected items are displayed in the Items Table section.,,Passed,,
1273-040,[RS Creation] - Filtering of RS Fields - Cancel Button – No Items Added,High,Validate Cancel Button – No Items Added,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Inside the modal, select one or more items.
2. Click Cancel instead of Add.",,No items are added to the Items Table.,,Passed,,
1273-041,[RS Creation] - Filtering of RS Fields -  Input Quantity per Item,High,Validate Input Quantity per Item,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. In the Items Table, enter a quantity for each listed item.",,"Quantity input accepts valid values (e.g., positive integers).

Input field is editable.",,Passed,,
1273-042,[RS Creation] - Filtering of RS Fields - Display Remaining GFQ,Minor,Validate Display Remaining GFQ (Goods for Quotation),"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. After entering quantity, check for Remaining GFQ value.",,System displays the correct remaining GFQ per item based on available quantity.,,Passed,,
1273-043,[RS Creation] - Filtering of RS Fields - Remove Individual Item,High,Validate Remove Individual Item,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. In the Items Table, click the remove/delete icon for a specific item.",,"The item is removed from the table.

Total or related values update accordingly.",,Passed,,
 [RS Creation] - Update Charge To Function ,,,,,,,,,,,
1273-044,[RS Creation] - Update Charge To Function -   Set Charge To Fields as Optional,Critical,Validate Set Charge To Fields as Optional,1. User has successfully logged in to their Account,"1. Open the requisition slip form.
2. Do not fill in the Charge To field.
3. Save as a draft.",,Form saves successfully without requiring the Charge To field.,https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********,Passed,,
1273-045,[RS Creation] - Update Charge To Function -  Allow Selection of Different Charge To Based on Situation,Critical,Validate Allow Selection of Different Charge To Based on Situation,1. User has successfully logged in to their Account,"1. Open a new requisition slip.
2. Select different categories (Company, Association, Project, Supplier).
3. For each category, check the available options in the Charge To dropdown.",,Charge To options change appropriately based on selected category.,,Passed,,
1273-046,"[RS Creation] - Update Charge To Function -   Category selected as Company, but No Company in Request Details",High,"Validate Category selected as Company, but No Company in Request Details","1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select Company category.
2. No company is selected in Request Details.
3. Click submit.",,Error Message to set company in Request Details,,Failed,"4/ 25 No error message appeared
Unable to click Submit button","4/28

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636"
1273-047,"[RS Creation] - Update Charge To Function -  Category selected as Association, but Request Details is Missing Association",High,"Validate Category selected as Association, but Request Details is Missing Association","1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select Association category.
2. No company is selected in Request Details.
3. Click submit.",,Error Message to set Associationin Request Details,,Failed,"4/ 25 No error message appeared
Unable to click Submit button","4/28

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636"
1273-048,"[RS Creation] - Update Charge To Function -  Category selected as Project, but No Project in Request Details",High,"Validate Category selected as Project, but No Project in Request Details","1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select Project category.
2. No company is selected in Request Details.
3. Click submit.",,Error Message to set Project in Request Details,,Failed,"4/ 25 No error message appeared
Unable to click Submit button","4/28

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636"
1273-049,"[RS Creation] - Update Charge To Function -  Category selected as Company, but No Supplier is Selected",High,"Validate Category selected as Company, but No Supplier is Selected","1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select Supplier category.
2. No company is selected in Request Details.
3. Click submit.",,Error Message to set Supplier in Request Details,,Failed,"4/ 25 No error message appeared
Unable to click Submit button","4/28

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636"
1273-050,[RS Creation] - Update Charge To Function -   Behavior of Charge To Based on Category,High,Validate Behavior of Charge To Based on Category,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select Company as the category.
2. Submit the requisition slip.
3. Open the requisition in the Approval view.",,Charge To field auto-populates with the selected company from Request Details.,,Passed,,
1273-051,[RS Creation] - Update Charge To Function -   Filter Charge To Options Based on Selected Category,High,Validate Filter Charge To Options Based on Selected Category,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Select each Charge To (Category).
2. Observe the options shown in the Charge To dropdown.",,Only valid options matching the category are displayed.,,Passed,,
1273-052,"[RS Creation] - Update Charge To Function -  Rename ""Charge To (Client)"" to ""Charge To""",Minor,"Rename ""Charge To (Client)"" to ""Charge To""","1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Open the requisition slip form.
2. Look for the ""Charge To"" label.",,"The label reads ""Charge To"" instead of ""Charge To (Client)"".",,Passed,,
1273-053,[RS Creation] - Update Charge To Function -   Behavior in Draft View,High,Validate Behavior in Draft View,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Save a requisition slip as a draft with all Charge To fields filled.
2. Reopen the draft.",,All Charge To values persist and behave as expected based on their category.,,Passed,,
1273-054,[RS Creation] - Update Charge To Function -  Behavior in Approver's View,High,Validate Behavior in Approver's View,1. Requisition slip is already created. ,"1. Log in as an approver.
2. Open the requisition.",,Charge To behavior is consistent with user selection and category logic.,,Passed,,
1273-055,[RS Creation] - Update Charge To Function -  Requisition Slip Creation with Enlarged Add Items Modal ,Minor,Validate Requisition Slip Creation with Enlarged Add Items Modal,"1. User is logged in and has permission to create a Requisition Slip
2. User has access to the Requisition Slip form page","1. Navigate to the Requisition Slip Form
2. Click ""Create New Requisition Slip""
3. Fill in the Request Details section (e.g., Request Type, Project, etc.)
4. Click the Add Items button
5. Observe the size of the modal",,"Modal should be larger than the standard/default modal size, providing ample space for displaying item lists",,Failed,"4/25 Modal Size is not larger than
the standard modal size, or does
not match Modal design in Figma","4/25

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1293"
1273-056,[RS Creation] - Update Charge To Function - Items Table Responsive Spacing in Requisition Slip View,Minor,Validate Items Table Responsive Spacing in Requisition Slip View,"1. Requisition Slip has been saved (either as Draft or Submitted)
2. The slip includes at least one item in the Items Table","1. Open a Draft or Submitted Requisition Slip
2. Observe the Items Table section
3. View a slip with multiple items (e.g., 5+)
4. Resize the viewport or test on different screen sizes",,Table should remain readable and not overflow or create scroll issues unnecessarily,,Passed,,
1273-057,[RS Creation] - Update Charge To Function - Account Code column is no longer visible,Minor,Validate Account Code column is no longer visible,"1. User has access to view a Requisition Slip (Draft or Submitted)

2. At least one item exists in the Items Table","1. Open any Requisition Slip with items listed
2. Observe the Items Table Columns
3. Locate the Item Column",,"The Account Code column is no longer visible. 

Should display the Full Item name in the Item Column
    a. Item Name
    b. Unit
    c. Remaining GFQ
    d. Quantity
    e. Notes
    f. Actions
        i. Remove",,Failed,"4/25 Full Item name is not 
displayed
","4/25

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1542"
1273-058,[RS Creation] - Update Charge To Function - Character Limit for Notes,Minor,Validate Character Limit for Notes,"1. Requisition Slip is created
2. Items are added to the requisition.","1. Enter text in the item notes field.
2. Confirm that up to 500 characters are accepted.
3. Attempt to enter more than 500 characters.",,Notes accept up to 500 characters.,,Failed,"4/25 Items Table notes only 
accepts 100 characters instead
of 500 characters","4/25

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1544"
1273-059,[RS Creation] - Update Charge To Function -  Requisition Slip Submit Without Any Notes,High,Validate Requisition Slip Submit Without Any Notes,"1. Requisition Slip is created
2.  Items are added without any notes.","1. Leave all notes fields empty.
2. Submit the Requisition Slip.",,The system allows submission without any item notes.,,Passed,,
1273-060,[RS Creation] - Update Charge To Function - Status Update from Submitted to For Approval,High,Validate Status Update from Submitted to For Approval,1. A Requisition Slip is in Submitted status.,"1. Submit a Requisition Slip.
2. Wait for system rules to process the submission.",,Status is updated from Submitted to For Approval.,,Out of Scope,"4/25 Status is ""Submitted"" instead
of ""For RS Approval""",
1273-061,[RS Creation] - Update Charge To Function -  Approvers Are Assigned When RS is in For Approval Status,High,Validate Approvers Are Assigned When RS is in For Approval Status,1. Requisition Slip is in For Approval status.,"1. View the RS with “For Approval” status.
2. Check the assigned approvers or approval flow.",,For Approval Status should now be the basis for the Approvers of the RS Approval,,Out of Scope,"4/25 No Status of ""For RS 
Approval"" displayed",
 [RS Creation] - Item UI and Function Updates,,,,,,,,,,,
1273-062, [RS Creation] - Item UI and Function Updates - Search Field is Not Displayed,Minor,Verify Search Field is Not Displayed,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Scroll to the Items Table section.
2. Inspect the table header and top controls.",,"No Search Field is visible.

Table functionality remains unaffected.",Cherry_Sprint1_Test Result,Passed,,
1273-063, [RS Creation] - Item UI and Function Updates - Scroll and Layout in Draft View,High,Verify Scroll and Layout in Draft View,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Fill out the requisition and save as draft.
2. Reopen the draft.
3. Scroll to Items Table.",,"Scroll behavior and layout are consistent.

Search Field is still not present.

Data in Items Table is retained.",,Passed,,
1273-064, [RS Creation] - Item UI and Function Updates - Scroll and Section Visibility in Approval View,Minor,Verify Scroll and Section Visibility in Approval View,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Submit a requisition.
2. Log in as an approver and open the requisition.
3. Scroll to Items Table.",,"Items Table section is visible.

No Search Field present.

Table content is read-only but fully accessible.",,Failed,4/23: Minor bug for Items table displayed,4/23: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1533
[RS Creation] -Attachments and Notes Placement,,,,,,,,,,,
1273-065, [RS Creation] -Attachments and Notes Placement - Section Sequence in Requisition Slip Form,High,Validate Section Sequence in Requisition Slip Form,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Open a new Requisition Slip Form.
2. Observe the layout and ordering of the sections.",,"Section order should be:
-Request Details
-Charge To
-Items Table
-Attachments and Notes",Cherry_Sprint1_Test Result,Passed,,
1273-066, [RS Creation] -Attachments and Notes Placement - Retain Functionality of Uploading Attachments,Minor,Validate Retain Functionality of Uploading Attachments,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Go to the Attachments and Notes section.
2. Upload 1 file with 25 MB in size.",,File uploads successfully and is visible in the list.,,Passed,,
1273-067, [RS Creation] -Attachments and Notes Placement - Retain Functionality of Adding Notes,Minor,Validate Retain Functionality of Adding Notes,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Add Note with 100 characters.
2. Save or submit the form.",,Notes are saved and retained.,,Failed,4/24: Notes limitto 500 characters,4/24 https://youtrack.stratpoint.com/issue/CITYLANDPRS-1522
1273-068, [RS Creation] -Attachments and Notes Placement - Retain Functionality of Remove/Edit Attachments,Minor,Validate Retain Functionality of Remove/Edit Attachments,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Upload a file.
2. Remove before clicking submit.",,Should be able to remove or replace attachments before final submission.,,Passed,,
1273-069, [RS Creation] -Attachments and Notes Placement - Retain Functionality of Attachment Type/Size Validation,Minor,Validate Retain Functionality of Attachment Type/Size Validation,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Upload incorrect file type (e.g., .exe) or large file (over 25 MB).",,"Validation message ""The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV""",,Passed,,
1273-070, [RS Creation] -Attachments and Notes Placement - Behavior in Draft View,High,Validate Behavior in Draft View,"1. User has successfully logged in to their Account
2. User is in Requisition Slip Page","1. Fill all sections of the form.
2. Save as draft.
3. Reopen the draft.",,"All data persists correctly.

Section layout and sequence is intact.",,Passed,,
1273-071, [RS Creation] -Attachments and Notes Placement - Behavior in Approver View – Read-Only but Fully Visible,High,Validate Behavior in Approver View – Read-Only but Fully Visible,"1. User has successfully logged in to their Account
","1. Submit a requisition.
2. Log in as an approver.
3. Open the form.",,"All sections (Request Details → Charge To → Items Table → Attachments and Notes) are shown in the same order.

Attachments can be viewed/downloaded.

Notes are readable.

Fields are read-only.",,Passed,,