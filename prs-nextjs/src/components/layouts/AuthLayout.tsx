"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { signOut } from "next-auth/react";

interface AuthLayoutProps {
  children: React.ReactNode;
  navigateVisible?: boolean;
}

export function AuthLayout({
  children,
  navigateVisible = false
}: AuthLayoutProps) {
  const handleBack = () => {
    signOut({ callbackUrl: "/auth/login" });
  };

  return (
    <div className="flex h-screen bg-gray-100">
      <div className="m-auto bg-white rounded-lg shadow-lg overflow-hidden w-[56rem] sm:h-[40rem]">
        <div className="flex h-full">
          <div className="w-1/2 relative">
            <div
              className="absolute inset-0 bg-cover bg-center brightness-150"
              style={{ backgroundImage: `url(/cityland-bg-form.png)` }}
            ></div>
            <div className="absolute inset-0 bg-black bg-opacity-50"></div>
            <div className="absolute inset-0 flex flex-col justify-between p-8 text-white mx-4 my-6">
              <div className="flex flex-col gap-6 items-center sm:flex-row">
                <Image
                  src="/cityland-logo-plain.png"
                  alt="Cityland Logo"
                  width={48}
                  height={48}
                />
                <h2 className="text-xl">Purchase Request System</h2>
              </div>
              <div>
                <div className="flex flex-col gap-4">
                  <Image
                    src="/speaker-notes.png"
                    alt="Speaker Notes"
                    width={64}
                    height={64}
                  />
                  <h3 className="text-2xl leading-tight font-bold mb-2 sm:text-[2.5rem]">
                    Manage Requests
                    <br /> With Ease
                  </h3>
                  <p className="text-sm">
                    Streamline your purchase requests. Track, approve, and
                    manage your requests in one place with the PRS.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="w-1/2 px-8 py-12 mx-6 flex flex-col justify-between h-full">
            <div className="flex justify-center mb-8">
              <Image
                src="/cityland-logo.png"
                alt="Cityland Logo"
                width={71}
                height={76}
                className="mx-auto"
                style={{ objectFit: 'contain' }}
              />
            </div>
            <div className="flex-grow flex flex-col">
              <div className="flex flex-col justify-center flex-grow">
                {children}
              </div>
            </div>

            {navigateVisible && (
              <Button
                type="button"
                variant="link"
                hover="link"
                onClick={handleBack}
                className="mb-4"
              >
                Back to login
              </Button>
            )}

            <div className="flex flex-col gap-1">
              <div className="text-center text-xs text-[#420001]">
                <p>2024 Cityland Group of Companies</p>
                <p>All Rights Reserved</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
