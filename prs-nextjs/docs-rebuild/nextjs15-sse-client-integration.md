# Client-Side SSE Integration in Next.js 15

This document provides a comprehensive guide on integrating Server-Sent Events (SSE) on the client side in the Next.js 15 PRS application.

## Table of Contents

1. [Client-Side SSE Components](#client-side-sse-components)
2. [Notification Listener](#notification-listener)
3. [Approval Status Listener](#approval-status-listener)
4. [Dashboard Updates Listener](#dashboard-updates-listener)
5. [Delivery Tracking Listener](#delivery-tracking-listener)
6. [Error Handling and Reconnection](#error-handling-and-reconnection)
7. [Performance Optimization](#performance-optimization)
8. [Testing SSE Connections](#testing-sse-connections)

## Client-Side SSE Components

### 1. Base SSE Listener Component

```tsx
// components/sse/BaseSSEListener.tsx
'use client'

import { useEffect, useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'

interface BaseSSEListenerProps {
  endpoint: string
  onEvent: (eventData: any) => void
  eventName?: string
  enabled?: boolean
}

export function BaseSSEListener({
  endpoint,
  onEvent,
  eventName,
  enabled = true
}: BaseSSEListenerProps) {
  const { data: session, status } = useSession()
  const [eventSource, setEventSource] = useState<EventSource | null>(null)
  
  const connect = useCallback(() => {
    if (!enabled || status !== 'authenticated') return
    
    // Close existing connection if any
    if (eventSource) {
      eventSource.close()
    }
    
    // Create new EventSource connection
    const sse = new EventSource(endpoint)
    
    // Handle specific event if eventName is provided
    if (eventName) {
      sse.addEventListener(eventName, (event) => {
        try {
          const data = JSON.parse(event.data)
          onEvent(data)
        } catch (error) {
          console.error('Error parsing SSE event data:', error)
        }
      })
    } else {
      // Handle default message event
      sse.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onEvent(data)
        } catch (error) {
          console.error('Error parsing SSE event data:', error)
        }
      }
    }
    
    // Handle connection error
    sse.onerror = (error) => {
      console.error('SSE connection error:', error)
      sse.close()
      
      // Attempt to reconnect after a delay
      setTimeout(() => {
        connect()
      }, 5000)
    }
    
    setEventSource(sse)
    
    return () => {
      sse.close()
    }
  }, [endpoint, onEvent, eventName, enabled, status, eventSource])
  
  useEffect(() => {
    const cleanup = connect()
    
    return () => {
      if (eventSource) {
        eventSource.close()
      }
      if (cleanup) {
        cleanup()
      }
    }
  }, [connect])
  
  return null
}
```

## Notification Listener

### 1. Notification Listener Component

```tsx
// components/notifications/NotificationListener.tsx
'use client'

import { useState, useEffect } from 'react'
import { BaseSSEListener } from '@/components/sse/BaseSSEListener'
import { useToast } from '@/components/ui/use-toast'

interface Notification {
  id: number
  type: string
  title: string
  message: string
  referenceId: number
  referenceType: string
  read: boolean
  createdAt: string
}

export function NotificationListener() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const { toast } = useToast()
  
  const handleNotifications = (newNotifications: Notification[]) => {
    // Find notifications that weren't in the previous state
    const previousIds = new Set(notifications.map(n => n.id))
    const brandNewNotifications = newNotifications.filter(
      n => !previousIds.has(n.id)
    )
    
    // Show toast for new notifications
    brandNewNotifications.forEach(notification => {
      toast({
        title: notification.title,
        description: notification.message,
        variant: 'default',
      })
    })
    
    // Update state with all notifications
    setNotifications(newNotifications)
  }
  
  return (
    <>
      <BaseSSEListener
        endpoint="/api/sse/notifications"
        onEvent={handleNotifications}
        eventName="notifications"
      />
      
      {/* Notification indicator */}
      {notifications.length > 0 && (
        <div className="relative">
          <button className="p-2">
            <BellIcon className="h-6 w-6" />
            <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
              {notifications.length}
            </span>
          </button>
        </div>
      )}
    </>
  )
}

// Bell icon component
function BellIcon(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
      />
    </svg>
  )
}
```

### 2. Notification Display Component

```tsx
// components/notifications/NotificationList.tsx
'use client'

import { useState, useEffect } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { BaseSSEListener } from '@/components/sse/BaseSSEListener'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { markNotificationAsRead } from '@/app/notifications/actions'

interface Notification {
  id: number
  type: string
  title: string
  message: string
  referenceId: number
  referenceType: string
  read: boolean
  createdAt: string
}

export function NotificationList() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isOpen, setIsOpen] = useState(false)
  
  const handleNotifications = (newNotifications: Notification[]) => {
    setNotifications(newNotifications)
  }
  
  const handleMarkAsRead = async (id: number) => {
    await markNotificationAsRead(id)
    
    // Update local state
    setNotifications(prev => 
      prev.map(n => 
        n.id === id ? { ...n, read: true } : n
      )
    )
  }
  
  const handleMarkAllAsRead = async () => {
    const ids = notifications.filter(n => !n.read).map(n => n.id)
    
    if (ids.length === 0) return
    
    await Promise.all(ids.map(id => markNotificationAsRead(id)))
    
    // Update local state
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    )
  }
  
  return (
    <>
      <BaseSSEListener
        endpoint="/api/sse/notifications"
        onEvent={handleNotifications}
        eventName="notifications"
      />
      
      <div className="relative">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
          className="relative"
        >
          <BellIcon className="h-6 w-6" />
          {notifications.filter(n => !n.read).length > 0 && (
            <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
              {notifications.filter(n => !n.read).length}
            </span>
          )}
        </Button>
        
        {isOpen && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20">
            <div className="p-3 border-b flex justify-between items-center">
              <h3 className="text-lg font-medium">Notifications</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsRead}
                disabled={!notifications.some(n => !n.read)}
              >
                Mark all as read
              </Button>
            </div>
            
            <ScrollArea className="h-64">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  No notifications
                </div>
              ) : (
                <ul className="divide-y divide-gray-200">
                  {notifications.map(notification => (
                    <li
                      key={notification.id}
                      className={`p-4 hover:bg-gray-50 ${
                        !notification.read ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex justify-between">
                        <p className="font-medium">{notification.title}</p>
                        <small className="text-gray-500">
                          {formatDistanceToNow(new Date(notification.createdAt), {
                            addSuffix: true
                          })}
                        </small>
                      </div>
                      <p className="text-sm text-gray-600">{notification.message}</p>
                      {!notification.read && (
                        <Button
                          variant="link"
                          size="sm"
                          className="mt-2 p-0 h-auto"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          Mark as read
                        </Button>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </ScrollArea>
          </div>
        )}
      </div>
    </>
  )
}
```
