'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisitions', 'company_name', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('requisitions', 'company_name_locked', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisitions', 'company_name');
    await queryInterface.removeColumn('requisitions', 'company_name_locked');
  }
};
