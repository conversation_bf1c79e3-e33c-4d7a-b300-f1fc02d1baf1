<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="766367493C0" style="width:100px;" class="column-headers-background">A</th><th id="766367493C1" style="width:100px;" class="column-headers-background">B</th><th id="766367493C2" style="width:100px;" class="column-headers-background">C</th><th id="766367493C3" style="width:100px;" class="column-headers-background">D</th><th id="766367493C4" style="width:100px;" class="column-headers-background">E</th><th id="766367493C5" style="width:100px;" class="column-headers-background">F</th><th id="766367493C6" style="width:506px;" class="column-headers-background">G</th></tr></thead><tbody><tr style="height: 19px"><th id="766367493R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0">1. Management Module - Lahat ng sync yung may connection sa integration<br>&gt; Sync Supplier<br>&gt; Sync Company<br>&gt; Sync Department<br>&gt; Sync Project<br>&gt; Sync OFM Items<br>meaning ipupull kay cityland yan<br><br>2. Last is yung payment request module &gt; Payment Submission to Accounting<br>ipapasa kay cityland yung payment request</td></tr><tr style="height: 19px"><th id="766367493R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="766367493R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></tbody></table></div><div id='embed_183791996' class='waffle-embedded-object-overlay' style='width: 538px; height: 222px; display: block;'><img src='https://lh7-rt.googleusercontent.com/sheetsz/AHOq17G_1ua3tzdXUiM3HUBijzVYN8OZM6QqVt0Lg8zfTcMGZuBmjWzIisf_2r2SzDV6igdJu7IJqoQzUDv2vyznB4xIJxTQDybvDzQfGjHaEn7DvZCMo-5DyuETFWyvDmTBzSwJT4RKeEPhfFErG4BSV-E?key=NN19urqwamtM8J1xTnhjCeBh' style='display: block;' height='222' width='538'></div><script>
  function posObj(sheet, id, row, col, x, y) {
      var rtl = false;
      var sheetElement = document.getElementById(sheet);
      if (!sheetElement) {
        sheetElement = document.getElementById(sheet + '-grid-container');
      }
      if (sheetElement) {
        rtl = sheetElement.getAttribute('dir') == 'rtl';
      }
      var r = document.getElementById(sheet+'R'+row);
      var c = document.getElementById(sheet+'C'+col);
      if (r && c) {
        var objElement = document.getElementById(id);
        var s = objElement.style;
        var t = y;
        while (r && r != sheetElement) {
          t += r.offsetTop;
          r = r.offsetParent;
      }
      var offsetX = x;
      while (c && c != sheetElement) {
        offsetX += c.offsetLeft;
        c = c.offsetParent;
      }
      if (rtl) {
        offsetX -= objElement.offsetWidth;
      }
      s.left = offsetX + 'px';
      s.top = t + 'px';
      s.display = 'block';
      s.border = '1px solid #000000';
    }
  }

  function posObjs() {
  posObj('766367493', 'embed_183791996', 1, 0, 0, 0);}posObjs();</script>