/**
 * BaseService class that provides common CRUD operations and utilities
 * for service layer implementations.
 */
class BaseService {
  /**
   * Creates a new BaseService instance
   *
   * @param {Object} container - Dependency injection container
   */
  constructor(container) {
    const { db } = container;

    this.db = db;

    // Import error utilities
    const { ErrorFactory, ErrorTranslator } = require('../errors');
    this.ErrorFactory = ErrorFactory;
    this.ErrorTranslator = ErrorTranslator;

    // For backward compatibility
    const { clientErrors, infraError } = container;
    this.clientErrors = clientErrors;
    this.infraError = infraError;
  }

  /**
   * Executes a callback within a transaction
   *
   * @param {Function} callback - Function to execute within transaction
   * @returns {Promise<any>} - Result of the callback
   * @throws {Error} - If transaction fails
   */
  async withTransaction(callback) {
    const transaction = await this.db.sequelize.transaction();

    try {
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Creates a new record
   *
   * @param {Object} repository - Repository to use for creation
   * @param {Object} data - Data to create
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Created record
   */
  async create(repository, data, options = {}) {
    try {
      return await repository.create(data, options);
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw this.ErrorFactory.conflict(
          'Record with this unique identifier already exists',
          options.resource || 'Record',
          error.errors?.[0]?.path || 'field',
          error.errors?.[0]?.value || 'value',
          error
        );
      }

      throw this.ErrorTranslator.translate(error, {
        operation: 'create',
        resource: options.resource || 'record'
      });
    }
  }

  /**
   * Updates an existing record
   *
   * @param {Object} repository - Repository to use for update
   * @param {Object} where - Conditions to find record
   * @param {Object} data - Data to update
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Updated record
   */
  async update(repository, where, data, options = {}) {
    try {
      const result = await repository.update(where, data, options);

      if (result[0] === 0) {
        throw this.ErrorFactory.notFound(
          options.resource || 'Record',
          JSON.stringify(where),
          'Record not found or no changes made'
        );
      }

      return result;
    } catch (error) {
      // If it's already an AppError, rethrow it
      if (error.name && error.name.endsWith('Error') && error.getHttpStatus) {
        throw error;
      }

      if (error.name === 'SequelizeUniqueConstraintError') {
        throw this.ErrorFactory.conflict(
          'Record with this unique identifier already exists',
          options.resource || 'Record',
          error.errors?.[0]?.path || 'field',
          error.errors?.[0]?.value || 'value',
          error
        );
      }

      throw this.ErrorTranslator.translate(error, {
        operation: 'update',
        resource: options.resource || 'record'
      });
    }
  }

  /**
   * Deletes a record
   *
   * @param {Object} repository - Repository to use for deletion
   * @param {Object} where - Conditions to find record
   * @param {Object} options - Additional options
   * @returns {Promise<number>} - Number of deleted records
   */
  async delete(repository, where, options = {}) {
    try {
      const result = await repository.destroy(where, options);

      if (result === 0) {
        throw this.ErrorFactory.notFound(
          options.resource || 'Record',
          JSON.stringify(where),
          'Record not found'
        );
      }

      return result;
    } catch (error) {
      // If it's already an AppError, rethrow it
      if (error.name && error.name.endsWith('Error') && error.getHttpStatus) {
        throw error;
      }

      throw this.ErrorTranslator.translate(error, {
        operation: 'delete',
        resource: options.resource || 'record'
      });
    }
  }

  /**
   * Gets a record by ID
   *
   * @param {Object} repository - Repository to use for retrieval
   * @param {string|number} id - ID of the record
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Found record
   */
  async getById(repository, id, options = {}) {
    try {
      const result = await repository.getById(id, options);

      if (!result) {
        throw this.ErrorFactory.notFound(
          options.resource || 'Record',
          id,
          'Record not found'
        );
      }

      return result;
    } catch (error) {
      // If it's already an AppError, rethrow it
      if (error.name && error.name.endsWith('Error') && error.getHttpStatus) {
        throw error;
      }

      throw this.ErrorTranslator.translate(error, {
        operation: 'getById',
        resource: options.resource || 'record'
      });
    }
  }

  /**
   * Gets all records with pagination
   *
   * @param {Object} repository - Repository to use for retrieval
   * @param {Object} query - Query parameters
   * @returns {Promise<Object>} - Found records with pagination
   */
  async getAll(repository, query = {}) {
    try {
      return await repository.findAll(query);
    } catch (error) {
      throw this.ErrorTranslator.translate(error, {
        operation: 'getAll',
        resource: query.resource || 'records'
      });
    }
  }
}

module.exports = BaseService;
