# Domain-Driven Design (DDD) Guide for PRS Project

## What is Domain-Driven Design?

Domain-Driven Design (DDD) is simply a way to organize your code around the business problems you're solving, rather than around technical concerns.

### Real-World Analogy: The Hospital

Think of it like organizing a hospital:

- **Traditional Approach (What We Have Now)**: Organizing by technical specialties (all X-ray machines in one department, all blood testing equipment in another, all doctors in another area). A patient has to move between many departments to get treatment.

- **DDD Approach (What We Want)**: Organizing by patient needs (a cardiac care unit has its own X-ray machines, blood testing equipment, and cardiac specialists all working together). Everything needed to solve a specific problem is grouped together.

### Another Analogy: The Restaurant Kitchen

- **Traditional Approach**: Organizing a kitchen by tool type (all knives in one area, all pots in another, all ingredients in another). The chef has to run around the kitchen to make a single dish.

- **DDD Approach**: Organizing by meal preparation (pasta station has its own knives, pots, and ingredients needed for pasta dishes). Everything needed to make a specific type of dish is grouped together.

### What This Means For Our Code

- **Current Structure**: We organize by technical layers (controllers folder, models folder, services folder)
- **DDD Structure**: We'll organize by business capability (requisition folder with its controllers, models, and services)

This means when we need to change how requisitions work, all the code is in one place, not scattered across multiple folders.

## Why We Need DDD in Our Project

Our PRS (Purchase Requisition System) codebase currently has these problems:

1. **Mixed Concerns**: Business logic is mixed with database access and UI logic
2. **Hard to Change**: When business rules change, we have to modify code in many places
3. **Difficult to Understand**: New developers struggle to understand how the system works
4. **Inconsistent Terms**: Different parts of the code use different terms for the same concepts

## DDD Concepts Explained Simply

### 1. Ubiquitous Language

**What It Is**: Using the same terms in code that business users use in conversation.

**Current Problem**: Our code uses terms like `req_slip`, `po`, and `dr` while business users say "Requisition", "Purchase Order", and "Delivery Receipt".

**Solution**: Rename variables, functions, and classes to match business terminology.

**Example**:
```javascript
// BEFORE: Technical/abbreviated naming
function createReqSlip(reqData) {
  // Create a requisition slip
}

// AFTER: Business terminology
function createRequisition(requisitionData) {
  // Create a requisition
}
```

### 2. Bounded Contexts

**What It Is**: Dividing your system into separate areas where certain terms have specific meanings.

**Current Problem**: The term "status" means different things in different parts of our system, causing confusion.

**Solution**: Clearly define what terms mean in each context and don't mix them.

**Example**:
```javascript
// BEFORE: Same term used differently
// In requisition service:
const status = "APPROVED"; // Means approved by all approvers

// In purchase order service:
const status = "APPROVED"; // Means approved but not yet sent to supplier

// AFTER: Clear context boundaries
// In requisition service:
const requisitionStatus = "FULLY_APPROVED";

// In purchase order service:
const purchaseOrderStatus = "APPROVED_INTERNALLY";
```

### 3. Entities and Value Objects

**What It Is**:
- **Entities**: Objects with identity that persist over time (like a specific Purchase Order)
- **Value Objects**: Objects defined only by their attributes (like an Address or Money amount)

**Current Problem**: Our code treats everything as database records without distinguishing between these types.

**Solution**: Create proper classes for important business concepts.

**Example**:
```javascript
// BEFORE: Just using database records
const requisition = await db.Requisition.findByPk(id);
requisition.status = "APPROVED";
await requisition.save();

// AFTER: Using domain entities
const requisition = await requisitionRepository.findById(id);
requisition.approve(approver);  // Domain logic in the entity
await requisitionRepository.save(requisition);
```

### 4. Aggregates and Aggregate Roots

**What It Is**: A cluster of related objects treated as a single unit, with one main object (the root) controlling access.

**Current Problem**: Our code allows direct access to related objects, bypassing business rules.

**Solution**: Access related objects only through their parent object.

**Example**:
```javascript
// BEFORE: Direct access to related objects
const requisitionItem = await db.RequisitionItem.findByPk(itemId);
requisitionItem.quantity = 10;  // Can change quantity without any validation
await requisitionItem.save();

// AFTER: Access through aggregate root
const requisition = await requisitionRepository.findById(requisitionId);
requisition.updateItemQuantity(itemId, 10);  // Validation happens inside
await requisitionRepository.save(requisition);
```

### 5. Repositories

**What It Is**: Objects that handle storing and retrieving domain objects, hiding database details.

**Current Problem**: Our services directly use database models and SQL queries.

**Solution**: Create repository classes that handle data access.

**Example**:
```javascript
// BEFORE: Direct database access in services
class RequisitionService {
  async getRequisition(id) {
    return await db.Requisition.findByPk(id, {
      include: [db.RequisitionItem, db.RequisitionApprover]
    });
  }
}

// AFTER: Using repositories
class RequisitionService {
  constructor(requisitionRepository) {
    this.requisitionRepository = requisitionRepository;
  }

  async getRequisition(id) {
    return await this.requisitionRepository.findById(id);
  }
}
```

### 6. Services

**What It Is**: Classes that handle operations that don't naturally belong to any single entity.

**Current Problem**: Our services mix business logic, data access, and sometimes even UI concerns.

**Solution**: Create focused services for specific business operations.

**Example**:
```javascript
// BEFORE: Mixed concerns in one service
class RequisitionService {
  async approveRequisition(id, approverId) {
    const requisition = await db.Requisition.findByPk(id);
    const approver = await db.User.findByPk(approverId);

    // Data access, business logic, and notification all mixed
    if (requisition.status !== "PENDING") {
      throw new Error("Cannot approve non-pending requisition");
    }

    await db.RequisitionApproval.create({
      requisitionId: id,
      approverId,
      date: new Date()
    });

    // Check if all approvers approved
    const approvals = await db.RequisitionApproval.findAll({
      where: { requisitionId: id }
    });

    if (approvals.length === requisition.requiredApprovals) {
      requisition.status = "APPROVED";
      await requisition.save();

      // Send email notification
      await sendEmail(requisition.createdBy, "Your requisition was approved");
    }

    return requisition;
  }
}

// AFTER: Focused service with domain logic
class RequisitionApprovalService {
  constructor(requisitionRepository, notificationService) {
    this.requisitionRepository = requisitionRepository;
    this.notificationService = notificationService;
  }

  async approveRequisition(requisitionId, approverId) {
    const requisition = await this.requisitionRepository.findById(requisitionId);

    // Business logic in domain entity
    requisition.approve(approverId);

    await this.requisitionRepository.save(requisition);

    // Notification handled by separate service
    if (requisition.isFullyApproved()) {
      await this.notificationService.notifyRequisitionApproved(requisition);
    }

    return requisition;
  }
}
```

## How to Apply DDD in Our Project

### Step 1: Identify Core Domains

Our core domains are:

1. **Requisition Management**: Creating and processing purchase requisitions
2. **Approval Workflow**: Managing the approval process for requisitions
3. **Supplier Management**: Managing supplier information and selection
4. **Purchase Order Management**: Creating and tracking purchase orders
5. **Delivery Management**: Tracking deliveries against purchase orders
6. **Payment Processing**: Managing payment requests and processing

### Step 2: Create Domain Entities

For each core domain, create proper entity classes:

```javascript
// src/domain/entities/Requisition.js
class Requisition {
  constructor(id, requesterId, department, items, status = 'DRAFT') {
    this.id = id;
    this.requesterId = requesterId;
    this.department = department;
    this.items = items || [];
    this.status = status;
    this.approvals = [];
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  addItem(item) {
    // Validation logic here
    this.items.push(item);
    this.updatedAt = new Date();
  }

  submit() {
    if (this.status !== 'DRAFT') {
      throw new Error('Only draft requisitions can be submitted');
    }

    if (this.items.length === 0) {
      throw new Error('Cannot submit requisition with no items');
    }

    this.status = 'PENDING_APPROVAL';
    this.updatedAt = new Date();
  }

  approve(approverId) {
    if (this.status !== 'PENDING_APPROVAL') {
      throw new Error('Only pending requisitions can be approved');
    }

    // Check if already approved by this approver
    if (this.approvals.some(a => a.approverId === approverId)) {
      throw new Error('Already approved by this approver');
    }

    this.approvals.push({
      approverId,
      date: new Date()
    });

    this.updatedAt = new Date();
  }

  isFullyApproved() {
    // Logic to determine if all required approvers have approved
    return this.approvals.length >= this.requiredApprovalCount;
  }
}

module.exports = Requisition;
```

### Step 3: Create Value Objects

```javascript
// src/domain/valueObjects/Money.js
class Money {
  constructor(amount, currency = 'PHP') {
    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new Error('Amount must be a number');
    }

    // Store amount in cents/smallest unit to avoid floating point issues
    this.amount = Math.round(amount * 100) / 100;
    this.currency = currency;
  }

  add(money) {
    if (money.currency !== this.currency) {
      throw new Error('Cannot add money with different currencies');
    }

    return new Money(this.amount + money.amount, this.currency);
  }

  subtract(money) {
    if (money.currency !== this.currency) {
      throw new Error('Cannot subtract money with different currencies');
    }

    return new Money(this.amount - money.amount, this.currency);
  }

  toString() {
    return `${this.currency} ${this.amount.toFixed(2)}`;
  }
}

module.exports = Money;
```

### Step 4: Create Repositories

```javascript
// src/infra/repositories/RequisitionRepository.js
class RequisitionRepository {
  constructor(db) {
    this.db = db;
  }

  async findById(id) {
    // Get data from database
    const requisitionData = await this.db.Requisition.findByPk(id, {
      include: [
        this.db.RequisitionItem,
        this.db.RequisitionApprover
      ]
    });

    if (!requisitionData) {
      return null;
    }

    // Convert to domain entity
    return this._toDomainEntity(requisitionData);
  }

  async save(requisition) {
    // Start a transaction
    const transaction = await this.db.sequelize.transaction();

    try {
      // Convert domain entity to database model
      const requisitionData = this._toDataModel(requisition);

      // Save to database
      if (requisition.id) {
        // Update existing
        await this.db.Requisition.update(requisitionData, {
          where: { id: requisition.id },
          transaction
        });
      } else {
        // Create new
        const newRequisition = await this.db.Requisition.create(requisitionData, {
          transaction
        });
        requisition.id = newRequisition.id;
      }

      // Save items
      // ... code to save items

      // Save approvals
      // ... code to save approvals

      await transaction.commit();
      return requisition;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  _toDomainEntity(data) {
    // Convert database model to domain entity
    const requisition = new Requisition(
      data.id,
      data.requesterId,
      data.department,
      data.RequisitionItems.map(item => ({
        id: item.id,
        itemId: item.itemId,
        description: item.description,
        quantity: item.quantity,
        unitPrice: new Money(item.unitPrice)
      })),
      data.status
    );

    // Set other properties
    requisition.approvals = data.RequisitionApprovers.map(approver => ({
      approverId: approver.approverId,
      date: approver.approvedAt
    }));

    requisition.createdAt = data.createdAt;
    requisition.updatedAt = data.updatedAt;

    return requisition;
  }

  _toDataModel(requisition) {
    // Convert domain entity to database model
    return {
      id: requisition.id,
      requesterId: requisition.requesterId,
      department: requisition.department,
      status: requisition.status,
      createdAt: requisition.createdAt,
      updatedAt: requisition.updatedAt
    };
  }
}

module.exports = RequisitionRepository;
```

### Step 5: Create Application Services

```javascript
// src/app/services/RequisitionService.js
class RequisitionService {
  constructor(requisitionRepository, notificationService) {
    this.requisitionRepository = requisitionRepository;
    this.notificationService = notificationService;
  }

  async createRequisition(data, userFromToken) {
    // Create a new requisition
    const requisition = new Requisition(
      null, // id will be assigned when saved
      userFromToken.id,
      data.department,
      data.items.map(item => ({
        itemId: item.itemId,
        description: item.description,
        quantity: item.quantity,
        unitPrice: new Money(item.unitPrice)
      })),
      data.isDraft ? 'DRAFT' : 'PENDING_APPROVAL'
    );

    // If not a draft, submit it
    if (!data.isDraft) {
      requisition.submit();
    }

    // Save to repository
    await this.requisitionRepository.save(requisition);

    // Send notifications if needed
    if (!data.isDraft) {
      await this.notificationService.notifyNewRequisition(requisition);
    }

    return requisition;
  }

  async approveRequisition(requisitionId, userFromToken) {
    const requisition = await this.requisitionRepository.findById(requisitionId);

    if (!requisition) {
      throw new Error('Requisition not found');
    }

    // Business logic in domain entity
    requisition.approve(userFromToken.id);

    await this.requisitionRepository.save(requisition);

    // Notification if fully approved
    if (requisition.isFullyApproved()) {
      await this.notificationService.notifyRequisitionApproved(requisition);
    }

    return requisition;
  }
}

module.exports = RequisitionService;
```

## Benefits of This Approach

1. **Clearer Code**: Business rules are explicit in domain entities
2. **Easier Testing**: Domain logic can be tested without database or UI
3. **Flexible Architecture**: Easier to change database or UI without affecting business logic
4. **Better Communication**: Code uses the same language as business users
5. **Reduced Bugs**: Business rules are enforced consistently

## Common Mistakes to Avoid

1. **Anemic Domain Model**: Creating entities that are just data containers without behavior
2. **Leaky Abstractions**: Letting database concerns leak into domain logic
3. **Over-Engineering**: Creating too many small classes that make the system harder to understand
4. **Ignoring Performance**: Focusing too much on design purity at the expense of performance
5. **Not Using Bounded Contexts**: Trying to create a single model for the entire system

## Practical Implementation Steps

1. Start with one core domain (e.g., Requisition Management)
2. Create domain entities with business logic
3. Create repositories for data access
4. Update services to use the new domain model
5. Gradually expand to other domains

Remember: The goal is to make the code easier to understand and change, not to follow DDD principles perfectly. If something doesn't make sense for our project, we can adapt it.
