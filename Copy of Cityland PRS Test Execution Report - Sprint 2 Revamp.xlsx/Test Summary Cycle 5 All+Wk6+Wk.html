<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:12pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b6d7a8;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-right:1px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#ffd966;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{background-color:#ffffff;text-align:left;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:12pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;background-color:#0000ff;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;background-color:#0000ff;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b6d7a8;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{background-color:#000000;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:center;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:3px SOLID #000000;background-color:#000000;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle no-grid" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1377482354C0" style="width:68px;" class="column-headers-background">A</th><th id="1377482354C1" style="width:687px;" class="column-headers-background">B</th><th id="1377482354C2" style="width:100px;" class="column-headers-background">C</th><th id="1377482354C3" style="width:100px;" class="column-headers-background">D</th><th id="1377482354C4" style="width:100px;" class="column-headers-background">E</th><th id="1377482354C5" style="width:100px;" class="column-headers-background">F</th><th id="1377482354C6" style="width:100px;" class="column-headers-background">G</th><th id="1377482354C7" style="width:100px;" class="column-headers-background">H</th><th id="1377482354C8" style="width:113px;" class="column-headers-background">I</th><th id="1377482354C9" style="width:89px;" class="column-headers-background">J</th><th id="1377482354C10" style="width:177px;" class="column-headers-background">K</th><th id="1377482354C11" style="width:177px;" class="column-headers-background">L</th><th id="1377482354C12" style="width:129px;" class="column-headers-background">M</th><th id="1377482354C13" style="width:100px;" class="column-headers-background">N</th><th id="1377482354C14" style="width:132px;" class="column-headers-background">O</th><th id="1377482354C15" style="width:100px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 31px"><th id="1377482354R0" style="height: 31px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 31px">1</div></th><td class="s0"></td><td class="s1" colspan="12">E2E - Run 1 =&quot;[Cityland-PRS] Cycle 5 All+Wk6+Wk7 Regression and Functional Testing (Inital E2E) Test Report - [&quot;&amp;&quot;&quot;&amp;text(TODAY(),&quot;MMmm dd, yyyy&quot;)&amp;&quot;]&quot;</td><td></td><td class="s2"></td><td></td></tr><tr style="height: 42px"><th id="1377482354R1" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">2</div></th><td class="s3">PRS #</td><td class="s4">Stories to be Tested</td><td class="s4">Total Tests</td><td class="s4">Not Started</td><td class="s4">Passed</td><td class="s4">Failed</td><td class="s4">Blocked</td><td class="s4">Out of Scope</td><td class="s4">Deprecated</td><td class="s4">Not Run</td><td class="s4">In Progress</td><td class="s4">Percentage Complete %</td><td class="s5"># of Defects Raised</td><td class="s6">Assignee</td><td class="s7">Priority</td><td></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1377482354R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s8">PRS-001</td><td class="s9">PRS-001 - [Login] - Cityland PRS Landing Page, Login, Password Reset, Session Timeout</td><td class="s10">42</td><td class="s11">0</td><td class="s11">35</td><td class="s11">7</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">10</td><td class="s14">Aira</td><td class="s14">P3</td><td></td></tr><tr style="height: 19px"><th id="1377482354R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s8">PRS-002</td><td class="s9">PRS-002 - [User Types]</td><td class="s10">12</td><td class="s11">0</td><td class="s11">9</td><td class="s11">3</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">3</td><td class="s14">Verna</td><td class="s14">P3</td><td></td></tr><tr style="height: 19px"><th id="1377482354R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s8">PRS-003</td><td class="s9">PRS-003 - [User Management] - Manage IT Admin and Root User, Account Creation, User Deactivation</td><td class="s10">39</td><td class="s11">0</td><td class="s11">39</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">7</td><td class="s14">Ann</td><td class="s14">P3</td><td></td></tr><tr style="height: 19px"><th id="1377482354R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s8">PRS-004</td><td class="s9">PRS-004 - [Manage Supplier] - View and Edit Supplier Details, Attachment, Notes, Activate/Suspend Supplier</td><td class="s10">20</td><td class="s11">0</td><td class="s11">17</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">1</td><td class="s12">100.00%</td><td class="s13">7</td><td class="s14">Kurt</td><td class="s14">P2</td><td></td></tr><tr style="height: 19px"><th id="1377482354R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s8">PRS-005</td><td class="s9">PRS-005 - [Manage Company] - Create, View, Update and Delete of Association</td><td class="s10">16</td><td class="s11">0</td><td class="s11">15</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">10</td><td class="s14">Jeric</td><td class="s14">P2</td><td class="s15"></td></tr><tr style="height: 53px"><th id="1377482354R7" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">8</div></th><td class="s8">PRS-006</td><td class="s9">PRS-006 - [Manage Project] - Pull data, View Project Details, Assigning/Updating Approvers, Optional Approvers, Add/Update Engineers per Trade</td><td class="s10">18</td><td class="s11">0</td><td class="s11">11</td><td class="s11">5</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">9</td><td class="s14">Gela</td><td class="s14">P2</td><td></td></tr><tr style="height: 53px"><th id="1377482354R8" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">9</div></th><td class="s8">PRS-007</td><td class="s9">PRS-007 - [Manage Department] - Pull data, Assigning &amp; Updating of Assigned Approvers, Optional Approvers for Association Department </td><td class="s10">19</td><td class="s11">0</td><td class="s11">17</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">1</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">8</td><td class="s14">Kams</td><td class="s14">P2</td><td></td></tr><tr style="height: 53px"><th id="1377482354R9" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">10</div></th><td class="s8">PRS-008</td><td class="s9">PRS-008 - [Manage Items] - View, Create, Update OFM Items, OFM List and Non-OFM Items, Search, Filter</td><td class="s10">18</td><td class="s11">0</td><td class="s11">14</td><td class="s11">3</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">10</td><td class="s14">Cherry</td><td class="s14">P2</td><td class="s15"></td></tr><tr style="height: 53px"><th id="1377482354R10" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">11</div></th><td class="s8">PRS-009</td><td class="s9">PRS-009 - [Manage Requisition Slip] - Create Requistion Slip for Non-OFM, Transfer of Materials, OFM</td><td class="s10">6</td><td class="s11">0</td><td class="s11">3</td><td class="s11">3</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">6</td><td class="s14">Ann</td><td class="s14">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R11" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">12</div></th><td class="s8">PRS-010</td><td class="s9">PRS-010 - [RS Dashboard] - Dashboard view, Search, Filter, Pagination</td><td class="s10">16</td><td class="s11">0</td><td class="s11">5</td><td class="s11">3</td><td class="s11">6</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">6</td><td class="s16">Gela</td><td class="s16">P2</td><td class="s15"></td></tr><tr style="height: 53px"><th id="1377482354R12" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">13</div></th><td class="s8">PRS-011</td><td class="s9">PRS-011 - [RS Creation] - Submit &amp; Draft Requisition Slip, Add notes, Upload attachment</td><td class="s10">29</td><td class="s11">0</td><td class="s11">20</td><td class="s11">9</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">13</td><td class="s16">Cherry</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R13" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">14</div></th><td class="s8">PRS-012</td><td class="s9">PRS-012 - [RS Viewing] - Search, Filter, View Attachment, View Notes, Related Documents, Edit Requsition Slip</td><td class="s10">15</td><td class="s11">0</td><td class="s11">12</td><td class="s11">3</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">4</td><td class="s16">Cherry</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R14" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">15</div></th><td class="s8">PRS-013</td><td class="s9">PRS-013 - [RS Approval] - Approve, Reject, Re-Submit, Re-Approve, Cancel, Approver Updates</td><td class="s10">23</td><td class="s11">0</td><td class="s11">11</td><td class="s11">6</td><td class="s11">4</td><td class="s11">0</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">17</td><td class="s16">Kams</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R15" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">16</div></th><td class="s8">PRS-014</td><td class="s9">PRS-014 - [RS Assigning] - Assign RS to me, Assign RS to others</td><td class="s10">5</td><td class="s11">0</td><td class="s11">2</td><td class="s11">3</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">3</td><td class="s16">Gela</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R16" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">17</div></th><td class="s8">PRS-015</td><td class="s9">PRS-015 - [Requisition Slip] - Updating of Requisition Slip during Approval, Cancel created RS, Update RS Number Format for RS Draft</td><td class="s10">25</td><td class="s11">0</td><td class="s11">14</td><td class="s11">5</td><td class="s11">6</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">3</td><td class="s16">Gela</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R17" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">18</div></th><td class="s8">PRS-016</td><td class="s9">PRS-016 - [Manage Items in RS] -  Adding of Item</td><td class="s10">8</td><td class="s11">0</td><td class="s11">2</td><td class="s11">2</td><td class="s11">3</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">6</td><td class="s16">Jeric</td><td class="s16">P2</td><td></td></tr><tr style="height: 53px"><th id="1377482354R18" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">19</div></th><td class="s8">PRS-017</td><td class="s17">PRS-017 - [Canvassing] - Viewing of Canvass Sheet by Approver, Approving of Assigned Purchasing Staff Supervisor , Supplier selection of Purchasing Head for OFM and Non-OFM Request Type, Approving of Management, Creating a Canvass - Attachments are clickable, Allow Manual overriding of Canvass Quantity, Assigning of Canvass Sheet Approvers with an existing Canvass Sheet, Adding of Items for Canvass Sheet, Supplier for Transfer of Materials</td><td class="s10">56</td><td class="s11">0</td><td class="s11">21</td><td class="s11">32</td><td class="s11">2</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">42</td><td class="s16">Jeric</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R19" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">20</div></th><td class="s8">PRS-018</td><td class="s17">PRS-018 - [History] - OFM Items History</td><td class="s10">7</td><td class="s11">0</td><td class="s11">6</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">4</td><td class="s16">Ann</td><td class="s16">P2</td><td></td></tr><tr style="height: 53px"><th id="1377482354R20" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">21</div></th><td class="s8">PRS-019</td><td class="s17">PRS-019 - [Syncing] - Supplier Sync Scenario, Suspending a Supplier, Company Sync Scenario,</td><td class="s10">31</td><td class="s11">0</td><td class="s11">8</td><td class="s11">5</td><td class="s11">18</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">17</td><td class="s16">Ann</td><td class="s16">P2</td><td class="s15"></td></tr><tr style="height: 53px"><th id="1377482354R21" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">22</div></th><td class="s8">PRS-020</td><td class="s17">PRS-020 - [Syncing] - OFM Item Sync Scenario, Assigning Alt Approver, Viewing of Leave,  Editing of Leave, Cancelling of Leave</td><td class="s10">69</td><td class="s11">0</td><td class="s11">48</td><td class="s11">13</td><td class="s11">7</td><td class="s11">0</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">8</td><td class="s16">Kams</td><td class="s16">P2</td><td class="s15"></td></tr><tr style="height: 53px"><th id="1377482354R22" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">23</div></th><td class="s8">PRS-021</td><td class="s17">PRS-021 - [Canvassing] Producing of PO after all Canvass Approval</td><td class="s10">6</td><td class="s11">0</td><td class="s11">4</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">4</td><td class="s16">Jeric</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R23" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">24</div></th><td class="s8">PRS-022</td><td class="s17">PRS-022 - [Delivery Receipt] Creation of Delivery Receipt per Supplier, Entering of Invoice in the Delivery Receipt, Viewing of Delivery Receipt per Supplier, Updating of Data based on Purchase Order for Delivery Receipt</td><td class="s10">25</td><td class="s11">0</td><td class="s11">19</td><td class="s11">6</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">14</td><td class="s16">Cherry</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R24" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">25</div></th><td class="s8">PRS-023</td><td class="s17">PRS-023 - [History] Non-OFM History, RS Related Documents, Request History</td><td class="s10">36</td><td class="s11">0</td><td class="s11">28</td><td class="s11">5</td><td class="s11">0</td><td class="s11">3</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">5</td><td class="s16">Ann</td><td class="s16">P2</td><td></td></tr><tr style="height: 53px"><th id="1377482354R25" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">26</div></th><td class="s8">PRS-024</td><td class="s17">PRS-024 - [Steelbars] Tagging of Steelbars in OFM Item List, Adding of Dimensions for the Steelbars, RS with Steelbars, Canvassing for Steelbars Item Group,  Purchase Order for Steelbars, Payment Request for Steelbars , Delivery Record for Steelbars, Delivery Receipt for Steelbars</td><td class="s10">104</td><td class="s11">0</td><td class="s11">81</td><td class="s11">19</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s11">2</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">17</td><td class="s16">Kurt</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R26" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">27</div></th><td class="s8">PRS-025</td><td class="s17">PRS-025 - [Purchase Order] - Viewing of Purchase Order List, Approving of PO, Rejecting of PO, Resubmitting of rejected PO, Purchase Order View For Approval , Viewing Purchase Order for Requester, Reviewing of Purchase Order by Assigned Purchasing Staff</td><td class="s10">124</td><td class="s11">0</td><td class="s11">66</td><td class="s11">26</td><td class="s11">32</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">26</td><td class="s16">Verna</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R27" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">28</div></th><td class="s8">PRS-026</td><td class="s17">PRS-026 - [Payment Request] - Scenario per Terms of the Supplier, Entering of Payment Request per Purchase Order, Viewing of Payment Request, Payment Request Approval - OFM Request, Payment Request Approval - Non-OFM Request, Payment Request Approval - Transfer of Materials, Payment Submission to Accounting, Assigning of Payment Request Approvers with an existing Payment Request, Gate Pass for Transfer of Material Request</td><td class="s10">152</td><td class="s11">0</td><td class="s11">81</td><td class="s11">61</td><td class="s11">2</td><td class="s11">0</td><td class="s11">0</td><td class="s11">8</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">26</td><td class="s16">Aira</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R28" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">29</div></th><td class="s8">PRS-027</td><td class="s17">PRS-027 - [Return items] Returning an Item, Recording the Delivered Item after return, Cancelling of Request to Return Item</td><td class="s10">41</td><td class="s11">0</td><td class="s11">27</td><td class="s11">6</td><td class="s11">8</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">8</td><td class="s16">Ann</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R29" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">30</div></th><td class="s8">PRS-028</td><td class="s17">PRS-028 - [Enhancements_1]  Root User - Search Function for User Management Landing Page<br>IT Admin - Search Function for User Management Landing Page<br>User Management: IT Admin - Add User Types Management - only 2 Users will be assigned<br>Sorting for Department Columns<br>Sorting for User Type and Department Columns<br>OFM Items: Landing Page - Sorting of Table<br>Non-OFM: View Non-OFM - Enable RS Search in Item Table<br>Non-OFM: Enable Filter Function in Landing Page<br>RS Dashboard: : Update RS Number to Reference Number<br>RS Creation: Update Reference Number for Draft</td><td class="s10">20</td><td class="s11">0</td><td class="s11">19</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">1</td><td class="s16">Cherry</td><td class="s16">P3</td><td></td></tr><tr style="height: 53px"><th id="1377482354R30" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">31</div></th><td class="s8">PRS-029</td><td class="s17">PRS-029 - [Enhancements_2] - Manage Company: Allow Editing only for Association<br>Non-OFM: Update Dropdown Function for Units<br>RS Dashboard: RS Dashboard - Sorting of RS Table<br>RS Creation: Error Message Enhancement for required Field<br>RS Viewing: Add criteria for Searching in the Item Table<br>OFM Items: Manage of pulled Data from Cityland<br>Tagging of Steelbars in upon Syncing<br>OFM Items: Editing of OFM Item Units<br>RS Creation: Updating of Item View when adding an Item during OFM Request creation<br>Allow editing or deleting of Additional Approvers for RS Approval<br>Allow combobox Drop-down Fields for all Fields in PRS<br>Retaining of selected sorting to the Tables of PRS</td><td class="s10">54</td><td class="s11">0</td><td class="s11">32</td><td class="s11">17</td><td class="s11">5</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">12</td><td class="s18">Jeric</td><td class="s18">P2</td><td class="s15"></td></tr><tr style="height: 53px"><th id="1377482354R31" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">32</div></th><td class="s8">PRS-030</td><td class="s17">PRS-030 - [Remaining AC]  - Updated Attachment Filtering in Supplier Management, Download in Dashboard, Enable Searching in Dashboard Page, Selection of Date in Date Picker in RS Creation, Submitting and Saving of Draft for Created RS, Enable Filtering in Dashboard Page</td><td class="s10">74</td><td class="s11">0</td><td class="s11">58</td><td class="s11">15</td><td class="s11">1</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">4</td><td class="s16">Kurt</td><td class="s16">P3</td><td></td></tr><tr style="height: 53px"><th id="1377482354R32" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">33</div></th><td class="s8">PRS-031</td><td class="s17">PRS-031- [NON-RS PAYMENT REQUEST] - Non-RS Dashboard, Mapping of Approvers for Non-RS Payment, Non-RS Payment Creation, Non-RS Payment Approval, Non-RS Payment Viewing, Editing during Non-RS Approval,  Non-RS Payment Rejecting, Resubmitting of rejected Non-RS</td><td class="s10">112</td><td class="s11">0</td><td class="s11">79</td><td class="s11">21</td><td class="s11">12</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">17</td><td class="s16">Gela</td><td class="s16">P1</td><td></td></tr><tr style="height: 53px"><th id="1377482354R33" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">34</div></th><td class="s8">PRS-032</td><td class="s17">PRS-032 - [M2 Feedback] - Mockup Data Cleanup for Company                                                <br>Update the behavior of Charge To, Company, Project and DepartmentInclude Company Intials in the Drop-down Values for Company when creating a Requisition Slip                                                <br>Update the functionality of the Drop-down Fields in Requisition Slip Creation<br>Updating of Draft Reference Number when updating the Company in the Requisition Slip<br>Updating of Draft Requisition Slip should only be done by the Requestor        </td><td class="s10">47</td><td class="s11">0</td><td class="s11">36</td><td class="s11">2</td><td class="s11">9</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">2</td><td class="s16">Kams</td><td class="s16">P2</td><td></td></tr><tr style="height: 53px"><th id="1377482354R34" style="height: 53px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 53px">35</div></th><td class="s8">PRS-033</td><td class="s17">PRS-033 - [Enhancements_3} -  User Types: Add Management User Type, Manage Department: Enhance Error Messaging for blank Approvers during Submission, Manage Projects: Approver Assignment - Enhance Error Messaging for blank Approvers during Submission , Manage Projects: Project Sync - Project Address needs a Sample Data</td><td class="s10">4</td><td class="s11">0</td><td class="s11">4</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s11">0</td><td class="s12">100.00%</td><td class="s13">0</td><td class="s16">Cherry</td><td class="s16">P3</td><td class="s15"></td></tr><tr style="height: 24px"><th id="1377482354R35" style="height: 24px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 24px">36</div></th><td class="s19">PRS-034</td><td class="s20">TOTAL</td><td class="s21">1273</td><td class="s21">0</td><td class="s21">843</td><td class="s21">288</td><td class="s21">120</td><td class="s21">7</td><td class="s21">3</td><td class="s21">11</td><td class="s21">1</td><td class="s22">100.00%</td><td class="s23">37</td><td></td><td class="s24"></td><td></td></tr></tbody></table></div>