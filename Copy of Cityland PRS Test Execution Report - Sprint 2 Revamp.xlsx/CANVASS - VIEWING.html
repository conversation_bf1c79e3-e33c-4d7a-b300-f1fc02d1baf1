<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="148200146C0" style="width:103px;" class="column-headers-background">A</th><th id="148200146C1" style="width:194px;" class="column-headers-background">B</th><th id="148200146C2" style="width:100px;" class="column-headers-background">C</th><th id="148200146C3" style="width:252px;" class="column-headers-background">D</th><th id="148200146C4" style="width:233px;" class="column-headers-background">E</th><th id="148200146C5" style="width:330px;" class="column-headers-background">F</th><th id="148200146C7" style="width:363px;" class="column-headers-background">H</th><th id="148200146C8" style="width:100px;" class="column-headers-background">I</th><th id="148200146C9" style="width:164px;" class="column-headers-background">J</th></tr></thead><tbody><tr style="height: 42px"><th id="148200146R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="148200146R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2">PRS-000-001</td><td class="s2">Update Related Documents</td><td class="s2">Critical</td><td class="s2">Verify Related Documents Table shows &quot;Canvass No&quot; for Draft and Submitted.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created.</td><td class="s2">1. Navigate to Related Documents &gt; Canvasses tab.<br>2. View Canvass Table.<br>3. Verify visibility of &quot;Canvass No&quot;.</td><td class="s2">3. Canvass Sheet No. is displayed.</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s2">PRS-000-002</td><td class="s2">Update Related Documents</td><td class="s2">Minor</td><td class="s2">Verify &quot;Canvass No&quot; shows correct format.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created</td><td class="s2">1. Navigate to Related Documents &gt; Canvasses tab &gt; Canvass Table.<br>2. Verify correct format and status for &quot;Canvass No&quot;.</td><td class="s2">2. Should be able to meet the following format:<br>           i) CS Draft - CS-TMP-[Company Code] + AA-ZZ + 8-incremental digits.<br>              BG - #5F636833<br>              Text - #5F6368<br>           ii) For CS Approval - CS-[Company Code] + AA-ZZ + 8-incremental digits.<br>              BG - #F0963D33<br>              Text - #F0963D</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s2">PRS-000-003</td><td class="s2">Update Related Documents</td><td class="s2">High</td><td class="s2">Verify &quot;Last Updated&quot; column displays the latest modification date.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created</td><td class="s2">1. Update the Canvass Sheet.<br>2. View Canvass Table.<br>3. Verify &quot;Last Updated&quot; column.</td><td class="s2">3. Date reflects the most recent update to the Canvass Sheet</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s2">PRS-000-004</td><td class="s2">Update Related Documents</td><td class="s2">Minor</td><td class="s2">Verify &quot;Last Updated&quot; format is in &#39;DD MMM YYYY&#39;.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created and updated.</td><td class="s2">1. View Canvass Table.<br>2. Verify format of &quot;Last Updated&quot; .</td><td class="s2"><span style="font-family:Poppins,Arial;color:#000000;">2. Date appears in correct format: </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">DD MMM YYYY<br>e.g.  23 Jul 2025</span></td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s2">PRS-000-005</td><td class="s2">Update Related Documents</td><td class="s2">Critical</td><td class="s2">Verify &quot;Next Approver&quot; shows the name of the actual next approver.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created.</td><td class="s2">1. Submit Canvass Sheet.<br>2. View Canvass Table.<br>3. Verify &quot;Next Approver&quot;.</td><td class="s2">3. Actual approver&#39;s name is shown</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s2">PRS-000-006</td><td class="s2">Update Related Documents</td><td class="s2">High</td><td class="s2">Verify &quot;Next Approver&quot; displays &#39;---&#39; when no approver is present.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created and has no approver.</td><td class="s2">1. Open Canvass Sheet without approval.<br>2. View &quot;Next Approver&quot; column.</td><td class="s2">2. &#39;Next Approver&#39; column shows &quot;---&quot;.</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s2">PRS-000-007</td><td class="s2">Update Related Documents</td><td class="s2">Critical</td><td class="s2">Verify &quot;Status&quot; column displays CS Draft with correct colors.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is in Draft status.</td><td class="s2">1. View Canvass Table.<br>2. Verify &quot;Status&quot;.</td><td class="s2">2. Status shows the ff format:<br>           i) CS Draft <br>              BG - #5F636833<br>              Text - #5F6368</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s2">PRS-000-008</td><td class="s2">Update Related Documents</td><td class="s2">Critical</td><td class="s2">Verify &quot;Status&quot; column displays For CS Approval with correct colors.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is for approval.</td><td class="s2">1. View Canvass Table.<br>2. Verify &quot;Status&quot;.</td><td class="s2">2. Status shows the ff format:<br>           ii) For CS Approval<br>              BG - #F0963D33<br>              Text - #F0963D</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s2">PRS-000-009</td><td class="s2">Update Related Documents</td><td class="s2">Critical</td><td class="s2">Verify &quot;Status&quot; column displays CS Rejected with correct colors.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is rejected.</td><td class="s2">1. View Canvass Table.<br>2. Verify &quot;Status&quot;.</td><td class="s2">2. Status shows the ff format:<br>           iii) CS Rejected<br>              BG - #DC433B33<br>              Text - #DC433B</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s2">PRS-000-010</td><td class="s2">Update Related Documents</td><td class="s2">Critical</td><td class="s2">Verify &quot;Status&quot; column displays CS Approved with correct colors.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is approved.</td><td class="s2">1. View Canvass Table.<br>2. Verify &quot;Status&quot;.</td><td class="s2">2. Status shows the ff format:<br>           iv) CS Approved<br>              BG - #1EA52B33<br>              Text - #1EA52B</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s2">PRS-000-011</td><td class="s2">Update Related Documents</td><td class="s2">High</td><td class="s2">Verify fields in Related Documents update dynamically after changes.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is modified.</td><td class="s2">1. Change status or approver.<br>2. Reopen Canvass Table.<br>3. View Related Documents.</td><td class="s2">3. All applicable columns are refreshed to reflect the latest values.</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s2">PRS-000-012</td><td class="s2">Update Related Documents</td><td class="s2">High</td><td class="s2">Verify data does not show outdated values after edits.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet was recently edited.</td><td class="s2">1. Modify Canvass Sheet.<br>2. Navigate to Canvass Table.<br>3. Observe column values.</td><td class="s2">3. All values are updated real-time or upon refresh.</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s2">PRS-000-013</td><td class="s2">Update Related Documents</td><td class="s2">Minor</td><td class="s2">Verify all columns in Related Documents are aligned and properly labeled.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created.</td><td class="s2">1. Navigate to Related Documents &gt; Canvasses tab.<br>2. View Canvass Table.</td><td class="s2">2. Columns are labeled and aligned correctly.</td><td class="s2"></td><td class="s3">Not Started</td></tr><tr style="height: 19px"><th id="148200146R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s2">PRS-000-014</td><td class="s2">Update Related Documents</td><td class="s2">High</td><td class="s2">Verify no unexpected columns appear in the Canvass Table.</td><td class="s2">1. Logged in as any type of user except for Root user.<br>2. Canvass Sheet is created.</td><td class="s2">1. View Canvass Table</td><td class="s2">1. Only expected columns (Canvass No, Last Updated, Next Approver, Status) appear.</td><td class="s2"></td><td class="s3">Not Started</td></tr></tbody></table></div>