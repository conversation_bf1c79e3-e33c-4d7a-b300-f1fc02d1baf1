import { signIn, signOut } from "next-auth/react";
import { generateOTPSecret } from "./otp";

export interface LoginResponse {
  success: boolean;
  error?: string;
  requireUpdatePassword?: boolean;
  requireOTPSetup?: boolean;
  requireOTPVerification?: boolean;
  otpAuthUrl?: string;
  otpSecret?: string;
  accessToken?: string;
  refreshToken?: string;
  expiredAt?: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiredAt: number;
}

/**
 * Authentication service for handling login, logout, and token management
 * This service provides a clean interface for authentication operations
 * and abstracts away the implementation details
 *
 * This implementation closely follows the original project's authentication flow:
 * 1. User logs in with username and password
 * 2. If the user has a temporary password, they are redirected to update it
 * 3. If the user doesn't have OTP set up, they are redirected to set it up
 * 4. If the user has OTP set up, they are asked to verify it
 * 5. After successful authentication, the user receives access and refresh tokens
 */
export class AuthService {
  /**
   * Attempt to log in with username and password
   * @param username User's username
   * @param password User's password
   * @returns LoginResponse with status and any required next steps
   */
  static async login(username: string, password: string): Promise<LoginResponse> {
    try {
      const result = await signIn("credentials", {
        redirect: false,
        username,
        password,
      });

      if (result?.error) {
        return {
          success: false,
          error: result.error,
        };
      }

      // Parse the URL to check for special auth flow parameters
      const url = new URL(result?.url || window.location.href);
      const requireUpdatePassword = url.searchParams.get("requireUpdatePassword") === "true";
      const requireOTPSetup = url.searchParams.get("requireOTPSetup") === "true";
      const requireOTPVerification = url.searchParams.get("requireOTPVerification") === "true";
      const accessToken = url.searchParams.get("accessToken") || "";
      const refreshToken = url.searchParams.get("refreshToken") || "";
      const expiredAt = parseInt(url.searchParams.get("expiredAt") || "0");
      const otpSecret = url.searchParams.get("otpSecret") || "";
      const otpAuthUrl = url.searchParams.get("otpAuthUrl") || "";

      // Handle different authentication flows
      if (requireUpdatePassword) {
        return {
          success: true,
          requireUpdatePassword: true,
          accessToken,
          expiredAt
        };
      }

      if (requireOTPSetup) {
        return {
          success: true,
          requireOTPSetup: true,
          otpSecret,
          otpAuthUrl,
          accessToken,
          expiredAt
        };
      }

      if (requireOTPVerification) {
        return {
          success: true,
          requireOTPVerification: true,
          accessToken,
          expiredAt
        };
      }

      // Regular successful login
      return {
        success: true,
        accessToken,
        refreshToken,
        expiredAt
      };
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        error: "An unexpected error occurred"
      };
    }
  }

  /**
   * Verify OTP code during login
   * @param username User's username
   * @param password User's password
   * @param otp OTP code
   * @returns LoginResponse with status
   */
  static async verifyOTP(username: string, password: string, otp: string): Promise<LoginResponse> {
    try {
      const result = await signIn("credentials", {
        redirect: false,
        username,
        password,
        otp,
        action: "verifyOTP"
      });

      if (result?.error) {
        return {
          success: false,
          error: result.error,
        };
      }

      // Parse the URL to get tokens
      const url = new URL(result?.url || window.location.href);
      const accessToken = url.searchParams.get("accessToken") || "";
      const refreshToken = url.searchParams.get("refreshToken") || "";
      const expiredAt = parseInt(url.searchParams.get("expiredAt") || "0");

      return {
        success: true,
        accessToken,
        refreshToken,
        expiredAt
      };
    } catch (error) {
      console.error("OTP verification error:", error);
      return {
        success: false,
        error: "An unexpected error occurred",
      };
    }
  }

  /**
   * Set up OTP for a user
   * @param username User's username
   * @param password User's password
   * @param otp OTP code
   * @param otpSecret OTP secret
   * @returns LoginResponse with status
   */
  static async setupOTP(
    username: string,
    password: string,
    otp: string,
    otpSecret: string
  ): Promise<LoginResponse> {
    try {
      const result = await signIn("credentials", {
        redirect: false,
        username,
        password,
        otp,
        otpSecret,
        action: "setupOTP"
      });

      if (result?.error) {
        return {
          success: false,
          error: result.error,
        };
      }

      // Parse the URL to get tokens
      const url = new URL(result?.url || window.location.href);
      const accessToken = url.searchParams.get("accessToken") || "";
      const refreshToken = url.searchParams.get("refreshToken") || "";
      const expiredAt = parseInt(url.searchParams.get("expiredAt") || "0");

      return {
        success: true,
        accessToken,
        refreshToken,
        expiredAt
      };
    } catch (error) {
      console.error("OTP setup error:", error);
      return {
        success: false,
        error: "An unexpected error occurred",
      };
    }
  }

  /**
   * Update temporary password
   * @param username User's username
   * @param oldPassword Old password
   * @param newPassword New password
   * @returns LoginResponse with status
   */
  static async updateTemporaryPassword(
    username: string,
    oldPassword: string,
    newPassword: string
  ): Promise<LoginResponse> {
    try {
      const result = await signIn("credentials", {
        redirect: false,
        username,
        password: oldPassword,
        newPassword,
        action: "updatePassword"
      });

      if (result?.error) {
        return {
          success: false,
          error: result.error,
        };
      }

      // Parse the URL to get tokens
      const url = new URL(result?.url || window.location.href);
      const accessToken = url.searchParams.get("accessToken") || "";
      const refreshToken = url.searchParams.get("refreshToken") || "";
      const expiredAt = parseInt(url.searchParams.get("expiredAt") || "0");

      return {
        success: true,
        accessToken,
        refreshToken,
        expiredAt
      };
    } catch (error) {
      console.error("Password update error:", error);
      return {
        success: false,
        error: "An unexpected error occurred",
      };
    }
  }

  /**
   * Refresh the access token using the refresh token
   * @param refreshToken Refresh token
   * @returns New access token and expiration
   */
  static async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      const result = await signIn("credentials", {
        redirect: false,
        refreshToken,
        action: "refreshToken"
      });

      if (result?.error) {
        return {
          success: false,
          error: result.error,
        };
      }

      // Parse the URL to get tokens
      const url = new URL(result?.url || window.location.href);
      const accessToken = url.searchParams.get("accessToken") || "";
      const newRefreshToken = url.searchParams.get("refreshToken") || "";
      const expiredAt = parseInt(url.searchParams.get("expiredAt") || "0");

      return {
        success: true,
        accessToken,
        refreshToken: newRefreshToken,
        expiredAt
      };
    } catch (error) {
      console.error("Token refresh error:", error);
      return {
        success: false,
        error: "An unexpected error occurred",
      };
    }
  }

  /**
   * Log out the current user
   */
  static async logout(): Promise<void> {
    await signOut({ callbackUrl: "/auth/login" });
  }

  /**
   * Request password reset
   * @param username User's username
   * @returns Success status and message
   */
  static async forgotPassword(username: string): Promise<{ success: boolean; message: string }> {
    try {
      const result = await signIn("credentials", {
        redirect: false,
        username,
        action: "forgotPassword"
      });

      if (result?.error) {
        return {
          success: false,
          message: result.error,
        };
      }

      return {
        success: true,
        message: "Password reset request sent successfully",
      };
    } catch (error) {
      console.error("Forgot password error:", error);
      return {
        success: false,
        message: "Failed to send reset password request",
      };
    }
  }
}
