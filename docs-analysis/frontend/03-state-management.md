# State Management

## Overview

The PRS-Frontend uses Zustand as its primary state management solution, complemented by React Query for server state management. This document provides a detailed analysis of the state management approach, including store organization, patterns, and integration with the rest of the application.

## State Management Architecture

The application's state management is divided into several categories:

1. **Client State**: Managed with Zustand stores
2. **Server State**: Managed with React Query
3. **Local Component State**: Managed with React's useState and useReducer hooks
4. **URL State**: Managed with React Router's useSearchParams hook

This separation allows for more efficient and targeted state management based on the nature of the data.

## Zustand Stores

Zustand is used for managing client-side state that needs to be shared across components. The stores are organized by domain and located in the `src/store` directory.

### Store Organization

```
store/
├── authStore.js         # Authentication tokens
├── userStore.js         # User information
├── permissionStore.js   # User permissions
├── rsTabStore.js        # Requisition slip tabs
├── requisitionItemsStore.js # Requisition items
├── canvassItemsStore.js # Canvass items
├── ofmSyncItemsStore.js # OFM sync items
├── nonRSItemsStore.js   # Non-requisition slip items
├── deliveryReceiptStore.js # Delivery receipt
├── dimensionStore.js    # UI dimensions
└── index.js             # Store exports
```

### Store Implementation

Zustand stores are implemented as simple functions that return an object with state and actions:

```javascript
// From src/store/authStore.js
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useTokenStore = create(
  persist(
    (set, get) => ({
      token: null,
      type: null,
      refreshToken: null,
      expiredAt: null,
      setToken: (token, type = null) => set({ token, type }),
      setRefreshToken: ({ accessToken, refreshToken, expiredAt }) => {
        const currentAccessToken = get().token;

        set({
          token: accessToken || currentAccessToken,
          refreshToken,
          expiredAt,
        });
      },
      setType: type => set({ type }),
      removeToken: () =>
        set({ token: null, type: null, refreshToken: null, expiredAt: null }),
    }),
    {
      name: 'auth-storage', // name of the item in the storage (must be unique)
    },
  ),
);

export { useTokenStore };
```

Many stores use the `persist` middleware to persist state to localStorage, which is particularly important for authentication-related state.

### Store Usage

Stores are used in components by importing the hook and destructuring the needed state and actions:

```jsx
// Example of store usage
import { useUserStore, useTokenStore } from '@store';

const SomeComponent = () => {
  const { user } = useUserStore();
  const { token, setToken } = useTokenStore();

  // Use state and actions
  return (
    <div>
      {user ? (
        <p>Welcome, {user.name}</p>
      ) : (
        <button onClick={() => setToken('some-token', 'auth')}>Login</button>
      )}
    </div>
  );
};
```

## React Query for Server State

React Query is used for managing server state, including data fetching, caching, and synchronization. It's configured in `src/lib/reactQuery.js` and used throughout the application.

### Query Configuration

```javascript
// From src/lib/reactQuery.js
export const queryConfig = {
  queries: {
    refetchOnWindowFocus: false,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  },
};
```

### API Integration

React Query is used in conjunction with the Axios-based API client to fetch data from the server:

```javascript
// Example API function
export const getCompanies = ({
  page = 1,
  limit = 10,
  paginate,
  filterBy,
  sortBy,
}) => {
  return api.get('/v1/companies', {
    params: {
      page,
      limit,
      paginate,
      filterBy: convertObjectToString(removeEmptyStringFromObject(filterBy)),
      ...formatSortParams(sortBy),
    },
  });
};

// Example React Query hook
export const useGetCompanies = (params, config = {}) => {
  return useQuery({
    queryKey: ['companies', params],
    queryFn: () => getCompanies(params),
    ...config,
  });
};
```

### Mutations

React Query is also used for mutations, such as creating, updating, or deleting data:

```javascript
// Example mutation function
export const createNotes = ({
  model,
  modelId,
  userType,
  commentType,
  note,
}) => {
  const { success, error, data } = submitNoteSchema.safeParse({ notes: note });

  if (!success) {
    throw new Error(error.format().notes._errors[0]);
  }

  return api.post('/v1/notes/', {
    model,
    modelId: parseInt(modelId),
    userType,
    commentType,
    note: data.notes,
  });
};

// Example React Query mutation hook
export const useCreateNotes = (config = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createNotes,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
    ...config,
  });
};
```

## Local Component State

For state that is specific to a component and doesn't need to be shared, React's built-in state management hooks are used:

```jsx
// Example of local component state
const SomeComponent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div>
      <input
        type="text"
        value={searchQuery}
        onChange={e => setSearchQuery(e.target.value)}
      />
      <button onClick={() => setIsOpen(!isOpen)}>Toggle</button>
      {isOpen && <div>Some content</div>}
    </div>
  );
};
```

## URL State

For state that should be reflected in the URL, React Router's `useSearchParams` hook is used:

```jsx
// Example of URL state
const SomeComponent = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');

  const handlePageChange = newPage => {
    setSearchParams({ ...Object.fromEntries(searchParams), page: newPage });
  };

  return (
    <div>
      <Table
        data={data}
        page={page}
        limit={limit}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
```

## State Management Patterns

### 1. Store Composition

Stores are composed together to manage different aspects of the application state:

```javascript
// Authentication-related stores
const { token, setToken } = useTokenStore();
const { user, setUser } = useUserStore();
const { permissions, setPermissions } = usePermissionStore();

// Feature-specific stores
const { requisitionItems, addRequisitionItem } = useRequisitionItemsStore();
const { activeTab, setActiveTab } = useRSTabStore();
```

### 2. Store Persistence

Many stores use the `persist` middleware to persist state to localStorage:

```javascript
// Example of persist middleware
const useUserStore = create(
  persist(
    set => ({
      user: null,
      // ... other state and actions
    }),
    {
      name: 'user-storage',
    },
  ),
);
```

### 3. Store Selectors

Stores use selectors to extract specific pieces of state:

```javascript
// Example of store selector
const user = useUserStore(state => state.user);
const permissions = usePermissionStore(state => state.permissions);
```

### 4. Store Actions

Stores define actions that modify the state:

```javascript
// Example of store actions
const { setUser, removeUser } = useUserStore();
const { setToken, removeToken } = useTokenStore();
```

## State Management Issues

### 1. Store Fragmentation

The application has many small stores, which can make it difficult to understand the overall state structure and relationships between stores.

### 2. Inconsistent Store Design

Some stores follow different patterns, with inconsistent naming conventions and action structures.

### 3. Limited Store Documentation

Many stores lack documentation explaining their purpose, state structure, and usage patterns.

### 4. Tight Coupling

Some stores are tightly coupled to specific features or components, limiting their reusability.

### 5. Redundant State

There is some redundancy in state management, with the same data being stored in multiple places.

## Recommendations

### 1. Consolidate Related Stores

Combine related stores to reduce fragmentation and improve cohesion:

```javascript
// Before: Separate stores
const useUserStore = create(/* ... */);
const usePermissionStore = create(/* ... */);
const useTokenStore = create(/* ... */);

// After: Consolidated store
const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      permissions: null,
      token: null,
      refreshToken: null,
      expiredAt: null,
      // ... actions
    }),
    {
      name: 'auth-storage',
    },
  ),
);
```

### 2. Standardize Store Design

Adopt a consistent pattern for store design:

```javascript
// Standardized store pattern
const useExampleStore = create(
  persist(
    (set, get) => ({
      // 1. State
      someState: null,
      
      // 2. Computed state (getters)
      get computedState() {
        return get().someState ? 'computed value' : null;
      },
      
      // 3. Actions
      setSomeState: value => set({ someState: value }),
      resetState: () => set({ someState: null }),
      
      // 4. Complex actions
      someComplexAction: async () => {
        // ... complex logic
        set({ someState: 'new value' });
      },
    }),
    {
      name: 'example-storage',
    },
  ),
);
```

### 3. Add Store Documentation

Document each store with its purpose, state structure, and usage patterns:

```javascript
/**
 * Authentication store
 * 
 * Manages authentication-related state, including tokens, user information, and permissions.
 * 
 * State:
 * - token: The access token for API requests
 * - refreshToken: The refresh token for obtaining new access tokens
 * - expiredAt: The expiration timestamp for the access token
 * - user: The authenticated user information
 * - permissions: The user's permissions
 * 
 * Actions:
 * - setToken: Sets the access token
 * - setRefreshToken: Sets the refresh token and expiration
 * - removeToken: Removes all token information
 * - setUser: Sets the user information
 * - removeUser: Removes the user information
 * - setPermissions: Sets the user's permissions
 * - removeAll: Removes all authentication-related state
 */
const useAuthStore = create(/* ... */);
```

### 4. Use Selectors for Derived State

Use selectors to derive state from the store, rather than computing it in components:

```javascript
// Before: Computing derived state in components
const { user } = useUserStore();
const isAdmin = user?.role === 'admin';

// After: Using selectors for derived state
const isAdmin = useUserStore(state => state.user?.role === 'admin');
```

### 5. Implement Store Middleware

Use middleware to add cross-cutting concerns to stores:

```javascript
// Example of custom middleware
const logger = config => (set, get, api) => config(
  (...args) => {
    console.log('Prev state:', get());
    console.log('Args:', args);
    set(...args);
    console.log('Next state:', get());
  },
  get,
  api,
);

const useExampleStore = create(
  logger(
    persist(
      (set, get) => ({
        // ... state and actions
      }),
      {
        name: 'example-storage',
      },
    ),
  ),
);
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
