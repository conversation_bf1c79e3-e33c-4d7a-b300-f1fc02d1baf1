<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#dd7e6b;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9d2e9;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d5a6bd;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="374143680C0" style="width:103px;" class="column-headers-background">A</th><th id="374143680C1" style="width:167px;" class="column-headers-background">B</th><th id="374143680C2" style="width:252px;" class="column-headers-background">C</th><th id="374143680C3" style="width:84px;" class="column-headers-background">D</th><th id="374143680C4" style="width:233px;" class="column-headers-background">E</th><th id="374143680C5" style="width:330px;" class="column-headers-background">F</th><th id="374143680C6" style="width:69px;" class="column-headers-background">G</th><th id="374143680C7" style="width:511px;" class="column-headers-background">H</th><th id="374143680C8" style="width:63px;" class="column-headers-background">I</th><th id="374143680C9" style="width:114px;" class="column-headers-background">J</th><th id="374143680C10" style="width:170px;" class="column-headers-background">K</th><th id="374143680C11" style="width:170px;" class="column-headers-background">L</th><th id="374143680C12" style="width:170px;" class="column-headers-background">M</th><th id="374143680C13" style="width:170px;" class="column-headers-background">N</th><th id="374143680C14" style="width:78px;" class="column-headers-background">O</th><th id="374143680C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="374143680R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(STG_wk4&amp;5)</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="374143680R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="16">PRS-029 - [Enhancements_2] - Manage Company: Allow Editing only for Association<br>Non-OFM: Update Dropdown Function for Units<br>RS Dashboard: RS Dashboard - Sorting of RS Table<br>RS Creation: Error Message Enhancement for required Field<br>RS Viewing: Add criteria for Searching in the Item Table<br>OFM Items: Manage of pulled Data from Cityland<br>Tagging of Steelbars in upon Syncing<br>OFM Items: Editing of OFM Item Units<br>RS Creation: Updating of Item View when adding an Item during OFM Request creation<br>Allow editing or deleting of Additional Approvers for RS Approval<br>Allow combobox Drop-down Fields for all Fields in PRS<br>Retaining of selected sorting to the Tables of PRS</td></tr><tr style="height: 19px"><th id="374143680R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-029-001</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Positive Scenario: <br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Manage Company: Allow Editing only for Association</span></td><td class="s4">Verify the edit icon for Association only</td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as IT Admin. </td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Go to Manage<br>2. Go to Company &gt;&gt; Landing the page on Company/Association Management<br>3. In Action, Click the edit icon for </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Association</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Validate Save button or Cancel button</span></td><td class="s6"></td><td class="s4">1. Should only enable the Edit Icon to Created Associations or Data that are tagged as Association</td><td class="s4" rowspan="18"></td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="374143680R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-029-002</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#ff0000;">Negative Scenario:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Manage Company: Allow Editing only for Association</span></td><td class="s4">Verify the edit icon for Company</td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as IT Admin. </td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Go to Manage<br>2. Go to Company &gt;&gt; Landing the page on Company/Association Management<br>3. In Action, Click the edit icon for </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Company</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. then click Save button or Cancel button</span></td><td class="s6"></td><td class="s4">1. Should disable the Edit Icon to Edit/Create the Company or Data that are tagged as Company</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="374143680R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-029-003</td><td class="s10"><br>Non-OFM: Update Dropdown Function for Units</td><td class="s4">Verify Non-OFM update Drop-down Function for Units<br><br></td><td class="s4">Critical</td><td class="s4">1. User already logged in successfully to their Account as <br> -IT Admin<br> -Purchasing Staff<br> -Purchasing Head<br> -Engineering Group</td><td class="s4">1. In Dashboard, Go to Item<br>2. Go to Non-OFM<br>3. Create New Non-OFM Item <br>4. Select value on Units dropdown field <br>5. Click Units dropdown field <br>6. Check list of values displayed<br>7. Search a keyword in Unit dropdown field <br>8. Search a keyword that is not existing on the list<br>9. Click create added value<br>10. Click Submit</td><td class="s6"></td><td class="s4">1. Should apply the changes when creating a New Non-OFM Item and when updating an Existing Non-OFM Item<br>    a. Units Field should be a combobox of Current Values and New Values<br>        i. Should allow typing of Value<br>            i) If existing, should display the matched value for selection<br>            ii) If not existing, should allow adding of the entered value<br>                a) Once Submitted, should add this new value in the selection<br>             iii) Should have a Default or Drop-down values of<br>                 a) pc<br>                 b) lot<br>                 c) pack<br>                 d) unit<br>                 e) set<br>                 f) m<br>                 g) gal<br>                 h) liter<br>                 i) bundle<br>                 j) kilo<br>                 k) yard<br>                 l) ream<br>                 m) box<br>                 n) bottle<br>                 o) pair<br>                 p) roll<br>                 q) dozen<br>                 r) can<br>                 s) unit<br>                 t) tin</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="374143680R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">PRS-029-004</td><td class="s10"><br>Non-OFM: Update Dropdown Function for Units</td><td class="s4">Verify Unit field if keyboard navigation keys are working as expected</td><td class="s4">Critical</td><td class="s4">1. User already logged in successfully to their Account as <br> -IT Admin<br> -Purchasing Staff<br> -Purchasing Head<br> -Engineering Group</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Click Create New Non-OFM Item <br>2. Click drop-down fields for Units<br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click dropdown fields for Units<br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s6"></td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display Create New Non-OFM Item modal<br>2. Should implement a Combobox Drop-down on Units Fields<br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on Units Fields<br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br></span></td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="374143680R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-029-005</td><td class="s10"><br>Non-OFM: Update Dropdown Function for Units</td><td class="s4">Verify Update Existing Non-OFM Item Drop-down Function for Units</td><td class="s4">Critical</td><td class="s4">1. User already logged in successfully to their Account as <br> -IT Admin<br> -Purchasing Staff<br> -Purchasing Head<br> -Engineering Group</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. In Dashboard, Go to Item<br>2. Go to Non-OFM<br>3. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Update Existing Non-OFM Item</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>4. Select value on Units dropdown field <br>5. Click Units dropdown field <br>6. Check list of values displayed<br>7. Search a keyword in Unit dropdown field <br>8. Search a keyword that is not existing on the list<br>9. Select / Enter the Value on Units dropdown Fields<br>10. Click Save</span></td><td class="s6"></td><td class="s4">1. Should apply the changes when creating a New Non-OFM Item and when updating an Existing Non-OFM Item<br>    a. Units Field should be a combobox of Current Values and New Values<br>        i. Should allow typing of Value<br>            i) If existing, should display the matched value for selection<br>            ii) If not existing, should allow adding of the entered value<br>                a) Once Submitted, should add this new value in the selection<br>             iii) Should have a Default or Drop-down values of<br>                 a) pc<br>                 b) lot<br>                 c) pack<br>                 d) unit<br>                 e) set<br>                 f) m<br>                 g) gal<br>                 h) liter<br>                 i) bundle<br>                 j) kilo<br>                 k) yard<br>                 l) ream<br>                 m) box<br>                 n) bottle<br>                 o) pair<br>                 p) roll<br>                 q) dozen<br>                 r) can<br>                 s) unit<br>                 t) tin</td><td class="s11">Failed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-919"> https://youtrack.stratpoint.com/issue/CITYLANDPRS-919<br><br><br></a></td></tr><tr style="height: 19px"><th id="374143680R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-029-006</td><td class="s10"><br>Non-OFM: Update Dropdown Function for Units</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#ff0000;">Negative Scenario:</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if the fields is blank the submit button is disable</span></td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as <br> -IT Admin<br> -Purchasing Staff<br> -Purchasing Head<br> -Engineering Group</td><td class="s4">1. In Dashboard, Go to Item<br>2. Go to Non-OFM<br>3. Create New Non-OFM Item / Existing Non-OFM Item<br>4. Fill up the fields<br>5. No value for Units Fields <br>6. Validate the Submit / Save Button</td><td class="s6"></td><td class="s4">Should be disable the Submit/Save button if the Unit Fields is No Value </td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-029-007</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;RS Number&quot; works in ascending (A-Z),(0-9) and descending (Z-A)(9-0) order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click RS Number column name for sorting</td><td class="s6"></td><td class="s4"> Sorting functions correctly.<br>    a. RS Number<br>        i. A-Z, 0-9 || Z-A, 9-0<br>   </td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">PRS-029-008</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Type&quot; works in ascending (A-Z) and descending (Z-A) order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Type column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>    b. Type<br>        i. A-Z, Z-A<br>   </td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-029-009</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Requestor&quot; works in ascending (A-Z) and descending (Z-A)order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Requestor column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>    c. Requestor<br>          i. A-Z, Z-A<br>   </td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">PRS-029-010</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Company&quot; works in ascending (A-Z),(0-9) and descending (Z-A)(9-0) order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Company column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>    d. Company<br>          i. A-Z, 0-9 || Z-A, 9-0</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 113px"><th id="374143680R12" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">13</div></th><td class="s4">PRS-029-011</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Project&quot; works in ascending (A-Z),(0-9) and descending (Z-A)(9-0) order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Project column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>    e. Project<br>        i. A-Z, 0-9 || Z-A, 9-0</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">PRS-029-012</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Department&quot; works in ascending (A-Z) and descending (Z-A) order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Department column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>      f. Department<br>          i. A-Z, Z-A</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">PRS-029-013</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Date Requested works from Oldest Date-Latest Date, Latest Date-Oldest Date.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Date Requested column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>      g. Date Requested<br>           i. Oldest Date-Latest Date, Latest Date-Oldest Date<br></td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">PRS-029-014</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Last Updated&quot; works from Oldest Date-Latest Date, Latest Date-Oldest Date.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Last Updated column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>      h. Last Updated<br>           i. Oldest Date-Latest Date, Latest Date-Oldest Date</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-029-015</td><td class="s12">RS Dashboard: RS Dashboard - Sorting of RS Table</td><td class="s4">Verify that sorting by &quot;Status&quot; works in ascending (A-Z) and descending (Z-A) order.</td><td class="s4">High</td><td class="s4">1.User already logged in successfully to their Account (All Users except Root User)<br>2. A Requisition Slip and other Related Documents has been created</td><td class="s4">1. In Dashboard, Click Status column name for sorting</td><td class="s6"></td><td class="s4">Sorting functions correctly.<br>      i. Status<br>          i. A-Z, Z-A</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4">PRS-029-016</td><td class="s13">RS Creation: Error Message Enhancement for required Field</td><td class="s4">Verify Error Message for required Field</td><td class="s4">Critical</td><td class="s4">1. Requisition Slip Form has been filled up</td><td class="s4">1. In Dashboard, Click New Request<br>2. Click Save as Draft button <br>3. Validate the error message.</td><td class="s6"></td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should click either Save as Draft Button or Submit Button<br>    a. Should trigger an Error Message displaying, </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;All Fields are required, kindly check your entered Data&quot;</span></td><td class="s7">Passed</td><td class="s14">Failed</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1104">CITYLANDPRS-1104</a></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s16">PRS-029-017</td><td class="s16">RS Viewing: Add criteria for Searching in the Item Table</td><td class="s16">Verify Search Button to search for item</td><td class="s16">High</td><td class="s16">1. User already logged in successfully to their Account (All Users except Root User)<br>2.  Requisition Slip has been created</td><td class="s16">1. In Dashboard, Click Requisition Slip Number to View the RS Details and Items<br>2. Click the Search Field and input the keyword<br>3. Click the Search Button</td><td class="s17"></td><td class="s16">1. Should be able to search for Item<br>    a. Should be triggered by Search Button<br>    b. Should allow searching just by Keyword<br>    c. Should allow searching for the whole Table<br></td><td class="s7">Passed</td><td class="s11">Failed</td><td class="s18"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-871">CITYLANDPRS-871</a></td><td class="s8">Not Started</td><td class="s4"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 19px"><th id="374143680R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s16">PRS-029-018</td><td class="s16">RS Viewing: Add criteria for Searching in the Item Table</td><td class="s16">Verify Clear Button to reset the Search Field</td><td class="s16">High</td><td class="s16">1. User already logged in successfully to their Account (All Users except Root User)<br>2.  Requisition Slip has been created</td><td class="s16">1. In Dashboard, Click Requisition Slip Number to View the RS Details and Items<br>2. Click the Search Field and input the keyword<br>3. Click the Clear Button. </td><td class="s17"></td><td class="s16">2. Should reset the Search Field and Table when Clear Button is clicked</td><td class="s7">Passed</td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 19px"><th id="374143680R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s4">PRS-029-019</td><td class="s12">OFM Items: Manage of pulled Data from Cityland</td><td class="s4">Verify if the ItemCD and Units from syncing the OFM Items is not include</td><td class="s4">High</td><td class="s4" rowspan="3">1. User already logged in successfully to their Account <br>2. OFM Items syncing</td><td class="s4" rowspan="3"><br>1. Go to Item<br>2. Go to OFM items<br>3. Click Sync Button<br></td><td class="s6"></td><td class="s4">1. Should not include the ItemCD and Units from syncing the OFM Items<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s4">PRS-029-020</td><td class="s12">RS Viewing: Add criteria for Searching in the Item Table</td><td class="s4">Verify if the ItemCD value equal to Account Code</td><td class="s4">High</td><td class="s6"></td><td class="s4">2. Should have the ItemCD value equal to Account Code</td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s4">PRS-029-021</td><td class="s12">OFM Items: Manage of pulled Data from Cityland</td><td class="s4">Verify if the newly added OFM items for the Units should be display.</td><td class="s4">High</td><td class="s6"></td><td class="s4">3. Should display &quot;---&quot; for the Units of newly added OFM Items</td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s4">PRS-029-022</td><td class="s20">OFM Items: Adding of Units per OFM Item upon syncing</td><td class="s4">Verify the Sync Button in OFM </td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button</td><td class="s4">1. In Dashboard, Go to Item<br>2. Go to OFM Item<br>3. Landing the page to OFM Items<br>4. Click Sync Button<br><br></td><td class="s6"></td><td class="s4">1. Should click Sync Buttons in OFM<br>    a. Once Synced, should check all Items that does not have a Unit<br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Adding of Units per OFM Item upon syncing] Synced Items modal is not showing up when clicking sync button</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158</a></div></td></tr><tr style="height: 19px"><th id="374143680R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s4">PRS-029-023</td><td class="s20">OFM Items: Adding of Units per OFM Item upon syncing</td><td class="s4">Verify the Synced Item Modal</td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button</td><td class="s4">1. In Dashboard, Go to Item<br>2. Go to OFM Item<br>3. Landing the page to OFM Items<br>4. Click Sync Button<br>5. Validate Synced Item Modal<br>6. Validate the item that does not have a Unit<br>7. Input New Value<br>8. Click Submit<br></td><td class="s6"></td><td class="s4">2. Should display a Modal that will require the User to input the Unit of the Items<br>     a. Modal should have<br>         i. Item Description should be the Item Name synced from Cityland<br>         ii. Units Field should be a combobox of Current Values and New Values<br>             a) Should allow typing of Value<br>                 1) If existing, should display the matched value for selection<br>                 2) If not existing, should allow adding of the entered value<br>                     a1. Once Submitted, should add this new value in the selection<br>             b) Should have a Default or Drop-down values of<br>                 1) pc<br>                 2) lot<br>                 3) pack<br>                 4) unit<br>                 5) set<br>                 6) m<br>                 7) gal<br>                 8) liter<br>                 9) bundle<br>                 10) kilo<br>                 11) yard<br>                 12) ream<br>                 13) box<br>                 14) bottle<br>                 15) pair<br>                 16) roll<br>                 17) dozen<br>                 18) can<br>                 19) unit<br>                 20) tin<br></td><td class="s6"></td><td class="s6"></td><td class="s22">Blocked</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Adding of Units per OFM Item upon syncing] Synced Items modal is not showing up when clicking sync button</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158</a></div></td></tr><tr style="height: 19px"><th id="374143680R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s4">PRS-029-024</td><td class="s20">OFM Items: Adding of Units per OFM Item upon syncing</td><td class="s4">Verify the Synced Item Modal</td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button</td><td class="s4" rowspan="2">9. Validate all Field require for Units a Value<br> 10. Click Add Items Button</td><td class="s6"></td><td class="s4">3. Should require all Field of Units to have a Value<br></td><td class="s6"></td><td class="s6"></td><td class="s22">Blocked</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Adding of Units per OFM Item upon syncing] Synced Items modal is not showing up when clicking sync button</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158</a></div></td></tr><tr style="height: 19px"><th id="374143680R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s4">PRS-029-025</td><td class="s20">OFM Items: Adding of Units per OFM Item upon syncing</td><td class="s4">Verify the Synced Item Modal</td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button</td><td class="s6"></td><td class="s4"><br>4. Should click Add Items Button to save all of the Items and their Units in the OFM Items Table</td><td class="s6"></td><td class="s6"></td><td class="s22">Blocked</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Adding of Units per OFM Item upon syncing] Synced Items modal is not showing up when clicking sync button</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158</a></div></td></tr><tr style="height: 19px"><th id="374143680R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s4">PRS-029-026</td><td class="s20">OFM Items: Adding of Units per OFM Item upon syncing</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br><br>Verify if the unit fields is No Value </span></td><td class="s4">High</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button</td><td class="s4">1. In Dashboard, Go to Item<br>2. Go to OFM Item<br>3. Landing the page to OFM Items<br>4. Click Sync Button<br>5. Validate Synced Item Modal<br>6. No value for Units Fields <br>7. Validate the Submit / Save Button</td><td class="s6"></td><td class="s4">Should be disable the Submit/Save button if the Unit Fields is No Value </td><td class="s6"></td><td class="s6"></td><td class="s22">Blocked</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Adding of Units per OFM Item upon syncing] Synced Items modal is not showing up when clicking sync button</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158</a></div></td></tr><tr style="height: 19px"><th id="374143680R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s4">PRS-029-027</td><td class="s10">OFM Items: Editing of OFM Item Units</td><td class="s4">Verify the Pencil icon in Action Section to Edit the OFM Items</td><td class="s4">Critical</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button<br>3. Units were already declared for the Item</td><td class="s4">1. In Dashboard, Go to Item<br>2. Go to OFM Item<br>3. Landing the page to OFM Items<br>4. In Action Section, Click the Pencil Icon <br>5. Click the Edit Button During Viewing of the OFM items details<br>6. Check the Edit Modal </td><td class="s6"></td><td class="s4">1. Should click Edit through<br>    a. Edit Icon in OFM Items Table<br>    b. Edit Button during Viewing of OFM Item Details</td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s4">PRS-029-028</td><td class="s10">OFM Items: Editing of OFM Item Units</td><td class="s4">Verify Edit Modal of the OFM items </td><td class="s4">Critical</td><td class="s4">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button<br>3. Units were already declared for the Item</td><td class="s4"><br>6. Check the Edit Modal <br>7. Check the Non-editable Fields<br>         i. Item Code<br>         ii. Item Description<br>         iii. Account Code<br>         iv. GFQ<br>         v. Remaining GFQ<br>         vi. Company<br>         vii. Project<br>         viii. Trade<br>8. Validate the Combobos for Current values and New Values<br>9. Input the New Value<br>10. Click Submit<br>11. Check the New Add Value in the Selection<br>12. Validate the Defualt or Drop-down values</td><td class="s6"></td><td class="s4">2. Should display the Edit Modal when clicked either of the Entry Points<br>     a. Should have Non-editable Fieds<br>         i. Item Code<br>         ii. Item Description<br>         iii. Account Code<br>         iv. GFQ<br>         v. Remaining GFQ<br>         vi. Company<br>         vii. Project<br>         viii. Trade<br>    b. Should have the Units Field should be a combobox of Current Values and New Values<br>         i. Should allow typing of Value<br>            i) If existing, should display the matched value for selection<br>            ii) If not existing, should allow adding of the entered value<br>                a) Once Submitted, should add this new value in the selection<br>         ii. Should have a Default or Drop-down values of<br>            i) pc<br>            ii) lot<br>            iii) pack<br>            iv) unit<br>            v) set<br>            vi) m<br>            vii) gal<br>            viii) liter<br>            ix) bundle<br>            x) kilo<br>            xi) yard<br>            xii) ream<br>            xiii) box<br>            xiv) bottle<br>            xv) pair<br>            xvi) roll<br>            xvii) dozen<br>            xviii) can<br>            xix) unit<br>            xx) tin<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 73px"><th id="374143680R30" style="height: 73px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 73px">31</div></th><td class="s4">PRS-029-029</td><td class="s10">OFM Items: Editing of OFM Item Units</td><td class="s4">Verify Edit Modal of the OFM items </td><td class="s4">Critical</td><td class="s4" rowspan="2">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button<br>3. Units were already declared for the Item</td><td class="s4" rowspan="2">9. Validate require fields for Units have a Value<br> 10. Click Save Button</td><td class="s6"></td><td class="s4">3. Should require the Units to have a Value</td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Editing of OFM Item Units] Unit can be set as &quot;---&quot; in OFM Items</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1159">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1159</a></div></td></tr><tr style="height: 19px"><th id="374143680R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s4">PRS-029-030</td><td class="s10">OFM Items: Editing of OFM Item Units</td><td class="s4">Verify Edit Modal of the OFM items </td><td class="s4">Critical</td><td class="s6"></td><td class="s4">4. Should click Save Button to save the Units in the Item and OFM Items Table<br>  a. Should update the Requests that has used the Item</td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Editing of OFM Item Units] Updating unit of OFM item doesn&#39;t reflect changes on created RS</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1160">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1160</a></div></td></tr><tr style="height: 19px"><th id="374143680R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s4">PRS-029-031</td><td class="s23">Tagging of Steelbars in upon Syncing</td><td class="s24">Verify the Tagging of Steelbars upon Syncing</td><td class="s24">Critical</td><td class="s24">1. User already logged in successfully to their Account as Purchasing Staff<br>2. OFM Items Masterlist were connected to the Sync Button</td><td class="s24">1. In Dashboard, Go to Item<br>2. Go to OFM Item<br>3. Landing the page to OFM Items<br>4. Click Sync Button<br>5. Validate Synced Item Modal<br>6. Check the item that does not have a Unit<br>7. Require all Field of Units to have a Value<br>8. In Steelbar section, Click the Toggle to Yes<br>9. Click Add Item Button<br></td><td class="s25"></td><td class="s24">1. Should click Sync Buttons in OFM<br>    a. Once Synced, should check all Items that does not have a Unit<br>2. Should display a Modal that will require the User to input the Unit of the Items<br>3. Should display a Toggle for Steelbars<br>    a. Should have the Toggle set as No by Default<br>4. Should allow the User to click the Toggle to Yes<br>5. Should click Add Items Button<br>    a. Should save the Item as a Steelbar<br>    b. Should still display under Civil and Architectural Works Trade</td><td class="s6"></td><td class="s6"></td><td class="s22">Blocked</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6 softmerge"><div class="softmerge-inner" style="width:75px;left:-1px">[OFM Items: Adding of Units per OFM Item upon syncing] Synced Items modal is not showing up when clicking sync button</div></td><td class="s21 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1158</a></div></td></tr><tr style="height: 19px"><th id="374143680R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s4">PRS-029-032</td><td class="s26">RS Creation: Updating of Item View when adding an Item during OFM Request creation</td><td class="s24">Verify Updating of Item View when adding an Item during OFM Request Creation</td><td class="s24">High</td><td class="s24">1. User already logged in successfully to their Account as a Requester<br>2. Requisition Slip Form has been filled up </td><td class="s24">1. In Dashboard, Click Requisition Slip Number<br>2. Click Add Items Button <br>3. Landing to Add Items Modal<br>4. Validate the Item list <br>5. Select the Items<br>6. Validate the items Selected in Right box</td><td class="s25"></td><td class="s24">1. Should click Add Items Button<br>2. Should display the Items Modal<br>3. Should contain the OFM List, depending on the Trade and Project of the User logged in<br>4. Should display the Item List with an Accordion Function<br>5. Should indent the Items per List<br>     a. Once an Item is selected, should display the Item to the Right Box<br>         i. All of the selected Items should be counted and displayed at the Right Box<br>6. Should unselect an Item to remove it in the List of Selected Items</td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s4">PRS-029-033</td><td class="s27">Allow editing or deleting of Additional Approvers for RS Approval</td><td class="s24">Validate Approver to editing or deleting of Additionsl Approver for RS Approval</td><td class="s24">High</td><td class="s24">1. Requisition Slip has been Submitted<br>2. Should be a Requisition Slip Approver</td><td class="s24">1. Login as RS Approver<br>2. In Dashboard, click RS Number<br>3. In RS Main Tab, See the Section for Status, Assigned Purchasing Staff and Approvers<br>4. Click Add on the Approvers part<br>5. Click the search field<br>6. Check different User - User Types<br>7. Input/Select a User<br>8. Click Add Approver<br>9. Click the tree Ellipsis for the Added Approver<br>10. Validate the Edit and Delete Added Approver</td><td class="s25"></td><td class="s24">1. Should access the submitted Requisition Slip<br>2. Should have sections for Status, Assigned Purchasing Staff, and Approvers<br>     a. Should allow adding of the Approver by clicking Add Button<br>         i. Once clicked, should display a Modal that will allow adding a New Approver after the Current Approver<br>         ii. Once Added, should display the Added Approver below the Current Approver Name in the Approvers Section<br>         iii. Should allow Editing or Removing of the Added Approver<br>              i. Should click the three Ellipsis for the Added Approver<br>                 a) Should display Options for<br>                      1) Edit - this will open a Modal to re-assign the Added Approver<br>                      2) Delete - this will open a Modal to confirm if the Added Approver will be removed</td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s4">PRS-029-034</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate functionality of all the Combobox Drop-down Fields in </span><span style="font-family:Arial;font-weight:bold;color:#000000;">User Management Tab<br><br></span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts</td><td class="s24">1. In Dashboard, Go to Admin tab<br>2. Click User Management<br>3. Click Create New User Button<br>4. Click drop-down fileds of the following:<br>   a. User Type<br>   b. Supervisor<br>   c. Department<br><br>5. Check list of values displayed<br>6. Search a keyword in the following dropdown field <br>   a. User Type<br>   b. Supervisor<br>   c. Department<br><br>7. Search a keyword that is not existing on the list<br></td><td class="s25"></td><td class="s24">1. Should update the behavior of the Drop-down Fields across Cityland PRS<br>2. Should implement a Combobox Drop-down<br>    a. Should allow searching of the Values in the Drop-down Fields<br>        i. Should allow searching only for a Keyword<br>           i) Should display matched Data upon typing of Keyword<br>        ii. Should display No Data if no Value has matched the entered Keyword<br>3. Should sort the Values of the Drop-downs Alphabetically [A-Z]<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard<br>5. Should allow the User to use Enter Key and Space Bar in the Keyboard in selecting the option in the Drop-down Values<br>6. Should update the Drop-down Fields in the following:<br>    a. Selected Drop-down Fields are those with options that were greater than five Options<br><br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s4">PRS-029-035</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24">Validate User Management tab if all the Combobox Drop-down field keyboard navigation keys are working as expected</td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts</td><td class="s24"><span style="font-family:Arial;color:#000000;"><br>1. Click Create New User Button on User Management page<br>2. Click drop-down fields of the following:<br>   a. User Type<br>   b. Supervisor<br>   c. Department<br><br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click dropdown field of the following:<br>   a. User Type<br>   b. Supervisor<br>   c. Department<br><br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-family:Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 5 and 8:</span><span style="font-family:Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s25"></td><td class="s24"><span style="font-family:Arial;color:#000000;">1. Should display Create New User Modal<br>2. Should implement a Combobox Drop-down on the following:<br>   a. User Type<br>   b. Supervisor<br>   c. Department<br><br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on the following:<br>   a. User Type<br>   b. Supervisor<br>   c. Department<br><br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br><br></span></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="374143680R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s4">PRS-029-036</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate functionality of all the Combobox Drop-down Fields in </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Department tab</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts</td><td class="s24">1. In Dashboard, Go to Manage tab<br>2. Click Department<br>3. Click Department Name<br>4. Click Edit Button<br>5. On Approvers (RS), Click Add Level button<br>6. Click Add Approver or Edit Approver<br>7. Check the update drop-down fileds of the following:<br>    a. Adding Approver Name<br>    b. Editing Approver Name<br><br>8. Check list of values displayed<br>9. Search a keyword in the following dropdown field <br>   a. Adding Approver Name<br>   b. Editing Approver Name<br><br>10. Search a keyword that is not existing on the list<br></td><td class="s25"></td><td class="s24"><br><br>1. Should update the behavior of the Drop-down Fields across Cityland PRS<br>2. Should implement a Combobox Drop-down<br>    a. Should allow searching of the Values in the Drop-down Fields<br>        i. Should allow searching only for a Keyword<br>           i) Should display matched Data upon typing of Keyword<br>        ii. Should display No Data if no Value has matched the entered Keyword<br>3. Should sort the Values of the Drop-downs Alphabetically [A-Z]<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard<br>5. Should allow the User to use Enter Key and Space Bar in the Keyboard in selecting the option in the Drop-down Values<br>6. Should update the Drop-down Fields in the following:<br>    a. Selected Drop-down Fields are those with options that were greater than five Options<br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Allow combobox Drop-down Fields for all Fields in PRS] Approver Dropdowns under Manage Tab</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1109">CITYLANDPRS-1109</a></td></tr><tr style="height: 19px"><th id="374143680R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s4">PRS-029-037</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Department Tab</span><span style="font-family:Arial;color:#000000;"> if all the Combobox Drop-down field keyboard navigation keys are working as expected</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts</td><td class="s24"><span style="font-family:Arial;color:#000000;"><br>1. Click Add Approver or Edit Approver in Department Tab<br>2. Click drop-down fileds of the following:<br>    a. Adding Approver Name<br>    b. Editing Approver Name<br><br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click dropdown field of the following:<br>     a. Adding Approver Name<br>     b. Editing Approver Name<br><br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-family:Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 5 and 8:</span><span style="font-family:Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s25"></td><td class="s24"><span style="font-family:Arial;color:#000000;">1. Should display Add Approver/Edit Approver Modal<br>2. Should implement a Combobox Drop-down on the following:<br>  a. Adding Approver Name<br>    b. Editing Approver Name<br><br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on the following:<br>   a. Adding Approver Name<br>    b. Editing Approver Name<br><br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br><br></span></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Allow combobox Drop-down Fields for all Fields in PRS] Approver Dropdowns under Manage Tab</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1109">CITYLANDPRS-1109</a></td></tr><tr style="height: 19px"><th id="374143680R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s4">PRS-029-038</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate functionality of all the Combobox Drop-down Fields in </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Project Tab</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts</td><td class="s24">1. In Dashboard, Go to Manage tab<br>2. Click Project tab<br>3. Click Project Name<br>4. In Project Approvers, Click Edit button<br>5. Click Add Level Button<br>6. Click Add Approver or Edit Approver<br>7. Click drop-down fileds of the following:<br>    a. Adding Approver Name<br>    b. Editing Approver Name<br><br>8. Check list of values displayed<br>9. Search a keyword in the following dropdown field <br>   a. Adding Approver Name<br>   b. Editing Approver Name<br><br>10. Search a keyword that is not existing on the list<br></td><td class="s25"></td><td class="s24"><br><br>1. Should update the behavior of the Drop-down Fields across Cityland PRS<br>2. Should implement a Combobox Drop-down<br>    a. Should allow searching of the Values in the Drop-down Fields<br>        i. Should allow searching only for a Keyword<br>           i) Should display matched Data upon typing of Keyword<br>        ii. Should display No Data if no Value has matched the entered Keyword<br>3. Should sort the Values of the Drop-downs Alphabetically [A-Z]<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard<br>5. Should allow the User to use Enter Key and Space Bar in the Keyboard in selecting the option in the Drop-down Values<br>6. Should update the Drop-down Fields in the following:<br>    a. Selected Drop-down Fields are those with options that were greater than five Options<br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Allow combobox Drop-down Fields for all Fields in PRS] Approver Dropdowns under Manage Tab</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1109">CITYLANDPRS-1109</a></td></tr><tr style="height: 19px"><th id="374143680R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s4">PRS-029-039</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Project Tab</span><span style="font-family:Arial;color:#000000;"> if all the Combobox Drop-down field keyboard navigation keys are working as expected</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts</td><td class="s24"><span style="font-family:Arial;color:#000000;">1. In Project Details, Click Add Approver or Edit Approver <br>2. Click drop-down fileds of the following:<br>    a. Adding Approver Name<br>    b. Editing Approver Name<br><br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click dropdown field of the following:<br>     a. Adding Approver Name<br>     b. Editing Approver Name<br><br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-family:Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 5 and 8:</span><span style="font-family:Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s25"></td><td class="s24"><span style="font-family:Arial;color:#000000;">1. Should display Add Approver/Edit Approver Modal<br>2. Should implement a Combobox Drop-down on the following:<br>  a. Adding Approver Name<br>    b. Editing Approver Name<br><br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on the following:<br>   a. Adding Approver Name<br>    b. Editing Approver Name<br><br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br><br></span></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Allow combobox Drop-down Fields for all Fields in PRS] Approver Dropdowns under Manage Tab</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1109">CITYLANDPRS-1109</a></td></tr><tr style="height: 19px"><th id="374143680R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s4">PRS-029-040</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate functionality of all the Combobox Drop-down Fields in </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Canvassing Tab</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts as Assigned Purchasing Staff</td><td class="s24"> <br>         <br>1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>4. Click drop-down fileds of the following:<br>     a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>5. Check list of values displayed<br>6. Search a keyword in the following dropdown field <br>   a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>7. Search a keyword that is not existing on the list<br></td><td class="s25"></td><td class="s24"><br><br>1. Should update the behavior of the Drop-down Fields across Cityland PRS<br>2. Should implement a Combobox Drop-down<br>    a. Should allow searching of the Values in the Drop-down Fields<br>        i. Should allow searching only for a Keyword<br>           i) Should display matched Data upon typing of Keyword<br>        ii. Should display No Data if no Value has matched the entered Keyword<br>3. Should sort the Values of the Drop-downs Alphabetically [A-Z]<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard<br>5. Should allow the User to use Enter Key and Space Bar in the Keyboard in selecting the option in the Drop-down Values<br>6. Should update the Drop-down Fields in the following:<br>    a. Selected Drop-down Fields are those with options that were greater than five Options<br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Allow combobox Drop-down Fields for all Fields in PRS] Enter and spacebar is not working when selecting Supplier and Terms in Canvassing</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1114">CITYLANDPRS-1114</a></td></tr><tr style="height: 19px"><th id="374143680R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s4">PRS-029-041</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Canvassing Tab</span><span style="font-family:Arial;color:#000000;"> if all the Combobox Drop-down field keyboard navigation keys are working as expected</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24"><span style="font-family:Arial;color:#000000;">1. In Canvassing, Click Enter Canvass or Edit Canvass<br>2. Click drop-down fileds of the following:<br>    a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click dropdown field of the following:<br>     a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-family:Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-family:Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s25"></td><td class="s24"><span style="font-family:Arial;color:#000000;">1. Should display Enter Canvass or Edit Canvass Modal<br>2. Should implement a Combobox Drop-down on the following:<br>  a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on the following:<br>   a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br><br></span></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Allow combobox Drop-down Fields for all Fields in PRS] Enter and spacebar is not working when selecting Supplier and Terms in Canvassing</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1114">CITYLANDPRS-1114</a></td></tr><tr style="height: 19px"><th id="374143680R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s4">PRS-029-042</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate functionality of all the Drop-down Fields </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Delivery Receipt Tab</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24"><span style="font-family:Arial;color:#000000;"> <br>         <br>1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Record Delivery Receipt<br>4. Click </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Purchase Order Number</span><span style="font-family:Arial;color:#000000;"> drop-down field<br>5. Check list of values displayed<br>6. Search a keyword in </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Purchase Order Number </span><span style="font-family:Arial;color:#000000;">dropdown field <br>7. Search a keyword that is not existing on the list<br></span></td><td class="s25"></td><td class="s24"><br><br>1. Should update the behavior of the Drop-down Fields across Cityland PRS<br>2. Should implement a Combobox Drop-down<br>    a. Should allow searching of the Values in the Drop-down Fields<br>        i. Should allow searching only for a Keyword<br>           i) Should display matched Data upon typing of Keyword<br>        ii. Should display No Data if no Value has matched the entered Keyword<br>3. Should sort the Values of the Drop-downs Alphabetically [A-Z]<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard<br>5. Should allow the User to use Enter Key and Space Bar in the Keyboard in selecting the option in the Drop-down Values<br>6. Should update the Drop-down Fields in the following:<br>    a. Selected Drop-down Fields are those with options that were greater than five Options<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s4">PRS-029-043</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Delivery Receipt Tab</span><span style="font-family:Arial;color:#000000;"> if all the Combobox Drop-down field keyboard navigation keys are working as expected</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24"><span style="font-family:Arial;color:#000000;">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action &gt;&gt; Click Record Delivery Receipt<br>2. Click Selecting of Purchase Order Number drop-down fileds <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click Selecting of Purchase Order Number drop-down fileds <br><br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-family:Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-family:Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s25"></td><td class="s24"><span style="font-family:Arial;color:#000000;">1. Should display Delivery Receipt  Modal<br>2. Should implement a Combobox Drop-down on the following:<br>  a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on the following:<br>   a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br><br></span></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">Note: On changing PO Number there will be a popup modal for confirmation</td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s4">PRS-029-044</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate functionality of all the Drop-down Fields </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Payment Request Tab</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24"><span style="font-family:Arial;color:#000000;">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Create Payment Request<br>4. Click </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Purchase Order Number</span><span style="font-family:Arial;color:#000000;"> drop-down field<br>5. Check list of values displayed<br>6. Search a keyword in </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Purchase Order Number</span><span style="font-family:Arial;color:#000000;"> dropdown field <br>7. Search a keyword that is not existing on the list</span></td><td class="s25"></td><td class="s24"><br><br>1. Should update the behavior of the Drop-down Fields across Cityland PRS<br>2. Should implement a Combobox Drop-down<br>    a. Should allow searching of the Values in the Drop-down Fields<br>        i. Should allow searching only for a Keyword<br>           i) Should display matched Data upon typing of Keyword<br>        ii. Should display No Data if no Value has matched the entered Keyword<br>3. Should sort the Values of the Drop-downs Alphabetically [A-Z]<br>4. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard<br>5. Should allow the User to use Enter Key and Space Bar in the Keyboard in selecting the option in the Drop-down Values<br>6. Should update the Drop-down Fields in the following:<br>    a. Selected Drop-down Fields are those with options that were greater than five Options<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s4">PRS-029-045</td><td class="s28">Allow combobox Drop-down Fields for all Fields in PRS</td><td class="s24"><span style="font-family:Arial;color:#000000;">Validate </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Payment Request Tab</span><span style="font-family:Arial;color:#000000;"> if all the Combobox Drop-down field keyboard navigation keys are working as expected</span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24"><span style="font-family:Arial;color:#000000;">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action &gt;&gt; Click Create Payment Request<br>2. Click Selecting of </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Purchase Order Number </span><span style="font-family:Arial;color:#000000;">drop-down fileds <br>3. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Click Enter Key in Keyboard<br>5. Click Selecting of </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Purchase Order Number</span><span style="font-family:Arial;color:#000000;"> drop-down fileds <br><br>6. Check Navigation using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Click Space Bar in Keyboard<br><br></span><span style="font-family:Arial;font-weight:bold;color:#000000;">Searching  Scenarios for Steps 3 and 6:</span><span style="font-family:Arial;color:#000000;"><br>-Do a partial search &gt; Delete the input on search &gt; Dropdown values still remain open &gt; User clicks the keyboard keys up, down arrows and enter <br>-Without Search &gt; User holds the up or down arrow keys &gt; Not scrolling down<br>-With partial search &gt; User holds the up or down arrow keys &gt; Not scrolling down</span></td><td class="s25"></td><td class="s24"><span style="font-family:Arial;color:#000000;">1. Should display Payment Request Modal<br>2. Should implement a Combobox Drop-down on the following:<br>  a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>3. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>4. Should be able to select value in drop down field<br>5. Should implement a Combobox Drop-down on the following:<br>   a. Enter Canvass - Supplier<br>     b. Enter Canvass - Terms<br><br>6. Should allow the User to navigate through the Drop-down Options using Arrow up and Arrow Down keys of the Keyboard </span><span style="font-family:Arial;font-weight:bold;color:#000000;">on both when Searching or Not  </span><span style="font-family:Arial;color:#000000;"><br>7. Should be able to select values in drop down field<br><br><br></span></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s4">PRS-029-046</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">RS Dashboard Sorting</span><span style="font-family:Arial;color:#000000;"> should be Retained after Viewing the RS Details </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard, Click Sorting Column RS Dashboard tabs <br>2. Click RS Number &gt;&gt; to View RS Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24">1. Should be retain the Sorted Table afte viewing RS Details<br>    a. Applied for All RS Dashboard Tabs<br><br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Retaining of selected sorting to the Tables of PRS] RS Dashboard Sorted Column resets after going back from a draft status RS</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1128">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1128</a></td></tr><tr style="height: 19px"><th id="374143680R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s4">PRS-029-047</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Column in</span><span style="font-family:Arial;color:#000000;"> </span><span style="font-family:Arial;font-weight:bold;color:#000000;">User Management </span><span style="font-family:Arial;color:#000000;">should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Admin Tab<br>2. Click User Management <br>3. Click Sorting Coulmn in User Management page<br>4. Click User Name &gt;&gt; to View User Details<br>3. Click X Button or Close Window Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing User Details<br>  a. Applied for All Tabs<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s4">PRS-029-048</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Column in</span><span style="font-family:Arial;color:#000000;"> </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Supplier Management</span><span style="font-family:Arial;color:#000000;"> should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Manage Tab<br>2. Click Supplier <br>3. Click Sorting Coulmn in Supplier List<br>4. Click Supplier Name &gt;&gt; to View Supplier Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing Supplier Details<br>  a. Applied for All Tabs<br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Retaining of selected sorting to the Tables of PRS] Line of Business column sorting is not working in supplier list under Manage tab</td><td class="s30"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1129">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1129</a></td></tr><tr style="height: 19px"><th id="374143680R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s4">PRS-029-049</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Coulmn in Company/Association Management</span><span style="font-family:Arial;color:#000000;"> should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Manage Tab<br>2. Click Company<br>3. Click Sorting Coulmn in Company/Association Management<br>4. Click Company/Association Name &gt;&gt; to View Company/Association Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing Company Details or Association Details<br>  a. Applied for All Tabs<br><br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Retaining of selected sorting to the Tables of PRS] Company/Association Sorted columns reset after going back from a company/association details when Co./Assoc. is Association</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1132">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1132</a></td></tr><tr style="height: 19px"><th id="374143680R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s4">PRS-029-050</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Coulmn in Project Management</span><span style="font-family:Arial;color:#000000;"> should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Manage Tab<br>2. Click Project<br>3. Click Sorting Coulmn in Project List<br>4. Click Project Name &gt;&gt; to View Project Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing Project Details<br>  a. Applied for All Tabs</td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Retaining of selected sorting to the Tables of PRS] Company and Address Sorting is not working correctly in Project list under Manage tab</td><td class="s30"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1134">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1134</a></td></tr><tr style="height: 19px"><th id="374143680R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s4">PRS-029-051</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Coulmn in Department Management</span><span style="font-family:Arial;color:#000000;"> should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Manage Tab<br>2. Click Department<br>3. Click Sorting Coulmn in Department Management<br>4. Click Department Name &gt;&gt; to View Department Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing Department Details<br>  a. Applied for All Tabs<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s4">PRS-029-052</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Coulmn in OFM Items</span><span style="font-family:Arial;color:#000000;"> should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Items Tab<br>2. Click OFM <br>3. Click Sorting Coulmn in OFM Items<br>4. Click Item Code &gt;&gt; to View Item Code Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing an OFM Item Details<br>  a. Applied for All Tabs<br></td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Retaining of selected sorting to the Tables of PRS] Steelbars column sorting is not working in OFM Items under Items tab<br><br>[Retaining of selected sorting to the Tables of PRS] Sort not being retained for OFM items under Items tab</td><td class="s30"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1143">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1143<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1144</a></td></tr><tr style="height: 19px"><th id="374143680R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s4">PRS-029-053</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Coulmn in OFM List </span><span style="font-family:Arial;color:#000000;">should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Items Tab<br>2. Click OFM List<br>3. Click Sorting Coulmn in OFM List<br>4. Click List Name &gt;&gt; to View List Name Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing an OFM List Details<br>  a. Applied for All Tabs<br></td><td class="s6"></td><td class="s6"></td><td class="s19">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="374143680R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s4">PRS-029-054</td><td class="s29">Retaining of selected sorting to the Tables of PRS</td><td class="s24"><span style="font-family:Arial;font-weight:bold;color:#000000;"><br></span><span style="font-family:Arial;color:#000000;">Validate if </span><span style="font-family:Arial;font-weight:bold;color:#000000;">Sorting Coulmn in Non-OFM Items </span><span style="font-family:Arial;color:#000000;">should be Retained </span></td><td class="s24">Critical</td><td class="s24">1. Users must be logged in to their Accounts </td><td class="s24">1. In Dashboard,Go to Items Tab<br>2. Click Non-OFM<br>3. Click Sorting Coulmn in Non-OFM Items<br>4. Click Item Name &gt;&gt; to View Non-OFM Details<br>3. Click Go Back Button<br>4. Validated the Sorted Table</td><td class="s25"></td><td class="s24"><br>1. Should be retain the Sorted Table after viewing a Non-OFM Item Details<br>  a. Applied for All Tabs</td><td class="s6"></td><td class="s6"></td><td class="s14">Failed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">[Retaining of selected sorting to the Tables of PRS] Columns sorting doesn&#39;t retain when going back from the Non-OFM item code</td><td class="s30"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1137">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1137</a></td></tr></tbody></table></div>