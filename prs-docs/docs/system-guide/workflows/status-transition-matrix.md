# Status Transition Matrix

This document defines the valid status transitions for each workflow in the PRS system. It serves as a reference for both frontend and backend developers to ensure consistent implementation of workflow rules.

## Requisition Workflow

### Status Definitions

| Status | Backend Value | Frontend Display | Description |
|--------|---------------|------------------|-------------|
| Draft | draft | Draft | Initial status when a requisition is created but not submitted |
| Submitted | submitted | For Approval | Requisition has been submitted for approval |
| Approved | approved | Approved | All approvers have approved the requisition |
| Rejected | rejected | Rejected | Requisition has been rejected by an approver |
| Assigned | assigned | Assigned | Requisition has been assigned to a purchasing staff |
| Assigning | assigning | Assigning | Requisition is in the process of being assigned |
| Canvass For Approval | canvass_for_approval | Canvass Approval | Canvass has been submitted for approval |
| Partially Canvassed | partially_canvassed | Partially Canvassed | Not all requisition items are included in the canvass |
| For PO Review | for_po_review | For PO Review | Purchase orders have been created and are under review |
| For Delivery | for_delivery | For Delivery | Purchase orders have been approved and are awaiting delivery |
| Closed | closed | Closed | All items have been delivered and paid for |

### Valid Transitions

```mermaid
stateDiagram-v2
    [*] --> draft: Create
    draft --> submitted: Submit
    submitted --> approved: All Approve
    submitted --> rejected: Any Reject
    rejected --> submitted: Resubmit
    approved --> assigning: Ready for Assignment
    assigning --> assigned: Assign Staff
    assigned --> partially_canvassed: Create Partial Canvass
    assigned --> canvass_for_approval: Create Complete Canvass
    partially_canvassed --> canvass_for_approval: Complete Canvass
    canvass_for_approval --> for_po_review: Approve Canvass
    for_po_review --> for_delivery: Approve PO
    for_delivery --> closed: All Items Delivered & Paid
```

### Transition Rules

| Current Status | Valid Next Status | Required Permission | Frontend Action |
|----------------|-------------------|---------------------|-----------------|
| draft | submitted | submit_requisition | Submit button |
| submitted | approved | approve_requisition | Approve button |
| submitted | rejected | reject_requisition | Reject button |
| rejected | submitted | submit_requisition | Resubmit button |
| approved | assigning | assign_requisition | Assign button |
| assigning | assigned | assign_requisition | Select Assignee button |
| assigned | partially_canvassed | create_canvass | Create Canvass button |
| assigned | canvass_for_approval | create_canvass | Create Canvass button |
| partially_canvassed | canvass_for_approval | update_canvass | Update Canvass button |
| canvass_for_approval | for_po_review | approve_canvass | Approve Canvass button |
| for_po_review | for_delivery | approve_po | Approve PO button |
| for_delivery | closed | close_requisition | Close button |

## Canvass Workflow

### Status Definitions

| Status | Backend Value | Frontend Display | Description |
|--------|---------------|------------------|-------------|
| Draft | draft | CS Draft | Initial status when a canvass is created but not submitted |
| Partially Canvassed | partially_canvassed | Partially Canvassed | Not all requisition items are included in the canvass |
| For Approval | for_approval | For CS Approval | Canvass has been submitted for approval |
| Approved | approved | CS Approved | All approvers have approved the canvass |
| Rejected | rejected | CS Rejected | Canvass has been rejected by an approver |

### Valid Transitions

```mermaid
stateDiagram-v2
    [*] --> draft: Create
    draft --> partially_canvassed: Submit Partial
    draft --> for_approval: Submit Complete
    partially_canvassed --> for_approval: Complete
    for_approval --> approved: All Approve
    for_approval --> rejected: Any Reject
    rejected --> for_approval: Resubmit
    approved --> [*]: Generate POs
```

### Transition Rules

| Current Status | Valid Next Status | Required Permission | Frontend Action |
|----------------|-------------------|---------------------|-----------------|
| draft | partially_canvassed | submit_canvass | Submit button (partial) |
| draft | for_approval | submit_canvass | Submit button (complete) |
| partially_canvassed | for_approval | update_canvass | Update button |
| for_approval | approved | approve_canvass | Approve button |
| for_approval | rejected | reject_canvass | Reject button |
| rejected | for_approval | submit_canvass | Resubmit button |

## Purchase Order Workflow

### Status Definitions

| Status | Backend Value | Frontend Display | Description |
|--------|---------------|------------------|-------------|
| For PO Review | for_po_review | For PO Review | Initial status when a purchase order is created |
| For PO Approval | for_po_approval | For PO Approval | Purchase order has been submitted for approval |
| For Delivery | for_delivery | For Delivery | Purchase order has been approved and is awaiting delivery |
| Rejected PO | reject_po | Rejected PO | Purchase order has been rejected by an approver |
| Cancelled PO | cancelled_po | Cancelled PO | Purchase order has been cancelled |

### Valid Transitions

```mermaid
stateDiagram-v2
    [*] --> for_po_review: Create
    for_po_review --> for_po_approval: Submit
    for_po_review --> cancelled_po: Cancel
    for_po_approval --> for_delivery: All Approve
    for_po_approval --> reject_po: Any Reject
    for_po_approval --> cancelled_po: Cancel
    reject_po --> for_po_approval: Resubmit
    reject_po --> cancelled_po: Cancel
    for_delivery --> [*]: All Items Delivered
```

### Transition Rules

| Current Status | Valid Next Status | Required Permission | Frontend Action |
|----------------|-------------------|---------------------|-----------------|
| for_po_review | for_po_approval | submit_po | Submit button |
| for_po_review | cancelled_po | cancel_po | Cancel button |
| for_po_approval | for_delivery | approve_po | Approve button |
| for_po_approval | reject_po | reject_po | Reject button |
| for_po_approval | cancelled_po | cancel_po | Cancel button |
| reject_po | for_po_approval | submit_po | Resubmit button |
| reject_po | cancelled_po | cancel_po | Cancel button |

## API Endpoints for Status Transitions

### Requisition Workflow

| Transition | API Endpoint | Method | Required Status | Resulting Status |
|------------|--------------|--------|----------------|------------------|
| Submit | /v1/requisitions/submit | PUT | draft | submitted |
| Approve | /v1/requisitions/{id}/approve | PUT | submitted | approved |
| Reject | /v1/requisitions/{id}/reject | PUT | submitted | rejected |
| Assign | /v1/requisitions/{id}/assign | PUT | approved | assigned |
| Cancel | /v1/requisitions/{id}/cancel | PUT | * | cancelled |

### Canvass Workflow

| Transition | API Endpoint | Method | Required Status | Resulting Status |
|------------|--------------|--------|----------------|------------------|
| Submit | /v1/canvass/ | POST | N/A | draft/partially_canvassed/for_approval |
| Approve | /v1/canvass/{id}/approve | POST | for_approval | approved |
| Reject | /v1/canvass/{id}/reject | POST | for_approval | rejected |

### Purchase Order Workflow

| Transition | API Endpoint | Method | Required Status | Resulting Status |
|------------|--------------|--------|----------------|------------------|
| Submit | /v1/purchase-orders/{id}/submit | POST | for_po_review | for_po_approval |
| Approve | /v1/purchase-orders/{id}/approve | POST | for_po_approval | for_delivery |
| Reject | /v1/purchase-orders/{id}/reject | POST | for_po_approval | reject_po |
| Resubmit | /v1/purchase-orders/{id}/resubmit-rejected-po | POST | reject_po | for_po_approval |
| Cancel | /v1/purchase-orders/{id}/cancel | POST | * | cancelled_po |

## Frontend Components for Status Transitions

### Requisition Workflow

| Component | Action | API Mutation | Status Check |
|-----------|--------|--------------|--------------|
| SubmitRequisitionButton | Submit | useSubmitRequisition | status === 'draft' |
| ApproveRequisitionButton | Approve | useApproveRequisition | status === 'submitted' && isPendingApprover |
| RejectRequisitionButton | Reject | useRejectRequisition | status === 'submitted' && isPendingApprover |
| AssignRequisitionButton | Assign | useAssignRequisition | status === 'approved' && isPurchasingHead |
| CancelRequisitionButton | Cancel | useCancelRequisition | status !== 'closed' && isRequestor |

### Canvass Workflow

| Component | Action | API Mutation | Status Check |
|-----------|--------|--------------|--------------|
| SubmitCanvassButton | Submit | useSubmitCanvass | isAssignedPurchasingStaff |
| ApproveCanvassButton | Approve | useApproveCanvass | status === 'for_approval' && isPendingApprover |
| RejectCanvassButton | Reject | useRejectCanvass | status === 'for_approval' && isPendingApprover |

### Purchase Order Workflow

| Component | Action | API Mutation | Status Check |
|-----------|--------|--------------|--------------|
| SubmitPOButton | Submit | useSubmitPO | status === 'for_po_review' && isAssignedPurchasingStaff |
| ApprovePOButton | Approve | useApprovePO | status === 'for_po_approval' && isPendingApprover |
| RejectPOButton | Reject | useRejectPO | status === 'for_po_approval' && isPendingApprover |
| ResubmitPOButton | Resubmit | useResubmitPO | status === 'reject_po' && isAssignedPurchasingStaff |
| CancelPOButton | Cancel | useCancelPO | (status === 'for_po_review' || status === 'for_po_approval') && isAssignedPurchasingStaff |
