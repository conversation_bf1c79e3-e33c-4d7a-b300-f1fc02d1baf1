{"name": "Node.js 22 Alpine", "build": {"dockerfile": "Dockerfile", "context": "."}, "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "forwardPorts": [3000], "postCreateCommand": "npm install -g npm@latest", "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}}}}}