const { ZodError } = require('zod');
const { Sequelize } = require('sequelize');
const AppError = require('./appError');
const ErrorTranslator = require('./errorTranslator');

/**
 * Global error handler for the application
 *
 * @param {Error} error - Error to handle
 * @param {Object} _request - Fastify request object
 * @param {Object} reply - Fastify reply object
 * @returns {Object} - Fastify reply
 */
const errorHandler = function (error, _request, reply) {
  // Handle special case for "reply.onSend is not a function" error
  if (error.message === 'reply.onSend is not a function') {
    this.log.error({
      errorType: 'INTERNAL_SERVER_ERROR',
      'x-request-id': _request.id,
      message: 'Swagger UI route error: reply.onSend is not a function',
      errorCode: 'SWAGGER_UI_ERROR',
      stack: error.stack,
    });

    // Redirect to the Swagger UI root instead
    return reply.redirect('/api-docs/');
  }

  // Translate error to AppError
  let appError;
  try {
    appError = ErrorTranslator.translate(error, {
      operation: _request.routeOptions?.config?.url || 'unknown',
      resource: _request.routeOptions?.config?.url?.split('/')[1]
        ? (_request.params?.id
          ? `${_request.routeOptions.config.url.split('/')[1]} ${_request.params.id}`
          : _request.routeOptions.config.url.split('/')[1])
        : 'unknown'
    });
  } catch (translationError) {
    // If error translation fails, create a generic AppError
    appError = new AppError({
      message: error.message || 'An unexpected error occurred',
      errorCode: 'INTERNAL_SERVER_ERROR',
      statusCode: 500,
      originalError: error
    });
  }

  // Determine error type for logging
  const errorType = determineErrorType(error);

  // Log error
  this.log.error({
    errorType,
    'x-request-id': _request.id,
    message: appError.message,
    errorCode: appError.errorCode,
    stack: appError.stack,
    originalError: appError.originalError?.message,
    timestamp: appError.timestamp,
  });

  // Send response
  return reply.status(appError.getHttpStatus()).send(appError.toResponse());
};

/**
 * Determines the error type for logging
 *
 * @param {Error} error - Error to determine type for
 * @returns {string} - Error type
 */
function determineErrorType(error) {
  if (error instanceof AppError) {
    return error.errorCode;
  }

  if (error instanceof ZodError) {
    return 'VALIDATION_ERROR';
  }

  if (error instanceof Sequelize.Error) {
    return 'DATABASE_ERROR';
  }

  return error?.errorCode || 'INTERNAL_SERVER_ERROR';
}

// Add determineErrorType to the error handler
errorHandler.determineErrorType = determineErrorType;

module.exports = errorHandler;
