<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cccccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1854861251C0" style="width:103px;" class="column-headers-background">A</th><th id="1854861251C1" style="width:98px;" class="column-headers-background">B</th><th id="1854861251C2" style="width:252px;" class="column-headers-background">C</th><th id="1854861251C3" style="width:252px;" class="column-headers-background">D</th><th id="1854861251C4" style="width:233px;" class="column-headers-background">E</th><th id="1854861251C5" style="width:330px;" class="column-headers-background">F</th><th id="1854861251C6" style="width:184px;" class="column-headers-background">G</th><th id="1854861251C7" style="width:349px;" class="column-headers-background">H</th><th id="1854861251C8" style="width:100px;" class="column-headers-background">I</th><th id="1854861251C9" style="width:192px;" class="column-headers-background">J</th><th id="1854861251C10" style="width:114px;" class="column-headers-background">K</th><th id="1854861251C11" style="width:114px;" class="column-headers-background">L</th><th id="1854861251C12" style="width:114px;" class="column-headers-background">M</th><th id="1854861251C13" style="width:114px;" class="column-headers-background">N</th><th id="1854861251C14" style="width:78px;" class="column-headers-background">O</th><th id="1854861251C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1854861251R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr style="height: 19px"><th id="1854861251R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-011- [RS Creation] -</td></tr><tr style="height: 19px"><th id="1854861251R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-011-001</td><td class="s5">RS CREATION</td><td class="s6">Critical</td><td class="s5">Validate Requisition Slip page</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Dashboard &gt; Click New Request<br>2. Should display the following Fields:<br>    a. Charge To (Category) - Required<br>        i. Drop-down with values of Company, Project, Association, or Supplier<br>    b.  Charge To (Client)  - Required<br>        i. Drop-down of values depending on the selected Category<br>    c. Type of Request  - Required<br>        i. OFM<br>        ii. Non-OFM<br>        iii. OFM Transfer of Materials<br>        iv.  Non-OFM Transfer of Materials<br>    d. Company  - Required<br>        i. Drop-down of all Companies that were Synced<br>    e. Project  - Required<br>        i. Drop-down of all Projects that were Synced<br>    f. Department  - Required<br>        i. Should be by Default the assigned Department of the Approver in User Management<br>    g. Date Required   - Required<br>        i. Date Picker that the Items will be needed<br>        ii. Allow selecting of Date greater than the current Date<br>    h. Deliver To<br>        i. Drop-down of Addresses from Company and Projects<br>    i. Purpose - Required<br>        i. Text Field<br>        ii. Maximum of 50 Characters<br>        iii. Alphanumeric and specific Special Charactes: ,.-&#39;<br>    j. Attachments<br>       i. Uploading of Files such as Images (JPG,JPEG,PNG) and PDF<br>       ii. Maximum of 25MB per File<br>       iii. Note should display &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.&quot;<br>    k. Add Note<br>       i. Text area of Notes with a Maximum Character of 100 Characters<br>    l. Items Table List<br>       i. Should be blank<br>       ii. Should have search that will allow searching by Item Name entered within the Request<br>       iii. Should have Add Item Button<br><br>3. Validate Save Draft button</td><td class="s7"></td><td class="s5">All fields should be present upon clicking &#39;New Request&#39;</td><td class="s8" rowspan="6"></td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11" rowspan="29"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=1048379932#gid=1048379932">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=1048379932#gid=1048379932</a></td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-011-002</td><td class="s5">RS CREATION</td><td class="s6">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with OFM Items</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the below fields. Check parameters.<br>   a. Charge To (Category) - Required<br>    b.  Charge To (Client)  - Required<br>    c. Type of Request  - Required<br>        i. OFM<br>    d. Company  - Required<br>    e. Project  - Required<br>    f. Department  - Required<br>    g. Date Required   - Required<br>    h. Deliver To<br>    i. Purpose - Required<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters<br>5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom<br>6. Click Submit<br>7. User will be redirected to Dashboard page<br>8. RS number will be automatically generated and details will be displayed on top of the table</td><td class="s5">    a. Charge To (Category) - Company<br>    b.  Charge To (Client)  - &lt;any&gt;<br>    c. Type of Request  - OFM<br>    d. Company  - &lt;any&gt;<br>    e. Project  - &lt;any&gt;<br>    f. Department  - &lt;any&gt;<br>    g. Date  - &lt;any&gt;<br>    h. Deliver To<br>    i. Purpose - 50 characters with  alpanumeric such as ,.-&#39;<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! &quot;&#39;, @+=</td><td class="s5">1. Requisition slip will be sucessfully submitted and will be visible on top of the table<br>2. Will be reflected on Dashboard with RS number automatically generated <br>3. Correct details details will be displayed<br><br>Ex:<br>R.S. Number - 32AA00000017<br>Type - non-ofm        <br>Requestor - Kamille Agregado        <br>Company - AF CITYLAND CROWN DEVELOPMENT<br>Project - ALABANG HEIGHTS<br>Department - EXEC.-S.R.        <br>Date Requested - 15 Jan 2025        <br>Last Updated - 15 Jan 2025        <br>Status - Submitted</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 211px"><th id="1854861251R4" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">5</div></th><td class="s5">PRS-011-003</td><td class="s5">RS CREATION</td><td class="s6">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with Non-OFM Items</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the below fields. Check parameters.<br>   a. Charge To (Category) - Required<br>    b.  Charge To (Client)  - Required<br>    c. Type of Request  - Required<br>        i. Non-OFM<br>    d. Company  - Required<br>    e. Project  - Required<br>    f. Department  - Required<br>    g. Date Required    - Required<br>    h. Deliver To<br>    i. Purpose - Required<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters<br>5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom<br>6. Click Submit<br>7. User will be redirected to Dashboard page<br>8. RS number will be automatically generated and details will be displayed on top of the table</td><td class="s5">    a. Charge To (Category) - Project<br>    b.  Charge To (Client)  - &lt;any&gt;<br>    c. Type of Request  - Non-OFM<br>    d. Company  - &lt;any&gt;<br>    e. Project  - &lt;any&gt;<br>    f. Department  - &lt;any&gt;<br>    g. Date  - &lt;any&gt;<br>    h. Deliver To<br>    i. Purpose - 50 characters with  alpanumeric such as ,.-&#39;<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! &quot;&#39;, @+=</td><td class="s5">1. Requisition slip will be sucessfully submitted and will be visible on top of the table<br>2. Will be reflected on Dashboard with RS number automatically generated <br>3. Correct details details will be displayed<br><br>Ex:<br>R.S. Number - 32AA00000017<br>Type - non-ofm        <br>Requestor - Kamille Agregado        <br>Company - AF CITYLAND CROWN DEVELOPMENT<br>Project - ALABANG HEIGHTS<br>Department - EXEC.-S.R.        <br>Date Requested - 15 Jan 2025        <br>Last Updated - 15 Jan 2025        <br>Status - Submitted</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">2/28: Able to create but w/ Bug raised</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-973">2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-973</a></td></tr><tr style="height: 211px"><th id="1854861251R5" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">6</div></th><td class="s5">PRS-011-004</td><td class="s5">RS CREATION</td><td class="s6">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with Transfer of Materials</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the below fields. Check parameters.<br>   a. Charge To (Category) - Required<br>    b.  Charge To (Client)  - Required<br>    c. Type of Request  - Required<br>        i. Transfer of Materials<br>    d. Company  - Required<br>    e. Project  - Required<br>    f. Department  - Required<br>    g. Date Required  - Required<br>    h. Deliver To<br>    i. Purpose - Required<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters including alphanumeric 01-$ $% ()-! &quot;&#39;, @+=<br>5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom<br>6. Click Submit<br>7. User will be redirected to Dashboard page<br>8. RS number will be automatically generated and details will be displayed on top of the table</td><td class="s5">    a. Charge To (Category) - Supplier<br>    b.  Charge To (Client)  - &lt;any&gt;<br>    c. Type of Request  - Transfer of Materials<br>    d. Company  - &lt;any&gt;<br>    e. Project  - &lt;any&gt;<br>    f. Department  - &lt;any&gt;<br>    g. Date  - &lt;any&gt;<br>    h. Deliver To<br>    i. Purpose -  50 characters with  alpanumeric such as ,.-&#39;<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! &quot;&#39;, @+=</td><td class="s5">1. Requisition slip will be sucessfully submitted and will be visible on top of the table<br>2. Will be reflected on Dashboard with RS number automatically generated <br>3. Correct details details will be displayed<br><br>Ex:<br>R.S. Number - 32AA00000017<br>Type - non-ofm        <br>Requestor - Kamille Agregado        <br>Company - AF CITYLAND CROWN DEVELOPMENT<br>Project - ALABANG HEIGHTS<br>Department - EXEC.-S.R.        <br>Date Requested - 15 Jan 2025        <br>Last Updated - 15 Jan 2025        <br>Status - Submitted</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">2/28: Able to create but w/ Bug raised</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-891">2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-891</a></td></tr><tr style="height: 211px"><th id="1854861251R6" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">7</div></th><td class="s8">PRS-011-005</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate error message returns when required fields are empty</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Leave the required fields empty.<br>   a. Charge To (Category) - Required<br>    b.  Charge To (Client)  - Required<br>    c. Type of Request  - Required<br>    d. Company  - Required<br>    e. Project  - Required<br>    f. Department  - Required<br>    g. Date Required  - Required<br>    i. Purpose - Required<br>5. Click Save Draft<br>6. Validate error message returns &quot;Please input ONLY: ofm, non-ofm or transfer&quot;<br>7. Populate &#39;Type of Request&#39;<br>8. Click Save Draft. Validate error message returns &quot;Invalid date&quot;<br>9. Populate Date  Required<br>10. Click Save Draft. Validate error message returns &quot;Create RS Purpose must not be Empty&quot;<br>11. Populate Purpose with 50 characters and alpanumeric such as ,.-&#39;<br>12. Click Save Draft. Validate error message returns &quot;Invalid charge to (category)&quot;<br>13. Populate Charge To (Category)<br>14. Click Save Draft. Validate error message returns &quot;Charge to (client) is required&quot;<br>15. Populate Charge To (Client)<br>16. Click Save Draft. Validate error message returns &quot;Department not found&quot;<br>17. Populate Department<br>18.  Click Save Draft. Validate error message returns &quot;Project not found&quot;<br>19. Populate Project<br>20. Click Save Draft. Validate error message returns &quot;Company not found&quot;<br>21. Populate Company<br>22. Click Save Draft. Validate green pop-up message returns &quot;Draft created successfully&quot;<br>23. Click Submit<br>24. Validate green pop-up message returns &quot;Requisition Submitted successfully&quot;</td><td class="s8">   a. Charge To (Category) - &lt;empty&gt;<br>    b.  Charge To (Client)  - &lt;empty&gt;<br>    c. Type of Request  - &lt;empty&gt;<br>    d. Company  - &lt;empty&gt;<br>    e. Project  - &lt;empty&gt;<br>    f. Department  - &lt;empty&gt;<br>    g. Date  - &lt;empty&gt;<br>    i. Purpose - &lt;empty&gt;</td><td class="s8">Error message with associated field should return on every field in question</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">2/28: Able to create but w/ Bug raised</td><td class="s8">2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-980</td></tr><tr style="height: 211px"><th id="1854861251R7" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">8</div></th><td class="s8">PRS-011-006</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate error message returns when input is invalid</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4.  Input 51 Characters in &quot;Purpose&quot;<br>5. Validate error message returns &quot;Create RS Purpose maximum 50 characters&quot;<br>6. Validate you cannot input more than 100 characters in notes</td><td class="s15"></td><td class="s8">Error message will return for purpose and input has limitation for Notes</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 105px"><th id="1854861251R8" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">9</div></th><td class="s8">PRS-011-007</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate past date is not allowed</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Click past dates on Date Required<br>5. User should not be able to select past dates and past months/years</td><td class="s15"></td><td class="s8">User should not be able to select past dates  and past months/years</td><td class="s8" rowspan="6"></td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 105px"><th id="1854861251R9" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">10</div></th><td class="s8">PRS-011-008</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate error message returns for attachment with more than 25MB</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Upload attachment with more than 25MB<br>5. Validate error message associated with the field in question should return</td><td class="s15"></td><td class="s8">Validate error message associated with the field in question should return</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 105px"><th id="1854861251R10" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">11</div></th><td class="s8">PRS-011-009</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Uploading other files than  PNG, JPG, JPEG, PDF, Excel, CSV in RS Creation should return error message</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Upload zip file, ppt, PNG, txt, mp4, JSON, gif<br>5. Click Save Draft<br>6. Validate error message returns &quot;Invalid file type. Only PNG, JPG, JPEG, PDF, Excel, and CSV are allowed.&quot;</td><td class="s15"></td><td class="s8">Should not save. Validate error message returns &quot;Invalid file type. Only PNG, JPG, JPEG, PDF, Excel, and CSV are allowed.&quot;</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s8">PRS-011-010</td><td class="s8">RS CREATION</td><td class="s14">Minor</td><td class="s8">Uploading combination of valid and invalid format in RS Creation should return error message</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Upload PNG, JPG, zip file, ppt<br>5. Click Save Draft<br>6. Validate error message returns &quot;Invalid file type. Only PNG, JPG, JPEG, PDF, Excel, and CSV are allowed.&quot;</td><td class="s15"></td><td class="s8">Should not save. Validate error message returns &quot;Invalid file type. Only PNG, JPG, JPEG, PDF, Excel, and CSV are allowed.&quot;</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s8">PRS-011-011</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate multiple upload of same files</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Upload same file 5 times<br>5. Validate naming convention has unique number added to file name<br>6. Populate all required fields. Click Save Draft<br>7. Upload should be successful</td><td class="s15"></td><td class="s8">Upload is succesful with unique numbers added to file name</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s8">PRS-011-012</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate multiple attachment works for different and large file sizes</td><td class="s15"></td><td class="s8">Per BA, there is no limit in uploading files<br><br>1. Login<br>2. Click Dashboard<br>3. Upload 20 files with different file sizes at the same time<br>4. Save as Draft<br>5. Draft is successful<br>6. Click Submit<br>5. Submit is successful<br>7. Click created RS<br>8. Click Check attachments. Validate all 20 attachments were uploaded successfully<br>9. Click New Request again<br>10. Upload 20 files with 24MB or 25MB in size at the same time. You can re-use same file but different naming convention<br>11. Click Save Draft and should be successful<br>12. Click Submit and should be successful<br>13. Click Check attachments. Validate all 20 files with 24MB or 25MB in size were uploaded successfully</td><td class="s15"></td><td class="s8">Uploading 20 files with different sizes is successful<br>Uploading 20 files with large size is successful</td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">3/3: Issue has been logged</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1854861251R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s8">PRS-011-013</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate uploading additional attachment works in Draft RS</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Upload 2 files<br>4. Save as Draft<br>5. Draft should be successful with 2 files<br>6. Click same RS<br>7. Upload another 2 files<br>8. Draft should be successful and display the additional 2 files<br>9. Re-open same RS and Validate there are total of 4 files</td><td class="s15"></td><td class="s8">Successfully saved as Draft and additional attachment is reflected with scroll bar</td><td class="s8" rowspan="4"></td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">3/3: Issue has been logged</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1854861251R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s8">PRS-011-014</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate uploading additional multiple attachment works in Draft RS</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>3. Upload 2 files at the same time<br>4. Save as Draft<br>5. Draft should be successful with 2 files<br>6. Click same RS<br>7. Upload another 2 files<br>8. Draft should be successful and display the additional 2 files<br>9. Re-open same RS and Validate there are total of 4 files</td><td class="s15"></td><td class="s8">Upload should be successful when user upload additional file on an existing draft RS</td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">3/3: Issue has been logged</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1854861251R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s8">PRS-011-015</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate scroll bar for multiple attachments</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>4. Upload 15 files at the same time<br>5. Validate scroll bar<br>6. Click Save Draft<br>7. Open saved RS<br>8. Validate scroll bar</td><td class="s15"></td><td class="s8">Scroll bar should be present when multiple files were uploaded</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 105px"><th id="1854861251R17" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">18</div></th><td class="s8">PRS-011-016</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate remove attachment works in draft RS</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>4. Upload 5 files at the same time<br>5. Delete 2 files by clicking &#39;x&#39;<br>6. Click Save Draft<br>7. Open same RS<br>8. Validate only 2 files were uploaded<br>9. Delete 1 file again<br>10. Click Save Draft<br>11. Validate only 1 file is present on attachment</td><td class="s15"></td><td class="s8">Delete file should work and reflect on draft RS</td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">3/3: Issue has been logged</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1854861251R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s8">PRS-011-017</td><td class="s8">Allow removing of Attachments per Request</td><td class="s14">High</td><td class="s8">Validate user should be able to remove an Attachment made to the Requisition Slip</td><td class="s15"></td><td class="s8">1. Create 1 Requisition Slip with 3 attachments till you reach each status below or find RS with below status:-<br><br>submitted<br>for-approval<br>assigning<br>assigned<br>partially-canvassed<br>canvass-approval<br>purchase-order<br>partially-ordered<br>transfer<br>pending<br>on-hold<br>returned<br>rejected<br>delivered<br>paying<br><br>2. Open RS<br>3. Click Check Attachments button<br>4. In attachment modal view, remove a file by clicking &#39;X&#39; icon<br>5.  Should display a Confirmation Modal once clicked<br>6.Once Confirmed, should remove the Attachment from the Requisition Slip<br>7. Should Log the Action in the System Audit Logs and Request History</td><td class="s8">Remove attachment for all RS with below status:<br><br>submitted<br>for-approval<br>assigning<br>assigned<br>partially-canvassed<br>canvass-approval<br>purchase-order<br>partially-ordered<br>transfer<br>pending<br>on-hold<br>returned<br>paying</td><td class="s8">Remove attachment should work for RS with the following status:<br><br>submitted<br>for-approval<br>assigning<br>assigned<br>partially-canvassed<br>canvass-approval<br>purchase-order<br>partially-ordered<br>transfer<br>pending<br>on-hold<br>returned<br>paying</td><td class="s8" rowspan="4"></td><td class="s18">Failed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">unable to remove attachemnts<br>om/issue/CITYLANDPRS-808</td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s8">PRS-011-018</td><td class="s8">RS CREATION</td><td class="s16"></td><td class="s8">Validate attachment cannot be removed for Cancelled and Closed RS status </td><td class="s15"></td><td class="s8">1. Create 1 Requisition Slip with 3 attachments till you reach each status below or find RS with below status:-<br><br>closed<br>cancelled<br>2. Open RS<br>3. Click Check Attachments button<br>4. In attachment modal view, remove a file by clicking &#39;X&#39; icon<br>5. Validate user should not be able to remove an attachment</td><td class="s15"></td><td class="s8">User should not be able to remove an attachment for RS with status as:<br>closed<br>cancelled</td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8">3/3: able to delete attachment</td><td class="s8">3/3: able to delete attachment</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1008">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1008</a></td></tr><tr style="height: 19px"><th id="1854861251R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s8">PRS-011-019</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate delete attachment on Draft RS works</td><td class="s15"></td><td class="s8">1. Click Dashboard &gt; New Request<br>2. Add 3 attachments<br>3. Remove 1 attached file or Click &#39;x&#39;&#39;<br>4. Validate attachment is removed<br>5. Click Save Draft<br>6. Click Go back to dashboard<br>7. Open the draft RS created, verify there are only 2 attached files</td><td class="s15"></td><td class="s8">Attachment is deleted for Draft RS</td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">3/3: Issue has been logged</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1854861251R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s8">PRS-011-020</td><td class="s8">RS CREATION</td><td class="s14">Critical</td><td class="s8">Validate draft Requisition Slip is created</td><td class="s15"></td><td class="s8">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>4. Populate required fields<br>5. Click Save Draft<br>6. Go back to Dashboard<br>7. Validate the RS you created has draft status<br>8. Validate the RS Number format is in TMP-************ or ex: TMP-12AA00000011</td><td class="s15"></td><td class="s8">Should be able to suiccessfully create DRAFT requisition slip</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s8">PRS-011-021</td><td class="s8">RS CREATION</td><td class="s14">Critical</td><td class="s8">Validate updating Draft RS is successful</td><td class="s15"></td><td class="s8">1. Click an existing RS with Draft status<br>2. Update the values of the following fields<br>Charge to (Category)<br>Charge to (Client)<br>Date Required<br>Type of Request<br>Company<br>Project<br>Department<br>Deliver to<br>Purpose<br>Attachment/s<br>Notes<br>3. Click Save Draft<br>4. Validate table has updated value for all columns<br>5. Open created Draft RS<br>6. Validate all fields are updated with new value</td><td class="s15"></td><td class="s8">Update is successful and reflects on the table and on the Requisition Slip form/page</td><td class="s8" rowspan="2"></td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s8">PRS-011-022</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate uneditable fields on Submitted RS</td><td class="s15"></td><td class="s8">1. Click Dashboard &gt; New Request<br>2. Populate all required fields &gt; Save as Draft &gt; Submit<br>3. Open submitted RS<br>4. Validate fields are disabled and cannot be updated:<br>Type of Request<br>Date Required<br>Company<br>Project<br>Department<br>Purpose<br>Deliver To<br>Charge To<br><br>5. Validate buttons are enabled and can view:<br>attachments<br>notes</td><td class="s15"></td><td class="s8">Fields should not be editable</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s8">PRS-011-023</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Validate there are no duplicate Requisition Slip or Ref. Number created and displayed on the table</td><td class="s15"></td><td class="s8">1. Click dashboard<br>2. Validate table that there are no duplicates on Ref. Number column</td><td class="s15"></td><td class="s8">No duplicate Requisition Slip or Ref. Number created</td><td class="s8" rowspan="2"></td><td class="s9">Passed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-951">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-951</a></td></tr><tr style="height: 105px"><th id="1854861251R25" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">26</div></th><td class="s8">PRS-011-024</td><td class="s8">RS CREATION</td><td class="s14">Critical</td><td class="s8">Dropdown search should work</td><td class="s15"></td><td class="s8">NOT IMPLEMENTED<br><br>1. Validate Dropdown search should work for Company, Project, Deliver to</td><td class="s15"></td><td class="s8">Dropdown search should work</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s8">PRS-011-025</td><td class="s8">RS CREATION</td><td class="s14">Minor</td><td class="s8">Validate attachment and notes added should be available on Draft RS</td><td class="s15"></td><td class="s8">1. On Dashboard, click New Request<br>2. Populate all required fields<br>3. Upload 1 file on attachment<br>4. Add notes<br>5. Click Save draft<br>6. Go back to dashbard<br>7. Open created drafted RS<br>8. Validate that attachment is retained<br>9. Validate that notes is retained</td><td class="s15"></td><td class="s8">Attachment and Notes initially added should retain when user re-opens draft RS</td><td class="s8" rowspan="2"></td><td class="s18">Failed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">CITYLAND-807<br>3/3: Current behavior only attachments should retained in draft RS. Notes will displayed upon submit RS</td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s8">PRS-011-026</td><td class="s8">RS CREATION</td><td class="s14">Minor</td><td class="s8">Validate notes accepts emoji</td><td class="s15"></td><td class="s8">1. Create a RS<br>2. Populate all required fields<br>3. Add emoji on notes<br>4. Save as Draft<br>5. Click Submit<br>6. Open RS<br>7. Click Check Notes<br>8. Open notes and validate emoji is added</td><td class="s15"></td><td class="s8">Emoji should nott added on Notes</td><td class="s18">Failed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">able to accept emoji<br>CITYLANDPRS-738<br><br>3/3: still accepts emoji</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-582">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-582</a></td></tr><tr style="height: 19px"><th id="1854861251R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s8">PRS-011-027</td><td class="s8">RS CREATION</td><td class="s14">Minor</td><td class="s8">Validate timestamp gets updated every modification on Draft RS</td><td class="s15"></td><td class="s8">1. On Dashboard, click New Request<br>2. Populate all required fields. Save as Draft<br>3. Open draft RS<br>4. Upload attachment<br>5. Click Save<br>6. Timestamp at the bottom should change based on the time of your latest update<br><br><br>Example: Draft Saved:15 January 2025 | 9:01:44 PM</td><td class="s15"></td><td class="s8">Timestamp should get updated based on the time of your latest update</td><td class="s8" rowspan="2"></td><td class="s18">Failed</td><td class="s17">Failed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8">unable to update timestamp<br>CITYLANDPRS-810<br><br>3/3: Still encountered the error</td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s8">PRS-011-028</td><td class="s8">RS CREATION</td><td class="s14">High</td><td class="s8">Cancel requisition slip should work</td><td class="s15"></td><td class="s8">1. Dashboard &gt; Click New Request<br>2. Click Cancel button<br>3. Validate pop-up msg &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot;<br>4. Click &#39;&#39;Continue&quot; and you will be redirected to Dashboard page<br>5. Click New Request again<br>6. Populate all required fields<br>7. Save as draft. Draft saved successfully<br>8. Update purpose<br>9. Click Cancel<br>10. Validate pop-up msg &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot;<br>11. Re-open draft RS<br>12. Validate updated purpose do not reflect</td><td class="s15"></td><td class="s8">1. RS will not be created when user cancel the request upon creation<br>2. Changes do not reflect when user modify draft RS and clicks cancel</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1854861251R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s8">PRS-011-029</td><td class="s8">RS CREATION</td><td class="s19">Critcal</td><td class="s8">Validate that requestor can still create RS even when 1 of RS approver in Project has been deleted</td><td class="s8"></td><td class="s20">1. Login as admin<br>2. Click Manage &gt; Project <br>3. Delete 1 approver from ALABANG HEIGHTS or any project<br>4. Create and Submit RS using ALABANG HEIGHTS as Charge to (Client) and Project<br>5. Validate admin/requestor will be able to create RS successfully<br>6. Validate that the latest approvers appears on submitted RS</td><td class="s8"></td><td class="s8">Admin/requestor will be able to create RS successfully with the latest project approvers</td><td class="s15"></td><td class="s21"></td><td class="s10">Passed</td><td class="s12">Not Started</td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr></tbody></table></div>