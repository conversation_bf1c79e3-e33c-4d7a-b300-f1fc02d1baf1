'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { cn } from '@/lib/utils';

type NavItem = {
  title: string;
  href: string;
  children?: NavItem[];
};

const navItems: NavItem[] = [
  {
    title: 'Overview',
    href: '/docs/overview',
  },
  {
    title: 'Architecture',
    href: '/docs/architecture',
    children: [
      { title: 'System Architecture', href: '/docs/architecture/system' },
      { title: 'Backend Architecture', href: '/docs/architecture/backend' },
      { title: 'Frontend Architecture', href: '/docs/architecture/frontend' },
      { title: 'Database Design', href: '/docs/architecture/database' },
    ],
  },
  {
    title: 'Business Flows',
    href: '/docs/business-flows',
    children: [
      { title: 'Requisition Flow', href: '/docs/business-flows/requisition' },
      { title: 'Approval Flow', href: '/docs/business-flows/approval' },
      { title: 'Canvassing Flow', href: '/docs/business-flows/canvassing' },
      { title: 'Purchase Order Flow', href: '/docs/business-flows/purchase-order' },
      { title: 'Delivery Flow', href: '/docs/business-flows/delivery' },
      { title: 'Payment Flow', href: '/docs/business-flows/payment' },
    ],
  },
  {
    title: 'API Documentation',
    href: '/docs/api',
    children: [
      { title: 'Authentication', href: '/docs/api/authentication' },
      { title: 'Users', href: '/docs/api/users' },
      { title: 'Requisitions', href: '/docs/api/requisitions' },
      { title: 'Approvals', href: '/docs/api/approvals' },
      { title: 'Suppliers', href: '/docs/api/suppliers' },
      { title: 'Purchase Orders', href: '/docs/api/purchase-orders' },
      { title: 'Delivery Receipts', href: '/docs/api/delivery-receipts' },
      { title: 'Payments', href: '/docs/api/payments' },
    ],
  },
  {
    title: 'Frontend Guide',
    href: '/docs/frontend',
    children: [
      { title: 'Architecture', href: '/docs/frontend/architecture' },
      { title: 'State Management', href: '/docs/frontend/state-management' },
      { title: 'Components', href: '/docs/frontend/components' },
      { title: 'Routing', href: '/docs/frontend/routing' },
      { title: 'API Integration', href: '/docs/frontend/api-integration' },
      { title: 'Forms', href: '/docs/frontend/forms' },
      { title: 'Security', href: '/docs/frontend/security' },
    ],
  },
  {
    title: 'Backend Guide',
    href: '/docs/backend',
    children: [
      { title: 'Architecture', href: '/docs/backend/architecture' },
      { title: 'API Design', href: '/docs/backend/api-design' },
      { title: 'Data Modeling', href: '/docs/backend/data-modeling' },
      { title: 'Authentication', href: '/docs/backend/authentication' },
      { title: 'Authorization', href: '/docs/backend/authorization' },
      { title: 'Error Handling', href: '/docs/backend/error-handling' },
      { title: 'Validation', href: '/docs/backend/validation' },
      { title: 'Testing', href: '/docs/backend/testing' },
    ],
  },
  {
    title: 'Security Guide',
    href: '/docs/security',
    children: [
      { title: 'Authentication', href: '/docs/security/authentication' },
      { title: 'Authorization', href: '/docs/security/authorization' },
      { title: 'Data Protection', href: '/docs/security/data-protection' },
      { title: 'Input Validation', href: '/docs/security/input-validation' },
      { title: 'CSRF Protection', href: '/docs/security/csrf' },
      { title: 'XSS Prevention', href: '/docs/security/xss' },
    ],
  },
  {
    title: 'Development Guide',
    href: '/docs/development',
    children: [
      { title: 'Setup', href: '/docs/development/setup' },
      { title: 'Workflow', href: '/docs/development/workflow' },
      { title: 'Coding Standards', href: '/docs/development/coding-standards' },
      { title: 'Testing', href: '/docs/development/testing' },
      { title: 'Debugging', href: '/docs/development/debugging' },
    ],
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const [expanded, setExpanded] = useState<string[]>([]);

  const toggleExpand = (href: string) => {
    setExpanded((prev) =>
      prev.includes(href)
        ? prev.filter((item) => item !== href)
        : [...prev, href]
    );
  };

  const isActive = (href: string) => pathname === href;
  const isExpanded = (href: string) => expanded.includes(href);
  const hasActiveChild = (item: NavItem) =>
    item.children?.some((child) => isActive(child.href));

  return (
    <aside className="w-full md:w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
      <div className="p-6">
        <Link href="/" className="flex items-center space-x-2">
          <span className="text-xl font-bold text-primary-700">PRS Docs</span>
        </Link>
      </div>
      <nav className="px-4 pb-6">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.href}>
              {item.children ? (
                <div>
                  <button
                    onClick={() => toggleExpand(item.href)}
                    className={cn(
                      'flex items-center justify-between w-full px-4 py-2 text-left rounded-md',
                      (isActive(item.href) || hasActiveChild(item)) && 'text-primary-700 font-medium',
                      'hover:bg-gray-100'
                    )}
                  >
                    <span>{item.title}</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={cn(
                        'h-4 w-4 transition-transform',
                        isExpanded(item.href) && 'transform rotate-90'
                      )}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  {isExpanded(item.href) && (
                    <ul className="mt-1 ml-4 space-y-1">
                      {item.children.map((child) => (
                        <li key={child.href}>
                          <Link
                            href={child.href}
                            className={cn(
                              'block px-4 py-2 rounded-md',
                              isActive(child.href)
                                ? 'bg-primary-50 text-primary-700 font-medium'
                                : 'hover:bg-gray-100'
                            )}
                          >
                            {child.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={cn(
                    'block px-4 py-2 rounded-md',
                    isActive(item.href)
                      ? 'bg-primary-50 text-primary-700 font-medium'
                      : 'hover:bg-gray-100'
                  )}
                >
                  {item.title}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
