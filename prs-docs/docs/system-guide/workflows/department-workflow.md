# Department Management Workflow

This document outlines the complete workflow for managing departments in the PRS system, including synchronization from external sources and approver assignment for requisition workflows.

## Workflow Diagram

```mermaid
flowchart TD
    Start([Department Management]) --> A[Department Landing Page]
    
    A --> B[View Departments]
    A --> C{User Role}
    
    C -->|IT Admin/Purchasing Admin| D[Sync Departments]
    C -->|IT Admin/Purchasing Admin| E[Manage Approvers]
    
    D --> F[Pull from Master File]
    F --> G[Update Department List]
    
    B --> H[View Department Details]
    H --> I{Department Type}
    
    I -->|Regular Department| J[Regular Department View]
    I -->|Association Department| K[Association Department View]
    
    J --> L[View Regular Approvers]
    K --> M[View Association Approvers]
    
    E --> N{Approver Management}
    N -->|Regular Department| O[Manage Regular Approvers]
    N -->|Association Department| P[Manage Association Approvers]
    
    O --> Q[Manage Approval Levels]
    Q --> R[Assign Level Approvers]
    Q --> S[Assign Optional Approvers]
    
    R --> T[Add/Edit/Delete Level]
    S --> U[Add/Edit Optional]
    
    P --> V[Assign Area Staff Approvers]
    V --> W[Level 1: Four or more Area Heads]
    
    T --> X[Save Approver Changes]
```

## Entity Types

### Regular Departments
- **Source**: Synced from Cityland's Department Master File
- **Management**: Read-only department info, full approver management
- **Approver Structure**: Multiple levels + optional approvers

### Association Departments
- **Source**: System-defined for association workflows
- **Management**: Specialized approver management
- **Approver Structure**: Level 1 with four or more Area Head approvers

## User Roles and Permissions

| Role | View Departments | Sync Departments | Assign Approvers | Update Approvers | View Approvers |
|------|------------------|------------------|------------------|------------------|----------------|
| Root User | ✓ | ✗ | ✗ | ✗ | ✗ |
| IT Admin | ✓ | ✓ | ✓ | ✓ | ✓ |
| Purchasing Admin | ✓ | ✓ | ✓ | ✓ | ✓ |
| All Other Users | ✗ | ✗ | ✗ | ✗ | ✗ |

## Detailed Workflow Steps

### 1. Department Landing Page Access

**Actor**: Root User, IT Admin, Purchasing Admin

**Actions**:

- Access Department menu from navigation
- View list of all departments

**Features**:

- Search functionality by department name
- Sortable columns (Department Name, Department Code, Supervisor)
- Pagination (10 rows per page)
- Default sorting: Alphabetical by Department Name (A-Z)


### 2. Department Data Synchronization

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**: 

- Cityland Legacy System integration setup

**Actions**:

- Click "Sync Department List" button
- System pulls data from Cityland's Department Master File
- Updates existing departments or adds new ones

**Synchronized Data**:

- Department Name
- Department Code

**Business Rules**:

- Adds departments not yet in the system
- Updates existing department details from master file
- Updates filter and form options across the system
- Display last sync date and time
- Button is disabled during sync process
- Returns to page 1 after synchronization
- Synced data is read-only (except approver assignments)

### 3. View Department Details

#### 3.1 Regular Department View

**Actor**: Root User, IT Admin, Purchasing Admin

**Actions**:

- Click on department name or view button
- Display full page view (not modal)

**Information Displayed**:

- Department Name (non-editable)
- Department Code (non-editable)
- Approvers Section (accordion-style view)
- Edit button (for approver management)

**Approvers Section**:

- Collapsible accordion showing approval levels
- Displays approver names for each level
- Shows note if no approvers assigned
- Separate section for optional approvers

#### 3.2 Association Department View

**Actor**: IT Admin, Purchasing Admin

**Actions**:

- Click "Association Department" option
- Display specialized view for association workflows

**Information Displayed**:

- Department Details section
- Requests Section with approvers view
- Edit button for approver management

### 4. Regular Department Approver Management

#### 4.1 Assign Regular Approvers

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**:

- Department exists in system
- User has approver management permissions

**Actions**:

- Access via Edit button in department view
- Manage multiple approval levels
- Add approvers to each level

**Level Management**:

- Click "Add Level" to create new approval level
- New levels always added as the last level
- Each level can have multiple approvers
- Levels can be deleted if empty

**Add Approver Process**:

1. Click "Add Approver" button for specific level
2. Select from modal with filtered user list
3. Available user types: All except Root User
4. Confirm selection to add to approver list

**Business Rules**:

- Same user cannot be approver on different levels within same workflow
- All levels must have at least one approver
- Empty levels can be deleted before submission
- Changes are not saved until confirmation

#### 4.2 Assign Optional Approvers

**Actor**: IT Admin, Purchasing Admin

**Actions**:

- Manage optional approvers (single level only)
- Add/edit optional approver in separate section

**Optional Approver Rules**:

- Only one level available for optional approvers
- Same user type restrictions as regular approvers
- Cannot be same user as regular approvers
- Optional - not required for workflow completion

#### 4.3 Update Regular Approvers

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**:

- Approvers already assigned to department
- User has update permissions

**Actions**:

- Click Edit icon for specific level
- Select new approver from filtered list
- Confirm changes

**Update Process**:

- Edit existing approver assignments
- Add new levels if needed
- Remove approvers or entire levels
- Maintain workflow integrity

### 5. Association Department Approver Management

#### 5.1 Assign Association Approvers

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**:

- Association department access
- User has approver management permissions

**Actions**:

- Access specialized association approver management
- Assign four or more Area Head approvers for Level 1

**Level 1 Requirements**:

- Must have four different Area Head approvers
- Only Area Staff user type allowed
- Each approver must be unique
- All four positions must be filled

**Assignment Process**:

1. Click "Add Approver" for each of four positions
2. Select from Area Staff user list
3. Confirm each selection
4. Ensure all four positions filled before saving

#### 5.2 Update Association Approvers

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**:

- Association approvers already assigned
- User has update permissions

**Actions**:

- Click Edit icon for specific approver position
- Select replacement from Area Staff list
- Maintain requirement of four unique approvers

**Business Rules**:

- Must maintain exactly four approvers
- All approvers must be different Area Staff users
- Changes require confirmation before saving

### 6. Save and Validation

**Actor**: IT Admin, Purchasing Admin

**Actions**:

- Review all approver assignments
- Submit changes via Save button
- Handle validation errors

**Save Process**:

1. Click Save button after making changes
2. System validation checks:
   - All levels have assigned approvers
   - No duplicate approvers across levels
   - Association departments have four unique approvers
3. Display confirmation modal
4. Final save or cancel option

**Validation Rules**:

- Error message: "Approver is Required" for empty levels
- Cannot save with empty approval levels
- Cannot have same approver on multiple levels
- Association departments must have exactly four Area Staff approvers

**Success Actions**:

- Display success toast message
- Automatically close modal after 5 seconds
- Return to department view with updated approvers

## Business Rules Summary

### Department Synchronization Rules

- Departments synced from external master file
- Only department name and code are synchronized
- Synced data is read-only except for approver assignments
- Sync updates existing or adds new departments

### Approver Assignment Rules

- Regular departments: Multiple levels + optional approvers
- Association departments: Exactly four Area Staff approvers
- No duplicate approvers across levels within same workflow
- All levels must have at least one approver before saving
- Empty levels can be deleted during editing

### User Type Restrictions

- Regular approvers: All except Root User
- Association approvers Level 1: Area Staff only
- Each approver must be unique within the workflow

### Validation Requirements

- "Approver is Required" error for empty assignments
- Confirmation modals for all save/cancel operations
- Success messaging with auto-close functionality

## Example Scenarios

### Scenario 1: Sync Department Data

1. IT Admin accesses Department menu
2. Clicks "Sync Department List" button
3. System connects to master file and updates departments
4. Sync completion message displays with timestamp
5. Updated department list refreshes to page 1
6. Button re-enabled after sync completion

### Scenario 2: Setup Multi-Level Approvers

1. IT Admin views regular department details
2. Clicks Edit button for approver management
3. Clicks "Add Level" to create Level 1
4. Adds Supervisor and Department Head to Level 1
5. Clicks "Add Level" to create Level 2
6. Adds Division Head to Level 2
7. Adds Assistant Manager as optional approver
8. Clicks Save and confirms changes
9. Success toast displays and returns to department view

### Scenario 3: Configure Association Department

1. IT Admin accesses Association Department
2. Clicks Edit button for approver management
3. Adds four different Area Staff users to Level 1
4. Attempts to save with only three approvers
5. System shows validation error
6. Adds fourth Area Staff approver
7. Successfully saves configuration
8. Returns to association department view

### Scenario 4: Update Existing Approvers

1. Purchasing Admin views department with existing approvers
2. Clicks Edit button to modify assignments
3. Clicks Edit icon for Level 2 approver
4. Selects different Division Head from list
5. Attempts to select same user as Level 1 approver
6. System prevents duplicate selection
7. Selects different valid approver
8. Saves changes with confirmation
9. Updated approvers display in accordion view

### Scenario 5: Handle Validation Errors

1. IT Admin edits department approvers
2. Creates new level but doesn't assign approver
3. Attempts to save configuration
4. System displays "Approver is Required" error
5. Either assigns approver to level or deletes empty level
6. Successfully saves valid configuration
7. Returns to department view with success message

## Data Flow Integration

### Incoming Data

- Department Master File → Department records (name, code)
- User Management System → Approver selection lists
- Organization hierarchy → User type classifications

### Outgoing Data

- Department data → Requisition workflow assignment
- Approver configurations → Requisition approval routing
- Department filters → Various system forms and reports

## Common Issues and Solutions

### Issue 1: Sync Button Not Responding

**Cause**: Network connectivity or master file access issues

**Solution**:

- Check network connection
- Verify master file system availability
- Wait for current sync to complete if in progress
- Contact system administrator if issue persists

### Issue 2: Cannot Save Approver Configuration

**Cause**: Validation errors or missing approvers

**Solution**:

- Review error messages for specific issues
- Ensure all levels have assigned approvers
- Check for duplicate approvers across levels
- For associations, verify exactly four Area Staff approvers

### Issue 3: Approver Not Available in Selection List

**Cause**: User doesn't have required user type or already assigned

**Solution**:

- Verify user has correct user type (Supervisor, Assistant Manager, etc.)
- Check if user is already assigned to different level
- Confirm user account is active in User Management
- Contact IT Admin to update user permissions if needed

### Issue 4: Cannot Delete Approval Level

**Cause**: Level has assigned approvers or validation rules

**Solution**:

- Remove all approvers from level before deletion
- Ensure at least one level remains in configuration
- Cannot delete if it would violate minimum level requirements
- Use Edit function to reassign approvers instead

### Issue 5: Association Department Configuration Error

**Cause**: Incorrect number of approvers or wrong user types

**Solution**:

- Ensure exactly four Area Staff approvers assigned
- Verify all four approvers are different users
- Check each approver has Area Staff user type
- Remove and re-add approvers if necessary