<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Purchase Request System</title>
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <style>
      @page {
        size: letter;
        margin: 0.5in;
      }

      body {
        font-family: 'Inter', sans-serif;
        font-size: 12px;
        line-height: 1.2;
        color: #000;
        margin: 0;
        padding: 0;
        width: 8.5in;
        height: 11in;
        box-sizing: border-box;
      }

      /* Support for Inter variable font */
      @supports (font-variation-settings: normal) {
        body {
          font-family: 'Inter var', sans-serif;
        }
      }

      .wrapper {
        max-width: 7.5in;
        margin: 0 auto;
        padding: 0;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 15px;
        padding-top: 15px;
      }

      .logo-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .logo {
        font-size: 20px;
        line-height: 1;
      }

      .title {
        font-weight: bold;
        font-size: 12px;
        color: #800000;
      }

      .page-info {
        text-align: right;
        font-size: 12px;
      }

      .project-company-header {
        font-weight: bold;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        width: 100%;
        padding-bottom: 10px;
        border-bottom: 1px solid #000;
      }

      .pr-number {
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: left;
        /*gap: 10px;*/
      }

      .pr-label {
        font-size: 14px;
        font-weight: bold;
      }

      .pr-value {
        text-decoration: underline;
        font-weight: bold;
      }

      .date-section {
        display: flex;
        align-items: center;
      }

      .date-label {
        color: #323232;
        font-size: 7px;
        margin-left: 30px;
      }

      .date-value {
        font-size: 8px;
        font-weight: bold;
      }

      .note-section {
        font-size: 8px;
        margin-top: 10px;
        text-align: right;
        width: 100%;
        margin-bottom: 25px;
      }

      .amount-words {
        font-style: italic;
        font-weight: bold;
      }

      .note-section input {
        border: none;
        border-bottom: 1px solid #000;
        width: 30%;
        font-size: 8px;
        margin-left: 5px;
        font-style: italic;
      }

      .section {
        margin: 10px 0;
      }

      .section-title {
        font-weight: bold;
        margin-bottom: 5px;
        font-size: 12px;
      }

      .section-description {
        margin-bottom: 5px;
        font-size: 9px;
      }

      .details-grid-outer {
        border: 1px solid #ccc;
        border-radius: 10px;
        padding: 10px;
        padding-bottom: 0px;
      }

      .signing-details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 10px;
        padding: 10px;
        font-size: 10px;
      }

      .signing-details-label {
        color: #323232;
        font-weight: normal;
        font-size: 8px;
      }

      .signing-details-label input {
        margin-top: 20px;
        border-bottom: solid 1px;
        width: 150px;
      }

      .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 10px;
        padding: 5px;
        font-size: 10px;
      }

      .details-row {
        display: flex;
        flex-direction: column;
      }

      .details-label {
        color: #323232;
        font-weight: normal;
        font-size: 8px;
        /*margin-bottom: 4px;*/
      }

      .details-label input {
        margin-top: 20px;
        border-bottom: solid 1px;
        width: 150px;
      }

      .checks-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;
        width: 100%;
        padding: 0 0 10px;
      }

      .check-item {
        display: flex;
        align-items: center;
        white-space: nowrap;
      }

      .check-item label {
        font-size: 8px;
        margin-right: 3px;
      }

      /* Style for the checkboxes */
      .check-item input[type='checkbox'] {
        width: 10px;
        height: 10px;
        margin-right: 3px;
      }

      .check-textfield {
        border: none;
        border-bottom: 1px solid #000;
        font-size: 7px;
        padding: 0;
        margin: 0;
      }

      input,
      select {
        border: none;
        background: transparent;
        width: 100%;
        font-family: inherit;
        font-size: inherit;
        outline: none;
      }

      .items-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
      }

      .items-title {
        font-weight: bold;
      }

      .table-container {
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #ccc;
        margin-top: 5px;
        margin-bottom: 15px;
        padding: 1px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        border: none;
      }

      th,
      td {
        border: 1px solid #ccc;
        padding: 4px;
        text-align: left;
        font-size: 10px;
      }

      /* Remove border from outer edges of table cells */
      tr:first-child th {
        border-top: none;
      }

      tr:last-child td {
        border-bottom: none;
      }

      th:first-child,
      td:first-child {
        border-left: none;
      }

      th:last-child,
      td:last-child {
        border-right: none;
      }

      th,
      td {
        padding: 4px;
        text-align: left;
        font-size: 10px;
      }

      th {
        color: #4f575e;
        vertical-align: top;
        text-align: left;
        font-weight: bold;
        font-size: 8px;
      }

      td {
        height: 30px;
      }

      .base-col {
        width: 60px;
      }

      .num-col {
        width: 20px;
      }

      .pricing-col {
        width: 60px;
      }

      .pricing-col input {
        width: 90%;
        margin-right: 5px;
      }

      .item-name-cell {
        vertical-align: center;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
        padding: 4px;
      }

      .item-name-input {
        color: #4f575e;
        width: 100%;
        outline: none;
        border: none;
        background: transparent;
        font-family: inherit;
        font-size: inherit;
        text-decoration: underline;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
        text-align: left;
        resize: none;
        overflow: hidden;
      }

      .text-left {
        text-align: left;
      }

      .text-right {
        text-align: right;
      }

      .footer {
        margin-top: 20px;
        text-align: right;
        font-size: 10px;
        color: #666;
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="header">
        <div class="logo-title">
          <div class="title">Non-RS Payment Request</div>
        </div>
        <div class="page-info">
          Page
          <input
            type="text"
            name="currentPage"
            style="width: 20px; text-align: center"
          />
          of
          <input
            type="text"
            name="totalPage"
            style="width: 20px; text-align: center"
          />
        </div>
      </div>

      <div>
        <div>
          <input class="project-company-header" name="project-company" />
        </div>
      </div>

      <div class="pr-number">
        <div class="pr-label">
          P.R. #
          <input
            class="pr-value"
            type="text"
            name="rsNumber"
            style="width: 200px; font-weight: bold"
          />
        </div>

        <div class="date-label">
          Date Prepared:
          <input
            class="date-value"
            type="text"
            name="datePrepared"
            style="width: 60px; font-weight: bold"
          />
        </div>
        <div class="date-label">
          Date Needed:
          <input
            class="date-value"
            type="text"
            name="dateNeeded"
            style="width: 60px; font-weight: bold"
          />
        </div>
        <div class="date-label">
          Time Needed:
          <input
            class="date-value"
            type="text"
            name="timeNeeded"
            style="width: 60px; font-weight: bold"
          />
        </div>
      </div>

      <div class="items-header">
        <div class="items-title">Items</div>
        <div>
          <input
            type="text"
            name="firstItemCount"
            style="width: 20px; text-align: center"
          />
          -
          <input
            type="text"
            name="lastItemCount"
            style="width: 20px; text-align: center"
          />
          of
          <input
            type="text"
            name="total"
            style="width: 30px; text-align: center"
          />
        </div>
      </div>

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>Item Name</th>
              <th>Qty</th>
              <th>Unit</th>
              <th>Unit Price</th>
              <th>Discount</th>
              <th>Total Price</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="num-col"><input type="text" name="itemNum1" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName1" />
              </td>
              <td class="base-col"><input type="text" name="qty1" /></td>
              <td class="base-col"><input type="text" name="unit1" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice1" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount1" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price1" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum2" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName2" />
              </td>
              <td class="base-col"><input type="text" name="qty2" /></td>
              <td class="base-col"><input type="text" name="unit2" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice2" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount2" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price2" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum3" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName3" />
              </td>
              <td class="base-col"><input type="text" name="qty3" /></td>
              <td class="base-col"><input type="text" name="unit3" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice3" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount3" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price3" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum4" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName4" />
              </td>
              <td class="base-col"><input type="text" name="qty4" /></td>
              <td class="base-col"><input type="text" name="unit4" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice4" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount4" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price4" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum5" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName5" />
              </td>
              <td class="base-col"><input type="text" name="qty5" /></td>
              <td class="base-col"><input type="text" name="unit5" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice5" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount5" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price5" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum6" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName6" />
              </td>
              <td class="base-col"><input type="text" name="qty6" /></td>
              <td class="base-col"><input type="text" name="unit6" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice6" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount6" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price6" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum7" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName7" />
              </td>
              <td class="base-col"><input type="text" name="qty7" /></td>
              <td class="base-col"><input type="text" name="unit7" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice7" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount7" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price7" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum8" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName8" />
              </td>
              <td class="base-col"><input type="text" name="qty8" /></td>
              <td class="base-col"><input type="text" name="unit8" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice8" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount8" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price8" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum9" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName9" />
              </td>
              <td class="base-col"><input type="text" name="qty9" /></td>
              <td class="base-col"><input type="text" name="unit9" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice9" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount9" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price9" class="text-right" />
              </td>
            </tr>

            <tr>
              <td class="num-col"><input type="text" name="itemNum10" /></td>
              <td class="item-name-cell">
                <input class="item-name-input" name="itemName10" />
              </td>
              <td class="base-col"><input type="text" name="qty10" /></td>
              <td class="base-col"><input type="text" name="unit10" /></td>
              <td class="pricing-col">
                <input type="text" name="unitPrice10" class="text-right" />
              </td>
              <td class="pricing-col">
                <input type="text" name="discount10" class="text-left" />
              </td>
              <td class="pricing-col">
                <input type="text" name="price10" class="text-right" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="note-section">
        Total Amount in words: <input name="totalAmount" />
      </div>

      <div class="section">
        <div class="details-grid-outer">
          <div class="section-description">
            Checks pay to "Cash" uncrossed must be approved by Pres. / EVP SVP.
            Please issue checks.
          </div>
          <div class="checks-container">
            <div class="check-item">
              <input type="checkbox" />
              <label for="cash">Pay To Cash</label>
              <input type="text" class="check-textfield" />
            </div>

            <div class="check-item">
              <input type="checkbox" />
              <label for="encashment">For Encashment</label>
              <input type="text" class="check-textfield" />
            </div>

            <div class="check-item">
              <input type="checkbox" />
              <label for="uncrossed">Uncrossed Check</label>
              <input type="text" class="check-textfield" />
            </div>

            <div class="check-item">
              <input type="checkbox" />
              <label for="managers">Manager's Check</label>
              <input type="text" class="check-textfield" />
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="details-grid-outer">
          <div class="section-description">Supporting Documents:</div>
          <div class="checks-container">
            <div class="check-item">
              <input type="checkbox" />
              <label for="cash">Attached</label>
              <input type="text" />
            </div>

            <div class="check-item">
              <input type="checkbox" />
              <label for="encashment">To Follow</label>
              <input type="text" />
            </div>

            <div class="check-item">
              <input type="checkbox" />
              <label for="uncrossed">None Available</label>
              <input type="text" />
            </div>

            <div class="check-item">
              <input type="checkbox" />
              <label for="managers">Orig. RS/OS/CS</label>
              <input type="text" />
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="details-grid-outer">
          <div class="checks-container">
            <div class="signing-details-label">
              <label for="requested-by">Requested By: </label>
              <input type="text" />
            </div>

            <div class="signing-details-label">
              <label for="endorsed-by">Endorsed By:</label>
              <input type="text" />
            </div>

            <div class="signing-details-label">
              <label for="approved-by">Approved By:</label>
              <input type="text" />
            </div>

            <div class="signing-details-label">
              <label for="countersigned-by">Countersigned By: </label>
              <input type="text" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
