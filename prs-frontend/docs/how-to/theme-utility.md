# Theme Utility Guide

This guide explains how to use the theme utility for standardizing color usage across the PRS Frontend application.

## Overview

The theme utility provides a standardized way to manage colors and styles in the application. It includes:

- Color constants for primary, secondary, success, error, and neutral colors
- Utility functions for getting Tailwind CSS classes for colors
- A consistent approach to theme management

## Color Constants

The theme utility defines several color palettes:

```javascript
// Primary color palette - maroon/burgundy tones
export const PRIMARY_COLORS = {
  DEFAULT: '#754445', // Main primary color
  LIGHT: '#8B4F4F',   // Lighter variant for hover states
  DARK: '#614A4C',    // Darker variant for backgrounds
  DARKER: '#795C5F',  // Darker variant for headers
  LIGHTEST: '#f3e5e5', // Very light variant for backgrounds
};

// Secondary color palette - blue tones
export const SECONDARY_COLORS = {
  DEFAULT: '#445475', // Main secondary color
  LIGHT: '#4F5F8B',   // Lighter variant for hover states
  DARK: '#394463',    // Darker variant
};

// Success color palette - green tones
export const SUCCESS_COLORS = {
  DEFAULT: '#306F4E', // Main success color
  LIGHT: '#f3fff8',   // Light background for success states
};

// Error/danger color palette - red tones
export const ERROR_COLORS = {
  DEFAULT: '#EB5757', // Main error color
  LIGHT: '#F58A8A',   // Lighter variant for hover states
  LIGHTEST: '#FFF7F7', // Very light background for error states
};

// Neutral color palette - grays
export const NEUTRAL_COLORS = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY_50: '#F9FAFB',
  GRAY_100: '#F3F4F6',
  // ... other gray shades
};
```

## Utility Functions

The theme utility provides several functions for getting Tailwind CSS classes:

### `getColorClass`

Returns the appropriate Tailwind class for a background color:

```javascript
getColorClass('primary'); // 'bg-[#754445] text-white'
getColorClass('primary', 'light'); // 'bg-[#8B4F4F] text-white'
getColorClass('error'); // 'bg-[#EB5757] text-white'
getColorClass('success'); // 'bg-[#306F4E] text-white'
```

### `getTextColorClass`

Returns the appropriate Tailwind class for text color:

```javascript
getTextColorClass('primary'); // 'text-[#754445]'
getTextColorClass('error'); // 'text-[#EB5757]'
getTextColorClass('gray', '500'); // 'text-gray-500'
```

### `getBorderColorClass`

Returns the appropriate Tailwind class for border color:

```javascript
getBorderColorClass('primary'); // 'border-[#754445]'
getBorderColorClass('error'); // 'border-[#EB5757]'
getBorderColorClass('gray', '200'); // 'border-gray-200'
```

## Basic Usage

### Importing the Theme Utility

```javascript
import {
  PRIMARY_COLORS,
  SECONDARY_COLORS,
  SUCCESS_COLORS,
  ERROR_COLORS,
  getColorClass,
  getTextColorClass,
  getBorderColorClass
} from '@utils/theme';
```

### Using Color Constants

You can use the color constants directly for inline styles or dynamic values:

```javascript
// Inline styles
const styles = {
  backgroundColor: PRIMARY_COLORS.DEFAULT,
  color: 'white',
};

// Dynamic values
const getBackgroundColor = (isActive) =>
  isActive ? PRIMARY_COLORS.DEFAULT : NEUTRAL_COLORS.GRAY_100;
```

### Using Utility Functions with Tailwind

```javascript
import { cn } from '@utils/cn';
import { getColorClass, getTextColorClass } from '@utils/theme';

// Combine with other Tailwind classes
const buttonClass = cn(
  getColorClass('primary'),
  'rounded-lg p-4 shadow-md'
);

// Use different variants
const alertClass = cn(
  getColorClass('error', 'lightest'),
  getTextColorClass('error'),
  'rounded-md p-2 border'
);
```

## Advanced Usage

### With Class Variance Authority (cva)

When using cva for component variants, you can use template literals with color constants:

```javascript
import { cva } from 'class-variance-authority';
import { PRIMARY_COLORS, SECONDARY_COLORS, ERROR_COLORS } from '@utils/theme';

const buttonVariants = cva(
  'base-button-styles',
  {
    variants: {
      variant: {
        primary: `bg-[${PRIMARY_COLORS.DEFAULT}] text-white`,
        secondary: `bg-[${SECONDARY_COLORS.DEFAULT}] text-white`,
        danger: `bg-[${ERROR_COLORS.DEFAULT}] text-white`,
        outline: `text-[${PRIMARY_COLORS.DEFAULT}] border border-[${PRIMARY_COLORS.DEFAULT}]`,
      },
      hover: {
        primary: `hover:bg-[${PRIMARY_COLORS.LIGHT}]`,
        secondary: `hover:bg-[${SECONDARY_COLORS.LIGHT}]`,
        danger: `hover:bg-[${ERROR_COLORS.LIGHT}]`,
      }
    },
    defaultVariants: {
      variant: 'primary',
      hover: 'primary',
    }
  }
);
```

### Creating Custom Theme Mixins

You can create custom mixins for common style combinations:

```javascript
import { cn } from '@utils/cn';
import { getColorClass, getBorderColorClass, getTextColorClass } from '@utils/theme';

// Create a custom card style mixin
export const createCardStyle = (colorType = 'primary', variant = 'DEFAULT') => {
  return cn(
    'rounded-lg shadow-md p-4',
    getColorClass(colorType, variant),
    getBorderColorClass(colorType)
  );
};

// Create a custom button style mixin
export const createButtonStyle = (colorType = 'primary', variant = 'DEFAULT') => {
  return cn(
    'rounded-md px-4 py-2 font-medium',
    getColorClass(colorType, variant)
  );
};

// Usage
const cardClass = createCardStyle('secondary');
const buttonClass = createButtonStyle('error');
```

## Best Practices

1. **Use Color Constants**: Always use the color constants instead of hardcoded hex values.

2. **Prefer Utility Functions**: Use the utility functions (`getColorClass`, etc.) when working with Tailwind classes.

3. **Consistent Naming**: When adding new colors or variants, follow the existing naming convention.

4. **Document Custom Colors**: If you need to add custom colors, document them in the theme utility file.

5. **Component Refactoring**: When refactoring components, update them to use the theme utility.

## Example: Refactoring a Component

### Before

```javascript
const Alert = ({ children, type = 'info' }) => {
  let className = 'p-4 rounded-md';

  if (type === 'error') {
    className += ' bg-[#FFF7F7] text-[#EB5757] border border-[#EB5757]';
  } else if (type === 'success') {
    className += ' bg-[#f3fff8] text-[#306F4E] border border-[#306F4E]';
  } else if (type === 'warning') {
    className += ' bg-[#FFFBEB] text-[#D97706] border border-[#D97706]';
  } else {
    className += ' bg-[#EFF6FF] text-[#3B82F6] border border-[#3B82F6]';
  }

  return <div className={className}>{children}</div>;
};
```

### After

```javascript
import { cn } from '@utils/cn';
import {
  getColorClass,
  getTextColorClass,
  getBorderColorClass,
  ERROR_COLORS,
  SUCCESS_COLORS
} from '@utils/theme';

const Alert = ({ children, type = 'info' }) => {
  const getAlertClasses = (alertType) => {
    switch (alertType) {
      case 'error':
        return cn(
          `bg-[${ERROR_COLORS.LIGHTEST}]`,
          getTextColorClass('error'),
          getBorderColorClass('error')
        );
      case 'success':
        return cn(
          `bg-[${SUCCESS_COLORS.LIGHT}]`,
          getTextColorClass('success'),
          getBorderColorClass('success')
        );
      case 'warning':
        return 'bg-[#FFFBEB] text-[#D97706] border-[#D97706]'; // Not yet in theme
      default:
        return 'bg-[#EFF6FF] text-[#3B82F6] border-[#3B82F6]'; // Not yet in theme
    }
  };

  return (
    <div className={cn('p-4 rounded-md', getAlertClasses(type))}>
      {children}
    </div>
  );
};
```

## Conclusion

The theme utility provides a standardized way to manage colors and styles in the application. By using the color constants and utility functions, you can ensure consistency across the application and make it easier to update the theme in the future.
