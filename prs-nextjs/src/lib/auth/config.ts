import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "@/lib/db";
import { verifyPassword } from "@/lib/auth/password";
import { verifyOTP, generateOTPSecret } from "@/lib/auth/otp";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
    maxAge: 60 * 60, // 1 hour, matching the original project's access token expiry
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
        otp: { label: "OTP Code", type: "text" },
        otpSecret: { label: "OTP Secret", type: "text" },
        newPassword: { label: "New Password", type: "password" },
        refreshToken: { label: "Refresh Token", type: "text" },
        action: { label: "Action", type: "text" }
      },
      async authorize(credentials) {
        // Handle different authentication flows based on the action
        const action = credentials?.action;

        // Handle refresh token flow
        if (action === "refreshToken" && credentials?.refreshToken) {
          try {
            // In a real implementation, we would verify the refresh token
            // For now, we'll simulate a successful token refresh
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 60 * 60; // 1 hour

            return {
              id: "refreshed-user-id",
              accessToken: `new-access-token-${Date.now()}`,
              refreshToken: `new-refresh-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          } catch (error) {
            console.error("Refresh token error:", error);
            return null;
          }
        }

        // Handle forgot password flow
        if (action === "forgotPassword" && credentials?.username) {
          try {
            // In a real implementation, we would send a password reset email
            // For now, we'll simulate a successful request
            return {
              id: "forgot-password-user-id",
              message: "Password reset request sent successfully"
            };
          } catch (error) {
            console.error("Forgot password error:", error);
            return null;
          }
        }

        // For all other flows, we need username and password
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          // Verify credentials against database
          const user = await prisma.user.findUnique({
            where: { username: credentials.username },
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              },
              department: true
            }
          });

          if (!user || user.status !== 'ACTIVE') {
            throw new Error("Invalid username or password");
          }

          // Verify password
          const isValidPassword = await verifyPassword(
            credentials.password,
            user.password
          );

          if (!isValidPassword) {
            throw new Error("Invalid username or password");
          }

          // Handle update temporary password flow
          if (action === "updatePassword" && credentials.newPassword) {
            if (!user.isPasswordTemporary) {
              throw new Error("Temporary password has been already updated");
            }

            // Check if new password is the same as the old password
            const isSamePassword = await verifyPassword(
              credentials.newPassword,
              user.password
            );

            if (isSamePassword) {
              throw new Error("New password cannot be the same as the old password");
            }

            // In a real implementation, we would update the password in the database
            // For now, we'll simulate a successful password update
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 60 * 60; // 1 hour

            return {
              id: user.id.toString(),
              accessToken: `access-token-${Date.now()}`,
              refreshToken: `refresh-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          }

          // Handle OTP setup flow
          if (action === "setupOTP" && credentials.otp && credentials.otpSecret) {
            if (user.otpSecret) {
              throw new Error("User OTP already setup");
            }

            // Verify the OTP code
            try {
              const isValidOTP = verifyOTP(
                credentials.otp,
                credentials.otpSecret
              );

              if (!isValidOTP) {
                throw new Error("Invalid OTP provided");
              }
            } catch (error) {
              throw new Error("Invalid OTP provided");
            }

            // In a real implementation, we would save the OTP secret in the database
            // For now, we'll simulate a successful OTP setup
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 60 * 60; // 1 hour

            return {
              id: user.id.toString(),
              accessToken: `access-token-${Date.now()}`,
              refreshToken: `refresh-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          }

          // Handle OTP verification flow
          if (action === "verifyOTP" && credentials.otp) {
            if (!user.otpSecret) {
              throw new Error("User MFA is not yet setup");
            }

            // Verify the OTP code
            const isValidOTP = verifyOTP(
              credentials.otp,
              user.otpSecret
            );

            if (!isValidOTP) {
              throw new Error("Invalid OTP provided");
            }

            // Generate tokens for successful authentication
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 60 * 60; // 1 hour

            return {
              id: user.id.toString(),
              name: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.username,
              email: user.email,
              username: user.username,
              role: user.role.name,
              roleId: user.roleId,
              departmentId: user.departmentId,
              accessToken: `access-token-${Date.now()}`,
              refreshToken: `refresh-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          }

          // Regular login flow
          // Check if user has temporary password
          if (user.isPasswordTemporary) {
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 5 * 60; // 5 minutes

            return {
              id: user.id.toString(),
              requireUpdatePassword: true,
              accessToken: `temp-pass-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          }

          // Check if user needs to set up OTP
          if (!user.otpSecret) {
            const otpSecret = generateOTPSecret();
            const otpAuthUrl = `otpauth://totp/PRS:${user.username}?secret=${otpSecret}&issuer=PRS&algorithm=SHA1&digits=6&period=30`;

            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 10 * 60; // 10 minutes

            return {
              id: user.id.toString(),
              requireOTPSetup: true,
              otpSecret,
              otpAuthUrl,
              accessToken: `otp-setup-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          }

          // Check if OTP verification is required
          if (user.otpSecret && !credentials.otp) {
            const currentTime = Math.floor(Date.now() / 1000);
            const expiresAt = currentTime + 5 * 60; // 5 minutes

            return {
              id: user.id.toString(),
              requireOTPVerification: true,
              accessToken: `otp-verify-token-${Date.now()}`,
              expiredAt: expiresAt
            };
          }

          // Verify OTP if provided
          if (user.otpSecret && credentials.otp) {
            const isValidOTP = verifyOTP(
              credentials.otp,
              user.otpSecret
            );

            if (!isValidOTP) {
              throw new Error("Invalid OTP provided");
            }
          }

          // Format permissions similar to the original project
          const permissions = user.role.permissions.map(rp => ({
            module: rp.permission.name.split(':')[1],
            action: rp.permission.name.split(':')[0]
          }));

          // Generate tokens for successful authentication
          const currentTime = Math.floor(Date.now() / 1000);
          const expiresAt = currentTime + 60 * 60; // 1 hour

          // Return full user data for successful login
          return {
            id: user.id.toString(),
            name: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.username,
            email: user.email,
            username: user.username,
            role: user.role.name,
            roleId: user.roleId,
            departmentId: user.departmentId,
            permissions,
            accessToken: `access-token-${Date.now()}`,
            refreshToken: `refresh-token-${Date.now()}`,
            expiredAt: expiresAt
          };
        } catch (error) {
          console.error("Authentication error:", error);
          throw new Error(error instanceof Error ? error.message : "Authentication failed");
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        // Store all user data in the token
        token.id = user.id;

        // Cast user to any to access custom properties
        const customUser = user as any;

        if (customUser.username) token.username = customUser.username;
        if (customUser.role) token.role = customUser.role;
        if (customUser.roleId) token.roleId = customUser.roleId;
        if (customUser.departmentId) token.departmentId = customUser.departmentId;
        if (customUser.permissions) token.permissions = customUser.permissions;
        if (customUser.accessToken) token.accessToken = customUser.accessToken;
        if (customUser.refreshToken) token.refreshToken = customUser.refreshToken;
        if (customUser.expiredAt) token.expiredAt = customUser.expiredAt;

        // Store special auth flow flags
        if (customUser.requireUpdatePassword) token.requireUpdatePassword = customUser.requireUpdatePassword;
        if (customUser.requireOTPSetup) token.requireOTPSetup = customUser.requireOTPSetup;
        if (customUser.requireOTPVerification) token.requireOTPVerification = customUser.requireOTPVerification;
        if (customUser.otpSecret) token.otpSecret = customUser.otpSecret;
        if (customUser.otpAuthUrl) token.otpAuthUrl = customUser.otpAuthUrl;
      }

      // Check token expiration and refresh if needed
      const expiredAt = token.expiredAt as number;
      if (expiredAt && Date.now() / 1000 > expiredAt) {
        // Token expired, should refresh
        // In a real implementation, we would call the refresh token API here
        // For now, we'll just extend the expiration
        token.expiredAt = Math.floor(Date.now() / 1000) + 60 * 60;
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        // Extend the session.user type
        const user = session.user as any;

        // Pass all token data to the session
        user.id = token.id;
        user.username = token.username;
        user.role = token.role;
        user.roleId = token.roleId;
        user.departmentId = token.departmentId;
        user.permissions = token.permissions;

        // Add auth-related properties to session
        (session as any).accessToken = token.accessToken;
        (session as any).refreshToken = token.refreshToken;
        (session as any).expiredAt = token.expiredAt;

        // Add special auth flow flags
        (session as any).requireUpdatePassword = token.requireUpdatePassword;
        (session as any).requireOTPSetup = token.requireOTPSetup;
        (session as any).requireOTPVerification = token.requireOTPVerification;
        (session as any).otpSecret = token.otpSecret;
        (session as any).otpAuthUrl = token.otpAuthUrl;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle redirects with auth flow parameters
      return url.startsWith(baseUrl) ? url : baseUrl;
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  }
};
