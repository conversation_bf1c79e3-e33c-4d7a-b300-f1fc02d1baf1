<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1001471672C0" style="width:103px;" class="column-headers-background">A</th><th id="1001471672C1" style="width:98px;" class="column-headers-background">B</th><th id="1001471672C2" style="width:72px;" class="column-headers-background">C</th><th id="1001471672C3" style="width:252px;" class="column-headers-background">D</th><th id="1001471672C4" style="width:233px;" class="column-headers-background">E</th><th id="1001471672C5" style="width:330px;" class="column-headers-background">F</th><th id="1001471672C6" style="width:90px;" class="column-headers-background">G</th><th id="1001471672C7" style="width:339px;" class="column-headers-background">H</th><th id="1001471672C8" style="width:100px;" class="column-headers-background">I</th><th id="1001471672C9" style="width:88px;" class="column-headers-background">J</th><th id="1001471672C10" style="width:120px;" class="column-headers-background">K</th><th id="1001471672C11" style="width:120px;" class="column-headers-background">L</th><th id="1001471672C12" style="width:120px;" class="column-headers-background">M</th><th id="1001471672C13" style="width:120px;" class="column-headers-background">N</th><th id="1001471672C14" style="width:78px;" class="column-headers-background">O</th><th id="1001471672C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1001471672R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s3">STG</td><td class="s4">Status<br>(E2E_Run1)</td><td class="s4">Actual Results<br>(E2E_Run1)</td><td class="s4">Status<br>(E2E_Run2)</td><td class="s4">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1001471672R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5" colspan="16"></td></tr><tr style="height: 211px"><th id="1001471672R2" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">3</div></th><td class="s6">PRS-018-001</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Table of History</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3.Create and Submit an RS with OFM Type and atleast 1 item added</td><td class="s6">1. Click Items<br>2. Click OFM Items<br>3. Click an Item Code</td><td class="s7"></td><td class="s6">1. Able to view the table of history of an OFM Item<br>2. Able to see the following columns:<br>        i. RS Number<br>        ii. Date Requested<br>        iii. Quantity Requested<br>        iv. Price<br>        v. Date Delivered<br>        vi. Quantity Delivered<br>3. Able to see the List of RS Numbers that used the item</td><td class="s8" rowspan="7"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1jjY3FNBosJYUeJjQfreH6ypgv1f9_pFfXgGan7sXYg8/edit?gid=*********#gid=*********">https://docs.google.com/spreadsheets/d/1jjY3FNBosJYUeJjQfreH6ypgv1f9_pFfXgGan7sXYg8/edit?gid=*********#gid=*********</a></td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 211px"><th id="1001471672R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s6">PRS-018-002</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Search in History</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3.Create and Submit an RS with OFM Type and atleast 1 item added</td><td class="s6">1. Click Items<br>2. Click OFM Items<br>3. Click an Item Code<br>4. Type on search bar<br>5. Click Clear</td><td class="s7"></td><td class="s6">1. Able to Click Search bar<br>2. Able to Input in search bar<br>3. Able to search even with keywords<br>4. Able to Clear in input of search bar</td><td class="s13">Failed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-777">https://youtrack.stratpoint.com/issue/CITYLANDPRS-777</a></td></tr><tr style="height: 19px"><th id="1001471672R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s6">PRS-018-003</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Sorting of Table in History</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3.Create and Submit an RS with OFM Type and atleast 1 item added</td><td class="s6">1. Click Items<br>2. Click OFM Items<br>3. Click an Item Code<br>4. Click on one of the following headers to sort:<br>        i. RS Number<br>        ii. Date Requested<br>        iii. Quantity Requested<br>        iv. Price<br>        v. Date Delivered<br>        vi. Quantity Delivered</td><td class="s7"></td><td class="s6">1. Able to view List sorted default by most recent Date Requested<br>2. Able to sort by clicking on the following headers:<br>        i. RS Number<br>        ii. Date Requested<br>        iii. Quantity Requested<br>        iv. Price<br>        v. Date Delivered<br>        vi. Quantity Delivered<br>3. Able to sort the following with the following:<br>     a. RS Number - 0-9, 9-0<br>     b. Date Requested - Oldest Date-Latest Date, Latest Date-Oldest Date<br>     c. Quantity Requested - 0-9, 9-0<br>     d. Price  - 0-9, 9-0<br>     e. Date Delivered - Oldest Date-Latest Date, Latest Date-Oldest Date<br>     f. Quantity Delivered  - 0-9, 9-0</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1001471672R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s6">PRS-018-004</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Maximum Display of items in a Page</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3.Create and Submit an RS with OFM Type and atleast 1 item added</td><td class="s6">1. Click Items<br>2. Click OFM Items<br>3. Click an Item Code<br></td><td class="s7"></td><td class="s6">1. Able to view maximum of 10 rows</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1001471672R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s6">PRS-018-005</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Display for Columns: Price, Date Delivered and Quantity Delivered if no data</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3.Create and Submit an RS with OFM Type and atleast 1 item added<br>4. A Requistion Slip with no price?, no Date Delivered?, No Quantity Delivered? for item</td><td class="s6">1. Click Items<br>2. Click OFM Items<br>3. Click an Item Code<br></td><td class="s7"></td><td class="s6">1. &quot;---&quot; is displayed if the following columns if data is none:<br>     a. Price<br>     b. Date Delivered<br>     c. Quantity Delivered</td><td class="s14">Blocked</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s6">By Bug 777</td></tr><tr style="height: 19px"><th id="1001471672R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s6">PRS-018-006</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Displayed Data in History</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3. Create and Submit an RS with OFM Type and atleast 1 item added</td><td class="s6">Check #3 from pre-req<br><br>1. Get the RS Number and item description added on Requsition Slip then Click Items<br>2. Click OFM Items<br>3. Search the item description copied &gt; Click the Item Code<br>4. Validate that the RS Number is present from History<br>5. Validate date requested match from dashboard vs OFM History for that particular RS Number<br>8. Open the RS number<br>9. Compare RS items table vs the OFM History for that particular RS</td><td class="s7"></td><td class="s6">1. RS Number is match<br>2. Date Requested is match<br>3. Quantity Requested is match<br>4. Price is match<br>5. Date Delivered is match<br>6. Quantity Delivered is match</td><td class="s13">Failed</td><td class="s15">Failed</td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-783">https://youtrack.stratpoint.com/issue/CITYLANDPRS-783</a></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-783">https://youtrack.stratpoint.com/issue/CITYLANDPRS-783<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-787</a></td></tr><tr style="height: 19px"><th id="1001471672R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s6">PRS-018-007</td><td class="s6">OFM Items History</td><td class="s6"></td><td class="s6">Verify Pagination is Working</td><td class="s6">1. Logged in as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>2. An OFM Item List has used the Item<br>3.Create and Submit an RS with OFM Type and atleast 1 item added</td><td class="s6">1. Click Items<br>2. Click OFM Items<br>3. Click an Item Code<br>4a. Click on the page numbers<br>or<br>4b. Click on the arrows in the pagination</td><td class="s7"></td><td class="s6">1. Able to switch page in the history according to the page number clicked<br>2. Able to move page by clicking on the arrow</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s6"></td><td class="s6"></td></tr></tbody></table></div>