Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,STATUS,Remarks,Defects
Invoice Entry Points and Invoice Creation,,,,,,,,,,,
1304-001,Invoice Entry Points and Invoice Creation,High,"Verify option for ""Received Invoice"" is displayed in Select Action Modal","1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Ref number with status of ""Delivered"" to open RS
2. Click ""Select Actions"" button
3. Check options displayed",,"3. Should displayed a ""Receive Invoice"" Option",Aira_Results,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1475,
1304-002,Invoice Entry Points and Invoice Creation,High,"Verify option for ""Received Invoice"" is not displayed in Select Action Modal for Non requester and other Purchasing staff","1. Logged in as:
-Non-Requester
-Non-Assigned Purchasing Staff
-Other User types
2. A Delivery Report has been created and Submitted","1. Click Ref number with status of ""Delivered"" to open RS
2. Click ""Select Actions"" button
3. Check options displayed",,"3. Should not displayed a ""Receive Invoice"" Option",,Passed,,
1304-003,Invoice Entry Points and Invoice Creation,High,"Verify ""Floating banner"" is displayed upon submit a Delivery Report","1. Logged in as:
-Requester
2. A Delivery Report has been created ","1. Create Delivery Report
2. Populate all required fields
3. Click Submit Delivery Report",,"3. A Floating Banner ""Receive Invoice"" should be displayed",,Passed,,
1304-004,Invoice Entry Points and Invoice Creation,High,"Verify ""Floating banner"" is not displayed when viewing DR of Non-requester and non-assiged purchasing staff","1. Logged in as:
-Non-Requester
-Non-Assigned Purchasing Staff
-Other User types
2. A Delivery Report has been created and Submitted","1.  Click Ref number with status of ""Delivered"" to open RS
2. Click Related Documents tab 
3. Click Deliveries tab
4. Click DR number
5. Check Floating banner if displayed",,"5. A Floating Banner for  ""Receive Invoice"" should not be displayed",,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1475,
1304-005,Invoice Entry Points and Invoice Creation,Minor,"Verify ""Floating banner"" should be sticky ","1. Logged in as:
-Requester
2. A Delivery Report has been created and Submitted",1. Check Floating banner when scroll and zoom in/out the page,,"1. ""Receive Invoice""   Floating Banner should be sticky and scrollable",,Passed,,
1304-006,Invoice Entry Points and Invoice Creation,Minor,"Verify Redirection when click ""Receive Invoice"" option in Select Actions modal","1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. On Select Actions Modal, click ""Receive Invoice"" Option",,"1. Should navigated to ""Invoice Reports"" Create Form",,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1475,
1304-007,Invoice Entry Points and Invoice Creation,Minor,"Verify Redirection when click ""Receive Invoice"" Floating banner","1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. On Delivery Report Page, click ""Receive Invoice""  floating banner",,"1. Should navigated to ""Invoice Reports"" Create Form",,Passed,,
1304-008,Invoice Entry Points and Invoice Creation,Critical,Verify Invoice Report Form in Select Action modal,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click ""Receive Invoice"" in Select Actions modal",,"1. Should display a Form with the following Sections
    a. Invoice Details
        i. Purchase Order Number
        ii. Supplier Invoice No
        iii. Supplier Invoice Issued Date
        iv. Supplier Invoice Amount
        v. Attachments
        vi. Notes
    b. Delivery Reports Table",,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1475,
1304-009,Invoice Entry Points and Invoice Creation,Critical,Verify Invoice Report Form in Floating banner,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click ""Receive Invoice"" in Floating banner in DR",,"1. Should display a Form with the following Sections
    a. Invoice Details
        i. Purchase Order Number
        ii. Supplier Invoice No
        iii. Supplier Invoice Issued Date
        iv. Supplier Invoice Amount
        v. Attachments
        vi. Notes
    b. Delivery Reports Table",,Passed,,
1304-010,Invoice Entry Points and Invoice Creation,High,Verify Purchase Order field values displayed,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click ""Receive Invoice"" in Floating banner in DR or Select Actions modal
2. Click Purchase Order field",,2. Should displayed a List of Purchase Order Numbers that was linked to a created Delivery Report and Field should required,,Passed,,
1304-011,Invoice Entry Points and Invoice Creation,Critical,Verify behavior when  Purchase Order Number is selected in Purchase Order field,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click ""Receive Invoice"" in Floating banner in DR or Select Actions modal
2. Select Purchase Order number in Purchase Order Field",,"
2. Section for the Delivery Reports Related to the Purchase Order should be displayed
                a) User may be able to select one or more Delivery Reports
                b) Once the Puchase Order Number is changed, should reset the selected Delivery Reports and Items Table
            ",Verna_Sprint1_Test Result,Not Started,"1. Cannot verify the ff due to 1 PO = 1 DR
a) User may be able to select one or more Delivery Reports",
1304-012,Invoice Entry Points and Invoice Creation,Critical,Verify Selected Delivery Reports will be displayed under the Selected Delivery Reports Section,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click ""Receive Invoice"" in Floating banner in DR or Select Actions modal
2. Select Purchase Order number in Purchase Order Field
3. Click ""Add DR"" button
4. Tick DR numbers
5. Click ""Add"" button in the modal",,"3. Should display a ""Select Delivery Report"" Modal
4. Selected DR number should display on right side of the modal
5. Selected Delivery Reports should be displayed under the Selected Delivery Reports Section",,Passed,,
1304-013,Invoice Entry Points and Invoice Creation,High,Verify Delivery Report Number is Removed through Selected Deliver Receipts Section,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Remove DR number in  Selected Deliver Report Section,,1. DR number should be unchecked to the Delivery Reports No Section,,Passed,,
1304-014,Invoice Entry Points and Invoice Creation,High,Verify Delivery Report Number is uncheckd through Delivery Reports No Section,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Uncheck DR Number through Delivery Report No. Section,,   1. DR number should be remove to the Selected Deliver Receipts Section,,Passed,,
1304-015,Invoice Entry Points and Invoice Creation,High,Verify Supplier Invoice No field accepts Alphanumeric and Special Characters ,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate Alphanumeric and Special Characters  in Supplier Invoice No field,,1. Should accepts alphanumeric and Special Characters when submit Invoice,,Passed,Can't accept Alphanumeric and Special Characters.,CITYLANDPRS-1525
1304-016,Invoice Entry Points and Invoice Creation,Minor,Verify Supplier Invoice No field accepts Emoji Characters,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate Emoji Characters  in Supplier Invoice No field,,"1. Should not accepts emoji Characters when submit Invoice, and an error message is dispayed",,Passed,,
1304-017,Invoice Entry Points and Invoice Creation,Minor,Verify Supplier Invoice No field when populate more than 100 characters,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate more than 100 Characters  in Supplier Invoice No field,,"1. Should only accept Maximum of 100 Characters
",,Passed,,
1304-018,Invoice Entry Points and Invoice Creation,High,Verify blank Supplier Invoice No field when create invoice,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Do not Populate Supplier Invoice No. field
2. Click Submit Invoice",,"2. Unable to submit Invoice due to Supplier Invoice No. field
is required",,Passed,,
1304-019,Invoice Entry Points and Invoice Creation,Minor,Verify Supplier Invoice Issued Date field,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Click Supplier Invoice Issued Date field,,"1. Should display a Date Picker that will allow the User to select the Current Date and 
Previous Date",,Passed,,
1304-020,Invoice Entry Points and Invoice Creation,High,Verify blank DR Issued Date field when create invoice,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Do not Populate DR Issued date field
2. Click Submit Invoice",,"2. Unable to submit Invoice due to DR Issued date field
is required",,Passed,,
1304-021,Invoice Entry Points and Invoice Creation,High,Verify Supplier Invoice Amount field,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate numbers that has 2 decimal places in Supplier Invoice Amount Field,,"1. Supplier Invoice Amount should be able to entered numbers that has 2 decimal place and have a Placeholder of Peso Sign
",,Passed,,
1304-022,Invoice Entry Points and Invoice Creation,Minor,"Verify Supplier Invoice Amount field when entered Alphanumeric, Special Chracters, and emoji","1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Populate Alphanumeric, Special Chracters, and emoji in Supplier Invoice Amount Field",,1. Should display an error message and only numbers should be accepted,,Passed,,
1304-023,Invoice Entry Points and Invoice Creation,Minor,Verify Supplier Invoice Amount field when entered more than 10 Characters,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate more than 10 characters in Supplier Invoice Amount Field,,"1. Should only accept Maximum of 10 Characters
  ",,Passed,,
1304-024,Invoice Entry Points and Invoice Creation,High,Verify blank Supplier Invoice Amount field when create invoice,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Do not Populate Supplier Invoice Amount field
2. Click Submit Invoice",,"2. Unable to submit Invoice due to Supplier Invoice Amount field
is required",,Passed,,
1304-025,Invoice Entry Points and Invoice Creation,High,Verify Attachment when upload correct file format,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Attachment
2. Upload a files with  PNG, JPG, JPEG, PDF, Excel, and CSV file types",,"2. File should upload successfully
  ",,Passed,,
1304-026,Invoice Entry Points and Invoice Creation,High,Verify Attachment when upload less than or equal to 25mb file Size,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Attachment
2. Upload a file with less than or equal to 25mb file size",,"2. File should upload successfully
  ",,Passed,,
1304-027,Invoice Entry Points and Invoice Creation,Minor,Verify Attachment when upload different file format,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Attachment
2. Upload a files with GIF, PPT or Docs file types",,2. Should displayed an error in uploading a file,,Passed,,
1304-028,Invoice Entry Points and Invoice Creation,Minor,Verify Attachment when upload greater than 25mb file Size,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Attachment
2. Upload a file with greater than 25mb file size",,2. Should displayed an error in uploading a file,,Passed,,
1304-029,Invoice Entry Points and Invoice Creation,High,Verify Attachment is required in Invoice Creation,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Attachment
2. Do not upload any attachment
3. Click Submit Invoice",,3. Unable to submit Invoice due to attachment is required,,Passed,,CITYLANDPRS-1526
1304-030,Invoice Entry Points and Invoice Creation,High,Verify if Notes accept Alphanumeric and Special Characters,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate Alphanumeric and Special Characters in Notes,,1. Should be able to accept Alphanumeric and Special Characters,,Passed,,
1304-031,Invoice Entry Points and Invoice Creation,Minor,Verify if Notes accept Emoji characters,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Populate Emoji Characters in Notes,,"1. Should not accept Emoji characters
",,Passed,,
1304-032,Invoice Entry Points and Invoice Creation,Minor,Verify if Notes accept Maximum of 100 Characters,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Populate 100 characters in Notes
2. Populate more than 100 characters in Notes",, Should only accept less than or  equal to 100 characters,,Passed,,
1304-033,Invoice Entry Points and Invoice Creation,Minor,Verify Notes is Optional when create Invoice,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Do not populate Notes
2. Click Submit Invoice",,2. Should be able to create/submit invoice aue to notes is only optional,,Passed,,
1304-034,Invoice Entry Points and Invoice Creation,High,Verify Delivery Reports Table View,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Select Purchase Order No. in Purchase Order field,,1. Should only have a Content if the Purchase Order Number and the Delivery Report/s is/are selected,,Passed,,
1304-035,Invoice Entry Points and Invoice Creation,Minor,Verify Columns in Delivery Reports table,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Check columns in DR table,,"1.  Should have the following Columns
          i. Delivery Report No.
          ii. Date Delivered
          iii. Status
          vi. Actions - Remove
              i) Allow to remove the Row of Item in the Table",,Passed,,
1304-036,Invoice Entry Points and Invoice Creation,Minor,Verify Default sorting of Delivery Reports table,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Check default sorting of tables,,1. Should have a default sorting by Latest Delivered Date,,Not Started,1 PO = 1 DR,
1304-037,Invoice Entry Points and Invoice Creation,High,Verify Search of DR number in Delivery Reports table,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Populate DR number keyword  in search field
2. Click search button
3. Click ""Clear"" button
4. Populate exact DR number  in search field
5. Click search button",,"2. Should be able to display all related DR numbers
3. Should clear the search field and reset the table filters
5. Should be able to display the result of exact DR number",Aira_Results,Passed,,
1304-038,Invoice Entry Points and Invoice Creation,Minor,Verify Search when no data is available,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Populate DR number that are not existing on the table  in search field
2. Click search button
3. Click ""Clear"" button
4. Populate Date Delivered or Status in search field
5. Click search button",,"2. Should display no data
3. Should clear the search field and reset the table filters
5. Should display no data",,Passed,,
1304-039,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Delivery Report No.,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Sort of Delivery Report No 
2. Click Sort for 2nd time
3. Click Sort for 3rd time",,"1. Should sorted Ascending -  0-9, A-Z
2. Should sorted Descending - 9-0, Z-A
3. Back to default",,Not Started,,
1304-040,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Date delivered,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Sort of Date Delivered
2. Click Sort for 2nd time
3. Click Sort for 3rd time",,"1. Should sorted Ascending -Oldest - Latest
2. Should sorted Descending - Latest - Oldest
3. Back to default",,Not Started,,
1304-041,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Status,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click Sort of Status
2. Click Sort for 2nd time
3. Click Sort for 3rd time",,"1. Should sorted Ascending -  A-Z
2. Should sorted Descending - Z-A
3. Back to default",,Not Started,,
1304-042,Invoice Entry Points and Invoice Creation,Minor,Verify Rows displayed on Delivery Reports table,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Check count of rows displayed in Delivery Report Table,,1.  Should display 10 Rows of Data per Page,,Not Started,,
1304-043,Invoice Entry Points and Invoice Creation,Minor,Verify Paginations of Delivery Reports table,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click ""<"""">"" or next or previous page
2. Click page numbers (1,2,3,4..)",,"1. Should be able to display the next and previous page with correct total counts of entries
2. Should be able to display the selected page number with correct total counts of entries",,Not Started,,
1304-044,Invoice Entry Points and Invoice Creation,High,Verify Modal displayed when Delivery Number is clicked,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted",1. Click Delivery Number in Delivery Report table,,"1. Should display a Modal with the following:
    a. Should display the Delivery Report Number as the Modal Title
    b. Should have a Table with the following Columns
        ii. Item Name
        ii. Requested Qty
        iii. Delivered Qty
        iv. Returned Qty
        v. Unit
    c. Should have a Close Window Button to close the Modal and return to the Invoice Form
    d. Should display 10 Rows of Data per Page",,Passed,,
1304-045,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Item Name column in Delivery Number modal,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click DR number in Delivery Report # column 
2. Click Sort of Item Name
3. Click Sort for 2nd time
4. Click Sort for 3rd time",,"1. Should sorted Ascending -  0-9, A-Z
2. Should sorted Descending - 9-0, Z-A
3. Back to default",,Failed,,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1529
1304-046,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Requested Qty column in Delivery Number modal,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click DR number in Delivery Report # column 
2. Click Sort of Requested Qty
3. Click Sort for 2nd time
4. Click Sort for 3rd time",,"1. Should sorted Ascending -  0-9,
2. Should sorted Descending - 9-0
3. Back to default",,Failed,,
1304-047,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Delivered Qty column in Delivery Number modal,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click DR number in Delivery Report # column 
2. Click Sort of Delivered Qty
3. Click Sort for 2nd time
4. Click Sort for 3rd time",,"1. Should sorted Ascending -  0-9,
2. Should sorted Descending - 9-0
3. Back to default",,Failed,,
1304-048,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Returned Qty column in Delivery Number modal,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click DR number in Delivery Report # column 
2. Click Sort of Returned Qty
3. Click Sort for 2nd time
4. Click Sort for 3rd time",,"1. Should sorted Ascending -  0-9,
2. Should sorted Descending - 9-0
3. Back to default",,Out of Scope,"It's been clarified in Sprint REview Meeting that Receive Invoice only shows when the Delivery is fully delivered, so sorting Returned Qty in Delivery Report is not applicable.",
1304-049,Invoice Entry Points and Invoice Creation,Minor,Verify Sorting of Unit column in Delivery Number modal,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted","1. Click DR number in Delivery Report # column 
2. Click Sort of Unit
3. Click Sort for 2nd time
4. Click Sort for 3rd time",,"1. Should sorted Ascending -  A-Z
2. Should sorted Descending - Z-A
3. Back to default",,Failed,,
1304-050,Invoice Entry Points and Invoice Creation,Critical,Verify Creation of Invoice when click Save Draft button,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted
3. Invoice has been created","1. Click Save Draft button 
2. Click ""Cancel"" button on the modal
3. Click Save Draft button again
4. Click Save Draft or Continue on the modal",,"1. Should display a Confirmation Modal
2. Should close the modal to redirect back to Invoice Form and will not proceed with saving the Invoice to a Draft
3. Should display a Confirmation Modal
4. Should save the Invoice to a Draft with a Status of IR Draft
            i) A Temporary Invoice Number will be generated
            FORMAT: IR-TMP-[Company Code]+[AA-ZZ]+[8-digit incremental code]
            IR-TMP-12AA00000089",Verna_Sprint1_Test Result,Passed,,
1304-051,Invoice Entry Points and Invoice Creation,High,Verify Creation of Invoice when click Cancel button ,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted
3. Invoice has been created","1. Click Cancel button 
2. Click ""Cancel"" button on the modal
3. Click Cancel button again
4. Click Continue on the modal",,"1. Should display a Confirmation Modal
2. Should close the modal to redirect back to Invoice Form and will not proceed with cancelling the Invoice
3. Should display a Confirmation Modal
4. Should cancel the Invoice Creation",,Passed,,
1304-052,Invoice Entry Points and Invoice Creation,Critical,Verify Creation of Invoice when click Submit button,"1. Logged in as:
-Requester
-Assigned Purchasing Staff
2. A Delivery Report has been created and Submitted
3. Invoice has been created","1. Click Submit button 
2. Click ""Cancel"" button on the modal
3. Click Submit button again
4. Click Submit or Continue on the modal",,"1. Should display a Confirmation Modal
2. Should close the modal to redirect back to Invoice Form and will not proceed  with submission the Invoice
3. Should display a Confirmation Modal
4. Should submit the Invoice with a Status of Invoice Received
        i.  An Invoice Number will be generated
            FORMAT: IR-[Company Code]+[AA-ZZ]+[8-digit incremental code]
            IR-12AA00000089
",,Passed,,
[INVOICE] Viewing of an Invoice,,,,,,,,,,,
1305-001,[INVOICE] Viewing of an Invoice,Critical,Verify if user can access Invoice from Dashboard,1. An Invoice has been created,"1. Navigate to Dashboard
2. Click Invoice Number 
3. Validate that the invoice is accessible through dashboard",,Invoice is accessible from dashboard,Inaccesible through dashboard,Not Started,Not included in Sprint 1,
1305-002,[INVOICE] Viewing of an Invoice,Critical,Verify if user can access Invoice from Requisition Slip,1. An Invoice has been created,"1. Navigate to Requisition Slip
2. Open “Related Documents” section
3. Validate that the invoice is accessible through Related Documents in the Requisition Slip
4. Click Invoice tab > Click Invoice Number to View Invoice",,Invoice is accessible through Related Documents in the Requisition Slip,No invoice tab in related documents,Not Started,,
1305-003,[INVOICE] Viewing of an Invoice,High,Verify if user can click the Invoice Number to open,1. An Invoice has been created,1. Click on the Invoice Number from either access point,,Invoice page opens	,,Not Started,,
1305-004,[INVOICE] Viewing of an Invoice,High,Verify Open Editable Invoice - Draft status,1. An Invoice has been save as draft,"1. Open Invoice with status ""Draft""",,Editable Invoice Form is displayed	,,Not Started,,
1305-005,[INVOICE] Viewing of an Invoice,Minor,Verify Field Validations on Draft        ,1. An Invoice has been save as draft,1. Try submitting form with empty or invalid required fields,,Proper validation messages are shown,,Not Started,,
1305-006,[INVOICE] Viewing of an Invoice,High,Verify Open View-Only Invoice,"1. An Invoice has been created
2. Invoice is For IR Approval Status / submitted invoice","1. Open Invoice with status ""For IR Approval""",,Read-only Invoice view is displayed,,Not Started,,
1305-007,[INVOICE] Viewing of an Invoice,High,Verify Invoice Details section,1. An Invoice has been created,"1. Open any Invoice
2. Check presence of:
- PO Number
- Supplier Invoice No
- Issued Date
- Amount
- Attachments
- Notes",,All fields in Invoice Details are visible,,Not Started,,
1305-008,[INVOICE] Viewing of an Invoice,Minor,Verify Status section,1. An Invoice has been created,"1. Open an Invoice
2. Observe Status Section",,Status section is present and shows current status	,,Not Started,,
1305-009,[INVOICE] Viewing of an Invoice,High,Verify Assigned To section,1. An Invoice has been created,"1. Open an Invoice
2. Observe Assigned Status Section",,Assigned To section is visible,,Not Started,,
1305-010,[INVOICE] Viewing of an Invoice,High,Verify Items Table	,1. An Invoice has been created,"1. Open an  Invoice
2. Check table columns:
- DR No.
- Qty Ordered
- Qty Delivered
- Date Delivered",,All specified columns are visible in Items Table	,,Not Started,,
1305-011,[INVOICE] Viewing of an Invoice,High,Verify Search Funtionality is working in Items Table,1. An Invoice has been created,"1. Open an  Invoice
2. Type an Item
3. Validate search result",,The Items Table was filtered based on the search query,,Not Started,,
1305-012,[INVOICE] Viewing of an Invoice,High,Verify Sorting button is working in Items Table,1. An Invoice has been created,"1. Open an  Invoice
2. Click the sorting button (e.g. Delivery Report No., Item Name, Qty. Ordered, etc.,)
3. Observe sorting in Items Table",,"Items are sorted by ascending, descenting and by defult. ",,Not Started,,
1305-013,[INVOICE] Viewing of an Invoice,High,Verify if the items table creates another page when the content exceeds the limit,1. An Invoice has been created,"1. Open an  Invoice
2. Observe the items table exceeding the limit",,Another page/s is created and the items table continue on another page,,Not Started,,
1305-014,[INVOICE] Viewing of an Invoice,High,Verify correct value for Supplier Invoice,1. An Invoice has been created,"1. Open an  Invoice
2. Observe Value for Supplier Invoice",,Supplier Invoice should have accurate details,,Not Started,,
1305-015,[INVOICE] Viewing of an Invoice,High,Verify correct value of Amount in Invoice,1. An Invoice has been created,"1. Open an  Invoice
2. Observe Amount in Invoice",,Amount in Invoice should have accurate details,,Not Started,,
1305-016,[INVOICE] Viewing of an Invoice,High,Verify correct value of Issues Date,1. An Invoice has been created,"1. Open an  Invoice
2. Observe Issues Date",,Issues Date should have accurate details,,Not Started,,
1305-017,[INVOICE] Viewing of an Invoice,High,Verify correct value for Purchase Order No,1. An Invoice has been created,"1. Open an  Invoice
2. Observe Value for Purchase Order No.",,Purchase Order No. should have accurate details,,Not Started,,
1305-018,[INVOICE] Viewing of an Invoice,Minor,Verify 60-Character Limit for Notes,1. An Invoice has been created,"1. Try to enter 61-character.
2. Click Submit .",,System prevents entry exceeding 60 characters.,,Not Started,,
1305-019,[INVOICE] Viewing of an Invoice,Minor,"Verify notes are filtered correctly and display the correct user's name, date, and content",1. An Invoice has been created,"1. Open an Invoice
2. Verify all notes are listed initially without filters applied
3. Apply a filter to show only notes added by the Requester.",,"Each note should display the following:
a) Display the full name of the user who added the note
b) Show the correct date and time in the expected format (e.g., DD MM YYYY, HH:MM AM/PM)
c) Contain the accurate note content as entered by the user.",,Not Started,,
1305-020,[INVOICE] Viewing of an Invoice,Minor,Verify that entered Notes are displayed in the Check Notes section after Submit.,1. An Invoice has been created,"1. Verify the presence of the “New Attachment” badge.
2. Click on “Check Notes”.
3. Verify the badge is cleared.",,"1. “New Attachment” badge is displayed.
2. “New Attachment” badge is cleared when viewed.",,Not Started,,
1305-021,[INVOICE] Viewing of an Invoice,Minor,Verify that attachment is displayed in the Check Notes section after Submit.,1. An Invoice has been created,"1. Verify the presence of the “New Attachment” badge.
2. Click on “Check Attachment”.
3. Verify the badge is cleared.",,"1. “New Attachment” badge is displayed.
2. “New Attachment” badge is cleared when viewed.",,Not Started,,
[INVOICE] Invoice Status,,,,,,,,,,,
PRS-1385-001,Invoice Status,High,"Verify ""IR Draft"" status and styling",1. Invoice Status is in IR Draft,"
1. Navigate to an invoice with ""IR Draft"" status.
2. Observe status label, background color, and text color.
",,"2. Label displays IR Draft
Background Color: #5F636833
Text Color: #5F6368",Aira_Results,Passed,,
PRS-1385-002,Invoice Status,High,"Verify ""Invoice Received"" status and styling",1. Invoice Status is Invoice Received,"
1. Navigate to an invoice with ""Invoice Received"" status.
2. Observe status label, background color, and text color.
",,"2. Label displays Invoice Received
Background Color: #35C54933
Text Color: #35C549",,Passed,,
INVOICE - Adding of Invoice Tab in Related Documents,,,,,,,,,,,
PRS-001,Adding of Invoice Tab in Related Documents,Critical,Verify that the user can access RS Details via RS Number,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click any RS Number
2. Check redirection to the corresponding Requisition Slip Details Page",,2. Should click a Requisition Slip Number to display the Requisition Slip Details,,Not Started,,
PRS-002,Adding of Invoice Tab in Related Documents,Critical,Verify that the user can access the Related Documents tab,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click the ""Related Documents"" tab
2. Related Documents Tab is clickable and accessible",,2. Should click the Related Documents Tab,,Not Started,,
PRS-003,Adding of Invoice Tab in Related Documents,Medium,Verify that the Invoices Tab appears after Deliveries and before Payments,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Observe tab order in Related Documents
2. Validate if Invoices Tab is in between Deliveries Tab and Payments Tab",,2. Should add the Invoices Tab after Deliveries Tab and before the Payments Tab,,Not Started,,
PRS-004,Adding of Invoice Tab in Related Documents,High,Verify the presence of correct columns in Invoices Tab,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Open Invoices tab
2. Observe displayed columns",,"2. Should display the following Columns
    a. Invoice No - Document Number
    b. Supplier Invoice No - indicated in the created Invoice
    c. Supplier Invoice Issued Date - indicated in the created Invoice
    d. Supplier Invoice Amount - Total Amount of the Invoice
    e. Last Updated - Last Updated Date of the Invoice
    f. Status - Status of the Invoice",,Not Started,,
PRS-005,Adding of Invoice Tab in Related Documents,High,Verify Format of Last Updated Date Column,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Open Invoices tab
2. Observe format of Last Updated Column",,"2. Last Updated - Last Updated Date of the Invoice
        FORMAT: DD MMM YYYY
        23 Jul 2024",,Not Started,,
PRS-006,Adding of Invoice Tab in Related Documents,High,Verify that Latest Updated Date appears first in default sorting,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Locate Last Updated Date in the Table
2. Observe the table",,2. Should have a default sorting by the Latest Updated Date shown first,,Not Started,,
PRS-007,Adding of Invoice Tab in Related Documents,Medium,Verify that invoices are sorted by Latest Updated Date by default,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Open Invoices tab
2. Observe sorting order",,2. Should be sorted by Latest Updated Date by default,,Not Started,,
PRS-008,Adding of Invoice Tab in Related Documents,High,Verify sorting by Invoice No in ascending and descending order,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click on Invoice No column header's arrow buttons
2. Validate if table is sorted accordingly",,"2. Should be able to sort per Columns by
    a. Invoice No - 0-9, A-Z || 9-0, Z-A || Default",,Not Started,,
PRS-009,Adding of Invoice Tab in Related Documents,High,Verify sorting by Supplier Invoice No,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click on Supplier Invoice No column header's arrow buttons
2. Validate if table is sorted accordingly",,"2. Should be able to sort per Columns by
    b. Supplier Invoice No - 0-9, A-Z || 9-0, Z-A || Default",,Not Started,,
PRS-010,Adding of Invoice Tab in Related Documents,High,Verify sorting by Supplier Invoice Issued Date,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click on Supplier Invoice Issued Date column header's arrow buttons
2. Validate if table is sorted accordingly",,"2. Should be able to sort per Columns by
    c. Supplier Invoice Issued Date - Oldest Date-Latest Date || Latest Date-Oldest Date || Default",,Not Started,,
PRS-011,Adding of Invoice Tab in Related Documents,High,Verify sorting by Supplier Invoice Amount,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click on Supplier Invoice Amount column header's arrow buttons
2. Validate if table is sorted accordingly",,"2. Should be able to sort per Columns by
    d. Supplier Invoice Amount - 0-9, 9-0 || Default",,Not Started,,
PRS-012,Adding of Invoice Tab in Related Documents,High,Verify sorting by Last Updated Date,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click on Last Updated column header's arrow buttons
2. Validate if table is sorted accordingly",,"2. Should be able to sort per Columns by
    e. Last Updated - Oldest Date-Latest Date || Latest Date-Oldest Date || Default
",,Not Started,,
PRS-013,Adding of Invoice Tab in Related Documents,High,Verify sorting by Invoice Status,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click on Status column header's arrow buttons
2. Validate if table is sorted accordingly",,"2. Should be able to sort per Columns by
    f. Status - A-Z || Z-A || Default",,Not Started,,
PRS-014,Adding of Invoice Tab in Related Documents,High,Verify that Go back button is working,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click Go back Button
2. Validate if Go back button is working as intended",,2. Go back button is working as intended,,Not Started,,
PRS-015,Adding of Invoice Tab in Related Documents,High,Verify that Pagination buttons are working,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Click the Numbered Buttons and [Left & Right] Arrow Buttons to navigate to different pages of the table
2. Validate if the Pagination buttons are working as intended",,2. Pagination buttons are working as intended,,Not Started,,
PRS-016,Adding of Invoice Tab in Related Documents,Medium,Verify that table is responsive and adjusts with the number of invoices,"1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Observe the table
2. Check if the Table is responsive and adjust with the number of invoices in the table",,2. Table is responsive,,Not Started,,
PRS-017,Adding of Invoice Tab in Related Documents,Medium,"Verify that the pagination displays ""1 to X of X items"" properly below the table","1. Logged in as any user type except Root User 
2. At least one Invoice has been created or saved","1. Observe pagination section at bottom of table
2. Check if it displays ""1 to X of X items"" properly below the table",,"2. ""1 to X of X items"" is displayed properly below the table",,Not Started,,
[INVOICE] Adding of Invoice Tab in Request History ,,,,,,,,,,,
PRS-1501-001,Adding of Invoice Tab in Request History ,Critical,Verify Invoice Tab in Request History,"1. User must not be logged in as Root User 
2. At least one Invoice has been created or saved",1. Click History icon in Requistion Slip,,1. Should add the Invoices Tab after Deliveries Tab and before the Payments Tab,,Not Started,,
PRS-1501-002,Adding of Invoice Tab in Request History ,Critical,Verify Invoice Request History Columns,"1. User must not be logged in as Root User 
2. At least one Invoice has been created or saved","1. Click Invoices Tab
2. Validate Columns",,"2. Should display the following Columns
    a. Invoice No - Document Number
    b. Supplier Invoice No - indicated in the created Invoice
    c. Supplier Invoice Issued Date - indicated in the created Invoice
    d. Supplier Invoice Amount - Total Amount of the Invoice
    e. Last Updated - Last Updated Date of the Invoice
        FORMAT: DD MMM YYYY
        23 Jul 2024
    f. Status - Status of the Invoice",,Not Started,,
PRS-1501-003,Adding of Invoice Tab in Request History ,High,Verify Last Updated Column's Format,"1. User must not be logged in as Root User 
2. At least one Invoice has been created or saved","1. Click Invoices Tab
2. Validate Last Updated Date Format",,"2 Last Updated - Last Updated Date of the Invoice
        FORMAT: DD MMM YYYY
        23 Jul 2024",,Not Started,,
PRS-1501-004,Adding of Invoice Tab in Request History ,High,Verify Default Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Validate Default Sorting when Invoices Tab is clicked",,2. Should have a default sorting by the Latest Updated Date shown first,,Not Started,,
PRS-1501-005,Adding of Invoice Tab in Request History ,Minor,Verify Invoice No Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Click Sort button for the Invoice No column
3. Click Sort button for a 2nd time
4. Click Sort button for the last time",,"2. Should be sorted in ascending order from 0-9, A-Z
3. Should be sorted in descending order from 9-0, Z-A
4. Should go back to default sorting",,Not Started,,
PRS-1501-006,Adding of Invoice Tab in Request History ,Minor,Verify Supplier Invoice No Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Click Sort button for the Supplier Invoice No column
3. Click Sort button for a 2nd time
4. Click Sort button for the last time",,"2. Should be sorted in ascending order from 0-9, A-Z
3. Should be sorted in descending order from 9-0, Z-A
4. Should go back to default sorting",,Not Started,,
PRS-1501-007,Adding of Invoice Tab in Request History ,Minor,Verify Supplier Invoice Issued Date Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Click Sort button for the Supplier Invoice Issued Date column
3. Click Sort button for a 2nd time
4. Click Sort button for the last time",,"2. Should be sorted in ascending order from Oldest Date-Latest Date
3. Should be sorted in descending order from Latest Date-Oldest Date
4. Should go back to default sorting",,Not Started,,
PRS-1501-008,Adding of Invoice Tab in Request History ,Minor,Verify Supplier Invoice Amount Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Click Sort button for the Supplier Invoice Amount column
3. Click Sort button for a 2nd time
4. Click Sort button for the last time",,"2. Should be sorted in ascending order from 0-9
3. Should be sorted in descending order from 9-0
4. Should go back to default sorting",,Not Started,,
PRS-1501-009,Adding of Invoice Tab in Request History ,Minor,Verify Last Updated Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Click Sort button for the Last Updated column
3. Click Sort button for a 2nd time
4. Click Sort button for the last time",,"2. Should be sorted in ascending order from Oldest Date-Latest Date
3. Should be sorted in descending order from Latest Date-Oldest Date
4. Should go back to default sorting",,Not Started,,
PRS-1501-010,Adding of Invoice Tab in Request History ,Minor,Verify Status Sorting,"1. User must not be logged in as Root User 
2. At least two Invoices have been created or saved","1. Click Invoices Tab
2. Click Sort button for the Status column
3. Click Sort button for a 2nd time
4. Click Sort button for the last time",,"2. Should be sorted in ascending order from A-Z
3. Should be sorted in descending order from Z-A
4. Should go back to default sorting",,Not Started,,
PRS-1501-011,Adding of Invoice Tab in Request History ,High,Verify Activities Displayed Per Invoice,"1. User must not be logged in as Root User 
2. At least one Invoice has been created or saved","1. Click Invoices Tab
2. Observe the activities listed under the Invoice",,"2. Should display all of the Activities done per Invoice
    a. Should display as a New Row of Data per Activity",,Not Started,,
PRS-1501-012,Adding of Invoice Tab in Request History ,High,Verify Invoice No Redirection from Invoice Request History,"1. User must not be logged in as Root User 
2. At least one Invoice has been created or saved","1. Click Invoices Tab
2. Click Invoice No",,2. Should redirect user to respective Invoice page,,Not Started,,
PRS-1501-013,Adding of Invoice Tab in Request History ,Minor,Verify that Go back button is working,"1. User must not be logged in as Root User 
2. At least one Invoice has been created or saved","1. Click Invoices Tab
2. Click Go back Button",,2. Should redirect to RS page,,Not Started,,
PRS-1501-014,Adding of Invoice Tab in Request History ,High,Verify that Pagination buttons are working,"1. User must not be logged in as Root User 
2. At least eleven Invoices have been created or saved","1. Click Invoices Tab
2. Click ""<"""">"" or next or previous page
3. Click page numbers (1,2,3,4..)",,"2. Should go to the next or previous page
3. Should navigate to page clicked",,Not Started,,