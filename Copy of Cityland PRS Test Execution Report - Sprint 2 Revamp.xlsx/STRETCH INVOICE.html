<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#000000;font-family:docs-<PERSON>pins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1050536734C0" style="width:103px;" class="column-headers-background">A</th><th id="1050536734C1" style="width:259px;" class="column-headers-background">B</th><th id="1050536734C2" style="width:141px;" class="column-headers-background">C</th><th id="1050536734C3" style="width:252px;" class="column-headers-background">D</th><th id="1050536734C4" style="width:233px;" class="column-headers-background">E</th><th id="1050536734C5" style="width:406px;" class="column-headers-background">F</th><th id="1050536734C7" style="width:352px;" class="column-headers-background">H</th><th id="1050536734C14" style="width:245px;" class="column-headers-background">O</th><th id="1050536734C15" style="width:133px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1050536734R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1050536734R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="9">[INVOICE] Approval of an Invoice</td></tr><tr style="height: 19px"><th id="1050536734R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3">PRS-001</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify that the Invoice can be accessed in Dashboard </td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Navigate to Dashboard<br>2. Click Invoice Number <br>3. Validate that the invoice is accessible through dashboard</td><td class="s3">3. Invoice is accessible through Dashboard</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3">PRS-002</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify that the Invoice can be accessed in Related Documents in Requisition Slip</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Navigate to Dashboard<br>2. Click RS Number and navigate to Related Documents Tab <br>3. Validate that the invoice is accessible through Related Documents in Requisition Slip</td><td class="s3">3. Invoice is accessible through Related Documents in Requisition Slip</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s3">PRS-003</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Critical</td><td class="s6">Verify clicking Invoice Number redirects to Invoice</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Invoice Number<br>2, Validate redirection to Invoice </td><td class="s3">2. Redirects user to Invoice Form</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3">PRS-004</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Invoice Form displays all required sections and item columns</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Invoice Number<br>2, Validate display of Form with the following Sections<br>    a. Invoice Details<br>        i. Purchase Order Number<br>        ii. Supplier Invoice No<br>        iii. Supplier Invoice Issued Date<br>        iv. Supplier Invoice Amount<br>        v. Attachements<br>        vi. Notes<br>    b. Status Section<br>    c. Assigned to Section<br>    d. List of Approvers Section<br>    e. Items Table<br>        i. Should have the following Columns<br>           i) DR No.<br>           ii) Qty Ordered<br>           iii) Qty Delivered<br>           iv) Date Delivered</td><td class="s3">2 A Form with the following Sections is displayed<br>    a. Invoice Details<br>        i. Purchase Order Number<br>        ii. Supplier Invoice No<br>        iii. Supplier Invoice Issued Date<br>        iv. Supplier Invoice Amount<br>        v. Attachements<br>        vi. Notes<br>    b. Status Section<br>    c. Assigned to Section<br>    d. List of Approvers Section<br>    e. Items Table<br>        i. Should have the following Columns<br>           i) DR No.<br>           ii) Qty Ordered<br>           iii) Qty Delivered<br>           iv) Date Delivered</td><td class="s3"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1050536734R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s3">PRS-005</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify approver can add an Additional Approver through Approvers Section</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Add Button in Approvers Section<br>2. Validate display of Modal that will allow adding of an Approver<br>3. Validate display of Search User Field and if it is working<br>     i) Should display Users with a User Types of<br>               a) Supervisor<br>               b) Assistant Manager<br>               c) Department Head<br>               d) Division Head<br>               e) Area Staff/Department Secretary</td><td class="s3">2. The Modal is displayed<br>3. The Search User Field is displayed and is working<br>     i) Users with a User Types of<br>               a) Supervisor<br>               b) Assistant Manager<br>               c) Department Head<br>               d) Division Head<br>               e) Area Staff/Department Secretary</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s3">PRS-006</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Minor</td><td class="s6">Verify that Additional Approver is labeled with *</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Add Additional Approver<br>2. Validate that Additional Approver is displayed below the current Approver with a * as their Label<br></td><td class="s3">2. The Additional Approver is displayed below the current Approver with a * as their Label<br></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s3">PRS-007</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Added Approver appears in List of Approvers immediately</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Add Additional Approver<br>2. Validate that Added Approver is reflected to the List of Approvers once added<br>           i) Will not need to wait for Approval for the User to be added as an Additional Approver</td><td class="s3">2. The Added Approver is reflected to the List of Approvers once added<br>           i) Will not need to wait for Approval for the User to be added as an Additional Approver</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s3">PRS-008</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify notification is sent to added Additional Approver</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Add Addtional Approver<br>2. Login as Additional Approver <br>3. Validate that the tagged Additional Approver is notified through the Notification Bell</td><td class="s3">3. The  tagged Additional Approver is notified through the Notification Bell</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s3">PRS-009</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Default Approver can edit/delete Additional Approver before approval</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click edit/delete icon beside added approver<br>2. Validate that the system allows the Default Approver to Edit or Delete the Added Approver until they haven&#39;t Approve the Invoice</td><td class="s3">2. The system allows the Default Approver to Edit or Delete the Added Approver until they haven&#39;t Approve the Invoice</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s3">PRS-010</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s8">Verify current approver must approve before additional can approve</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Attempt to approve as Additional Approver before current Approver<br>2. Validate that the system require the current Approver to Approve the Invoice before the Additional Approver can Approve</td><td class="s3">2. The system require the current Approver to Approve the Invoice before the Additional Approver can Approve</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s3">PRS-010</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify that Add Approver button is displayed in the Approval Confirmation Modal only when no Additional Approver exists</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1.  Click Approve Button<br>2. Validate that If an Additional Approver is not yet Added, should allow adding of Approver when Approving the Invoice<br>             i) Should only display the Add Approver Button in the Confirmation Message if the Additional Approver is not yet indicated<br></td><td class="s3">2. If an Additional Approver is not yet Added, the system allows adding of Approver when Approving the Invoice<br>             i) The Add Approver Button in the Confirmation Message if the Additional Approver is not yet indicated is displayed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s3">PRS-011</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Critical</td><td class="s6">Verify Reject action displays modal with Notes</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Reject Button<br>2. Validate that Confirmation Modal appears with Add Note field</td><td class="s3">2. The Confirmation Modal appears with Add Note field</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s3">PRS-012</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Reject Notes field accepts valid inputs</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Enter valid inputs in Reject notes<br>2. Validate that the sytem require the Approver to enter a Notes before they are allowed to Reject  <br>   a) Alphanumeric and Special Characters except Emojis<br>   b) Maximum of 100 Characters</td><td class="s3">2. The sytem require the Approver to enter a Notes before they are allowed to Reject  <br>   a) Alphanumeric and Special Characters except Emojis<br>   b) Maximum of 100 Characters</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s3">PRS-013</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s8">Verify the system must not accept invalid inputs</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Attempt to enter invalid inputs in Reject notes<br>2. Validate that the system does not accept invalid inputs like emojis</td><td class="s3">2. The system does not accept invalid inputs like emojis</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s3">PRS-014</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Minor</td><td class="s8">Verify the system must not accept character length more than the maximum (100 characters)</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Attempt to enter invalid inputs in Reject notes<br>2. Validate that the system does not accept inputs exceeding 100 character length</td><td class="s3">2 The system does not accept inputs exceeding 100 character length</td><td class="s3"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="1050536734R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s3">PRS-015</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Cancel Button closes modal and returns to Invoice Approve form</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Cancel Button in Reject Modal<br>2. Validate that the Reject Modal closes and returns back to Invoice Approve Form<br></td><td class="s3">2. Reject Modal closes and returns back to Invoice Approve Form</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s3">PRS-016</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Critical</td><td class="s6">Verify Reject Button updates Invoice status, displays note, notifies users</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Reject Button in Reject Modal<br>2. Validate that Invoice Status must be IR Rejected<br>                a) Should dislpay the Rejecting Note to the Invoice&#39;s Check Notes Button<br>                b) Should notify the Requester and Assgined Purchasing Staff though the Notification Bell that the Invoice has been rejected<br>                    1) Should require the Requester or Assigned Purchasing Staff to resubmit the Invoice</td><td class="s3">2.  Invoice Status is IR Rejected<br>                a) The Rejecting Note to the Invoice&#39;s Check Notes Button is displayed<br>                b) The Requester and Assgined Purchasing Staff is notified through the Notification Bell that the Invoice has been rejected<br>                    1) Requires the Requester or Assigned Purchasing Staff to resubmit the Invoice</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s3">PRS-017</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Critical</td><td class="s6">Verify Approve action displays modal with Notes</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Approve Button<br>2. Validate that Confirmation Modal appears with Add Note field</td><td class="s3">2. The Confirmation Modal appears with Add Note field</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s3">PRS-018</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Approve Notes field accepts valid inputs</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Enter valid inputs in Approve notes<br>2. Validate that the sytem require the Approver to enter a Notes before they are allowed to Approve  <br>   a) Alphanumeric and Special Characters except Emojis<br>   b) Maximum of 100 Characters</td><td class="s3">2. The sytem require the Approver to enter a Notes before they are allowed to Approve  <br>   a) Alphanumeric and Special Characters except Emojis<br>   b) Maximum of 100 Characters</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s3">PRS-019</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s8">Verify the system must not accept invalid inputs</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Attempt to enter invalid inputs in Approve notes<br>2. Validate that the system does not accept invalid inputs like emojis</td><td class="s3">2. The system does not accept invalid inputs like emojis</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s3">PRS-020</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Minor</td><td class="s8">Verify the system must not accept character length more than the maximum (100 characters)</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Attempt to enter invalid inputs in Approve notes<br>2. Validate that the system does not accept inputs exceeding 100 character length</td><td class="s3">2 The system does not accept inputs exceeding 100 character length</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s3">PRS-021</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">High</td><td class="s6">Verify Cancel Button closes modal and returns to Invoice Approve form</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Cancel Button in Approve Modal<br>2. Validate that the Approve Modal closes and returns back to Invoice Approve Form<br></td><td class="s3">2. Approve Modal closes and returns back to Invoice Approve Form</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s3">PRS-022</td><td class="s4">[INVOICE] Approval of an Invoice</td><td class="s5">Critical</td><td class="s6">Verify Approve Button updates Invoice status, displays note, notifies users</td><td class="s3">1. An Invoice has been created and is for Approval</td><td class="s3">1. Click Approve Button in Approve Modal<br>2. Validate that Invoice Status must be IR Approved and Approver&#39;s Status as Approved<br>               a) Should dislpay the Approval Note to the Invoice&#39;s Check Notes Button<br>               b) Should require All Approvers to Approve to create a Payment Request</td><td class="s3">2.  Invoice Status is IR Approved and Approver&#39;s Status as Approved<br>                a) The Approval Note to the Invoice&#39;s Check Notes Button is displayed<br>                b) All Approvers are required to approve too create a Payment Request</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s2" colspan="9">[INVOICE] Invoice Status</td></tr><tr style="height: 19px"><th id="1050536734R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s3">PRS-1385-001</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">[INVOICE] Invoice Status</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    </span></td><td class="s5">High</td><td class="s10">Verify &quot;IR Draft&quot; status and styling</td><td class="s3">1. Invoice Status is in IR Draft</td><td class="s3"><br>1. Navigate to an invoice with &quot;IR Draft&quot; status.<br>2. Observe status label, background color, and text color.<br></td><td class="s3">2. Label displays IR Draft<br>Background Color: #5F636833<br>Text Color: #5F6368</td><td class="s3">MOVED TO MAIN SPRINT 1</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s3">PRS-1385-002</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">[INVOICE] Invoice Status</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    </span></td><td class="s5">High</td><td class="s10">Verify &quot;Invoice Received&quot; status and styling</td><td class="s3">1. Invoice Status is Invoice Received</td><td class="s3"><br>1. Navigate to an invoice with &quot;Invoice Received&quot; status.<br>2. Observe status label, background color, and text color.<br></td><td class="s3">2. Label displays Invoice Received<br>Background Color: #35C54933<br>Text Color: #35C549</td><td class="s3">MOVED TO MAIN SPRINT 1</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s11">PRS-1385-002</td><td class="s12"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">[INVOICE] Invoice Status</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    </span></td><td class="s13">High</td><td class="s14">Verify &quot;For IR Approval&quot; status and styling</td><td class="s11">1. An Invoice has been created<br>2. Invoice Status is For IR Approval</td><td class="s11"><br>1. Navigate to an invoice with &quot;For IR Approval&quot; status.<br>2. Observe status label, background color, and text color.<br></td><td class="s11">2. Label displays For IR Approval<br>Background Color: #F0963D33<br>Text Color: #F0963D</td><td class="s3">MOVED TO MAIN SPRINT 1</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s11">PRS-1385-003</td><td class="s12"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">[INVOICE] Invoice Status</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    </span></td><td class="s13">High</td><td class="s14">Verify &quot;IR Rejected&quot; status and styling</td><td class="s11">1. An Invoice has been created<br>2. Invoice Status is IR Rejected</td><td class="s11"><br>1. Navigate to an invoice with &quot;IR Rejected&quot; status.<br>2. Observe status label, background color, and text color.<br></td><td class="s11">2. Label displays IR Rejected<br>Background Color: #DC433B33<br>Text Color: #DC433B</td><td class="s3">MOVED TO MAIN SPRINT 1</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1050536734R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s11">PRS-1385-004</td><td class="s12"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">[INVOICE] Invoice Status</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">    </span></td><td class="s13">High</td><td class="s14">Verify &quot;IR Approved&quot; status and styling	</td><td class="s11">1. An Invoice has been created<br>2. Invoice Status is IR Approved</td><td class="s11"><br>1. Navigate to an invoice with &quot;IR Approved&quot; status.<br>2. Observe status label, background color, and text color.<br></td><td class="s11">2. Label displays IR Approved<br>Background Color: #1EA52B33<br>Text Color: #1EA52B33</td><td class="s3">MOVED TO MAIN SPRINT 1</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1050536734R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1050536734R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1050536734R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s15"></td><td class="s16"> Removed from AC</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></tbody></table></div>