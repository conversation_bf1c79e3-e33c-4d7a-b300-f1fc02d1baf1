/**
 * Base error class for application errors
 * All custom errors should extend this class
 */
class AppError extends Error {
  /**
   * Creates a new AppError
   * 
   * @param {string} message - Error message
   * @param {string} errorCode - Error code for identifying the error type
   * @param {Object} metadata - Additional error metadata
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(message, errorCode = 'APP_ERROR', metadata = {}, originalError = null) {
    super(message);
    
    this.name = this.constructor.name;
    this.errorCode = errorCode;
    this.metadata = metadata;
    this.timestamp = new Date().toISOString();
    this.originalError = originalError;
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Gets HTTP status code for this error
   * Override in subclasses to provide specific status codes
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 500; // Default to internal server error
  }

  /**
   * Converts error to a response object
   * 
   * @returns {Object} - Response object
   */
  toResponse() {
    return {
      status: this.getHttpStatus(),
      errorCode: this.errorCode,
      message: this.message,
      timestamp: this.timestamp,
      ...(Object.keys(this.metadata).length > 0 && { metadata: this.metadata }),
    };
  }

  /**
   * Creates an error from another error
   * 
   * @param {Error} error - Original error
   * @param {string} message - Optional message override
   * @param {string} errorCode - Optional error code override
   * @param {Object} metadata - Optional additional metadata
   * @returns {AppError} - New AppError instance
   */
  static from(error, message = null, errorCode = null, metadata = {}) {
    if (error instanceof AppError) {
      return error;
    }

    return new AppError(
      message || error.message || 'An unknown error occurred',
      errorCode || 'UNKNOWN_ERROR',
      metadata,
      error
    );
  }
}

module.exports = AppError;
