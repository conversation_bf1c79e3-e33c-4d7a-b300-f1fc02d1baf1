<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:right;color:#ffffff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:right;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-left:none;border-bottom:1px SOLID #000000;background-color:#1c4587;text-align:right;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-right:none;border-bottom:1px SOLID #000000;background-color:#1c4587;text-align:right;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:right;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:right;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#f3f3f3;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:8pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="333595653C0" style="width:182px;" class="column-headers-background">A</th><th id="333595653C1" style="width:474px;" class="column-headers-background">B</th><th id="333595653C2" style="width:100px;" class="column-headers-background">C</th><th id="333595653C3" style="width:100px;" class="column-headers-background">D</th><th id="333595653C4" style="width:100px;" class="column-headers-background">E</th><th id="333595653C5" style="width:100px;" class="column-headers-background">F</th></tr></thead><tbody><tr style="height: 19px"><th id="333595653R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td class="s0">Ticket</td><td class="s0">Defect Description</td><td class="s0">Defect Priority</td><td class="s0">Assigned To</td><td class="s0">Status</td><td class="s0">Module</td></tr><tr style="height: 19px"><th id="333595653R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-378">CITYLANDPRS-378</a></td><td class="s2"> [QA BUGS] [ADMIN] [USER MANAGEMENT] View User Details - &quot;Incorrect Header Name is displayed in Admin Details - View Modal</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-368">CITYLANDPRS-368 -</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Create a User - &quot;Adding Spaces in First and Last Name fields are not accepted</td><td class="s3">High</td><td class="s3">Francis.ong</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-369">CITYLANDPRS-369</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Edit a User - &quot;Adding Spaces in First and Last Name fields are not accepted</td><td class="s3">High</td><td class="s3">Francis.ong</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-374">CITYLANDPRS-374</a></td><td class="s2"> [QA BUGS] [ADMIN] [USER MANAGEMENT] Edit a User - &quot;Incorrect Error Message displayed in Admin Details - Edit modal when Department field is blank</td><td class="s3">High</td><td class="s3">kurt.arellano</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-375">CITYLANDPRS-375</a></td><td class="s2"> [QA BUGS] [ADMIN] [USER MANAGEMENT] View User Details - &quot;Department field is leave as blank when view admin details&quot;</td><td class="s3">High</td><td class="s5"></td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-376">CITYLANDPRS-376</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] View User Details - &quot;Not able to view details on management page when clicked user&#39;s username</td><td class="s3">High</td><td class="s3">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-379">CITYLANDPRS-379</a></td><td class="s2"> [QA BUGS] [ROOT USER] [USER MANAGEMENT] Edit a User - &quot;Incorrect Header Name is displayed in Admin Details - Edit Modal</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-380">CITYLANDPRS-380</a></td><td class="s2"> [QA BUGS] [ADMIN] [USER MANAGEMENT] Create a User - &quot;Incorrect button name is displayed in Create a New User button&quot;&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-396">CITYLANDPRS-396</a></td><td class="s2"> [QA BUGS] [ADMIN] [USER MANAGEMENT] Users Landing Page - &quot;Incorrect Label for &quot;Actions&quot; column is displayed&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-401">CITYLANDPRS-401</a></td><td class="s2"> [QA BUGS] [ALL USERS] [USER MANAGEMENT] User Profile - &quot;Edit Leave&quot; modal header and description is incorrect</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-400">CITYLANDPRS-400</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Users Landing Page - &quot;Incorrect counts of usernames displayed on the table list&quot;</td><td class="s3">High</td><td class="s3">Kurt.Arellano</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-544">CITYLANDPRS-544</a></td><td class="s2">[QA BUGS] [ALL USERS] [USER MANAGEMENT] User Profile - &quot;Incorrect placeholder - &quot;object object&quot; is displayed on edit and delete action buttons when hovered in user profile page&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">Reopened</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-402">CITYLANDPRS-402</a></td><td class="s2"> [QA BUGS] [ALL USERS] [LOGIN] Login to Users Account - &quot;Accepts Emoji character when user Create a New Password</td><td class="s3">High</td><td class="s3">Ejnar</td><td class="s3">For PO Acceptance</td><td class="s4">Login</td></tr><tr style="height: 19px"><th id="333595653R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-520">CITYLANDPRS-520</a></td><td class="s2"> [QA BUGS] [ADMIN] [USER MANAGEMENT] Create a User - &quot; Able to create a new User/IT Admin when Department Field is blank&quot;</td><td class="s3">High</td><td class="s3">Francis.ong</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-540">CITYLANDPRS-540</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Edit a User - &quot;Able to edit a User/IT Admin when Department Field is blank&quot;</td><td class="s3">High</td><td class="s3">Francis.ong</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-541">CITYLANDPRS-541</a></td><td class="s2"> [QA BUGS] [ALL USERS] [USER MANAGEMENT] User Profile - &quot;Incorrect display when clicked &quot;Leave date&quot; on user profile module&quot;<br></td><td class="s3">High</td><td class="s5"></td><td class="s3">New</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-542">CITYLANDPRS-542</a></td><td class="s2">[QA BUGS] [ALL USERS] [USER MANAGEMENT] User Profile - &quot;Incorrect display when clicked &quot;X&quot; or delete icon on action column in user profile &quot;</td><td class="s3">High</td><td class="s5"></td><td class="s3">New</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-545">CITYLANDPRS-545</a></td><td class="s2">[QA BUGS] [MANAGE SUPPLIER] Supplier Landing Page - &quot;Incorrect placeholder &quot;object object&quot; for Actions column buttons is displayed when hovered in supplier list page&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-546">CITYLANDPRS-546</a></td><td class="s2">[QA BUGS] [MANAGE SUPPLIER] Viewing of Supplier - &quot;Incorrect note displayed for Maximum limit file size to upload in Attachments button Supplier Details page (it should be 25mb)&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-547">CITYLANDPRS-547</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE SUPPLIER] Additional Fields for Supplier Edit - &quot;Incorrect header display for confirm changes modal in Supplier Details page&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-551">CITYLANDPRS-551</a></td><td class="s2">[QA BUGS] [MANAGE COMPANIES] Company Landing Page - &quot;Incorrect placeholder - &quot;object object&quot; is displayed on edit action button when hovered in Company/Association Management page&quot;</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-548">CITYLANDPRS-548</a></td><td class="s2">[QA BUGS] [PURCHASING OR PROCUREMENT STAFF] [MANAGE SUPPLIER] Indicating a Note for a Supplier - &quot;No submit comment modal is Displayed on Supplier Details page&quot;</td><td class="s3">High</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-552">CITYLANDPRS-552</a></td><td class="s6">Incorrect placeholder - &quot;object object&quot; is displayed on edit action button when hovered in Department management page&quot;</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-553">CITYLANDPRS-553</a></td><td class="s6">[QA BUGS] [MANAGE ITEMS] [NON-OFM ITEM] Non-OFM Items Landing Page &quot;Incorrect placeholder - &quot;object object&quot; is displayed on edit action buttons when hovered in Non OFM Items page&quot;&quot;</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Non-OFM Item</td></tr><tr style="height: 19px"><th id="333595653R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-561">CITYLANDPRS-561</a></td><td class="s2">[QA BUGS] [MANAGE COMPANIES] Company Landing Page - &quot;Unable to sort columns for Company Code or Association Code, Company Initials, TIN, Company Address, Contact Number and Co./Assoc. in Company/Association management page&quot;</td><td class="s3">Minor</td><td class="s5"></td><td class="s3">Invalid Defect</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-563">CITYLANDPRS-563</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Manual Creation of Association - &quot;Incorrect button name for &quot;confirm&quot; is displayed when creating Association&quot;</td><td class="s3">Minor</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-564">CITYLANDPRS-564</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Manual Creation of Association - &quot;Incorrect Modal name displayed when clicked cancel button when creating Association&quot;</td><td class="s3">Minor</td><td class="s3">Matty</td><td class="s3">Reopened</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-574">CITYLANDPRS-574</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE DEPARTMENT] Updating of Assigned Approvers for Association Department - &quot;Incorrect modal name displayed when clicked cancel button in edit department page&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-575">CITYLANDPRS-575</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE DEPARTMENT] Updating of Assigned Approvers for Association Department - &quot;Cancel button still enabled even update already saved in Association edit page&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">Reopened</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-576">CITYLANDPRS-576</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE DEPARTMENT] Updating of Assigned Approvers for Association Department - &quot;Incorrect modal name displayed when clicked cancel button in edit Association page&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-580">CITYLANDPRS-580</a></td><td class="s2">[RS CREATION] Creation of Requisition Slip - RS Details - &quot;Incorrect note of max file size is displayed (should be 25MB not 10MB) in RS Creation&quot;</td><td class="s7">Minor</td><td class="s8 softmerge"><div class="softmerge-inner" style="width:100px;left:-3px">Maureen / Richard</div></td><td class="s8">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-584">CITYLANDPRS-584</a></td><td class="s2">[QA BUG] [ROOT USER] [USER MANAGEMENT] Root Access User Management - &quot;Incorrect to Sort for Status Column in Root User Access IT management page&quot;</td><td class="s3">High</td><td class="s3">Francis</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-585">CITYLANDPRS-585</a></td><td class="s2">[QA BUGS] [ROOT USER] [USER MANAGEMENT] Root Access User Management - &quot;Username accepts Spaces in Root User - IT Admin User Creation&quot;</td><td class="s3">High</td><td class="s3">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-586">CITYLANDPRS-586</a></td><td class="s2">[QA BUGS] [ROOT USER] [USER MANAGEMENT] Root Access User Management - &quot;Not stated the User&#39;s First Name and Last Name in the Confirmation Modal in Root User - Deactivate an IT Admin User from Status Column&quot;</td><td class="s3">High</td><td class="s3">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-376">CITYLANDPRS-376</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] View User Details - &quot;Not able to view details on management page when clicked user&#39;s username</td><td class="s3">High</td><td class="s3">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-560">CITYLANDPRS-560</a></td><td class="s2">[QA BUGS] [MANAGE COMPANIES] Company Landing Page - &quot;Unable to search company name in Company/Association management page&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-581">CITYLANDPRS-581</a></td><td class="s2">[QA BUGS] [RS CREATION] Creation of Requisition Slip - RS Details &quot;UI issue &quot;OFM&quot; in Non-OFM displayed as lower case in RS Creation for Non OFM items&quot;</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-562">CITYLANDPRS-562</a></td><td class="s2">[QA BUGS] [MANAGE COMPANIES] View Company - &quot;Incorrect Project List displayed when viewing a Company/Association&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-582">CITYLANDPRS-582</a></td><td class="s2">[QA BUGS] [RS CREATION] Creation of Requisition Slip - RS Details - &quot;Field error not specified in toast message when note characters exceeds the 100 characters limit in Saving RS as Draft&quot;</td><td class="s3">Minor</td><td class="s5"></td><td class="s3">New</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-606">CITYLANDPRS-606</a></td><td class="s2">[QA BUGS] [Manage Supplier]  -  Sort not working after clicking more than 2x</td><td class="s3">Minor</td><td class="s5"></td><td class="s3">New</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-565">CITYLANDPRS-565</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Manual Creation of Association - &quot;Association Name field Accepts emoji when creating Association&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-566">CITYLANDPRS-566</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Manual Creation of Association - &quot;Company Initial field accepts numbers when creating Association&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-567">CITYLANDPRS-567</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Manual Creation of Association - &#39;&#39;TIN Field accepts Alphanumeric characters when creating Association</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-568">CITYLANDPRS-568</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Updating Association Details &quot;Association Name field Accepts emoji when updating Association details&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-571">CITYLANDPRS-571</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Updating Association Details - &quot;Company Initial field accepts numbers when updating Association details&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-572">CITYLANDPRS-572</a></td><td class="s2">[QA BUGS] [ENHANCEMENTS] [MANAGE COMPANY] Updating Association Details &quot;TIN Field accepts Alphanumeric characters when updating Association details&quot;</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-611">CITYLANDPRS-611</a></td><td class="s2">[QA BUGS] [Manage Supplier]  - Field Supplier Name shows Supplier only</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-613">CITYLANDPRS-613</a></td><td class="s2">[QA BUGS] [Manage Supplier]  - Individual/Corporate Code shows Individual/Corporate only</td><td class="s3">Minor</td><td class="s3">Kurt</td><td class="s3">Invalid Defect</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-614">CITYLANDPRS-614</a></td><td class="s2">[QA BUGS] [Manage Supplier]  -  Supplier List table with empty or &#39;&quot;--&quot;&quot; values mismatch on Supplier Details</td><td class="s3">Minor</td><td class="s3">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-625">CITYLANDPRS-625</a></td><td class="s2">[QA BUGS] [User Managerment]  - Sort not working when click multple times or after 2 times</td><td class="s3">Minor</td><td class="s4">Francis</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-627">CITYLANDPRS-627</a></td><td class="s2">[QA BUGS] [User Managerment]  - Edit button shows [object] [object]</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-634">CITYLANDPRS-634</a></td><td class="s2">[QA BUGS] [User Managerment]  -  Username accepts spaces and against the requirement</td><td class="s4">Minor</td><td class="s4">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-579">CITYLANDPRS-579</a></td><td class="s2">[QA BUGS] [RS CREATION] Creation of Requisition Slip - RS Details &quot;Purpose text field accepts greater than 50 characters in RS Creation&quot;</td><td class="s3">High</td><td class="s3">keith</td><td class="s3">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-635">CITYLANDPRS-635</a></td><td class="s2">[QA BUGS] [User Managerment]  -  User Type displays Engineer, Department Division Head and against the requirement</td><td class="s4">Minor</td><td class="s5"></td><td class="s3">New</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-636">CITYLANDPRS-636</a></td><td class="s2">[QA BUGS] [User Managerment]  - When highlighting all characters in email address, keep changes pop-up keeps on showing</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s3">Invalid Defect</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-654">CITYLANDPRS-654</a></td><td class="s2">[QA BUGS] [Manage Supplier]  - Contact number accepts other format than +63</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s3">Reopened</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-655">CITYLANDPRS-655</a></td><td class="s2">[QA BUGS] [Manage Supplier]  - Edit supplier | Error message for Contact Person and Contact Number do not display the exact field in question</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-646">CITYLANDPRS-646</a></td><td class="s2">[QA BUGS] [Login]  - Session timeout appears even session is active</td><td class="s4">Minor</td><td class="s5"></td><td class="s4">New</td><td class="s4">Login</td></tr><tr style="height: 19px"><th id="333595653R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-587">CITYLANDPRS-587</a></td><td class="s2">[QA BUGS] [RS VIEWING] Edit the Draft of Requisition Slip - &quot;No Edit button displayed on RS Viewing&quot;</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">Invalid Defect</td><td class="s4">RS Viewing</td></tr><tr style="height: 19px"><th id="333595653R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-656">CITYLANDPRS-656</a></td><td class="s2">[QA BUGS] [Manage Supplier]  -  Viewing of Added Note | No identification of upload date per note</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s4">New</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-608">CITYLANDPRS-608</a></td><td class="s2">[QA BUGS] [Manage Requistion Slip}  - Go back in Requisition Slip not working</td><td class="s3">High</td><td class="s3">Matty</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Requistion Slip</td></tr><tr style="height: 19px"><th id="333595653R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s9"></td><td class="s2">[QA BUGS] [Manage Items]  - GFQ and Trade sort not working after clicking more than 2x</td><td class="s4">Minor</td><td class="s4">Ejnar</td><td class="s4">New</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-661">CITYLANDPRS-661</a></td><td class="s2">[QA BUGS] [Manage Items]  - Non-OFM | Actual shows Type instead of Item Type</td><td class="s4">Minor</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-661">CITYLANDPRS-661</a></td><td class="s2">[QA BUGS] [Manage Items]  - Non-OFM | Sort not working when clicked more than 2x</td><td class="s4">Minor</td><td class="s5"></td><td class="s4">Invalid Defect</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-618">CITYLANDPRS-618</a></td><td class="s2">[QA BUGS] [User Managerment]  -   IT Admin |  Previous and next button not working properly</td><td class="s3">High</td><td class="s4">keith</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-554">CITYLANDPRS-554</a></td><td class="s2">[QA BUGS] [MANAGE SUPPLIER] Supplier Landing Page - &quot;Oops! Something went wrong.&quot; error is displayed when clicked next button on pagination  in Supplier List page</td><td class="s4">Critical</td><td class="s4">Mau</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-633">CITYLANDPRS-633</a></td><td class="s2">[QA BUGS] [User Managerment] - Create a User | Characters such as &quot;.&quot;, &quot;-&quot;, &quot;ñ&quot; are not accepted on First Name/Last Name</td><td class="s4">High</td><td class="s4">keith</td><td class="s3">For PO Acceptance</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-556">CITYLANDPRS-556</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] View User Details - &quot;Oops! Something went wrong.&quot; error is displayed when viewing user details without department value thru edit action column in  IT Admin access</td><td class="s4">Critical</td><td class="s4">kurt.arellano</td><td class="s4">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-557">CITYLANDPRS-557</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Edit a User - Unable to update User/Admin details due to Supervisor field error</td><td class="s4">Critical</td><td class="s4">kurt.arellano</td><td class="s4">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-558">CITYLANDPRS-558</a></td><td class="s2">[QA BUGS] [ALL USERS] [LOGIN] Login to Users Account -  Incorrect page locations is displayed[FE]</td><td class="s4">Critical</td><td class="s4">Matty</td><td class="s4">For PO Acceptance</td><td class="s4">Login</td></tr><tr style="height: 19px"><th id="333595653R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-637">CITYLANDPRS-637</a></td><td class="s2">[QA BUGS] [User Managerment]  -  Getting error  &quot;Something went wrong&quot; when gmail has 100 characters. Requirement not met.</td><td class="s4">High</td><td class="s4">keith</td><td class="s3">For PO Acceptance</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-638">CITYLANDPRS-638</a></td><td class="s2">[QA BUGS] [User Managerment] - Deactivating the user in Staus column returns error 422 and not the First Name and Last Name in confirmation model</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">User Managerment</td></tr><tr style="height: 19px"><th id="333595653R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-559">CITYLANDPRS-559</a></td><td class="s2">[QA BUG][Page Location] Error on Page location pagination</td><td class="s3">Critical</td><td class="s3">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">Login</td></tr><tr style="height: 19px"><th id="333595653R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-583">CITYLANDPRS-583</a></td><td class="s2">[QA BUGS] [ROOT USER] [USER MANAGEMENT] Root Access User Management - &quot;Unable to Search Users in Root User Access IT management page&quot;</td><td class="s3">Critical</td><td class="s3">Keith</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-573">CITYLANDPRS-573</a></td><td class="s2">[QA BUGS] [MANAGE DEPARTMENT] Department Landing Page - &quot;Unable to search department name in Department management page&quot;</td><td class="s3">Critical</td><td class="s3">Keith</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R76" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">77</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-588">CITYLANDPRS-588</a></td><td class="s2">[QA BUGS] [Manage Supplier]  - Edit Supplier |  Intermittent | Pop-up error related to Contact Person keeps on returning when updating Line of business</td><td class="s4">High</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R77" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">78</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-577">CITYLANDPRS-577</a></td><td class="s2">[QA BUGS] [MANAGE ITEMS] [OFM LIST] OFM List Landing Page - &quot;No filter button and table displayed in OFM List landing Page&quot; [FE]</td><td class="s3">Critical</td><td class="s3">Keith</td><td class="s3">For PO Acceptance</td><td class="s4">OFM List</td></tr><tr style="height: 19px"><th id="333595653R78" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">79</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-578">CITYLANDPRS-578 </a></td><td class="s2">MANAGE ITEMS] [OFM LIST] OFM List Landing Page -&#39;&#39;Unable to search OFM List name in OFM List page&quot; [FE]</td><td class="s3">Critical</td><td class="s3">Keith</td><td class="s3">For PO Acceptance</td><td class="s4">OFM List</td></tr><tr style="height: 19px"><th id="333595653R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-570">CITYLANDPRS-570</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Create a User - &quot;Unable to create user w/ Purchasing Head User type&quot;</td><td class="s3">Critical</td><td class="s5"></td><td class="s3">Invalid Defect</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-569">CITYLANDPRS-569</a></td><td class="s2">[QA BUGS] [ADMIN] [USER MANAGEMENT] Edit a User - &quot;Unable to update user to Purchasing Head User type&quot;</td><td class="s3">Critical</td><td class="s5"></td><td class="s3">Invalid Defect</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-615">CITYLANDPRS-615</a></td><td class="s2">[QA BUGS] [User Managerment]  - IT Admin |  Search not working in Admin &gt; User Management</td><td class="s3">Critical</td><td class="s4">keith</td><td class="s3">For PO Acceptance</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-647">CITYLANDPRS-647</a></td><td class="s2">[QA BUGS] [Manage Supplier]  -  Able to edit Supplier such as contact number and Line of business when Supplier status is Suspended</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-642">CITYLANDPRS-642</a></td><td class="s2">[QA BUGS] [Manage Supplier]  - Updating contact number returning &quot;Contact must be at most 12 characters&quot; error</td><td class="s4">Critical</td><td class="s4">Maureen</td><td class="s3">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-659">CITYLANDPRS-659</a></td><td class="s2">[QA BUGS] [Manage Items]  -  OFM Items | Pagination previous and next button returning oops! something went wrong </td><td class="s4">High</td><td class="s4">keith</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-589">CITYLANDPRS-589</a></td><td class="s2">[QA BUGS] [Manage Supplier]  -  More than 2MB file upload failed expected is 25MB per file [NGINX]</td><td class="s4">Critical</td><td class="s4">keith</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-639">CITYLANDPRS-639</a></td><td class="s10">[QA BUGS] [Manage Supplier]  -  View Note | Filter by Date not working in viewing of notes [FE]</td><td class="s4">Critical</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-640">CITYLANDPRS-640</a></td><td class="s2">[QA BUGS] [Manage Supplier]  -  Deleting attachment file failed and returning error &quot;Failed while updating supplier attachments&quot; [FE]</td><td class="s4">Critical</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-643">CITYLANDPRS-643</a></td><td class="s2">[QA BUGS] [Manage Items]  -  OFM Items | Search field not working in OFM Items</td><td class="s4">Critical</td><td class="s4">KEITH</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-645">CITYLANDPRS-645</a></td><td class="s2">[QA BUGS] [Manage Items]  -  OFM Items | Filter icon do not exist</td><td class="s4">Critical</td><td class="s4">KEITH</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R90" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">91</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-660">CITYLANDPRS-660</a></td><td class="s2">[QA BUGS] [Manage Items]  - Non-OFM | Filter by type returns items for all types [BE]</td><td class="s4">Critical</td><td class="s4">Ejnar</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R91" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">92</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-648">CITYLANDPRS-648</a></td><td class="s2">[QA BUGS] [Manage Items]  - Non-OFM | No error message displayed on invalid input</td><td class="s4">High</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R92" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">93</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-653">CITYLANDPRS-653</a></td><td class="s2">[QA BUGS] [Manage Items]  - Non-OFM | Trade Drop-down not displayed as per requirement</td><td class="s4">High</td><td class="s5"></td><td class="s4">Invalid Defect</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R93" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">94</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-644">CITYLANDPRS-644</a></td><td class="s2">[QA BUGS] [Manage Items]  -  OFM Items | Filter with no match returning  &quot;Request failed with status code 500&quot;</td><td class="s4">High</td><td class="s4">Francis</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R94" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">95</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-666">CITYLANDPRS-666</a></td><td class="s2">[QA BUGS] [ALL USERS] [LOGIN] Cityland PRS Landing Page - &quot;Oops! Something went wrong. is displayed upon validate otp&quot;</td><td class="s4">Critical</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Login</td></tr><tr style="height: 19px"><th id="333595653R95" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">96</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-668">CITYLANDPRS-668</a></td><td class="s2">[CITYLAND BUG] - ITEMS&gt;NON-OFM CREATION &quot;Remove Trade in Non-OFM, allow entry of new &#39;unit&#39;. Does not indicate where to setup the unit&quot;</td><td class="s4">High</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">NON-OFM Item</td></tr><tr style="height: 19px"><th id="333595653R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-669">CITYLANDPRS-669</a></td><td class="s2">[CITYLAND BUG] - ITEMS&gt;NON-OFM CREATION &quot;Cannot add new item, no error message given. special character in description probably not accepted&quot;</td><td class="s4">High</td><td class="s4">Francis</td><td class="s4">For PO Acceptance</td><td class="s4">NON-OFM Item</td></tr><tr style="height: 19px"><th id="333595653R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-577">CITYLANDPRS-577</a></td><td class="s2">[CITYLAND BUG]  -  ITEMS&gt;OFM LIST &quot;No List reflected/ bug on display from OFM dropdown will show a table, but when you click it, it will have an error&quot;</td><td class="s4">Critical</td><td class="s4">Keith</td><td class="s4">For PO Acceptance</td><td class="s4">OFM List</td></tr><tr style="height: 19px"><th id="333595653R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-670">CITYLANDPRS-670</a></td><td class="s2">[CITYLAND BUG] - REQUISITION SLIP &quot;Cannot submit requisition slip/ cannot save draft of requisition slip&quot;</td><td class="s4">Critical</td><td class="s4">Francis/Matty</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-671">CITYLANDPRS-671</a></td><td class="s2">[CITYLAND BUG] -RS DASHBOARD &quot;Cannot edit submitted Non-OFM Requisition Slip and an error page appears.&quot;</td><td class="s4">Critical</td><td class="s5"></td><td class="s4">Invalid Defect</td><td class="s4">RS Dashboard</td></tr><tr style="height: 19px"><th id="333595653R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-672">CITYLANDPRS-672</a></td><td class="s2">[QA BUG] [ENHANCEMENTS] [MANAGE COMPANY] Viewing an Association - &quot;Association Details are not displayed, when viewing and editing created association&quot;</td><td class="s4">Critical</td><td class="s5"></td><td class="s4">For PO Acceptance</td><td class="s4">Manage Company</td></tr><tr style="height: 19px"><th id="333595653R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-677">CITYLANDPRS-677</a></td><td class="s2">[QA BUGS]  [RS CREATION] Creation of Requisition Slip - RS Details - Specific field not specified in error message, when quantity and notes is blank</td><td class="s4">High</td><td class="s4">keith</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-678">CITYLANDPRS-678</a></td><td class="s2">[QA BUGS] [MANAGE ITEMS] [OFM LIST] Viewing of OFM Items - &quot;Project and company fields are displayed when viewing created Non - OFM Items&quot;</td><td class="s4">High</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Non-OFM Items</td></tr><tr style="height: 19px"><th id="333595653R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-679">CITYLANDPRS-679</a></td><td class="s2">[QA BUGS] [RS CREATION] Creation of Requisition Slip - RS Details - &quot;Updated changes not displayed when viewing created draft RS&quot;</td><td class="s4">High</td><td class="s4">Ejnar/Matty</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-687">CITYLANDPRS-687</a></td><td class="s2">[QA BUGS] RS Creation | Error message not associated to the field in error. Error returns &quot;Invalid enum value. Expected &#39;supplier&#39; | &#39;project&#39; | &#39;association&#39; | &#39;company&#39;, received&quot;</td><td class="s4">High</td><td class="s4">Francis/Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-688">CITYLANDPRS-688</a></td><td class="s2">[QA BUGS] RS Creation | Error message not associated to the field in error when Charge to client is empty. Error returns &quot;Charge to supplier with id of 0 not found&quot; and &quot;Charge to company with id of 0 not found&quot;</td><td class="s4">High</td><td class="s4">Francis/Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R106" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">107</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-682">CITYLANDPRS-682</a></td><td class="s2">[QA BUGS] | RS Creation] - Items pagination not working when adding more than 10 items upon RS Creation</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R107" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">108</div></th><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-683">CITYLANDPRS-683</a></td><td class="s2">QA BUGS] | RS Creation] - Items pagination not available and displays &#39;1 to 0 of 0 items&#39; count when adding more than 10 items upon RS Submission</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R108" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">109</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-681">CITYLANDPRS-681</a></td><td class="s2">[QA BUGS] | RS Creation - Updating RS - Items added do not reflect when updating draft RS</td><td class="s4">High</td><td class="s4">Ejnar/Matty</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R109" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">110</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-686">CITYLANDPRS-686</a></td><td class="s2">[QA BUGS] RS Creation| Duplicate approvers for Supervisor</td><td class="s4">High</td><td class="s4">Richard</td><td class="s4">Invalid Defect</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R110" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">111</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-680">CITYLANDPRS-680</a></td><td class="s2">[QA BUGS] [RS DASHBOARD] Dashboard View for Approvers - &quot;Incorrect number showing of entries displayed in the table for My Approval/Assigned&quot;</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">For PO Acceptance</td><td class="s4">RS Dashboard</td></tr><tr style="height: 19px"><th id="333595653R111" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">112</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-715">CITYLANDPRS-715</a></td><td class="s2">No notification received for Assigned Purchasing staff</td><td class="s4">Minor</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Assign</td></tr><tr style="height: 19px"><th id="333595653R112" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">113</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-714">CITYLANDPRS-714</a></td><td class="s2">Cannot scroll to select another approver</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Assign</td></tr><tr style="height: 19px"><th id="333595653R113" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">114</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-684">CITYLANDPRS-684</a></td><td class="s2">[QA BUGS] | RS Creation - Select Actions displays options for &#39;&#39;Assign to me&#39; and &#39;Assign to others&#39; on submitted RS when user is not Purchasing Head</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R114" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">115</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-685">CITYLANDPRS-685</a></td><td class="s2">[QA BUGS] [RS CREATION] Creation of Requisition Slip - RS Details - &quot;Attached attachment during RS creation is not displayed after submit RS.&quot;</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R115" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">116</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-690">CITYLANDPRS-690</a></td><td class="s2">[QA BUGS] Edit Supplier | Required fields Contact Number and Line of Business do not have error handling when contact person is populated. Save is successful for empty input even these 3 are required fields Contact Number, Line of Business and Contact Person</td><td class="s4">High</td><td class="s4">Keith</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R116" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">117</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-691">CITYLANDPRS-691</a></td><td class="s2">[QA BUGS] RS Creation | Notes returning error msg as required field. Notes should be optional</td><td class="s4">High</td><td class="s5"></td><td class="s4">For PO Acceptance</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R117" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">118</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-692">CITYLANDPRS-692</a></td><td class="s2">[QA BUGS] Manage Supplier | Unable to suspend Supplier on both Actions and Supplier Name Edit</td><td class="s4">Critical</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Supplier</td></tr><tr style="height: 19px"><th id="333595653R118" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">119</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-694">CITYLANDPRS-694</a></td><td class="s2">[QA BUGS] RS Creation | Uploading attachment do not reflect after saving as Draft but reflects on Check attachment after RS Submission</td><td class="s4">High</td><td class="s4">Bryan</td><td class="s4">Invalid Defect</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R119" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">120</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-713">CITYLANDPRS-713</a></td><td class="s2">Non-OFM | New Item | Item Name accepts emoji</td><td class="s4">Minor</td><td class="s5"></td><td class="s4">New</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R120" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">121</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-712">CITYLANDPRS-712</a></td><td class="s2">Unit selection on search and Unit value on table are not equal</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">Manage Items</td></tr><tr style="height: 19px"><th id="333595653R121" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">122</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-711">CITYLANDPRS-711</a></td><td class="s2">Invalid input error when QTY and Notes of items are empty. Error msg not associated with field</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R122" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">123</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-697">CITYLANDPRS-697</a></td><td class="s2">[QA BUGS] [MANAGE DEPARTMENT] Department Landing Page - Incorrect number of entries displayed in manage department page upon first visit and pagination doesn&#39;t work as expected</td><td class="s4">Critical</td><td class="s4">Kurt</td><td class="s4">For PO Acceptance</td><td class="s4">Manage Department</td></tr><tr style="height: 19px"><th id="333595653R123" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">124</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-698">CITYLANDPRS-698</a></td><td class="s2">[QA BUGS] UAT | User Management | Email gets deleted after user details modification</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">User Management</td></tr><tr style="height: 19px"><th id="333595653R124" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">125</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-710">CITYLANDPRS-710</a></td><td class="s2">RS Creation | Adding items from 2 item list do not reflect on the table</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R125" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">126</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-709">CITYLANDPRS-709</a></td><td class="s2">RS Creation | Adding 11 items from 1 item list returning &#39;Invalid Input&#39; error upon saving as draft and the 11th item gets qty and notes automatically updated</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R126" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">127</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-708">CITYLANDPRS-708</a></td><td class="s2">RS Creation | Upload file with more than 25MB returns something went wrong</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Creation</td></tr><tr style="height: 19px"><th id="333595653R127" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">128</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-699">CITYLANDPRS-699</a></td><td class="s2">[QA BUGS] [RS APPROVAL] Rejecting of Requisition Slip - &quot;Succeeding assigned approvers are still able to approved RS, even thought there is previously rejected  approver</td><td class="s4">High</td><td class="s4">Maureen</td><td class="s4">For PO Acceptance</td><td class="s4">RS Approval</td></tr><tr style="height: 19px"><th id="333595653R128" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">129</div></th><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-700">CITYLANDPRS-700</a></td><td class="s2">[QA BUGS] [PURCHASING OR PROCURMENT STAFF] [MANAGE PROJECTS] Editing of Projects - &quot;Unable to add users of trade management in project management view page&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">Project Management</td></tr><tr style="height: 19px"><th id="333595653R129" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">130</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-701">CITYLANDPRS-701</a></td><td class="s2">[QA BUGS] NON-OFM | Filter by Item type not working</td><td class="s4">High</td><td class="s5"></td><td class="s4">For PO Acceptance</td><td class="s4">Non-OFM Item</td></tr><tr style="height: 19px"><th id="333595653R130" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">131</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-702">CITYLANDPRS-702</a></td><td class="s2">[QA BUGS] [RS DASHBOARD] Dashboard Search - &quot;Search and Filter RS Items are not working in all tabs of RS Dashboard&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Dashboard</td></tr><tr style="height: 19px"><th id="333595653R131" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">132</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-703">CITYLANDPRS-703</a></td><td class="s2">[QA BUGS] [ADMIN] [USER TYPE ACCESS] Admin Menu - &quot;Able to Access RS History with Assistant Manager User type access&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">User Types</td></tr><tr style="height: 19px"><th id="333595653R132" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">133</div></th><td class="s9"></td><td class="s2">RS Assigning | Assigning same or existing assignee no error handling</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">RS Assigning</td></tr><tr style="height: 19px"><th id="333595653R133" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">134</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-704">CITYLANDPRS-704</a></td><td class="s2">[QA BUGS] [ADMIN] [USER TYPE ACCESS] Admin Menu - &quot;Able to Access RS History with Department Head User type access&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">User Types</td></tr><tr style="height: 19px"><th id="333595653R134" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">135</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-705">CITYLANDPRS-705</a></td><td class="s2">[QA BUGS] [ADMIN] [USER TYPE ACCESS] Admin Menu - &quot;Able to Access RS History with Department Secretary User type access&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">User Types</td></tr><tr style="height: 19px"><th id="333595653R135" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">136</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-706">CITYLANDPRS-706</a></td><td class="s2">[QA BUGS] [ADMIN] [USER TYPE ACCESS] Admin Menu - &quot;Able to Access RS History with Division Haed User type access&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">User Types</td></tr><tr style="height: 19px"><th id="333595653R136" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">137</div></th><td class="s1"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-707">CITYLANDPRS-707</a></td><td class="s2">[QA BUGS] [ADMIN] [USER TYPE ACCESS] Admin Menu - &quot;Able to Access RS History with Area Staff User type access&quot;</td><td class="s4">High</td><td class="s5"></td><td class="s4">New</td><td class="s4">User Types</td></tr></tbody></table></div>