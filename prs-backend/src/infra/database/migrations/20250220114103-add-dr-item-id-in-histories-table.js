'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('histories', 'dr_item_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'delivery_receipt_items',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('histories', 'dr_item_id');
  }
};

