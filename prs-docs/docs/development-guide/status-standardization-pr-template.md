# Status Standardization PR Template

## Description

This PR standardizes status constants in the [module/file] to follow the established naming convention.

## Changes

- Updated status constants to use `Object.freeze()`
- Standardized status values to use lowercase with underscores (snake_case)
- Replaced raw string comparisons with constant references
- Added migration script for database updates (if applicable)

## Checklist

- [ ] Updated constant definitions in constants files
- [ ] Replaced raw string comparisons in services
- [ ] Replaced raw string comparisons in controllers
- [ ] Replaced raw string comparisons in repositories
- [ ] Added/updated migration scripts for database changes
- [ ] Verified that all tests pass
- [ ] Ran the `find-raw-status-strings.js` script to verify no raw strings remain

## Testing

Describe how you tested these changes:

1. Ran unit tests
2. Manually tested affected workflows
3. Verified database records have correct status values

## Related Issues

Closes #[issue number]

## Screenshots (if applicable)

[Add screenshots here]
