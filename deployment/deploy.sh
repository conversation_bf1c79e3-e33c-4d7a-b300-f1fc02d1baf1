#!/bin/bash

# Exit on error
set -e

# Display help message
function show_help {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -b, --build                Build all containers"
    echo "  -u, --up                   Start all containers"
    echo "  -d, --down                 Stop all containers"
    echo "  -r, --restart              Restart all containers"
    echo "  -m, --migrate              Run database migrations"
    echo "  -s, --seed                 Run database seeders"
    echo "  -l, --logs                 Show logs"
    echo "  -c, --clean                Clean up unused Docker resources"
    echo "  --init-db                  Initialize the database (migrate + seed)"
    echo "  --backup-db                Backup the database"
    echo "  --restore-db [file]        Restore the database from backup"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running or not installed"
    exit 1
fi

# Check if docker compose is installed
if ! docker compose version > /dev/null 2>&1; then
    echo "Error: docker compose is not installed or not available"
    exit 1
fi

# Default values
BUILD=false
UP=false
DOWN=false
RESTART=false
MIGRATE=false
SEED=false
LOGS=false
CLEAN=false
INIT_DB=false
BACKUP_DB=false
RESTORE_DB=false
RESTORE_FILE=""

# Parse arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -u|--up)
            UP=true
            shift
            ;;
        -d|--down)
            DOWN=true
            shift
            ;;
        -r|--restart)
            RESTART=true
            shift
            ;;
        -m|--migrate)
            MIGRATE=true
            shift
            ;;
        -s|--seed)
            SEED=true
            shift
            ;;
        -l|--logs)
            LOGS=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        --init-db)
            INIT_DB=true
            shift
            ;;
        --backup-db)
            BACKUP_DB=true
            shift
            ;;
        --restore-db)
            RESTORE_DB=true
            if [[ $# -gt 1 && ! $2 =~ ^- ]]; then
                RESTORE_FILE="$2"
                shift
            else
                echo "Error: --restore-db requires a file argument"
                exit 1
            fi
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# If no options provided, show help
if [[ $BUILD == false && $UP == false && $DOWN == false && $RESTART == false && $MIGRATE == false && $SEED == false && $LOGS == false && $CLEAN == false && $INIT_DB == false && $BACKUP_DB == false && $RESTORE_DB == false ]]; then
    show_help
    exit 0
fi

# Build containers
if [[ $BUILD == true ]]; then
    echo "Building containers..."
    docker compose build
fi

# Start containers
if [[ $UP == true ]]; then
    echo "Starting containers..."
    docker compose up -d
fi

# Stop containers
if [[ $DOWN == true ]]; then
    echo "Stopping containers..."
    docker compose down
fi

# Restart containers
if [[ $RESTART == true ]]; then
    echo "Restarting containers..."
    docker compose restart
fi

# Run migrations
if [[ $MIGRATE == true ]]; then
    echo "Running migrations..."
    docker compose exec backend npm run migrate:prod
fi

# Run seeders
if [[ $SEED == true ]]; then
    echo "Running seeders..."
    docker compose exec backend npm run seed:prod
fi

# Initialize database
if [[ $INIT_DB == true ]]; then
    echo "Initializing database..."
    docker compose exec backend npm run migrate:prod
    docker compose exec backend npm run seed:prod
fi

# Show logs
if [[ $LOGS == true ]]; then
    echo "Showing logs..."
    docker compose logs -f
fi

# Clean up
if [[ $CLEAN == true ]]; then
    echo "Cleaning up Docker resources..."
    docker system prune -f
fi

# Backup database
if [[ $BACKUP_DB == true ]]; then
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="prs_db_backup_$TIMESTAMP.sql"
    echo "Backing up database to $BACKUP_FILE..."
    docker compose exec -T postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > $BACKUP_FILE
    echo "Backup completed"
fi

# Restore database
if [[ $RESTORE_DB == true ]]; then
    if [[ ! -f $RESTORE_FILE ]]; then
        echo "Error: Backup file $RESTORE_FILE not found"
        exit 1
    fi
    echo "Restoring database from $RESTORE_FILE..."
    cat $RESTORE_FILE | docker compose exec -T postgres psql -U $POSTGRES_USER $POSTGRES_DB
    echo "Restore completed"
fi

echo "Done!"
