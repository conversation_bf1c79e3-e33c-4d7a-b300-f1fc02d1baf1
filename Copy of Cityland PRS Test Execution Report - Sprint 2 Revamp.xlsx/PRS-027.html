<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffe599;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON>s,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b6d7a8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle no-grid" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="2024762676C0" style="width:103px;" class="column-headers-background">A</th><th id="2024762676C1" style="width:167px;" class="column-headers-background">B</th><th id="2024762676C2" style="width:252px;" class="column-headers-background">C</th><th id="2024762676C3" style="width:86px;" class="column-headers-background">D</th><th id="2024762676C4" style="width:330px;" class="column-headers-background">E</th><th id="2024762676C5" style="width:281px;" class="column-headers-background">F</th><th id="2024762676C6" style="width:63px;" class="column-headers-background">G</th><th id="2024762676C7" style="width:384px;" class="column-headers-background">H</th><th id="2024762676C9" style="width:126px;" class="column-headers-background">J</th><th id="2024762676C10" style="width:130px;" class="column-headers-background">K</th><th id="2024762676C11" style="width:130px;" class="column-headers-background">L</th><th id="2024762676C12" style="width:130px;" class="column-headers-background">M</th><th id="2024762676C13" style="width:130px;" class="column-headers-background">N</th><th id="2024762676C14" style="width:78px;" class="column-headers-background">O</th><th id="2024762676C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="2024762676R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-Requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s2">Dev_wk4+wk5</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="2024762676R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="15">PRS-027 - [Return items] [Critical Scenarios] Returning an Item, Recording the Delivered Item after return, Cancelling of Request to Return Item</td></tr><tr style="height: 19px"><th id="2024762676R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-027-001</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Redirection to Delivery Receipt</td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Click an RS Number from the Dashboard.<br>2. Verify RS Details are displayed.<br>3. Click the Related Documents tab.<br>4. Click the Deliveries tab.<br>5. Click a DR Number to view the Delivery Receipt.</td><td class="s8"></td><td class="s5">1. RS Details should be displayed correctly.<br>2. Related Documents tab should open without errors.<br>3. Deliveries tab should be accessible.<br>4. DR Number should be clickable and display Delivery Receipt details.</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="2024762676R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-027-002</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Viewing Attachments and Notes</td><td class="s7">High</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Click the &quot;Check Attachment&quot; button.<br>2. Verify a list of attachments is displayed.<br>3. Verify a &quot;New Attachment&quot; badge appears if a new file was added.<br>4. Open the Check Attachment button to clear the badge.<br>5. Search for a specific file name.<br>6. Click the &quot;Check Notes&quot; button.<br>7. Verify notes are displayed.<br>8. Verify &quot;New Notes&quot; badge appears for new notes.<br>9. Open the Check Notes button to clear the badge.<br>10. Filter notes by date.</td><td class="s8"></td><td class="s5">1. List of attachments should be displayed.<br>2. New attachment badge should appear only if a new file was uploaded.<br>3. Badge should clear when the button is opened.<br>4. Search should filter correctly.<br>5. Notes should be displayed properly.<br>6. New Notes badge should function as expected.<br>7. Date filtering should work as intended.</td><td class="s10"></td><td class="s13">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-978">CITYLANDPRS-978</a></td></tr><tr style="height: 19px"><th id="2024762676R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">PRS-027-003</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Uploading Attachments</td><td class="s7">High</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Upload a file.<br>2. Verify the maximum file size does not exceed 25MB.<br>3. Verify file type is within allowed formats (PDF, Doc, JPG, JPEG, PNG, CSV, Excel).<br>4. Click the Submit button.</td><td class="s8"></td><td class="s5">1.  Should validate file size and format.<br>2. Files should be successfully uploaded upon submission.</td><td class="s10"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="2024762676R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">PRS-027-004</td><td class="s6">Recording the Delivered Item after return</td><td class="s15">Verify Uploading Attachments - Negative Scenario</td><td class="s7">High</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Upload a different file type<br>2. Upload a file more than 25MB</td><td class="s8"></td><td class="s5">1. Should not be able to upload different file type<br>2. Should not be able to upload file size greater than 25MB</td><td class="s10"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">PRS-027-005</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Adding Notes</td><td class="s7">High</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Enter a note with alphanumeric and special characters (except emojis).<br>2. Verify character limit (max 100 characters).<br>3. Click Submit.</td><td class="s8"></td><td class="s5">1. Notes should be saved if criteria are met.<br>2. Character limit validation should work properly.</td><td class="s10"></td><td class="s13">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-979">CITYLANDPRS-979</a></td></tr><tr style="height: 19px"><th id="2024762676R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">PRS-027-006</td><td class="s6">Recording the Delivered Item after return</td><td class="s15">Verify Adding Notes - Negative Scenario</td><td class="s7">High</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Enter a note with emoji<br>2. Verify character max limit by inputting more than 100 characters</td><td class="s8"></td><td class="s5">1. Should not be able to save emoji input<br>2. Should not be able to input more than 100 characters</td><td class="s10"></td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-979">CITYLANDPRS-979</a></td></tr><tr style="height: 19px"><th id="2024762676R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">PRS-027-007</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Viewing and Sorting Items Table</td><td class="s7">Minor</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Search for an item by name.<br>2. Verify the default display of 10 rows per page.<br>3. Sort items by various fields (Item Name, Quantity Ordered, Quantity Delivered, Unit, Date Delivered, Delivery Status, Returns).</td><td class="s8"></td><td class="s5">1. Search should return correct results.<br>2. Pagination should display 10 rows per page.<br>3. Sorting should work correctly based on selected criteria.</td><td class="s10"></td><td class="s13">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-981">CITYLANDPRS-981</a></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s18">PRS-027-008</td><td class="s18">Recording the Delivered Item after return</td><td class="s18">Verify Editing the Item</td><td class="s19">Critical</td><td class="s18">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s18">1. Click the Edit icon from the Actions column or Edit button from Item Details.<br>2. Verify Edit Modal opens.<br>3. Verify non-editable fields (Item, Quantity Ordered).<br>4. Edit the Date Delivered (should not be future date).<br>5. Edit Quantity Delivered (within remaining undelivered quantity, max 3 characters, numbers only).<br>6. Edit Quantity for Return (within remaining undelivered quantity and quantity delivered, max 3 characters, numbers only).<br>7. Edit Notes (alphanumeric and special characters except emojis, max 100 characters).<br>8. Click Save and verify confirmation modal appears.<br>9. Confirm and verify changes are saved correctly.<br>10. Verify Item History is updated with the changes.</td><td class="s20"></td><td class="s18">1. Edit Modal should display correct fields and validations should apply.<br>2. Changes should be saved and reflected in the system.<br>3. Item History should log the updates.</td><td class="s13">Failed</td><td class="s13">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">[Delivery Receipt] Qty for Return and Qty Delivered exceeding 3 characters input</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-964">02/28 : https://youtrack.stratpoint.com/issue/CITYLANDPRS-964<br><br><br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-961<br><br><br><br></a></td></tr><tr style="height: 19px"><th id="2024762676R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5">PRS-027-009</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Canceling Edits</td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Click Edit icon or Edit button.<br>2. Make changes.<br>3. Click Cancel.<br>4. Confirm cancellation in the modal.</td><td class="s8"></td><td class="s5">1. Changes should not be saved upon cancelation.</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 113px"><th id="2024762676R11" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">12</div></th><td class="s5">PRS-027-010</td><td class="s6">Recording the Delivered Item after return</td><td class="s5">Verify Updating Item Status</td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned<br>3. Items has been redelivered by the Supplier</td><td class="s5">1. Edit an item and modify delivery details.<br>2. Save changes and confirm.<br>3. Verify status updates based on:<br>   - Quantity Delivered and Quantity for Return -&gt; Partially Delivered w/ Return.<br>   - Only Quantity for Return -&gt; Returned.<br></td><td class="s8"></td><td class="s5">1. Status should update correctly based on delivery details.</td><td class="s13">Failed</td><td class="s13">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Mar 06 : [Returning an Item] The &quot;Returned&quot; Status on the Item Return column is not displayed. Fully Delivered with Return is not displayed on Item History<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1060</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-967">https://youtrack.stratpoint.com/issue/CITYLANDPRS-967</a></td></tr><tr style="height: 19px"><th id="2024762676R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">PRS-027-011</td><td class="s22">Cancelling of Request to Return Item</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(Delivery Receipt is Draft or to be submitted)<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Redirection to Delivery Receipt Form </span></td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s5">1. Click RS Number in the Dashboard.<br>2. Click the Select Actions button.<br>3. Click &quot;Record Delivery Receipt.&quot;<br></td><td class="s8"></td><td class="s5">1. RS Details page is displayed.<br>2. Options are displayed, including &quot;Record Delivery Receipt.&quot;<br>3. Delivery Receipt Form is displayed.<br>4. Sections for Purchase Details and Items are available.</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5">PRS-027-012</td><td class="s22">Cancelling of Request to Return Item</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(Delivery Receipt is Draft or to be submitted)</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br><br>Verify Edit Item Details</span></td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s5">1. Click the Edit button under the Items section.</td><td class="s8"></td><td class="s5">1. Item Details Modal is displayed.<br>2. &quot;Quantity for Return&quot; field is present.<br>3. Field accepts numbers only, with a maximum of 3 characters.<br>4. Validation ensures that &quot;Quantity for Return&quot; and &quot;Quantity Delivered&quot; do not exceed &quot;Quantity Ordered&quot;.</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s18">PRS-027-013</td><td class="s18">Cancelling of Request to Return Item</td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(Delivery Receipt is Draft or to be submitted)</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br><br>Verify Confirm Delivery</span></td><td class="s19">Critical</td><td class="s18">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s18">1. Click the Save button to confirm delivery.</td><td class="s20"></td><td class="s18">1. &quot;Date Delivered&quot; and &quot;Quantity Delivered&quot; fields are updated.<br><br>2. Status updates based on Quantity Delivered and Quantity for Return.<br>   - If both have values: Status = &quot;Partially Delivered w/ Return.&quot;<br>   - If only Quantity for Return has a value: Status = &quot;Returned.&quot;<br><br>3. Returns Column displays:<br>   - &quot;No Returns&quot; if no value for Quantity Returned.<br>   - &quot;Returned&quot; if all Quantity Returned has been redelivered.<br>   - &quot;Cancel Returns&quot; button if Quantity Returned has a value and has active returns.</td><td class="s13">Failed</td><td class="s16">Blocked</td><td class="s10">same issue in dev env.</td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Mar 06 : [Returning an Item] The &quot;Returned&quot; Status on the Item Return column is not displayed. Fully Delivered with Return is not displayed on Item History<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1060</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-967">https://youtrack.stratpoint.com/issue/CITYLANDPRS-967</a></td></tr><tr style="height: 19px"><th id="2024762676R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">PRS-027-014</td><td class="s22">Cancelling of Request to Return Item</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(Delivery Receipt is Draft or to be submitted)<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Cancel Returns from Returns Column</span></td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s5">1. Click &quot;Cancel Returns&quot; button in the Returns column.<br>2. Confirm cancellation in the confirmation modal.</td><td class="s8"></td><td class="s5">1. &quot;Quantity for Return&quot; value is cleared.<br>2. Status updates to &quot;No Returns.&quot;</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">PRS-027-015</td><td class="s22">Cancelling of Request to Return Item</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(DELIVERY RECEIPT SUBMITTED)<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Redirection to Delivery Receipt</span></td><td class="s7">Critical</td><td class="s5">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s5">1. Click an RS Number from the Dashboard.<br>2. Verify RS Details are displayed.<br>3. Click the Related Documents tab.<br>4. Click the Deliveries tab.<br>5. Click a DR Number to view the Delivery Receipt.</td><td class="s8"></td><td class="s5">1. RS Details should be displayed correctly.<br>2. Related Documents tab should open without errors.<br>3. Deliveries tab should be accessible.<br>4. DR Number should be clickable and display Delivery Receipt details.</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s18">PRS-027-016</td><td class="s18">Cancelling of Request to Return Item</td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(DELIVERY RECEIPT SUBMITTED)<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Cancel Returns from Returns Column</span></td><td class="s19">Critical</td><td class="s18">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s18">1. Click &quot;Cancel Returns&quot; button in the Returns column.<br>2. Confirm cancellation in the confirmation modal.</td><td class="s20"></td><td class="s18">1. &quot;Quantity for Return&quot; value is cleared.<br>2. Status retains &quot;w/ Return.&quot;</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s18">PRS-027-017</td><td class="s18">Cancelling of Request to Return Item</td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">(DELIVERY RECEIPT SUBMITTED)<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Cancel Returns from Item Details Modal</span></td><td class="s19">Critical</td><td class="s18">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s18">1. Open the View Item Details Modal.<br>2. Click &quot;Cancel Returns.&quot;<br>3. Confirm cancellation in the confirmation modal.</td><td class="s20"></td><td class="s18">1. &quot;Quantity for Return&quot; value is cleared.<br>2. Status retains &quot;w/ Return.&quot;</td><td class="s13">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">[Delivery Receipt] Item Details is missing the cancel request button</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-970">https://youtrack.stratpoint.com/issue/CITYLANDPRS-970</a></td></tr><tr style="height: 19px"><th id="2024762676R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s18">PRS-027-018</td><td class="s18">Cancelling of Request to Return Item</td><td class="s18">Verify Item History after Cancelling Returns</td><td class="s19">Critical</td><td class="s18">1. Requisition Slip has been created<br>2. Items was returned</td><td class="s18">1. Click &quot;Cancel Returns&quot; button in the Returns column.<br>2. Confirm cancellation in the confirmation modal.</td><td class="s20"></td><td class="s18">1. A new row is created for the item with &quot;Cancelled Return&quot; status.<br>2. Columns should have values:<br> - &quot;Quantity Ordered&quot;<br> - &quot;Quantity Returned&quot;<br> - &quot;Status&quot;<br><br>3. Columns should have &quot;---&quot; value:<br> - &quot;Quantity Delivered&quot;<br> - &quot;Date of Delivery&quot;</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s18">PRS-027-019</td><td class="s20">Returning an Item</td><td class="s18">Verify entry points for delivery reciept</td><td class="s20">Critical</td><td class="s18">1. Requisition Slip has been created<br>2. Items has been delivered by the Supplier<br>3. logged in as requestor</td><td class="s18">1. Click RS Number in Dashboard<br>2. Click Select Actions Button<br>3. Click Record Delivery Receipt</td><td class="s18"></td><td class="s18">1. user should see the ff sections:<br>a. blank DR Number and the RS Number<br>b.  Drop-down of Purchase Order Number<br>c. Supplier<br>d. Button for invoice<br>e. attachements and notes<br>f. save button<br>g. Items table with the following columns:<br> - item<br> - Qty Ordered<br> - Qty Delivered<br> - Unit<br> - Date Delivered<br> - Delivery Status<br> - Notes <br> - Returns<br> - Actions<br></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">PRS-027-020</td><td class="s23">Returning an Item</td><td class="s24">Verify the drop-down of Purchase Order Number displays the correct values        </td><td class="s8">Critical</td><td class="s8"></td><td class="s5">1. click the purchase order number dropdown<br>2. validate the details</td><td class="s5"></td><td class="s5">1. Drop-down should display all Purchase Order Numbers produced in the Requisition Slip.        <br>2. should be sorted in alphabetical order</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">PRS-027-021</td><td class="s23">Returning an Item</td><td class="s24">Verify Purchase Order Numbers with no Delivery Receipt are displayed        </td><td class="s8">Critical</td><td class="s5">1. At least one Purchase Order exists without Delivery Receipt</td><td class="s5">1. Ensure at least one Purchase Order in the system has no associated Delivery Receipt.<br>2. click the Purchase Order drop-down.        </td><td class="s5"></td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. The Purchase Order Numbers without associated Delivery Receipts </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">should be visible in the drop-down.        </span></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">PRS-027-022</td><td class="s23">Returning an Item</td><td class="s24">Verify Purchase Order Numbers with Delivery Receipts are not displayed        </td><td class="s8">Critical</td><td class="s5">1. Requisition Slip includes Purchase Order with Delivery Receipt</td><td class="s5">1. Ensure a Purchase Order exists with an associated Delivery Receipt<br>2. Open the Purchase Order drop-down.	</td><td class="s5"></td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. The Purchase Order Number with an associated Delivery Receipt </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">should NOT be displayed in the drop-down</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">.        </span></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">PRS-027-023</td><td class="s23">Returning an Item</td><td class="s5">Verfiy Supplier text field</td><td class="s8">Critical</td><td class="s8"></td><td class="s5">1. validate the supplier text field content</td><td class="s5"></td><td class="s5">1. Supplier should be the same indicated in the Purchase Order and the field should not be editable</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">PRS-027-024</td><td class="s23">Returning an Item</td><td class="s5">Verfiy Invoice button</td><td class="s8">Critical</td><td class="s8"></td><td class="s5">1. validate if invoice button is visible</td><td class="s5"></td><td class="s5">1. Invoice button should be visible</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">PRS-027-025</td><td class="s23">Returning an Item</td><td class="s24">Verify that files exceeding 25MB cannot be uploaded.</td><td class="s8">High</td><td class="s8"></td><td class="s5">1. Attempt to upload a file larger than 25MB.<br>2. Click Submit.</td><td class="s5"></td><td class="s5">1. An error message is displayed indicating the file size exceeds the maximum limit.</td><td class="s25"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">PRS-027-026</td><td class="s23">Returning an Item</td><td class="s24">Verify that only specified file types (PDF, Doc, JPG, JPEG, PNG, CSV, Excel) can be uploaded.</td><td class="s8">High</td><td class="s8"></td><td class="s5">1. Attempt to upload unsupported file types (e.g., .exe).<br>2. Verify the system’s response.</td><td class="s5"></td><td class="s5">1. An error message is displayed for unsupported file types.</td><td class="s25"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">for macbook - before mag uplaod ng file attachment nakadisable ung na unsupported file</td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s5">PRS-027-027</td><td class="s23">Returning an Item</td><td class="s5">Verify file attachment after hitting submit button</td><td class="s8">High</td><td class="s8"></td><td class="s5">1. Attempt to upload file types <br>2. fill out all the fields<br>3. click submit</td><td class="s5"></td><td class="s5">1. Attachement should be saved and can be viewed in the Check Attachement button</td><td class="s25"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s5">PRS-027-028</td><td class="s23">Returning an Item</td><td class="s5" rowspan="3">Verify adding of notes</td><td class="s8">High</td><td class="s8"></td><td class="s5" rowspan="3">1. Navigate to the Attachments and Notes section.<br>2. Fill out notes<br>3. click submit</td><td class="s5"></td><td class="s5">1. Should Alphanumeric and Special Characters except Emojis<br>             </td><td class="s25"></td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s14" rowspan="3"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-979">CITYLANDPRS-979</a></td></tr><tr style="height: 19px"><th id="2024762676R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5">PRS-027-029</td><td class="s23">Returning an Item</td><td class="s8">High</td><td class="s8"></td><td class="s5"></td><td class="s5">2. Should allow maximum of 100 Characters</td><td class="s25"></td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5">PRS-027-030</td><td class="s23">Returning an Item</td><td class="s8">High</td><td class="s8"></td><td class="s5"></td><td class="s5">1. Noted should be saved and can be viewed in the Notes button</td><td class="s25"></td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s18">PRS-027-031</td><td class="s20">Returning an Item</td><td class="s18">Verify Item Details Modal in edit modal on Items section</td><td class="s20">Critical</td><td class="s20"></td><td class="s18">1. Navigate to the Items section<br>2. click Edit on one of the items<br>3. validate the iterms details modal</td><td class="s18"></td><td class="s18">1. Items modal should appear with the ff sections:<br>a. Items<br>b. Date delivered<br>c. Qty ordered<br>d. Qty delivered<br>e. Qty of return<br>f. Notes<br>g. Delivery Status<br>h. Returns (status)<br>i. Actions</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s18">PRS-027-032</td><td class="s20">Returning an Item</td><td class="s18">Verift Qty of returned field - max characters</td><td class="s20">Critical</td><td class="s20"></td><td class="s18">1. Navigate to the Items section<br>2. click Edit on one of the items<br>3. validate the qty of returned field</td><td class="s18"></td><td class="s18">1. Should be Numbers Only, with Maximum of 3 Characters</td><td class="s13">Failed</td><td class="s13">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">[Delivery Receipt] Qty for Return exceeding 3 characters input</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-961">https://youtrack.stratpoint.com/issue/CITYLANDPRS-961</a></td></tr><tr style="height: 19px"><th id="2024762676R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s5">PRS-027-033</td><td class="s23">Returning an Item</td><td class="s5">Verify the maximum quantity check for return when combining Quantity for Return &amp; Quantity Delivered        </td><td class="s8">Critical</td><td class="s8"></td><td class="s5">1. Enter Quantity for Return and Quantity Delivered values.<br>2. Check the maximum value.        </td><td class="s5"></td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. The combined</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> Quantity for Return and Quantity Delivered should not exceed Quantity Ordered. </span></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s5">PRS-027-034</td><td class="s23">Returning an Item</td><td class="s5">Verify clicking Save Button updates delivery information        </td><td class="s8">Critical</td><td class="s8"></td><td class="s5">1. click Save button</td><td class="s5"></td><td class="s5">1. Date Delivered, Quantity Delivered, and Status fields should be updated based on the conditions.        </td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s18">PRS-027-035</td><td class="s20">Returning an Item</td><td class="s26">Verify status of Partially Delivered w/ Return        </td><td class="s20">Critical</td><td class="s20"></td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Enter values for Quantity Delivered and Quantity for Return.<br> 2. Click the Save Button.        <br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-style:italic;color:#000000;">(Quantity Delivered and Quantity for Return has Values)</span></td><td class="s18"></td><td class="s18">1. Status should be &quot;Partially Delivered w/ Return&quot;.</td><td class="s27"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s18">PRS-027-036</td><td class="s20">Returning an Item</td><td class="s18">Verify status of Returned when only Quantity for Return has value        </td><td class="s20">Critical</td><td class="s20"></td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Enter value for Quantity for Return and leave Quantity Delivered empty.<br> 2. Click the Save Button.        <br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-style:italic;color:#000000;">(If only the Quantity for Return has Value)</span></td><td class="s18"></td><td class="s18">1. Status should be &quot;Returned&quot;.        </td><td class="s13">Failed</td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10">&quot;Mar 06 : [Returning an Item] The &quot;&quot;Returned&quot;&quot; Status on the Item Return column is not displayed. Fully Delivered with Return is not displayed on Item History<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1060&quot;</td><td class="s5">[Delivery Receipt] Unable to Submit Delivery Receipt when Item Qty Delivered is set to 0</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-967">https://youtrack.stratpoint.com/issue/CITYLANDPRS-967</a></td></tr><tr style="height: 19px"><th id="2024762676R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s18">PRS-027-037</td><td class="s20">Returning an Item</td><td class="s18">Verify behavior when there are No Returns (Quantity Returned = 0)        </td><td class="s20">Critical</td><td class="s20"></td><td class="s18">1. Navigate to Item details modal.<br>2. Set Quantity Returned to 0.<br>3. Click Save Button.<br>4. validate the status in the Returns column </td><td class="s18"></td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Status should indicate </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;No Returns&quot;</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">. <br>2. No entry should be created in the Item History related to this returned item.</span></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2024762676R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s18">PRS-027-038</td><td class="s20">Returning an Item</td><td class="s28">Verify behavior when all of the Quantity for Returned has been redelivered        </td><td class="s20">Critical</td><td class="s20"></td><td class="s18">1. Set Quantity Returned equal to Quantity Delivered<br>2. Click Save Button.        <br>3. validate the status in the Returns column </td><td class="s18"></td><td class="s18"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Status should indicate </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">&quot;Returned&quot;.<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. An entry for the returned item should be created in the Item History.</span></td><td class="s27"></td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s5">Mar 06 : [Returning an Item] The &quot;Returned&quot; Status on the Item Return column is not displayed. Fully Delivered with Return is not displayed on Item History<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1060</td><td class="s8"></td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-967">https://youtrack.stratpoint.com/issue/CITYLANDPRS-967</a></td></tr><tr style="height: 19px"><th id="2024762676R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s18">PRS-027-039</td><td class="s20">Returning an Item</td><td class="s26">Verify behavior when there is an active return and a Cancel Returns button is available        </td><td class="s20">Critical</td><td class="s18"> Quantity Returned &gt; 0</td><td class="s18">1. Set a positive value for Quantity Returned<br> 2. Ensure there is an active return scenario<br> 3. Check the Cancel Returns Button.        </td><td class="s18"></td><td class="s18">1. Cancel Returns button should be visible, allowing the user to cancel the return.	</td><td class="s27"></td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="2024762676R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s18">PRS-027-040</td><td class="s20">Returning an Item</td><td class="s26">Verify that the Returned Item is added as an Entry in the Item History        </td><td class="s20">Critical</td><td class="s20"></td><td class="s18">1. Complete a return process<br> 2. Go to Item History.	</td><td class="s18"></td><td class="s18">1. The returned item should be listed in the Item History with appropriate details reflecting its return status.	</td><td class="s27"></td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s5">Mar 06 : [Returning an Item] The &quot;Returned&quot; Status on the Item Return column is not displayed. Fully Delivered with Return is not displayed on Item History<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1060</td><td class="s8"></td><td class="s29"></td></tr><tr style="height: 125px"><th id="2024762676R42" style="height: 125px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 125px">43</div></th><td class="s5">PRS-027-041</td><td class="s23">Returning an Item</td><td class="s5">Verify that clicking on the Item Name displays the Value of the Quantity for Return        </td><td class="s8">Critical</td><td class="s8"></td><td class="s5">1. Go to the Item History.<br> 2. Click on the Item Name of the returned item.	</td><td class="s5"></td><td class="s5">1. The Quantity for Return should be displayed correctly when the Item Name is clicked.        </td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s8"></td><td class="s8"></td></tr></tbody></table></div>