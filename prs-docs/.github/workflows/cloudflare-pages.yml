name: Deploy to Cloudflare Pages

on:
  push:
    branches:
      - main  # or master, depending on your default branch name
  pull_request:
    branches:
      - main  # or master, depending on your default branch name

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Build
        run: mkdocs build
        
      - name: Publish to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: prs-docs
          directory: site
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
