# Comprehensive Frontend Improvement Guide for PRS Project

## Table of Contents
1. [Introduction](#introduction)
2. [Current Architecture Overview](#current-architecture-overview)
3. [Key Issues Identified](#key-issues-identified)
4. [Improvement Recommendations](#improvement-recommendations)
5. [Security Recommendations](#security-recommendations)
6. [Implementation Roadmap](#implementation-roadmap)
7. [Code Examples](#code-examples)
8. [Best Practices](#best-practices)

## Introduction

This guide provides a comprehensive plan for improving the PRS frontend codebase based on the code analysis. The recommendations focus on enhancing maintainability, performance, and developer experience while addressing specific issues identified in the current implementation.

## Current Architecture Overview

The PRS frontend is built with the following technologies:

- **React**: Core UI library
- **Vite**: Build tool and development server
- **React Router**: Client-side routing
- **Zustand**: State management
- **React Query (TanStack Query)**: Data fetching and caching
- **Axios**: HTTP client
- **Zod**: Schema validation
- **Tailwind CSS**: Utility-first CSS framework

The project follows a feature-based architecture with the following directory structure:

```
prs-frontend/
├── src/
│   ├── app/               # Application core
│   ├── assets/            # Static assets
│   ├── components/        # Shared UI components
│   ├── config/            # Configuration files
│   ├── contexts/          # React contexts
│   ├── features/          # Feature modules
│   ├── helpers/           # Helper functions
│   ├── hooks/             # Custom hooks
│   ├── lib/               # Library code
│   ├── schema/            # Validation schemas
│   └── store/             # State management
```

## Key Issues Identified

Based on the code analysis, we've identified several areas for improvement:

### 1. Component Structure Issues

- **Large, Complex Components**: Many components have too many responsibilities, making them difficult to maintain and test.
- **Prop Drilling**: Excessive passing of props through multiple component levels.
- **Inconsistent Component Patterns**: Lack of standardized approach to component creation.
- **Limited Component Documentation**: Insufficient documentation for component usage and props.

### 2. State Management Issues

- **Store Fragmentation**: Too many small, disconnected Zustand stores.
- **Inconsistent Store Design**: Varying patterns for store implementation.
- **Mixed State Management Approaches**: Inconsistent use of Zustand, React Query, and local state.
- **Lack of Store Documentation**: Insufficient documentation for store usage and purpose.

### 3. Performance Issues

- **Unnecessary Re-renders**: Components re-rendering when they don't need to.
- **Large Bundle Size**: Insufficient code splitting and lazy loading.
- **Inefficient Data Fetching**: Redundant API calls and inadequate caching.
- **Heavy Components**: Components with excessive logic and rendering complexity.

### 4. Code Organization Issues

- **Inconsistent Feature Organization**: Some features are not fully encapsulated.
- **Duplicate Code**: Similar functionality implemented multiple times.
- **Unclear Boundaries**: Blurred lines between feature modules.
- **Inconsistent File Naming**: Varying conventions for file naming.

### 5. Security Issues

- **Missing Content Security Policy**: No CSP implementation to prevent XSS attacks.
- **Insecure Authentication Storage**: Authentication tokens stored in localStorage, which is vulnerable to XSS attacks.
- **Lack of CSRF Protection**: No protection against Cross-Site Request Forgery attacks.
- **Inconsistent Input Validation**: Some forms lack proper validation and sanitization.
- **Insufficient Security Headers**: Missing important security headers for browser protection.

## Improvement Recommendations

### 1. Component Structure Improvements

#### 1.1 Implement Atomic Design Methodology

Reorganize components following the Atomic Design methodology:

```
components/
├── atoms/         # Basic building blocks (Button, Input, etc.)
├── molecules/     # Combinations of atoms (Form fields, search bars, etc.)
├── organisms/     # Complex UI sections (Forms, tables, etc.)
├── templates/     # Page layouts
└── pages/         # Complete pages
```

**Current Example**:
```jsx
// Large, monolithic component
const RSMainTab = () => {
  // Lots of state
  const [formData, setFormData] = useState({});
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  // ... many more state variables

  // Many event handlers
  const handleSubmit = () => { /* ... */ };
  const handleAddItem = () => { /* ... */ };
  const handleRemoveItem = () => { /* ... */ };
  // ... many more handlers

  // Complex JSX with many conditionals
  return (
    <div>
      {/* Hundreds of lines of JSX */}
    </div>
  );
};
```

**Improved Example**:
```jsx
// Decomposed into smaller components
const RSMainTab = () => {
  return (
    <div>
      <RSHeader />
      <RSForm onSubmit={handleSubmit} />
      <RSItemsSection />
      <RSActions />
    </div>
  );
};

// Each sub-component handles its own concerns
const RSForm = ({ onSubmit }) => {
  const [formData, setFormData] = useState({});
  // Form-specific logic
  return (
    <form onSubmit={onSubmit}>
      {/* Form fields */}
    </form>
  );
};
```

#### 1.2 Standardize Component Structure

Adopt a consistent pattern for all components:

```jsx
// Standard component structure
export const ComponentName = ({ prop1, prop2, ...props }) => {
  // 1. Hooks (useState, useEffect, custom hooks)
  const [state, setState] = useState(initialState);
  const { data } = useQuery(...);

  // 2. Derived state and memoization
  const derivedValue = useMemo(() => {
    // Compute value based on props and state
  }, [dependencies]);

  // 3. Event handlers
  const handleEvent = useCallback(() => {
    // Handle event
  }, [dependencies]);

  // 4. Render helpers
  const renderItem = (item) => (
    <div key={item.id}>{item.name}</div>
  );

  // 5. Return JSX
  return (
    <div className="component-name">
      {/* Component JSX */}
    </div>
  );
};

// 6. PropTypes
ComponentName.propTypes = {
  prop1: PropTypes.string.isRequired,
  prop2: PropTypes.number,
};
```

#### 1.3 Implement Component Documentation

Add comprehensive documentation for all components:

```jsx
/**
 * Button component
 *
 * @component
 * @example
 * <Button variant="primary" onClick={handleClick}>
 *   Click me
 * </Button>
 *
 * @param {Object} props - Component props
 * @param {string} [props.variant="primary"] - Button variant (primary, secondary, outline)
 * @param {function} props.onClick - Click handler
 * @param {ReactNode} props.children - Button content
 * @param {boolean} [props.isLoading=false] - Loading state
 * @param {string} [props.className] - Additional CSS classes
 */
export const Button = ({ variant = "primary", onClick, children, isLoading, className }) => {
  // Component implementation
};
```

### 2. State Management Improvements

#### 2.1 Consolidate Zustand Stores

Reduce the number of stores by grouping related state:

**Current Example**:
```javascript
// Multiple small stores
const useUserStore = create(/* ... */);
const usePermissionStore = create(/* ... */);
const useTokenStore = create(/* ... */);
```

**Improved Example**:
```javascript
// Consolidated store
const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      permissions: null,
      token: null,
      refreshToken: null,

      // Actions
      setUser: (user) => set({ user }),
      setPermissions: (permissions) => set({ permissions }),
      setToken: (token) => set({ token }),
      setRefreshToken: (refreshToken) => set({ refreshToken }),

      // Complex actions
      login: async (credentials) => {
        // Login logic
      },
      logout: () => set({ user: null, permissions: null, token: null, refreshToken: null }),
    }),
    {
      name: 'auth-storage',
    }
  )
);
```

#### 2.2 Standardize Store Design

Adopt a consistent pattern for all stores:

```javascript
/**
 * Example store with standardized structure
 */
const useExampleStore = create(
  persist(
    (set, get) => ({
      // 1. State
      items: [],
      isLoading: false,
      error: null,

      // 2. Computed state (getters)
      get itemCount() {
        return get().items.length;
      },

      // 3. Simple actions
      setItems: (items) => set({ items }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),

      // 4. Complex actions
      fetchItems: async () => {
        set({ isLoading: true, error: null });
        try {
          const items = await api.getItems();
          set({ items, isLoading: false });
        } catch (error) {
          set({ error, isLoading: false });
        }
      },

      // 5. Reset actions
      reset: () => set({ items: [], isLoading: false, error: null }),
    }),
    {
      name: 'example-storage',
    }
  )
);
```

#### 2.3 Implement Store Documentation

Add comprehensive documentation for all stores:

```javascript
/**
 * Authentication store
 *
 * Manages authentication-related state, including tokens, user information, and permissions.
 *
 * @example
 * const { user, token, login, logout } = useAuthStore();
 *
 * // Login
 * login({ username, password });
 *
 * // Access user info
 * console.log(user.name);
 *
 * // Logout
 * logout();
 *
 * @property {Object|null} user - The authenticated user information
 * @property {string|null} token - The access token for API requests
 * @property {Object|null} permissions - The user's permissions
 * @property {string|null} refreshToken - The refresh token for obtaining new access tokens
 *
 * @method setUser - Sets the user information
 * @method setToken - Sets the access token
 * @method setPermissions - Sets the user's permissions
 * @method login - Logs in the user with the provided credentials
 * @method logout - Logs out the user and clears all auth state
 */
const useAuthStore = create(/* ... */);
```

### 3. Performance Improvements

#### 3.1 Implement Memoization

Use React.memo, useMemo, and useCallback to prevent unnecessary re-renders:

**Current Example**:
```jsx
// Component that re-renders unnecessarily
const ItemList = ({ items, onItemClick }) => {
  // This function is recreated on every render
  const handleItemClick = (item) => {
    onItemClick(item);
  };

  return (
    <div>
      {items.map(item => (
        <div key={item.id} onClick={() => handleItemClick(item)}>
          {item.name}
        </div>
      ))}
    </div>
  );
};
```

**Improved Example**:
```jsx
// Memoized component with stable callbacks
const ItemList = React.memo(({ items, onItemClick }) => {
  // This function is stable across renders
  const handleItemClick = useCallback((item) => {
    onItemClick(item);
  }, [onItemClick]);

  return (
    <div>
      {items.map(item => (
        <Item
          key={item.id}
          item={item}
          onClick={handleItemClick}
        />
      ))}
    </div>
  );
});

// Memoized child component
const Item = React.memo(({ item, onClick }) => (
  <div onClick={() => onClick(item)}>
    {item.name}
  </div>
));
```

#### 3.2 Optimize React Query Usage

Improve data fetching with proper query keys and caching strategies:

**Current Example**:
```jsx
// Inefficient query usage
const { data, isLoading } = useQuery(['items'], fetchItems);

// Separate query with duplicate data
const { data: itemDetails } = useQuery(['itemDetails', itemId], () =>
  fetchItemDetails(itemId)
);
```

**Improved Example**:
```jsx
// Efficient query with proper key structure
const { data, isLoading } = useQuery({
  queryKey: ['items', { filters, page, limit }],
  queryFn: () => fetchItems({ filters, page, limit }),
  staleTime: 5 * 60 * 1000, // 5 minutes
});

// Query that uses existing data when possible
const { data: itemDetails } = useQuery({
  queryKey: ['items', itemId],
  queryFn: () => fetchItemDetails(itemId),
  initialData: () => {
    // Use data from the items query if available
    const items = queryClient.getQueryData(['items']);
    if (items) {
      return items.find(item => item.id === itemId);
    }
  },
});
```

#### 3.3 Implement Code Splitting

Use dynamic imports and React.lazy for code splitting:

**Current Example**:
```jsx
// All routes imported eagerly
import { Dashboard } from './pages/Dashboard';
import { RequisitionForm } from './pages/RequisitionForm';
import { PurchaseOrders } from './pages/PurchaseOrders';

const routes = [
  { path: '/dashboard', element: <Dashboard /> },
  { path: '/requisition/new', element: <RequisitionForm /> },
  { path: '/purchase-orders', element: <PurchaseOrders /> },
];
```

**Improved Example**:
```jsx
// Routes loaded lazily
import { Suspense, lazy } from 'react';
import { Spinner } from './components/ui/Spinner';

const Dashboard = lazy(() => import('./pages/Dashboard'));
const RequisitionForm = lazy(() => import('./pages/RequisitionForm'));
const PurchaseOrders = lazy(() => import('./pages/PurchaseOrders'));

const LazyRoute = ({ component: Component }) => (
  <Suspense fallback={<Spinner />}>
    <Component />
  </Suspense>
);

const routes = [
  { path: '/dashboard', element: <LazyRoute component={Dashboard} /> },
  { path: '/requisition/new', element: <LazyRoute component={RequisitionForm} /> },
  { path: '/purchase-orders', element: <LazyRoute component={PurchaseOrders} /> },
];
```

### 4. Code Organization Improvements

#### 4.1 Enforce Feature Encapsulation

Ensure all feature-related code is contained within the feature directory:

```
features/requisition/
├── api/              # API calls related to requisitions
├── components/       # Requisition-specific components
├── hooks/            # Requisition-specific hooks
├── utils/            # Requisition-specific utilities
└── index.js          # Public API for the feature
```

**Current Example**:
```javascript
// Feature-specific code scattered across the codebase
// In src/api/requisitionApi.js
export const fetchRequisitions = () => { /* ... */ };

// In src/components/RequisitionList.jsx
export const RequisitionList = () => { /* ... */ };

// In src/hooks/useRequisition.js
export const useRequisition = () => { /* ... */ };
```

**Improved Example**:
```javascript
// All feature code encapsulated
// In src/features/requisition/api/index.js
export const fetchRequisitions = () => { /* ... */ };

// In src/features/requisition/components/RequisitionList.jsx
export const RequisitionList = () => { /* ... */ };

// In src/features/requisition/hooks/useRequisition.js
export const useRequisition = () => { /* ... */ };

// In src/features/requisition/index.js (public API)
export { RequisitionList } from './components/RequisitionList';
export { useRequisition } from './hooks/useRequisition';
export * from './api';
```

#### 4.2 Create Shared Utilities

Extract common functionality into shared utilities:

**Current Example**:
```javascript
// Duplicate formatting logic in multiple components
// In RequisitionDetail.jsx
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US');
};

// In PurchaseOrderDetail.jsx
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US');
};
```

**Improved Example**:
```javascript
// In src/utils/formatters.js
export const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US');
};

// In RequisitionDetail.jsx
import { formatDate } from '@utils/formatters';

// In PurchaseOrderDetail.jsx
import { formatDate } from '@utils/formatters';
```

#### 4.3 Standardize File Naming

Adopt consistent naming conventions:

```
// Component files: PascalCase.jsx
Button.jsx
RequisitionForm.jsx

// Hook files: camelCase.js with 'use' prefix
useAuth.js
useRequisition.js

// Utility files: camelCase.js
formatters.js
validators.js

// API files: camelCase.js with 'api' suffix
requisitionApi.js
userApi.js
```

## Security Recommendations

Based on the security issues identified, we recommend the following improvements to enhance the security of the PRS frontend application:

### 1. Implement Content Security Policy

#### 1.1 Add CSP Headers

Implement a Content Security Policy to prevent XSS attacks and restrict resource loading to trusted sources:

```html
<!-- In public/index.html -->
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://api.example.com;">
```

For production, configure the CSP headers on the server side:

```javascript
// Example server configuration (Node.js/Express)
app.use((req, res, next) => {
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://api.example.com;"
  );
  next();
});
```

#### 1.2 Use Trusted Libraries and Sanitize User Input

Ensure all user-generated content is properly sanitized before rendering:

```jsx
// Current Example (Unsafe):
<div dangerouslySetInnerHTML={{ __html: userProvidedContent }} />

// Improved Example (Safe):
import DOMPurify from 'dompurify';

<div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(userProvidedContent) }} />
```

### 2. Implement CSRF Protection

#### 2.1 Configure API Client with CSRF Protection

Update the API client to include CSRF token handling:

```javascript
// In src/lib/apiClient.js
import axios from 'axios';
import { env } from '@config/env';

// Get CSRF token from cookie or header
const getCsrfToken = () => {
  const match = document.cookie.match(/XSRF-TOKEN=([^;]+)/);
  return match ? match[1] : '';
};

function authRequestInterceptor(config) {
  const { getState } = useTokenStore;

  const token = getState().token;
  if (!config.headers.has('Authorization') && token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Add CSRF token to non-GET requests
  if (['post', 'put', 'patch', 'delete'].includes(config.method.toLowerCase())) {
    config.headers['X-CSRF-Token'] = getCsrfToken();
  }

  config.headers.Accept = 'application/json';
  return config;
}

export const api = axios.create({
  baseURL: env.API_URL,
  withCredentials: true, // Important for CSRF cookies
});

api.interceptors.request.use(authRequestInterceptor);
```

#### 2.2 Ensure Backend Support

Coordinate with the backend team to ensure proper CSRF token generation and validation:

```javascript
// Example backend implementation (Node.js/Express with csurf)
const csrf = require('csurf');
const csrfProtection = csrf({ cookie: true });

app.use(csrfProtection);
app.get('/api/csrf-token', (req, res) => {
  res.json({ csrfToken: req.csrfToken() });
});
```

### 3. Secure Authentication Storage

#### 3.1 Use HttpOnly Cookies for Token Storage

Coordinate with the backend team to implement HttpOnly cookies for token storage instead of localStorage:

```javascript
// Updated auth.js
export const login = async (credentials) => {
  const response = await api.post('/v1/auth/login', credentials);

  // Token is set as HttpOnly cookie by the server
  // No need to store it in localStorage

  return response;
};

export const logout = async () => {
  await api.post('/v1/auth/logout');

  // Cookie is cleared by the server
  // No need to clear localStorage
};
```

#### 3.2 Update Token Store

Modify the token store to work with HttpOnly cookies:

```javascript
// In src/store/authStore.js
const useTokenStore = create(
  persist(
    (set) => ({
      // Store only non-sensitive user info
      isAuthenticated: false,
      userRole: null,

      setAuthenticated: (isAuthenticated, userRole = null) =>
        set({ isAuthenticated, userRole }),

      clearAuth: () =>
        set({ isAuthenticated: false, userRole: null }),
    }),
    {
      name: 'user-info-storage',
    },
  ),
);
```

### 4. Enhance Input Validation and Sanitization

#### 4.1 Standardize Form Validation

Ensure all forms use Zod schemas with proper validation:

```javascript
// Example Zod schema with security considerations
const userInputSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name must not exceed 100 characters')
    .refine(
      val => !emojiRegex.test(val),
      { message: 'Emojis are not allowed' }
    )
    .refine(
      val => alphanumericWithBasicSymbolsRegex.test(val),
      { message: 'Name can only contain alphanumeric characters and basic symbols' }
    ),
  email: z
    .string()
    .email('Invalid email format'),
  comment: z
    .string()
    .optional()
    .transform(val => DOMPurify.sanitize(val || '')),
});
```

#### 4.2 Implement Client-Side Sanitization

Add sanitization for user inputs before sending to the API:

```javascript
// In src/utils/sanitize.js
import DOMPurify from 'dompurify';

export const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    return DOMPurify.sanitize(input);
  }

  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }

  if (typeof input === 'object' && input !== null) {
    return Object.keys(input).reduce((acc, key) => {
      acc[key] = sanitizeInput(input[key]);
      return acc;
    }, {});
  }

  return input;
};

// Usage in API calls
const submitForm = async (data) => {
  const sanitizedData = sanitizeInput(data);
  return api.post('/endpoint', sanitizedData);
};
```

### 5. Implement Security Headers

#### 5.1 Configure Security Headers

Work with the DevOps team to ensure proper security headers are set on all responses:

```javascript
// Example server configuration (Node.js/Express with helmet)
const helmet = require('helmet');

app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:"],
        connectSrc: ["'self'", "https://api.example.com"],
      },
    },
    xssFilter: true,
    noSniff: true,
    referrerPolicy: { policy: 'same-origin' },
    hsts: {
      maxAge: 15552000, // 180 days
      includeSubDomains: true,
      preload: true,
    },
  })
);
```

#### 5.2 Add Security Headers Check

Implement a security headers check in the development environment:

```javascript
// In src/utils/securityCheck.js
export const checkSecurityHeaders = async () => {
  if (process.env.NODE_ENV !== 'production') {
    try {
      const response = await fetch('/');
      const headers = response.headers;

      const securityHeaders = [
        'Content-Security-Policy',
        'X-XSS-Protection',
        'X-Content-Type-Options',
        'Referrer-Policy',
        'Strict-Transport-Security',
      ];

      const missingHeaders = securityHeaders.filter(
        header => !headers.get(header)
      );

      if (missingHeaders.length > 0) {
        console.warn(
          'Security warning: The following security headers are missing:',
          missingHeaders
        );
      }
    } catch (error) {
      console.error('Failed to check security headers:', error);
    }
  }
};

// Call this function when the app initializes
```

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)

1. **Create Component Standards Document**
   - Define component structure guidelines
   - Create component documentation template
   - Establish naming conventions

2. **Create State Management Standards Document**
   - Define store structure guidelines
   - Create store documentation template
   - Establish state management patterns

3. **Set Up Linting and Formatting Rules**
   - Configure ESLint for component structure
   - Configure Prettier for code formatting
   - Add pre-commit hooks for code quality

4. **Create Security Standards Document**
   - Define security best practices
   - Create security review checklist
   - Establish security testing procedures

### Phase 2: Core Improvements (Weeks 3-4)

1. **Refactor Shared UI Components**
   - Reorganize using Atomic Design
   - Implement standardized structure
   - Add comprehensive documentation

2. **Consolidate Zustand Stores**
   - Combine related stores
   - Implement standardized structure
   - Add comprehensive documentation

3. **Optimize Performance Critical Paths**
   - Identify performance bottlenecks
   - Implement memoization
   - Optimize React Query usage

4. **Implement Core Security Improvements**
   - Add Content Security Policy
   - Implement security headers
   - Add input sanitization utilities

### Phase 3: Feature Refactoring (Weeks 5-8)

1. **Refactor Authentication Feature**
   - Encapsulate all auth-related code
   - Implement standardized component structure
   - Optimize performance
   - **Implement secure authentication storage**
   - **Add CSRF protection**

2. **Refactor Requisition Feature**
   - Encapsulate all requisition-related code
   - Break down large components
   - Optimize performance
   - **Enhance input validation and sanitization**

3. **Refactor Purchase Order Feature**
   - Encapsulate all purchase order-related code
   - Break down large components
   - Optimize performance
   - **Enhance input validation and sanitization**

4. **Implement Form Security Improvements**
   - Standardize form validation with Zod
   - Add client-side sanitization
   - Implement consistent error handling

### Phase 4: Final Improvements (Weeks 9-10)

1. **Implement Code Splitting**
   - Add lazy loading for routes
   - Implement dynamic imports for large components
   - Optimize bundle size

2. **Create Developer Documentation**
   - Document component library
   - Document state management approach
   - Create onboarding guide for new developers
   - **Create security guidelines for developers**

3. **Performance Testing and Optimization**
   - Measure performance improvements
   - Address any remaining issues
   - Document performance best practices

4. **Security Testing and Auditing**
   - Conduct security audit
   - Run automated security scans
   - Address any security vulnerabilities
   - Document security best practices

## Code Examples

### Component Example

```jsx
/**
 * RequisitionCard component
 *
 * Displays a summary of a requisition with actions.
 *
 * @component
 * @example
 * <RequisitionCard
 *   requisition={requisitionData}
 *   onView={() => navigate(`/requisitions/${requisitionData.id}`)}
 *   onEdit={() => navigate(`/requisitions/${requisitionData.id}/edit`)}
 * />
 */
export const RequisitionCard = ({ requisition, onView, onEdit }) => {
  // Derived state
  const statusColor = useMemo(() => {
    switch (requisition.status) {
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }, [requisition.status]);

  // Event handlers
  const handleView = useCallback(() => {
    onView(requisition.id);
  }, [requisition.id, onView]);

  const handleEdit = useCallback(() => {
    onEdit(requisition.id);
  }, [requisition.id, onEdit]);

  // Render
  return (
    <div className="border rounded-lg p-4 shadow-sm">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium">{requisition.title}</h3>
          <p className="text-sm text-gray-500">#{requisition.number}</p>
        </div>
        <span className={`px-2 py-1 rounded text-xs ${statusColor}`}>
          {requisition.status}
        </span>
      </div>

      <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
        <div>
          <p className="text-gray-500">Requested By</p>
          <p>{requisition.requestedBy}</p>
        </div>
        <div>
          <p className="text-gray-500">Date</p>
          <p>{formatDate(requisition.createdAt)}</p>
        </div>
      </div>

      <div className="mt-4 flex justify-end space-x-2">
        <Button variant="outline" onClick={handleView}>View</Button>
        {requisition.status === 'DRAFT' && (
          <Button variant="primary" onClick={handleEdit}>Edit</Button>
        )}
      </div>
    </div>
  );
};

RequisitionCard.propTypes = {
  requisition: PropTypes.shape({
    id: PropTypes.string.isRequired,
    number: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    status: PropTypes.string.isRequired,
    requestedBy: PropTypes.string.isRequired,
    createdAt: PropTypes.string.isRequired,
  }).isRequired,
  onView: PropTypes.func.isRequired,
  onEdit: PropTypes.func.isRequired,
};
```

### Store Example

```javascript
/**
 * Requisition store
 *
 * Manages requisition-related state, including draft requisitions, filters, and pagination.
 *
 * @example
 * const {
 *   draftRequisition,
 *   saveDraft,
 *   clearDraft
 * } = useRequisitionStore();
 *
 * // Save draft
 * saveDraft({ title: 'New Requisition', items: [] });
 *
 * // Access draft
 * console.log(draftRequisition.title);
 *
 * // Clear draft
 * clearDraft();
 */
const useRequisitionStore = create(
  persist(
    (set, get) => ({
      // State
      draftRequisition: null,
      filters: {
        status: null,
        dateRange: null,
        search: '',
      },
      pagination: {
        page: 1,
        limit: 10,
      },

      // Computed state
      get hasDraft() {
        return !!get().draftRequisition;
      },

      // Simple actions
      saveDraft: (draft) => set({ draftRequisition: draft }),
      clearDraft: () => set({ draftRequisition: null }),

      setFilters: (filters) => set({
        filters: { ...get().filters, ...filters },
        pagination: { ...get().pagination, page: 1 }, // Reset page when filters change
      }),

      setPagination: (pagination) => set({
        pagination: { ...get().pagination, ...pagination }
      }),

      // Reset actions
      resetFilters: () => set({
        filters: {
          status: null,
          dateRange: null,
          search: '',
        },
        pagination: { ...get().pagination, page: 1 },
      }),

      resetAll: () => set({
        draftRequisition: null,
        filters: {
          status: null,
          dateRange: null,
          search: '',
        },
        pagination: {
          page: 1,
          limit: 10,
        },
      }),
    }),
    {
      name: 'requisition-storage',
      partialize: (state) => ({
        draftRequisition: state.draftRequisition,
        // Don't persist filters and pagination
      }),
    }
  )
);

export { useRequisitionStore };
```

## Best Practices

### Component Best Practices

1. **Single Responsibility Principle**
   - Each component should do one thing well
   - Break down complex components into smaller ones

2. **Consistent Structure**
   - Follow the standardized component structure
   - Group related logic together

3. **Memoization**
   - Use React.memo for pure components
   - Use useMemo for expensive calculations
   - Use useCallback for event handlers passed as props

4. **Prop Validation**
   - Always define PropTypes for all components
   - Use descriptive prop names

5. **Accessibility**
   - Use semantic HTML elements
   - Add proper ARIA attributes
   - Ensure keyboard navigation works

### State Management Best Practices

1. **State Ownership**
   - Keep state as close as possible to where it's used
   - Use local state for UI-only state
   - Use Zustand for shared state
   - Use React Query for server state

2. **Store Organization**
   - Group related state in the same store
   - Keep stores focused on a specific domain
   - Use computed state for derived values

3. **Immutability**
   - Never mutate state directly
   - Use the set function provided by Zustand
   - Return new objects/arrays when updating state

4. **Persistence**
   - Only persist state that needs to survive page refreshes
   - Use the partialize option to control what gets persisted
   - Be mindful of sensitive data

5. **Debugging**
   - Use the Zustand devtools middleware in development
   - Add meaningful action names
   - Log state changes when needed

### Performance Best Practices

1. **Render Optimization**
   - Avoid unnecessary re-renders
   - Use React.memo, useMemo, and useCallback
   - Keep component props stable

2. **Code Splitting**
   - Use dynamic imports for large components
   - Implement lazy loading for routes
   - Use Suspense for loading states

3. **Data Fetching**
   - Use proper query keys
   - Implement stale-while-revalidate pattern
   - Prefetch data when possible
   - Use optimistic updates for better UX

4. **Bundle Size**
   - Monitor bundle size with tools like Webpack Bundle Analyzer
   - Use tree-shaking friendly imports
   - Avoid large dependencies when possible

5. **Rendering Efficiency**
   - Virtualize long lists
   - Implement pagination for large data sets
   - Defer non-critical rendering

### Security Best Practices

1. **Input Validation and Sanitization**
   - Validate all user inputs using Zod schemas
   - Sanitize user-generated content before rendering
   - Use DOMPurify for HTML sanitization
   - Avoid using dangerouslySetInnerHTML when possible

2. **Authentication and Authorization**
   - Use HttpOnly cookies for token storage
   - Implement proper CSRF protection
   - Never store sensitive information in localStorage or sessionStorage
   - Validate permissions on the client-side for UI purposes only

3. **Secure Communication**
   - Use HTTPS for all API requests
   - Implement proper error handling for failed requests
   - Don't expose sensitive information in error messages
   - Use secure headers for all API requests

4. **Content Security**
   - Implement a Content Security Policy
   - Avoid inline scripts and styles when possible
   - Use nonce-based CSP for necessary inline scripts
   - Regularly audit third-party dependencies

5. **Secure Coding Practices**
   - Keep dependencies up to date
   - Regularly run security audits (npm audit)
   - Follow the principle of least privilege
   - Implement proper error boundaries to prevent information leakage
