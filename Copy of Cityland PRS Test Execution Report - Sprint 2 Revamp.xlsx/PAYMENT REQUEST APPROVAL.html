<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1407505681C0" style="width:72px;" class="column-headers-background">A</th><th id="1407505681C1" style="width:219px;" class="column-headers-background">B</th><th id="1407505681C2" style="width:61px;" class="column-headers-background">C</th><th id="1407505681C3" style="width:252px;" class="column-headers-background">D</th><th id="1407505681C4" style="width:233px;" class="column-headers-background">E</th><th id="1407505681C5" style="width:330px;" class="column-headers-background">F</th><th id="1407505681C7" style="width:359px;" class="column-headers-background">H</th><th id="1407505681C8" style="width:100px;" class="column-headers-background">I</th><th id="1407505681C9" style="width:164px;" class="column-headers-background">J</th><th id="1407505681C10" style="width:245px;" class="column-headers-background">K</th><th id="1407505681C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="1407505681R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s2">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1407505681R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="11">PAYMENT REQUEST APPROVAL - Adding of Notes during Approval</td></tr><tr style="height: 19px"><th id="1407505681R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-001</td><td class="s5">Adding of Notes during Approval</td><td class="s4">Critical</td><td class="s4">Verify that a Payment Request can be submitted for Approval</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Create a new Payment Request or open a drafted one and have it reviewed.<br>2. Click the &quot;Submit&quot; button.</td><td class="s4">2. Payment Request is successfully submitted and ready for approval.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-002</td><td class="s5">Adding of Notes during Approval</td><td class="s4">Minor</td><td class="s4">Verify Payment Request No. redirection.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Navigate to For My Approval/Assigned Tab in Dashboard<br>2. Click the PR No. hyperlink of submitted Payment Request</td><td class="s4">2. Payment Request details page is displayed.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-003</td><td class="s5">Adding of Notes during Approval</td><td class="s4">High</td><td class="s4">Verify that a floating Confirmation Message appears when initiating approval.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Click on the &quot;Approve&quot; button</td><td class="s4">1. A floating confirmation message for approval is displayed.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="1407505681R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">PRS-004</td><td class="s5">Adding of Notes during Approval</td><td class="s4">Crtitical</td><td class="s4">Verify that clicking Approve displays a Confirmation Modal with Notes field.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Click on the &quot;Approve&quot; button<br>2. Observe the modal</td><td class="s4">2. A confirmation modal is shown and displayed the following field and buttons:<br>     - Notes<br>     - Add Approver<br>     - Continue<br>     - Cancel</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-005</td><td class="s5">Adding of Notes during Approval</td><td class="s4">High</td><td class="s4">Verify that Notes field accepts alphanumeric and special characters.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Type a valid note with alphanumeric and special characters.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Note is accepted and approval proceeds.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-006</td><td class="s5">Adding of Notes during Approval</td><td class="s4">Minor</td><td class="s10">Verify that Notes field does not accept emojis.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Enter emojis in the Notes field.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Error message and approval does not proceed.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-007</td><td class="s5">Adding of Notes during Approval</td><td class="s4">High</td><td class="s4">Verify that Notes field accepts input up to 100 characters.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Enter exactly 100 characters in the Notes field.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Note is accepted and approval proceeds.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">PRS-008</td><td class="s5">Adding of Notes during Approval</td><td class="s4">Minor</td><td class="s10">Verify that Notes field does not accept more than 100 characters.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Enter more than 100 characters in the Notes field.<br>2. Attempt to click &quot;Continue&quot; button.</td><td class="s4">2. System prevents entry exceeding 100 characters.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-009</td><td class="s5">Adding of Notes during Approval</td><td class="s4">High</td><td class="s4">Verify that Notes field is optional.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Leave the Notes field blank.<br>2. Click &quot;Continue&quot; button.</td><td class="s4">2. Approval proceeds without error.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">PRS-010</td><td class="s5">Adding of Notes during Approval</td><td class="s4">High</td><td class="s4">Validate Cancel button functionality.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Click on &quot;Cancel&quot; button.</td><td class="s4">1. Modal closes and user is returned to Payment Request detail page.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1407505681R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">PRS-011</td><td class="s5">Adding of Notes during Approval</td><td class="s4">High</td><td class="s4">Verify that entered Notes are displayed under the Check Notes button after Approval.</td><td class="s4">1. User is a Payment Request Approver<br>2. A Payment Request has been submitted for Approval</td><td class="s4">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Notes” of the approved Payment Request.<br>3. Verify visibility of approver.<br>4. Verify the badge is cleared.</td><td class="s4">1. “New Attachment” badge is displayed.<br>2. Entered Approval Notes are displayed correctly.<br>3. Approver name should be displayed correctly.<br>4. “New Attachment” badge is cleared when viewed.</td><td class="s8"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s3" colspan="11">PAYMENT REQUEST APPROVAL - Update Adding of Additional Approver</td></tr><tr style="height: 19px"><th id="1407505681R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">PRS-001</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Critical</td><td class="s4">Verify access to Payment Request through Requisition Slip</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Click RS Number<br>2. Click Related Documents<br>3 Click Payments tab<br>4. Validate if Payment Request is accessible through Requisition Slip</td><td class="s4">4. Payment Request is accessible through Requisition Slip</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">PRS-002</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Critical</td><td class="s4">Verify access to Payment Request through Dashboard</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Click the Payment Request in Dashboard Page<br>2. Validate if it is accessible through Dashboard</td><td class="s4">2. Payment Request is accessible through Dashboard</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-003</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Critical</td><td class="s4">Verify that Add button in Approvers Section displays modal</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Click on the Add Button in the Approvers Section<br>2. Validate display of modal</td><td class="s4">2. Should display a Modal that will allow adding of an Approver</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s9"></td></tr><tr style="height: 19px"><th id="1407505681R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4">PRS-004</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s4">Verify user search field shows valid user types only	</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Click search field<br>2. Type name or leave blank to show all</td><td class="s4">2 Should display a Search User Field<br>    a. Should display Users with a User Types of<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Head<br>           iv) Division Head<br>           v) Area Staff/Department Secretary</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4">PRS-005</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Medium</td><td class="s4">Verify that added Approver is shown below current Approver with asterisk (*)	</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Add approver<br>2. Observe list order and label</td><td class="s4">2. Additional Approver should be displayed below the current Approver with a * as their Label</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s4">PRS-006</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s4">Verify that added Approver is reflected to List of Approvers without delay	</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Add an approver<br>2. Check Approvers list</td><td class="s4">2. Added Approver should be reflected to the List of Approvers once added<br>           i. Will not need to wait for Approval for the User to be added as an Additional Approver</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s4">PRS-007</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s4">Verify that added Approver receives notification	</td><td class="s4">1. User is a Payment Request Approver and the added Approver</td><td class="s4">1. Add approver<br>2. Log in as added approver<br>3. Check Notification Bell</td><td class="s4">3. Should notify the tagged Additional Approver through the Notification Bell</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s4">PRS-008</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Critical</td><td class="s4">Verify Default Approver can edit or delete Additional Approver before they approve	</td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. As default approver, click edit/delete on the added approver<br>2. Validate if it can be edited or deleted</td><td class="s4">2. Should allow the Default Approver to Edit or Delete the Added Approver until they haven&#39;t Approve the Payment Request</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s4">PRS-009</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Critical</td><td class="s10">Verify that current Approver must approve before added Approver can approve        </td><td class="s4">1. User is a Payment Request Approver and the added Approver</td><td class="s4">1. As added approver, try approving without current approver&#39;s action</td><td class="s4">2. Should require the current Approver to Approve the Payment Request before the Additional Approver can Approve</td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s4">PRS-010</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">Critical</td><td class="s10">Verify that added Approver must approve before next level approver can approve        </td><td class="s4">1. User is a Payment Request Approver and the current Approver</td><td class="s4">1. Add approver<br>2. Try approving as next level approver without added approver approving</td><td class="s4">2. Should require the Additional Approver to Approve the Payment Request before allowing to continue on the next Level of Approver<br></td><td class="s6"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1407505681R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s4">PRS-011</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s4">Verify Add Approver option appears in confirmation modal if no added approver yet	</td><td class="s4">1. User is a Payment Request Approver and the current Approver<br>2. Additional Approver has been added<br>3. Current Approver has not approved yet</td><td class="s4">1. Click Approve button<br>2. Observe confirmation modal</td><td class="s4">2. If an Additional Approver is not yet Added, should allow adding of Approver when Approving the Payment Request<br></td><td class="s8"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s4">PRS-012</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s10">Verify Add Approver button is not shown in confirmation modal if already added        </td><td class="s4">1. User is a Payment Request Approver and the current Approver<br>2. Additional Approver has been added<br>3. Current Approver has not approved yet</td><td class="s4">1. Click Approve button<br>2. Check confirmation modal</td><td class="s4">2. No Add Approver button appears. Should only display the Add Approver Button in the Confirmation Message if the Additional Approver is not yet indicated</td><td class="s8"></td><td class="s7">Not Started</td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1407505681R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s4">PRS-013</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s4">Verify Adding Additional Approver After Deletion</td><td class="s4">1. User is a Payment Request Approver and the current Approver<br>2. Additional Approver has been added<br>3. Current Approver has not approved yet</td><td class="s8">1. Select Payment Request<br>2. Validate Approvers section<br>3. Delete Approver<br>4. Click Add button<br>5. Select a different user as approver</td><td class="s4">3. Approver should be removed from Approvers section<br>4. Should be able to add a new additional approver<br>5. Should be added successfully into Approvers section</td><td class="s12"></td><td class="s7">Not Started</td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1407505681R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s4">PRS-014</td><td class="s5">Update Adding of Additional Approver</td><td class="s4">High</td><td class="s10">Verify Adding Additional Approver After Deletion and RS already approved by the current approver</td><td class="s8">1. User is a Payment Request Approver and the current Approver<br>2. Additional Approver has been added<br>3. Current Approver has already approved the RS</td><td class="s4">1. Select Payment Request<br>2. Validate Approvers section<br>3. Delete Approver<br>4.Check Add Button</td><td class="s4">3. Approver should be removed from Approvers section<br>4. Add button should no longer visible</td><td class="s12"></td><td class="s7">Not Started</td><td class="s12"></td><td class="s12"></td></tr></tbody></table></div>