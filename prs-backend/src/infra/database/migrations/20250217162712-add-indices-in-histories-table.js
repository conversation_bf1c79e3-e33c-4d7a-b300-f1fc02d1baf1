'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'histories';
    const indices = [
      {
        name: 'histories_item_id_type_index',
        fields: ['item_id', 'type'],
      },
      {
        name: 'histories_rs_letter_rs_number_company_id_index',
        fields: ['rs_letter', 'rs_number', 'company_id'],
      },
    ];

    for (const index of indices) {
      const indexExists = await queryInterface.sequelize.query(
        `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${index.name}'`,
      );

      if (indexExists[0].length === 0) {
        await queryInterface.addIndex(tableName, index.fields, {
          name: index.name,
        });
      }
    }
  },

  async down(queryInterface, Sequelize) {
    const tableName = 'histories';
    const indices = [
      {
        name: 'histories_item_id_type_index',
        fields: ['item_id', 'type'],
      },
      {
        name: 'histories_rs_letter_rs_number_company_id_index',
        fields: ['rs_letter', 'rs_number', 'company_id'],
      },
    ];

    for (const index of indices) {
      const indexExists = await queryInterface.sequelize.query(
        `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${index.name}'`,
      );

      if (indexExists[0].length > 0) {
        await queryInterface.removeIndex(tableName, index.name);
      }
    }
  }
};

