# Authentication and Authorization

## Overview

The PRS-Backend implements a multi-layered authentication and authorization system. This document provides a detailed analysis of the authentication mechanisms, authorization controls, and security considerations.

## Authentication System

The authentication system uses JWT (JSON Web Tokens) with multiple token types and OTP (One-Time Password) verification.

### Authentication Flow

1. **Login**: User provides username and password
2. **OTP Verification**: If enabled, user must provide OTP code
3. **Token Generation**: System generates access and refresh tokens
4. **Token Validation**: Tokens are validated on each request

### Token Types

The system uses different token types for different purposes:

1. **Access Token**: Used for API access (60 minutes validity)
2. **Refresh Token**: Used to obtain new access tokens (7 days validity)
3. **OTP Token**: Used during OTP verification (5 minutes validity)
4. **Temporary Password Token**: Used during password reset (5 minutes validity)

### Implementation Details

#### Login Process

```javascript
// From src/app/handlers/controllers/authController.js
async login(request, reply) {
  const existingUser = await this.authService.verifyUser(request.body);

  //TODO: Remove after development
  const isByPassOTP =
    process.env.BYPASS_OTP === 'true' || process.env.BYPASS_OTP === true;

  if (isByPassOTP) {
    return this.authService.sendAccessToken(existingUser.id, reply);
  }

  /* Force User to Update temporary password - 5 mins */
  if (existingUser.isPasswordTemporary) {
    return this.authService.sendTempPassToken(existingUser.id, reply);
  }

  /* Force User to Setup OTP if otpSecret does not exist - 10 mins */
  if (!existingUser.otpSecret) {
    return this.authService.sendUserOtpSecret(
      existingUser.id,
      existingUser.username,
      reply,
    );
  }

  /* Send verify OTP token for otp verification - 5 mins */
  return this.authService.sendVerifyOTPToken(existingUser.id, reply);
}
```

#### User Verification

```javascript
// From src/app/services/authService.js
async verifyUser(payload) {
  const { username, password } = payload;
  const { USER_STATUS } = this.userConstants;

  const existingUser = await this.userRepository.getUser({
    username,
  });

  if (!existingUser || existingUser?.status === USER_STATUS.INACTIVE) {
    throw this.clientErrors.UNAUTHORIZED({
      message: 'Invalid username or password',
    });
  }

  const isMatch = this.bcrypt.compareSync(password, existingUser.password);

  if (!isMatch) {
    throw this.clientErrors.UNAUTHORIZED({
      message: 'Invalid username or password',
    });
  }

  return existingUser;
}
```

#### OTP Verification

```javascript
// From src/app/services/authService.js
verifyOTP(userFromToken, otp) {
  if (!userFromToken.otpSecret) {
    throw this.clientErrors.UNPROCESSABLE_ENTITY({
      message: 'User MFA is not yet setup',
    });
  }

  const plainOtpSecret = this.utils.decrypt(userFromToken.otpSecret);
  const totp = this.createTOTP(userFromToken.username, plainOtpSecret);
  const delta = totp.validate({ token: otp, window: 1 });

  if (delta !== 0) {
    throw this.clientErrors.UNPROCESSABLE_ENTITY({
      message: 'Invalid OTP provided',
    });
  }

  return userFromToken;
}
```

#### Token Generation

```javascript
// From src/app/services/authService.js
generateJWT(payload, expiresInMinutes) {
  const currentTime = Math.floor(Date.now() / 1000);
  const expiresAt = currentTime + expiresInMinutes * 60;
  const token = this.fastify.jwt.sign({
    ...payload,
    iat: currentTime,
    exp: expiresAt,
  });

  return { token, expiresAt };
}

/**
 * Generate Access Token - 60 mins
 * Refresh token valid for 7 days (10080 minutes)
 */
sendAccessToken(userId, reply) {
  const jwtResult = this.generateJWT({ id: userId }, 60);
  const refreshTokenResult = this.generateJWT(
    { id: userId, isForRefresh: true },
    10080,
  );

  return reply.status(200).send({
    accessToken: jwtResult.token,
    refreshToken: refreshTokenResult.token,
    expiredAt: jwtResult.expiresAt,
  });
}
```

#### Token Validation

```javascript
// From src/app/handlers/middlewares/authenticate.js
const authenticate = async function (request, _reply) {
  const clientErrors = this.diScope.resolve('clientErrors');
  const userRepository = this.diScope.resolve('userRepository');
  const authErrorMsg =
    'Invalid authorization token. Please provide a valid token';

  try {
    const { id, isForOTP, isForTempPass, isForRefresh } =
      await request.jwtVerify();

    const isInvalidTokenPayload =
      !id || isForOTP || isForTempPass || isForRefresh;

    if (isInvalidTokenPayload) {
      throw new Error(authErrorMsg);
    }

    /* Get user via PK query -> without paranoid option (will disregard inactive users) */
    const userFromToken = await userRepository.getUserById(id);

    request.userFromToken = userFromToken;
  } catch (error) {
    this.log.error(
      `[VerifyAuthToken Error]: User ID:${request.body?.id} Error:${error?.message}`,
    );

    throw clientErrors.UNAUTHORIZED({
      message: authErrorMsg,
    });
  }
};
```

## Authorization System

The authorization system is designed to control access to resources based on user roles and permissions. However, it is currently disabled due to a bug.

### Role-Based Access Control

The system uses a role-based access control (RBAC) model:

1. **Roles**: Users are assigned roles (e.g., Admin, Purchasing Staff)
2. **Permissions**: Roles have permissions for specific actions
3. **Resources**: Permissions control access to resources

### Permission Structure

Permissions are defined as combinations of modules and actions:

```javascript
// From src/domain/constants/permissionConstants.js
const MODULES = {
  ROLES: 'roles',
  USERS: 'users',
  COMPANIES: 'companies',
  // ... other modules
};

const ACTIONS = {
  VIEW: 'view',
  GET: 'get',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  SYNC: 'sync',
  APPROVAL: 'approval',
};

const PERMISSIONS = {
  /* Roles */
  GET_ROLES: { module: MODULES.ROLES, action: ACTIONS.GET },

  /* Users */
  VIEW_USERS: { module: MODULES.USERS, action: ACTIONS.VIEW },
  GET_USERS: { module: MODULES.USERS, action: ACTIONS.GET },
  CREATE_USERS: { module: MODULES.USERS, action: ACTIONS.CREATE },
  // ... other permissions
};
```

### Authorization Middleware

The authorization middleware is designed to check if a user has the required permissions:

```javascript
// From src/app/handlers/middlewares/authorize.js
const authorize = (permissions, requireAll = false) => {
  if (!Array.isArray(permissions)) {
    permissions = [permissions];
  }

  return async function (request, _reply) {
    // disable permission checking, kasi may bug
    return true;
    const clientErrors = this.diScope.resolve('clientErrors');
    const errorMessage = 'You do not have permission to perform this action';
    const { userFromToken } = request;

    const isUserWithoutRole =
      !userFromToken ||
      !userFromToken?.role ||
      !userFromToken?.role?.permissions;

    if (isUserWithoutRole) {
      throw clientErrors.FORBIDDEN({
        message: errorMessage,
      });
    }

    /* Support OR & AND permission logic */
    const checkPermissions = requireAll ? 'every' : 'some';
    const hasPermission = permissions[checkPermissions]((requiredPermission) =>
      userFromToken.role.permissions.some(
        (permission) =>
          permission.module === requiredPermission.module &&
          permission.action === requiredPermission.action,
      ),
    );

    if (!hasPermission) {
      throw clientErrors.FORBIDDEN({
        message: errorMessage,
      });
    }
  };
};
```

### Route Protection

Routes are protected using the authorization middleware:

```javascript
// From src/interfaces/router/private/userRoute.js
fastify.route({
  method: 'GET',
  url: '/',
  preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
  handler: userController.getUserList.bind(userController),
});
```

## Security Issues

### 1. Disabled Authorization

The most critical security issue is that authorization is completely disabled:

```javascript
// From src/app/handlers/middlewares/authorize.js
return async function (request, _reply) {
  // disable permission checking, kasi may bug
  return true;
  // ... actual authorization code never runs
};
```

This means any authenticated user can access any endpoint regardless of their permissions.

### 2. OTP Bypass in Development

OTP verification can be bypassed in development:

```javascript
// From src/app/handlers/controllers/authController.js
const isByPassOTP =
  process.env.BYPASS_OTP === 'true' || process.env.BYPASS_OTP === true;

if (isByPassOTP) {
  return this.authService.sendAccessToken(existingUser.id, reply);
}
```

If this setting is enabled in production, it would bypass the OTP verification.

### 3. Hardcoded Secrets

Some secrets are hardcoded or have weak defaults:

```javascript
// From .env.example
JWT_SECRET=secret
OTP_KEY=U29tZVNlY3JldEtleVdpdGg2NEJ5dGVz # Minimum of 32 characters - crypto encrypt
PASS_SECRET=12345
```

### 4. Missing Rate Limiting

There's no rate limiting for authentication endpoints, which makes the system vulnerable to brute force attacks.

### 5. Insufficient Password Policies

Password policies are not strictly enforced:

```javascript
// From src/infra/database/models/userModel.js
password: {
  type: Sequelize.STRING(255),
  allowNull: false,
  validate: {
    len: [8, 255],
  },
},
```

This only enforces a minimum length of 8 characters without other complexity requirements.

## Recommendations

### 1. Fix Authorization System

The most critical recommendation is to fix and re-enable the authorization system:

```javascript
// Fixed authorization middleware
return async function (request, _reply) {
  const clientErrors = this.diScope.resolve('clientErrors');
  const errorMessage = 'You do not have permission to perform this action';
  const { userFromToken } = request;

  const isUserWithoutRole =
    !userFromToken ||
    !userFromToken?.role ||
    !userFromToken?.role?.permissions;

  if (isUserWithoutRole) {
    throw clientErrors.FORBIDDEN({
      message: errorMessage,
    });
  }

  /* Support OR & AND permission logic */
  const checkPermissions = requireAll ? 'every' : 'some';
  const hasPermission = permissions[checkPermissions]((requiredPermission) =>
    userFromToken.role.permissions.some(
      (permission) =>
        permission.module === requiredPermission.module &&
        permission.action === requiredPermission.action,
    ),
  );

  if (!hasPermission) {
    throw clientErrors.FORBIDDEN({
      message: errorMessage,
    });
  }
};
```

### 2. Implement Environment-Specific Security Controls

Ensure security controls are appropriate for each environment:

```javascript
// Environment-specific security controls
if (process.env.NODE_ENV === 'production') {
  // Disable OTP bypass in production
  app.decorate('bypassOTP', false);
  
  // Enable strict security headers
  app.register(require('@fastify/helmet'));
  
  // Enable rate limiting
  app.register(require('@fastify/rate-limit'), {
    max: 100,
    timeWindow: '1 minute'
  });
}
```

### 3. Strengthen Password Policies

Implement stronger password policies:

```javascript
// Enhanced password validation
password: {
  type: Sequelize.STRING(255),
  allowNull: false,
  validate: {
    is: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    len: [8, 255],
  },
},
```

### 4. Implement Secure Secret Management

Use a secure secret management solution:

- Store secrets in a vault (e.g., HashiCorp Vault, AWS Secrets Manager)
- Rotate secrets regularly
- Use environment-specific secrets

### 5. Add Security Headers

Implement security headers to protect against common web vulnerabilities:

```javascript
// Add security headers
app.register(require('@fastify/helmet'), {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:'],
      scriptSrc: ["'self'"]
    }
  }
});
```

### 6. Implement Audit Logging

Enhance the audit logging system to track security events:

```javascript
// Enhanced audit logging
app.addHook('onRequest', async (request, reply) => {
  request.log.info({
    requestId: request.id,
    method: request.method,
    url: request.url,
    ip: request.ip,
    userId: request.userFromToken?.id,
  });
});

app.addHook('onResponse', async (request, reply) => {
  request.log.info({
    requestId: request.id,
    statusCode: reply.statusCode,
    responseTime: reply.getResponseTime(),
  });
});
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
