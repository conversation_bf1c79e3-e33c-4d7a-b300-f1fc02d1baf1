# Testing Enhanced Logging

This guide explains how to test the enhanced logging features that include IP addresses and other related information in production logs.

## Overview of Enhanced Logging Features

The enhanced logging system now captures:

1. **IP Address Information**:
   - Direct client IP (`ip`)
   - Forwarded IP addresses (`forwardedIp`, `x-forwarded-for`)
   - Original IP when behind proxies (`originalIp`)

2. **Request Metadata**:
   - User agent (`userAgent`)
   - Referer (`referer`)
   - Origin (`origin`)
   - Host (`host`)
   - Path (`path`)
   - Request timestamp (`requestTime`, `timestamp`)

3. **User Context**:
   - User ID (`userId`)
   - Username (`username`)
   - Role (`role`)
   - Department (`department`)

4. **Performance Metrics**:
   - Response time (`responseTime`)
   - Start time (`startTime`)
   - End time (`endTime`)
   - Total time (`totalTime`)

## Testing Methods

There are two scripts provided for testing the enhanced logging:

1. **Node.js Test Script** (`test-enhanced-logging.js`): A comprehensive test that simulates various request scenarios.
2. **Bash Test Script** (`test-production-logging.sh`): A simpler test that runs the application in production mode and makes test requests.

### Method 1: Using the Node.js Test Script

This script makes multiple HTTP requests with different headers and checks the log files for the expected content.

```bash
# Navigate to the project directory
cd /path/to/prs-backend

# Run the test script
node scripts/test-enhanced-logging.js
```

The script will:
- Clear existing log files
- Send test requests with various headers
- Check log files for expected content
- Test log rotation

### Method 2: Using the Bash Test Script

This script runs the application in production mode and makes test requests with curl.

```bash
# Navigate to the project directory
cd /path/to/prs-backend

# Run the test script
./scripts/test-production-logging.sh
```

The script will:
- Start the application in production mode
- Make test requests with different headers
- Check log files for expected content
- Test log rotation
- Stop the application

### Method 3: Manual Testing

You can also test the enhanced logging manually:

1. Start the application in production mode:
   ```bash
   NODE_ENV=production node index.js
   ```

2. Make requests with curl:
   ```bash
   # Standard request
   curl -X GET http://localhost:4000/health
   
   # Request with X-Forwarded-For
   curl -X GET -H "X-Forwarded-For: *************" http://localhost:4000/health
   
   # Request with referer and origin
   curl -X GET -H "Referer: https://example.com/dashboard" -H "Origin: https://example.com" http://localhost:4000/health
   ```

3. Check the log files:
   ```bash
   # View app.log
   cat logs/app.log | grep -i ip
   
   # View error.log (if any errors occurred)
   cat logs/error.log
   ```

## Verifying Log Content

When checking the logs, verify that the following information is present:

1. **IP Address Information**:
   ```
   grep -i "ip" logs/app.log
   ```

2. **Request Metadata**:
   ```
   grep -i "userAgent\|referer\|origin\|host" logs/app.log
   ```

3. **Timestamps**:
   ```
   grep -i "timestamp\|requestTime" logs/app.log
   ```

4. **Path Information**:
   ```
   grep -i "path" logs/app.log
   ```

## Testing Log Rotation

To test log rotation:

```bash
# Run the log rotation script
./scripts/rotate-logs.sh

# Check for rotated logs
ls -la logs/*.gz
```

The script should:
1. Create compressed versions of logs that exceed the size threshold
2. Clear the original log files
3. Keep only the configured number of rotated logs

## Troubleshooting

If logs don't contain the expected information:

1. **Check Environment Variables**:
   - Ensure `NODE_ENV=production` is set
   - Verify `LOG_LEVEL` is set to `info` or lower

2. **Check Log Directory Permissions**:
   - Ensure the application has write permissions to the logs directory

3. **Check Request Headers**:
   - Verify that the test requests include the expected headers

4. **Check Log Configuration**:
   - Review the log settings in `src/infra/logs/index.js`
   - Ensure the serializers are correctly configured

## Production Deployment Considerations

When deploying to production:

1. **Set Up Log Rotation**:
   ```bash
   ./scripts/setup-log-rotation.sh
   ```

2. **Configure Log Retention**:
   - Edit `scripts/rotate-logs.sh` to adjust retention periods
   - Default: 14 days for app logs, 30 days for error logs

3. **Monitor Log Size**:
   - Regularly check log sizes to ensure they don't consume too much disk space
   - Consider setting up alerts for large log files

4. **Review Logs Regularly**:
   - Set up a process to review logs for security and performance issues
   - Consider using a log aggregation tool for easier analysis
