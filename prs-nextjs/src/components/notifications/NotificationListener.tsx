"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Notification } from "@prisma/client";

interface NotificationListenerProps {
  onNotification: (notifications: Notification[]) => void;
}

export function NotificationListener({ onNotification }: NotificationListenerProps) {
  const { data: session } = useSession();
  const [eventSource, setEventSource] = useState<EventSource | null>(null);

  useEffect(() => {
    if (!session) return;

    // Create EventSource connection
    const sse = new EventSource("/api/sse/notifications");

    // Handle incoming events
    sse.onmessage = (event) => {
      const notifications = JSON.parse(event.data);
      onNotification(notifications);
    };

    // Handle connection error
    sse.onerror = (error) => {
      console.error("SSE error:", error);
      sse.close();

      // Attempt to reconnect after a delay
      setTimeout(() => {
        setEventSource(new EventSource("/api/sse/notifications"));
      }, 5000);
    };

    setEventSource(sse);

    // Clean up on unmount
    return () => {
      sse.close();
    };
  }, [session, onNotification]);

  return null;
}
