<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-right:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s25{background-color:#3c78d8;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-left:none;background-color:#ffffff;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s27{background-color:#ffffff;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{background-color:#6aa84f;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-right:none;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Verdana;font-size:9pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#4a86e8;text-align:center;font-weight:bold;color:#000000;font-family:Verdana;font-size:16pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00ff00;text-align:left;font-weight:bold;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Verdana;font-size:9pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{background-color:#f1c232;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:18pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;font-weight:bold;color:#000000;font-family:Verdana;font-size:9pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#4a86e8;text-align:center;font-weight:bold;color:#000000;font-family:Verdana;font-size:16pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Verdana;font-size:9pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{background-color:#ff0000;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;font-weight:bold;color:#000000;font-family:Verdana;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Verdana;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Verdana;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1197899851C0" style="width:138px;" class="column-headers-background">A</th><th id="1197899851C1" style="width:157px;" class="column-headers-background">B</th><th id="1197899851C2" style="width:100px;" class="column-headers-background">C</th><th id="1197899851C3" style="width:100px;" class="column-headers-background">D</th><th id="1197899851C4" style="width:100px;" class="column-headers-background">E</th><th id="1197899851C5" style="width:100px;" class="column-headers-background">F</th><th id="1197899851C6" style="width:100px;" class="column-headers-background">G</th><th id="1197899851C7" style="width:100px;" class="column-headers-background">H</th><th id="1197899851C8" style="width:153px;" class="column-headers-background">I</th><th id="1197899851C9" style="width:100px;" class="column-headers-background">J</th><th id="1197899851C10" style="width:213px;" class="column-headers-background">K</th><th id="1197899851C11" style="width:100px;" class="column-headers-background">L</th><th id="1197899851C12" style="width:100px;" class="column-headers-background">M</th><th id="1197899851C13" style="width:100px;" class="column-headers-background">N</th><th id="1197899851C14" style="width:100px;" class="column-headers-background">O</th><th id="1197899851C15" style="width:100px;" class="column-headers-background">P</th><th id="1197899851C16" style="width:100px;" class="column-headers-background">Q</th><th id="1197899851C17" style="width:100px;" class="column-headers-background">R</th><th id="1197899851C18" style="width:100px;" class="column-headers-background">S</th><th id="1197899851C19" style="width:100px;" class="column-headers-background">T</th><th id="1197899851C20" style="width:100px;" class="column-headers-background">U</th><th id="1197899851C21" style="width:100px;" class="column-headers-background">V</th><th id="1197899851C22" style="width:100px;" class="column-headers-background">W</th><th id="1197899851C23" style="width:100px;" class="column-headers-background">X</th><th id="1197899851C24" style="width:100px;" class="column-headers-background">Y</th><th id="1197899851C25" style="width:100px;" class="column-headers-background">Z</th><th id="1197899851C26" style="width:100px;" class="column-headers-background">AA</th><th id="1197899851C27" style="width:100px;" class="column-headers-background">AB</th></tr></thead><tbody><tr style="height: 19px"><th id="1197899851R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td class="s0" colspan="9">[Cityland-PRS] Defect Verification Test Report - [May 04, 2025]</td><td></td><td class="s1 softmerge"><div class="softmerge-inner" style="width:311px;left:-1px">State: {For PO Acceptance} updated: 2025-03-17<br>State: {Reopened} updated: 2025-03-17</div></td><td class="s2"></td><td class="s2"></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s4" colspan="9">[Cityland-PRS] Cycle 5 All+Wk6+Wk7 Regression and Functional Testing (Inital E2E) Test Report - [May 04, 2025]</td></tr><tr style="height: 19px"><th id="1197899851R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5">Overall   Summary</td><td class="s6" colspan="8">91.33%</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s7 softmerge"><div class="softmerge-inner" style="width:97px;left:-1px">Overall   Summary</div></td><td class="s8" colspan="8"></td></tr><tr style="height: 19px"><th id="1197899851R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">Scope</td><td class="s9" colspan="8">Bug Verification for the Entire PRS Process</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s7">Scope</td><td class="s10" colspan="8">Defect retest on dev environment</td></tr><tr style="height: 19px"><th id="1197899851R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5" rowspan="5">Defect</td><td class="s11">Open - Showstopper</td><td class="s12">0</td><td class="s11">To Do</td><td class="s12">4</td><td class="s11" colspan="4" rowspan="5"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s7" rowspan="5">Defect</td><td class="s13">Critical</td><td class="s14">124</td><td class="s13">To Do</td><td class="s14">0</td><td class="s15" colspan="4" rowspan="5"></td></tr><tr style="height: 19px"><th id="1197899851R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s11">Open - Critical</td><td class="s12">0</td><td class="s11">In Progress</td><td class="s12">1</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s13">High</td><td class="s14">249</td><td class="s13">In Progress</td><td class="s14">0</td></tr><tr style="height: 19px"><th id="1197899851R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s11">Open - High</td><td class="s12">1</td><td class="s11">Code Review</td><td class="s12">2</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s13">Minor</td><td class="s14">156</td><td class="s13">Code Review</td><td class="s14">0</td></tr><tr style="height: 19px"><th id="1197899851R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s11">Open - Minor</td><td class="s12">3</td><td class="s11">For QA</td><td class="s12">0</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s16">Showstopper</td><td class="s14">38</td><td class="s13">For QA</td><td class="s14">0</td></tr><tr style="height: 19px"><th id="1197899851R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s11" colspan="2"></td><td class="s17">Reopened</td><td class="s12">1</td><td></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s3"></td><td class="s15" colspan="2"></td><td class="s19">For PO Review - STG</td><td class="s14">0</td></tr><tr style="height: 63px"><th id="1197899851R8" style="height: 63px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 63px">9</div></th><td class="s5">Highlights</td><td class="s9" colspan="8"></td><td class="s3"></td><td class="s10" colspan="8">- Regression Testing for Week 4 + Week 5 Completed as of February 26, 2025<br>- Scope of testing new features completed only from Month 3-  week 4 + week 5<br>- 64 Total defects raised for week 4 + week 5<br>- Regression Testing 100.00% execution completed. Total of 402 TCs in scope<br>- Not Started-0, Passed-218, Failed-45, Blocked-113, Out of Scope-0, Deprecated- 0, Not Run - 26, In Progress - 0<br>- Not Run are for features that are in progress for development<br>- 267 total defects raised for both month 2 and month 3</td><td class="s3"></td><td class="s7">Highlights</td><td class="s10" colspan="8">Continued Cycle 5 All+Wk6+Wk7 Regression and Functional Testing (Initial End-to-End).<br>Identified and reported 16 new defects.</td></tr><tr style="height: 19px"><th id="1197899851R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5">Blocker/s</td><td class="s9" colspan="8"></td><td></td><td class="s20">Tab colors</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s7">Blocker/s</td><td class="s15" colspan="8"></td></tr><tr style="height: 19px"><th id="1197899851R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5">Test Case Link</td><td class="s21" colspan="8"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TeL0p1J9Q5HOlO3_RBHCrz8g_SWdmv0B/edit?usp=sharing&amp;ouid=118039089979223405590&amp;rtpof=true&amp;sd=true">https://docs.google.com/spreadsheets/d/1TeL0p1J9Q5HOlO3_RBHCrz8g_SWdmv0B/edit?usp=sharing&amp;ouid=118039089979223405590&amp;rtpof=true&amp;sd=true</a></td><td></td><td class="s22">Yellow - in progress tc</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s7">Test Case Link</td><td class="s23" colspan="8"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TeL0p1J9Q5HOlO3_RBHCrz8g_SWdmv0B/edit?usp=sharing&amp;ouid=118039089979223405590&amp;rtpof=true&amp;sd=true">https://docs.google.com/spreadsheets/d/1TeL0p1J9Q5HOlO3_RBHCrz8g_SWdmv0B/edit?usp=sharing&amp;ouid=118039089979223405590&amp;rtpof=true&amp;sd=true</a></td></tr><tr style="height: 19px"><th id="1197899851R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5">Incident Log&#39;s Link</td><td class="s24" colspan="8"><a target="_blank" href="https://youtrack.stratpoint.com/reports/issueDistribution/136-38">https://youtrack.stratpoint.com/reports/issueDistribution/136-38 </a></td><td></td><td class="s25">Blue - done tc</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s3"></td><td class="s7 softmerge"><div class="softmerge-inner" style="width:97px;left:-1px">Incident Log&#39;s Link</div></td><td class="s26" colspan="8"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TeL0p1J9Q5HOlO3_RBHCrz8g_SWdmv0B/edit?usp=sharing&amp;ouid=118039089979223405590&amp;rtpof=true&amp;sd=true">Defect Summary tab:-<br>https://docs.google.com/spreadsheets/d/1TeL0p1J9Q5HOlO3_RBHCrz8g_SWdmv0B/edit?usp=sharing&amp;ouid=118039089979223405590&amp;rtpof=true&amp;sd=true</a></td></tr><tr style="height: 19px"><th id="1197899851R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s27" colspan="9"></td><td></td><td class="s28">Green - done testing</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1197899851R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s27" colspan="9"></td><td></td><td class="s29">Red - Spillover</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R14" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">15</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R15" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">16</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R16" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">17</div></th><td class="s27" colspan="9"></td><td></td><td class="s1 softmerge"><div class="softmerge-inner" style="width:311px;left:-1px">State: {For PO Acceptance} updated: 2025-03-17<br>State: {Reopened} updated: 2025-03-17</div></td><td class="s2"></td><td class="s2"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R17" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">18</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R18" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">19</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R19" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">20</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R20" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">21</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R21" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">22</div></th><td class="s27" colspan="9"></td><td></td><td class="s20"> </td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R22" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">23</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R23" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">24</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R24" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">25</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R25" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">26</div></th><td class="s27" colspan="9"></td><td class="s30"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R26" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">27</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R27" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">28</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R28" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">29</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R29" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">30</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R30" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">31</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 38px"><th id="1197899851R31" style="height: 38px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 38px">32</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R32" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">33</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R33" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">34</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R34" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">35</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R35" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">36</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R36" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">37</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R37" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">38</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R38" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">39</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R39" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">40</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R40" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">41</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R41" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">42</div></th><td class="s27" colspan="9"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 20px"><th id="1197899851R42" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">43</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R43" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">44</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R44" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">45</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R45" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">46</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R46" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">47</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R47" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">48</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R48" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">49</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R49" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">50</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R50" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">51</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R51" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">52</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R52" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">53</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R53" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">54</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R54" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">55</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R55" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">56</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R56" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">57</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R57" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">58</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R58" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">59</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R59" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">60</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R60" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">61</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R61" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">62</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R62" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">63</div></th><td class="s27" colspan="9"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R63" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">64</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R64" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">65</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R65" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">66</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R66" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">67</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R67" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">68</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R68" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">69</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R69" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">70</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R70" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">71</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R71" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">72</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="1197899851R72" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">73</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></tbody></table></div><div id='embed_1919199894' class='waffle-embedded-object-overlay' style='width: 540px; height: 335px; display: block;'></div><div id='embed_1001122552' class='waffle-embedded-object-overlay' style='width: 540px; height: 335px; display: block;'></div><div id='embed_451815554' class='waffle-embedded-object-overlay' style='width: 747px; height: 462px; display: block;'></div><script>
  function posObj(sheet, id, row, col, x, y) {
      var rtl = false;
      var sheetElement = document.getElementById(sheet);
      if (!sheetElement) {
        sheetElement = document.getElementById(sheet + '-grid-container');
      }
      if (sheetElement) {
        rtl = sheetElement.getAttribute('dir') == 'rtl';
      }
      var r = document.getElementById(sheet+'R'+row);
      var c = document.getElementById(sheet+'C'+col);
      if (r && c) {
        var objElement = document.getElementById(id);
        var s = objElement.style;
        var t = y;
        while (r && r != sheetElement) {
          t += r.offsetTop;
          r = r.offsetParent;
      }
      var offsetX = x;
      while (c && c != sheetElement) {
        offsetX += c.offsetLeft;
        c = c.offsetParent;
      }
      if (rtl) {
        offsetX -= objElement.offsetWidth;
      }
      s.left = offsetX + 'px';
      s.top = t + 'px';
      s.display = 'block';
      s.border = '1px solid #000000';
    }
  }

  function posObjs() {
  posObj('1197899851', 'embed_1919199894', 55, 0, 23, 17);posObj('1197899851', 'embed_1001122552', 55, 2, 88, 17);posObj('1197899851', 'embed_451815554', 13, 0, 31, 2);}posObjs();</script><script src="resources/340999021-ChartsCombinedJ2clBootstrap_bootstrap_core.js"></script><script>var ritzspreadsheetconstants = {"localeName":"en_US","timeZoneConstants":{"GMT":{"names_ext":{"STD_GENERIC_LOCATION":"GMT","STD_LONG_NAME_GMT":"GMT"},"std_offset":0,"names":["GMT","Greenwich Mean Time"],"id":"GMT","transitions":[]},"America/Los_Angeles":{"names_ext":{"DST_GENERIC_LOCATION":"Los Angeles Time","DST_LONG_NAME_GMT":"GMT-07:00","STD_GENERIC_LOCATION":"Los Angeles Time","STD_LONG_NAME_GMT":"GMT-08:00"},"std_offset":-480,"names":["PST","Pacific Standard Time","PDT","Pacific Daylight Time"],"id":"America/Los_Angeles","transitions":[2770,60,7137,0,11506,60,16041,0,20410,60,24777,0,29146,60,33513,0,35194,60,42249,0,45106,60,50985,0,55354,60,59889,0,64090,60,68625,0,72994,60,77361,0,81730,60,86097,0,90466,60,94833,0,99202,60,103569,0,107938,60,112473,0,116674,60,121209,0,125578,60,129945,0,134314,60,138681,0,143050,60,147417,0,151282,60,156153,0,160018,60,165057,0,168754,60,173793,0,177490,60,182529,0,186394,60,191265,0,195130,60,200001,0,203866,60,208905,0,212602,60,217641,0,221338,60,226377,0,230242,60,235113,0,238978,60,243849,0,247714,60,252585,0,256450,60,261489,0,265186,60,270225,0,273922,60,278961,0,282826,60,287697,0,291562,60,296433,0,300298,60,305337,0,309034,60,314073,0,317770,60,322809,0,326002,60,331713,0,334738,60,340449,0,343474,60,349185,0,352378,60,358089,0,361114,60,366825,0,369850,60,375561,0,378586,60,384297,0,387322,60,393033,0,396058,60,401769,0,404962,60,410673,0,413698,60,419409,0,422434,60,428145,0,431170,60,436881,0,439906,60,445617,0,448810,60,454521,0,457546,60,463257,0,466282,60,471993,0,475018,60,480729,0,483754,60,489465,0,492490,60,498201,0,501394,60,507105,0,510130,60,515841,0,518866,60,524577,0,527602,60,533313,0,536338,60,542049,0,545242,60,550953,0,553978,60,559689,0,562714,60,568425,0,571450,60,577161,0,580186,60,585897,0,588922,60,594633,0]}},"numberFormatSymbols":{"DECIMAL_SEP":".","PERMILL":"‰","MINUS_SIGN":"-","PERCENT_PATTERN":"#,##0%","INFINITY":"∞","DEF_CURRENCY_CODE":"USD","PLUS_SIGN":"+","CURRENCY_PATTERN":"¤#,##0.00","DECIMAL_PATTERN":"#,##0.###","SCIENTIFIC_PATTERN":"#E0","PERCENT":"%","EXP_SYMBOL":"E","GROUP_SEP":",","NAN":"NaN","ZERO_DIGIT":"0"},"allowTerminalDateSeparator":true,"amPmEnglishAccepted":false,"currencyPrefix":true,"currencyTag":"\"$\"","datePostsAreSuffix":true,"dateTimeWithoutYearPattern":"M/d H:mm","dateWithoutYearPattern":"M/d","dayPost":"","decimalSeparator":".","defaultDatePattern":"M/d/yyyy","defaultDateTimePattern":"M/d/yyyy H:mm:ss","defaultTimePattern":"h:mm:ss am/pm","defaultUiLanguage":"en","exponentSeparator":"E","extraDateSeparator":"","firstDayOfWeek":0,"additionalFonts":[],"additionalFormats":[{"1":5,"2":"yyyy-MM-dd","3":1},{"1":5,"2":"MM-dd-yyyy","3":1},{"1":5,"2":"M/d/yy","3":1},{"1":5,"2":"MM-dd-yy","3":1},{"1":5,"2":"M/d","3":1},{"1":5,"2":"MM-dd","3":1},{"1":5,"2":"d-MMM","3":1},{"1":5,"2":"d-MMM-yyyy","3":1},{"1":5,"2":"MMMM d, yyyy","3":1},{"1":5,"2":"MMMM d","3":1},{"1":5,"2":"MMM-d","3":1},{"1":6,"2":"h:mm:ss am/pm","3":1},{"1":6,"2":"h:mm am/pm","3":1},{"1":6,"2":"H:mm:ss","3":1},{"1":6,"2":"H:mm","3":1},{"1":7,"2":"M/d H:mm","3":1}],"amPmStrings":["AM","PM"],"amString":"AM","monthsFull":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthsShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"pmString":"PM","timePrefix":"","timeSeparator":":","weekdaysFull":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"weekdaysShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"groupingSeparator":",","hourPost":"","minimalDaysInFirstWeek":1,"minusSign":"-","minutePost":"","monthPost":"","negativeParens":true,"percent":"%","periodIsDateSeparator":false,"plusSign":"+","secondPost":"","shortDateFormatSuffix":"","yearPost":"","textInputCurrencySymbol":"$"};</script><script>var chartData = { };chartData['1919199894'] = null;chartData['1001122552'] = null;chartData['451815554'] = null;function initCharts() {chartData['1919199894'] = {'chartId': '1919199894', 'elementId': 'embed_1919199894', 'chartJson': '\x7b\x22view\x22:\x7b\x22columns\x22:\x5b0,1\x5d\x7d,\x22dataTable\x22:\x7b\x22parsedNumHeaders\x22:0,\x22rows\x22:\x5b\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Out of Scope\x22\x7d,\x7b\x22v\x22:8,\x22f\x22:\x228\x22\x7d\x5d\x7d,\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Defects Raised\x22\x7d,\x7b\x22v\x22:1,\x22f\x22:\x221\x22\x7d\x5d\x7d\x5d,\x22cols\x22:\x5b\x7b\x22id\x22:\x22Col0\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22string\x22\x7d,\x7b\x22pattern\x22:\x22General\x22,\x22id\x22:\x22Col1\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22number\x22\x7d\x5d\x7d,\x22chartType\x22:\x22PieChart\x22,\x22options\x22:\x7b\x22pieSliceText\x22:\x22value\x22,\x22slices\x22:\x7b\x220\x22:\x7b\x22color\x22:\x22#999999\x22\x7d,\x221\x22:\x7b\x22color\x22:\x22#6aa84f\x22\x7d\x7d,\x22legendTextStyle\x22:\x7b\x22fontName\x22:\x22Arial\x22,\x22color\x22:\x22#1a1a1a\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false\x7d,\x22legend\x22:\x22right\x22,\x22useFirstColumnAsDomain\x22:true,\x22width\x22:540,\x22is3D\x22:true,\x22title\x22:\x22Sprint 4 Testing\x22,\x22titleTextStyle\x22:\x7b\x22fontName\x22:\x22Arial\x22,\x22color\x22:\x22#000000\x22,\x22fontSize\x22:30,\x22bold\x22:true,\x22alignment\x22:\x22center\x22,\x22italic\x22:false\x7d,\x22height\x22:335\x7d\x7d', 'serializedChartProperties': '\x5bnull,\x5b\x221087747010\x22,\x221007247290\x22\x5d,0,0,0,null,\x5b\x22\x7b\\\x22cols\\\x22:\x5b\x7b\\\x22id\\\x22:\\\x220\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22string\\\x22\x7d,\x7b\\\x22id\\\x22:\\\x221\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22number\\\x22\x7d\x5d,\\\x22rows\\\x22:\x5b\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Out of Scope\\\x22\x7d,\x7b\\\x22v\\\x22:8.0,\\\x22f\\\x22:\\\x228\\\x22\x7d\x5d\x7d,\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Defects Raised\\\x22\x7d,\x7b\\\x22v\\\x22:1.0,\\\x22f\\\x22:\\\x221\\\x22\x7d\x5d\x7d\x5d,\\\x22parsedNumHeaders\\\x22:0\x7d\x22,\x220.6\x22\x5d,0,null,\x5bnull,null,13,null,\x5b\x5b\x22Sprint 4 Testing\x22,\x5bnull,null,null,\x22Arial\x22,30,1,0,null,null,null,1,\x5b-16777216\x5d\x5d\x5d\x5d,\x5b1\x5d,null,\x5b4,null,\x5bnull,null,null,\x22Arial\x22,null,0,0,null,null,null,1,\x5b-15066598\x5d\x5d\x5d,\x5bnull,2,null,null,\x5b\x5b0,\x5bnull,null,\x5b-6710887\x5d\x5d\x5d,\x5b1,\x5bnull,null,\x5b-9787313\x5d\x5d\x5d\x5d,\x5b45.57\x5d\x5d\x5d\x5d', 'fallbackUri': '\/spreadsheets\/d\/1YEgUST0mDphlidLKpxbPuBJ6P4xP5wPQ\/embed\/oimg?newCharts\x3dfalse\x26oid\x3d1919199894', 'chart': null, 'interactionState': ritz_tviz_charts.ChartsExportApi.newInteractionState()}; drawRitzChart( chartData['1919199894'], 'en_US','\x7b\x22enableHighPrecisionTrendLines\x22:true,\x22enableOverflowLegendHover\x22:false,\x22enableChartWebFonts\x22:false\x7d', 540.0 , 335.0 ,'\x5b6,\x22Calibri\x22,\x5b\x5b1,\x5b2,0\x5d\x5d,\x5b2,\x5b2,16777215\x5d\x5d,\x5b3,\x5b2,5210557\x5d\x5d,\x5b4,\x5b2,12603469\x5d\x5d,\x5b5,\x5b2,10206041\x5d\x5d,\x5b6,\x5b2,8414370\x5d\x5d,\x5b7,\x5b2,4959430\x5d\x5d,\x5b8,\x5b2,16225862\x5d\x5d,\x5b9,\x5b2,255\x5d\x5d\x5d\x5d', true );chartData['1001122552'] = {'chartId': '1001122552', 'elementId': 'embed_1001122552', 'chartJson': '\x7b\x22view\x22:\x7b\x22columns\x22:\x5b0,1\x5d\x7d,\x22dataTable\x22:\x7b\x22parsedNumHeaders\x22:0,\x22rows\x22:\x5b\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Out of Scope\x22\x7d,\x7b\x22v\x22:\x22#REF!\x22\x7d\x5d\x7d\x5d,\x22cols\x22:\x5b\x7b\x22id\x22:\x22Col0\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22string\x22\x7d,\x7b\x22id\x22:\x22Col1\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22string\x22\x7d\x5d\x7d,\x22chartType\x22:\x22PieChart\x22,\x22options\x22:\x7b\x22pieSliceText\x22:\x22value\x22,\x22slices\x22:\x7b\x220\x22:\x7b\x22color\x22:\x22#999999\x22\x7d\x7d,\x22legendTextStyle\x22:\x7b\x22color\x22:\x22#1a1a1a\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22legend\x22:\x22right\x22,\x22useFirstColumnAsDomain\x22:true,\x22width\x22:540,\x22is3D\x22:true,\x22title\x22:\x22Sprint 4 API Testing\x22,\x22titleTextStyle\x22:\x7b\x22color\x22:\x22#000000\x22,\x22fontSize\x22:30,\x22bold\x22:true,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22height\x22:335\x7d\x7d', 'serializedChartProperties': '\x5bnull,\x5b\x221044235293\x22,\x222069697142\x22\x5d,1,0,1,null,\x5b\x22\x7b\\\x22cols\\\x22:\x5b\x7b\\\x22id\\\x22:\\\x220\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22string\\\x22\x7d,\x7b\\\x22id\\\x22:\\\x221\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22string\\\x22\x7d\x5d,\\\x22rows\\\x22:\x5b\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Out of Scope\\\x22\x7d,\x7b\\\x22v\\\x22:null\x7d\x5d\x7d\x5d,\\\x22parsedNumHeaders\\\x22:0\x7d\x22,\x220.6\x22\x5d,0,null,\x5bnull,null,13,null,\x5b\x5b\x22Sprint 4 API Testing\x22,\x5bnull,null,null,null,30,1,0,null,null,null,1,\x5b-16777216\x5d,null,null,0\x5d\x5d\x5d,\x5b1\x5d,null,\x5b4,null,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-15066598\x5d,null,null,0\x5d\x5d,\x5bnull,2,null,null,\x5b\x5b0,\x5bnull,null,\x5b-6710887\x5d\x5d\x5d\x5d,\x5b45.57\x5d\x5d\x5d\x5d', 'fallbackUri': '\/spreadsheets\/d\/1YEgUST0mDphlidLKpxbPuBJ6P4xP5wPQ\/embed\/oimg?newCharts\x3dfalse\x26oid\x3d1001122552', 'chart': null, 'interactionState': ritz_tviz_charts.ChartsExportApi.newInteractionState()}; drawRitzChart( chartData['1001122552'], 'en_US','\x7b\x22enableHighPrecisionTrendLines\x22:true,\x22enableOverflowLegendHover\x22:false,\x22enableChartWebFonts\x22:false\x7d', 540.0 , 335.0 ,'\x5b6,\x22Calibri\x22,\x5b\x5b1,\x5b2,0\x5d\x5d,\x5b2,\x5b2,16777215\x5d\x5d,\x5b3,\x5b2,5210557\x5d\x5d,\x5b4,\x5b2,12603469\x5d\x5d,\x5b5,\x5b2,10206041\x5d\x5d,\x5b6,\x5b2,8414370\x5d\x5d,\x5b7,\x5b2,4959430\x5d\x5d,\x5b8,\x5b2,16225862\x5d\x5d,\x5b9,\x5b2,255\x5d\x5d\x5d\x5d', true );chartData['451815554'] = {'chartId': '451815554', 'elementId': 'embed_451815554', 'chartJson': '\x7b\x22view\x22:\x7b\x22columns\x22:\x5b0,1\x5d\x7d,\x22dataTable\x22:\x7b\x22parsedNumHeaders\x22:3,\x22rows\x22:\x5b\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22To Do\x22\x7d,\x7b\x22v\x22:4,\x22f\x22:\x224\x22\x7d\x5d\x7d,\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22In Progress\x22\x7d,\x7b\x22v\x22:1,\x22f\x22:\x221\x22\x7d\x5d\x7d,\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Code Review\x22\x7d,\x7b\x22v\x22:2,\x22f\x22:\x222\x22\x7d\x5d\x7d,\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22For QA\x22\x7d,\x7b\x22v\x22:0,\x22f\x22:\x220\x22\x7d\x5d\x7d,\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Reopened\x22\x7d,\x7b\x22v\x22:1,\x22f\x22:\x221\x22\x7d\x5d\x7d\x5d,\x22cols\x22:\x5b\x7b\x22id\x22:\x22Col0\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22string\x22\x7d,\x7b\x22pattern\x22:\x22General\x22,\x22id\x22:\x22Col1\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22number\x22\x7d\x5d\x7d,\x22chartType\x22:\x22PieChart\x22,\x22options\x22:\x7b\x22slices\x22:\x7b\x220\x22:\x7b\x22color\x22:\x22#4f81bd\x22\x7d,\x221\x22:\x7b\x22color\x22:\x22#c0504d\x22\x7d,\x222\x22:\x7b\x22color\x22:\x22#9bbb59\x22\x7d,\x223\x22:\x7b\x22color\x22:\x22#8064a2\x22\x7d,\x224\x22:\x7b\x22color\x22:\x22#4bacc6\x22\x7d\x7d,\x22legendTextStyle\x22:\x7b\x22color\x22:\x22#1a1a1a\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22legend\x22:\x22right\x22,\x22useFirstColumnAsDomain\x22:true,\x22width\x22:747,\x22title\x22:\x22\x5bCityland-PRS\x5d Defect Verification Test Report\x22,\x22titleTextStyle\x22:\x7b\x22color\x22:\x22#757575\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22height\x22:462\x7d\x7d', 'serializedChartProperties': '\x5bnull,\x5b\x221960893687\x22,\x22578612727\x22\x5d,0,3,0,null,\x5b\x22\x7b\\\x22cols\\\x22:\x5b\x7b\\\x22id\\\x22:\\\x220\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22string\\\x22\x7d,\x7b\\\x22id\\\x22:\\\x221\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22number\\\x22\x7d\x5d,\\\x22rows\\\x22:\x5b\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22To Do\\\x22\x7d,\x7b\\\x22v\\\x22:4.0,\\\x22f\\\x22:\\\x224\\\x22\x7d\x5d\x7d,\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22In Progress\\\x22\x7d,\x7b\\\x22v\\\x22:1.0,\\\x22f\\\x22:\\\x221\\\x22\x7d\x5d\x7d,\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Code Review\\\x22\x7d,\x7b\\\x22v\\\x22:2.0,\\\x22f\\\x22:\\\x222\\\x22\x7d\x5d\x7d,\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22For QA\\\x22\x7d,\x7b\\\x22v\\\x22:0.0,\\\x22f\\\x22:\\\x220\\\x22\x7d\x5d\x7d,\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Reopened\\\x22\x7d,\x7b\\\x22v\\\x22:1.0,\\\x22f\\\x22:\\\x221\\\x22\x7d\x5d\x7d\x5d,\\\x22parsedNumHeaders\\\x22:3\x7d\x22,\x220.6\x22\x5d,0,null,\x5bnull,null,13,null,\x5b\x5b\x22\x5bCityland-PRS\x5d Defect Verification Test Report\x22,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-9079435\x5d,null,null,0\x5d\x5d\x5d,\x5b1\x5d,null,\x5b4,null,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-15066598\x5d,null,null,0\x5d\x5d,\x5bnull,null,null,null,\x5b\x5b0,\x5bnull,null,\x5b-11566659\x5d\x5d\x5d,\x5b1,\x5bnull,null,\x5b-4173747\x5d\x5d\x5d,\x5b2,\x5bnull,null,\x5b-6571175\x5d\x5d\x5d,\x5b3,\x5bnull,null,\x5b-8362846\x5d\x5d\x5d,\x5b4,\x5bnull,null,\x5b-11817786\x5d\x5d\x5d\x5d\x5d\x5d\x5d', 'fallbackUri': '\/spreadsheets\/d\/1YEgUST0mDphlidLKpxbPuBJ6P4xP5wPQ\/embed\/oimg?newCharts\x3dfalse\x26oid\x3d451815554', 'chart': null, 'interactionState': ritz_tviz_charts.ChartsExportApi.newInteractionState()}; drawRitzChart( chartData['451815554'], 'en_US','\x7b\x22enableHighPrecisionTrendLines\x22:true,\x22enableOverflowLegendHover\x22:false,\x22enableChartWebFonts\x22:false\x7d', 747.0 , 462.0 ,'\x5b6,\x22Calibri\x22,\x5b\x5b1,\x5b2,0\x5d\x5d,\x5b2,\x5b2,16777215\x5d\x5d,\x5b3,\x5b2,5210557\x5d\x5d,\x5b4,\x5b2,12603469\x5d\x5d,\x5b5,\x5b2,10206041\x5d\x5d,\x5b6,\x5b2,8414370\x5d\x5d,\x5b7,\x5b2,4959430\x5d\x5d,\x5b8,\x5b2,16225862\x5d\x5d,\x5b9,\x5b2,255\x5d\x5d\x5d\x5d', true );}function drawRitzChart( chartState, spreadsheetLocale, serializedChartFlags, width, height, serializedWorkbookTheme, enableStandaloneCharts) {if (enableStandaloneCharts) {drawChartComponent(chartState, spreadsheetLocale, serializedChartFlags, width, height, serializedWorkbookTheme); return;}var canvas = document.createElement('canvas'); var chartElement = document.getElementById(chartState.elementId); width = width == 0 ? window.innerWidth : width; height = height == 0 ? window.innerHeight : height; canvas.width = width; canvas.height = height; canvas.id = 'chart_' + chartState.chartId; chartState.chart = ritz_tviz_charts.ChartsExportApi.buildAndLayoutChartFromGvizWrapper( canvas.getContext('2d'), chartState.chartJson, width, height, spreadsheetLocale, serializedChartFlags); if (chartState.chart && ritz_tviz_charts.ChartsExportApi.renderChart( canvas.getContext('2d'), chartState.chart, chartState.interactionState)) {canvas.addEventListener('click', function(e) {handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale);}); canvas.addEventListener('mousemove', function(e) {handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale);}); canvas.addEventListener('mouseleave', function(e) {handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale);}); chartElement.appendChild(canvas);} else {var img = document.createElement('img'); img.setAttribute('src', chartState.fallbackUri); img.setAttribute('width', width); img.setAttribute('height', height); img.id = 'chart_' + chartState.chartId; chartElement.appendChild(img);}chartState.tooltipRenderer = new docs.charts.Tooltip(new docs.zoom.Zoom(), chartElement);}function drawChartComponent(chartState, spreadsheetLocale, serializedChartFlags, width, height, serializedWorkbookTheme) {var chartElement = document.getElementById(chartState.elementId); width = width == 0 ? window.innerWidth : width; height = height == 0 ? window.innerHeight : height; chartState.chartComponent = new waffle.charts.export.RitzExportChartComponent( spreadsheetLocale, serializedChartFlags, new docs.zoom.Zoom()); chartState.chartComponent.render(chartElement); chartState.chartComponent.setSizeFromPrimitives(width, height); chartState.chartComponent.updateFromModel( ritz_tviz_charts.ChartsExportApi.createChartFromChartProperties( chartState.serializedChartProperties, serializedWorkbookTheme));}function layoutChart(chartState, canvas) {ritz_tviz_charts.ChartsExportApi.layoutChart( canvas.getContext('2d'), chartState.chart, chartState.interactionState, canvas.width, canvas.height);}function renderChart(chartState, canvas) {ritz_tviz_charts.ChartsExportApi.renderChart( canvas.getContext('2d'), chartState.chart, chartState.interactionState);}function handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale) {var x = e.clientX - canvas.getBoundingClientRect().left; var y = e.clientY - canvas.getBoundingClientRect().top; var oldState = chartState.interactionState; chartState.interactionState = (e.type == 'click') ? ritz_tviz_charts.ChartsExportApi.onClick(chartState.chart, oldState, x, y) : ritz_tviz_charts.ChartsExportApi.onMouseMove(chartState.chart, oldState, x, y); if(ritz_tviz_charts.ChartsExportApi.isLayoutNeeded( chartState.chart, oldState, chartState.interactionState)) {layoutChart(chartState, canvas);}renderChart(chartState, canvas); if (chartState.tooltipRenderer) {if (e.type == 'mouseleave') {chartState.tooltipRenderer.hide();} else {ritz_tviz_charts.ChartsExportApi.refreshTooltip( chartState.chart, chartState.tooltipRenderer, chartState.interactionState, serializedChartFlags, spreadsheetLocale);}}}function onChartsExportApiLoad() {initCharts();}window['__serializedchartmessages'] = {"p":{"CALL_CHART_REQUIRES_MINIMUM_COLUMNS":2,"CALL_CHART_PIE_SLICE":2,"CALL_CHART_ORG_CHART_EDGE":2,"MSG_CHART_LEGEND_MORE":0,"CALL_CHART_CHART_SUBTITLE":1,"CALL_CHART_ERROR_BARS":1,"MSG_CHART_HISTOGRAM_COUNT":0,"MSG_CHART_NO_DATA_DISPLAY":0,"CALL_CHART_PIE_SLICE_LABEL":1,"CALL_CHART_ERROR_BAR_ITEM":2,"CALL_CHART_STEPPED_LINE_SERIES":1,"CALL_CHART_TREE_MAP_MISSING_PARENT":1,"CALL_CHART_POINT_SERIES":1,"MSG_CHART_HISTOGRAM_INVALID_BUCKET_SIZE":0,"MSG_CHART_TABLE_CHART_CONTENT":0,"MSG_CHART_WATERFALL_CHART_TOTAL_UNLABELED":0,"CALL_CHART_WATERFALL_CHART_POSITIVE_LABELED":1,"CALL_CHART_LINE_SERIES":1,"CALL_CHART_CURVED_TREND_LINE":1,"CALL_CHART_CANDLESTICK_SERIES_ITEM":2,"MSG_CHART_TREE_MAP_MULTIPLE_ROOTS":0,"CALL_CHART_BAR_SERIES_ITEM":2,"MSG_CHART_PIE_CHART":0,"CALL_CHART_RIGHT_VERTICAL_AXIS_TITLE":1,"MSG_CHART_ORG_CHART_CONTENT":0,"MSG_CHART_GAUGE_CHART_CONTENT":0,"MSG_CHART_GRID_CHART_CONTENT":0,"MSG_CHART_LEGEND":0,"CALL_CHART_EMPHASIZED_PIE_SLICE":2,"MSG_CHART_PIE_CHART_CONTENT":0,"CALL_CHART_ANNOTATION_SERIES_ITEM":2,"CALL_CHART_GAUGE_SERIES_ITEM":1,"CALL_CHART_POINT_SERIES_ITEM":2,"CALL_CHART_AREA_SERIES":1,"CALL_CHART_COLUMN_MUST_BE_TEXT":1,"CALL_CHART_ORG_CHART_NODE":1,"CALL_CHART_TOTAL_DATA_LABEL_ITEM":1,"MSG_CHART_RIGHT_VERTICAL_AXIS_TICK_MARKS":0,"CALL_CHART_COLUMN_SERIES_ITEM":2,"MSG_CHART_HORIZONTAL_TICK_LABELS":0,"CALL_CHART_COLUMN_MUST_BE_NUMERIC":1,"CALL_CHART_LEGEND_ITEM":2,"CALL_CHART_HISTOGRAM_COUNT_LABEL":1,"MSG_CHART_WATERFALL_CHART_POSITIVE_UNLABELED":0,"MSG_CHART_LOADING_DISPLAY":0,"CALL_CHART_LINEAR_TREND_LINE":1,"MSG_CHART_HISTOGRAM_INVALID_BUCKET_PERCENTILE":0,"CALL_CHART_WATERFALL_CHART_TOTAL_LABELED":1,"CALL_CHART_COLUMN_SERIES":1,"MSG_CHART_LEFT_VERTICAL_AXIS_TICK_MARKS":0,"CALL_CHART_CANDLESTICK_SERIES":1,"MSG_CHART_GEO_CHART_CONTENT":0,"CALL_CHART_WATERFALL_CHART_NEGATIVE_LABELED":1,"MSG_CHART_INTERNAL_ERROR":0,"CALL_CHART_ANNOTATION_SERIES":1,"MSG_CHART_BOTTOM_HORIZONTAL_AXIS_TICK_MARKS":0,"MSG_CHART_WATERFALL_CHART_NEGATIVE_UNLABELED":0,"CALL_CHART_STEPPED_AREA_SERIES":1,"CALL_CHART_BAR_SERIES":1,"MSG_CHART_TREE_MAP_CHART_CONTENT":0,"MSG_CHART_VERTICAL_TICK_LABELS":0,"CALL_CHART_CHART_TITLE":1,"MSG_CHART_HORIZONTAL_MAJOR_GRIDLINES":0,"CALL_CHART_LEGEND_MORE_ENTRIES":1,"CALL_CHART_LEFT_VERTICAL_AXIS_TITLE":1,"CALL_CHART_TREE_MAP_DUPLICATE_NODE":1,"MSG_CHART_SCORECARD_CHART_CONTENT":0,"MSG_CHART_CHART_AREA":0,"MSG_CHART_VERTICAL_MAJOR_GRIDLINES":0,"CALL_CHART_HORIZONTAL_AXIS_TITLE":1},"m":{"CALL_CHART_REQUIRES_MINIMUM_COLUMNS":"Requires at least {0} column(s) but only {1} provided.","CALL_CHART_PIE_SLICE":"Pie slice {0}, {1} percent.","CALL_CHART_ORG_CHART_EDGE":"Edge from parent {0} to child {1}.","MSG_CHART_LEGEND_MORE":"Legend more","CALL_CHART_CHART_SUBTITLE":"Subtitle: {0}","CALL_CHART_ERROR_BARS":"Error bars for series {0}","MSG_CHART_HISTOGRAM_COUNT":"(count)","MSG_CHART_NO_DATA_DISPLAY":"Add a series to start visualizing your data","CALL_CHART_PIE_SLICE_LABEL":"Label for pie slice {0}","CALL_CHART_ERROR_BAR_ITEM":"Error bar for series {0} item {1}","CALL_CHART_STEPPED_LINE_SERIES":"Stepped line series {0}","CALL_CHART_TREE_MAP_MISSING_PARENT":"Couldn\u0027t find parent row with label: {0}","CALL_CHART_POINT_SERIES":"Point series {0}","MSG_CHART_HISTOGRAM_INVALID_BUCKET_SIZE":"Bucket size is invalid. It must be greater than zero.","MSG_CHART_TABLE_CHART_CONTENT":"Table chart content","MSG_CHART_WATERFALL_CHART_TOTAL_UNLABELED":"Subtotal","CALL_CHART_WATERFALL_CHART_POSITIVE_LABELED":"Positive ({0})","CALL_CHART_LINE_SERIES":"Line series {0}","CALL_CHART_CURVED_TREND_LINE":"Curved trendline for series {0}","CALL_CHART_CANDLESTICK_SERIES_ITEM":"Candlestick series {0} item {1}","MSG_CHART_TREE_MAP_MULTIPLE_ROOTS":"Found two root nodes. Only one root node is allowed.","CALL_CHART_BAR_SERIES_ITEM":"Bar series {0} item {1}","MSG_CHART_PIE_CHART":"Pie chart","CALL_CHART_RIGHT_VERTICAL_AXIS_TITLE":"Right vertical axis title: {0}","MSG_CHART_ORG_CHART_CONTENT":"Org chart content","MSG_CHART_GAUGE_CHART_CONTENT":"Gauge chart content","MSG_CHART_GRID_CHART_CONTENT":"Grid chart content","MSG_CHART_LEGEND":"Legend","CALL_CHART_EMPHASIZED_PIE_SLICE":"Emphasized pie slice {0}, {1} percent.","MSG_CHART_PIE_CHART_CONTENT":"Pie chart content","CALL_CHART_ANNOTATION_SERIES_ITEM":"Annotation for series {0} item {1}","CALL_CHART_GAUGE_SERIES_ITEM":"Gauge series item {0}","CALL_CHART_POINT_SERIES_ITEM":"Points series {0} item {1}","CALL_CHART_AREA_SERIES":"Area series {0}","CALL_CHART_COLUMN_MUST_BE_TEXT":"Column {0} must be text.","CALL_CHART_ORG_CHART_NODE":"Node {0}","CALL_CHART_TOTAL_DATA_LABEL_ITEM":"Total data label item {0}.","MSG_CHART_RIGHT_VERTICAL_AXIS_TICK_MARKS":"Right vertical axis tick marks","CALL_CHART_COLUMN_SERIES_ITEM":"Column series {0} item {1}","MSG_CHART_HORIZONTAL_TICK_LABELS":"Horizontal tick labels","CALL_CHART_COLUMN_MUST_BE_NUMERIC":"Column {0} must be numeric.","CALL_CHART_LEGEND_ITEM":"Legend entry {0}: {1}","CALL_CHART_HISTOGRAM_COUNT_LABEL":"{0} (count)","MSG_CHART_WATERFALL_CHART_POSITIVE_UNLABELED":"Positive","MSG_CHART_LOADING_DISPLAY":"Loading…","CALL_CHART_LINEAR_TREND_LINE":"Linear trendline for series {0}","MSG_CHART_HISTOGRAM_INVALID_BUCKET_PERCENTILE":"Bucket percentile is invalid. It must be between zero and one hundred percent.","CALL_CHART_WATERFALL_CHART_TOTAL_LABELED":"Subtotal ({0})","CALL_CHART_COLUMN_SERIES":"Column series {0}","MSG_CHART_LEFT_VERTICAL_AXIS_TICK_MARKS":"Left vertical axis tick marks","CALL_CHART_CANDLESTICK_SERIES":"Candlestick series {0}","MSG_CHART_GEO_CHART_CONTENT":"Geo chart content","CALL_CHART_WATERFALL_CHART_NEGATIVE_LABELED":"Negative ({0})","MSG_CHART_INTERNAL_ERROR":"An internal error has occurred while rendering the chart.","CALL_CHART_ANNOTATION_SERIES":"Annotations for series {0}","MSG_CHART_BOTTOM_HORIZONTAL_AXIS_TICK_MARKS":"Bottom horizontal axis tick marks","MSG_CHART_WATERFALL_CHART_NEGATIVE_UNLABELED":"Negative","CALL_CHART_STEPPED_AREA_SERIES":"Stepped area series {0}","CALL_CHART_BAR_SERIES":"Bar series {0}","MSG_CHART_TREE_MAP_CHART_CONTENT":"Treemap chart content","MSG_CHART_VERTICAL_TICK_LABELS":"Vertical tick labels","CALL_CHART_CHART_TITLE":"Title: {0}","MSG_CHART_HORIZONTAL_MAJOR_GRIDLINES":"Horizontal major gridlines","CALL_CHART_LEGEND_MORE_ENTRIES":"{0} more","CALL_CHART_LEFT_VERTICAL_AXIS_TITLE":"Left vertical axis title: {0}","CALL_CHART_TREE_MAP_DUPLICATE_NODE":"Found two entries with the same label: {0}","MSG_CHART_SCORECARD_CHART_CONTENT":"Scorecard chart content","MSG_CHART_CHART_AREA":"Chart area","MSG_CHART_VERTICAL_MAJOR_GRIDLINES":"Vertical major gridlines","CALL_CHART_HORIZONTAL_AXIS_TITLE":"Horizontal axis title: {0}"}};</script><script>var CHARTS_EXPORT_URI = [];CHARTS_EXPORT_URI.push('resources\/404429202-ChartsExportJ2cl_j2cl_core.js');
    if (window.addEventListener) {
      window.addEventListener('load',
        function() {
          window.tvizScriptLoader.load();
        });
    }
    </script>