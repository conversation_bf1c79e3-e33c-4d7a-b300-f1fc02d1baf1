/* ===== CODE BLOCK STYLING ===== */
.md-typeset pre {
  font-size: 0.9em;
  line-height: 1.5;
  border-radius: 4px;
}

/* Increase code font size for better readability */
.md-typeset code {
  font-size: 0.9em;
}

/* Add a subtle background to inline code */
:root {
  --md-code-bg-color: rgba(175, 184, 193, 0.2);
}

[data-md-color-scheme="slate"] {
  --md-code-bg-color: rgba(101, 109, 118, 0.2);
}

/* Improve code block padding */
.md-typeset pre > code {
  padding: 1em;
}

/* Add a subtle border to code blocks */
.md-typeset pre {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-md-color-scheme="slate"] .md-typeset pre {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Improve line number styling */
.linenums ol {
  margin-left: 0;
  padding-left: 0.5em;
}

.linenums li {
  padding-left: 0.5em;
}

/* Highlight the current line when hovering */
.md-typeset .highlight:hover .hll {
  background-color: rgba(255, 255, 0, 0.1);
}

/* Improve code block title styling */
.md-typeset .highlight .filename {
  font-family: var(--md-text-font-family);
  font-weight: 600;
  padding: 0.5em 1em;
  background-color: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

[data-md-color-scheme="slate"] .md-typeset .highlight .filename {
  background-color: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Syntax highlighting colors - light theme */
:root {
  --md-code-hl-number-color: #005cc5;
  --md-code-hl-special-color: #6f42c1;
  --md-code-hl-function-color: #6f42c1;
  --md-code-hl-constant-color: #005cc5;
  --md-code-hl-keyword-color: #d73a49;
  --md-code-hl-string-color: #032f62;
  --md-code-hl-name-color: #22863a;
  --md-code-hl-operator-color: #d73a49;
  --md-code-hl-punctuation-color: #24292e;
  --md-code-hl-comment-color: #6a737d;
  --md-code-hl-generic-color: #6a737d;
  --md-code-hl-variable-color: #e36209;
}

/* Syntax highlighting colors - dark theme */
[data-md-color-scheme="slate"] {
  --md-code-hl-number-color: #79b8ff;
  --md-code-hl-special-color: #b392f0;
  --md-code-hl-function-color: #b392f0;
  --md-code-hl-constant-color: #79b8ff;
  --md-code-hl-keyword-color: #f97583;
  --md-code-hl-string-color: #9ecbff;
  --md-code-hl-name-color: #85e89d;
  --md-code-hl-operator-color: #f97583;
  --md-code-hl-punctuation-color: #e1e4e8;
  --md-code-hl-comment-color: #959da5;
  --md-code-hl-generic-color: #959da5;
  --md-code-hl-variable-color: #ffab70;
}

/* Copy button styling */
.md-clipboard {
  color: rgba(0, 0, 0, 0.5);
}

.md-clipboard:hover {
  color: var(--md-accent-fg-color);
}

[data-md-color-scheme="slate"] .md-clipboard {
  color: rgba(255, 255, 255, 0.5);
}

/* Improve table styling for code blocks with line numbers */
.highlighttable {
  display: block;
  overflow-x: auto;
}

.highlighttable tbody, .highlighttable tr {
  display: block;
  width: 100%;
}

.highlighttable td.linenos {
  padding-right: 0.5em;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  text-align: right;
  color: rgba(0, 0, 0, 0.4);
  width: 3em;
}

[data-md-color-scheme="slate"] .highlighttable td.linenos {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
}

.highlighttable td.code {
  padding-left: 0.5em;
  width: calc(100% - 3.5em);
}

/* ===== SIDEBAR NAVIGATION STYLING ===== */

/* Define custom colors for light and dark themes */
:root {
  --md-sidebar-item-color: rgba(0, 0, 0, 0.54);
  --md-sidebar-active-color: var(--md-primary-fg-color);
  --md-sidebar-section-color: rgba(0, 0, 0, 0.87);
  --md-sidebar-border-color: rgba(0, 0, 0, 0.07);
  --md-sidebar-hover-bg: rgba(0, 0, 0, 0.035);
  --md-sidebar-category-color: rgba(0, 0, 0, 0.76);
  --md-sidebar-subcategory-color: rgba(0, 0, 0, 0.66);
  --md-sidebar-category-bg: rgba(0, 0, 0, 0.02);
  --md-sidebar-subcategory-bg: rgba(0, 0, 0, 0.01);
}

[data-md-color-scheme="slate"] {
  --md-sidebar-item-color: rgba(255, 255, 255, 0.6);
  --md-sidebar-active-color: var(--md-primary-fg-color);
  --md-sidebar-section-color: rgba(255, 255, 255, 0.9);
  --md-sidebar-border-color: rgba(255, 255, 255, 0.07);
  --md-sidebar-hover-bg: rgba(255, 255, 255, 0.05);
  --md-sidebar-category-color: rgba(255, 255, 255, 0.85);
  --md-sidebar-subcategory-color: rgba(255, 255, 255, 0.75);
  --md-sidebar-category-bg: rgba(255, 255, 255, 0.03);
  --md-sidebar-subcategory-bg: rgba(255, 255, 255, 0.02);
}

/* Top-level section headers (System Guide, How-To Guides, etc.) */
.md-nav--primary > .md-nav__list > .md-nav__item > .md-nav__link {
  font-weight: 700;
  font-size: 1rem;
  color: var(--md-sidebar-section-color) !important;
  padding: 8px 0;
  margin-top: 16px;
  margin-bottom: 8px;
  border-bottom: 2px solid var(--md-primary-fg-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Second-level section headers (Architecture, Business Rules, etc.) */
.md-nav--primary > .md-nav__list > .md-nav__item > .md-nav > .md-nav__list > .md-nav__item > .md-nav__link {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--md-sidebar-category-color) !important;
  padding: 6px 8px;
  margin-top: 8px;
  margin-bottom: 4px;
  background-color: var(--md-sidebar-category-bg);
  border-radius: 4px;
  border-left: 3px solid var(--md-primary-fg-color);
}

/* Third-level section headers (Overview, Requisition, etc.) */
.md-nav--primary > .md-nav__list > .md-nav__item > .md-nav > .md-nav__list > .md-nav__item > .md-nav > .md-nav__list > .md-nav__item > .md-nav__link {
  font-weight: 500;
  font-size: 0.85rem;
  color: var(--md-sidebar-subcategory-color) !important;
  padding: 4px 8px;
  margin: 2px 0;
  background-color: var(--md-sidebar-subcategory-bg);
  border-radius: 4px;
}

/* Add visual separation between sections */
.md-nav__list .md-nav__list {
  margin-left: 0.6rem;
  padding-left: 0.6rem;
  border-left: 1px solid var(--md-sidebar-border-color);
}

/* Style for regular nav items */
.md-nav__item .md-nav__link {
  margin: 0.3rem 0;
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.15s, color 0.15s;
}

/* Hover effect for nav items */
.md-nav__item .md-nav__link:hover {
  background-color: var(--md-sidebar-hover-bg);
}

/* Active nav item */
.md-nav__item .md-nav__link--active {
  font-weight: 600;
  color: var(--md-sidebar-active-color) !important;
  background-color: var(--md-sidebar-hover-bg);
}

/* Top-level nav items (tabs) */
.md-tabs__item {
  font-weight: 600;
}

/* Improve spacing in the sidebar */
.md-nav__title {
  padding-bottom: 1rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--md-sidebar-border-color);
}

/* Improve sidebar toggle button */
.md-nav__toggle {
  color: var(--md-sidebar-item-color);
}

/* Improve sidebar scrollbar */
.md-sidebar__scrollwrap::-webkit-scrollbar {
  width: 4px;
}

.md-sidebar__scrollwrap::-webkit-scrollbar-thumb {
  background: var(--md-sidebar-border-color);
  border-radius: 4px;
}

/* Improve indentation for nested navigation */
.md-nav__list .md-nav__list .md-nav__list {
  margin-left: 0.4rem;
  padding-left: 0.4rem;
}

/* Add visual indicator for expandable sections */
.md-nav__item .md-nav__link--active + .md-nav {
  border-left: 2px solid var(--md-sidebar-active-color);
  margin-left: 0.3rem;
  padding-left: 0.7rem;
}

/* Improve spacing between nav items */
.md-nav__item {
  padding: 0;
  margin: 0.2rem 0;
}

/* Add icons to main sections */
.md-nav--primary > .md-nav__list > .md-nav__item:nth-child(1) > .md-nav__link::before {
  content: "🏠 ";
}

.md-nav--primary > .md-nav__list > .md-nav__item:nth-child(2) > .md-nav__link::before {
  content: "📚 ";
}

.md-nav--primary > .md-nav__list > .md-nav__item:nth-child(3) > .md-nav__link::before {
  content: "📝 ";
}

.md-nav--primary > .md-nav__list > .md-nav__item:nth-child(4) > .md-nav__link::before {
  content: "🔄 ";
}

.md-nav--primary > .md-nav__list > .md-nav__item:nth-child(5) > .md-nav__link::before {
  content: "⚡ ";
}

/* Add visual grouping for System Guide subsections */
.md-nav--primary > .md-nav__list > .md-nav__item:nth-child(2) > .md-nav > .md-nav__list {
  background-color: rgba(63, 81, 181, 0.03);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid rgba(63, 81, 181, 0.1);
}

/* ===== SMALL STICKY FOOTER STYLING ===== */

/* Set up the page layout for sticky footer */
html {
  height: 100%;
}

body {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* Make the main content area flex to fill available space */
.md-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.md-main {
  flex: 1;
}

/* Make the footer small and sticky at the bottom */
.md-footer {
  position: sticky !important;
  bottom: 0 !important;
  margin-top: auto !important;
  z-index: 1000 !important;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.1) !important;
  border-top: 1px solid var(--md-default-fg-color--lightest) !important;
  min-height: auto !important;
}

/* Small footer content styling */
.md-footer-meta {
  padding: 4px 16px !important;
  min-height: auto !important;
}

/* Small footer text styling */
.md-footer-copyright {
  font-size: 0.6rem !important;
  text-align: center !important;
  margin: 0 !important;
  line-height: 1.2 !important;
}

/* Hide navigation footer elements */
.md-footer__inner {
  display: none !important;
}

/* Dark mode footer adjustments */
[data-md-color-scheme="slate"] .md-footer {
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.2) !important;
  border-top: 1px solid var(--md-default-fg-color--lightest) !important;
}

/* Responsive adjustments for mobile */
@media screen and (max-width: 76.1875em) {
  .md-footer-meta {
    padding: 6px 16px !important;
  }

  .md-footer-copyright {
    font-size: 0.65rem !important;
  }
}

/* Improve footer scrollbar visibility on hover */
.md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover {
  background: var(--md-primary-fg-color);
}

/* ===== MAIN CONTENT PADDING ===== */

/* Add more left/right padding to main content */
.md-main__inner {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

/* Additional spacing for content */
.md-content {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Responsive padding adjustments */
@media screen and (max-width: 76.1875em) {
  .md-main__inner {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }

  .md-content {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

@media screen and (max-width: 44.9375em) {
  .md-main__inner {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .md-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

/* ===== SIMPLE SIDEBAR FIX ===== */

/* Make sidebar sticky and appear above footer */
.md-sidebar {
  position: sticky !important;
  top: 0 !important;
  z-index: 1001 !important;
  height: calc(100vh - 30px) !important; /* Account for small footer */
  overflow: hidden !important;
}

.md-sidebar--primary {
  z-index: 1001 !important;
}

.md-sidebar--secondary {
  z-index: 1001 !important;
}

/* Make sidebar content scrollable */
.md-sidebar__scrollwrap {
  height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  z-index: 1001 !important;
  padding-bottom: 20px !important;
}

/* Ensure header stays on top */
.md-header {
  z-index: 1002 !important;
}

/* Ensure search and other header elements stay on top */
.md-search {
  z-index: 1003 !important;
}

.md-search__overlay {
  z-index: 1003 !important;
}

.md-search__form {
  z-index: 1003 !important;
}

/* ===== BACK TO TOP BUTTON ===== */

/* Back to top button styling */
.back-to-top {
  position: fixed !important;
  bottom: 50px !important; /* Above the footer */
  right: 30px !important;
  width: 50px !important;
  height: 50px !important;
  background-color: var(--md-primary-fg-color) !important;
  color: white !important;
  border: none !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  z-index: 999 !important; /* Below footer but above content */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateY(20px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 20px !important;
  line-height: 1 !important;
}

/* Show button when scrolled */
.back-to-top.visible {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateY(0) !important;
}

/* Hover effects */
.back-to-top:hover {
  background-color: var(--md-accent-fg-color) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

/* Dark mode adjustments */
[data-md-color-scheme="slate"] .back-to-top {
  background-color: var(--md-primary-fg-color) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-md-color-scheme="slate"] .back-to-top:hover {
  background-color: var(--md-accent-fg-color) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4) !important;
}

/* Mobile adjustments */
@media screen and (max-width: 76.1875em) {
  .back-to-top {
    bottom: 40px !important;
    right: 20px !important;
    width: 45px !important;
    height: 45px !important;
    font-size: 18px !important;
  }
}

@media screen and (max-width: 44.9375em) {
  .back-to-top {
    bottom: 35px !important;
    right: 15px !important;
    width: 40px !important;
    height: 40px !important;
    font-size: 16px !important;
  }
}
