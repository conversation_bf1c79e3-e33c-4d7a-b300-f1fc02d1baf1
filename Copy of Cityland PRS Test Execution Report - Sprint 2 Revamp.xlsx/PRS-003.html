<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="665557538C0" style="width:103px;" class="column-headers-background">A</th><th id="665557538C1" style="width:247px;" class="column-headers-background">B</th><th id="665557538C2" style="width:68px;" class="column-headers-background">C</th><th id="665557538C3" style="width:252px;" class="column-headers-background">D</th><th id="665557538C4" style="width:233px;" class="column-headers-background">E</th><th id="665557538C5" style="width:330px;" class="column-headers-background">F</th><th id="665557538C6" style="width:78px;" class="column-headers-background">G</th><th id="665557538C7" style="width:396px;" class="column-headers-background">H</th><th id="665557538C8" style="width:100px;" class="column-headers-background">I</th><th id="665557538C9" style="width:88px;" class="column-headers-background">J</th><th id="665557538C10" style="width:136px;" class="column-headers-background">K</th><th id="665557538C11" style="width:136px;" class="column-headers-background">L</th><th id="665557538C12" style="width:136px;" class="column-headers-background">M</th><th id="665557538C13" style="width:136px;" class="column-headers-background">N</th><th id="665557538C14" style="width:78px;" class="column-headers-background">O</th><th id="665557538C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="665557538R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="665557538R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-003 - User Management - Manage IT Admin and Root User, Account Creation, User Deactivation</td></tr><tr style="height: 19px"><th id="665557538R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">TC-003-01</td><td class="s5">[USER MANAGEMENT] [ROOT USER}  IT Admin User Management Landing Page</td><td class="s5">Critical</td><td class="s5">Verify IT  Admin User Management display of Root user</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Check the IT Admin Management Display<br>2. Validate &quot;Search&quot; functionality<br>3. Validate &quot;Clear&quot; button<br>4. Validate &quot;Create a New Admin&quot; button<br>5. Validate sorting of Table Columns</td><td class="s7"></td><td class="s5">1. Should display IT Admin User Management after successful Login<br>2. Should display a Page Header &quot;IT Admin Management&quot;<br>3. Should have a Search Field<br>    a. Search is applied for Username, First Name, and Last Name<br>        i. Should be triggered by clicking Search Button<br>        ii. Display matched User, else display a No Data in the Table<br>    b. Should be cleared by clicking Clear Button<br>4. Should display &quot;Create a New Admin&quot; button<br>    a. Should display a new modal form when clicked<br>5. Should display a Table with the Columns for<br>    a. User&#39;s Username<br>    b. User&#39;s First Name<br>    c. User&#39;s Last Name<br>    d. Email Address<br>    e. User Type<br>    f. Department<br>    g. Status<br>    h. Actions<br>       i. Edit Button<br>6. Should have a Default sorting Alphabetically of Last Name (A-Z) and Status (Active-Inactive)<br>7. Should display 10 Rows of Data per Page <br>8. Should display the Total Number of Data per Page against Total Table Data</td><td class="s6" rowspan="38"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=1476672164#gid=1476672164">https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=1476672164#gid=1476672164</a></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">TC-003-02</td><td class="s5">[USER MANAGEMENT] [ROOT USER} Root User -  IT Admin User Creation</td><td class="s5">Critical</td><td class="s5">Verify IT  Admin User Creation display for Root user</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Check the IT Admin Management Display<br>2. Click  &quot;Create a New Admin&quot; button<br>3. Check the &quot;Create a New Admin&quot;  Modal display<br>4. Validate all fields in &quot;Create a New Admin&quot; Modal<br>5. Validate &quot;Cancel&quot; button <br>6. Validate &quot;Confirm&quot; button </td><td class="s7"></td><td class="s5">1. Should display a Table List of registered IT Admins in the App<br>2. Should display Create New Admin Modal and have the following Fields:<br>    a. Username<br>    b. First Name<br>    c. Last Name<br>    d. User Type<br>    e. Email Address<br>    f. Supervisor<br>     g. Department  <br>3. Should follow Field validations:<br>    a. Username<br>        i. Should allow Alphanumeric and Special Characters except Emojis<br>        ii. Should have a Minimum of 5 Characters and Maximum of 50 Characters<br>        iii. Should not allow spaces<br>        iv. Should require the Field to proceed with User creation<br>    b. First Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    c. Last Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    d. User Type<br>        i. Should be not editable with a Default value of IT Admin<br>    e. Email Address<br>        i. Alphanumeric and Special Characters exept Emojis<br>        ii. Require Email Address Domain<br>        iii. Must accept a maximum of 100 Characters<br>    f. Department<br>        i. Drop-down Values of Departments synced in Departments Menu<br>4. Should have Confirm and Cancel Buttons<br>    a. If Cancel Button is clicked, should display a Confirmation Modal <br>        i. Once Confirmed, should cancel Account creation of the User and redirect Back to the User List<br>    b. If Confiirm Button is clicked, should check Validations<br>        i. If with Errors, highlight the Field and display an Error Message<br>        ii. If without Errors, should display a Confirmation Modal<br>            i) Once Confirmed, Add the User in the User List and allow them to access their Accounts<br>            ii) Should display a Success Toast Message and automatically close after 3 seconds<br>            iii) Should have a Temporary Password once created with a combination of Alphanumeric and Special Characters except Emojis</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">3/11: Fixed Issue<br><br>Bug has been raised</td><td class="s5">CITYLANDPRS-768<br>CITYLANDPRS-773</td></tr><tr style="height: 19px"><th id="665557538R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">TC-003-03</td><td class="s5">[USER MANAGEMENT] [ROOT USER} Root User -  IT Admin User Creation</td><td class="s5">High</td><td class="s5">Verify IT  Admin User Creation when user  did not entered email address </td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Check the IT Admin Management Display<br>2. Click  &quot;Create a New Admin&quot; button<br>3. Populate all fields except &quot;Email Address&quot; field<br>4. Click &quot;Confirm&quot; button in Create a New Admin Modal<br>5. Click &quot;Cancel&quot; button in the confirmation modal<br>6. Click &quot;Confirm&quot; button again n Create a New Admin Modal<br>7. Click &quot;Continue in the confirmation modal</td><td class="s7"></td><td class="s5">1. Should display a Table List of registered IT Admins in the App<br>2. Should display Create New Admin Modal and have the following Fields:<br>    a. Username<br>    b. First Name<br>    c. Last Name<br>    d. User Type<br>    e. Email Address<br>     f. Department  <br>3. All fields should be populated except &quot;Email Address&quot; field<br>4; Should Display a Confirmation Modal that contains the ff:<br>    a. A description &quot;You are about to create a new user. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>   b. Cancel and Confirm buttons<br>5.User should redirected back to &quot;Create a New Admin&quot; Modal<br>6. Should Display a Confirmation Modal again <br>7. Should be able to create a new user with success toast mesage and new created user should be added on the list</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">TC-003-04</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User -  IT Admin User Creation</td><td class="s5">Critical</td><td class="s5">Verify IT  Admin User Creation when user populate all fields</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Check the IT Admin Management Display<br>2. Click  &quot;Create a New Admin&quot; button<br>3. Populate all fields <br>4. Click &quot;Confirm&quot; button in Create a New Admin Modal<br>5. Click &quot;Cancel&quot; button in the confirmation modal<br>6. Click &quot;Confirm&quot; button again n Create a New Admin Modal<br>7. Click &quot;Continue in the confirmation modal</td><td class="s7"></td><td class="s5">1. Should display a Table List of registered IT Admins in the App<br>2. Should display Create New Admin Modal and have the following Fields:<br>    a. Username<br>    b. First Name<br>    c. Last Name<br>    d. User Type<br>    e. Email Address<br>     f. Department  <br>3. All fields should be populated<br>4; Should Display a Confirmation Modal that contains the ff:<br>    a. A description &quot;You are about to create a new user. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>   b. Cancel and Confirm buttons<br>5.User should redirected back to &quot;Create a New Admin&quot; Modal<br>6. Should Display a Confirmation Modal again <br>7. Should be able to create a new user with success toast mesage and new created user should be added on the list</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">TC-003-05</td><td class="s13">[USER MANAGEMENT] [ROOT USER] Root User -  IT Admin Edit User Details</td><td class="s13">Critical</td><td class="s13">Verify IT  Admin User Edit display for Root user</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Check Non-editable fields<br>5. Check Editable fields<br>6. Validate all editable fields<br>7. Check buttons displayed on &quot;Admin Details - Edit&quot; Modal</td><td class="s15"></td><td class="s13">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Should display a non-editable Fields for Username and User Type<br>5. Should allow editing of User&#39;s Information and Status for First Name, Last name, Email address, Department and Status text and dropdown Fields<br>6. Should follow Field validations:<br>    a. First Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    b. Last Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    c. Email Address<br>        i. Alphanumeric and Special Characters except Emojis<br>        ii. Require Email Address Domain<br>        iii. Must accept a maximum of 100 Characters<br>    d. Department<br>        i. Drop-down Values of Departments synced in Departments Menu<br>    e. Status<br>        i. Dropdown for Inactive and Active status<br>    f. Should require all Fields to proceed except email address<br>7. Should have Close, Save and Cancel Buttons<br>    a. If Close Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should close the Edit User Details Modal<br>    b. If Cancel Button is clicked, should display a Confirmation Modal<br>        i. Once confirmed, cancel updating of User&#39;s Account and redirect Back to the User List<br>    b. If Submit Button is clicked, should check Validations<br>        i. If with Errors, highlight the Field and display an Error Message<br>        ii. If without Errors, should display a Confirmation Modal<br>            i) Once confirmed, should edit the User&#39;s Information<br>            ii) Should display a Success Toast Message and automatically close after 3 seconds</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">3/11: Fixed Issue<br><br>Bug has been raised</td><td class="s5">CITYLANDPRS-773</td></tr><tr style="height: 19px"><th id="665557538R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">TC-003-06</td><td class="s13">[USER MANAGEMENT] [ROOT USER] Root User -  IT Admin Edit User Details</td><td class="s13">High</td><td class="s13">Verify Root User Edit Confirmation Modal when user clicked close button</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields<br>   -First Name<br>   -Last Name<br>   -Email Address<br>   -Department<br>   -Status<br>5. Click  &quot;Close&quot;  button<br>6. Click &quot;Cancel&quot; button on Cancel Changes Modal <br>7. Click again the &quot;Close&quot; button in &quot;Admin Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button</td><td class="s15"></td><td class="s13">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Fields should be updated<br>5.Should display a &quot;Cancel Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;Admin Details - Edit&quot; Modal<br>7. Should display again the &quot;Cancel Changes&quot; Modal<br>8. Should close the &quot;Admin Details - Edit&quot; Modal<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">TC-003-07</td><td class="s13">[USER MANAGEMENT] [ROOT USER] Root User -  IT Admin Edit User Details</td><td class="s13">High</td><td class="s13">Verify Root User Edit Confirmation Modal when user clicked cancel button</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields<br>   -First Name<br>   -Last Name<br>   -Email Address<br>   -Department<br>   -Status<br>5. Click  &quot;Cancel&quot;  button<br>6. Click &quot;Cancel&quot; button on Cancel Changes Modal <br>7. Click again the &quot;Cancel&quot; button in &quot;Admin Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button</td><td class="s15"></td><td class="s13">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Fields should be updated<br>5.Should display a &quot;Cancel Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;Admin Details - Edit&quot; Modal<br>7. Should display again the &quot;Cancel Changes&quot; Modal<br>8. Should close the &quot;Admin Details - Edit&quot; Modal and redirected back to user list<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5">TC-003-08</td><td class="s13">[USER MANAGEMENT] [ROOT USER] Root User -  IT Admin Edit User Details</td><td class="s13">Critical</td><td class="s13">Verify Root User Edit Confirmation Modal when user clicked save button</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields<br>   -First Name<br>   -Last Name<br>   -Email Address<br>   -Department<br>   -Status<br>5. Click  &quot;Save&quot;  button<br>6. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>7. Click again the &quot;Save&quot; button in &quot;Admin Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button</td><td class="s15"></td><td class="s13">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Fields should be updated<br>5.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;Admin Details - Edit&quot; Modal<br>7. Should display again the &quot;Confirm Changes&quot; Modal<br>8. Should display a Success Toast Message and automatically close after 3 seconds<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5">TC-003-09</td><td class="s13">[USER MANAGEMENT] [ROOT USER] Root User -  IT Admin Edit User Details</td><td class="s13">Critical</td><td class="s13">Verify Root User Editl when user edit and save with blank editable field</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields <br>   -First Name<br>   -Last Name<br>   -Email Address<br>   -Department<br>   -Status<br>5. Leave 1 blank field<br>6. Click  &quot;Save&quot;  button<br>7. Click &quot;Continue&quot; button in  Confirm Changes Modal </td><td class="s15"></td><td class="s13">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Fields should be updated<br>5. 1 blank field should be displayed<br>6.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should highlight the blank Field and display an Error Message<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5">TC-003-10</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - Deactivate an IT Admin User from Edit Modal</td><td class="s5">Critical</td><td class="s5"> Verify Root User - Deactivate an IT Admin User from Edit Modal</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Update the Status dropdown field and select &quot;Inactive&quot;<br>5. Click  &quot;Save&quot;  button<br>6. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>7. Click again the &quot;Save&quot; button in &quot;Admin Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button<br>9. Request the Inactive user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Status field should be Inactive<br>5.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;Admin Details - Edit&quot; Modal<br>7. Should display again the &quot;Confirm Changes&quot; Modal<br>8. Should display a Success Toast Message and automatically close after 3 seconds<br>9. User should be Inactive and not be able to login to Cityland-PRS </td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">TC-003-11</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - Deactivate an IT Admin User from Status Column</td><td class="s5">Critical</td><td class="s5"> Verify Root User - Reactivate an IT Admin User from Edit Modal</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column <br>3. Click &quot;Edit&quot; button on &quot;Admin Details - View&quot; Modal<br>4. Update the Status of Inactive user to &quot;Active&quot;<br>5. Click  &quot;Save&quot;  button<br>6. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>7. Click again the &quot;Save&quot; button in &quot;Admin Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button<br>9. Request the Reactived  user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to IT Admin Management Page<br>2. &quot;Admin Details - View&quot; Modal should be displayed<br>3. Should display the &quot;Admin Details - Edit&quot; Modal<br>4. Status field should be Active<br>5.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;Admin Details - Edit&quot; Modal<br>7. Should display again the &quot;Confirm Changes&quot; Modal<br>8. Should display a Success Toast Message and automatically close after 3 seconds<br>9. User should be Active again and able to login to Cityland-PRS </td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5">TC-003-12</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - Deactivate an IT Admin User from Status Column</td><td class="s5">Critical</td><td class="s5"> Verify Root User - Deactivate an IT Admin User from Status Column</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Select a user to :&quot;Inactive&quot; on Status dropdown <br>3. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>4. Select again a user to :&quot;Inactive&quot; on Status dropdown in IT Admin Management page<br>5. Click &quot;Continue&quot; button<br>6. Request the Inactive user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to IT Admin Management Page<br>2.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes for [First and Last Name]. Make sure all items are correct. Press continue if you want to proceed with this action..&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>3. Should redirected back to &quot;IT Admin Management&quot; Page<br>4. Should display again the &quot;Confirm Changes&quot; Modal<br>5. Should display a Success Toast Message and automatically close after 3 seconds<br>6. User should be Inactive and not be able to login to Cityland-PRS </td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Fixed Issue</td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-791">CITYLANDPRS-791</a></td></tr><tr style="height: 19px"><th id="665557538R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5">TC-003-13</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - Deactivate an IT Admin User from Status Column</td><td class="s5">Critical</td><td class="s5"> Verify Root User - Reactivate an IT Admin User from Status Column</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Select an inactive user to &quot;Active&quot; in Status dropdown <br>3. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>4. Select again an inactive user to :&quot;Active&quot; on Status dropdown in IT Admin Management page<br>5. Click &quot;Continue&quot; button<br>6. Request the Reactived user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to IT Admin Management Page<br>2.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes for [First and Last Name]. Make sure all items are correct. Press continue if you want to proceed with this action..&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>3. Should redirected back to &quot;IT Admin Management&quot; Page<br>4. Should display again the &quot;Confirm Changes&quot; Modal<br>5. Should display a Success Toast Message and automatically close after 3 seconds<br>6. User should be active and able to login to Cityland-PRS </td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">TC-003-14</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - View IT Admin User Details</td><td class="s5">Critical</td><td class="s5">Verify Root User - View IT Admin User Details</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Click the User&#39;s &quot;Username&quot; on the table list<br>3. Check display of the modal</td><td class="s7"></td><td class="s5">1.User should navigated to IT Admin Management Page<br>2. Should open the &quot;Admin Details -View&#39; Modal<br>3. Should display a non-editable Fields for:<br>    a. Username<br>    b. Password<br>        i. Should display Password if the Password is still on default then mask the Password once the default Password has been updated<br>    c. First Name<br>    d. Last Name<br>    e. User Type<br>    f. Email Address<br>    g. Department<br>    h. Status<br>4. Should have a &quot;Reset Pasword&quot; Link Button<br>5. Should display an Edit, Close, and Close Window Button<br>    a. Allow access of Edit User Details for Edit Button<br>    b. Close Window and Close Button should close View User Modal</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">TC-003-15</td><td class="s13">[USER MANAGEMENT] [ROOT USER] Root User - View IT Admin User Details</td><td class="s13">Critical</td><td class="s13">Verify Root User - View IT Admin User Details when user clicked edit</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to IT Admin Management Page<br>2. Click  &quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Check display of the modal</td><td class="s15"></td><td class="s13">1.User should navigated to IT Admin Management Page<br>2. Should open the &quot;Admin Details -View&#39; Modal<br>3. Should display a non-editable Fields for:<br>    a. Username<br>    b. Password<br>        i. Should display Password if the Password is still on default then mask the Password once the default Password has been updated<br>    c. First Name<br>    d. Last Name<br>    e. User Type<br>    f. Email Address<br>    g. Department<br>    h. Status<br>4. Should have a &quot;Reset Pasword&quot; Link Button<br>5. Should display an Edit, Close, and Close Window Button<br>    a. Allow access of Edit User Details for Edit Button<br>    b. Close Window and Close Button should close View User Modal</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s5">TC-003-16</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - Reset IT Admin&#39;s Password</td><td class="s5">Critical</td><td class="s5">Verify Root User - Reset IT Admin&#39;s Password</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users<br>5. Should have an IT Admin requested for Password Reset</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Click the User&#39;s &quot;Username&quot; on the table list<br>3. Click &quot;Reset Password&quot; Text Link<br>4. Click &quot;Cancel&quot; button<br>5. Click again the &quot;Reset Password&quot; Text Link in &quot;Admin Details - View&quot; Modal<br>6. Click &quot;Continue&quot; button in the &quot;Confirm Changes&quot; modal<br>7. Ask the user to relogin again using the temporary password from &quot;Reset Password&quot; link button</td><td class="s7"></td><td class="s5">1.User should navigated to IT Admin Management Page<br>2. Should open the &quot;Admin Details -View&#39; Modal<br>3. Should display a &quot;Confirm Changes&quot; Modal<br>4. Should redirected back to &quot;Admin Details -View&#39; Modal<br>5.Should display a &quot;Confirm Changes&quot; Modal<br>6. Should reset the Password with Alphanumeric and Special Characters except Emojis<br>    a. Should also reset the QR Setup once Password has been reset<br>7. When logging in should require the Admin User to:<br>    a. Login using their Temporary Password<br>    b. Update Password<br>    c. Setup QR Linking to Google Authenticator</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s5">TC-003-17</td><td class="s5">[USER MANAGEMENT] [ROOT USER] Root User - Reset IT Admin&#39;s Password</td><td class="s5">critical</td><td class="s5">Verify Root User - Reset IT Admin&#39;s Password when user clicked edit</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Root User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users<br>5. Should have an IT Admin requested for Password Reset</a></td><td class="s5">1. Navigate to IT Admin Management Page<br>2. Click &quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Reset Password&quot; Text Link<br>4. Click &quot;Cancel&quot; button<br>5. Click again the &quot;Reset Password&quot; Text Link in &quot;Admin Details - View&quot; Modal<br>6. Click &quot;Continue&quot; button in the &quot;Confirm Changes&quot; modal<br>7. Ask the user to relogin again using the temporary password from &quot;Reset Password&quot; link button</td><td class="s7"></td><td class="s5">1.User should navigated to IT Admin Management Page<br>2. Should open the &quot;Admin Details -View&#39; Modal<br>3. Should display a &quot;Confirm Changes&quot; Modal<br>4. Should redirected back to &quot;Admin Details -View&#39; Modal<br>5.Should display a &quot;Confirm Changes&quot; Modal<br>6. Should reset the Password with Alphanumeric and Special Characters except Emojis<br>    a. Should also reset the QR Setup once Password has been reset<br>7. When logging in should require the Admin User to:<br>    a. Login using their Temporary Password<br>    b. Update Password<br>    c. Setup QR Linking to Google Authenticator</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s5">TC-003-18</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] IT Admin User Management Landing Page</td><td class="s5">critical</td><td class="s5">Verify IT Admin User Management display of Admin user</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Check the User Management Display<br>4. Validate &quot;Search&quot; functionality<br>5. Validate &quot;Clear&quot; button<br>6. Validate &quot;Create New User&quot; button<br>7. Validate sorting of Table Columns</td><td class="s7"></td><td class="s5">1. Should display a Sub-Menus for &quot;Approval Managementt&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3.Should display the ff on the page:<br>    a. Page Header &quot;User Management&quot;<br>    b. Search Field<br>    c. Clear button<br>    d.Table columns for <br>      -Username<br>      -First Name<br>      -Last Name<br>      -Email address<br>      -User Type<br>      -Department<br>      -Status<br>      -Actions (Edit  Icon button)<br>    e.&quot;Create a New Admin&quot; button<br>    f. 10 Rows of Data per Page<br>   g. Total Number of Data per Page against Total Table Data<br>4. Search Field functionalily should be:<br>    a. Search is applied for Username, First Name, and Last Name<br>        i. Should be triggered by clicking Search Button<br>        ii. Display matched User, else display a No Data in the Table<br>5. Typed &quot;words on search field should be cleared by clicking Clear Button<br>6. &quot;Create New User&quot; button Should display a new modal form when clicked<br>7. Should have a Default sorting Alphabetically of Last Name (A-Z) and Status (Active-Inactive)</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s5">TC-003-19</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] IT Admin User Creation</td><td class="s5">Critical</td><td class="s5">Verify IT  Admin User Creation display for Admin user</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Check the User Management Display<br>4. Click  &quot;Create a New Admin&quot; button<br>5. Validate all fields in &quot;Create a New Admin&quot; Modal<br>6. Validate &quot;Cancel&quot; and &quot;Confirm&quot; buttons<br></td><td class="s7"></td><td class="s5">1. Should display a Sub-Menus for &quot;Approval Managementt&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3.Should display the ff on the page:<br>    a. Page Header &quot;User Management&quot;<br>    b. Search Field<br>    c. Clear button<br>    d.Table columns for <br>      -Username<br>      -First Name<br>      -Last Name<br>      -Email address<br>      -User Type<br>      -Department<br>      -Status<br>      -Actions (Edit  Icon button)<br>    e.&quot;Create a New Admin&quot; button<br>    f. 10 Rows of Data per Page<br>   g. Total Number of Data per Page against Total Table Data<br>4. Should display Create New Admin Modal and have the following Fields:<br>    a. Username<br>    b. First Name<br>    c. Last Name<br>    d. User Type<br>    e. Email Address<br>    f. Supervisor<br>     g. Department  <br>5. Should follow Field validations:<br>    a. Username<br>        i. Should allow Alphanumeric and Special Characters except Emojis<br>        ii. Should have a Minimum of 5 Characters and Maximum of 50 Characters<br>        iii. Should not allow spaces<br>        iv. Should require the Field to proceed with User creation<br>    b. First Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    c. Last Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    d. User Type<br>        i. Engineerings<br>        ii. Supervisor<br>        iii. Assistant Manager<br>        iv. Department Head<br>        v. Department Secretary<br>        vi. Division Head<br>        vii. Area Staff<br>        viii. Purchasing Staff<br>        ix. Purchasing Head<br>        x. Management<br>    e. Email Address<br>        i. Alphanumeric and Special Characters exept Emojis<br>        ii. Require Email Address Domain<br>        iii. Must accept a maximum of 100 Characters<br>    f. Department<br>        i. Drop-down Values of Departments synced in Departments Menu<br>6. Should have Confirm and Cancel Buttons<br>    a. If Cancel Button is clicked, should display a Confirmation Modal <br>        i. Once Confirmed, should cancel Account creation of the User and redirect Back to the User List<br>    b. If Confiirm Button is clicked, should check Validations<br>        i. If with Errors, highlight the Field and display an Error Message<br>        ii. If without Errors, should display a Confirmation Modal<br>            i) Once Confirmed, Add the User in the User List and allow them to access their Accounts<br>            ii) Should display a Success Toast Message and automatically close after 3 seconds<br>            iii) Should have a Temporary Password once created with a combination of Alphanumeric and Special Characters except Emojis</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">3/11: Issue is Fixed<br><br>Bug  has been raised</td><td class="s5">CITYLANDPRS-768<br>CITYLANDPRS-770<br>CITYLANDPRS-773</td></tr><tr style="height: 19px"><th id="665557538R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">TC-003-20</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] IT Admin User Creation</td><td class="s5">High</td><td class="s5">Verify IT  Admin User Creation when user  did not entered email address </td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Check the User Management Display<br>4. Click  &quot;Create a New Admin&quot; button<br>5. Populate all fields except &quot;Email Address&quot; field<br>6. Click &quot;Confirm&quot; button in Create a New Admin Modal<br>7. Click &quot;Cancel&quot; button in the confirmation modal<br>8. Click &quot;Confirm&quot; button again n Create a New Admin Modal<br>9. Click &quot;Continue in the confirmation modal</td><td class="s7"></td><td class="s5">1. Should display a Sub-Menus for &quot;Approval Managementt&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3.Should display the ff on the page:<br>    a. Page Header &quot;User Management&quot;<br>    b. Search Field<br>    c. Clear button<br>    d.Table columns for <br>    e.&quot;Create a New Admin&quot; button<br>    f. 10 Rows of Data per Page<br>   g. Total Number of Data per Page against Total Table Data<br>4. Should display Create New Admin Modal and have the following Fields:<br>    a. Username<br>    b. First Name<br>    c. Last Name<br>    d. User Type<br>    e. Email Address<br>    f. Suppervisor<br>    g.. Department  <br>5. All fields should be populated except &quot;Email Address&quot; field<br>6; Should Display a Confirmation Modal that contains the ff:<br>    a. A description &quot;You are about to create a new user. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>   b. Cancel and Confirm buttons<br>7.User should redirected back to &quot;Create a New Admin&quot; Modal<br>8. Should Display a Confirmation Modal again <br>9. Should be able to create a new user with success toast mesage and new created user should be added on the list<br>Note: <br>-Only one Purchasing Head user type should be created<br>-Only two Management user type should be created<br>-Should create multiple user for succeeding other user types</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">TC-003-21</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] IT Admin User Creation</td><td class="s5">Critical</td><td class="s5">Verify IT  Admin User Creation when user populate all fields</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s5">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Check the User Management Display<br>4. Click  &quot;Create a New Admin&quot; button<br>5. Populate all fields <br>6. Click &quot;Confirm&quot; button in Create a New Admin Modal<br>7. Click &quot;Cancel&quot; button in the confirmation modal<br>8. Click &quot;Confirm&quot; button again n Create a New Admin Modal<br>9. Click &quot;Continue in the confirmation modal</td><td class="s7"></td><td class="s5">1. Should display a Sub-Menus for &quot;Approval Managementt&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3.Should display the ff on the page:<br>    a. Page Header &quot;User Management&quot;<br>    b. Search Field<br>    c. Clear button<br>    d.Table columns for <br>      -Username<br>      -First Name<br>      -Last Name<br>      -Email address<br>      -User Type<br>      -Department<br>      -Status<br>      -Actions (Edit  Icon button)<br>    e.&quot;Create a New Admin&quot; button<br>    f. 10 Rows of Data per Page<br>   g. Total Number of Data per Page against Total Table Data<br>4. Should display Create New Admin Modal and have the following Fields:<br>    a. Username<br>    b. First Name<br>    c. Last Name<br>    d. User Type<br>    e. Email Address<br>     f.  Supervisor<br>     g. Department  <br>5. All fields should be populated <br>6; Should Display a Confirmation Modal that contains the ff:<br>    a. A description &quot;You are about to create a new user. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>   b. Cancel and Confirm buttons<br>7.User should redirected back to &quot;Create a New Admin&quot; Modal<br>8. Should Display a Confirmation Modal again <br>9. Should be able to create a new user with success toast mesage and new created user should be added on the list<br><br>Note: <br>-Only one Purchasing Head user type should be created<br>-Only two Management user type should be created<br>-Should create multiple user for succeeding other user types</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">TC-003-22</td><td class="s13">[USER MANAGEMENT] [IT ADMIN] IT Admin Edit User Details</td><td class="s13">High</td><td class="s13">Verify IT  Admin User Edit display for Admin  user</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Check Non-editable fields<br>5. Check Editable fields<br>6. Validate all editable fields<br>7. Check buttons displayed on &quot;User Details - Edit&quot; Modal</td><td class="s15"></td><td class="s13">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Should display a non-editable Fields for Username <br>5. Should allow editing of User&#39;s Information and Status for First Name, Last name, Email address, Department and Status text and dropdown Fields<br>6. Should follow Field validations:<br>    a. First Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    b. Last Name<br>        i. Letters only<br>        ii. Allow specific Special Characters: -&#39;.<br>        iii. Must accept a maximum of 100 Characters<br>    c. User Type<br>        i. Engineerings<br>        ii. Supervisor<br>        iii. Assistant Manager<br>        iv. Department Head<br>        v. Department Secretary<br>        vi. Division Head<br>        vii. Area Staff<br>        viii. Purchasing Staff<br>        ix. Purchasing Head<br>    d. Email Address<br>        i. Alphanumeric and Special Characters except Emojis<br>        ii. Require Email Address Domain<br>        iii. Must accept a maximum of 100 Characters<br>    e. Department<br>        i. Drop-down Values of Departments synced in Departments Menu<br>     f. Supervisor<br>        i. Drop-down Values Users with a User Type of Supervisor<br>    g. Should require all Fields to proceed except email address<br>7. Should have Close, Save and Cancel Buttons<br>    a. If Close Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should close the Edit User Details Modal<br>    b. If Cancel Button is clicked, should display a Confirmation Modal<br>        i. Once confirmed, cancel updating of User&#39;s Account and redirect Back to the User List<br>    c. If Submit Button is clicked, should check Validations<br>        i. If with Errors, highlight the Field and display an Error Message<br>        ii. If without Errors, should display a Confirmation Modal<br>            i) Once confirmed, should edit the User&#39;s Information<br>            ii) Should display a Success Toast Message and automatically close after 3 seconds</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Bug has been raised</td><td class="s5">CITYLANDPRS-771<br>CITYLANDPRS-770<br>CITYLANDPRS-773</td></tr><tr style="height: 19px"><th id="665557538R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">TC-003-23</td><td class="s13">[USER MANAGEMENT] [IT ADMIN] IT Admin Edit User Details</td><td class="s13">High</td><td class="s13">Verify Admin User Edit Confirmation Modal when user clicked close button</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields<br>   -First Name<br>   -Last Name<br>   -User Type<br>   -Email Address<br>   -Department<br>   -Supervisor<br>   -Status<br>5. Click  &quot;Close&quot;  button<br>6. Click &quot;Cancel&quot; button on Cancel Changes Modal <br>7. Click again the &quot;Close&quot; button in &quot;User Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button</td><td class="s15"></td><td class="s13">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Fields should be updated<br>5.Should display a &quot;Cancel Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;User Details - Edit&quot; Modal<br>7. Should display again the &quot;Cancel Changes&quot; Modal<br>8. Should close the &quot;Admin Details - Edit&quot; Modal<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">TC-003-24</td><td class="s13">[USER MANAGEMENT] [IT ADMIN] IT Admin Edit User Details</td><td class="s13">High</td><td class="s13">Verify Admin User Edit Confirmation Modal when user clicked cancel button</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields<br>   -First Name<br>   -Last Name<br>   -User Type<br>   -Email Address<br>   -Department<br>   -Supervisor<br>   -Status<br>5. Click  &quot;Cancel&quot;  button<br>6. Click &quot;Cancel&quot; button on Cancel Changes Modal <br>7. Click again the &quot;Cancel&quot; button in &quot;User Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button</td><td class="s15"></td><td class="s13">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Fields should be updated<br>5.Should display a &quot;Cancel Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;User Details - Edit&quot; Modal<br>7. Should display again the &quot;Cancel Changes&quot; Modal<br>8. Should close the &quot;User Details - Edit&quot; Modal and redirected back to user list<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">TC-003-25</td><td class="s13">[USER MANAGEMENT] [IT ADMIN] IT Admin Edit User Details</td><td class="s13">Critical</td><td class="s13">Verify Admin User Edit Confirmation Modal when user clicked save button</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields<br>   -First Name<br>   -Last Name<br>   -User Type<br>   -Email Address<br>   -Department<br>   -Supervisor<br>   -Status<br>5. Click  &quot;Save&quot;  button<br>6. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>7. Click again the &quot;Save&quot; button in &quot;User Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button</td><td class="s15"></td><td class="s13">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Fields should be updated<br>5.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;User Details - Edit&quot; Modal<br>7. Should display again the &quot;Confirm Changes&quot; Modal<br>8. Should display a Success Toast Message and automatically close after 3 seconds<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">TC-003-26</td><td class="s13">[USER MANAGEMENT] [IT ADMIN] IT Admin Edit User Details</td><td class="s13">Critical</td><td class="s13">Verify Admin User Edit when user edit and save with blank editable field</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Update any of editable text and dropdown fields <br>   -First Name<br>   -Last Name<br>   -User Type<br>   -Email Address<br>   -Department<br>   -Supervisor<br>   -Status<br>5. Leave 1 blank required field <br>6. Click  &quot;Save&quot;  button<br>7. Click &quot;Continue&quot; button in  Confirm Changes Modal </td><td class="s15"></td><td class="s13">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Fields should be updated<br>5. 1 blank field should be displayed<br>6.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should highlight the blank Field and display an Error Message<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s5">TC-003-27</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] Deactivate an IT Admin User from Edit Modal</td><td class="s5">Critical</td><td class="s5"> Verify Admin User - Deactivate an IT Admin User from Edit Modal</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Update the Status dropdown field and select &quot;Inactive&quot;<br>5. Click  &quot;Save&quot;  button<br>6. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>7. Click again the &quot;Save&quot; button in &quot;User Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button<br>9. Request the Inactive user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Status field should be Inactive<br>5.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;Admin Details - Edit&quot; Modal<br>7. Should display again the &quot;Confirm Changes&quot; Modal<br>8. Should display a Success Toast Message and automatically close after 3 seconds<br>9. User should be Inactive and not be able to login to Cityland-PRS </td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s5">TC-003-28</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] Deactivate an IT Admin User from Edit Modal</td><td class="s5">Critical</td><td class="s5"> Verify Admin User - Reactivate an IT Admin User from Edit Modal</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to User Management Page<br>2. Click  &quot;&quot;Edit&quot; icon button on any of the users displayed in the column <br>3. Click &quot;Edit&quot; button on &quot;User Details - View&quot; Modal<br>4. Update the Status of Inactive user to &quot;Active&quot;<br>5. Click  &quot;Save&quot;  button<br>6. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>7. Click again the &quot;Save&quot; button in &quot;User Details - Edit&quot; Modal<br>8. Click &quot;Continue&quot; button<br>9. Request the Reactived  user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to User Management Page<br>2. &quot;User Details - View&quot; Modal should be displayed<br>3. Should display the &quot;User Details - Edit&quot; Modal<br>4. Status field should be Active<br>5.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to &quot;User Details - Edit&quot; Modal<br>7. Should display again the &quot;Confirm Changes&quot; Modal<br>8. Should display a Success Toast Message and automatically close after 3 seconds<br>9. User should be Active again and able to login to Cityland-PRS </td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5">TC-003-29</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] Deactivate an IT Admin User from Status Column</td><td class="s5">Critical</td><td class="s5"> Verify Admin User - Deactivate an IT Admin User from Status Column</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to User Management Page<br>2. Select a user to :&quot;Inactive&quot; on Status dropdown <br>3. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>4. Select again a user to :&quot;Inactive&quot; on Status dropdown in IT Admin Management page<br>5. Click &quot;Continue&quot; button<br>6. Request the Inactive user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to User Management Page<br>2.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes for [First and Last Name]. Make sure all items are correct. Press continue if you want to proceed with this action..&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>3. Should redirected back to &quot;IT Admin Management&quot; Page<br>4. Should display again the &quot;Confirm Changes&quot; Modal<br>5. Should display a status to inactive<br>6. User should be Inactive and not be able to login to Cityland-PRS </td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7">Fixed Issue</td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-791">CITYLANDPRS-791</a></td></tr><tr style="height: 19px"><th id="665557538R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5">TC-003-30</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] Deactivate an IT Admin User from Status Column</td><td class="s5">Critical</td><td class="s5"> Verify Admin User - Reactivate an IT Admin User from Status Column</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to User Management Page<br>2. Select an inactive user to &quot;Active&quot; in Status dropdown <br>3. Click &quot;Cancel&quot; button on Confirm Changes Modal <br>4. Select again an inactive user to :&quot;Active&quot; on Status dropdown in IT Admin Management page<br>5. Click &quot;Continue&quot; button<br>6. Request the Reactived user to relogin using their credentials</td><td class="s7"></td><td class="s5">1. User should navigated to User Management Page<br>2.Should display a &quot;Confirm Changes&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to make changes for [First and Last Name]. Make sure all items are correct. Press continue if you want to proceed with this action..&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>3. Should redirected back to &quot;IT Admin Management&quot; Page<br>4. Should display again the &quot;Confirm Changes&quot; Modal<br>5. Should display a status to active<br>6. User should be active and able to login to Cityland-PRS </td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s5">TC-003-31</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] View IT Admin User Details</td><td class="s5">High</td><td class="s5">Verify Admin User - View IT Admin User Details</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s5">1. Navigate to User Management Page<br>2. Click the User&#39;s &quot;Username&quot; on the table list<br>3. Check display of the modal</td><td class="s7"></td><td class="s5">1.User should navigated to User Management Page<br>2. Should open the &quot;User Details -View&#39; Modal<br>3. Should display a non-editable Fields for:<br>    a. Username<br>    b. Password<br>        i. Should display Password if the Password is still on default then mask the Password once the default Password has been updated<br>    c. First Name<br>    d. Last Name<br>    e. User Type<br>    f. Email Address<br>    g. Department<br>    h. Supervisor<br>    i. Status<br>4. Should have a &quot;Reset Pasword&quot; Link Button<br>5. Should display an Edit, Close, and Close Window Button<br>    a. Allow access of Edit User Details for Edit Button<br>    b. Close Window and Close Button should close View User Modal</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s5">TC-003-32</td><td class="s13">[USER MANAGEMENT] [IT ADMIN] View IT Admin User Details</td><td class="s13">High</td><td class="s13">Verify Admin User - View IT Admin User Details when user clicked edit</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users</a></td><td class="s13">1. Navigate to User Management Page<br>2. Click  &quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Check display of the modal</td><td class="s15"></td><td class="s13">1.User should navigated to User Management Page<br>2. Should open the &quot;User Details -View&#39; Modal<br>3. Should display a non-editable Fields for:<br>    a. Username<br>    b. Password<br>        i. Should display Password if the Password is still on default then mask the Password once the default Password has been updated<br>    c. First Name<br>    d. Last Name<br>    e. User Type<br>    f. Email Address<br>    g. Department<br>    h. Supervisor<br>    i. Status<br>4. Should have a &quot;Reset Pasword&quot; Link Button<br>5. Should display an Edit, Close, and Close Window Button<br>    a. Allow access of Edit User Details for Edit Button<br>    b. Close Window and Close Button should close View User Modal</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s5">TC-003-33</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] Reset IT Admin&#39;s Password</td><td class="s5">Critical</td><td class="s5">Verify Admin User - Reset IT Admin&#39;s Password</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users<br>5. Should have an IT Admin requested for Password Reset</a></td><td class="s5">1. Navigate to User Management Page<br>2. Click the User&#39;s &quot;Username&quot; on the table list<br>3. Click &quot;Reset Password&quot; Text Link<br>4. Click &quot;Cancel&quot; button<br>5. Click again the &quot;Reset Password&quot; Text Link in &quot;User Details - View&quot; Modal<br>6. Click &quot;Continue&quot; button in the &quot;Confirm Changes&quot; modal<br>7. Ask the user to relogin again using the temporary password from &quot;Reset Password&quot; link button</td><td class="s7"></td><td class="s5">1.User should navigated to User Management Page<br>2. Should open the &quot;User Details -View&#39; Modal<br>3. Should display a &quot;Confirm Changes&quot; Modal<br>4. Should redirected back to &quot;User Details -View&#39; Modal<br>5.Should display a &quot;Confirm Changes&quot; Modal<br>6. Should reset the Password with Alphanumeric and Special Characters except Emojis<br>    a. Should also reset the QR Setup once Password has been reset<br>7. When logging in should require the Admin User to:<br>    a. Login using their Temporary Password<br>    b. Update Password<br>    c. Setup QR Linking to Google Authenticator</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s5">TC-003-34</td><td class="s5">[USER MANAGEMENT] [IT ADMIN] Reset IT Admin&#39;s Password</td><td class="s5">Critical</td><td class="s5">Verify Admin User - Reset IT Admin&#39;s Password when user clicked edit</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have declared Admin User account<br>3. User already logged in successfully to their Accounts<br>4. User should already created an admin users<br>5. Should have an IT Admin requested for Password Reset</a></td><td class="s5">1. Navigate to User Management Page<br>2. Click &quot;Edit&quot; icon button on any of the users displayed in the column<br>3. Click &quot;Reset Password&quot; Text Link<br>4. Click &quot;Cancel&quot; button<br>5. Click again the &quot;Reset Password&quot; Text Link in &quot;User Details - View&quot; Modal<br>6. Click &quot;Continue&quot; button in the &quot;Confirm Changes&quot; modal<br>7. Ask the user to relogin again using the temporary password from &quot;Reset Password&quot; link button</td><td class="s7"></td><td class="s5">1.User should navigated to User Management Page<br>2. Should open the &quot;User Details -View&#39; Modal<br>3. Should display a &quot;Confirm Changes&quot; Modal<br>4. Should redirected back to &quot;User Details -View&#39; Modal<br>5.Should display a &quot;Confirm Changes&quot; Modal<br>6. Should reset the Password with Alphanumeric and Special Characters except Emojis<br>    a. Should also reset the QR Setup once Password has been reset<br>7. When logging in should require the Admin User to:<br>    a. Login using their Temporary Password<br>    b. Update Password<br>    c. Setup QR Linking to Google Authenticator</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="665557538R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s5">TC-003-35</td><td class="s5">[USER MANAGEMENT] [ALL USERS] User Profile</td><td class="s5">Critical</td><td class="s5">Verify All User - User Profile</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br></a></td><td class="s5">1. Click Drop-down Icon beside the user&#39;s name at the Top Left of the Screen<br>2. Click &quot;My Profile&quot; on the sub menu<br>3. Check display of My Profile page<br>4.Click &quot;Logout&quot; on Sub Menu options</td><td class="s7"></td><td class="s5">1. A Sub-menu options for &quot;My Profile&quot; and &quot;Logout&quot; button should be displayed<br>2. Should redirect the User to their Profile Page <br>3. Should display the following for My Profile:<br>    a. User Details<br>        i. User&#39;s First Name and Last Name Fields<br>        ii. Username<br>        iii. Email Address<br>        iv. User Type<br>4. If a User is an Approver should also display<br>    a. Leave Setup<br>        i. Leave Table with Columns<br>           i) Leave Date<br>           ii) Days of Leave<br>           iii) Leave Until<br>           iv.) Edit and Close Icon buttons in Action&#39;s Column<br>         ii.  Add Leave Button<br>     b. Approver Status<br>         i.  Approvals where the User has been assigned to<br>5. Should logout the User from their Accounts<br></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="665557538R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s5">TC-003-36</td><td class="s5">[USER MANAGEMENT] [ALL USERS] User Profile</td><td class="s5">Critical</td><td class="s5">Verify All User - User Profile when user add leave</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br></a></td><td class="s5">1. Click Drop-down Icon beside the user&#39;s name at the Top Left of the Screen<br>2. Click &quot;My Profile&quot; on the sub menu<br>3. Click &quot;Add Leave&quot; button<br>4. Click &quot;Cancel&quot; button on the modal<br>5. Click &quot;Add Leave&quot; button again on the user profile page<br>6. Populate Start and End Dates fields<br>7. Click &quot;Add Leave&quot; button on the modal</td><td class="s7"></td><td class="s5">1. A Sub-menu options for &quot;My Profile&quot; and &quot;Logout&quot; button should be displayed<br>2. Should redirect the User to their Profile Page <br>3. Should display the &quot;Add Leave&quot; Modal that contains the ff:<br>   a. A description &quot;You are about to add a leave. Please select the designated start and end leave date and press “Add Leave” if you want to proceed with this action.&quot;<br>   b. &quot;Start and End dates&quot; Calendar fields with widget<br>   c. &quot;Cancel&quot; and &quot;Add Leave&quot; buttons<br>4. User should redirected back to User profile page<br>5. Should display the &quot;Add Leave&quot; Modal again<br>6. Start and End dates are populated<br>7. &quot;Leave Added&quot; Modal should be displayed that contains the ff:<br>   a. A description &quot;Your leave has been successfully added. Please check the affected workflow to assign alternative approvers.&quot;<br>   b. &quot;Check Affected Workflow&quot; button</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">3/11: Issue Fixed<br><br>Bug Has been raised</td><td class="s5">CITYLANDPRS-778</td></tr><tr style="height: 19px"><th id="665557538R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s5">TC-003-37</td><td class="s5">[USER MANAGEMENT] [ALL USERS] User Profile</td><td class="s5">Critical</td><td class="s5">Verify All User - User Profile when user edit leave</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br></a></td><td class="s5">1. Click Drop-down Icon beside the user&#39;s name at the Top Left of the Screen<br>2. Click &quot;My Profile&quot; on the sub menu<br>3. Click &quot;Edit&quot; icon button on Leave Setup<br>4. Click &quot;Cancel&quot; button on the modal<br>5. Click &quot;Continue&quot; on Discard Changes Modal<br>6. Click again &quot;Edit&quot; icon button on Leave Setup in my profile page<br>7. Update Start and End Dates fields<br>8. Click &quot;Confirrm&quot; button on the modal<br>9. Click &quot;Save&quot; button on Save Changes modal</td><td class="s7"></td><td class="s5">1. A Sub-menu options for &quot;My Profile&quot; and &quot;Logout&quot; button should be displayed<br>2. Should redirect the User to their Profile Page <br>3. Should display the &quot;Leave Details - Edit&quot; Modal that contains the ff:<br>   a. A description &quot;You are about to edit a leave. Please select the designated start and end leave date and press “Confirm” if you want to proceed with this action.&quot;<br>   b. &quot;Start and End dates&quot; Calendar fields with widget<br>   c. &quot;Cancel&quot; and &quot;Confirm&quot; buttons<br>4. &quot;Discard Changes&quot; Modal should be displayed with contains of the ff:<br>    a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>5. User should redirected back to User profile page<br>6. Should display the &quot;Leave Details - Edit&quot; Modal again<br>7. Start and End dates are updated<br>8. &quot;Save Changes&quot; Modal should be displayed that contains the ff:<br>   a. A description &quot;You are about to make changes to this request. Make sure all items are correct. Press save if you want to proceed with this action.&quot;<br>   b. &quot;Cancel&quot; and &quot;Save&quot; buttons<br>9.&quot;Should displayed a success toast message</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">3/11: Issue Fixed<br><br>Bug Has been raised</td><td class="s5">CITYLANDPRS-778</td></tr><tr style="height: 19px"><th id="665557538R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s5">TC-003-38</td><td class="s5">[USER MANAGEMENT] [ALL USERS] User Profile</td><td class="s5">Critical</td><td class="s5">Verify All User - User Profile when click delete leave</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br></a></td><td class="s5">1. Click Drop-down Icon beside the user&#39;s name at the Top Left of the Screen<br>2. Click &quot;My Profile&quot; on the sub menu<br>3. Click &quot;Delete&quot; icon button on Leave Setup<br>4. Click &quot;Cancel&quot; button on the modal<br>5. Click again  &quot;Delete&quot; icon button on Leave Setup<br>6. Click &quot;Continue&quot; button on the modal<br></td><td class="s7"></td><td class="s5">1. A Sub-menu options for &quot;My Profile&quot; and &quot;Logout&quot; button should be displayed<br>2. Should redirect the User to their Profile Page <br>3. Should display the &quot;Cancel Leave&quot; Modal that contains the ff:<br>   a. A description You are about to cancel this leave. Press continue if you want to proceed with this action.&quot;<br>   b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>4. User should redirected back to their Profile Page <br>    b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>5. Should display again the &quot;Cancel Leave&quot; Modal <br>6. Should display a Success Toast Message of deleting leave and automatically close after 3 seconds</td><td class="s12">Failed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">3/11: Issue Fixed<br><br>Bug Has been raised</td><td class="s5">CITYLANDPRS-782</td></tr><tr style="height: 19px"><th id="665557538R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s17"></td><td class="s5">[USER MANAGEMENT] [ALL USERS] User Profile</td><td class="s5">Critical</td><td class="s5">Validate that you can only create maximum of 2 management users</td><td class="s5"></td><td class="s5">1. Login as admin<br>2. Create 3 users with &#39;Management&#39; user type<br>3. Validate error message</td><td class="s5"></td><td class="s5">Error message should return Maximum of 2 management users already exists</td><td class="s5"></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">1/31: Check screenshot from User Types actual screenshot. Find Validate limit of user creation</td><td class="s5"></td></tr></tbody></table></div>