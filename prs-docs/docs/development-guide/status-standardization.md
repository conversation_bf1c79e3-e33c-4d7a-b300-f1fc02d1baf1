# Status Standardization Guide

## Overview

This guide explains the status standardization initiative in the PRS system. The goal is to ensure consistent naming conventions for status values across all workflows.

## Why Standardize Status Values?

1. **Consistency**: Makes the codebase more consistent and easier to understand
2. **Maintainability**: Reduces bugs caused by inconsistent status comparisons
3. **Type Safety**: Using constants instead of raw strings provides better type checking
4. **Documentation**: Makes it clear what status values are valid for each entity
5. **Refactoring**: Makes it easier to refactor status-related code

## Status Naming Convention

1. **Constant Keys**: All constant keys will use UPPERCASE with underscores (SCREAMING_SNAKE_CASE)
2. **Constant Values**: All constant values will use lowercase with underscores (snake_case)
3. **Constant Usage**: All status comparisons will use constants, not raw strings
4. **Immutability**: All constant objects will use `Object.freeze()` to prevent modification

## Example

```javascript
// CORRECT
const REQUISITION_STATUS = Object.freeze({
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  APPROVED: 'approved',
  // ...
});

// Usage
if (requisition.status === REQUISITION_STATUS.DRAFT) {
  // ...
}
```

## Standard Status Constants

### Requisition Status

```javascript
const REQUISITION_STATUS = Object.freeze({
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  APPROVED: 'approved',
  ASSIGNED: 'assigned',
  ASSIGNING: 'assigning',
  CANVASS_FOR_APPROVAL: 'canvass_for_approval',
  FOR_DELIVERY: 'for_delivery',
  FOR_PO_REVIEW: 'for_po_review',
  FOR_PR_APPROVAL: 'for_pr_approval',
  PARTIALLY_CANVASSED: 'partially_canvassed',
  REJECTED: 'rejected',
  CLOSED: 'closed',
});
```

### Canvass Status

```javascript
const CANVASS_STATUS = Object.freeze({
  DRAFT: 'draft',
  PARTIAL: 'partially_canvassed',
  FOR_APPROVAL: 'for_approval',
  REJECTED: 'rejected',
  APPROVED: 'approved',
});
```

### Purchase Order Status

```javascript
const PO_STATUS = Object.freeze({
  FOR_PO_REVIEW: 'for_po_review',
  FOR_PO_APPROVAL: 'for_po_approval',
  FOR_DELIVERY: 'for_delivery',
  CANCELLED_PO: 'cancelled_po',
  REJECT_PO: 'reject_po',
});
```

## How to Standardize Your Code

1. **Find Raw Status Strings**: Use the `find-raw-status-strings.js` script to find raw status strings in your code
2. **Import Constants**: Import the appropriate constants at the top of your file
3. **Replace Raw Strings**: Replace all raw strings with constant references
4. **Verify**: Run the script again to verify all raw strings are replaced
5. **Test**: Run tests to ensure everything still works correctly

## Database Considerations

If your changes affect database records, you'll need to:

1. Create a migration script to update existing records
2. Test the migration script in a development environment
3. Include the migration script in your PR

## Frontend Considerations

If your changes affect the frontend, you'll need to:

1. Update any status display components
2. Update any status-based conditional rendering
3. Update any API requests that filter by status

## Resources

- [Status Naming Convention](./status-naming-convention.md)
- [Status Standardization PR Template](./status-standardization-pr-template.md)
- [Find Raw Status Strings Script](https://gitlab.stratpoint.dev/prs-backend/scripts/find-raw-status-strings.js)
