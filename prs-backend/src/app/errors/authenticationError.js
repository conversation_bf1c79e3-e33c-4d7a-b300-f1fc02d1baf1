const AppError = require('./appError');

/**
 * Error class for authentication errors
 */
class AuthenticationError extends AppError {
  /**
   * Creates a new AuthenticationError
   * 
   * @param {string} message - Error message
   * @param {string} reason - Reason for authentication failure
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(
    message = 'Authentication failed',
    reason = 'INVALID_CREDENTIALS',
    originalError = null
  ) {
    super(message, 'AUTHENTICATION_ERROR', { reason }, originalError);
    this.reason = reason;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 401; // Unauthorized
  }
}

module.exports = AuthenticationError;
