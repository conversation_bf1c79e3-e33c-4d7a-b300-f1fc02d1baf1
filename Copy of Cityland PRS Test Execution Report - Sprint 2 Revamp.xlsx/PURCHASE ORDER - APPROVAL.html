<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1585517397C0" style="width:103px;" class="column-headers-background">A</th><th id="1585517397C1" style="width:219px;" class="column-headers-background">B</th><th id="1585517397C2" style="width:55px;" class="column-headers-background">C</th><th id="1585517397C3" style="width:252px;" class="column-headers-background">D</th><th id="1585517397C4" style="width:233px;" class="column-headers-background">E</th><th id="1585517397C5" style="width:330px;" class="column-headers-background">F</th><th id="1585517397C7" style="width:258px;" class="column-headers-background">H</th><th id="1585517397C9" style="width:164px;" class="column-headers-background">J</th></tr></thead><tbody><tr style="height: 42px"><th id="1585517397R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">STATUS</td></tr><tr style="height: 19px"><th id="1585517397R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="8">Update Adding of Approver behavior for Purchase Order Approval</td></tr><tr style="height: 19px"><th id="1585517397R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3">PRS-000-001</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify access to Purchase Order through Requisition Slip.</td><td class="s3">1. Logged in as PO Approver.</td><td class="s3">1. Click RS Number<br>2. Go to Related Documents tab.<br>3. Click Orders tab.</td><td class="s3">3. Purchase Order page is displayed.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3">PRS-000-002</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify access to Purchase Order through Dashboard page.</td><td class="s3">1. Logged in as PO Approver.</td><td class="s3">1. Go to Dashboard.<br>2. Click on the Purchase Order from the list.</td><td class="s3">2. Purchase Order page is displayed.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s3">PRS-000-003</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify that PO Details, Items, and Approvers are shown upon accessing PO.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.</td><td class="s3">1. Verify the Purchase Order Details, Purchase Order Items, and Approver section</td><td class="s3">1. Should display the Purchase Order Details, Purchase Order Items, and Approvers.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3">PRS-000-004</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify Add Button is visible in the Approvers section</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.</td><td class="s3">1. Look for Approver section.</td><td class="s3">1. Add button is visible.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s3">PRS-000-005</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify clicking the Add Button opens a modal to add approver</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.</td><td class="s3">1. Click Add in Approver section.</td><td class="s3">1. Modal opens allowing user to add an approver.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s3">PRS-000-006</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify modal displays a Search User field.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Add Approver modal is open.</td><td class="s3">1. Look for Search User input.</td><td class="s3">1. Search User field is displayed.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s3">PRS-000-007</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify only allowed user types appear in Search results</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Add Approver modal is open.</td><td class="s3">1. Search for users in modal.</td><td class="s3">1. Should display a Search User Field<br>        i. Should display Users with a User Types of<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Head<br>           iv) Division Head<br>           v) Area Staff/Department Secretary</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s3">PRS-000-008</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Minor</td><td class="s3">Verify user search field allows typing and filters list dynamically.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Add Approver modal is open.</td><td class="s3">1. Type in user name in Search field.</td><td class="s3">1. Matching users dynamically appear</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s3">PRS-000-009</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify added approver appears below current approver with * label</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Additional Approver is added.</td><td class="s3">1. Confirm addition.</td><td class="s3">1. Approver appears below current one, labeled with &quot;*&quot;.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s3">PRS-000-010</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify added approver appears in Approver list immediately</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Additional Approver is added.</td><td class="s3">1. View Approver list after addition.</td><td class="s3">1. Added approver is listed immediately without reloading.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s3">PRS-000-011</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify additional approver is notified via Notification Bell.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Additional Approver is added.</td><td class="s3">1. Switch to added user&#39;s account.<br>2. Open Notifications.</td><td class="s3">1. Notification is received.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s3">PRS-000-012</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Minor</td><td class="s3">Verify notification template details are correct.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Additional Approver is added.</td><td class="s3">1. View notification content.</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1.  Notification format are as follows:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Title: </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Assigned as an Additional Approver<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Content:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Purchase Order and have it Approved. Click here or access the Dashboard to proceed in reviewing the Purchase Order.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date of the Notification: [MMM-DD-YYY]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Nov 08 2024</span></td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s3">PRS-000-013</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify default approver can edit or delete added approver before approving PO.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. PO is not yet approve.</td><td class="s3">1. Add new approver<br>2. Edit or delete approver.</td><td class="s3">2. Should allow the Default Approver to Edit or Delete the Added Approver until they haven&#39;t Approve the Purchase Order.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s3">PRS-000-014</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify added approver cannot be deleted after PO is approved.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. PO is approved.</td><td class="s3">1. Add new approver<br>2. Attempt to Edit or delete approver.</td><td class="s3">2. Edit/Delete option is disabled.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s3">PRS-000-015</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify additional approver cannot approve before current approver.</td><td class="s3">1. Logged in as Additional PO Approver.<br>2. User is on PO page.<br>3. PO is waiting for approval.</td><td class="s3">1. Attempt to approve</td><td class="s3">1. Approval is blocked until default approver approves.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s3">PRS-000-016</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Critical</td><td class="s3">Verify process requires additional approver to approve before next approver.</td><td class="s3">1. Logged in as Additional PO Approver.<br>2. User is on PO page.<br>3. Additional approver approves.</td><td class="s3">1. Verify if next approver can approve.</td><td class="s3">1. Next approver can only approve after additional approver approves.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s3">PRS-000-017</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify Add Approver button appears in confirmation if no approver is added yet.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. PO is about to be approved.</td><td class="s3">1. Start approval flow without additional approver<br>2. Approve PO.<br>3. Verify confirmation message.</td><td class="s3">3. Confirmation message shows &quot;Add Approver&quot; button.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s3">PRS-000-018</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Minor</td><td class="s3">Verify Add Approver button does not appear if one is already added.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. PO has additional approver.</td><td class="s3">1. Approve PO<br>2. Verify confirmation message.</td><td class="s3">2. Confirmation message does not show &quot;Add Approver&quot;.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s3">PRS-000-019</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify error shown when trying to add a user not allowed as approver.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Add Approver modal is open.</td><td class="s3">1. Attempt adding a user with user type not listed in criteria.</td><td class="s3">1. No search result appears.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s3">PRS-000-020</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">High</td><td class="s3">Verify duplicate approvers cannot be added again.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Approver already added.</td><td class="s3">1. Try adding same user again.</td><td class="s3">2. User cannot be added again hence system blocks duplicate.</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s3">PRS-000-021</td><td class="s3">Update Adding of Approver behavior for Purchase Order Approval.</td><td class="s3">Minor</td><td class="s3">Verify UI handles modal close and cancel operations gracefully.</td><td class="s3">1. Logged in as PO Approver.<br>2. User is on PO page.<br>3. Approver already added.</td><td class="s3">1. Click X or Cancel.<br>2. Reopen modal.</td><td class="s3">2. Modal closes and reopens without error</td><td class="s4">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s2" colspan="8">Purchase Order Status Updates</td></tr><tr style="height: 19px"><th id="1585517397R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s3">PRS-001-001</td><td class="s3">Purchase Order Status Updates</td><td class="s3">Critical</td><td class="s3">Verify that the Purchase Order status updates to &quot;For PO Review&quot; upon PO generation.</td><td class="s3">1. PO has been generated.</td><td class="s3">1. Verify the status on PO List.</td><td class="s3">1. Status should be displayed as follows:<br>    a. For PO Review<br>        BG - #5B3D2C33<br>        Text - #5B3D2C</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s3">PRS-001-002</td><td class="s3">Purchase Order Status Updates</td><td class="s3">High</td><td class="s3">Verify that the status updates to &quot;For PO Approval&quot; after submission by Purchasing Staff.</td><td class="s3">1. PO is in &quot;For PO Review&quot; status</td><td class="s3">1. Submit the PO for approval.<br>2. Verify status update.</td><td class="s3">2. Status should be displayed as follows:<br>    b. For PO Approval<br>        BG - #F0963D33<br>        Text - #F0963D</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s3">PRS-001-003</td><td class="s3">Purchase Order Status Updates</td><td class="s3">Critical</td><td class="s3">Verify that PO status updates to &quot;PO Rejected&quot; if any approver rejects.</td><td class="s3">1. PO is in &quot;For PO Approval&quot; status</td><td class="s3">1. Reject PO as an approver.<br>2. Verify status update.</td><td class="s3">2. Status should be displayed as follows:<br>    c. PO Rejected<br>        BG - #DC433B33<br>        Text - PO Rejected</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s3">PRS-001-004</td><td class="s3">Purchase Order Status Updates</td><td class="s3">Critical</td><td class="s3">Verify that the PO status becomes &quot;For Sending&quot; when all approvers approve.</td><td class="s3">1. PO is approved by all approvers.</td><td class="s3">1. Verify status is update.</td><td class="s3">2. Status should be displayed as follows:<br>    d. For Sending<br>        BG - #2F80ED33<br>        Text - #2F80ED</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s3">PRS-001-005</td><td class="s3">Purchase Order Status Updates</td><td class="s3">High</td><td class="s3">Verify that PO status becomes &quot;For Delivery&quot; once sent to supplier.</td><td class="s3">1. PO has been marked as sent</td><td class="s3">1. Verify status is update.</td><td class="s3">2. Status should be displayed as follows:<br>    e. For Delivery<br>        BG - #1EA52B33<br>        Text - For Delivery</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s3">PRS-001-006</td><td class="s3">Purchase Order Status Updates</td><td class="s3">Critical</td><td class="s3">Verify that PO status updates to &quot;Closed PO&quot; once PR is approved.</td><td class="s3">1. All items delivered.</td><td class="s3">1. Create DR, Invoice, and PR.<br>2. Approve PR.</td><td class="s3">2. Status should be displayed as follows:<br>    f. Closed PO<br>        BG - #5B3D2C33<br>        Text - #5B3D2C</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s3">PRS-001-007</td><td class="s3">Purchase Order Status Updates</td><td class="s3">High</td><td class="s3">Verify that PO status updates to &quot;Cancelled PO&quot; if cancelled before full delivery.</td><td class="s3">1. PO is in &quot;For PO Approval&quot; or &quot;For Delivery&quot;<br>2. Not all items are delivered.</td><td class="s3">1. Cancel the PO before full delivery.<br>2. Confirm cancellation.</td><td class="s3">2. Status should be displayed as follows:<br>    g. Cancelled PO<br>        BG - #5F636833<br>        Text - #5F6368</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s3">PRS-001-008</td><td class="s3">Purchase Order Status Updates</td><td class="s3">High</td><td class="s3">Verify error when trying to cancel a PO that has already been closed.</td><td class="s3">1. PO is in “Closed PO” status.</td><td class="s3">1. Try to cancel the closed PO.</td><td class="s3">1. Cancellatiom should not occur.</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s3">PRS-001-009</td><td class="s3">Purchase Order Status Updates</td><td class="s3">Minor</td><td class="s3">Verify that cancelled PO returns to canvassing.</td><td class="s3">1. PO is cancelled.</td><td class="s3">1. Verify origin Canvass Sheet.</td><td class="s3">1. Items is returned to Canvassing.</td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s6" colspan="8"> Adding of Notes during Purchase Order Approval</td></tr><tr style="height: 19px"><th id="1585517397R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s7">PRS-1318-001</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">Critical</td><td class="s8">Verify that a Purchase Order can be submitted for Approval.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A drafted Purchase Order exists.</td><td class="s8">1.  Create or open a drafted Purchase Order<br>2. Click the &quot;Submit&quot; button.</td><td class="s8">2. Purchase Order is submitted successfully and becomes available for approval.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s7">PRS-1318-002</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">Minor</td><td class="s8">Verify Purchase Order No. redirection.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.</td><td class="s8">1. Navigate to the submitted Purchase Orders list<br>2. Click the Purchase Order Number.</td><td class="s8">2. Purchase Order details page is displayed.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s7">PRS-1318-003</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">High</td><td class="s8">Verify that a sticky Confirmation Message is displayed when initiating approval.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details</td><td class="s8">1. Observe Purchase Order details page.</td><td class="s8">1. A sticky confirmation message for approval is displayed.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s7">PRS-1318-004</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">Critical</td><td class="s8">Verify that clicking Approve displays a Confirmation Modal with Notes field.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details</td><td class="s8">1. Click on the “Approve” button.<br>2. Observe the confirmation modal.</td><td class="s8">1. A confirmation modal is displayed with the following field and buttons:<br>     - Notes<br>     - Continue<br>     - Add Approver<br>     - Cancel</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s7">PRS-1318-005</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">High</td><td class="s8">Verify that Notes field accepts alphanumeric and special characters.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details<br>4. Confirmation model is open.</td><td class="s8">1. Enter a valid note using alphanumeric and special characters.<br>2. Click the &quot;Continue&quot; button.</td><td class="s8">2. Note is accepted and the approval proceeds.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s7">PRS-1318-006</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">Minor</td><td class="s8">Verify that Notes field does not accept emojis.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details<br>4. Confirmation model is open.</td><td class="s8">1. Enter emojis in the Notes field.<br>2. Click the &quot;Continue&quot; button.</td><td class="s8">2. Emoji input is rejected and user is prompted with error.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s7">PRS-1318-007</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">High</td><td class="s8">Verify that Notes field accepts input up to 100 characters.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details<br>4. Confirmation model is open.</td><td class="s8">1. Enter exactly 100 characters in the Notes field.<br>2. Click the &quot;Continue&quot; button.</td><td class="s8">2. Note is accepted and approval proceeds.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s7">PRS-1318-008</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">Minor</td><td class="s8">Verify that Notes field does not accept more than 100 characters.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details<br>4. Confirmation model is open.</td><td class="s8">1. Enter exactly 100 characters in the Notes field.<br>2. Click the &quot;Continue&quot; button.</td><td class="s8">2. System prevents excess input.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s7">PRS-1318-009</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">High</td><td class="s8">Verify that Notes field is optional for approval.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details<br>4. Confirmation model is open.</td><td class="s8">1. Leave the Notes field blank<br>2. Click the &quot;Continue&quot; button.</td><td class="s8">2. Approval proceeds without an error.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s7">PRS-1318-010</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">High</td><td class="s8">Verify Cancel button functionality.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. A Purchase Order is submitted and available.<br>3. Approver has opened the Purchase Order details<br>4. Confirmation model is open.</td><td class="s8">1. Click the &quot;Cancel&quot; button on the modal</td><td class="s8">1. Modal closes and returns to Purchase Order detail page.</td><td class="s9">Not Started</td></tr><tr style="height: 19px"><th id="1585517397R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s7">PRS-1318-011</td><td class="s8">Adding of Notes during Purchase Order Approval</td><td class="s8">High</td><td class="s8">Verify that entered Notes are displayed in the Check Notes section after Approval.</td><td class="s8">1. Logged in as Purchase Order Approver.<br>2. Purchase Order is approved with Notes.</td><td class="s8">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Notes” of the approved Purchase Order.<br>3. Verify the badge is cleared.</td><td class="s8">1. “New Attachment” badge is displayed.<br>2. Entered Approval Notes are displayed correctly.<br>3. Approver name should be displayed correctly.<br>4. “New Attachment” badge is cleared when viewed.</td><td class="s9">Not Started</td></tr></tbody></table></div>