<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-left:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1068162422C0" style="width:103px;" class="column-headers-background">A</th><th id="1068162422C1" style="width:98px;" class="column-headers-background">B</th><th id="1068162422C2" style="width:252px;" class="column-headers-background">C</th><th id="1068162422C3" style="width:252px;" class="column-headers-background">D</th><th id="1068162422C4" style="width:233px;" class="column-headers-background">E</th><th id="1068162422C5" style="width:330px;" class="column-headers-background">F</th><th id="1068162422C6" style="width:184px;" class="column-headers-background">G</th><th id="1068162422C7" style="width:144px;" class="column-headers-background">H</th><th id="1068162422C8" style="width:100px;" class="column-headers-background">I</th><th id="1068162422C9" style="width:88px;" class="column-headers-background">J</th><th id="1068162422C10" style="width:124px;" class="column-headers-background">K</th><th id="1068162422C11" style="width:124px;" class="column-headers-background">L</th><th id="1068162422C12" style="width:124px;" class="column-headers-background">M</th><th id="1068162422C13" style="width:124px;" class="column-headers-background">N</th><th id="1068162422C14" style="width:78px;" class="column-headers-background">O</th><th id="1068162422C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1068162422R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1068162422R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-013 - [RS Approval] - Approve, Reject, Re-Submit, Re-Approve, Cancel, Approver Updates</td></tr><tr style="height: 19px"><th id="1068162422R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-013-001</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s6">Validate request for approval confirmation message</td><td class="s7"></td><td class="s5">1. Login as approver of the requestor<br>2. Click Dashboard &gt; Click For My Approval/Assigned tab<br>3. Click 1 Ref Number with Submitted status</td><td class="s7"></td><td class="s7 softmerge"><div class="softmerge-inner" style="width:141px;left:-1px">1. Validate Confirmation Message for Approving and Rejecting a Requisition Slip<br>    a. Confirmation Message should be following along scrolling &quot;You have a pending action: Request Approval&quot;<br>    b. Should display Reject and Approve Buttons</div></td><td class="s8" rowspan="6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=1298925388#gid=1298925388">https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=1298925388#gid=1298925388</a></td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-013-002</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s6">Validate RS approval is successful for multiple approver</td><td class="s5">Add approver to a project:<br><br>1. Login as admin<br>2. Create a 2 supervisor user<br>3. Go to Manage &gt; Project<br>4. Click 1 project name (Remember the project name you clicked)<br>5. Scroll down &gt; Select Requisition Slip &gt; Click &#39;Edit&#39; button on Project Approvers<br>6. Click Add level to add the 1st approver<br>7. Click Add approver<br>8. Select the 1st approver/supervisor user that you have created as Level 1 approver<br>9. Click Add level again to add the 2nd approver<br>10.Click Add approver<br>11. Select the 2nd approver/supervisor you have created<br>12. Click Add Approver &gt; Save &gt; Continue</td><td class="s6">1. Login as admin<br>2. Dashboard &gt; Click New Request<br>3. Submit a RS with details below and populate other required fields:<br>Charge to (Category) - Project<br>Charge to (Client) - &lt;Select the project name where you add the supervisor as approver&gt;<br>Project - &lt;Select the project name where you add the supervisor as approver&gt;<br>4. Open the submitted RS and there should be 2 approvers on the right side<br>5. Logout as admin<br>5. Login as the the 1st approver (the 1st level supervisor created and added as approver on project)<br>6. Click Dashboard &gt; Click For My Approval/Assigned tab<br>7. Click RS Ref Number link with Submitted status<br>8. Validate Confirmation Message for Approving and Rejecting a Requisition Slip<br>9. Should display the Names of the Approvers (Should display the 2 approvers if you added 2 approvers based on your pre-requisite)<br>10. Click Approve<br>11. Validate pop-up message &quot;You are about to approve this Requisition Slip. Press &quot;Continue&quot; if you want to proceed with this action or &quot;Add an Approver&quot; to add another approver before submitting.&quot;<br>12. Click Continue<br>13. Validate approver status updated from Pending(Orange) to Approved(Green)<br>14. Allow the Next Approver to Approve and update the Approver&#39;s Status to Approved<br>15. Login as 2nd approver. Repeat steps 6-13<br>16. Click Go back &gt; Click For My Approval/Assigned tab &gt; Click the RS Number that has been approved<br>17. Validate status change to Assigning (Yellow)</td><td class="s7"></td><td class="s5">Approving RS is successful</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">PRS-013-003</td><td class="s5">RS Approval</td><td class="s5">High</td><td class="s5">Validate requestor received notification after RS is Fully Approved</td><td class="s5">1. RS approved by all approvers</td><td class="s6">1. Login as the requestor of requsition slip<br>2. Click Notification bell<br>3. Validate notification message. <br>Title: <br>Requisition Slip Approved<br><br>Content:<br>Submitted Requisition Slip with RS Number of ### has been Approved by all of the Approvers. A Purchasing or Procurement Staff will be assigned to proceed with the Request Flow<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s7"></td><td class="s5">Requestor should receive notification of approved RS</td><td class="s9">Passed</td><td class="s13">Failed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-987">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-987</a></td></tr><tr style="height: 19px"><th id="1068162422R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">PRS-013-004</td><td class="s5">RS Approval</td><td class="s5">Minor</td><td class="s5">Validate redirection to RS once Notification message is clicked for Approved RS</td><td class="s5">1. RS approved by all approvers</td><td class="s5">1. Login as the requestor of requsition slip<br>2. Click Notification bell<br>3. Click the notification message<br>4. Should redirect you to requisition slip</td><td class="s7"></td><td class="s5">Should redirect you to a page after clicking the notification message</td><td class="s15">Failed</td><td class="s16">Blocked</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-809">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-987<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-809</a></td></tr><tr style="height: 19px"><th id="1068162422R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">PRS-013-005</td><td class="s5">RS Approval</td><td class="s5">High</td><td class="s6">Validate Level 1 approver approved after Level 2 approver approved works</td><td class="s5">1. Requestor submits RS<br>2. RS status is submitted<br>3. 2 approvers of Requisition Slip</td><td class="s5">1. Login as Level 2 approver of the requestor<br>2. Click Dashboard &gt; Click For My Approval/Assigned tab<br>3. Click 1 Ref Number with Submitted status<br>4. Validate Confirmation Message for Approving and Rejecting a Requisition Slip<br>5. Click Approve<br>6. Validate successful confirmation message and validate RS Level 2 approver in &#39;Approved&#39; status while Level 1 approver status is &#39;Pending&#39;<br>7. Logout as Level 2 approver<br>8. Login as Level 1 approver of the requestor<br>9. Repeat steps 2 - 5<br>10. Validate successful confirmation message and validate both RS Level 1 and Level 2 approver in &#39;Approved&#39; status<br>11. Validate that the overall status of RS is Assigning</td><td class="s7"></td><td class="s5">Approval is successful</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">PRS-013-006</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s5">Validate approver should be able to reject RS</td><td class="s5">1. Requestor submits RS<br>2. RS status is submitted</td><td class="s5">1. Login as approver of the requestor<br>2. Click Dashboard &gt; Click For My Approval/Assigned tab<br>3. Click 1 Ref Number with Submitted status<br>4. Validate Confirmation Message for Approving and Rejecting a Requisition Slip<br>5. Click Reject Button<br>6. Validate confirmation message &quot;You are about to disapprove this Requisition Slip. Please select a reason and press continue if you want to proceed with this action.&quot;<br>7. Validate Add Note (Optional) and input message<br>8. Add notes with 100 characters: This request has been rejected Please update the quantity 10 special characters !@#$%^&amp;()-+=&quot;&quot;?.,: AV<br>9. Validate Cancel and Continue buttons<br>10. Click Continue<br>11. Validate confirmation message that RS has been rejected<br>12. Validate overall status of RS is Rejected<br>13. Validate on dashboard that status of RS Number is Rejected on &#39;All&#39; and &#39;For My Approval/Assigned&#39; tab</td><td class="s7"></td><td class="s5">Reject is successful and reflects according to its status</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">PRS-013-007</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s5">Validate the rejected note added by approver appears on RS notes</td><td class="s5">1. RS has been rejected</td><td class="s5">1. Login as requestor of the rejected RS<br>2. Click the RS that has been rejected<br>3. Click Check notes button<br>4. Validate the Approver name appears and the rejected note added by the approver</td><td class="s7"></td><td class="s5">Approver rejected note should be visible to requestor</td><td class="s5" rowspan="6"></td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5">PRS-013-008</td><td class="s5">RS Approval</td><td class="s5">Minor</td><td class="s5">Validate requestor received notification when RS is rejected</td><td class="s5">1. Approver reject submitted RS</td><td class="s5">1. Login as requestor of rejected RS<br>2. Click notification bell<br>3. Validate notification message format:<br>Title: <br>Requisition Slip Rejected<br><br>Content:<br>Requisition Slip has been Rejected by one of the Approvers. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s7"></td><td class="s5">Notification message context received by the requestor</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5">PRS-013-009</td><td class="s5">RS Approval</td><td class="s5">Minor</td><td class="s5">Validate redirection to RS once Notification message is clicked for Rejected RS</td><td class="s5">1. RS approved by all approvers</td><td class="s5">1. Login as the requestor of requsition slip<br>2. Click Notification bell<br>3. Click the notification message<br>4. Should redirect you to requisition slip</td><td class="s7"></td><td class="s5">Clicking notification should redirect you to requisition slip</td><td class="s15">Failed</td><td class="s15">Failed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s6">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-990<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-809</td></tr><tr style="height: 19px"><th id="1068162422R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5">PRS-013-010</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s6">Validate next approver is not allowed to approve when prior approver rejected the Requistion Slip</td><td class="s5">1. Requestor submits RS<br>2. RS status is submitted<br>3. RS has 3 approvers</td><td class="s5">1. Login as Level 1 approver<br>2. Click Dashboard &gt; Click For My Approval/Assigned tab<br>3. Click 1 Ref Number with Submitted status<br>4. Click Approve<br>5. Validate Approver is in green status and displays &quot;Approved&quot;<br>6. Login as Level 2 approver<br>7. Click Dashboard &gt; Click For My Approval/Assigned tab<br>8. Click 1 Ref Number with Submitted status<br>9. Click Reject<br>10. Validate confirmation message that RS has been rejected<br>11. Login as Level 3 approver<br>12. Repeat steps 2-4<br>13. Validate Level 3 approver is not allowed to Approved and the Pending action message is not displayed<br>14. Validate overall status of RS is rejected<br>15. Validate on dashboard that status of RS Number is Rejected on &#39;All&#39; and &#39;For My Approval/Assigned&#39; tab</td><td class="s7"></td><td class="s5">Next approver not allowed to approve when prior approver rejected the RS</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">PRS-013-011</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s5">Validate requestor should be able to resubmit the rejected RS</td><td class="s7">1. RS status is rejected</td><td class="s18 softmerge"><div class="softmerge-inner" style="width:511px;left:-1px">1. Login as requestor of rejected RS<br>2. Click the Ref Number of rejected RS<br>3. Validate Edit Button is displayed<br>     a. Edit Item Details<br>     b. Upload and Delete an Attachment<br>     c. Add Note<br>5. Click Submit Button for the Approver to review the Requisition Slip again<br>    a. Should notify the Approver who has rejected the Requisition Slip through the Notification Bell</div></td><td class="s19"></td><td class="s5">Requestor should be able to re-submit after approver rejected</td><td class="s15">Failed</td><td class="s13">Failed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-812">https://youtrack.stratpoint.com/issue/CITYLANDPRS-812</a></td></tr><tr style="height: 19px"><th id="1068162422R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5">PRS-013-012</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s5">Validate approver receives notification after RS has been re-submitted</td><td class="s5">1. Requestor re-submit RS</td><td class="s5">1. Login as approver of the re-submitted RS<br>2. Click notification bell<br>3. Validate notification received<br>Title: <br>Requisition Slip has been resubmitted and for review<br><br>Content:<br>Requester has resubmitted the Requisition Slip that you have Rejected. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s7"></td><td class="s5">Approver received notification message for re-submitted RS</td><td class="s21">Blocked</td><td class="s16">Blocked</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-812">https://youtrack.stratpoint.com/issue/CITYLANDPRS-812</a></td></tr><tr style="height: 19px"><th id="1068162422R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5">PRS-013-013</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s5">Validate approver can re-approve re-submitted RS</td><td class="s5">1. RS has been re-submited by Requestor after being rejected by approver</td><td class="s5">1. Login as approver of the re-submitted RS<br>2. Validate Should notify the Approver who has rejected the Requisition Slip through the Notification Bell<br>2. Click Dashboard &gt; Click For My Approval/Assigned tab<br>3. Click 1 Ref Number with Submitted status<br>4. Click Approve<br>5. Validate Approver is in green status and displays &quot;Approved&quot;<br>6. Once all approver approved the RS, login as requestor</td><td class="s7"></td><td class="s5">Approver can re-approve re-submitted RS</td><td class="s5" rowspan="4"></td><td class="s21">Blocked</td><td class="s16">Blocked</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-812">https://youtrack.stratpoint.com/issue/CITYLANDPRS-812</a></td></tr><tr style="height: 19px"><th id="1068162422R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">PRS-013-014</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s5">Validate approver can add an additional Approver before it can proceed to the next Level of Approval</td><td class="s5">1. Submitted RS<br>2. RS with 3 approvers<br>3. RS with 2 approvers approved</td><td class="s6">1. Login as Level 1 approver<br>2. Click Dashboard &gt; Click For My Approval/Assigned tab<br>3. Click 1 Ref Number with Submitted status<br>4. Click Add button from Approvers<br>5. Validate pop-up message &quot;You are about to add an approver. Please select your designated approver and press &quot;Add Approver&quot; if you want to proceed with this action.&quot;<br>6. Validate  Search User Field<br>7. Click search field and validate it displays users with a User Types of<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Head<br>           iv) Division Head<br>           v) Area Staff<br>8. Once added, the Additional Approver should be displayed below the current Approver with a * as their Label<br>9. Validate successful confirmation</td><td class="s7"></td><td class="s6">Can successfully add additional approver before proceeding to next level of approver</td><td class="s15">Failed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">Bug has been raised</td><td class="s6">3/3: Passed when adding after level 1 approver but with bug when adding from level 1 approver<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-991<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-813</td></tr><tr style="height: 19px"><th id="1068162422R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">PRS-013-015</td><td class="s5">RS Approval</td><td class="s5">Critical</td><td class="s5">Validate RS approval is successful for optional approver</td><td class="s5">1. Approver adds additional approver</td><td class="s5">1. Login as optional approver<br>2. Validate notification bell that optional approver receives notification for Approval<br>3. Validate optional approver can approve before allowing to continue on the next Level of Approver<br>4. Validate confirmation message<br>5. Validate status in Approved reflected on Approver&#39;s name</td><td class="s7"></td><td class="s5">Successful approval for optional approver</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s5">PRS-013-016</td><td class="s5">RS Approval</td><td class="s5">Minor</td><td class="s6">Click Notification message after Requisition Slip has been Rejected</td><td class="s7"></td><td class="s5">1. Login as the requestor of requsition slip<br>2. Click Notification bell<br>3. Click the notification message<br>4. Should redirect you to dashboard</td><td class="s7"></td><td class="s5">Clicking notification should redirect you to requisition slip</td><td class="s15">Failed</td><td class="s13">Failed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s6">3/3: CITYLANDPRS-992<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-809</td></tr><tr style="height: 19px"><th id="1068162422R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s5">PRS-013-017</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s5">Validate requestor received notification when Requisition Slip updated by Approver</td><td class="s7"></td><td class="s5">1. Login as requestor of rejected RS<br>2. Click notification bell<br>3. Validate notification message format:<br>Title: <br>Requisition Slip has updated by the Approver<br><br>Content:<br>[APPROVER NAME] has updated the Requisition Slip you have created. Click here or access the Dashboard to review the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s7"></td><td class="s5">Requestor received notification when Requisition Slip updated by Approver</td><td class="s5" rowspan="4"></td><td class="s21">Blocked</td><td class="s22">Deprecated</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-812">https://youtrack.stratpoint.com/issue/CITYLANDPRS-812</a></td></tr><tr style="height: 19px"><th id="1068162422R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s5">PRS-013-018</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s6">Validate can successfully update the Request during approval</td><td class="s5">1. RS Submitted</td><td class="s6">1. Should display an Edit Button for the Items Table<br>    a. Once clicked, should allow editing of Requested Item Quantity<br>2. Should have Cancel and Submit Buttons<br>    a. If Cancel Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should not update the Requisition Slip<br>    b. If Submit Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should update the Requisition Slip<br>           i) Updates shall be Approved by the Current Approver and succeeding Approver<br>           ii) Should notify the Requester of the update</td><td class="s7"></td><td class="s5"> Successful update of the Request during approval</td><td class="s21">Blocked</td><td class="s22">Deprecated</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s6">3/3: Can&#39;t find user story anymore - Updating of Requisition Slip during Approval on latest copy of backlog for month 2. This has been initially defined based on QA test cases file tab - Month2_ACs &amp; Testing<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-812</td></tr><tr style="height: 19px"><th id="1068162422R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s5">PRS-013-019</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s5">Validate cancel button</td><td class="s7"></td><td class="s5">1. At the bottom of RS, click Cancel<br>2. Validate it should return to dashboard page</td><td class="s7"></td><td class="s5">Cancel button works</td><td class="s9">Passed</td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">PRS-013-020</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s6">Validate approver cannot cancel the RS</td><td class="s5">1. RS Submitted</td><td class="s6">1. Login as approver<br>2. Click RS Number<br>3. Click Cancel Request at bottom left</td><td class="s7"></td><td class="s6">Approver cannot cancel the RS</td><td class="s15">Failed</td><td class="s13">Failed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-817">https://youtrack.stratpoint.com/issue/CITYLANDPRS-817</a></td></tr><tr style="height: 19px"><th id="1068162422R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5"></td><td class="s5"></td><td class="s20"></td><td class="s6">Validate Requestor can Cancel Request</td><td class="s7"></td><td class="s6">1. Login as requestor<br>2. Submit an RS<br>3. Click Cancel Requet<br>4. Validate successful msg &quot;Requestistion Cancelled successfully&quot;&quot;</td><td class="s7"></td><td class="s6">Redirects back to darshboard and requestor successfully cancelled the RS with status Cancelled</td><td class="s5"></td><td class="s23"></td><td class="s10">Passed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1068162422R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">PRS-013-021</td><td class="s5">RS Approval</td><td class="s20"></td><td class="s6">Validate notification for RS has been Cancelled</td><td class="s7"></td><td class="s6">1. Login as requestor of cancelled RS<br>2. Validate notification received:<br>Title: <br>Requisition Slip Cancelled<br><br>Content:<br>Requisition Slip with a RS Number of ### has been Cancelled by the Requester. Click here or through Dashboard to review the Requisition Slip<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s7"></td><td class="s5">Notification message context received when has been Cancelled</td><td class="s5"></td><td class="s15">Failed</td><td class="s13">Failed</td><td class="s11"></td><td class="s12">Not Started</td><td class="s11"></td><td class="s5">bug has been raised</td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-819">https://youtrack.stratpoint.com/issue/CITYLANDPRS-819</a></td></tr><tr style="height: 19px"><th id="1068162422R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s24"></td><td class="s7">RS Approval</td><td class="s20"></td><td class="s6">Validate that RS Approvers will display approver according to project and supervisor</td><td class="s7"></td><td class="s6">1. Login as requestor<br>2. Submit an RS<br>3. Login as admin<br>4. Take note the approver of the project where RS is submitted<br>5. Login as root user<br>6. Take note the supervisor of the requestor<br>7. Login as requesto again and verify your submitted RS if all approvers are from the project approver and from your assigned supervisor</td><td class="s5"></td><td class="s6">RS Approvers will display approver according to project and the requesotr superviosr only</td><td class="s5"></td><td class="s25"></td><td class="s16">Blocked</td><td class="s11"></td><td class="s12">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s26"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1009">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1009</a></td></tr></tbody></table></div>