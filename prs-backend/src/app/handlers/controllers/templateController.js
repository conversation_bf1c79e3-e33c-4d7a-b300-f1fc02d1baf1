const fs = require('node:fs/promises');
const fsSync = require('node:fs');
const path = require('path');

const HTML_FOLDER = path.join(__dirname, '../../../templates/html');
const PDF_FOLDER = path.join(__dirname, '../../../templates/pdf');

class Template {
  constructor(container) {
    const { templateService, fastify, clientErrors } = container;

    this.fastify = fastify;
    this.templateService = templateService;
    this.clientErrors = clientErrors;
  }

  async generateTemplate(request, reply) {
    this.fastify.log.info(`Generating HTML to PDF templates...`);

    try {
      await fs.access(HTML_FOLDER);
      const stats = await fs.stat(HTML_FOLDER);

      if (!stats.isDirectory()) {
        throw this.clientErrors.NOT_FOUND({
          message: `Input folder "${HTML_FOLDER}" is not a directory.`,
        });
      }

      if (!fsSync.existsSync(PDF_FOLDER)) {
        try {
          await fs.mkdir(PDF_FOLDER, { recursive: true });
        } catch (mkdirError) {
          console.error(
            `Error creating output folder "${PDF_FOLDER}":`,
            mkdirError,
          );
          reply.status(500).send({
            error: `Failed to create output folder: ${mkdirError.message}`,
          });
          return;
        }
      }

      const templates = await fs.readdir(HTML_FOLDER);

      if (templates.length === 0) {
        throw this.clientErrors.NOT_FOUND({
          message: `No Templates files found in HTML folder.`,
        });
      }

      await this.templateService.generateTemplate(
        templates,
        HTML_FOLDER,
        PDF_FOLDER,
      );

      return reply.status(200).send({
        meta: 'Successfully created PDF templates',
      });
    } catch (error) {
      throw error;
    }
  }

  async requisitionPDF(request, reply) {
    const { id: requisitionId } = request.params;

    this.fastify.log.info(`Generating requisition slip PDF...`);

    try {
      const result = await this.templateService.requisitionPDF(
        requisitionId,
        PDF_FOLDER,
      );

      this.fastify.log.info(
        `PDF generated, sending response with ${result.pdfBytes.length} bytes`,
      );

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(result.pdfBytes);
    } catch (error) {
      this.fastify.log.error(
        `Error in requisitionPDF controller: ${error.message}`,
      );
      throw error;
    }
  }
}

module.exports = Template;
