<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#a4c2f4;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d0e0e3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b6d7a8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffe599;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#e6b8af;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ea9999;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b4a7d6;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9d2e9;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle no-grid" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1865166854C0" style="width:103px;" class="column-headers-background">A</th><th id="1865166854C1" style="width:160px;" class="column-headers-background">B</th><th id="1865166854C2" style="width:252px;" class="column-headers-background">C</th><th id="1865166854C3" style="width:252px;" class="column-headers-background">D</th><th id="1865166854C4" style="width:233px;" class="column-headers-background">E</th><th id="1865166854C5" style="width:330px;" class="column-headers-background">F</th><th id="1865166854C6" style="width:184px;" class="column-headers-background">G</th><th id="1865166854C7" style="width:208px;" class="column-headers-background">H</th><th id="1865166854C10" style="width:126px;" class="column-headers-background">K</th><th id="1865166854C11" style="width:126px;" class="column-headers-background">L</th><th id="1865166854C12" style="width:148px;" class="column-headers-background">M</th><th id="1865166854C13" style="width:148px;" class="column-headers-background">N</th><th id="1865166854C14" style="width:148px;" class="column-headers-background">O</th><th id="1865166854C15" style="width:148px;" class="column-headers-background">P</th><th id="1865166854C16" style="width:78px;" class="column-headers-background">Q</th><th id="1865166854C17" style="width:125px;" class="column-headers-background">R</th></tr></thead><tbody><tr style="height: 42px"><th id="1865166854R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Result<br>(wk4+wk5)</td><td class="s1">STG Status<br>(wk4+wk5)</td><td class="s1">Status<br>(E2E_Run1)</td><td class="s1">Actual Results<br>(E2E_Run1)</td><td class="s1">Status<br>(E2E_Run2)</td><td class="s1">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1865166854R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="16">PRS-024 - [Steelbars] Tagging of Steelbars in OFM Item List, Adding of Dimensions for the Steelbars, RS with Steelbars, Canvassing for Steelbars Item Group,  Purchase Order for Steelbars, Payment Request for Steelbars , Delivery Record for Steelbars, Delivery Receipt for Steelbars</td></tr><tr style="height: 19px"><th id="1865166854R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3"></td><td class="s4">Verify OFM Items - Tagging of Steelbars</td><td class="s3">High</td><td class="s3">Verify Steel Bars? Column</td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Is in Dashboard page</a></td><td class="s3">1. Click Item Menu<br>2. Click OFM</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3">- Should be redirected to OFM Items Page<br>- OFM Items Table should be visible<br>- Steel Bars? Column should be visible in the OFM Items Table</td><td class="s5" rowspan="26"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1yQv5iYv1t1mKKst0YeoQDANawRhCPJMKKgpfLPjfFDw/edit?gid=0#gid=0">https://docs.google.com/spreadsheets/d/1yQv5iYv1t1mKKst0YeoQDANawRhCPJMKKgpfLPjfFDw/edit?gid=0#gid=0</a></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/12PGm1XPyOkQ2Skhju59REfdSCiE8Z7QRTdJiaGC0niI/edit?gid=642851613#gid=642851613">https://docs.google.com/spreadsheets/d/12PGm1XPyOkQ2Skhju59REfdSCiE8Z7QRTdJiaGC0niI/edit?gid=642851613#gid=642851613</a></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1865166854R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3"></td><td class="s4">Verify OFM Items - Tagging of Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Steel Bars Toggle</td><td class="s5" rowspan="3"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Is in OFM Items Landing Page</a></td><td class="s3">1. Verify Steel Bars? Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3">- Should display a toggle button under Steel Bars? Column<br>- Toggle should be set as No by Default</td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1865166854R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s3"></td><td class="s4">Verify OFM Items - Tagging of Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Steel Bars Toggle Toggle Function</td><td class="s3">1. Click Toggle Button</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display a Confirmation Modal:<br><br>- Once Confirmed, should tag the Item as a Steelbar<br>- Else, Toggle should be still set as No<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- If tagged as &quot;yes&quot; in Steel Bars? it should display under their respective Trades<br>- Success Notification should be visible</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1865166854R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3"></td><td class="s4">Verify OFM Items - Tagging of Steelbars</td><td class="s3">High</td><td class="s3">Verify If Item contains Steel Bar properties once Toggled &quot;Yes&quot; as Steel Bars?</td><td class="s3">1. Click Toggle Button<br>2. Click Pen Icon<br>3. Verify Fields</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display the following fields once tagged as Steelbars:<br><br>Grade<br>Diameter<br>Length<br>Weight<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>Fields should have &quot;Select an option&quot; as default values</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1865166854R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s3"></td><td class="s4">Verify Steelbar access - With Access</td><td class="s3">High</td><td class="s3">Verify if OFM Menu is Visible and can access OFM Item Masterlist</td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Any of these User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Is in OFM Items Landing Page</a></td><td class="s3"> </td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned User Types:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Engineers</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_engineer<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 4829#0w/t<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">IT Admin</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_stg_admin<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: Test@123<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Purchasing Head</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: cherry_stg_PurchasingHead<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: Cherry_14</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should be able to Access Item Page and OFM Item Masterlist<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>- Should be able to see OFM Items Table with Steel Bars? Column<br>- Should be able to toggle Steel Bars</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 641px"><th id="1865166854R7" style="height: 641px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 641px">8</div></th><td class="s3"></td><td class="s4">Verify Steelbar access - Without Access</td><td class="s3">High</td><td class="s3">Verify if OFM Menu is not Visible and User cannot access OFM Item Masterlist</td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Any of these User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Is in Dashboard Page</a></td><td class="s3">1. Verify Item Menu</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned User Types:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Root User</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: rootuser<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: rootuser<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Supervisor</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: Kurt-supervisor<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 303#M1B0y<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Assistant Manager</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_assistant_manager<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 1848#Xk6/<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Department Head</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_department_head<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 1803#w+MM<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Department Secretary</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_secretary<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 2928#WElL<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Division Head</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: cherry_stg_DivisionHead<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: Cherry_14<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Area Staff</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_area_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 9218#Fovf<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Management</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: cherry_stg_Management<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: Cherry_14</span></td><td class="s3">Should not see Item Menu on Dashboard</td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">High</td><td class="s3">Verify Steel Bar Item Page</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Is in OFM Items Landing Page</td><td class="s3">1. Click Item Code Tagged &quot;Yes&quot; in Steel Bars? Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should redirect User to specific Item Page displaying Item Code, Item Details, and History<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>Item Details Fields should be uneditable<br>History should have a Search Bar and should display a maximum of </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">5/10</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> items </span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">Critical</td><td class="s3">Verify Adding Dimensions of Steel Bars - Edit Item Fields</td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page</a></td><td class="s3">1. Find Specific Item Tagged &quot;Yes&quot; on Steel Bars Column<br>2. On the action column, Click Pen Icon<br>3. Click Edit<br><br>Other Entry Way:<br>1. Click Edit</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should enabled the following Fields:<br><br>i. Grade <br>ii. Diameter<br>iii. Length<br>iv. Weight<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: <br>- The following should have uneditable fields:<br>        i. Item Code<br>        ii. Item Description<br>        iii. Unit<br>        iv. Account Code<br>        v. GFQ(Weight in kg)<br>        vi. Remaining GFQ(Weight in kg)<br>        vii. Company<br>        viii. Project<br>        ix. Trade<br>- Save and Cancel button should only be enabled if there are changes in the enabled fields</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Need to Clarify Unit Field on what its default behaviour</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Adding Dimensions of Steel Bars - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Drop Down Menu Validation: Grade</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page&quot;<br>&quot;1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>9. Entered Steel Bar Item Page<br>10. Entered Item Edit Modal<br>11. Is in Edit Modal&quot;<br><br><br><br></td><td class="s3">1. Click Grade Field</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3">Should have the Following Choices:<br><br>Grade of the Steelbar<br>   i) 40<br>   ii) 60<br>   iii) 80</td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Adding Dimensions of Steel Bars - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Drop Down Menu Validation: Diameter</span></td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page</a></td><td class="s3">1. Click Diameter Field</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have the Following Choices:<br><br>Diameter of the Steelbar<br>   i) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Grade 40</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         a) 10mm<br>         b) 12mm<br>         c) 16mm<br>         d) 20mm<br>         e) 25mm<br>         f) 28mm<br>         g) 32mm<br>         h) 36mm<br>   ii) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Grade 60 and Grade 80</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         a) 10mm<br>         b) 12mm<br>         c) 16mm<br>         d) 20mm<br>         e) 25mm<br>         f) 28mm<br>         g) 32mm<br>         h) 36mm<br>         i) 40mm<br>         j) 50mm</span></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Still have the same issue - Contains addition options not in AC<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br>Added new comment in Youtrack</span></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-913">https://youtrack.stratpoint.com/issue/CITYLANDPRS-913</a></td></tr><tr style="height: 19px"><th id="1865166854R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Adding Dimensions of Steel Bars -</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> Drop Down Menu Validation: Length</span></td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page</a></td><td class="s3">1. Click Length Field</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3">Should have the Following Choices:<br><br>Length of the Steelbar<br>  i) 6.0m<br>  ii) 7.5m<br>  iii) 9.0m<br>  iv) 10.5m<br>  v) 12.0m</td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Still have the same issue - Contains addition options not in AC<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Added new comment in Youtrack</span></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-914">https://youtrack.stratpoint.com/issue/CITYLANDPRS-914</a></td></tr><tr style="height: 19px"><th id="1865166854R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Adding Dimensions of Steel Bars -</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> Drop Down Menu Validation: Weight</span></td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page</a></td><td class="s3">1. Click Weight Field</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3">Should have the Following Choices:<br><br>Weight of the Steelbar<br>    i) 0.617<br>    ii) 0.888<br>    iii) 1.578<br>    iv) 2.466<br>    v) 3.853<br>    vi) 4.834<br>    vii) 6.313<br>    viii) 7.990</td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Still have the same issue - Addition options not in AC<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br>Added new comment in Youtrack</span></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-929">https://youtrack.stratpoint.com/issue/CITYLANDPRS-929</a></td></tr><tr style="height: 19px"><th id="1865166854R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Adding Dimensions of Steel Bars -</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> Button: Save</span></td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page</a></td><td class="s3">1. Fill in Fields<br>2. Click Save</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should save all changes and close modal redirecting User back to Specific Item Page<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>- Save should be disabled by default and ONLY enabled if User edited any Fields<br>- Should reflect changes in Specific Item Page after clicking save</span></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Still doesn&#39;t reflect option chosen even though it said it is saved<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br>Added new comment in Youtrack</span></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-915">https://youtrack.stratpoint.com/issue/CITYLANDPRS-915</a></td></tr><tr style="height: 19px"><th id="1865166854R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s3"></td><td class="s8">Verify OFM Items - Adding of Dimensions for Steel Bars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Adding Dimensions of Steel Bars -</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> Button: Cancel</span></td><td class="s5"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars<br>6. Some Items were tagged as a Steelbar<br>7. Entered OFM Items Page<br>8. Is in OFM Items Landing Page<br><br>Other Entry Way:<br>9. Entered Steel Bar Item Page<br>10. Is in Steel Bar Item Page</a></td><td class="s3">1. Click Cancel</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Assigned Purchasing Staff User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_purchasing_staff<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 8825#mqZj</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should delete any changes and close page redirecting User back to OFM Masterlist<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>Any changes should not reflect in the Specific Item Page when changes are cancelled </span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">if Steel Bars Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> are selected - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Items</span></td><td class="s5" rowspan="30"><a target="_blank" href="http://*************/login">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Engineers User Type<br>4. Should be assigned to Trade Code, Civil and Architechtural works<br>5. Should have an OFM Synced for Steel Bars in ALL trades<br>6. Clicked New Request in Dashboard<br>7. Is in Create New Requistion Slip Page</a></td><td class="s3">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s tagged as steelbars in OFM Masterlist<br>4. Click &quot;Add Item/s&quot; button</td><td class="s3" rowspan="30"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Engineer User Type:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">username</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: kurt_engineer<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">pass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">: 4829#0w/t</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">ONLY Steel Bars Tab should appear below &quot;Add Item/s&quot; button.<br><br>Steel Bars Tab should contain items selected on Add Item/s modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">if Steel Bars Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> are selected - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Transfer of Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist<br>4. Click &quot;Add Item/s&quot; button</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">ONLY Steel Bars Tab should appear below &quot;Add Item/s&quot; button.<br><br>Steel Bars Tab should contain items selected on Add Item/s modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">if Other Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> are selected - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">not tagged as steelbars </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">in OFM Masterlist<br>4. Click &quot;Add Item/s&quot; button</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">ONLY Items Tab should appear below &quot;Add Item/s&quot; button<br><br>Items Tab should contain items selected on Add Item/s modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">if Other Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> are selected - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Transfer of Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">not tagged as steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist<br>4. Click &quot;Add Item/s&quot; button</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">ONLY Items Tab should appear below &quot;Add Item/s&quot; button<br><br>Items Tab should contain items selected on Add Item/s modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">if Both Steelbar and Other Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> are selected - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged and not tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist<br>4. Click &quot;Add Item/s&quot; button</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Two tabs should appear below &quot;Add Item/s&quot; button.<br><br>- Items <br>- Steel Bars<br><br>Items Tab should contain items not tagged as Steel Bars<br>Steel Bars Tab should contain items tagged as Steel Bars<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">if Both Steelbar and Other Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> are selected - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Transfer of Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged and not tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist<br>4. Click &quot;Add Item/s&quot; button</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Two tabs should appear below &quot;Add Item/s&quot; button.<br><br>- Items <br>- Steel Bars<br><br>Items Tab should contain items not tagged as Steel Bars<br>Steel Bars Tab should contain items tagged as Steel Bars<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Non-OFM Items</span></td><td class="s3">1. Select &quot;Non-OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick any Item in Item List<br>4. Click &quot;Add Item/s&quot; button</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">ONLY Items Tab should appear below &quot;Add Item/s&quot; button<br><br>Items Tab should contain items selected on Add Item/s modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request<br>- Item List should not contain &quot;Civil and Architecture Works&quot; as an item</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars tab display</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Steel Bars tab is displayed in the Requistion Slip - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Non-OFM Transfer of Items</span></td><td class="s3">1. Select &quot;Non-OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick any Item in Item List<br>4. Click &quot;Add Item/s&quot; button</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">ONLY Items Tab should appear below &quot;Add Item/s&quot; button<br><br>Items Tab should contain items selected on Add Item/s modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- &quot;Add Item/s&quot; button should be disabled by default<br>- &quot;Add Item/s&quot; button should be enabled once User chooses Type of Request<br>- Item List should not contain &quot;Civil and Architecture Works&quot; as an item</span></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Mechanical Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Mechanical Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s11">Not Run</td><td class="s11">Not Run</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">No Current Items in Mechanical Works</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Electrical Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Electrical Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Plumbing &amp; Sanitary Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Plumbing &amp; Sanitary Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Fire Protection Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Fire Protection Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Transfer of Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Mechanical Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Mechanical Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s3"></td><td class="s11">Not Run</td><td class="s11">Not Run</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">No Current Items in Mechanical Works</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Transfer of Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Electrical Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Electrical Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Transfer of Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Plumbing &amp; Sanitary Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Plumbing &amp; Sanitary Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s3"></td><td class="s10">RS with Steelbars - OFM Transfer of Items - Verify Items tagged as Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if in </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">Fire Protection Works Trade</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> there are Items tagged as Steelbars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick &quot;Fire Protection Works&quot; in Item List<br>4. Search for Items </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged as Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> in OFM Masterlist</span></td><td class="s3">- Should be able to tick and add other items in the Selected Item/s list<br>- Items selected should display below the &quot;Add Item/s&quot; button in the Items Tab<br>- Steel Bars Tab should be displayed<br><br>Note:<br>- Should be able to only select ONE trade in Item List</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify if Steel Bars Columns are complete</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Select &quot;OFM&quot; or &quot;OFM Transfer of Items&quot; in Type of Request<br>2. Click &quot;Add Item/s&quot; button<br>3. Tick Item/s </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">tagged </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">as Steel Bars <br>4. Click &quot;Add Item/s&quot; button</span></td><td class="s3">Steel Bars Table should be visible and displays item selected<br><br>Table should contain the following columns:<br><br>a. Item<br>b. Account Code<br>c. Unit<br>d. Remaining GFQ<br>e. Grade<br>f. Diameter<br>g. Length<br>h. Weight<br>i. Quantity<br>j. Actions</td><td class="s3"></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Note column is still visible even if it is not listed in AC<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Added new comment in Youtrack</span></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-930">https://youtrack.stratpoint.com/issue/CITYLANDPRS-930</a></td></tr><tr style="height: 19px"><th id="1865166854R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s3"></td><td class="s10">RS with Steelbars - </td><td class="s3">Critical</td><td class="s3">Verify Item Column</td><td class="s3">1. Verify Item Column </td><td class="s3">Should display Selected Item/s name and is uneditable by default</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify Account Code Column</td><td class="s3">1. Verify Account Code Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Selected Item/s Account Code and is uneditable by Default<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"><br>Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>As per miss Irene, Account Code is provided by Cityland</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s3"></td><td class="s10">RS with Steelbars - </td><td class="s3">Critical</td><td class="s3">Verify Unit Column</td><td class="s3">1. Verify Unit Column</td><td class="s3">Should display Selected Item/s Unit and is uneditable by Default</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify Remaining GFQ Column</td><td class="s3">1. Verify Remaining GFQ Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Selected Item/s Remaining GFQ of the Item selected and is uneditable by Default<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">:<br>As per miss Irene, GFQ is provided by Cityland. Only computation needed is the following:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">FOR FIRST REQUEST OF THE ITEM<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">GFQ - Request Quantity = Remaining GFQ<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">AFTER FIRST REQUEST OF ITEM</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Last Remaining GFQ - Request Quantity = New Remaining GFQ</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s3"></td><td class="s10">RS with Steelbars - </td><td class="s3">Critical</td><td class="s3">Verify Grade Column</td><td class="s3">1. Verify Grade Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display the following and is uneditable by default:<br><br>i) 40<br>ii) 60<br>iii) 80<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Grade of the Steel Bar is defined in the OFM Item Management</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify Diameter Column</td><td class="s3">1. Verify Diameter Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display the following and is uneditable by default:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Grade 40</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>i) 10mm<br>ii) 12mm<br>iii) 16mm<br>iv) 20mm<br>v) 25mm<br>vi) 28mm<br>vii) 32mm<br>viii) 36mm<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Grade 60 and Grade 80</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>i) 10mm<br>ii) 12mm<br>iii) 16mm<br>iv) 20mm<br>v) 25mm<br>vi) 28mm<br>vii) 32mm<br>viii) 36mm<br>ix) 40mm<br>x) 50mm<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Diameter of the Steel Bar is defined in the OFM Item Management</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s3"></td><td class="s10">RS with Steelbars - </td><td class="s3">Critical</td><td class="s3">Verify Length Column</td><td class="s3">1. Verify Length Column</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display the following and is uneditable by default:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Values of</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>i) 6.0m<br>ii) 7.5m<br>iii) 9.0m<br>iv) 10.5m<br>v) 12.0m<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Length of the Steel Bar is defined in the OFM Item Management</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify Weight Column</td><td class="s3">1. Verify Weight Column</td><td class="s5"><a target="_blank" href="#gid=null">Should display the computed value base on Selected Grade, Diameter and Length and is uneditable by default<br><br>Note:<br>- Please base the computation on this Steel Bar Table<br>- Should be cross-checked to get the value for Weight(kg/m) and have it multiplied with the Length</a></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify Quantity Column</td><td class="s3">1. Verify Quantity Column<br>2. Input numbers and characters in the Quantity Field</td><td class="s3">- Should be editable by default<br>- Should be able to input ONLY numbers <br>- Should ONLY accept a maximum of 3 Characters</td><td class="s3"></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Quantity field can still go higher than 3 characters and less than 0 and also accepts 0 as an input<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Added new comment in Youtrack</span></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-932">https://youtrack.stratpoint.com/issue/CITYLANDPRS-932</a></td></tr><tr style="height: 19px"><th id="1865166854R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s3"></td><td class="s10">RS with Steelbars - Verify RS Steel Bars Table</td><td class="s3">Critical</td><td class="s3">Verify Action Column</td><td class="s3">1. Verify Action Column<br>2. Click Remove Line button</td><td class="s3">- A red box icon should be visible<br>- When clicked, it should be able to remove the item</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s3"></td><td class="s12">RS with Steelbars - Verify RS Submission with Steel Bars</td><td class="s3">High</td><td class="s3">Verify Save as Draft function</td><td class="s3">1. Fill in all required fields<br>2. Add Steel bars and other items<br>3. Click Save as draft</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should be enabled as default and display a confirmation modal <br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user saves:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should save all data inputted as well as items added on the Requisition Slip and redirects user back to Requisition Slip. Displaying draft saved with Date and Timestamps<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user cancels:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should close confirmation modal and redirects user back to Requisition Slip</span></td><td class="s3"></td><td class="s13">Blocked</td><td class="s13">Blocked</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Still doesn&#39;t reflect option chosen even though it said it is saved</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-911">CITYLANDPRS-911</a></td></tr><tr style="height: 19px"><th id="1865166854R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s3"></td><td class="s12">RS with Steelbars - Verify RS Submission with Steel Bars</td><td class="s3">High</td><td class="s3">Verify Cancel function</td><td class="s3">1. Fill in all required fields<br>2. Add Steel bars and other items<br>3. Click Cancel</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should be enabled as default and display a confirmation modal <br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user cancels:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should close confirmation modal and redirects user back to Requisition Slip</span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:line-through;color:#000000;"><br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user continues:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should erase all inputted data as well as added items on the Requisition Slip and redirects user to dashboard deleting the Requisiting Slip. Newly created Requisition Slip should not be visible in Dashboard</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s3"></td><td class="s12">RS with Steelbars - Verify RS Submission with Steel Bars</td><td class="s3">Critical</td><td class="s3">Verify Submit function</td><td class="s3">1. Fill in all required fields<br>2. Add Steel bars and other items<br>3. Click Submit</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should be disable as default and should only be enabled after all required fields are filled<br>- Should display confirmation modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user submits:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should save all data inputted as well as items added on the Requisition Slip and redirects user to dashboard. Displaying submitted Requisition Slip with status &quot;Submitted&quot;<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user cancels:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should close confirmation modal and redirects user back to Requisition Slip<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should not require the Requisition Slip to be Save as Draft in order to proceed with Submitting the Requisition Slip</span></td><td class="s3"></td><td class="s11">Not Run</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s14"></td><td class="s15">Canvassing for Steelbars Item Group - Verify RS with Steel Bars Canvass Sheet</td><td class="s3">Critical</td><td class="s3">Verify if Canvass Sheet is accessible</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items<br>5. Requisition Slip should be approved by Approvers<br>6. Requisition Slip Purchasing Staff has been assigned<br>7. Is in the approved Requisition Slip with Steel Bars and other Items</td><td class="s3">1. Click Select Actions<br>2. Click Enter Canvas<br><br>Note: <br>Related Documents - Another Entry way to Enter Canvass Sheet</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should redirect to Canvas Sheet, displaying Blank Canvass Sheet<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- User should be the Assigned Purchasing Staff<br>- Created Requisition Slip should be already approved<br>- Created Requisition Slip should have Assigned Purchasing Staff</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s14"></td><td class="s15">Canvassing for Steelbars Item Group - Verify RS with Steel Bars Canvass Sheet</td><td class="s3">Critical</td><td class="s3">Verify Canvass Sheet &quot;Add item/s&quot;</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Is in Canvas Sheet page</td><td class="s3">1. Click Add Item/s</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should display a pop-up modal displaying Items that should ONLY be the Items provided in Requisition Slip</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s14"></td><td class="s15">Canvassing for Steelbars Item Group - Verify RS with Steel Bars Canvass Sheet</td><td class="s3">Critical</td><td class="s3">Verify Items and Steel Bars tab</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Is in Canvas Sheet page</td><td class="s3">2. Select Multiple kinds of Items containing Steel Bars and Non-Steel Bars under Item Selection Modal<br>3. Click Add Item/s</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should close Add Item/s pop-up modal<br>- Should display two separate Tabs named Items and Steel Bars in Canvas Sheet Page<br>    a. Items Tab should contain other OFM Items added that were not Steel Bars<br>         i.) Should have the following columns:<br>             - Item<br>             - Account Code<br>             - Unit<br>             - Quantity<br>             - Canvas Status<br>             - Canvas<br>             - Actions<br><br>    b. Steel Bars Tab should display the Steel Bar Items added<br>          i.) Should have the following columns:<br>              - Checkbox<br>              - Item<br>              - Account Code<br>              - Unit<br>              - Weight(kg)<br>              - Qty/Pcs<br>              - Canvas Status<br>              - Canvas<br>              - Actions<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note: </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Canvas Status should be &quot;New&quot;<br>- Features in Action Column should be all disabled</span></td><td class="s3"></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Item name doesn&#39;t reflect and still contains missing columns</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-941">https://youtrack.stratpoint.com/issue/CITYLANDPRS-941</a></td></tr><tr style="height: 19px"><th id="1865166854R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Enter Canvass feature - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Items</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a OFM Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Is in Canvas Sheet Page</td><td class="s3">1. Go to Steel Bars Tab<br>2. Find any Item to Enter Canvass<br>3. Click Enter Canvass under Canvas Column</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display a pop-up modal for selecting and adding suppliers and other information needed for Canvassing<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Weight(kg) field should be disabled by default<br>- Unit Price (Discounted) field should be disabled by default<br>- Should be able to add a maximum of 4 supplier</span></td><td class="s3"></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Still missing Fields </td><td class="s5" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-944">https://youtrack.stratpoint.com/issue/CITYLANDPRS-944</a></td></tr><tr style="height: 19px"><th id="1865166854R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Enter Canvass feature - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">OFM Transfer Items</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a OFM Transfer of Items Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Is in Canvas Sheet Page</td><td class="s3" rowspan="2">1. Go to Steel Bars Tab<br>2. Find any Item to Enter Canvass<br>3. Click Enter Canvass under Canvas Column</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display a pop-up modal for selecting and adding suppliers and other information needed for Canvassing<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Weight(kg) field should be disabled by default<br>- Unit Price (Discounted) field should be disabled by default<br>- Should not be able to add other suppliers, limited to 1.</span></td><td class="s3"></td><td class="s13">Blocked</td><td class="s13">Blocked</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Enter Canvass feature - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Fields</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a OFM Transfer of Items Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Is in Canvas Sheet Page</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have the following Fields<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">a. Supplier</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Should be Drop-down Values of Company and Projects<br>        ii. Should only allow entering of one Supplier<br>           i) Cannot add another Supplier<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">b. Terms</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Should be Drop-down Values of<br>           i) NET 15 <br>           ii) NET 30<br>           iii) Cash in Advance (CIA)<br>           iv) Cash on Delivery (COD)<br>           v) 10% DP, Balance upon delivery<br>           vi) 20% DP, Balance upon delivery<br>           vii) 30% DP, Balance upon delivery<br>           viii) 50% DP, Balance upon delivery<br>           ix) 80% DP, Balance upon delivery<br>           x) 10% DP, PB, 10% RETENTION<br>           xi) 20% DP, PB, 10% RETENTION<br>           xii) 30% DP, PB, 10% RETENTION<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">c. Quantity</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Quantity that was communicated by the Supplier to the Purchasing Staff<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">d. Unit Price</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Unit Price that was communicated by the Supplier to the Purchasing Staff<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">e. Discount</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Fixed Amount - the fixed Amount of the Item that was already discounted<br>           i) Should display to the Unit Price (discounted)<br>        ii. Percentage - the Discount Percentage communicated by the Supplier to the Purchasing Staff<br>           i) Should get the lessen percentage of the Unit Price that should be deducted to the Unit Price<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">f. Unit Price (discounted)</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Computed if a Discount has been entered<br>        ii. Should be disabled<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">g. Attachments</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>       i. When Attachment Field is clicked should implement a File Validation<br>          i) File Formats should be PDF, Doc File, Excel or CSV, JPG, PNG, JPEG<br>          ii) Allow multiple file for uploading, Maximum File size is 25MB per File<br>    </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">h. Notes</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        i. Should allow 100 Characters for the Notes that will have Alphanumeric and Sepcial Characters except Emojis</span></td><td class="s3"></td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Still encountering issues</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-946">https://youtrack.stratpoint.com/issue/CITYLANDPRS-946</a></td></tr><tr style="height: 19px"><th id="1865166854R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Submission of Enter Canvass - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Delete</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Entered an item&#39;s Canvas<br>8. Is in Item&#39;s Canvas Sheet modal</td><td class="s3">1. Fill all required fields<br>2. Click Delete</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If more than one supplier:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should delete selected supplier and reduce number of supplier tab by 1<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">if only one supplier:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>All details inputted should be deleted and modal should still retain open<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">if user cancels:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Should redirect User back to Enter Canvass Modal with all data retained<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note: </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Canvas status should still be &quot;New&quot; and Action features are still disabled<br>in </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;">OFM Transfer of Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> &quot;Delete&quot; buttton should not be visible</span></td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3">Need confirmation with BA on proper flow</td></tr><tr style="height: 19px"><th id="1865166854R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Submission of Enter Canvass - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Cancel</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Entered an item&#39;s Canvas<br>8. Is in Item&#39;s Canvas Sheet modal</td><td class="s3">1. Fill all required fields<br>2. Click Cancel</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should close the Enter Canvass Modal and delete all data inputted on the modal</td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Submission of Enter Canvass - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Submit</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Entered an item&#39;s Canvas<br>8. Is in Item&#39;s Canvas Sheet modal</td><td class="s3">1. Fill all required fields<br>2. Click Submit</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should save all data inputted on the modal and close the Enter Canvass Modal and redirect User to Canvas Sheet<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- If Confirm Button is clicked, should check the entered Supplier Quantity<br>        i. Supplier Quantity should be equal or greater than the Request Quantity <br>           i) If the Supplier Quantity is less than the Request Quantity, should disbale the Button and require to enter another Supplier<br>              </span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:line-through;color:#000000;">a) Else User can click Save Draft Button to retain the entered Suppliers</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        ii. Once Confirmed, should add the Supplier to the Item<br>            i) Should disable the Enter Canvass Button<br>               a) Should change the Button Text to Canvass Entered<br>            ii) Should Enable the Actions Buttons<br>            iii) Should update the Status to For Approval</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Viewing of Supplier - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Down Icon</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Entered an item&#39;s Canvas<br>8. Is in Item&#39;s Canvas Sheet modal</td><td class="s3">1. Find Item with supplier/s<br>2. Click Down Icon</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display containers below requested Item<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Containers should reflect the details and the amount of supplier added by Purchasing Staff<br>- Should always have a pen icon on the top right of the containers</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Viewing of Supplier - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Eye Icon</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Entered an item&#39;s Canvas<br>8. Is in Item&#39;s Canvas Sheet modal</td><td class="s3">1. Find Item with supplier/s<br>2. Click Eye Icon</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display a pop-up modal of View Canvass with the Supplier/s full Details<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Supplier 1 should be the default supplier opened<br>All fields should be disabled<br>Should always have an Edit button on the top right</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Canvass Sheet - Steel Bars Tab</td><td class="s3">Critical</td><td class="s3">Verify Editing of Supplier</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Entered an item&#39;s Canvas and added supplier/s<br>8. Is in Canvas Sheet Page</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Find Item with supplier/s<br>2. Click either the Pen Icon, Down Icon or Eye Icon<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Down Icon:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>3. Click Pen Icon on the specific container<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Eye Icon:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>3. Click Edit on the specific supplier</span></td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Pen Icon:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Should display a pop-up modal of Edit Canvass that will allow updating of Supplier and/or Supplier Details<br><br>Note:<br>Supplier 1 should be the default supplier opened<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Down Icon:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Should display a pop-up modal of Edit Canvass on the specific supplier that will allow updating of Supplier and/or Supplier Details<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Eye Icon:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Should display a pop-up modal of Edit Canvass on the specific supplier that will allow updating of Supplier and/or Supplier Details<br></span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group -Verify Submission of Canvass Sheet</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Submission Features - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Save as Draft</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Canvassed all items added and suppliers are added<br>8. Is in Canvas Sheet page</td><td class="s3">1. Click Save as Draft</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should be enabled as default and display a confirmation modal <br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user saves:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should save all data inputted as well as items added on the Canvass Sheet and redirects user back to Canvass Sheet. Displaying draft saved with Date and Timestamps<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If  user cancels:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should close confirmation modal and redirects user back to Canvass Sheet<br><br>- Should allow Saving the Canvass Sheet as Draft if the other Items does not have a Supplier entered to them<br>         i. Should nominate a Temporary Canvass Sheet Number<br>            i) FORMAT: CS-TMP-[CANVASS NUMBER FORMAT]</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group - Verify Submission of Canvass Sheet</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Submission Features - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Cancel</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Canvassed all items added and suppliers are added<br>8. Is in Canvas Sheet page</td><td class="s3">1. Click Cancel</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should be enabled as default and display a confirmation modal <br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user cancels:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should close confirmation modal and redirects user back to Canvass Sheet<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user saves as draft:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:line-through;color:#000000;">- Should display confirmation modal<br>- Should save all data inputted as well as items added on the Canvass Sheet and redirects user back to Canvass Sheet. Displaying draft saved with Date and Timestamps<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user continues:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should erase all inputted data as well as added items on the Canvass Sheet and redirects user to dashboard deleting the Canvass Sheet.</span></td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3">Working but need to explore more</td></tr><tr style="height: 19px"><th id="1865166854R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s14"></td><td class="s16">Canvassing for Steelbars Item Group -Verify Submission of Canvass Sheet</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Submission Features - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Submit</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Entered Canvas through Select Actions<br>6. Added Steel Bars and Other Items in the Canvas Sheet<br>7. Canvassed all items added and suppliers are added<br>8. Is in Canvas Sheet page</td><td class="s3">1. Click Submit</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should be disable as default and should only be enabled after all items are canvass and added suppliers<br>- Should display confirmation modal<br>- Should display seperate confirmation if Canvassing is Incomplete<br><br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user submits:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should save all data inputted as well as items added on the Canvass Sheet and redirects user to dashboard.<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If user cancels:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should close confirmation modal and redirects user back to Canvass Sheet<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>Upon submission should trigger approving of the Canvass Sheet</span></td><td class="s3"></td><td class="s6">Passed</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order for RS with Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Purchase Order page for each Item</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">&quot;1. Find and Click RS Number with Steel Bars and Other Items<br>2. Click Related Documents Tab<br>3. Click Orders Tab<br>4. Find/Search PO Number<br>5. Click PO Number Text Link&quot;</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should display a New Page with the Purchase Order Details<br><br>Note:<br>- Should display the PO Number<br>- Should have Buttons <br>   i. Visit Main RS Button<br>       i) Should go back to RS Main Tab/Page<br>- Should display no Data if a Canvass has not been Approved yet to produce a Purchase Order</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-954">https://youtrack.stratpoint.com/issue/CITYLANDPRS-954</a></td></tr><tr style="height: 19px"><th id="1865166854R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Status of Purchase Order</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Status Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should Display Current status of Purchase Order</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Approvers</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Approvers Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should Display the following:<br><br>i) Supervisor of Assigned Purchasing Staff<br>ii) Purchasing Head</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Supplier Details</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Supplier Details Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should be uneditable and displays the following:<br><br>i) Supplier Name of the Supplier selected during Canvassing<br>ii) Delivery Address the selected Address in the RS for Deliver To<br>iii) Terms selected during Canvassing<br>iv) Deposit selected during Canvassing<br>v) Warranty selected during PO Review<br>     a) If without a Value, should display as &quot;---&quot;</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Attachments and Notes - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Attachment</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Attachment Button</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should be able to the following:<br><br>i) Should be able to view the List of Attachments<br>ii) Should have a Badge of New Attachment if a New Attachment has been attached<br>     a) Should only clear the Badge if the Check Attachment Button was opened<br>iii) Should be able search for the File Name</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Attachments and Notes - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Notes</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Notes Button</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should be able to the following:<br><br>i) Should be able to view the Notes<br>ii) Should have a Badge of New Notes if a New Note has been added<br>     a) Should only clear the Badge if the Check Notes Button was opened<br>iii) Should be able to Filter the Date of the Notes</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Attachments and Notes - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Uploading of Attachment</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Upload Attachment</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should be able to the following:<br><br>i) Should allow Maximum File Size of 25MB per File<br>ii) Should be able to upload File Types of PDF, Doc, JPG, JPEG, PNG, CSV, Excel<br>iii) Should click Submit Button to Confirm submission of Files</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Attachments and Notes - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Adding of Notes</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Add Notes</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should be able to the following:<br><br>i) Should Alphanumeric and Special Characters except Emojis<br>ii) Should allow maximum of 100 Characters<br>iii) Should click Submit Button to Confirm adding of Note</td><td class="s3"></td><td class="s13">Blocked</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">accepts emoji</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="1865166854R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Items </td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Items Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should be able to the following:<br><br>i) Should be able to Search for Item Name<br>ii) Display Table of Items for the same Suppliers<br>iii) Should display Tabs for Items and Steelbars. if Steelbars and Non-Steelbar Items were added in one Requisition Slip<br>iv) Should display 10 Rows per Page</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Need to try pagination</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Items - Items Column</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Click Items Tab</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should display the following Columns:<br><br>1) Item<br>2) Account Code<br>3) Weight<br>4) Remaining GFQ<br>5) Unit<br>6) Canvassed Price<br>7) History</td><td class="s3"></td><td class="s13">Blocked</td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s18"></td></tr><tr style="height: 19px"><th id="1865166854R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Items - Steel Bars Column</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Click Steel Bars Tab</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should display the following Columns:<br><br>1) Item<br>2) Account Code<br>3) Quantity<br>4) Remaining GFQ<br>5) Dia<br>6) Length<br>7) Weight(kg)<br>8) Qty</td><td class="s3"></td><td class="s13">Blocked</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Missing Columns as per Figma</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1044">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1044</a></td></tr><tr style="height: 19px"><th id="1865166854R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s14"></td><td class="s17"> Purchase Order for Steelbars - Verify Purchase Order Sections</td><td class="s3">Critical</td><td class="s3">Verify Purchase Order Total Amount</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Is in Dashboard Page</td><td class="s3">1. Verify Purchase Order Total Amount Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should display the following:<br><br>i) Sub-total - Added up Unit Price of the Items<br>ii) Discount - Added up of all subtracted Discounts per Item<br>iii) Total Amount - With the subtracted Discounted Amount Price</td><td class="s3"></td><td class="s13">Blocked</td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Issue encountered in Verna&#39;s Ticket</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1001">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1001</a></td></tr><tr style="height: 19px"><th id="1865166854R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Payment Request Page</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Find and Click RS Number with Steel Bars and Other Items<br>2. Click Select Actions<br>3. Click Create Payment Request</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Page with the Payment Request Form<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">&quot;Create Payment Request&quot; option should display under Select Actions Modal. If it does not show, make sure RS is Approved, Canvassed, Purchase Order has been made and is Fully Delivered or Fully Delivered w/ Return<br></span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Payment Request Page - Sections</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Sections of the Page</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should contain the following Sections:<br>    <br>a. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Purchase Order Details</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>       i. Purchase Order Number<br>            i) Drop-down Values of all Purchase Orders of the RS with a Status of Fully Delivered or Fully Delivered w/ Return<br>            ii) Should also retrieve the Invoive attached in the Delivery Receipt of the Purchase Order<br>       ii. Button for Viewing of Invoice<br>b. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Request Details</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>c. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Additional Fees</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>d. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Attachments and Notes</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>e. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items Table</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>      i. Should have a Search for Item Name<br>            i) Should have Tabs for Items and Steelbars<br>                  a) Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>                  b) Else display either the Table for other OFM Items or Table for Steelbars</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Purchase Order Details</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Purchase Order Details Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Purchase Order Number</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>           i) Drop-down Values of all Purchase Orders of the RS with a Status of Fully Delivered or Fully Delivered w/ Return<br>           ii) Once selected, should autofill the following:<br>               a) Supplier<br>               b) Delivery Address<br>               c) Terms<br>               d) Deposit %<br>               e) Warranty<br>            iii) Should also retrieve the Invoive attached in the Delivery Receipt of the Purchase Order<br>ii. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Button for Viewing of Invoice</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>             i) Should open a Modal containing of<br>               a) Invoice File that should open on a New Tab<br>               b) Invoice No<br>               c) Invoice Date<br>               d) Total Sales<br>               e) VAT Amount<br>               f) VAT Exempt Sales Amount<br>               g) Zero Rated Sales Amount</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R76" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">77</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Request Details</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Request Details Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Request Details<br>        i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Payable To</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>           i) Should be autoffilled once the PO Number was retrieved<br>        ii. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Charge To</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>           i) Declared Charge To of the Requisition Slip<br>        iii. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Payable Date</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>           i) Should be a Date Picker<br>           ii) Can be greater than from the Current Date<br>        iv. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Discount</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>            i) If Fixed Amount, this should be removed from the Current Total of the Purchase Order<br>            ii) If Percentage, should get the Certain percentage of the Total, to be subtracted to it</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R77" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">78</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Additional Fee</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Additional Fee Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Additional Fees<br>        i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Once clicked, this will display Fields to enter any additional charges and must be a Number</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>           i) Withholding Tax Reduction<br>           ii) Delivery Fee<br>           iii) Extra Charges<br>           iv) Tip</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R78" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">79</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Attachment and Notes</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Attachment and Notes Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Attachments and Notes<br>        i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Uploading of Attachment</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>           i)Should allow Maximum File Size of 25MB per File<br>           ii) Should be able to upload File Types of PDF, Doc, JPG, JPEG, PNG, CSV, Excel<br>          iii) Should click Submit Button to Confirm submission of Files<br>       ii. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Adding of Notes</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          i) Should Alphanumeric and Special Characters except Emojis<br>          ii) Should allow maximum of 100 Characters<br>          iii) Should click Submit Button to Confirm adding of Note</span></td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">accepts emoji</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="1865166854R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Items Table</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Items Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Items Table<br>        i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have a Search for Item Name</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        ii. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should display two tabs: Items and Steel Bars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        iii. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have Columns for Items Tab</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>            i) Item<br>               a) Item listed from the selected Purchase Order Number<br>            ii) Account Code<br>                a) Account Code of the Item<br>            iii) Delivered Qty<br>                 a) Should be retrieved from Delivery Receipt<br>            iv) Amount<br>                 a) Canvassed Price of the Item<br>                      1) Canvassed Price is the Price of the Item which the Discounts has been deducted from the Original Price of the Item<br>        iv. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have Columns for Steel Bars Tab</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>            i) Item<br>            ii) Account Code<br>            iii) Unit<br>            iv) Remaining GFQ<br>            v) Grade<br>            vi) Dia<br>            vii) Length<br>            viii) Weight(kg)<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>- Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>- Else display either the Table for other OFM Items or Table for Steelbars</span></td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Missing Columns in Steel Bars Tab</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1066">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1066</a></td></tr><tr style="height: 19px"><th id="1865166854R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Total</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Verify Total Section</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have these Validation Rules:<br><br>Total<br>        i. Should display the Sub-Total, Discounts, and Total Amount</td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">Issue encountered in Verna&#39;s Ticket</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1001">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1001</a></td></tr><tr style="height: 19px"><th id="1865166854R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Buttons: Cancel</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Click Cancel</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Cancel Button<br>        </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">i. If clicked, should display a Confirmation Modal</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          i) Once Confirmed, should cancel Payment Request</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Buttons: Save as Draft</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1. Fill in Fields<br>2. Click Save as Draft</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Save Draft Button<br>       </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> i. If clicked, should display a Confirmation Modal</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          i) Once Confirmed, should assign a Draft VR Number<br>             Format: <br>             </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">VR<br>             Company Code[2 Digit] + AA-ZZ + 8 Digits[00000000 - 99999999]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          ii) Should not be a pre-requisite to be able to Submit a Payment Request</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s3"></td><td class="s19">Payment Request for Steelbars - Verify Creation of Payment Request - RS with Steelbars</td><td class="s3">Normal</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Buttons: Submit</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Delivery of the items in the Purchase Order has been fully Delivered<br>8. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>9. Created Payment Request<br>10. Is in Payment Request Page</td><td class="s3">1.Fill in all required Fields<br>2. Click Submit</td><td class="s3">Assigned Purchasing Staff User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Submit Button<br>        i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If clicked, should display a Confirmation Modal</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          i) Once Confirmed, should submit the Payment Request For Approval<br>          ii) Should update the Status to For PR Approval</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3">Verify Redirection to Delivery Receipt Form</td><td class="s3">Verify Redirection to Delivery Receipt Form</td><td class="s3">1. Find and Click RS Number with Steel Bars and Other Items<br>2. Click Select Actions Button<br>3. Click Record Delivery Receipt</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">- Should display Delivery Receipt Form<br>- Should display blank DR Number and the RS Number<br><br>Note:<br><br>- Clicking RS Number should display RS Details<br>- Upon clicking Select Actions, Record Delivery Receipt should be among the choices</td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Drop-down of Purchase Order Number</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Drop-down of Purchase Order Number</span></td><td class="s3">1. Verify Drop-down Purchase Order Number</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have these Validation Rules:<br><br>Should display the Values of Purchase Orders Produced in the Requisition Slip<br>   i) Should check if the Purchase Order has an existing Delivery Receipt, may it be Draft or Submitted<br>         a) If with existing, should not display in the List of PO Numbers</td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Supplier</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Supplier</span></td><td class="s3">1. Verify Supplier</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should get the Supplier indicated in the Purchase Order</td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Invoice Button</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Invoice Button</span></td><td class="s3">1. Verify Invoice Button</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have Button for Invoice</td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Attachments and Notes</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Attachments and Notes</span></td><td class="s3">1. Verify Attachments and Notes</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Uploading of Attachment</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   i) Should allow Maximum File Size of 25MB per File<br>   ii) Should be able to upload File Types of PDF, Doc, JPG, JPEG, PNG, CSV, Excel<br>   iii)  Should click Submit Button to Confirm submission of Files<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Adding of Notes</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   i) Should Alphanumeric and Special Characters except Emojis<br>   ii) Should allow maximum of 100 Characters<br>   iii) Should click Submit Button to Confirm adding of Note</span></td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">accepts emoji</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="1865166854R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items</span></td><td class="s3">1. Verify Items</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have Search for Item Name</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   i. Should be triggered by Search Button<br>   ii. Should display matched Item even with Keyword only<br>         i) Should display No Data if there is no available Data matching the Searched Item<br>   iii. Should clear the Search Item and reset the Table once Clear Button is clicked<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have Tabs for Items and Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   i. Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>   ii. Else display either the Table for other OFM Items or Table for Steelbars<br>   iii. Should display 10 Rows per Page</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">should test pagination</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R90" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">91</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items - OFM Items</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items - OFM Items</span></td><td class="s3">1. Verify OFM Items Bar</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Columns for OFM Items<br>  a) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Item</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  b) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Quantity Ordered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  c) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Quantity Delivered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         1) Should display &quot;---&quot; if no Data has been indicated yet<br>  d) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Unit</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  e) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date Delivered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        1) Should display &quot;---&quot; if no Data has been indicated yet<br>  f) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Delivery Status</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>       1) Should display &quot;---&quot; if no Data has been indicated yet<br>              a1) ---<br>              a2) Partially Delivered<br>              a3) Partially Delivered w/ Return<br>              a4) Fully Delivered<br>              a5) Fullt Delivered w/ Return<br>  g) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Notes</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  h) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Actions</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         1) Edit</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R91" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">92</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items - Steel Bars</span></td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Form Sections - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Items - Steel Bars</span></td><td class="s3">1. Verify Steel Bars Tab</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Columns for Steelbars<br>  a) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Item</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  b) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Quantity Ordered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  c) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Quantity Delivered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         1) Should display &quot;---&quot; if no Data has been indicated yet<br>  d) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Unit</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  e) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date Delivered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         1) Should display &quot;---&quot; if no Data has been indicated yet<br>  f) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Delivery Status</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>        1) Should display &quot;---&quot; if no Data has been indicated yet<br>               a1) ---<br>               a2) Partially Delivered<br>               a3) Partially Delivered w/ Return<br>               a4) Fully Delivered<br>               a5) Fullt Delivered w/ Return<br>  g) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Notes</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  h) </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Actions</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         1) Edit</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R92" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">93</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Creation of Delivery Receipt</td><td class="s3">Critical</td><td class="s3">Verify Edit Item Function</td><td class="s3">Verify Edit Item Function</td><td class="s3">1. Click Edit Icon</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Item Details modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Item status is: Delivered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  i) Edit button and Close Window should be visible and enable and other fields and buttons are disabled<br>  ii) If Edit button is clicked, Date Delievered, Qty Delievered, Qty for Return, and Notes should be enabled and editable<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Item status is: Delivered W/ Return</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  i) Edit button, Close Window, and Cancel Return should be visible and other fields and buttons are disabled<br>  ii) If Edit button is clicked, Date Delievered, Qty Delievered, Qty for Return, and Notes should be enabled and editable</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R93" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">94</div></th><td class="s3"></td><td class="s20">Delivery Record for Steelbars-Verify Viewing of Delivery Receipt - Items ans Steel Bars Tab</td><td class="s3">Critical</td><td class="s3">Verify if there are seperate tabs for Items and Steelbars</td><td class="s3">Verify if there are seperate tabs for Items and Steelbars</td><td class="s3">1. Verify Delivery Receipt Form Items Tab</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have Tabs for Items and Steelbars<br>   a. Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>   b. Else display either the Table for other OFM Items or Table for Steelbars</td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1065">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1065</a></td></tr><tr style="height: 19px"><th id="1865166854R94" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">95</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Delivery Receipt Page</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Is in Dashboard Page</td><td class="s3">1. Find and Click RS Number with Steel Bars and Other Items<br>2. Click Select Actions<br>3. Click Record Delivery Receipt</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">- Should display Page with Delivery Receipt Form<br>- Should display blank DR Number and the RS Number on Delivery Receipt Form<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Note:<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">&quot;Record Delivery Receipt&quot; option should display under Select Actions Modal. If it does not show, make sure RS is Approved, Canvassed, Purchase Order has been made and is Fully Delivered</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R95" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">96</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Purchase Order Number</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Drop-down Purchase Order Number</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Drop-down of Purchase Order Number</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   i. Should display the Values of Purchase Orders Produced in the Requisition Slip<br>        i) Should check if the Purchase Order has an existing Delivery Receipt, may it be Draft or Submitted<br>              a) If with existing, should not display in the List of PO Numbers</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Supplier</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Supplier Section</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have these Validation Rules:<br><br>Supplier<br>   i. Should get the Supplier indicated in the Purchase Order</td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Invoice Button</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Invoice Button</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have Button for Invoice</td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Attachment and Notes</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Attachment and Notes Section</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Attachments and Notes<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">  </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> i. Uploading of Attachment</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>         i) Should allow Maximum File Size of 25MB per File<br>         ii) Should be able to upload File Types of PDF, Doc, JPG, JPEG, PNG, CSV, Excel<br>         iii)  Should click Submit Button to Confirm submission of Files<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">   ii. Adding of Notes<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">         i) Should Alphanumeric and Special Characters except Emojis<br>         ii) Should allow maximum of 100 Characters<br>         iii) Should click Submit Button to Confirm adding of Note</span></td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3">accepts emoji</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="1865166854R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Items Section</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Items Section</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have Search for Item Name<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   i. Should be triggered by Search Button<br>   ii. Should display matched Item even with Keyword only<br>         i) Should display No Data if there is no available Data matching the Searched Item<br>   iii. Should clear the Search Item and reset the Table once Clear Button is clicked<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should have Tabs for Items and Steelbars<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   i. Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>   ii. Else display either the Table for other OFM Items or Table for Steelbars<br>   iii) Should display 10 Rows per Page</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Items Tab</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Item Tab</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should display Columns for OFM Items<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">  a) Item<br>  b) Quantity Ordered<br>  c) Quantity Delivered<br>         1) Should display &quot;---&quot; if no Data has been indicated yet<br>  d) Unit<br>  e) Date Delivered<br>        1) Should display &quot;---&quot; if no Data has been indicated yet<br>  f) Delivery Status<br>        1) Should display &quot;---&quot; if no Data has been indicated yet<br>               a1) ---<br>               a2) Partially Delivered<br>               a3) Partially Delivered w/ Return<br>               a4) Fully Delivered<br>               a5) Fullt Delivered w/ Return<br>  g) Notes<br>  h) Actions<br>        1) Edit</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Payment Request Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Validation: Steel Bars Tab</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Verify Steel Bars Tab</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Should display Columns for Steelbars</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   a) Item<br>   b) Quantity Ordered<br>   c) Quantity Delivered<br>          1) Should display &quot;---&quot; if no Data has been indicated yet<br>   d) Unit<br>   e) Date Delivered<br>          1) Should display &quot;---&quot; if no Data has been indicated yet<br>   f) Delivery Status<br>         1) Should display &quot;---&quot; if no Data has been indicated yet<br>                a1) ---<br>                a2) Partially Delivered<br>                a3) Partially Delivered w/ Return<br>                a4) Fully Delivered<br>                a5) Fullt Delivered w/ Return<br>                 g) Notes<br>                 h)  Actions<br>                      1) Edit</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">Critical</td><td class="s3">Verify Edit Item Function</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Click Edit Icon</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should display Item Details modal<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Item status is: Delivered</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  i) Edit button and Close Window should be visible and enable and other fields and buttons are disabled<br>  ii) If Edit button is clicked, Date Delievered, Qty Delievered, Qty for Return, and Notes should be enabled and editable<br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If Item status is: Delivered W/ Return</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>  i) Edit button, Close Window, and Cancel Return should be visible and other fields and buttons are disabled<br>  ii) If Edit button is clicked, Date Delievered, Qty Delievered, Qty for Return, and Notes should be enabled and editable</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Buttons: Save as Draft</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1. Fill in Fields<br>2. Click Save as Draft</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Save Draft Button<br>       </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> i. If clicked, should display a Confirmation Modal</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          i) Once Confirmed, should assign a Draft VR Number<br>             Format: <br>             </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">VR<br>             Company Code[2 Digit] + AA-ZZ + 8 Digits[00000000 - 99999999]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          ii) Should not be a pre-requisite to be able to Submit a Delivery Receipt</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Record Delivery Receipt - RS with Steelbars</td><td class="s3">High</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Delivery Receipt Page - </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Buttons: Submit</span></td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account with Requested Requisition Slip<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7 Status of the Purchase Order is For Delivery<br>8. Created Delivery Receipt <br>9. Is in Delivery Receipt Page</td><td class="s3">1.Fill in all required Fields<br>2. Click Submit</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Should have these Validation Rules:<br><br>Submit Button<br>        i. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">If clicked, should display a Confirmation Modal</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>          i) Once Confirmed, should submit the Delivery Receipt Form</span></td><td class="s3"></td><td class="s3"></td><td class="s6">Passed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1865166854R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s3"></td><td class="s21">Delivery Receipt for Steelbars - Verify Viewing of Delivery Receipt - Items ans Steel Bars Tab</td><td class="s3">Critical</td><td class="s3">Verify if there are seperate tabs for Items and Steelbars</td><td class="s3">1. Should be in one of these test Environment:<br><br>- DEVELOP<br>- STAGING<br><br>2. User should have created User account<br>3. User already logged in successfully to their Account as Assigned Purchasing Staff User Type<br>4. Should have already created a Requisition Slip with Steel bars and other Items that is approved by approvers and have already assigned Purchasing staff<br>5. Should already Canvassed Item/s with Suppliers and already approved by approvers<br>6. Purchase Order has been made<br>7. Status of Purchase Order is For Delivery<br>8. Delivery Receipt should be created<br>9. Is in Delivery Receipt Form</td><td class="s3">1. Verify Delivery Receipt Form Items Tab</td><td class="s3">Assigned Requestor User Type:<br><br>[Insert Test Data here]</td><td class="s3">Should have Tabs for Items and Steelbars<br>   a. Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>   b. Else display either the Table for other OFM Items or Table for Steelbars</td><td class="s3"></td><td class="s3"></td><td class="s9">Failed</td><td class="s3"></td><td class="s7">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1065">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1065</a></td></tr></tbody></table></div>