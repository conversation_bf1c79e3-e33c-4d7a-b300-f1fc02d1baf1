const AppError = require('./appError');

/**
 * Error class for bad request errors
 */
class BadRequestError extends AppError {
  /**
   * Creates a new BadRequestError
   * 
   * @param {string} message - Error message
   * @param {Object} details - Additional error details
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(
    message = 'Bad request',
    details = {},
    originalError = null
  ) {
    super(message, 'BAD_REQUEST', details, originalError);
    this.details = details;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 400; // Bad Request
  }
}

module.exports = BadRequestError;
