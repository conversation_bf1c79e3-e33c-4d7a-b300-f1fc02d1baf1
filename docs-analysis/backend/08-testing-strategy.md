# Testing Strategy

## Overview

Testing is a critical aspect of software development that ensures the reliability and correctness of the application. This document provides an analysis of the current testing approach in the PRS-Backend and recommendations for improvement.

## Current Testing Approach

The PRS-Backend uses Mocha and Chai for testing, with some tests implemented using Sinon for mocking.

### Testing Framework

The testing framework consists of:

- **Mocha**: Test runner
- **Chai**: Assertion library
- **Sinon**: Mocking library
- **NYC**: Code coverage tool

### Test Structure

Tests are organized in a structure that mirrors the application structure:

```
test/
└── unit/
    └── src/
        ├── app/
        │   ├── handlers/
        │   │   └── controllers/
        │   │       └── UserController.spec.js
        │   └── infra/
        │       └── middleware/
        │           ├── accountVerification.spec.js
        │           └── authJwt.spec.js
        └── interfaces/
            └── routes.spec.js
```

### Test Examples

#### Controller Tests

```javascript
// From test/unit/src/app/handlers/controllers/UserController.spec.js
describe('Src :: App :: Handlers :: Controller :: UsersController', () => {
  const req = {};
  const reply = {};
  const usersObj = {
    id: 1,
    name: 'Name',
    description: 'Description',
    tweets: 'Tweets',
  };
  const message = 'Message Here..';
  const results = [usersObj];
  const data = {
    status: true,
    message,
    results,
  };
  const date = new Date();
  const expectedResponse = {
    timestamp: date,
    status: data.status,
    message: data.message,
    data: data.results,
  };
  const userRepository = {};
  const utils = {};
  let userController = new UserController({
    userRepository,
    utils,
    userErrors,
    InfraError,
  });
  
  describe('listAllUsers()', () => {
    context('When it has lists of users', () => {
      it('Should return users', async () => {
        const userCtrl = stub(userController, 'listAllUsers');
        const listAllUsers = userCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);
        expect(listAllUsers()).to.be.equal(expectedResponse);
        expect(listAllUsers()).to.be.an('object').include({ timestamp: date });
      });
    });
    
    // ... other tests
  });
  
  // ... other test suites
});
```

#### Middleware Tests

```javascript
// From test/unit/src/app/infra/middleware/accountVerification.spec.js
describe('Src :: App :: Infra :: Middleware :: AccountVerification', () => {
  beforeEach(() => {});
  afterEach(() => {
    restore();
  });

  describe('checkDuplicateUsernameOrEmail()', () => {
    context('When email is already duplicate', () => {
      it('Should return error', async () => {
        const email = 'duplicate email';
        const req = { email };
        const res = {};
        const expectedResponse = 'Failed email is already in use';
        const checkDuplicateEmail = stub(
          accountVerification,
          'checkDuplicateUsernameOrEmail',
        );

        try {
          checkDuplicateEmail.withArgs(req, res).returns(expectedResponse);
          expect.fail(expectedResponse);
        } catch (error) {
          expect(error.message).to.be.equal(expectedResponse);
        }
      });
    });
    
    // ... other tests
  });
});
```

#### Route Tests

```javascript
// From test/unit/src/interfaces/routes.spec.js
describe('Src :: Interfaces :: Routes', () => {
  beforeEach(() => {
    //token = jwt.sign({ id: 123 }, config.secret, { expiresIn: 86400 });
  });
  afterEach(() => {});

  describe('GET / ', async () => {
    context('/ endpoint', () => {
      it('Should response 200', async () => {
        const response = await fastify.inject({
          method: 'GET',
          url: '/',
        });
        expect(response.statusCode).to.be.equal(200);
      });
      it('Should return 404', async () => {
        const response = await fastify.inject({
          method: 'GET',
          url: '/not-found',
        });
        expect(response.statusCode).to.be.equal(404);
      });
    });
  });
});
```

### Test Configuration

The test configuration is defined in `package.json`:

```json
"scripts": {
  "test": "mocha",
  "format": "prettier --write \"src/**/*.js\"",
},
"nyc": {
  "reporter": [
    "text",
    "html"
  ],
  "extension": [
    ".js"
  ],
  "include": [
    "src/**/*.js"
  ],
  "exclude": [
    "test/**/*.js"
  ]
}
```

## Testing Issues

### 1. Limited Test Coverage

The current test coverage is limited, with many critical components lacking tests:

- Many services have no tests
- Many repositories have no tests
- Many business flows have no tests

### 2. Incomplete Test Cases

Many existing tests are incomplete:

```javascript
// From test/unit/src/app/handlers/controllers/UserController.spec.js
// Commented-out test case
// describe('loginAccount()', () => {
//   context('Accepted User', () => {
//     it('Should return a user and status 200', async () => {
//       const params = {param1: 'parameter value'};
//       const successMsg = 'Log in User Access';
//       expectedResponse.results = successMsg;
//       const response = {
//         message: successMsg,
//         status: 200,
//       };

//       const userCtrl = stub(userController, 'loginAccount');
//       const loginAccount = userCtrl.withArgs(params).returns(response);
//       expect(loginAccount(params)).to.be.equal(response);
//       expect(loginAccount(params)).to.be.an('object').include({message: successMsg});
//     });
```

### 3. Ineffective Mocking

Many tests use ineffective mocking techniques:

```javascript
// Ineffective mocking - testing the stub, not the implementation
const userCtrl = stub(userController, 'listAllUsers');
const listAllUsers = userCtrl
  .withArgs(req, reply)
  .returns(expectedResponse);
expect(listAllUsers()).to.be.equal(expectedResponse);
```

This approach tests the stub, not the actual implementation.

### 4. Missing Integration Tests

There are no integration tests that verify the interaction between components.

### 5. Missing End-to-End Tests

There are no end-to-end tests that verify the complete business flows.

### 6. Inconsistent Test Structure

The test structure is inconsistent, with some tests following different patterns.

## Recommended Testing Strategy

### 1. Unit Testing

Unit tests should verify the behavior of individual components in isolation.

#### Service Tests

```javascript
// Example service test
describe('RequisitionService', () => {
  let requisitionService;
  let mockRequisitionRepository;
  let mockUserRepository;
  let mockTransaction;
  
  beforeEach(() => {
    mockRequisitionRepository = {
      create: sinon.stub(),
      update: sinon.stub(),
      findById: sinon.stub(),
    };
    
    mockUserRepository = {
      findById: sinon.stub(),
    };
    
    mockTransaction = {
      commit: sinon.stub().resolves(),
      rollback: sinon.stub().resolves(),
    };
    
    const mockDb = {
      sequelize: {
        transaction: sinon.stub().resolves(mockTransaction),
      },
    };
    
    requisitionService = new RequisitionService({
      requisitionRepository: mockRequisitionRepository,
      userRepository: mockUserRepository,
      db: mockDb,
    });
  });
  
  describe('createRequisition', () => {
    it('should create a requisition with draft status when isDraft is true', async () => {
      // Arrange
      const requisitionData = {
        isDraft: true,
        type: 'ofm',
        departmentId: 1,
        projectId: 2,
        companyId: 3,
      };
      
      const createdRequisition = {
        id: 1,
        ...requisitionData,
        status: 'draft',
        rsLetter: 'AA',
        draftRsNumber: '00000001',
      };
      
      mockRequisitionRepository.create.resolves(createdRequisition);
      
      // Act
      const result = await requisitionService.createRequisition(requisitionData, mockTransaction);
      
      // Assert
      expect(mockRequisitionRepository.create).to.have.been.calledWith(
        sinon.match({
          ...requisitionData,
          status: 'draft',
          rsLetter: sinon.match.string,
          draftRsNumber: sinon.match.string,
        }),
        { transaction: mockTransaction }
      );
      
      expect(result).to.deep.equal(createdRequisition);
    });
    
    it('should create a requisition with submitted status when isDraft is false', async () => {
      // Arrange
      const requisitionData = {
        isDraft: false,
        type: 'ofm',
        departmentId: 1,
        projectId: 2,
        companyId: 3,
      };
      
      const createdRequisition = {
        id: 1,
        ...requisitionData,
        status: 'submitted',
        rsLetter: 'AA',
        rsNumber: '00000001',
      };
      
      mockRequisitionRepository.create.resolves(createdRequisition);
      
      // Act
      const result = await requisitionService.createRequisition(requisitionData, mockTransaction);
      
      // Assert
      expect(mockRequisitionRepository.create).to.have.been.calledWith(
        sinon.match({
          ...requisitionData,
          status: 'submitted',
          rsLetter: sinon.match.string,
          rsNumber: sinon.match.string,
        }),
        { transaction: mockTransaction }
      );
      
      expect(result).to.deep.equal(createdRequisition);
    });
    
    it('should handle errors and rollback transaction', async () => {
      // Arrange
      const requisitionData = {
        isDraft: true,
        type: 'ofm',
        departmentId: 1,
        projectId: 2,
        companyId: 3,
      };
      
      const error = new Error('Database error');
      mockRequisitionRepository.create.rejects(error);
      
      // Act & Assert
      await expect(requisitionService.createRequisition(requisitionData, mockTransaction))
        .to.be.rejectedWith(error);
      
      expect(mockTransaction.rollback).to.have.been.called;
    });
  });
  
  // ... other test suites
});
```

#### Repository Tests

```javascript
// Example repository test
describe('RequisitionRepository', () => {
  let requisitionRepository;
  let mockDb;
  let mockRequisitionModel;
  
  beforeEach(() => {
    mockRequisitionModel = {
      findByPk: sinon.stub(),
      findAll: sinon.stub(),
      create: sinon.stub(),
      update: sinon.stub(),
    };
    
    mockDb = {
      requisitionModel: mockRequisitionModel,
    };
    
    requisitionRepository = new RequisitionRepository({
      db: mockDb,
    });
  });
  
  describe('findById', () => {
    it('should return requisition when found', async () => {
      // Arrange
      const requisitionId = 1;
      const requisition = {
        id: requisitionId,
        type: 'ofm',
        status: 'draft',
      };
      
      mockRequisitionModel.findByPk.resolves(requisition);
      
      // Act
      const result = await requisitionRepository.findById(requisitionId);
      
      // Assert
      expect(mockRequisitionModel.findByPk).to.have.been.calledWith(requisitionId);
      expect(result).to.deep.equal(requisition);
    });
    
    it('should throw NOT_FOUND error when requisition not found', async () => {
      // Arrange
      const requisitionId = 1;
      mockRequisitionModel.findByPk.resolves(null);
      
      // Act & Assert
      await expect(requisitionRepository.findById(requisitionId))
        .to.be.rejectedWith('Record not found');
    });
  });
  
  // ... other test suites
});
```

#### Controller Tests

```javascript
// Example controller test
describe('RequisitionController', () => {
  let requisitionController;
  let mockRequisitionService;
  let mockClientErrors;
  
  beforeEach(() => {
    mockRequisitionService = {
      createRequisition: sinon.stub(),
      updateRequisition: sinon.stub(),
      getRequisitionById: sinon.stub(),
    };
    
    mockClientErrors = {
      BAD_REQUEST: sinon.stub().returns(new Error('Bad Request')),
      NOT_FOUND: sinon.stub().returns(new Error('Not Found')),
    };
    
    requisitionController = new RequisitionController({
      requisitionService: mockRequisitionService,
      clientErrors: mockClientErrors,
    });
  });
  
  describe('createRequisition', () => {
    it('should create requisition and return 201 status', async () => {
      // Arrange
      const request = {
        body: {
          isDraft: true,
          type: 'ofm',
          departmentId: 1,
          projectId: 2,
          companyId: 3,
        },
        userFromToken: {
          id: 1,
        },
      };
      
      const reply = {
        status: sinon.stub().returnsThis(),
        send: sinon.stub(),
      };
      
      const createdRequisition = {
        id: 1,
        ...request.body,
        status: 'draft',
      };
      
      mockRequisitionService.createRequisition.resolves(createdRequisition);
      
      // Act
      await requisitionController.createRequisition(request, reply);
      
      // Assert
      expect(mockRequisitionService.createRequisition).to.have.been.calledWith(
        request.body,
        sinon.match.object // transaction
      );
      
      expect(reply.status).to.have.been.calledWith(201);
      expect(reply.send).to.have.been.calledWith({
        message: 'Successfully saved draft Requisition Slip',
        data: createdRequisition,
      });
    });
    
    it('should handle errors and return 400 status', async () => {
      // Arrange
      const request = {
        body: {
          isDraft: true,
          type: 'ofm',
          departmentId: 1,
          projectId: 2,
          companyId: 3,
        },
        userFromToken: {
          id: 1,
        },
      };
      
      const reply = {
        status: sinon.stub().returnsThis(),
        send: sinon.stub(),
      };
      
      const error = new Error('Service error');
      mockRequisitionService.createRequisition.rejects(error);
      
      // Act
      await requisitionController.createRequisition(request, reply);
      
      // Assert
      expect(mockClientErrors.BAD_REQUEST).to.have.been.calledWith({
        message: 'Requisition creation failed',
      });
      
      expect(reply.status).to.have.been.calledWith(400);
      expect(reply.send).to.have.been.called;
    });
  });
  
  // ... other test suites
});
```

### 2. Integration Testing

Integration tests should verify the interaction between components.

```javascript
// Example integration test
describe('Requisition Flow Integration', () => {
  let app;
  let db;
  let authToken;
  
  before(async () => {
    // Set up test database
    db = await setupTestDatabase();
    
    // Set up application
    app = await setupApplication(db);
    
    // Get authentication token
    authToken = await getAuthToken(app);
  });
  
  after(async () => {
    // Clean up test database
    await cleanupTestDatabase(db);
  });
  
  describe('Requisition Creation and Approval', () => {
    it('should create, submit, and approve a requisition', async () => {
      // Create requisition
      const createResponse = await request(app)
        .post('/v1/requisitions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          isDraft: true,
          type: 'ofm',
          departmentId: 1,
          projectId: 2,
          companyId: 3,
        });
      
      expect(createResponse.status).to.equal(201);
      const requisitionId = createResponse.body.data.id;
      
      // Submit requisition
      const submitResponse = await request(app)
        .post('/v1/requisitions/submit')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          id: requisitionId,
          isDraft: false,
          type: 'ofm',
          departmentId: 1,
          projectId: 2,
          companyId: 3,
        });
      
      expect(submitResponse.status).to.equal(200);
      
      // Approve requisition
      const approveResponse = await request(app)
        .post(`/v1/requisitions/${requisitionId}/approve`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({});
      
      expect(approveResponse.status).to.equal(200);
      
      // Verify requisition status
      const getResponse = await request(app)
        .get(`/v1/requisitions/${requisitionId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(getResponse.status).to.equal(200);
      expect(getResponse.body.data.status).to.equal('approved');
    });
  });
  
  // ... other test suites
});
```

### 3. End-to-End Testing

End-to-end tests should verify complete business flows.

```javascript
// Example end-to-end test
describe('Requisition to Payment End-to-End', () => {
  let app;
  let db;
  let authToken;
  
  before(async () => {
    // Set up test database
    db = await setupTestDatabase();
    
    // Set up application
    app = await setupApplication(db);
    
    // Get authentication token
    authToken = await getAuthToken(app);
  });
  
  after(async () => {
    // Clean up test database
    await cleanupTestDatabase(db);
  });
  
  it('should process a requisition from creation to payment', async () => {
    // Step 1: Create requisition
    const requisitionId = await createRequisition(app, authToken);
    
    // Step 2: Submit requisition
    await submitRequisition(app, authToken, requisitionId);
    
    // Step 3: Approve requisition
    await approveRequisition(app, authToken, requisitionId);
    
    // Step 4: Assign requisition
    await assignRequisition(app, authToken, requisitionId);
    
    // Step 5: Create canvass
    const canvassId = await createCanvass(app, authToken, requisitionId);
    
    // Step 6: Submit canvass
    await submitCanvass(app, authToken, canvassId);
    
    // Step 7: Approve canvass
    await approveCanvass(app, authToken, canvassId);
    
    // Step 8: Get purchase order
    const purchaseOrderId = await getPurchaseOrder(app, authToken, requisitionId);
    
    // Step 9: Submit purchase order
    await submitPurchaseOrder(app, authToken, purchaseOrderId);
    
    // Step 10: Approve purchase order
    await approvePurchaseOrder(app, authToken, purchaseOrderId);
    
    // Step 11: Create delivery receipt
    const deliveryReceiptId = await createDeliveryReceipt(app, authToken, requisitionId, purchaseOrderId);
    
    // Step 12: Create payment request
    const paymentRequestId = await createPaymentRequest(app, authToken, requisitionId, purchaseOrderId);
    
    // Step 13: Submit payment request
    await submitPaymentRequest(app, authToken, paymentRequestId);
    
    // Step 14: Approve payment request
    await approvePaymentRequest(app, authToken, paymentRequestId);
    
    // Step 15: Verify requisition is closed
    const requisition = await getRequisition(app, authToken, requisitionId);
    expect(requisition.status).to.equal('closed');
  });
});
```

### 4. Test Coverage

Implement comprehensive test coverage:

1. **Unit Tests**: Cover all services, repositories, and controllers
2. **Integration Tests**: Cover all API endpoints and key interactions
3. **End-to-End Tests**: Cover all business flows

### 5. Test Automation

Implement test automation:

1. **CI/CD Integration**: Run tests on every commit
2. **Coverage Reports**: Generate and publish coverage reports
3. **Test Badges**: Display test status in README

## Implementation Plan

### 1. Set Up Testing Framework

```javascript
// Install dependencies
npm install --save-dev mocha chai sinon sinon-chai nyc supertest

// Configure test scripts in package.json
"scripts": {
  "test": "mocha 'test/**/*.spec.js'",
  "test:unit": "mocha 'test/unit/**/*.spec.js'",
  "test:integration": "mocha 'test/integration/**/*.spec.js'",
  "test:e2e": "mocha 'test/e2e/**/*.spec.js'",
  "test:coverage": "nyc npm test",
  "test:watch": "mocha --watch 'test/**/*.spec.js'"
}
```

### 2. Create Test Helpers

```javascript
// test/helpers/setup.js
const { createContainer } = require('awilix');
const sinon = require('sinon');

function createTestContainer() {
  const container = createContainer();
  
  // Register mocks
  container.register({
    db: asValue({
      sequelize: {
        transaction: sinon.stub().resolves({
          commit: sinon.stub().resolves(),
          rollback: sinon.stub().resolves(),
        }),
      },
    }),
    logger: asValue({
      info: sinon.stub(),
      error: sinon.stub(),
      warn: sinon.stub(),
    }),
    // ... other mocks
  });
  
  return container;
}

module.exports = {
  createTestContainer,
};
```

### 3. Implement Unit Tests

Start with critical components:

1. **Services**: Focus on business logic
2. **Repositories**: Focus on data access
3. **Controllers**: Focus on request handling

### 4. Implement Integration Tests

Focus on API endpoints:

1. **Authentication**: Login, token refresh, etc.
2. **Requisition**: Create, update, approve, etc.
3. **Canvass**: Create, submit, approve, etc.
4. **Purchase Order**: Create, submit, approve, etc.
5. **Delivery Receipt**: Create, update, etc.
6. **Payment Request**: Create, submit, approve, etc.

### 5. Implement End-to-End Tests

Focus on business flows:

1. **Requisition to Payment**: Complete flow
2. **Requisition to Rejection**: Rejection flow
3. **Requisition to Cancellation**: Cancellation flow

### 6. Set Up CI/CD Integration

Configure CI/CD to run tests on every commit:

```yaml
# .gitlab-ci.yml
test:unit:
  stage: test
  script:
    - npm install
    - npm run test:unit
  artifacts:
    paths:
      - coverage/

test:integration:
  stage: test
  script:
    - npm install
    - npm run test:integration
  artifacts:
    paths:
      - coverage/

test:e2e:
  stage: test
  script:
    - npm install
    - npm run test:e2e
  artifacts:
    paths:
      - coverage/
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
