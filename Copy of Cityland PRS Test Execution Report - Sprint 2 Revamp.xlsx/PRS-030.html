<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="809848333C0" style="width:103px;" class="column-headers-background">A</th><th id="809848333C1" style="width:167px;" class="column-headers-background">B</th><th id="809848333C2" style="width:252px;" class="column-headers-background">C</th><th id="809848333C3" style="width:84px;" class="column-headers-background">D</th><th id="809848333C4" style="width:233px;" class="column-headers-background">E</th><th id="809848333C5" style="width:330px;" class="column-headers-background">F</th><th id="809848333C6" style="width:329px;" class="column-headers-background">G</th><th id="809848333C7" style="width:511px;" class="column-headers-background">H</th><th id="809848333C8" style="width:63px;" class="column-headers-background">I</th><th id="809848333C9" style="width:114px;" class="column-headers-background">J</th><th id="809848333C10" style="width:124px;" class="column-headers-background">K</th><th id="809848333C11" style="width:124px;" class="column-headers-background">L</th><th id="809848333C12" style="width:124px;" class="column-headers-background">M</th><th id="809848333C13" style="width:124px;" class="column-headers-background">N</th><th id="809848333C14" style="width:78px;" class="column-headers-background">O</th><th id="809848333C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="809848333R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(E2E_Run1)</td><td class="s1">Actual Results<br>(E2E_Run1)</td><td class="s1">Status<br>(E2E_Run2)</td><td class="s1">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="809848333R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="16">PRS-030 - [Remaining AC] Selection of Date in Date Picker in RS Creation, Updated Attachment Filtering in Supplier Management, Download in Dashboard, Enable Filtering in Dashboard Page, Submitting and Saving of Draft for Created RS, Enable Filtering in Dashboard Page</td></tr><tr style="height: 19px"><th id="809848333R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3"></td><td class="s4">Updated Attachment Filtering in Supplier Management</td><td class="s3">Verify Filtering of Files displayed</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin <br>3. User already navigated on Supplier page</a></td><td class="s3">1. On, Supplier Page,  click the &quot;Supplier Name&quot; text Link<br>2. Click &quot;Check Attachments&quot; Button<br>3. Check the filtering of files display</td><td class="s3"></td><td class="s3">1. Should display the Supplier Details View page<br>2. Should displayed the Attachments view modal<br>3. Should update the Filtering of Files, by searching of File Name with &quot;Search&quot; and &quot;Clear&quot; button</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="809848333R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3"></td><td class="s3">Updated Attachment Filtering in Supplier Management</td><td class="s3">Verify Search when filtering of file names by keyword is working as expected</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin <br>3. User already navigated on Supplier page</a></td><td class="s3">1. On, Supplier Page,  click the &quot;Supplier Name&quot; text Link<br>2. Click &quot;Check Attachments&quot; Button<br>3. Populate a keyword of file name that is existing on the list<br>4. Click &quot;Search&quot; button<br>5. Click &quot;Clear&quot; button</td><td class="s3"></td><td class="s3">1. Should display the Supplier Details View page<br>2. Should displayed the Attachments view modal<br>3. Keyword should be populated<br>4. Should be able to displayed all files that are related to the keyword entered<br>5. Should reset the list and displayed all files</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="809848333R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s3"></td><td class="s3">Updated Attachment Filtering in Supplier Management</td><td class="s3">Verify Search when filtering of file names by exact file name is working as expected</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin <br>3. User already navigated on Supplier page</a></td><td class="s3">1. On, Supplier Page,  click the &quot;Supplier Name&quot; text Link<br>2. Click &quot;Check Attachments&quot; Button<br>3. Populate the exact file name that is existing on the list<br>4. Click &quot;Search&quot; button<br>5. Click &quot;Clear&quot; button</td><td class="s3"></td><td class="s3">1. Should display the Supplier Details View page<br>2. Should displayed the Attachments view modal<br>3. Exact file name should be populated<br>4. Should be able to displayed all files that are related to the file name entered<br>5. Should reset the list and displayed all files</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="809848333R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3"></td><td class="s3">Updated Attachment Filtering in Supplier Management</td><td class="s3">Verify filtering of file name that are not existing</td><td class="s3">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin <br>3. User already navigated on Supplier page</a></td><td class="s3">1. On, Supplier Page,  click the &quot;Supplier Name&quot; text Link<br>2. Click &quot;Check Attachments&quot; Button<br>3. Populate a keyword or  exact file name that is not existing on the list<br>4. Click &quot;Search&quot; button<br>5. Click &quot;Clear&quot; button</td><td class="s3"></td><td class="s3">1. Should display the Supplier Details View page<br>2. Should displayed the Attachments view modal<br>3. keyword or Exact file name should be populated<br>4. Should display a no data or blank in the modal<br>5. Should reset the list and displayed all files</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="809848333R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s10"></td><td class="s10">Download in Dashboard</td><td class="s10">Verify Download button in RS Dashboard All tab is working as expected</td><td class="s10">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s10">1. On RS Dahboard All Tab, click &quot;Download&quot; button<br>2. Check File name of downloaded data</td><td class="s10"></td><td class="s12"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1AuWDT6poouY0QpJDIBLoX2qUGL9qL60zrtLwS10lOPA/edit?gid=0#gid=0">1. Should download an Excel File of the Data or list displayed in All Tab dashboard <br>2.File Name format should be:<br>           [TAB NAME]_[DATE OF EXTRACT(YYYYMMDD)]<br>           Sample: All_20250131<br><br>FORMAT:<br>Download Template - Download RS Tab<br>Download Template</a></td><td class="s13"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s10"></td><td class="s10">Download in Dashboard</td><td class="s10">Verify Download button in RS Dashboard For My Approval/Assigned To Me Tab is working as expected</td><td class="s10">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s10">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2. Click &quot;Download&quot; button<br>3. Check File name of downloaded data</td><td class="s10"></td><td class="s12"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1AuWDT6poouY0QpJDIBLoX2qUGL9qL60zrtLwS10lOPA/edit?gid=0#gid=0">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Should download an Excel File of the Data or list displayed in For My Approval/Assigned To Me Tab dashboard <br>3.File Name format should be:<br>           [TAB NAME]_[DATE OF EXTRACT(YYYYMMDD)]<br>           Sample: All_20250131<br><br>FORMAT:<br>Download Template - Download RS Tab<br>Download Template</a></td><td class="s13"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s10"></td><td class="s10">Download in Dashboard</td><td class="s10">Verify Download button in RS Dashboard For My Requests Tab is working as expected<br></td><td class="s10">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s10">1. On RS Dahboard, click For My Requests Tab <br>2. Click &quot;Download&quot; button<br>3. Check File name of downloaded data</td><td class="s10"></td><td class="s12"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1AuWDT6poouY0QpJDIBLoX2qUGL9qL60zrtLwS10lOPA/edit?gid=0#gid=0">1. Should displayed all the User&#39;s Requested RS <br>2. Should download an Excel File of the Data or list displayed in For My Requests Tab dashboard <br>3.File Name format should be:<br>           [TAB NAME]_[DATE OF EXTRACT(YYYYMMDD)]<br>           Sample: All_20250131<br><br>FORMAT:<br>Download Template - Download RS Tab<br>Download Template</a></td><td class="s13"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s10"></td><td class="s10">Download in Dashboard</td><td class="s10">Verify Copy and Print Buttons are removed is All Tabs in RS Dashboard</td><td class="s10">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s10">1. On RS Dahboard, check &quot;Copy&quot; and &quot;Print&quot; buttons in all Tabs<br>         a. All Tab<br>         b. For My Approval/Assigned To Me Tab<br>         c. For My Requests Tab </td><td class="s10"></td><td class="s10">1. Should removed Copy and Print Buttons in all dashboard Tabs<br>         a. All Tab<br>         b. For My Approval/Assigned To Me Tab<br>         c. For My Requests Tab <br></td><td class="s13"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s3"></td><td class="s4">Enable Searching in Dashboard Page</td><td class="s3">Verify search for RS number in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate RS Number that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate RS Number that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched RS Number<br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Canvass number in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Canvass Number that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Canvass Number that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Canvass Number<br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 113px"><th id="809848333R12" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">13</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for PO number in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate PO Number that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate PO Number that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched PO Number<br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for DR number in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate DR Number that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate DR Number that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched DR Number<br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">Doesn&#39;t display RS even if data exist</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1098">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1098</a></td></tr><tr style="height: 19px"><th id="809848333R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for PR number in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate PR Number that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate PR Number that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched PR Number<br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">Doesn&#39;t display RS even if data exist</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1099">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1099</a></td></tr><tr style="height: 19px"><th id="809848333R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Type in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Type that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Type that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Type <br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">Doesn&#39;t display RS even if data exist</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1100">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1100</a></td></tr><tr style="height: 19px"><th id="809848333R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Requester in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Requester that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Requester that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Requester <br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Company in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Company that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Company that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Company <br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Project/Department in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Project/Department that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Project/Department that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Project/Department<br>6. Should reset the Search Field and Dashboard Table </td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Date Requested in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Date Requested that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Date Requested that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Date Requested <br>6. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s7"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3" rowspan="2">Search Tab cannot search for dates</td><td class="s15" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1097">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1097</a></td></tr><tr style="height: 19px"><th id="809848333R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Last Updated in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Last Updated that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Last Updated that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3"></td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Last Updated <br>6. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Status in All tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, Populate Status  that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Status  that is existing on the table in search field<br>5. Click &quot;Search&quot; button<br>6. Click Clear button</td><td class="s3">Related to PRS-253<br><br>1. Should be able to search for the Requests in the Dashboard Page and related Documents per Request<br>2. Should be able to Search per Tab of Data<br>3. Should be able to Search for<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>4. Should display the matching Data on the Table<br>5. Should display a No Data if the Data entered has no matched Item<br>6. Should have a Clear Button that will reset the Table Data</td><td class="s3">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Dashboard Table <br>4. Field should be populated<br>5. Should displayed all matched Status <br>6. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s16">Blocked</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for RS Number in For My Approval/Assigned tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate RS number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate RS number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched RS number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Canvass Number in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Canvass Number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Canvass Number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Canvass Number <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for PO Number in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate PO Number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate PO Number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched PO Number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for DR Number in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate DR Number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate DR Number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched DR Number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3" rowspan="3">Same issue with All Tab</td><td class="s3">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1098</td></tr><tr style="height: 19px"><th id="809848333R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for PR Number in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate PR Number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate PR Number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched PR Number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1099</td></tr><tr style="height: 19px"><th id="809848333R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Type in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Type that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Type that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Type <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1100</td></tr><tr style="height: 19px"><th id="809848333R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Requester in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Requester that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Requester that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Requester <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Company in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Company that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Company that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Company <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Project/Department in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Project/Department that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Project/Department that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Project/Department<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Date Requested in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Date Requested that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Date Requested that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Date Requested <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3" rowspan="2">Search Tab cannot search for dates</td><td class="s15" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1097">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1097</a></td></tr><tr style="height: 19px"><th id="809848333R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Last Updated in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2.  Populate Last Updated that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Last Updated that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Last Updated <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Status in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Approval/Assigned To Me Tab<br>2. Populate Status  that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Status  that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Status <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for RS number in For My Requests tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate RS number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate RS number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched RS number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Canvass number in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Canvass number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Canvass number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Canvass number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for PO number in For My Requests  tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate PO number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate PO number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched PO number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for DR number in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate DR number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate DR number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched DR number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3" rowspan="3">Same issue with All Tab</td><td class="s3">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1098</td></tr><tr style="height: 19px"><th id="809848333R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for PR number in For My Requests  tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate PR number that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate PR number that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched PR number<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1099</td></tr><tr style="height: 19px"><th id="809848333R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Type in For My Requests  tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Type that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Type that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Type <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1100</td></tr><tr style="height: 19px"><th id="809848333R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Requester in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Requester that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Requester that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Requester <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Company in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Company that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Company that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Company <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Project/Department in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate  Project/Department that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate  Project/Department that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched  Project/Department<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Date Requested in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Date Requested that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Date Requested that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Date Requested <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3" rowspan="2">Search Tab cannot search for dates</td><td class="s15" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1097">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1097</a></td></tr><tr style="height: 19px"><th id="809848333R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Last Updated in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Last Updated that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Last Updated that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Last Updated<br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s14">Failed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s3"></td><td class="s3">Enable Searching in Dashboard Page</td><td class="s3">Verify search for Status in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dahboard, click For My Requests Tab <br>2. Populate Status that is not existing on the table in search field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate Status that is existing on the table in search field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Search Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Status <br>7. Should reset the Search Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s10"></td><td class="s10">Selection of Date in Date Picker in RS Creation</td><td class="s10">Validate date picker in &quot;Date Requiired&quot; field RS Creation</td><td class="s10">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user (Requestor)</a></td><td class="s10">1. On RS Dashboard, click &quot;New Request&quot;<br>2. Populate all fields except &quot;Date Required&quot; field<br>3. Click date picker in &quot;Date Required&quot; field<br>4. Select date that are greater than the current Date</td><td class="s10"></td><td class="s10">1. Should displayed the RS Create form<br>2. All fields should be populated<br>3. A date picker should displayed<br>4. Should be able to select future dates</td><td class="s10"></td><td class="s10"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s10"></td><td class="s10">Selection of Date in Date Picker in RS Creation</td><td class="s10">Validate date picker in &quot;Date Requiired&quot; field RS Creation when select less than the current date</td><td class="s10">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user (Requestor)</a></td><td class="s10">1. On RS Dashboard, click &quot;New Request&quot;<br>2. Populate all fields except &quot;Date Required&quot; field<br>3. Click date picker in &quot;Date Required&quot; field<br>4. Select date that are less than the current Date</td><td class="s10"></td><td class="s10">1. Should displayed the RS Create form<br>2. All fields should be populated<br>3. A date picker should displayed<br>4. Past dates should be disabled and not able to select any past dates</td><td class="s10"></td><td class="s10"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s3"></td><td class="s3">Selection of Date in Date Picker in RS Creation</td><td class="s3">Validate manually enter date in &quot;Date Required&quot; Field</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user (Requestor)</a></td><td class="s3">1. On RS Dashboard, click &quot;New Request&quot;<br>2. Populate all fields except &quot;Date Required&quot; field<br>3. Manually populate date that are greater than the current Date in &quot;Date Required&quot; field<br>4. Click &quot;Save Draft&quot;</td><td class="s3"></td><td class="s3">1. Should displayed the RS Create form<br>2. All fields should be populated<br>3. Should be able to populate future dates <br>4. Should be able to Save draft with Temporary RS number</td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s3"></td><td class="s3">Selection of Date in Date Picker in RS Creation</td><td class="s3">Validate manually enter date in &quot;Date Required&quot;&quot; Field with less than the current date</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user (Requestor)</a></td><td class="s3">1. On RS Dashboard, click &quot;New Request&quot;<br>2. Populate all fields except &quot;Date Required&quot; field<br>3. Manually populate date that are less than the current Date in &quot;Date Required&quot; field<br>4. Click &quot;Save Draft&quot;</td><td class="s3"></td><td class="s3">1. Should displayed the RS Create form<br>2. All fields should be populated<br>3. Should be able to populate past dates <br>4. Should not be able to Save draft and an error message displayed for &quot;Date Required&quot; field</td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s3"></td><td class="s3">Selection of Date in Date Picker in RS Creation</td><td class="s3">Validate creation of RS when not entered any date in  &quot;Date Requiired&quot; field </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user (Requestor)</a></td><td class="s3">1. On RS Dashboard, click &quot;New Request&quot;<br>2. Populate all fields except &quot;Date Required&quot; field<br>3. Click &quot;Save Draft&quot;</td><td class="s3"></td><td class="s3">1. Should displayed the RS Create form<br>2. All fields should be populated except &quot;Date Required&quot; field<br>3. Should not be able to Save draft and an error message displayed for &quot;Date Required&quot; field</td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s10"></td><td class="s10">Submitting and Saving of Draft for Created RS</td><td class="s10">Verirfy Submit button when creating RS</td><td class="s10">Critical</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type as Requester</a></td><td class="s10">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Any Type of Request in dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Select Items &gt; click add items<br>6. Populate Quantity and Notes Field in the item list<br>7. Click &quot;Submit&quot; button<br>8. Check RS Created with submitted status</td><td class="s10"></td><td class="s10">1. Should display RS Creation Form<br>2. Should be able to select any Type of Request<br>3. Should be able to populate all fields<br>4. Should display a Modal for adding of Items<br>5. Should be able to add items in the item list<br>6. Quantity and Notes should be populated<br>7. Should be able to submit RS<br>8. RS Created should be the ff:<br>           a. Requisition Slip can be edited by the Approver<br>           b.  Requisition Slip is For Approval<br>           c.  Requisition Slip should have an RS Number<br>                i. Format - RS-[AA-ZZ]-[00000000]</td><td class="s10"></td><td class="s10"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s10"></td><td class="s10">Submitting and Saving of Draft for Created RS</td><td class="s10">Verirfy Save Draft button when creating RS</td><td class="s10">Critical</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type as Requester</a></td><td class="s10">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Any Type of Request in dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Select Items &gt; click add items<br>6. Populate Quantity and Notes Field in the item list<br>7. Click &quot;Save Draft&quot; button<br>8. Check RS Created with Draft status</td><td class="s10"></td><td class="s10">1. Should display RS Creation Form<br>2. Should be able to select any Type of Request<br>3. Should be able to populate all fields<br>4. Should display a Modal for adding of Items<br>5. Should be able to add items in the item list<br>6. Quantity and Notes should be populated<br>7. Should be able to create Draft RS<br>8. Should be able to edit the RS </td><td class="s10"></td><td class="s10"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s10"></td><td class="s10">Submitting and Saving of Draft for Created RS</td><td class="s10">Verirfy Cancel button when creating RS</td><td class="s10">Critical</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type as Requester</a></td><td class="s10">1. On RS dashboard, click &quot;New Request&quot; <br>2. Cliick Any Type of Request in dropdown field<br>3. Populate all fields<br>4.. Click &quot;Add Items&quot;  button in Items Section<br>5.  Select Items &gt; click add items<br>6. Populate Quantity and Notes Field in the item list<br>7. Click &quot;Cancel&quot; button<br></td><td class="s10"></td><td class="s10">1. Should display RS Creation Form<br>2. Should be able to select any Type of Request<br>3. Should be able to populate all fields<br>4. Should display a Modal for adding of Items<br>5. Should be able to add items in the item list<br>6. Quantity and Notes should be populated<br>7. Should be able to cancel the creation of Requisition Slip <br></td><td class="s10"></td><td class="s10"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="809848333R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s3"></td><td class="s4">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filter fields displayed</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click filter Icon<br>2. Check Filters per tab of the Dashboard</td><td class="s3"></td><td class="s3">1.Should be able to displayed a dropdown filter fields for the following:<br>    a. RS Number<br>    b. PR Number<br>    c. Type<br>    d. Requester<br>    e. Company<br>    f. Project/Department<br>    g. Date Requested<br>    h. Last Updated<br>    i. Status<br>2. Should be able to Filter per Tab of the Dashboard </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3">Need to Update TC</td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Type in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2. Populate Type that is not existing on the table in filter field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate/select Type  that is existing on the table in filter field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Filter Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Type <br>7. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Requester in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2. Populate Requester that is not existing on the table in filter field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate/select Requester that is existing on the table in filter field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Filter Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Requester <br>7. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Company in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2. Populate Company  that is not existing on the table in filter field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate/select Company that is existing on the table in filter field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Filter Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Company <br>7. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Department in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2. Populate Department  that is not existing on the table in filter field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate/select Department that is existing on the table in filter field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Filter Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Department <br>7. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Project in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2. Populate Project that is not existing on the table in filter field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate/select Project that is existing on the table in filter field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Filter Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Project <br>7. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Status in All tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2. Populate Status that is not existing on the table in filter field<br>3. Click &quot;Search&quot; button<br>4. Click Clear button<br>5. Populate/select Status that is existing on the table in filter field<br>6. Click &quot;Search&quot; button<br>7. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should displayed No Data in the table<br>4..Should reset the Filter Field and Dashboard Table <br>5. Field should be populated<br>6. Should displayed all matched Status <br>7. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Type in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned To Me Tab<br>2. Click filter Icon<br>3. Populate Type that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Type that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Type <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Requester in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned To Me Tab<br>2. Click filter Icon<br>3. Populate Requester that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Requester that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Requester <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Company in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned To Me Tab<br>2. Click filter Icon<br>3. Populate Company that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Company that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Company <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Department in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned To Me Tab<br>2. Click filter Icon<br>3. Populate Department that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Department that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Department<br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Project in For My Approval/Assigned tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned To Me Tab<br>2. Click filter Icon<br>3. Populate Project that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Project that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Project <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Status in For My Approval/Assigned tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned To Me Tab<br>2. Click filter Icon<br>3. Populate Status that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Status that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Status <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Type in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate Type that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Type that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Type <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Requester in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate Requester that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Requester that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Requester <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Company in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate Company that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Company that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Company <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Department in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate Department that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Department that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Department <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Project in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate Project that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Project that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Project<br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify Filtering for Status in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate Status that is not existing on the table in filter field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate/select Status that is existing on the table in filter field<br>7. Click &quot;Search&quot; button<br>8. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Filter Field and Dashboard Table <br>6. Field should be populated<br>7. Should displayed all matched Status <br>8. Should reset the Filter Field and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify multiple Filters are used at the same time in All  tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS All Tab Dashboard, click filter Icon<br>2.  Populate/select multiple filter fields that are existing on the dashboard table (example: Type and Requester)<br>3. Click &quot;Search&quot; button<br>4. Click Clear button</td><td class="s3"></td><td class="s3">1. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>2. Field should be populated<br>3. Should only be able to Filter the existing Data from the first Filter and displayed all matched results of multiple filters used<br>4. Should reset the Filter Fields and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify multiple Filters are used at the same time in For My Approval/Assigned tab dashboard </td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Approval/Assigned tab <br>2..Click filter Icon<br>3. Populate/select multiple filter fields that are existing on the dashboard table (example: Type  and Requester)<br>4. Click &quot;Search&quot; button<br>5. Click Clear button</td><td class="s3"></td><td class="s3">1. 1. Should displayed all the User&#39;s for Approval/Assigned RS or Related Documents<br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Filter fields should be populated<br>4. Should only be able to Filter the existing Data from the first Filter and displayed all matched results of multiple filters used<br>5. Should reset the Filter Fields and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="809848333R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s3"></td><td class="s3">Enable Filtering in Dashboard Page</td><td class="s3">Verify multiple Filters are used at the same time in For My Requests tab dashboard</td><td class="s3">High</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin  or any User Type</a></td><td class="s3">1. On RS Dashboard, click For My Requests Tab <br>2..Click filter Icon<br>3. Populate/select multiple filter fields that are existing on the dashboard table (example: Type and Requester)<br>4. Click &quot;Search&quot; button<br>5. Click Clear button</td><td class="s3"></td><td class="s3">1. Should displayed all the User&#39;s Requested RS <br>2. Filters are expanded and should display available Data per Filter based on the Table of Requisition Slip<br>3. Filter fields should be populated<br>4. Should only be able to Filter the existing Data from the first Filter and displayed all matched results of multiple filters used<br>5. Should reset the Filter Fields and Dashboard Table </td><td class="s3"></td><td class="s3"></td><td class="s8">Passed</td><td class="s3"></td><td class="s9">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr></tbody></table></div>