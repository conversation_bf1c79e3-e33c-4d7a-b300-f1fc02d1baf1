<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1083971157C0" style="width:103px;" class="column-headers-background">A</th><th id="1083971157C1" style="width:98px;" class="column-headers-background">B</th><th id="1083971157C2" style="width:252px;" class="column-headers-background">C</th><th id="1083971157C3" style="width:252px;" class="column-headers-background">D</th><th id="1083971157C4" style="width:233px;" class="column-headers-background">E</th><th id="1083971157C5" style="width:330px;" class="column-headers-background">F</th><th id="1083971157C6" style="width:184px;" class="column-headers-background">G</th><th id="1083971157C7" style="width:372px;" class="column-headers-background">H</th><th id="1083971157C8" style="width:100px;" class="column-headers-background">I</th><th id="1083971157C9" style="width:88px;" class="column-headers-background">J</th><th id="1083971157C10" style="width:118px;" class="column-headers-background">K</th><th id="1083971157C11" style="width:118px;" class="column-headers-background">L</th><th id="1083971157C12" style="width:118px;" class="column-headers-background">M</th><th id="1083971157C13" style="width:118px;" class="column-headers-background">N</th><th id="1083971157C14" style="width:164px;" class="column-headers-background">O</th><th id="1083971157C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1083971157R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1083971157R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-007 - [Manage Department] - Pull data, Assigning &amp; Updating of Assigned Approvers, Optional Approvers for Association Department </td></tr><tr style="height: 19px"><th id="1083971157R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Department Landing Page</td><td class="s5"></td><td class="s7">Validate Manage Department Landing Page</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Check Department Management page display<br>4. Validate Search Field and Search button<br>5. Validate Clear button<br>6. Validate sorting of each columns</td><td class="s9"></td><td class="s5">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management  page <br>3. Should have the ff:<br>     a. Search Field that able to search Department Name<br>     b. &quot;Search&quot; and &quot;Clear&quot; buttons<br>     c. Should display a Table list of Departments under Cityland<br>        - Department Name<br>        - Department Code<br>        - Actions<br>       i. Edit<br>    d. Should display 10 Projects per Page<br>    e. Should have Sync Button<br>4. Should be able to search Department Name<br>5. Should be able to delete entered words in Search field<br>6.. Should be able to Sort each Columns<br>    a. Department Name<br>        i. Default sorting by: A-Z<br>        ii. Should be able to sort by: A-Z, Z-A<br>    b. Department Code<br>        i. Should be able to sort by: 0-9, 9-0</td><td class="s5" rowspan="16"></td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Pull Department Data</td><td class="s5"></td><td class="s5">Validate Manage Department Pull Department Data  when Project is not on the list</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. Department Master File integration has been setup</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Sync&quot; button on Project List page </td><td class="s5"></td><td class="s5">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management page <br>3. Should initiate syncing of Departments from Department Master File and add the Deprtment/s in the List.<br>4. Should sync the Department List for Filters and Forms<br>5. Should display the Date and Time of the last triger of the Sync Button<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s10">Not Run</td><td class="s11">Not Run</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5">3/5: TASK ticket CITYLANDPRS-767 still open for Dev to add data. Current data on the table is 42. Dev to add and QA to verify if table has more than 42 items<br><br>1/27 Kams: Need dev to add data to test this. Raised TASK - CITYLANDPRS-767</td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Pull Department Data</td><td class="s5"></td><td class="s5">Validate Manage Department Pull Department Data  when Project has already on the file</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. Department Master File integration has been setup<br>6.  An update of existing Depament has been made</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Sync&quot; button on Project List page </td><td class="s14"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management page <br>3. Should initiate syncing of Departments from Department Master File and update the Department Details if an update on the existing Project has been made in the Department Master File<br>4. Should sync the Department List for Filters and Forms<br>5. Should display the Date and Time of the last triger of the Sync Button<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT]  View Department</td><td class="s5"></td><td class="s5">Validate Manage Department View Department</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br></a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Department Management list table</td><td class="s14"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management List page <br>3. Should display Department Details View page<br>4. Should display a non-editable Fields for<br>    a. Department Name<br>    b. Department Code<br>5. Should have a section for  Requests to Assigning Department&#39;s  Approvers<br>    a. Should have an Edit Button that will allow the Management of Approvers</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Assigning of Approvers for RS</td><td class="s5"></td><td class="s5">Validate Manage Department Assigning of Approvers for RS</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br></a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Deparment&quot; sub menu<br>3. Click &quot;Deparment Name&quot; text link on Deparment Management list table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Approver on Add Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Save&quot; button<br>13. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected<br>6. Should display an &quot;Add Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         -Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should have a success toast message and display the newly added Approver  to the Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Assigning of Approvers for RS</td><td class="s5"></td><td class="s5">Validate Manage Department Assigning of Approvers for RS when clicked cancel button</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br></a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Deparment&quot; sub menu<br>3. Click &quot;Deparment Name&quot; text link on Deparment Management list table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Approver on Add Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Cancel&quot; button<br>13. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected <br>6. Should display an &quot;Add Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>           -Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Viewing of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department of Viewing of Approvers for RS</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br></a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Department Management List table</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management page <br>3. Should display sections of the Department View<br>    a. Should display Fields for Department Name and Department Code<br>    b. Requests<br>4. Should have a section for Requests - Assigning of Approvers<br>    a. Should allow viewing by clicking the Accordion<br>        i. If without an Approver, should display a Note - &quot;Note: Only department heads can be assigned in this level.&quot;<br>        ii. If with an Approver, should display the Approver&#39;s Name</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1047">3/5: Passed with  bug https://youtrack.stratpoint.com/issue/CITYLANDPRS-1047</a></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Assigning of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department of Assigning of Optional Approvers for RS</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Department Management List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Optional Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level of Optional Approvers<br>9. Search and select an Approver on Add Optional Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Save&quot; button<br>13. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Department Details<br>    b. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected<br>6. Should display an &quot;Add Optional Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an optional approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>          i. Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Optional Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should have a success toast message and display the newly added Optional Approver to the Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Assigning of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department of Assigning of Optional Approvers for RS when clicked cancel button</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Deparment&quot; sub menu<br>3. Click &quot;Deparment Name&quot; text link on Deparment Management list table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Optional Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Optional Approver on Add Optional Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Cancel&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected <br>6. Should display an &quot;Add Optional Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an optional approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>           -Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Optional Approval&quot; Modal <br>9. Newly Optional Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department for Updating of Approvers for RS</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin<br>6. An Approver has been already assigned</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         i. Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should display a success toast message and should Save the new assigned approver to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department for Updating of Approvers for RS when clicked cancel button</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin<br>6. An Approver has been already assigned</a></td><td class="s7">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Cancel&quot; button</td><td class="s14"></td><td class="s15">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         i. Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department for Updating of Optional Approvers for RS</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin<br>6. An Approver has been already assigned</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned Optional approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on Optional Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Optional Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         i. Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of Optional approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Optional Approver should be selected<br>10. Should be able to display the User to the Optional Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should display a success toast message and should Save the new assigned Optional approver to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Department for Updating of Optional Approvers for RS when clicked cancel button</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin<br>6. An Optional Approver has been already assigned</a></td><td class="s7">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Department Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an Optional assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on Optional Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Optional Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Cancel&quot; button<br></td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Department Management List page <br>3. Should display sections of the Department View<br>    a. Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         i. Users with the following User Types should be shown<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Staff<br>           iv) Division Head<br>           v) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of Optional approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Optional Approver should be selected<br>10. Should be able to display the User to the Optional Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s18"></td><td class="s18"></td></tr><tr style="height: 19px"><th id="1083971157R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s18"></td><td class="s6">[MANAGE DEPARTMENT] Association Department View</td><td class="s6"></td><td class="s6">Validate Manage Department for Association Department View</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Association Department Name&quot; text link on Department Management List table</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management page <br>3. Should display sections of the Department  Association View<br>    a. Should display Fields for Department Name and Department Code<br>    b. Requests<br>4. Should have a section for Requests - Assigning of Approvers<br>    a. Should allow viewing by clicking the Accordion<br>        i. If without an Approver, should display a Note - &quot;Note: Only department heads can be assigned in this level.&quot;<br>        ii. If with an Approver, should display the Approver&#39;s Name<br>5. Should have Edit button</td><td class="s19">Failed</td><td class="s20">Failed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s18"></td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1059">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1059</a></td></tr><tr style="height: 19px"><th id="1083971157R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s18"></td><td class="s6">[MANAGE DEPARTMENT] Assigning of Approvers for Association Department</td><td class="s5"></td><td class="s5">Validate Manage Department  Assigning of Approvers for Association Department</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Deparment&quot; sub menu<br>3. Click &quot;Association Department Name&quot; text link on Deparment Management list table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an &#39;&#39;Association&#39;&#39; Department Approver on Add Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Save&quot; button<br>13. Click &quot;Continue&quot; button on the modal</td><td class="s14"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management List page <br>3. Should display sections of the Department View<br>    a. Association Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected<br>6. Should display an &quot;Add Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         -Users with the following User Types should be shown<br>           i) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Approval&quot; Modal <br>9. Association Department Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should have a success toast message and display the newly added Approver  to the Approvers List</td><td class="s21">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s18">w/ bug 756</td><td class="s18"></td></tr><tr style="height: 19px"><th id="1083971157R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s18"></td><td class="s6">[MANAGE DEPARTMENT] Assigning of Approvers for Association Department</td><td class="s5"></td><td class="s5">Validate Manage Department  Assigning of Approvers for Association Department when clicked cancel button</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s7">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Deparment&quot; sub menu<br>3. Click &quot;Association Department Name&quot; text link on Deparment Management list table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Association Department Approver on Add Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Cancel&quot; button</td><td class="s9"></td><td class="s7">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Department Management List page <br>3. Should display sections of the Department View<br>    a. Association Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected <br>6. Should display an &quot;Add Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>           -Users with the following User Types should be shown<br>           i) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Approval&quot; Modal <br>9. Association Department Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s18"></td><td class="s18"></td></tr><tr style="height: 19px"><th id="1083971157R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s22"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Assigned Approvers for Association Department</td><td class="s5"></td><td class="s5">Validate Manage Department for Updating of Assigned Approvers for Association Department</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s5">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Association Department Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Association Department Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s5"></td><td class="s7">1. Should click Association Department<br>2. Should display Fields for Department Name and Department Code<br>3. Should have a section for Assigning of Approvers<br>4. Should click Edit Button that will allow the Management of Approvers<br>5. Should click the Edit Icon for the Level<br>    a. Should display a Modal to select an Approver<br>        i. Users with the following User Types should be shown<br>           i) Area Staff<br>     b. Should select the User and Confirm Updating of the User as an Approver for the Level<br>         i. Once Confirmed, should display the User to the Approvers List that is subject for submission<br>         ii. Should not allow the same Approver with a different Level on the same Workflow<br>6. Should require to have an Approver for all of the Levels<br>    a. If a Level is empty User, has an ability to Delete the Level to proceed<br>    b. If a Level already has an Assigned User, should disable Delete of Level<br>7. Should have a Cancel and Save Buttons<br>    a. If Cancel Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should not update any of the selected Approvers for the Levels<br>     b. If Save Button is clicked, should display a Confirmation Modal<br>         i. Once Confirmed, should check if all of the Levels has an assigned Approver<br>            i) If without an Approver should not allow to continue<br>            ii) If all Levels has an Approver, should Save the Approvers assigned</td><td class="s5" rowspan="2"></td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s22"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Assigned Approvers for Association Department</td><td class="s5"></td><td class="s5">Validate Manage Department  for Updating of Assigned Approvers for Association Department when clicked cancel button</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Department page<br>5. User is an IT Admin</a></td><td class="s7">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Department&quot; sub menu<br>3. Click &quot;Association Department Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Association Department Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Cancel&quot; button</td><td class="s5"></td><td class="s7">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Department Management List page <br>3. Should display sections of the Department View<br>    a. Association Department Details<br>    b. Requests <br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>         i. Users with the following User Types should be shown<br>           i) Area Staff<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Association Department Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1083971157R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s22"></td><td class="s6">[MANAGE DEPARTMENT] Updating of Assigned Approvers for Association Department</td><td class="s5"></td><td class="s5">Validate Association Department area approvers are displayed in sequence</td><td class="s5"></td><td class="s5">1. Login<br>2. Click Manage &gt; Department<br>3. Search for &#39;Association&#39; Department &gt; Click link<br>4. Click Edit<br>4. Click add approver from area 1 to area 4<br>5. Click Save<br>6. Re-open same &quot;Association Department&quot;<br>7. Click the dropdown arrow<br>8. Validate the area approvers are in sequence</td><td class="s5"></td><td class="s5">Area approvers should be displayed in sequence</td><td class="s23"></td><td class="s24">Failed</td><td class="s11">Passed</td><td class="s12"></td><td class="s13">Not Started</td><td class="s12"></td><td class="s23">Bug 765</td><td class="s23"></td></tr></tbody></table></div>