# Purchase Order Workflow

This document outlines the complete workflow for purchase orders in the PRS system, from creation to delivery.

## Workflow Diagram

```mermaid
flowchart TD
  Start([PRS Generates PO]) --> S1[For PO Review]
  S1 -->|Submitted| S2[For PO Approval]
  S2 -->|Fully Approved| S3[For Sending]
  S1 -->|Cancels PO| S5[PO Cancelled]
  S2 -->|Cancels PO| S6[PO Cancelled]
  S2 -->|Rejected| S4[PO Rejected]
  S4 -->|Return| S1
  S3 -->|Change status| S8[For Delivery]
  S8 -->|All items delivered and paid| S9[Closed PO]
```

## Status Definitions

| Status | Description |
|--------|-------------|
| FOR_PO_REVIEW | Initial status when a purchase order is created, to be reviewed by assigned purchasing staff |
| FOR_PO_APPROVAL | Purchase order has been submitted for approval |
| FOR_SENDING | Purchase order has been approved and is awaiting sending of PO to supplier |
| FOR_DELIVERY | Purchase order has been sent to supplier and is awaiting delivery |
| PO_REJECTED | Purchase order has been rejected by an approver |
| PO_CANCELLED | Purchase order has been cancelled (e.g., supplier suspended, user triggered) |
| CLOSED_PO | Purchase order items is fully delivered and fully paid |

## Detailed Workflow Steps

### 1. Purchase Order Creation

**Actor**: System (Automatic)

**Actions**:

- Create purchase orders after canvass approval
- Group items by supplier and terms
- Generate PO Number

**Status Transitions**:

- FOR_PO_REVIEW (default)

**Business Rules**:

- Purchase orders are created automatically after canvass approval
- Purchase orders are grouped by supplier and terms
- PO Number is auto-generated
- Includes all selected items from the canvass for each supplier
- Includes all price, quantity and total amount


### 2. Purchase Order Review

**Actor**: Assigned Purchasing Staff

**Actions**:

- Review purchase order details
- Update item quantity
- Update warranty (optional) and delivery address
- Add further discount - fixed or percentage
- Submit for approval

**Status Transitions**:

- FOR_PO_REVIEW → FOR_PO_APPROVAL
- PO_REJECTED → FOR_PO_APPROVAL (if resubmitted after rejection)

**Business Rules**:

- Purchase order details can be updated during review
- Warranty and delivery address can be specified
- Status changes to FOR_PO_APPROVAL when submitted for approval

### 3. Purchase Order Approval

**Actor**: Approvers

**Actions**:

- Review the purchase order
- Approve or reject the purchase order
- Add note of approval (optional) or rejection (required)
- Edit item quantity or delete an item from the table (as needed)


**Status Transitions**:

- FOR_PO_APPROVAL → FOR_SENDING (if all approvers approve)
- FOR_PO_APPROVAL → PO_REJECTED (if any approver rejects)

**Business Rules**:

- Only FOR_PO_APPROVAL or PO_REJECTED purchase orders can be approved
- User must be an assigned approver for the purchase order
- Approvers are processed in order by level
- If all approvers approve, status changes to FOR_SENDING
- Status will remain as FOR PO APPROVAL until fully approved
- Additional approvers can be added during the approval process
- Approvers can input a note during approval
- Approvers can edit item quantity or delete an item from the table
- Changes in quantity should reflect in the remaining qty to be canvassed
- If all approvers approve, status changes to FOR_SENDING

### 4. Purchase Order Rejection

**Actor**: Approvers

**Actions**:

- Review the purchase order
- Reject the purchase order
- Provide rejection reason

**Status Transitions**:

- FOR_PO_APPROVAL → PO_REJECTED

**Business Rules**:

- Only FOR_PO_APPROVAL purchase orders can be rejected
- User must be an assigned approver for the purchase order
- Rejection requires a reason/comment
- Upon rejection, status changes to PO_REJECTED

### 5. Purchase Order Cancellation

**Actor**: System or Purchasing Staff

**Actions**:

- Cancel the purchase order
- Provide cancellation reason

**Status Transitions**:

- FOR_PO_REVIEW → PO_CANCELLED
- FOR_PO_APPROVAL → PO_CANCELLED

**Business Rules**:

- Purchase orders with status FOR_PO_REVIEW or FOR_PO_APPROVAL will be cancelled if the supplier is suspended or
- Purchase orders can still be cancelled manually if the status is FOR_PO_REVIEW or FOR_PO_APPROVAL - provided a reason
- Upon cancellation, status changes to PO_CANCELLED
- Purchase order approvers are removed
- Should allow cancelled PO items to be canvassed again (add PO Qty to Remaining Qty to be canvassed)

### 6. Purchase Order Sending

- Purchasing Staff will send the PO to supplier manually (outside of the system)
- After sending, assigned Purchasing Staff can change status from FOR_SENDING to FOR_DELIVERY

## Example Scenarios

### Scenario 1: Standard Purchase Order Flow

1. System creates purchase orders after canvass approval
2. Purchasing Staff reviews and updates purchase order details
3. Purchasing Staff submits the purchase order for approval
4. Approvers review and approve the purchase order
5. Purchase order status changes to FOR_SENDING
6. Purchasing Staff sends the Purchase Order to supplier - offline
7. Manually change the status from FOR_SENDING to FOR_DELIVERY
8. Items are delivered based on the purchase order

### Scenario 2: Purchase Order with Rejection

1. System creates purchase orders after canvass approval
2. Purchasing Staff reviews and submits the purchase order for approval
3. An approver rejects the purchase order with a reason
4. Purchasing Staff updates the purchase order and resubmits it
5. Approvers approve the purchase order
6. Continue with standard flow

### Scenario 3: Purchase Order Cancellation

1. System creates purchase orders after canvass approval
2. Supplier is suspended
3. System automatically cancels all pending purchase orders for the supplier
4. Allow purchasing staff to re-canvass items from cancelled purchase order

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/purchaseOrderController.js`
- **Service**: `src/app/services/purchaseOrderService.js`
- **Repository**: `src/infra/repositories/purchaseOrderRepository.js`
- **Entity**: `src/domain/entities/purchaseOrderEntity.js`
- **Constants**: `src/domain/constants/purchaseOrderConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic
async function updatePurchaseOrderStatus(purchaseOrderId, newStatus, transaction) {
  const purchaseOrder = await purchaseOrderRepository.findOne({
    where: { id: purchaseOrderId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'FOR_PO_REVIEW': ['FOR_PO_APPROVAL', 'CANCELLED_PO'],
    'FOR_PO_APPROVAL': ['FOR_DELIVERY', 'REJECT_PO', 'CANCELLED_PO'],
    'REJECT_PO': ['FOR_PO_APPROVAL', 'CANCELLED_PO'],
    'FOR_DELIVERY': ['CANCELLED_PO'],
  };
  
  if (!validTransitions[purchaseOrder.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${purchaseOrder.status} to ${newStatus}`);
  }
  
  // Update status
  await purchaseOrder.update({ status: newStatus }, { transaction });
  
  // Update requisition status if needed
  if (newStatus === 'FOR_DELIVERY') {
    await requisitionRepository.update(
      { id: purchaseOrder.requisitionId },
      { status: 'FOR_DELIVERY' },
      { transaction }
    );
  }
  
  return purchaseOrder;
}
```

## Common Issues and Solutions

### Issue 1: Cannot Submit Purchase Order

**Cause**: Missing required fields or validation errors.

**Solution**:

- Check validation error messages
- Ensure all required fields are filled
- Verify delivery address is specified

### Issue 2: Purchase Order Approval Issues

**Cause**: Approvers not assigned correctly or missing permissions.

**Solution**:

- Check if approvers are assigned correctly
- Verify approver permissions
- Ensure approvers are active users

### Issue 3: Purchase Order Cancellation

**Cause**: Supplier suspended or manual cancellation.

**Solution**:

- If supplier is suspended, create new canvass with alternative suppliers
- If manual cancellation, ensure status is still FOR_PO_REVIEW and FOR_PO_APPROVAL
- Ensure reason is provided
- Check if related requisition needs to be reassigned
