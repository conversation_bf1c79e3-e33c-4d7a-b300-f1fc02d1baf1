# Payment and Approval Workflows in Next.js 15 with SSE

This document outlines the implementation of the Payment Request and Approval workflows in the Next.js 15 PRS application with Server-Sent Events (SSE) for real-time updates.

## Table of Contents

1. [Payment Request Workflow](#payment-request-workflow)
2. [Payment Approval Process](#payment-approval-process)
3. [Real-Time Updates with SSE](#real-time-updates-with-sse)
4. [Integration with Accounting Systems](#integration-with-accounting-systems)

## Payment Request Workflow

The Payment Request workflow follows the Delivery and Invoice steps in the procurement process:

```
Delivery & Invoice → Payment → Payment Approval
```

### Implementation with Next.js 15

#### 1. Payment Request Creation

```typescript
// app/payments/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { PaymentRequestSchema } from '@/models/payment-request'

export async function createPaymentRequest(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')
  
  const userId = parseInt(session.user.id)
  
  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = PaymentRequestSchema.parse(rawData)
  
  // Get the purchase order and invoice
  const purchaseOrder = await prisma.purchaseOrder.findUnique({
    where: { id: validatedData.purchaseOrderId },
    include: { 
      requisition: true,
      supplier: true
    }
  })
  
  if (!purchaseOrder) throw new Error('Purchase Order not found')
  
  const invoice = await prisma.invoice.findUnique({
    where: { id: validatedData.invoiceId }
  })
  
  if (!invoice) throw new Error('Invoice not found')
  
  // Create payment request
  const paymentRequest = await prisma.paymentRequest.create({
    data: {
      purchaseOrderId: purchaseOrder.id,
      requisitionId: purchaseOrder.requisition.id,
      invoiceId: invoice.id,
      payableDate: validatedData.payableDate,
      amount: invoice.amount,
      status: 'pending_approval',
      terms: validatedData.terms,
      comment: validatedData.comment,
      createdById: userId,
      prNumber: `PR-${Date.now()}`
    }
  })
  
  // Create approvals for payment request
  const approvers = await getPaymentApprovers(
    purchaseOrder.requisition.departmentId,
    invoice.amount
  )
  
  await prisma.approval.createMany({
    data: approvers.map((approver, index) => ({
      paymentRequestId: paymentRequest.id,
      userId: approver.userId,
      level: index + 1,
      status: 'pending'
    }))
  })
  
  // Notify approvers
  await notifyUsers(
    approvers.map(a => a.userId),
    {
      type: 'PAYMENT_APPROVAL_REQUESTED',
      title: 'Payment Approval Requested',
      message: `A payment request (${paymentRequest.prNumber}) requires your approval`,
      referenceId: paymentRequest.id,
      referenceType: 'payment_request'
    }
  )
  
  // Revalidate paths
  revalidatePath('/payments')
  revalidatePath(`/payments/${paymentRequest.id}`)
  revalidatePath(`/purchase-orders/${purchaseOrder.id}`)
  revalidatePath(`/invoices/${invoice.id}`)
  
  return { success: true, id: paymentRequest.id }
}

// Helper function to get payment approvers
async function getPaymentApprovers(departmentId: number, amount: number) {
  // Get department head
  const departmentHead = await prisma.departmentHead.findUnique({
    where: { departmentId }
  })
  
  // Get finance approvers
  const financeApprovers = await prisma.financeApprover.findMany({
    where: {
      minAmount: { lte: amount },
      maxAmount: { gte: amount }
    },
    orderBy: { level: 'asc' }
  })
  
  // Combine approvers
  const allApprovers = [
    { userId: departmentHead.userId, level: 1 },
    ...financeApprovers.map((approver, index) => ({
      userId: approver.userId,
      level: index + 2
    }))
  ]
  
  return allApprovers
}
```

#### 2. Payment Request Form Component

```tsx
// components/payments/PaymentRequestForm.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createPaymentRequest } from '@/app/payments/actions'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { DatePicker } from '@/components/ui/date-picker'

export function PaymentRequestForm({ purchaseOrder, invoices }) {
  const router = useRouter()
  const [selectedInvoiceId, setSelectedInvoiceId] = useState('')
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    const form = e.target
    const formData = new FormData(form)
    
    // Add purchase order ID
    formData.append('purchaseOrderId', purchaseOrder.id.toString())
    
    // Submit form
    const result = await createPaymentRequest(formData)
    
    if (result.success) {
      router.push(`/payments/${result.id}`)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Form fields for payment request details */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="invoiceId">Invoice</label>
          <Select 
            id="invoiceId" 
            name="invoiceId" 
            value={selectedInvoiceId}
            onChange={(e) => setSelectedInvoiceId(e.target.value)}
            required
          >
            <option value="">Select Invoice</option>
            {invoices.map(invoice => (
              <option key={invoice.id} value={invoice.id}>
                {invoice.invoiceNumber} - {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'PHP'
                }).format(invoice.amount)}
              </option>
            ))}
          </Select>
        </div>
        
        <div>
          <label htmlFor="payableDate">Payable Date</label>
          <DatePicker 
            id="payableDate" 
            name="payableDate" 
            required
          />
        </div>
        
        <div>
          <label htmlFor="terms">Payment Terms</label>
          <Select 
            id="terms" 
            name="terms" 
            required
          >
            <option value="30 Days">30 Days</option>
            <option value="60 Days">60 Days</option>
            <option value="90 Days">90 Days</option>
            <option value="Cash on Delivery">Cash on Delivery</option>
            <option value="Cash in Advance (CIA)">Cash in Advance (CIA)</option>
          </Select>
        </div>
        
        <div className="col-span-2">
          <label htmlFor="comment">Comment</label>
          <Textarea 
            id="comment" 
            name="comment" 
            rows={3}
          />
        </div>
      </div>
      
      {/* Submit button */}
      <div className="flex justify-end">
        <Button type="submit">
          Submit Payment Request
        </Button>
      </div>
    </form>
  )
}
```

## Payment Approval Process

The Payment Approval process is the final step in the procurement workflow:

### Implementation with Next.js 15

#### 1. Payment Approval Action

```typescript
// app/payments/[id]/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'

export async function approvePayment(
  paymentRequestId: number,
  comment: string
) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')
  
  const userId = parseInt(session.user.id)
  
  // Get the payment request
  const paymentRequest = await prisma.paymentRequest.findUnique({
    where: { id: paymentRequestId },
    include: { 
      approvals: true,
      purchaseOrder: {
        include: { requisition: true }
      }
    }
  })
  
  if (!paymentRequest) throw new Error('Payment request not found')
  
  // Find the current user's approval
  const approval = paymentRequest.approvals.find(
    a => a.userId === userId && a.status === 'pending'
  )
  
  if (!approval) throw new Error('No pending approval found for this user')
  
  // Update the approval
  await prisma.approval.update({
    where: { id: approval.id },
    data: { 
      status: 'approved',
      comment,
      approvedAt: new Date()
    }
  })
  
  // Check if all approvals are complete
  const pendingApprovals = await prisma.approval.findMany({
    where: { 
      paymentRequestId,
      status: 'pending'
    }
  })
  
  // If all approved, update payment request status
  if (pendingApprovals.length === 0) {
    await prisma.paymentRequest.update({
      where: { id: paymentRequestId },
      data: { status: 'approved' }
    })
    
    // Update requisition status to closed
    await prisma.requisition.update({
      where: { id: paymentRequest.purchaseOrder.requisitionId },
      data: { status: 'closed' }
    })
    
    // Notify the payment request creator
    await notifyUsers([paymentRequest.createdById], {
      type: 'PAYMENT_APPROVED',
      title: 'Payment Request Approved',
      message: `Payment Request #${paymentRequest.prNumber} has been fully approved`,
      referenceId: paymentRequestId,
      referenceType: 'payment_request'
    })
  }
  
  // Revalidate paths
  revalidatePath(`/payments/${paymentRequestId}`)
  revalidatePath('/payments')
  
  return { success: true }
}
```
