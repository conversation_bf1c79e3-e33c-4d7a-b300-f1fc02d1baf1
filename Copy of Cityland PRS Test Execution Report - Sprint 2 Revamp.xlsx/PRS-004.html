<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fff2cc;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1063643287C0" style="width:103px;" class="column-headers-background">A</th><th id="1063643287C1" style="width:98px;" class="column-headers-background">B</th><th id="1063643287C2" style="width:66px;" class="column-headers-background">C</th><th id="1063643287C3" style="width:252px;" class="column-headers-background">D</th><th id="1063643287C4" style="width:233px;" class="column-headers-background">E</th><th id="1063643287C5" style="width:286px;" class="column-headers-background">F</th><th id="1063643287C6" style="width:73px;" class="column-headers-background">G</th><th id="1063643287C7" style="width:531px;" class="column-headers-background">H</th><th id="1063643287C8" style="width:100px;" class="column-headers-background">I</th><th id="1063643287C9" style="width:88px;" class="column-headers-background">J</th><th id="1063643287C10" style="width:156px;" class="column-headers-background">K</th><th id="1063643287C11" style="width:156px;" class="column-headers-background">L</th><th id="1063643287C12" style="width:156px;" class="column-headers-background">M</th><th id="1063643287C13" style="width:156px;" class="column-headers-background">N</th><th id="1063643287C14" style="width:105px;" class="column-headers-background">O</th><th id="1063643287C15" style="width:129px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1063643287R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s4">Remarks</td><td class="s4">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1063643287R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5" colspan="16">PRS-004 - Manage Supplier - View and Edit Supplier Details, Attachment, Notes, Activate/Suspend Supplier</td></tr><tr style="height: 19px"><th id="1063643287R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s6">PRS-004-001</td><td class="s7">[MANAGE SUPPLIER] Supplier Landing Page</td><td class="s6"></td><td class="s6">Validate Manage Supplier Landing Page</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Check the Supplier page Display<br>4. Validate &quot;Search&quot; functionality<br>5. Validate &quot;Clear&quot; button<br>6. Validate sorting of Table Columns</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier List with Columns for:<br>    a. Supplier Name<br>    b. Address<br>    c. Citizenship Code<br>    d. Nature of Income<br>    e. TIN<br>    f. Individual/Corporate Code<br>    g. Contact Person<br>    h. Contact Number<br>    i. Line of Business<br>    j. Status<br>       i. Active<br>       ii. Suspended<br>    k Actions<br>       i. Edit<br>       ii. Suspend/Activate<br>4. Should have Search Field for Supplier Name that is triggered by Search Button<br>5. Should be cleared  the entered text on the search field <br>6. Should be sorted by Supplier Name Alphabetically(A-Z) and by Status (Active-Suspended)<br>   a. Should have Sorting per Column<br>       i. Supplier Name<br>          i) Default sorting: A-Z<br>          ii) Can sort: A-Z, Z-A<br>       ii. Address<br>          i) Can sort: 0-9, A-Z || 9-0, Z-A<br>       iii. Citizenship Code<br>           i) Can sort: A-Z, Z-A<br>       iv. Nature of Income<br>           i) Can sort: A-Z, Z-A<br>       v. TIN<br>          i) Can sort: 0-9, 9-0<br>       vi. Individual/Corporate Code<br>          i) Can sort: C-I, I-C<br>       vii. Contact Person<br>           i) Can sort: A-Z, Z-A<br>       viii. Contact Number<br>          i) Can sort: 0-9, 9-0<br>       ix. Status <br>          i) Default: Active<br>          ii) Can sort: Active - Suspended || Suspended - Active<br>6. Should display 10 Suppliers per Page<br>7. Should have Sync Supplier List Button<br>    a. Should display the Date and Time of the Last Sync of Suppliers<br></td><td class="s8" rowspan="20"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=1837984458#gid=1837984458">https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=1837984458#gid=1837984458</a></td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s6">PRS-004-002</td><td class="s7">[MANAGE SUPPLIER] Pull Supplier Data</td><td class="s6"></td><td class="s6">Validate Manage Supplier Pull data when supplier is not yet on the table list</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. New Supplier has been available on the supplier master list</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  &quot;Sync&quot; Supplier list button<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should initiate syncing of Suppliers from Supplier Master List and add the Supplier in the List with a Active status <br>4. Should sync the Suppliers List for Filters and Forms<br>5. Should retain the Attachments and Notes to the Supplier if their Details were updated during the Syncing<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s6">PRS-004-003</td><td class="s7">[MANAGE SUPPLIER] Pull Supplier Data</td><td class="s6"></td><td class="s6">Validate Manage Supplier Pull data when supplier is already on the table list</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. Details of Existing Supplier has been updated on the supplier master list</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  &quot;Sync&quot; Supplier list button<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should initiate syncing of Suppliers from Supplier Master List and update the Supplier Details if an update on the existing Supplier has been made.<br>4. Should sync the Suppliers List for Filters and Forms<br>5. Should retain the Attachments and Notes to the Supplier if their Details were updated during the Syncing<br>6. Should return back to Page 1 of the Table after syncing<br>7. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s6">PRS-004-004</td><td class="s7">[MANAGE SUPPLIER] Viewing of Supplier</td><td class="s6"></td><td class="s6">Validate Manage Viewing of Supplier </td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click any &quot;Supplier Name&quot;  Text Link on the table list<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page with the ff fields:<br>    a. Supplier Name<br>    b. Address<br>    c. Citizenship Code<br>    d. Nature of Income<br>    e. TIN<br>    f. Individual/Corporate Code<br>    g. Contact Person<br>    h. Contact Number<br>    i. Line of Business<br>       a. Should display as &quot;---&quot; if Data is yet to be declared<br>    j. Status<br>4. Should have a section for Attachments and Notes<br>    a. Attachments<br>    b. Notes<br>    c. Should display Button for viewing of uploaded Attachments and indicated Notes<br>5. Should have Edit Button</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s6">PRS-004-005</td><td class="s7">[MANAGE SUPPLIER] Uploading of Attachment for a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier Uploading of Attachment for a Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Attachment/s&quot; button and upload a file that has 25MB and below size<br>5. Click &quot;Submit&quot; button on the Supplier Details page<br>6. Click &quot;Continue&quot; on the Submit Attachment Modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>     a. Should display a Note &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.&quot; below the Attachment Button<br>4. Should accept uploaded different File Formats PDF, Doc File, Excel or CSV, JPG, PNG, JPEG. with Maximum File size of 25MB per File<br>5. Should display a &quot;Submit Attachment&quot; Modal with contains of he ff:<br>    a. A description &quot; You are about to submit an attachment. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;Cancel&quot; nd &quot;Continue&quot; buttons<br>6. Should displayed a success toast message and add the Attached Document in the Attachments View<br>7. Should display a New Attachment Badge above the Attachment Button<br>    a. Should be removed once the Check Attachments Button has been opened</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s6">PRS-004-006</td><td class="s7">[MANAGE SUPPLIER] Uploading of Attachment for a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier Uploading of Attachment for a Supplier when file size is greater than 25MB</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Attachment/s&quot; button and upload a file that has 26MB and up size<br></td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>   a. Should display a Note &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.&quot; below the Attachment Button<br>4. Should not accept the 26MB and up file size<br>       </td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s6">PRS-004-007</td><td class="s7">[MANAGE SUPPLIER] Uploading of Attachment for a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier Uploading of Attachment for a Supplier when uploaded files is greater than 10</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Attachment/s&quot; button and upload 11 or up files <br>5. Click &quot;Submit&quot; button on the Supplier Details page<br>6. Click &quot;Continue&quot; on the Submit Attachment Modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>    a. Should display a Note &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.&quot; below the Attachment Button<br>4. Should accept uploading multiple Files and should no limit <br>5. Should display a &quot;Submit Attachment&quot; Modal with contains of he ff:<br>    a. A description &quot; You are about to submit an attachment. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;Cancel&quot; nd &quot;Continue&quot; buttons<br>6. Should displayed a success toast message and add the Attached Document in the Attachments View<br>7. Should display a New Attachment Badge above the Attachment Button<br>    a. Should be removed once the Check Attachments Button has been opened</td><td class="s14">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">bug has been raised</td><td class="s13">CITYLANDPRS-750</td></tr><tr style="height: 19px"><th id="1063643287R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s6">PRS-004-008</td><td class="s7">[MANAGE SUPPLIER] Indicating a Note for a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier Indicating a Note for a Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Populate or Input a Note in the &quot;Notes&quot; text area that has an emoji<br>5. Click &quot;Submit&quot; button on the Supplier Details page<br>6. Click &quot;Cancel&quot; button on Submit Comment modal<br>7. Populate or Input a Note again in the &quot;Notes&quot; text area that has an emoji<br>8. Click &quot;Submit&quot; button on the Supplier Details page<br>9. Click &quot;Continue&quot;</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4. Notes Text area should be populated<br>5.  Should display a &quot;Submit Comment&quot; Modal with contains of he ff:<br>    a. A description &quot; You are about to submit a comment. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;Cancel&quot; nd &quot;Continue&quot; buttons<br>6. Should Cancel the adding of comments and redirected back to Supplier Details page <br>7. Notes Text area should be populated and should not accept emojis<br>8. Should display a &quot;Submit Comment&quot; Modal <br>9. Should display a New Note Badge above the Note Text Area<br>    a. Should add the Note in the &quot;Comments&quot; View Modal<br>    b. Should be removed once the Check Notes Button has been opened</td><td class="s14">Failed</td><td class="s15">Failed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">bug has been raised</td><td class="s13">CITYLANDPRS-752</td></tr><tr style="height: 19px"><th id="1063643287R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s16">PRS-004-009</td><td class="s17">[MANAGE SUPPLIER] Edit a Supplier</td><td class="s16"></td><td class="s16">Validate Manage Supplier Edit Supplier details when viewing supplier details</td><td class="s18"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s16">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Edit&quot; button and validate fields<br>5. Update all editable fields<br>6. Click &quot;Save&quot; button on Supplier Details Edit page<br>7. Click &quot;Cancel&quot; on the Confirm Changes Modal<br>8. Click &quot;Save&quot; button again on Supplier Details Edit page<br>9. Click &quot;Continue&#39; on the Confirm Changes Modal</td><td class="s19"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4.The ff fields should be enabled and editable:<br>    a. Contact Person<br>        i. Text Field with maximum of 100 Characters<br>        ii. Should allow Letters and specific Special Characters (-.&#39;)<br>        iii. Should display as &quot;---&quot; if Contact Person is yet to be set<br>        iv. Field is required<br>    b. Contact Number<br>        i. Should have a format of +63<br>        ii. Text Field with maximum of 13 Digits including +63<br>        ii. Should allow numbers only<br>        iii. Should display as &quot;---&quot; if Contact Number is yet to be set<br>        iv. Field is required<br>    c. Line of Business<br>        i. Text Field with maximum of 50 Characters<br>        ii. Should allow Letters and Special Characters except Emojis<br>        iii. Should display as &quot;---&quot; if Lines of Business is yet to be set<br>        iv. Field is required<br>    d. Status<br>        i. Should be a Drop-down of values Active and Suspended<br>5. Editable fields should be updated<br>6. Should displayed a &quot;Confirm Changes&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should redirected back to Supplier Details edit page<br>8. Should displayed a &quot;Confirm Changes&quot; Modal<br>9. A success toast message should be displayed and supplier details are updated.</td><td class="s10">Passed</td><td class="s20">In Progress</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s16">PRS-004-010</td><td class="s17">[MANAGE SUPPLIER] Edit a Supplier</td><td class="s16"></td><td class="s16">Validate Manage Supplier when click cancel in Edit Supplier details</td><td class="s18"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s16">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Edit&quot; button and validate fields<br>5. Update all editable fields<br>6. Click &quot;Cancel&quot; button on Supplier Details Edit page<br>7. Click &quot;Cancel&quot; on the Discard Changes Modal<br>8. Click &quot;Cancel&quot; button again on Supplier Details Edit page<br>9. Click &quot;Continue&#39; on the Discard Changes Modal</td><td class="s19"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4.The ff fields should be enabled and editable:<br>     a. Contact Person<br>        i. Text Field with maximum of 100 Characters<br>        ii. Should allow Letters and specific Special Characters (-.&#39;)<br>        iii. Should display as &quot;---&quot; if Contact Person is yet to be set<br>        iv. Field is required<br>    b. Contact Number<br>        i. Should have a format of +63<br>        ii. Text Field with maximum of 13 Digits including +63<br>        ii. Should allow numbers only<br>        iii. Should display as &quot;---&quot; if Contact Number is yet to be set<br>        iv. Field is required<br>    c. Line of Business<br>        i. Text Field with maximum of 50 Characters<br>        ii. Should allow Letters and Special Characters except Emojis<br>        iii. Should display as &quot;---&quot; if Lines of Business is yet to be set<br>        iv. Field is required<br>    d. Status<br>        i. Should be a Drop-down of values Active and Suspended<br>5. Editable fields should be updated<br>6. Should displayed a &quot;Discard Changes&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should redirected back to Supplier Details edit page<br>8. Should displayed a &quot;Discard Changes&quot; Modal<br>9. Should redirected back to Supplier Details list table page</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s16">PRS-004-011</td><td class="s17">[MANAGE SUPPLIER] Edit a Supplier</td><td class="s16"></td><td class="s16">Validate Manage Supplier Edit Supplier details thru Supplier table list</td><td class="s18"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page</a></td><td class="s16">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  &quot;Edit&quot; Icon in Actions column on the table list<br>4. Click &quot;Edit&quot; button  and validate fields<br>5. Update all editable fields<br>6. Click &quot;Save&quot; button on Supplier Details Edit page<br>7. Click &quot;Cancel&quot; on the Confirm Changes Modal<br>8. Click &quot;Save&quot; button again on Supplier Details Edit page<br>9. Click &quot;Continue&#39; on the Confirm Changes Modal</td><td class="s19"></td><td class="s16">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4.The ff fields should be enabled and editable:<br>     a. Contact Person<br>        i. Text Field with maximum of 100 Characters<br>        ii. Should allow Letters and specific Special Characters (-.&#39;)<br>        iii. Should display as &quot;---&quot; if Contact Person is yet to be set<br>        iv. Field is required<br>    b. Contact Number<br>        i. Should have a format of +63<br>        ii. Text Field with maximum of 13 Digits including +63<br>        ii. Should allow numbers only<br>        iii. Should display as &quot;---&quot; if Contact Number is yet to be set<br>        iv. Field is required<br>    c. Line of Business<br>        i. Text Field with maximum of 50 Characters<br>        ii. Should allow Letters and Special Characters except Emojis<br>        iii. Should display as &quot;---&quot; if Lines of Business is yet to be set<br>        iv. Field is required<br>    d. Status<br>        i. Should be a Drop-down of values Active and Suspended<br>5. Editable fields should be updated<br>6. Should displayed a &quot;Confirm Changes&quot; Modal that contains the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should redirected back to Supplier Details edit page<br>8. Should displayed a &quot;Confirm Changes&quot; Modal<br>9. A success toast message should be displayed and supplier details are updated.</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="1063643287R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s6">PRS-004-012</td><td class="s7">[MANAGE SUPPLIER] Viewing of Uploaded Attachment</td><td class="s6"></td><td class="s6">Validate Manage Supplier Viewing of Uploaded Attachment</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br>6. An Attachment is made</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Check Attachments&quot; button on Supplier Details page</td><td class="s9"></td><td class="s6">1.1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4. Should be able to Search the File Name of an Attachment<br>     a. Should click Search Button to trigger the Search<br>     b. Should click Clear Button to delete entered file name<br>5. Should display<br>     a. Listing of Files<br>     b. New Attachment Badge<br>     c. Exit and &quot;Close Window&quot; button</td><td class="s14">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">Bug has been raised</td><td class="s13">CITYLANDPRS-749</td></tr><tr style="height: 19px"><th id="1063643287R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s6">PRS-004-013</td><td class="s7">[MANAGE SUPPLIER] Viewing of Added Note</td><td class="s6"></td><td class="s6">Validate Manage Supplier Viewing of Added Note</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br>6. A Note is made</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Check Notes&quot; button on Supplier Details page</td><td class="s9"></td><td class="s6">1.1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4. Should be able to displayed the ff;<br>    a. Display a Modal with all of the Notes per Uploaded Date<br>    b. Allow Filtering the Date From and Date To for easy viewing of Notes<br>       i. Should be triggered by Apply Button to proceed with filtering<br>       ii. X Icon should clear the Date Filters<br>    c. Can display the Note and the one who have added the Note for the Supplier<br>    d. Should have a Close Window Button to exit the Attachment Modal</td><td class="s14">Failed</td><td class="s15">Failed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">Bug has been raised</td><td class="s13">CITYLANDPRS-749</td></tr><tr style="height: 19px"><th id="1063643287R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s6">PRS-004-014</td><td class="s7">[MANAGE SUPPLIER] Allow removing of Attachments per Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier to Allow removing of Attachments per Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br>6. An Attachment is made</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Check Attachments&quot; button on Supplier Details page<br>5. Click &quot;X&quot; icon on the attachments<br>6. Click &quot;Cancel&quot; button on the Delete attachment modal<br>7. Click &quot;X&quot; icon again on the attachments view modal<br>8. Click &quot;Continue&quot; button on the Delete attachment modal<br>9. Check System Audit Logs</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details page and have a sections for &quot;Attachments&quot; and &quot;Notes&quot;<br>4. Should display Attachment View Modal and should display an X Icon per Attachment<br>5. Should display a &quot;Delete Attachment&quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to delete this attachment. This action is irreversible. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should redirected back to Attachment View Modal<br>7. Should display a &quot;Delete Attachment&quot; Modal <br>8. Should remove the Attachment from the Supplier Attachment View modal<br>9. Should display the removed  attachment action in the System Audit Logs</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s21"></td><td class="s21"></td></tr><tr style="height: 19px"><th id="1063643287R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s6">PRS-004-015</td><td class="s7">[MANAGE SUPPLIER] Suspending a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier when Suspending a Supplier thru Suspend Button under Actions</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click &quot;Suspend&quot; icon button under Actions column on the table list<br>4. Click &quot;Cancel&#39; on the modal<br>5. Click &quot;Suspend&quot; icon again button under Actions column on the table list<br>6. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1.1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display the &quot;Suspend Supplier&quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to suspend this supplier. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>4 Should redirected back to Manage Supplier list page<br>5. Should display the &quot;Suspend Supplier&quot; Modal <br>6. Should update Supplier Status to Suspended<br>        i.Should display a Success Toast Message and automatically close after 3 seconds<br>7. Should retain the Attachments and Notes added prior suspending the Supplier<br>8. Should disable Editing of Supplier<br>9. Should change Suspend Button to Active Button under Actions Column</td><td class="s14">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">Issue has been raised</td><td class="s21">CITYLANDPRS-758</td></tr><tr style="height: 19px"><th id="1063643287R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s6">PRS-004-016</td><td class="s7">[MANAGE SUPPLIER] Suspending a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier when Suspending a Supplier thru Status Drop-down in View Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Select &quot;Suspend&quot; on the dropdown Status <br>5. Click &quot;Cancel&#39; on the modal<br>6. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>7. Select &quot;Suspend&quot; again on the dropdown Status in Supplier Details view page<br>8. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1.  A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details View page <br>4. Should display the &quot;Suspend Supplier&quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to suspend this supplier. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>5 Should redirected back to Manage Supplier list page<br>6. Should display Supplier Details View page <br>7. Should display the &quot;Suspend Supplier&quot; Modal <br>8. Should update Supplier Status to Suspended<br>        i.Should display a Success Toast Message and automatically close after 3 seconds<br>9. Should retain the Attachments and Notes added prior suspending the Supplier<br>10. Should disable Editing of Supplier<br></td><td class="s14">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">Issue has been raised</td><td class="s21">CITYLANDPRS-761</td></tr><tr style="height: 19px"><th id="1063643287R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s6">PRS-004-017</td><td class="s7">[MANAGE SUPPLIER] Suspending a Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier when Suspending a Supplier thru Status Drop-down in Edit Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Edit&quot; button<br>5. Select &quot;Suspend&quot; on the dropdown Status <br>6. Click &quot;Save&quot; button<br>7. Click &quot;Cancel&#39; on the modal<br>8. Click &quot;Save&quot; button again on the Edit Supplier Details page<br>9. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1.  A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details View page <br>4. The ff fields should be enabled and editable:<br>     -Contact Person<br>     -Contact Number<br>     -Line of Business<br>     -Status<br>5. Supplier Status should be updated to &quot;Suspended&quot;<br>6. Should display the &quot;Save Changes &quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should redirected back to Edit Supplier details page<br>8. Should display the &quot;Save Changes&quot; Modal <br>9. Should update Supplier Status to Suspended<br>        i.Should display a Success Toast Message and automatically close after 3 seconds<br>10. Should retain the Attachments and Notes added prior suspending the Supplier<br>11. Should disable Editing of Supplier<br></td><td class="s14">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">Issue has been raised</td><td class="s21">CITYLANDPRS-758</td></tr><tr style="height: 19px"><th id="1063643287R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s6">PRS-004-018</td><td class="s7">[MANAGE SUPPLIER]] Activate a Suspended Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier when Activate a Suspended Supplier thru Suspend Button under Actions</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br>6. A Supplier is tagged and has a Status of Suspended</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click &quot;Active&quot; icon button under Actions column on the table list<br>4. Click &quot;Cancel&#39; on the modal<br>5. Click &quot;Active&quot; icon again button under Actions column on the table list<br>6. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display the &quot;Activate Supplier&quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to activate this supplier. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>4 Should redirected back to Manage Supplier list page<br>5. Should display the &quot;Activate Supplier&quot; Modal <br>6. Should update Supplier Status to Active<br>        i.Should display a Success Toast Message and automatically close after 3 seconds<br>7. Should Allow Supplier to be used in Requisition Slips<br>8. Should Allow updating of Supplier Details once Activated<br>9. Should change Active Button to Suspend Button under Actions Column</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s21"></td><td class="s21"></td></tr><tr style="height: 19px"><th id="1063643287R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s6">PRS-004-019</td><td class="s7">[MANAGE SUPPLIER]] Activate a Suspended Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier when Activate a Suspended Supplier thru Status Drop-down in View Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br>6. A Supplier is tagged and has a Status of Suspended</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Select &quot;Active&quot; on the dropdown Status <br>5. Click &quot;Cancel&#39; on the modal<br>6. Click  any &quot;Supplier Name&quot; Text Link on the table list<br>7. Select &quot;Active&quot; again the dropdown Status in Supplier Details view page<br>8. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1.  A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details View page <br>4. Should display the &quot;Activate Supplier&quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to activate this supplier. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>5 Should redirected back to Manage Supplier list page<br>6. Should display Supplier Details View page <br>7. Should display the &quot;Activate Supplier&quot; Modal <br>8. Should update Supplier Status to Active<br>        i.Should display a Success Toast Message and automatically close after 3 seconds<br>9. Should Allow Supplier to be used in Requisition Slips<br>10. Should Allow updating of Supplier Details once Activated<br></td><td class="s14">Failed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s13">Issue has been raised</td><td class="s21">CITYLANDPRS-761</td></tr><tr style="height: 19px"><th id="1063643287R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s6">PRS-004-020</td><td class="s7">[MANAGE SUPPLIER]] Activate a Suspended Supplier</td><td class="s6"></td><td class="s6">Validate Manage Supplier when Activate a Suspended Supplier thru Status Drop-down in Edit Supplier</td><td class="s8"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Supplier page<br>5. A Supplier has been added in the List<br>6. A Supplier is tagged and has a Status of Suspended</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Supplier&quot; sub menu<br>3. Click  any &quot;Supplier Name&quot;  Text Link on the table list<br>4. Click &quot;Edit&quot; button<br>5. Select &quot;Active&quot; on the dropdown Status <br>6. Click &quot;Save&quot; button<br>7. Click &quot;Cancel&#39; on the modal<br>8. Click &quot;Save&quot; button again on the Edit Supplier Details page<br>9. Click &quot;Continue&quot; on the modal</td><td class="s9"></td><td class="s6">1.  A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Manage Supplier page<br>3. Should display Supplier Details View page <br>4. The ff fields should be enabled and editable:<br>     -Contact Person<br>     -Contact Number<br>     -Line of Business<br>     -Status<br>5. Supplier Status should be updated to &quot;Active&quot;<br>6. Should display the &quot;Save Changes &quot; Modal with contains of the ff:<br>    a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>7. Should redirected back to Edit Supplier details page<br>8. Should display the &quot;Save Changes&quot; Modal <br>9. Should update Supplier Status to Suspended<br>        i.Should display a Success Toast Message and automatically close after 3 seconds<br>10.Should Allow Supplier to be used in Requisition Slips<br>11. Should Allow updating of Supplier Details once Activated</td><td class="s10">Passed</td><td class="s11">Passed</td><td class="s6"></td><td class="s12">Not Started</td><td class="s6"></td><td class="s21"></td><td class="s21"></td></tr></tbody></table></div>