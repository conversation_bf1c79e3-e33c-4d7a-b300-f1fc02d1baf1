services:
  # Backend API
  backend:
    build:
      context: ../prs-backend
      dockerfile: Dockerfile
    image: prs-backend:production
    restart: always
    depends_on:
      - redis
    env_file:
      - ./backend.env
    volumes:
      - backend_logs:/usr/app/logs
      - ../prs-backend/upload:/usr/app/upload
    networks:
      - app_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 150M

  # Frontend
  frontend:
    build:
      context: ../prs-frontend
      dockerfile: Dockerfile
      args:
        - VITE_APP_API_URL=${VITE_APP_API_URL}
    image: prs-frontend:production
    restart: always
    # Remove the command line, let nginx handle it
    env_file:
      - ./frontend.env
    networks:
      - app_network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 50M

  # Database
  postgres:
    image: postgres:13
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    # No external port mapping for security
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app_network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:latest
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - app_network
      - management_network
      - monitoring_network

  # MinIO Object Storage
  minio:
    image: minio/minio
    restart: always
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - minio_data:/data
    networks:
      - app_network

  # Monitoring: Prometheus
  prometheus:
    image: prom/prometheus
    restart: always
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - monitoring_network

  # Monitoring: Grafana
  grafana:
    image: grafana/grafana
    restart: always
    depends_on:
      - prometheus
    volumes:
      - ./grafana:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring_network

  # Container Metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor
    restart: always
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - monitoring_network

  # Node Metrics
  node-exporter:
    image: prom/node-exporter
    restart: always
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring_network

  # Container Management
  portainer:
    image: portainer/portainer-ce
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - management_network

  # Database Management
  adminer:
    image: adminer
    restart: unless-stopped
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    networks:
      - management_network
      - app_network  # Add to app_network to connect to external database

# Removed Nginx Proxy Manager to simplify architecture

  # Cloudflare Tunnel
  cloudflared:
    image: cloudflare/cloudflared:latest
    restart: always
    command: tunnel run
    environment:
      - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
    volumes:
      - ./cloudflared:/etc/cloudflared
    networks:
      - app_network

networks:
  app_network:
  monitoring_network:
  management_network:

volumes:
  postgres_data:
  redis_data:
  backend_logs:
  minio_data:
  prometheus_data:
  grafana_data:
  portainer_data:
