<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-right:none;border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-left:none;border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#538ed5;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-left:none;border-right:none;border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-left:none;border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1824309636C0" style="width:67px;" class="column-headers-background">A</th><th id="1824309636C1" style="width:228px;" class="column-headers-background">B</th><th id="1824309636C2" style="width:65px;" class="column-headers-background">C</th><th id="1824309636C3" style="width:220px;" class="column-headers-background">D</th><th id="1824309636C4" style="width:233px;" class="column-headers-background">E</th><th id="1824309636C5" style="width:494px;" class="column-headers-background">F</th><th id="1824309636C6" style="width:86px;" class="column-headers-background">G</th><th id="1824309636C7" style="width:349px;" class="column-headers-background">H</th><th id="1824309636C8" style="width:100px;" class="column-headers-background">I</th><th id="1824309636C9" style="width:208px;" class="column-headers-background">J</th><th id="1824309636C10" style="width:208px;" class="column-headers-background">K</th><th id="1824309636C11" style="width:208px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="1824309636R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s3">Test Case/Scenario</td><td class="s3">Pre-requisite</td><td class="s3">Test Steps</td><td class="s3">Parameters</td><td class="s3">Expected Results</td><td class="s3">Actual Results</td><td class="s1">Status</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1824309636R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="12"> [RS Creation] - Creation of RS</td></tr><tr style="height: 19px"><th id="1824309636R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">1273-001</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Creation of RS</span></td><td class="s5">Critical</td><td class="s5">Validate Requisition Slip page</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Dashboard &gt; Click New Request<br>2. Should display the following Fields:<br>    a. Category - Required<br>        i. Drop-down with values of Company, Project, Association<br>    b. Type of Request  - Required<br>        i. OFM<br>        ii. Non-OFM<br>        iii. OFM Transfer of Materials<br>        iv.  Non-OFM Transfer of Materials<br>    c. Company  - Required<br>        i. List of Companies Synced in Company Management or created Associations<br>    d. Project  - Required<br>        i.  List of Projects Synced in Project Management<br>    e. Department  - Required<br>        i. List of Departments Synced in Department Management<br>    f. Deliver To<br>        i. List of Company Addresses and Project Addresses <br>    g. Date Required   - Required<br>        i. Date Picker<br>        ii. Should allow selecting of Dates equal or greater than the current Date<br>    h. Purpose - Required<br>        i. Should allow Alphanumeric and Special Characters except Emojis<br>       ii. Maximum of 100 Characters<br>    i.  Charge To (Category)  - Optional<br>        i. Drop-down with values of Company, Project, Association<br>    j.  Charge To - Optional<br>        i. Drop-down of values depending on the selected Category<br>    k. Attachments<br>       i. Uploading of Files such as Images (JPG,JPEG,PNG) and PDF<br>       ii. Maximum of 25MB per File<br>       iii. Note should display &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.&quot;<br>    l. Add Note<br>       i. Text area of Notes with a Maximum Character of 100 Characters<br>    m. Items Table List<br>       i. Should be blank<br>       ii. Should have search that will allow searching by Item Name entered within the Request<br>       iii. Should have Add Item Button<br><br>3. Validate Save Draft button</td><td class="s7"></td><td class="s5">All fields should be present upon clicking &#39;New Request&#39;</td><td class="s8" rowspan="26"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********">https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********</a></td><td class="s9">Failed</td><td class="s5">4/24 Placements of Fields Do Not Match the Figma Design<br><br>4/24 Items Table Sorting Not Working<br><br>4/28: Placements of Fields Do Not Match the Figma Design<br>- Retest passed</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1523">4/24:<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1523<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1513<br><br></a></td></tr><tr style="height: 19px"><th id="1824309636R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">1273-002</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Creation of RS - Creating RS with</span><span style="font-family:Arial;font-weight:bold;color:#000000;"> category selected is company</span></td><td class="s5">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with category selected is Company</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the below fields. Check parameters.<br>   a. Category - Required<br>        i. Company<br>    b. Type of Request  - Required<br>    c. Company  - Required<br>    d. Project  - Required<br>    e. Department  - Required<br>    f. Date Required   - Required<br>    g. Deliver To<br>    h. Purpose - Required<br>    i. Add Note with 100 characters<br>5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom<br>6. Click Submit<br>7. User will be redirected to Dashboard page<br>8. RS number will be automatically generated and details will be displayed on top of the table</td><td class="s5">    a. Category - Company<br>    b. Type of Request  - &lt;any&gt;<br>    c. Company  - &lt;any&gt;<br>    d. Project  - &lt;any&gt;<br>    e. Department  - &lt;any&gt;<br>    f. Date  - &lt;any&gt;<br>    g. Deliver To<br>    h. Purpose - 50 characters with  alpanumeric such as ,.-&#39;<br>    i. Attachments - Upload 1 file with 25 MB in size<br>    j. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! &quot;&#39;, @+=</td><td class="s5">1. Requisition slip will be sucessfully submitted and will be visible on top of the table<br>2. Should be autofilled by the User&#39;s Department in User Management<br>         ii. Options must be the List of Departments Synced in Department Management<br>3. By Default is the Project Address if selected<br>        i) If Company is also selected, Drop-down Options should be the Company Address and project Address<br>.4. Project<br>       i. List of Projects related to the Association<br>       ii. Optional</td><td class="s11">Passed</td><td class="s5">4/24: Project field is not optional<br>4/28: Rtest passed</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1527">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1527</a></td></tr><tr style="height: 19px"><th id="1824309636R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">1273-003</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Creation of RS - Creating RS with </span><span style="font-family:Arial;font-weight:bold;color:#000000;">category selected is Association</span></td><td class="s5">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with Category selected is Company </td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the below fields. Check parameters.<br>  a. Category - Required<br>        i. Association<br>    b. Type of Request  - Required<br>    c. Company  - Required<br>    d. Project  - Required<br>    e. Department  - Required<br>    f. Date Required   - Required<br>    g. Deliver To<br>    h. Purpose - Required<br>    i. Add Note with 100 characters<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters<br>5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom<br>6. Click Submit<br>7. User will be redirected to Dashboard page<br>8. RS number will be automatically generated and details will be displayed on top of the table</td><td class="s5">   a. Category - Association<br>    b. Type of Request  - &lt;any&gt;<br>    c. Company  - &lt;any&gt;<br>    d. Project  - &lt;any&gt;<br>    e. Department  - &lt;any&gt;<br>    f. Date  - &lt;any&gt;<br>    g. Deliver To<br>    h. Purpose - 50 characters with  alpanumeric such as ,.-&#39;<br>    i. Attachments - Upload 1 file with 25 MB in size<br>    j. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! &quot;&#39;, @+=</td><td class="s5">1. Requisition slip will be sucessfully submitted and will be visible on top of the table<br>2. Will be reflected on Dashboard with RS number automatically generated <br>3. Should be autofilled by the User&#39;s Department in User Management<br>         ii. Options must be the List of Departments Synced in Department Management<br>4.i. By Default is the Project Address if selected<br>       i) If Association is also selected, Drop-down Options should be the Association Address and project Address<br>5.Project<br>       i. List of Projects related to the Association<br>       ii. Optional</td><td class="s11">Passed</td><td class="s5">2/28: Able to create but w/ Bug raised<br><br>4/24: Project field is not optional</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-973">2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-973<br><br>4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1527</a></td></tr><tr style="height: 19px"><th id="1824309636R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">1273-004</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Creation of RS - Creating RS with </span><span style="font-family:Arial;font-weight:bold;color:#000000;">category selected is project</span></td><td class="s5">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with category selected is Project</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the below fields. Check parameters.<br>   a. Category - Required<br>        i. Project<br>    b. Type of Request  - Required    <br>    c. Company  - Required<br>    d. Project  - Required<br>    e. Department  - Required<br>    f. Date Required   - Required<br>    g. Deliver To<br>    h. Purpose - Required<br>    i. Add Note with 100 characters<br>    j. Attachments - Upload 1 file with 25 MB in size<br>    k. Add Note with 100 characters including alphanumeric 01-$ $% ()-! &quot;&#39;, @+=<br>5. Click Save Draft. Validate Cancel and Submit button will appear at the bottom<br>6. Click Submit<br>7. User will be redirected to Dashboard page<br>8. RS number will be automatically generated and details will be displayed on top of the table</td><td class="s5">     a. Category - Project <br>    b. Type of Request  - &lt;any&gt;<br>    c. Company  - &lt;any&gt;<br>    d. Project  - &lt;any&gt;<br>    e. Department  - &lt;any&gt;<br>    f. Date  - &lt;any&gt;<br>    g. Deliver To<br>    h. Purpose - 50 characters with  alpanumeric such as ,.-&#39;<br>    i. Attachments - Upload 1 file with 25 MB in size<br>    j. Add Note with 100 characters including  alphanumeric 01-$ $% ()-! &quot;&#39;, @+=</td><td class="s5">1. Requisition slip will be sucessfully submitted and will be visible on top of the table<br>2. Will be reflected on Dashboard with RS number automatically generated <br>3. By Default is the Project Address if selected<br>         i) If Company is also selected, Drop-down Options should be the Company Address and project Address</td><td class="s9">Failed</td><td class="s5">2/28: Able to create but w/ Bug raised<br>4/24: Bug has been raised</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-891">2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-891<br>4/23: CITYLANDPRS-1537<br>CITYLANDPRS-1538</a></td></tr><tr style="height: 19px"><th id="1824309636R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">1273-005</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Creation of RS - Creating RS with </span><span style="font-family:Arial;font-weight:bold;color:#000000;">category selected is project</span></td><td class="s5">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with multiple items (more than 10 items)</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the the required fields <br>5. Add multiple or more than 10 items<br>6. Click Submit</td><td class="s5"></td><td class="s5">1.Should be able to create RS with multiple items</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">1273-006</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Creation of RS - Creating RS with </span><span style="font-family:Arial;font-weight:bold;color:#000000;">category selected is project</span></td><td class="s5">Critical</td><td class="s5">Validate user should be able to create a Requisition Slip with multiple items with steelbars</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login. Check pre-requisite for roles with access<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Populate the the required fields <br>5. Add multiple or more than 10 items with steelbars<br>6. Click Submit</td><td class="s5"></td><td class="s5">1.Should be able to create RS with steelbar items</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">1273-007</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;color:#000000;">Validating error message for empty fields</span></td><td class="s5">High</td><td class="s5">Validate error message returns when required fields are empty</td><td class="s5">1. Should have set-up the Company, Project, Department, OFM Items, and Non-OFM Items<br>2. The following role will have access to create RS or create New Request from Dashboard:-<br>IT Admin<br>Engineers<br>Supervisor<br>Assistant Manager<br>Department Head<br>Division Head<br>Area Staff<br>Purchasing Staff<br>Purchasing Head<br>Management</td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Leave the required fields empty.<br>   a. Category - Required<br>    b. Type of Request  - Required<br>        i. OFM<br>    c. Company  - Required<br>    d. Project  - Required<br>    e. Department  - Required<br>    f. Date Required   - Required<br>    g. Deliver To<br>    h. Purpose - Required<br>    i. Add Note with 100 characters<br>5. Click Save Draft<br>6. Validate error message returns &quot;Please input ONLY: ofm, non-ofm or transfer&quot;<br>7. Populate &#39;Type of Request&#39;<br>8. Click Save Draft. Validate error message returns &quot;Invalid date&quot;<br>9. Populate Date  Required<br>10. Click Save Draft. Validate error message returns &quot;Create RS Purpose must not be Empty&quot;<br>11. Populate Purpose with 50 characters and alpanumeric such as ,.-&#39;<br>12. Click Save Draft. Validate error message returns &quot;Invalid charge to (category)&quot;<br>13. Populate Charge To (Category)<br>14. Click Save Draft. Validate error message returns &quot;Charge to (client) is required&quot;<br>15. Populate Charge To (Client)<br>16. Click Save Draft. Validate error message returns &quot;Department not found&quot;<br>17. Populate Department<br>18.  Click Save Draft. Validate error message returns &quot;Project not found&quot;<br>19. Populate Project<br>20. Click Save Draft. Validate error message returns &quot;Company not found&quot;<br>21. Populate Company<br>22. Click Save Draft. Validate green pop-up message returns &quot;Draft created successfully&quot;<br>23. Click Submit<br>24. Validate green pop-up message returns &quot;Requisition Submitted successfully&quot;</td><td class="s5">   a. Category - &lt;empty&gt;<br>   b. Type of Request  - &lt;empty&gt;<br>    c. Company  - &lt;empty&gt;<br>    d. Project  - &lt;empty&gt;<br>    e. Department  - &lt;empty&gt;<br>    f. Date  - &lt;empty&gt;<br>    g. Purpose - &lt;empty&gt;</td><td class="s5">Error message with associated field should return on every field in question</td><td class="s9">Failed</td><td class="s5">2/28: Able to create but w/ Bug raised<br>4/24: Purpose text field allow only 50 characters</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1535">2/28: https://youtrack.stratpoint.com/issue/CITYLANDPRS-980<br><br>4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1535</a></td></tr><tr style="height: 211px"><th id="1824309636R9" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">10</div></th><td class="s5">1273-008</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;color:#000000;">Validating error message for invalid input</span></td><td class="s5">High</td><td class="s5">Validate error message returns when input is invalid</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4.  Try to input 101 or more Characters in &quot;Purpose&quot;<br>5. Validate error message returns &quot;Create RS Purpose maximum 100 characters&quot;<br>6. Validate you cannot input more than 100 characters in notes</td><td class="s7"></td><td class="s5">Error message will return for purpose and input has limitation for Notes</td><td class="s9">Failed</td><td class="s5">4/24 Error message displays &quot;Create RS Purpose maximum of  50 characters&quot;</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1535"> 4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1535</a></td></tr><tr style="height: 105px"><th id="1824309636R10" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">11</div></th><td class="s5">1273-009</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Validating past date is not allowed</span></td><td class="s5">Critical</td><td class="s5">Validate past date is not allowed</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Click &#39;New Request&#39;<br>4. Click past dates on Date Required<br>5. User should not be able to select past dates and past months/years</td><td class="s7"></td><td class="s5">User should not be able to select past dates  and past months/years</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5">1273-010</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;">  Uploading additional attachment works in Draft RS</span></td><td class="s7">Critical</td><td class="s5">Validate uploading additional attachment works in Draft RS</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Upload 2 files<br>4. Save as Draft<br>5. Draft should be successful with 2 files<br>6. Click same RS<br>7. Upload another 2 files<br>8. Draft should be successful and display the additional 2 files<br>9. Re-open same RS and Validate there are total of 4 files</td><td class="s7"></td><td class="s5">Successfully saved as Draft and additional attachment is reflected with scroll bar</td><td class="s11">Passed</td><td class="s5">3/3: Issue has been logged</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1824309636R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">1273-011</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Scroll bar for multiple attachments</span></td><td class="s7"></td><td class="s5">Validate scroll bar for multiple attachments</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>4. Upload 15 files at the same time<br>5. Validate scroll bar<br>6. Click Save Draft<br>7. Open saved RS<br>8. Validate scroll bar</td><td class="s7"></td><td class="s5">Scroll bar should be present when multiple files were uploaded</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5">1273-012</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;color:#000000;"> Removing attachment works in draft RS</span></td><td class="s7">High</td><td class="s5">Validate remove attachment works in draft RS</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>4. Upload 5 files at the same time<br>5. Delete 2 files by clicking &#39;x&#39;<br>6. Click Save Draft<br>7. Open same RS<br>8. Validate only 2 files were uploaded<br>9. Delete 1 file again<br>10. Click Save Draft<br>11. Validate only 1 file is present on attachment</td><td class="s7"></td><td class="s5">Delete file should work and reflect on draft RS</td><td class="s11">Passed</td><td class="s5">3/3: Issue has been logged</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1824309636R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5">1273-013</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;color:#000000;"> Removing Attachment in Requisition Slip</span></td><td class="s5">High</td><td class="s5">Validate user should be able to remove an Attachment made to the Requisition Slip</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft<br>4. Attachment has been uploaded</td><td class="s5">1. Open RS<br>2. Click Check Attachments button<br>3. In attachment modal view, remove a file by clicking &#39;X&#39; icon<br>4.  Should display a Confirmation Modal once clicked<br>5.Once Confirmed, should remove the Attachment from the Requisition Slip<br>6. Should Log the Action in the System Audit Logs and Request History</td><td class="s5"></td><td class="s5">Remove attachment should work for RS with the following status:<br><br>submitted<br>for-approval<br>assigning<br>assigned<br>partially-canvassed<br>canvass-approval<br>purchase-order<br>partially-ordered<br>transfer<br>pending<br>on-hold<br>returned<br>paying</td><td class="s11">Passed</td><td class="s5">unable to remove attachemnts<br>om/issue/CITYLANDPRS-808</td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">1273-014</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Attachment cannot be removed for Cancelled and Closed RS status </span></td><td class="s7">Critical</td><td class="s5">Validate attachment cannot be removed for Cancelled and Closed RS status </td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft<br>4. Attachment has been uploaded</td><td class="s5">1. Create 1 Requisition Slip with 3 attachments till you reach each status below or find RS with below status:-<br><br>closed<br>cancelled<br>2. Open RS<br>3. Click Check Attachments button<br>4. In attachment modal view, remove a file by clicking &#39;X&#39; icon<br>5. Validate user should not be able to remove an attachment</td><td class="s7"></td><td class="s5">User should not be able to remove an attachment for RS with status as:<br>closed<br>cancelled</td><td class="s11">Passed</td><td class="s5">3/3: able to delete attachment</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1008">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1008</a></td></tr><tr style="height: 19px"><th id="1824309636R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">1273-015</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Delete attachment on Draft RS </span></td><td class="s5">High</td><td class="s5">Validate delete attachment on Draft RS works</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft<br>4. Attachment has been uploaded</td><td class="s5">1. Click Dashboard &gt; New Request<br>2. Add 3 attachments<br>3. Remove 1 attached file or Click &#39;x&#39;&#39;<br>4. Validate attachment is removed<br>5. Click Save Draft<br>6. Click Go back to dashboard<br>7. Open the draft RS created, verify there are only 2 attached files</td><td class="s7"></td><td class="s5">Attachment is deleted for Draft RS</td><td class="s11">Passed</td><td class="s5">3/3: Issue has been logged</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-989">3/3; https://youtrack.stratpoint.com/issue/CITYLANDPRS-989</a></td></tr><tr style="height: 19px"><th id="1824309636R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s5">1273-016</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Draft Requisition Slip created</span></td><td class="s5">High</td><td class="s5">Validate draft Requisition Slip is created</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br></td><td class="s5">1. Login<br>2. Click Dashboard<br>3. Click New Request<br>4. Populate required fields<br>5. Click Save Draft<br>6. Go back to Dashboard<br>7. Validate the RS you created has draft status<br>8. Validate the RS Number format is in TMP-************ or ex: TMP-12AA00000011</td><td class="s7"></td><td class="s5">1. Should display RS Creation Form<br>2. Should be able to select any Type of Request<br>3. Should be able to populate all fields<br>4. Should display a Modal for adding of Items<br>5. Should be able to add items in the item list<br>6. Quantity and Notes should be populated<br>7. Should be able to submit RS<br>8. RS Created should be the ff:<br>           a. Requisition Slip can be edited by the Approver<br>           b.  Requisition Slip is For Approval<br>           c.  Requisition Slip should have an RS Number<br>                i. Format - RS-[AA-ZZ]-[********]</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s5">1273-017</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Updating Draft RS is successful</span></td><td class="s5">High</td><td class="s5">Validate updating Draft RS is successful</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Click an existing RS with Draft status<br>2. Update the values of the following fields<br>Charge to (Category)<br>Charge to (Client)<br>Date Required<br>Type of Request<br>Company<br>Project<br>Department<br>Deliver to<br>Purpose<br>Attachment/s<br>Notes<br>3. Click Save Draft<br>4. Validate table has updated value for all columns<br>5. Open created Draft RS<br>6. Validate all fields are updated with new value</td><td class="s7"></td><td class="s5">Update is successful and reflects on the table and on the Requisition Slip form/page</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s5">1273-018</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Uneditable fields on Submitted RS</span></td><td class="s5">Minor</td><td class="s5">Validate uneditable fields on Submitted RS</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Click Dashboard &gt; New Request<br>2. Populate all required fields &gt; Save as Draft &gt; Submit<br>3. Open submitted RS<br>4. Validate fields are disabled and cannot be updated:<br>Type of Request<br>Date Required<br>Company<br>Project<br>Department<br>Purpose<br>Deliver To<br>Charge To<br><br>5. Validate buttons are enabled and can view:<br>attachments<br>notes</td><td class="s7"></td><td class="s5">Following Fields should not be editable: <br>-Type of Request<br>-Date Required<br>-Company<br>-Project<br>-Department<br>-Purpose<br>-Deliver To<br>-Charge To</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 95px"><th id="1824309636R20" style="height: 95px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 95px">21</div></th><td class="s5">1273-019</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">No duplicate Requisition Slip or Ref. Number </span></td><td class="s5">Critical</td><td class="s5">Validate there are no duplicate Requisition Slip or Ref. Number created and displayed on the table</td><td class="s5">1. User has successfully logged in to their Account</td><td class="s5">1. Click dashboard<br>2. Validate table that there are no duplicates on Ref. Number column</td><td class="s7"></td><td class="s5">No duplicate Requisition Slip or Ref. Number created</td><td class="s11">Passed</td><td class="s5"></td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-951">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-951</a></td></tr><tr style="height: 105px"><th id="1824309636R21" style="height: 105px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 105px">22</div></th><td class="s5">1273-020</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Validating</span><span style="font-family:Arial;font-weight:bold;color:#000000;"> </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Dropdown Search</span></td><td class="s5">Critical</td><td class="s5">Dropdown search should work</td><td class="s5">1. User has successfully logged in to their Account</td><td class="s5">NOT IMPLEMENTED<br><br>1. Validate Dropdown search should work for Category, Type of Request, Company, Project, Department, Deliver to</td><td class="s7"></td><td class="s5">Dropdown search should work</td><td class="s11">Passed</td><td class="s5">4/24: Dropdown serqch for typeof request is not exist<br>4/28: Retest passed in staging</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1539">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1539<br></a></td></tr><tr style="height: 19px"><th id="1824309636R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">1273-021</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Attachment and Notes added on Draft RS</span></td><td class="s5">Critical</td><td class="s5">Validate attachment and notes added should be available on Draft RS</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. On Dashboard, click New Request<br>2. Populate all required fields<br>3. Upload 1 file on attachment<br>4. Add notes<br>5. Click Save draft<br>6. Go back to dashbard<br>7. Open created drafted RS<br>8. Validate that attachment is retained<br>9. Validate that notes is retained</td><td class="s7"></td><td class="s5">Attachment and Notes initially added should retain when user re-opens draft RS</td><td class="s9">Failed</td><td class="s5">CITYLAND-807<br>3/3: Current behavior only attachments should retained in draft RS. Notes will displayed upon submit RS<br>4;24: Notes not saved in draft</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1532">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1532</a></td></tr><tr style="height: 19px"><th id="1824309636R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">1273-022</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION]</span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Notes accepts emoji</span></td><td class="s5">Minor</td><td class="s5">Validate notes accepts emoji</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Create a RS<br>2. Populate all required fields<br>3. Add emoji on notes<br>4. Save as Draft<br>5. Click Submit<br>6. Open RS<br>7. Click Check Notes<br>8. Open notes and validate emoji is added</td><td class="s7"></td><td class="s5">Emoji should not added on Notes</td><td class="s11">Passed</td><td class="s5">able to accept emoji<br>CITYLANDPRS-738<br><br>3/3: still accepts emoji</td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-582">3/3: https://youtrack.stratpoint.com/issue/CITYLANDPRS-582</a></td></tr><tr style="height: 19px"><th id="1824309636R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">1273-023</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Timestamp gets updated every modification on Draft RS</span></td><td class="s5">High</td><td class="s5">Validate timestamp gets updated every modification on Draft RS</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. On Dashboard, click New Request<br>2. Populate all required fields. Save as Draft<br>3. Open draft RS<br>4. Upload attachment<br>5. Click Save<br>6. Timestamp at the bottom should change based on the time of your latest update<br><br><br>Example: Draft Saved:15 January 2025 | 9:01:44 PM</td><td class="s7"></td><td class="s5">Timestamp should get updated based on the time of your latest update</td><td class="s11">Passed</td><td class="s5">unable to update timestamp<br>CITYLANDPRS-810<br><br>3/3: Still encountered the error</td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">1273-024</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Cancelling Requisition Slip</span></td><td class="s5">High</td><td class="s5">Cancel requisition slip should work</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page<br>3. RS is saved as Draft</td><td class="s5">1. Dashboard &gt; Click New Request<br>2. Click Cancel button<br>3. Validate pop-up msg &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot;<br>4. Click &#39;&#39;Continue&quot; and you will be redirected to Dashboard page<br>5. Click New Request again<br>6. Populate all required fields<br>7. Save as draft. Draft saved successfully<br>8. Update purpose<br>9. Click Cancel<br>10. Validate pop-up msg &quot;You are about to cancel this request. Press continue if you want to proceed with this action.&quot;<br>11. Re-open draft RS<br>12. Validate updated purpose do not reflect</td><td class="s7"></td><td class="s5">1. RS will not be created when user cancel the request upon creation<br>2. Changes do not reflect when user modify draft RS and clicks cancel</td><td class="s11">Passed</td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1824309636R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">1273-025</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Creating RS even when 1 RS Approver in Project has been deleted</span></td><td class="s7">High</td><td class="s5">Validate that requestor can still create RS even when 1 of RS approver in Project has been deleted</td><td class="s5">1. User has successfully logged in to their Account</td><td class="s5">1. Login as admin<br>2. Click Manage &gt; Project <br>3. Delete 1 approver from ALABANG HEIGHTS or any project<br>4. Create and Submit RS using ALABANG HEIGHTS as Charge to (Client) and Project<br>5. Validate admin/requestor will be able to create RS successfully<br>6. Validate that the latest approvers appears on submitted RS</td><td class="s7"></td><td class="s5">Admin/requestor will be able to create RS successfully with the latest project approvers</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">1273-026</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS CREATION] </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Audit Log Entry is Created in the Database for RS Creation</span></td><td class="s7">High</td><td class="s5">Verify Audit Log Entry is Created in the Database for RS Creation</td><td class="s5">1. User has successfully logged in to their Account</td><td class="s5">1. Log in as a valid user (e.g., Requester).<br>2. Navigate to the Requisition Slip creation page.<br>3. Fill out the form with valid data (e.g., department/project, items, quantities).<br>4. Submit the RS.<br>5. Query the audit logs table (audit_logs or equivalent) for the specific RS ID.<br>Example: SELECT * FROM audit_logs WHERE entity_id = &#39;&lt;RS_ID&gt;&#39; AND action = &#39;CREATE&#39;;</td><td class="s7"></td><td class="s5">A new row exists in the audit table with:<br>-Action = &quot;CREATE&quot;<br>-Entity = &quot;RS&quot; or &quot;RequisitionSlip&quot;<br>-Entity ID = ID of the RS just created<br>-Created by = User ID who created the RS<br>-Timestamp = Time of creation</td><td class="s11">Passed</td><td class="s7">4/30: tested audit logs in RS create</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s14 softmerge"><div class="softmerge-inner" style="width:293px;left:-1px">[RS Creation] - Filtering of RS Fields</div></td><td class="s15"></td><td class="s15"></td><td class="s16"></td><td class="s16"></td><td class="s16"></td><td class="s16"></td><td class="s16"></td><td class="s16"></td><td class="s16"></td><td class="s16"></td><td class="s17"></td></tr><tr style="height: 77px"><th id="1824309636R29" style="height: 77px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 77px">30</div></th><td class="s5">1273-024</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Project Filter based on Selected Company</span></td><td class="s7">High</td><td class="s5">Validate Filter Projects Based on Selected Company</td><td class="s5">1. Multiple Companies and Projects exist. <br>2. Projects are associated with Companies.</td><td class="s5">1. Select a Company from the Company dropdown.<br>2. Open the Project dropdown.</td><td class="s7"></td><td class="s5">Only Projects associated with the selected Company are displayed.</td><td class="s18" rowspan="20"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********">https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********</a></td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 80px"><th id="1824309636R30" style="height: 80px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 80px">31</div></th><td class="s5">1273-025</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Project Filter based on Selected Association</span></td><td class="s7">High</td><td class="s5">Validate Filter Projects Based on Created Association</td><td class="s5">1. Associations exist between users and Projects.</td><td class="s5">1. Do not select a Company.<br>2. Open the Project dropdown.</td><td class="s7"></td><td class="s5">Only Projects that have an Association with the current user are displayed.</td><td class="s9">Failed</td><td class="s7"></td><td class="s19 softmerge"><div class="softmerge-inner" style="width:379px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1537">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1537</a></div></td></tr><tr style="height: 72px"><th id="1824309636R31" style="height: 72px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 72px">32</div></th><td class="s5">1273-026</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Project Filter based on Selected Project</span></td><td class="s7">High</td><td class="s5">Validate Change Company After Project Selection</td><td class="s5">1. A Project and Company are selected.</td><td class="s5">1. Select a Project from the dropdown.<br>2. Change the selected Company.</td><td class="s7"></td><td class="s5">The selected Project is cleared.<br><br>Project dropdown options are re-filtered based on the new Company.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 82px"><th id="1824309636R32" style="height: 82px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 82px">33</div></th><td class="s5">1273-027</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Select Project and Auto-fill Company</span></td><td class="s7">Critical</td><td class="s5">Validate Select Project First, Auto-Fill Company</td><td class="s5">1. Projects are associated with specific Companies.</td><td class="s5">1. Select a Project from the dropdown.</td><td class="s7"></td><td class="s5">The corresponding Company is automatically filled in the Company field.</td><td class="s9">Failed</td><td class="s7"></td><td class="s19 softmerge"><div class="softmerge-inner" style="width:383px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 </a></div></td></tr><tr style="height: 79px"><th id="1824309636R33" style="height: 79px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 79px">34</div></th><td class="s5">1273-028</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Replacing Company after Project Auto-fill</span></td><td class="s7">Critical</td><td class="s5">Validate  Replace Company After Project Auto-Fill</td><td class="s5">1. Projects are associated with specific Companies.</td><td class="s5">1. Select a Project (auto-fills Company).<br>2. Replace the auto-filled Company with a different one.</td><td class="s7"></td><td class="s5">The selected Project is cleared.<br><br>Project options are re-filtered based on the new Company.</td><td class="s9">Failed</td><td class="s7"></td><td class="s19 softmerge"><div class="softmerge-inner" style="width:383px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 </a></div></td></tr><tr style="height: 79px"><th id="1824309636R34" style="height: 79px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 79px">35</div></th><td class="s5">1273-029</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Editable Company Field</span></td><td class="s7">High</td><td class="s5">Validate Editable Company Field</td><td class="s5">1. Projects are associated with specific Companies.</td><td class="s5">1. Click on the Company field.</td><td class="s7"></td><td class="s5">All Companies (including Associations) are searchable and selectable.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 68px"><th id="1824309636R35" style="height: 68px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 68px">36</div></th><td class="s5">1273-030</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields -</span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Search Functionality in Project Dropdown</span></td><td class="s7">High</td><td class="s5">Validate Search Functionality in Project Dropdown</td><td class="s5">1. Company is selected.</td><td class="s5">1. Open the Project dropdown.<br>2. Type a keyword in the search box.</td><td class="s7"></td><td class="s5">Matching Projects (based on the keyword and current filter) are displayed in real time.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s5">1273-031</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Select Invalid Project</span></td><td class="s7">High</td><td class="s5">Validate Select Invalid Project (Not Associated with Selected Company)</td><td class="s5">1. Company is selected.</td><td class="s5">1. Select a Company (e.g., CDC - CITYLAND DEVELOPMENT CORPORATION).<br>2. Attempt to manually force-select a Project related to a different Company (e.g., Project from BGC RESIDENCES, via dropdown manipulation).</td><td class="s7"></td><td class="s5">Project selection is blocked or cleared automatically.<br><br>Error or warning shown: &quot;Selected Project is not associated with this Company.&quot;</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s5">1273-032</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Change Company After Selecting Valid Project</span></td><td class="s7">Critical</td><td class="s5"> Validate Change Company After Selecting Valid Project</td><td class="s5">1. Project is selected.</td><td class="s5">1. Select a valid Project.<br>2. Change the Company to another unrelated one.<br>3. Try to proceed without the Project being cleared.</td><td class="s7"></td><td class="s5">The system should automatically clear the Project selection.<br><br>Validation error: &quot;Selected Project is not valid for this Company.&quot;</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 80px"><th id="1824309636R38" style="height: 80px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 80px">39</div></th><td class="s5">1273-033</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Auto-Filled Company Does Not Match Selected Project</span></td><td class="s7">Critical</td><td class="s5">Validate Auto-Filled Company Does Not Match Selected Project</td><td class="s5">1. Project is selected.</td><td class="s5">1. Select a Project first (auto-fills the Company).<br>2. Manually override the auto-filled Company with a non-matching one.<br>3. Attempt to submit the form.</td><td class="s7"></td><td class="s5">Project field is cleared.<br><br>Validation prevents submission until a matching Project is selected.</td><td class="s9">Failed</td><td class="s7"></td><td class="s19 softmerge"><div class="softmerge-inner" style="width:383px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 </a></div></td></tr><tr style="height: 75px"><th id="1824309636R39" style="height: 75px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 75px">40</div></th><td class="s5">1273-034</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Search for Non-Existent Project</span></td><td class="s7">High</td><td class="s5">Search for Non-Existent Project</td><td class="s5">1. Company is selected.</td><td class="s5">1. Select a Company.<br>2. Open the Project dropdown.<br>3. Enter a keyword that matches no associated Projects.</td><td class="s7"></td><td class="s5">Dropdown displays “No results found.”<br><br>Prevents selecting an invalid Project.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 81px"><th id="1824309636R40" style="height: 81px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 81px">41</div></th><td class="s5">1273-035</td><td class="s6"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;color:#000000;">Remove Auto-Filled Company Manually</span></td><td class="s7">High</td><td class="s5">Remove Auto-Filled Company Manually</td><td class="s5">1. Project is selected.</td><td class="s5">1. Select a Project first (auto-fills Company).<br>2. Manually remove the Company field value.<br>3. Attempt to proceed without selecting another Company.</td><td class="s7"></td><td class="s5">Project is cleared or form cannot be submitted.<br><br>Error: &quot;Company is required when a Project is selected.&quot;<br></td><td class="s9">Failed</td><td class="s7"></td><td class="s19 softmerge"><div class="softmerge-inner" style="width:383px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538">4/24: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1538 </a></div></td></tr><tr style="height: 19px"><th id="1824309636R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s5">1273-036</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Selected Request Type </span></td><td class="s7">Minor</td><td class="s5">Validate Selected Request Type – OFM or OFM Transfer of Materials</td><td class="s5">1. Multiple Companies and Projects exist. <br>2. Projects are associated with Companies.</td><td class="s5">1. Open Requisition Slip Form.<br><br>2. Select &quot;OFM&quot; or &quot;OFM Transfer of Materials&quot; from the Type of Request dropdown.</td><td class="s7"></td><td class="s5">The correct request type is selected and form dynamically updates if needed.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s5">1273-037</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Add Item Button – Launch Item Modal</span></td><td class="s7">High</td><td class="s5">Validate Add Item Button – Launch Item Modal</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Click the &quot;Add Item&quot; button.</td><td class="s7"></td><td class="s5">Modal for adding items appears.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s5">1273-038</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Filter Items by Selected Trade and Project</span></td><td class="s7">High</td><td class="s5">Validate Filter Items by Selected Trade and Project</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select Trade and Project in the form.<br>2. Open the Item modal.</td><td class="s7"></td><td class="s5">Only OFM Items related to the selected Trade and Project are shown in the modal.<br><br>Unrelated items are not displayed.</td><td class="s11">Passed</td><td class="s5">4/25: all items displayed and not only items related to project and trade <br>4/29: Retest passed</td><td class="s7">4/25: CITYLANDPRS-1543</td></tr><tr style="height: 19px"><th id="1824309636R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s5">1273-039</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Select Item List and Individual Items</span></td><td class="s7">High</td><td class="s5">Validate Select Item List and Individual Items</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Inside the modal, select an Item List.<br>2. Select specific items from the list.<br>3. Click &quot;Add Items&quot;.</td><td class="s7"></td><td class="s5">Selected items are displayed in the Items Table section.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s5">1273-040</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Cancel Button – No Items Added</span></td><td class="s7">High</td><td class="s5">Validate Cancel Button – No Items Added</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Inside the modal, select one or more items.<br>2. Click Cancel instead of Add.</td><td class="s7"></td><td class="s5">No items are added to the Items Table.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s5">1273-041</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Input Quantity per Item</span></td><td class="s7">High</td><td class="s5">Validate Input Quantity per Item</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. In the Items Table, enter a quantity for each listed item.</td><td class="s7"></td><td class="s5">Quantity input accepts valid values (e.g., positive integers).<br><br>Input field is editable.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 64px"><th id="1824309636R47" style="height: 64px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 64px">48</div></th><td class="s5">1273-042</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Display Remaining GFQ</span></td><td class="s7">Minor</td><td class="s5">Validate Display Remaining GFQ (Goods for Quotation)</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. After entering quantity, check for Remaining GFQ value.</td><td class="s7"></td><td class="s5">System displays the correct remaining GFQ per item based on available quantity.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s5">1273-043</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Filtering of RS Fields - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Remove Individual Item</span></td><td class="s7">High</td><td class="s5">Validate Remove Individual Item</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. In the Items Table, click the remove/delete icon for a specific item.</td><td class="s7"></td><td class="s5">The item is removed from the table.<br><br>Total or related values update accordingly.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 22px"><th id="1824309636R49" style="height: 22px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 22px">50</div></th><td class="s14 softmerge"><div class="softmerge-inner" style="width:293px;left:-1px"> [RS Creation] - Update Charge To Function </div></td><td class="s20"></td><td class="s20"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s4"></td></tr><tr style="height: 60px"><th id="1824309636R50" style="height: 60px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 60px">51</div></th><td class="s5">1273-044</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Set Charge To Fields as Optional</span></td><td class="s7">Critical</td><td class="s5">Validate Set Charge To Fields as Optional</td><td class="s5">1. User has successfully logged in to their Account</td><td class="s5">1. Open the requisition slip form.<br>2. Do not fill in the Charge To field.<br>3. Save as a draft.</td><td class="s7"></td><td class="s5">Form saves successfully without requiring the Charge To field.</td><td class="s18" rowspan="18"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********">https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********</a></td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 69px"><th id="1824309636R51" style="height: 69px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 69px">52</div></th><td class="s5">1273-045</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Allow Selection of Different Charge To Based on Situation</span></td><td class="s7">Critical</td><td class="s5">Validate Allow Selection of Different Charge To Based on Situation</td><td class="s5">1. User has successfully logged in to their Account</td><td class="s5">1. Open a new requisition slip.<br>2. Select different categories (Company, Association, Project, Supplier).<br>3. For each category, check the available options in the Charge To dropdown.</td><td class="s7"></td><td class="s5">Charge To options change appropriately based on selected category.</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 69px"><th id="1824309636R52" style="height: 69px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 69px">53</div></th><td class="s5">1273-046</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Category selected as Company, but No Company in Request Details</span></td><td class="s7">High</td><td class="s5">Validate Category selected as Company, but No Company in Request Details</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select Company category.<br>2. No company is selected in Request Details.<br>3. Click submit.</td><td class="s7"></td><td class="s5">Error Message to set company in Request Details</td><td class="s9">Failed</td><td class="s7">4/ 25 No error message appeared<br>Unable to click Submit button</td><td class="s23 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636">4/28<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636</a></div></td></tr><tr style="height: 72px"><th id="1824309636R53" style="height: 72px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 72px">54</div></th><td class="s5">1273-047</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Category selected as Association, but Request Details is Missing Association</span></td><td class="s7">High</td><td class="s5">Validate Category selected as Association, but Request Details is Missing Association</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select Association category.<br>2. No company is selected in Request Details.<br>3. Click submit.</td><td class="s7"></td><td class="s5">Error Message to set Associationin Request Details</td><td class="s9">Failed</td><td class="s7">4/ 25 No error message appeared<br>Unable to click Submit button</td><td class="s23 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636">4/28<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636</a></div></td></tr><tr style="height: 64px"><th id="1824309636R54" style="height: 64px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 64px">55</div></th><td class="s5">1273-048</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -</span><span style="font-family:Arial;font-weight:normal;color:#000000;">  Category selected as Project, but No Project in Request Details</span></td><td class="s7">High</td><td class="s5">Validate Category selected as Project, but No Project in Request Details</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select Project category.<br>2. No company is selected in Request Details.<br>3. Click submit.</td><td class="s7"></td><td class="s5">Error Message to set Project in Request Details</td><td class="s9">Failed</td><td class="s7">4/ 25 No error message appeared<br>Unable to click Submit button</td><td class="s23 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636">4/28<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636</a></div></td></tr><tr style="height: 19px"><th id="1824309636R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s5">1273-049</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function - </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Category selected as Company, but No Supplier is Selected</span></td><td class="s7">High</td><td class="s5">Validate Category selected as Company, but No Supplier is Selected</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select Supplier category.<br>2. No company is selected in Request Details.<br>3. Click submit.</td><td class="s7"></td><td class="s5">Error Message to set Supplier in Request Details</td><td class="s9">Failed</td><td class="s7">4/ 25 No error message appeared<br>Unable to click Submit button</td><td class="s23 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636">4/28<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1636</a></div></td></tr><tr style="height: 65px"><th id="1824309636R56" style="height: 65px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 65px">57</div></th><td class="s5">1273-050</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -   </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Behavior of Charge To Based on Category</span></td><td class="s7">High</td><td class="s5">Validate Behavior of Charge To Based on Category</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select Company as the category.<br>2. Submit the requisition slip.<br>3. Open the requisition in the Approval view.</td><td class="s7"></td><td class="s5">Charge To field auto-populates with the selected company from Request Details.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 69px"><th id="1824309636R57" style="height: 69px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 69px">58</div></th><td class="s5">1273-051</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -   </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Filter Charge To Options Based on Selected Category</span></td><td class="s7">High</td><td class="s5">Validate Filter Charge To Options Based on Selected Category</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Select each Charge To (Category).<br>2. Observe the options shown in the Charge To dropdown.</td><td class="s7"></td><td class="s5">Only valid options matching the category are displayed.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 67px"><th id="1824309636R58" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">59</div></th><td class="s5">1273-052</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function - </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Rename &quot;Charge To (Client)&quot; to &quot;Charge To&quot;</span></td><td class="s7">Minor</td><td class="s5">Rename &quot;Charge To (Client)&quot; to &quot;Charge To&quot;</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Open the requisition slip form.<br>2. Look for the &quot;Charge To&quot; label.</td><td class="s7"></td><td class="s5">The label reads &quot;Charge To&quot; instead of &quot;Charge To (Client)&quot;.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 67px"><th id="1824309636R59" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">60</div></th><td class="s5">1273-053</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Behavior in Draft View</span></td><td class="s7">High</td><td class="s5">Validate Behavior in Draft View</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Save a requisition slip as a draft with all Charge To fields filled.<br>2. Reopen the draft.</td><td class="s7"></td><td class="s5">All Charge To values persist and behave as expected based on their category.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 60px"><th id="1824309636R60" style="height: 60px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 60px">61</div></th><td class="s5">1273-054</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Behavior in Approver&#39;s View</span></td><td class="s7">High</td><td class="s5">Validate Behavior in Approver&#39;s View</td><td class="s5">1. Requisition slip is already created. </td><td class="s5">1. Log in as an approver.<br>2. Open the requisition.</td><td class="s7"></td><td class="s5">Charge To behavior is consistent with user selection and category logic.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s5">1273-055</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Requisition Slip Creation with Enlarged Add Items Modal </span></td><td class="s7">Minor</td><td class="s5">Validate Requisition Slip Creation with Enlarged Add Items Modal</td><td class="s5">1. User is logged in and has permission to create a Requisition Slip<br>2. User has access to the Requisition Slip form page</td><td class="s5">1. Navigate to the Requisition Slip Form<br>2. Click &quot;Create New Requisition Slip&quot;<br>3. Fill in the Request Details section (e.g., Request Type, Project, etc.)<br>4. Click the Add Items button<br>5. Observe the size of the modal</td><td class="s7"></td><td class="s5">Modal should be larger than the standard/default modal size, providing ample space for displaying item lists</td><td class="s9">Failed</td><td class="s7">4/25 Modal Size is not larger than<br>the standard modal size, or does<br>not match Modal design in Figma</td><td class="s23 softmerge"><div class="softmerge-inner" style="width:345px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1293">4/25<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1293</a></div></td></tr><tr style="height: 19px"><th id="1824309636R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s5">1273-056</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -</span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Items Table Responsive Spacing in Requisition Slip View</span></td><td class="s7">Minor</td><td class="s5">Validate Items Table Responsive Spacing in Requisition Slip View</td><td class="s5">1. Requisition Slip has been saved (either as Draft or Submitted)<br>2. The slip includes at least one item in the Items Table</td><td class="s5">1. Open a Draft or Submitted Requisition Slip<br>2. Observe the Items Table section<br>3. View a slip with multiple items (e.g., 5+)<br>4. Resize the viewport or test on different screen sizes</td><td class="s7"></td><td class="s5">Table should remain readable and not overflow or create scroll issues unnecessarily</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s5">1273-057</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Account Code column is no longer visible</span></td><td class="s7">Minor</td><td class="s5">Validate Account Code column is no longer visible</td><td class="s5">1. User has access to view a Requisition Slip (Draft or Submitted)<br><br>2. At least one item exists in the Items Table</td><td class="s5">1. Open any Requisition Slip with items listed<br>2. Observe the Items Table Columns<br>3. Locate the Item Column</td><td class="s7"></td><td class="s5">The Account Code column is no longer visible. <br><br>Should display the Full Item name in the Item Column<br>    a. Item Name<br>    b. Unit<br>    c. Remaining GFQ<br>    d. Quantity<br>    e. Notes<br>    f. Actions<br>        i. Remove</td><td class="s9">Failed</td><td class="s7">4/25 Full Item name is not <br>displayed<br></td><td class="s23 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1542">4/25<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1542</a></div></td></tr><tr style="height: 90px"><th id="1824309636R64" style="height: 90px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 90px">65</div></th><td class="s5">1273-058</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Character Limit for Notes</span></td><td class="s7">Minor</td><td class="s5">Validate Character Limit for Notes</td><td class="s5">1. Requisition Slip is created<br>2. Items are added to the requisition.</td><td class="s5">1. Enter text in the item notes field.<br>2. Confirm that up to 500 characters are accepted.<br>3. Attempt to enter more than 500 characters.</td><td class="s7"></td><td class="s5">Notes accept up to 500 characters.</td><td class="s9">Failed</td><td class="s7">4/25 Items Table notes only <br>accepts 100 characters instead<br>of 500 characters</td><td class="s23 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1544">4/25<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1544</a></div></td></tr><tr style="height: 91px"><th id="1824309636R65" style="height: 91px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 91px">66</div></th><td class="s5">1273-059</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Requisition Slip Submit Without Any Notes</span></td><td class="s7">High</td><td class="s5">Validate Requisition Slip Submit Without Any Notes</td><td class="s5">1. Requisition Slip is created<br>2.  Items are added without any notes.</td><td class="s5">1. Leave all notes fields empty.<br>2. Submit the Requisition Slip.</td><td class="s7"></td><td class="s5">The system allows submission without any item notes.</td><td class="s11">Passed</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 60px"><th id="1824309636R66" style="height: 60px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 60px">67</div></th><td class="s5">1273-060</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Status Update from Submitted to For Approval</span></td><td class="s7">High</td><td class="s5">Validate Status Update from Submitted to For Approval</td><td class="s5">1. A Requisition Slip is in Submitted status.</td><td class="s5">1. Submit a Requisition Slip.<br>2. Wait for system rules to process the submission.</td><td class="s7"></td><td class="s5">Status is updated from Submitted to For Approval.</td><td class="s24">Out of Scope</td><td class="s7">4/25 Status is &quot;Submitted&quot; instead<br>of &quot;For RS Approval&quot;</td><td class="s7"></td></tr><tr style="height: 63px"><th id="1824309636R67" style="height: 63px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 63px">68</div></th><td class="s5">1273-061</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;">[RS Creation] - Update Charge To Function -  </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Approvers Are Assigned When RS is in For Approval Status</span></td><td class="s7">High</td><td class="s5">Validate Approvers Are Assigned When RS is in For Approval Status</td><td class="s5">1. Requisition Slip is in For Approval status.</td><td class="s5">1. View the RS with “For Approval” status.<br>2. Check the assigned approvers or approval flow.</td><td class="s7"></td><td class="s5">For Approval Status should now be the basis for the Approvers of the RS Approval</td><td class="s24">Out of Scope</td><td class="s7">4/25 No Status of &quot;For RS <br>Approval&quot; displayed</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1824309636R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s14 softmerge"><div class="softmerge-inner" style="width:358px;left:-1px"> [RS Creation] - Item UI and Function Updates</div></td><td class="s25"></td><td class="s20"></td><td class="s20"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1824309636R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s5">1273-062</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] - Item UI and Function Updates -</span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Search Field is Not Displayed</span></td><td class="s7">Minor</td><td class="s5">Verify Search Field is Not Displayed</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Scroll to the Items Table section.<br>2. Inspect the table header and top controls.</td><td class="s7"></td><td class="s5">No Search Field is visible.<br><br>Table functionality remains unaffected.</td><td class="s26" rowspan="3"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********">Cherry_Sprint1_Test Result</a></td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 19px"><th id="1824309636R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s5">1273-063</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] - Item UI and Function Updates - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Scroll and Layout in Draft View</span></td><td class="s7">High</td><td class="s5">Verify Scroll and Layout in Draft View</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Fill out the requisition and save as draft.<br>2. Reopen the draft.<br>3. Scroll to Items Table.</td><td class="s7"></td><td class="s5">Scroll behavior and layout are consistent.<br><br>Search Field is still not present.<br><br>Data in Items Table is retained.</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 19px"><th id="1824309636R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s5">1273-064</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] - Item UI and Function Updates - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Scroll and Section Visibility in Approval View</span></td><td class="s7">Minor</td><td class="s5">Verify Scroll and Section Visibility in Approval View</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Submit a requisition.<br>2. Log in as an approver and open the requisition.<br>3. Scroll to Items Table.</td><td class="s7"></td><td class="s5">Items Table section is visible.<br><br>No Search Field present.<br><br>Table content is read-only but fully accessible.</td><td class="s9">Failed</td><td class="s5">4/23: Minor bug for Items table displayed</td><td class="s19 softmerge"><div class="softmerge-inner" style="width:378px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1533">4/23: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1533</a></div></td></tr><tr style="height: 25px"><th id="1824309636R72" style="height: 25px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 25px">73</div></th><td class="s14 softmerge"><div class="softmerge-inner" style="width:358px;left:-1px">[RS Creation] -Attachments and Notes Placement</div></td><td class="s25"></td><td class="s20"></td><td class="s20"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s4"></td></tr><tr style="height: 95px"><th id="1824309636R73" style="height: 95px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 95px">74</div></th><td class="s5">1273-065</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Section Sequence in Requisition Slip Form</span></td><td class="s7">High</td><td class="s5">Validate Section Sequence in Requisition Slip Form</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Open a new Requisition Slip Form.<br>2. Observe the layout and ordering of the sections.</td><td class="s7"></td><td class="s5">Section order should be:<br>-Request Details<br>-Charge To<br>-Items Table<br>-Attachments and Notes</td><td class="s26" rowspan="7"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1vn7g-88vVxILIRfVbXpC88cFx7xIYSLKH04DGvKn7a0/edit?gid=**********#gid=**********">Cherry_Sprint1_Test Result</a></td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 77px"><th id="1824309636R74" style="height: 77px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 77px">75</div></th><td class="s5">1273-066</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Retain Functionality of Uploading Attachments</span></td><td class="s7">Minor</td><td class="s5">Validate Retain Functionality of Uploading Attachments</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Go to the Attachments and Notes section.<br>2. Upload 1 file with 25 MB in size.</td><td class="s7"></td><td class="s7">File uploads successfully and is visible in the list.</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 77px"><th id="1824309636R75" style="height: 77px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 77px">76</div></th><td class="s5">1273-067</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Retain Functionality of Adding Notes</span></td><td class="s7">Minor</td><td class="s5">Validate Retain Functionality of Adding Notes</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Add Note with 100 characters.<br>2. Save or submit the form.</td><td class="s7"></td><td class="s5">Notes are saved and retained.</td><td class="s9">Failed</td><td class="s7">4/24: Notes limitto 500 characters</td><td class="s19 softmerge"><div class="softmerge-inner" style="width:376px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1522">4/24 https://youtrack.stratpoint.com/issue/CITYLANDPRS-1522</a></div></td></tr><tr style="height: 77px"><th id="1824309636R76" style="height: 77px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 77px">77</div></th><td class="s5">1273-068</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement -</span><span style="font-family:Arial;font-weight:normal;color:#000000;"> Retain Functionality of Remove/Edit Attachments</span></td><td class="s7">Minor</td><td class="s5">Validate Retain Functionality of Remove/Edit Attachments</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Upload a file.<br>2. Remove before clicking submit.</td><td class="s7"></td><td class="s5">Should be able to remove or replace attachments before final submission.</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 77px"><th id="1824309636R77" style="height: 77px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 77px">78</div></th><td class="s5">1273-069</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Retain Functionality of Attachment Type/Size Validation</span></td><td class="s7">Minor</td><td class="s5">Validate Retain Functionality of Attachment Type/Size Validation</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Upload incorrect file type (e.g., .exe) or large file (over 25 MB).</td><td class="s7"></td><td class="s5">Validation message &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV&quot;</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 80px"><th id="1824309636R78" style="height: 80px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 80px">79</div></th><td class="s5">1273-070</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Behavior in Draft View</span></td><td class="s7">High</td><td class="s5">Validate Behavior in Draft View</td><td class="s5">1. User has successfully logged in to their Account<br>2. User is in Requisition Slip Page</td><td class="s5">1. Fill all sections of the form.<br>2. Save as draft.<br>3. Reopen the draft.</td><td class="s7"></td><td class="s5">All data persists correctly.<br><br>Section layout and sequence is intact.</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr><tr style="height: 19px"><th id="1824309636R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s5">1273-071</td><td class="s13"><span style="font-family:Arial;font-weight:bold;color:#000000;"> [RS Creation] -Attachments and Notes Placement - </span><span style="font-family:Arial;font-weight:normal;color:#000000;">Behavior in Approver View – Read-Only but Fully Visible</span></td><td class="s7">High</td><td class="s5">Validate Behavior in Approver View – Read-Only but Fully Visible</td><td class="s5">1. User has successfully logged in to their Account<br></td><td class="s5">1. Submit a requisition.<br>2. Log in as an approver.<br>3. Open the form.</td><td class="s7"></td><td class="s5">All sections (Request Details → Charge To → Items Table → Attachments and Notes) are shown in the same order.<br><br>Attachments can be viewed/downloaded.<br><br>Notes are readable.<br><br>Fields are read-only.</td><td class="s11">Passed</td><td class="s7"></td><td class="s22"></td></tr></tbody></table></div>