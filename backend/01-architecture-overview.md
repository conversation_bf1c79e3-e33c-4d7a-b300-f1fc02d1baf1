# Backend Architecture Overview

## Introduction

This document provides a comprehensive analysis of the PRS-Backend architecture, examining its structure, design patterns, and implementation. The backend is built using Node.js with a well-structured architecture that follows clean architecture principles.

## Architectural Pattern

The PRS-Backend follows a **Clean Architecture** approach with clear separation of concerns across multiple layers:

1. **Interface Layer** - Handles HTTP requests and responses
2. **Application Layer** - Contains business logic and use cases
3. **Domain Layer** - Defines core business entities and rules
4. **Infrastructure Layer** - Implements data access and external services

This layered architecture promotes maintainability, testability, and separation of concerns.

## Directory Structure

The backend codebase is organized into the following main directories:

```
/prs-backend
├── src/
│   ├── app/                  # Application layer
│   │   ├── errors/           # Error handling
│   │   ├── handlers/         # Request handlers
│   │   │   ├── controllers/  # Controllers
│   │   │   ├── middlewares/  # Middleware functions
│   │   ├── services/         # Business logic services
│   │   └── utils/            # Utility functions
│   ├── domain/               # Domain layer
│   │   ├── entities/         # Business entities
│   │   └── constants/        # Business constants
│   ├── infra/                # Infrastructure layer
│   │   ├── database/         # Database access
│   │   │   ├── models/       # Sequelize models
│   │   └── repositories/     # Data access repositories
│   ├── interfaces/           # Interface layer
│   │   └── router/           # API routes
│   │       ├── private/      # Authenticated routes
│   │       └── public/       # Public routes
│   ├── container.js          # Dependency injection container
│   └── index.js              # Application entry point
├── test/                     # Test directory
└── package.json              # Project dependencies
```

## Key Components

### 1. Dependency Injection

The backend uses a dependency injection pattern implemented through the `container.js` file. This approach:

- Decouples component creation from usage
- Facilitates testing through dependency mocking
- Centralizes dependency management
- Simplifies component replacement

```javascript
// Example from container.js
const awilix = require('awilix');
const container = awilix.createContainer();

container.register({
  // Registering services
  authService: awilix.asClass(AuthService).singleton(),
  userService: awilix.asClass(UserService).singleton(),
  
  // Registering repositories
  userRepository: awilix.asClass(UserRepository).singleton(),
  
  // Registering controllers
  userController: awilix.asClass(UserController).singleton(),
});
```

### 2. Routing System

The routing system is organized into public and private routes, with a clear separation of authentication requirements:

```javascript
// Example from interfaces/router/index.js
const privateRoutes = require('./private');
const publicRoutes = require('./public');

const routesDefinitions = async (serverContext) => {
  serverContext.addHook('onRoute', (routeOptions) => {
    routeOptions.validatorCompiler = (data) =>
      utils.validatorCompiler(data.schema);
  });

  serverContext.addHook('onSend', serverContext.auditLogs);

  await publicRoutes(serverContext);
  await privateRoutes(serverContext);
};
```

Private routes require authentication:

```javascript
// Example from interfaces/router/private/index.js
const privateRoutes = async (serverContext) => {
  serverContext.register((instance, _opts, next) => {
    instance.addHook('onRequest', serverContext.authenticate);

    serverContext.register(userRoutes, {
      prefix: 'v1/users',
    });
    
    // Other route registrations...
    
    next();
  });
};
```

### 3. Database Layer

The database layer uses Sequelize ORM with well-defined models and associations:

```javascript
// Example from infra/database/models/projectApprovalModel.js
module.exports = (sequelize, Sequelize) => {
  const ProjectApprovalModel = sequelize.define(
    'project_approvals',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'project_id',
      },
      // Other fields...
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  ProjectApprovalModel.associate = (models) => {
    ProjectApprovalModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
      targetKey: 'id',
    });

    ProjectApprovalModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      as: 'project',
    });
  };

  return ProjectApprovalModel;
};
```

### 4. Repository Pattern

The backend implements the repository pattern to abstract data access:

```javascript
// Example from infra/repositories/purchaseOrderItemRepository.js
class PurchaseOrderItemRepository extends BaseRepository {
  constructor({ db, steelbarsRepository }) {
    super(db.purchaseOrderItemModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.steelbarsRepository = steelbarsRepository;
  }

  async getPOItemsById(payload) {
    // Implementation details...
  }

  async getPOItemsSummary(payload) {
    // Implementation details...
  }
}
```

### 5. Error Handling

The backend implements a centralized error handling system:

```javascript
// Example from app/errors/errorHandler.js
const errorhandler = function (error, _request, reply) {
  const errorType =
    error instanceof Sequelize.Error
      ? 'DATABASE ERROR'
      : error?.errorCode || 'INTERNAL SERVER ERROR';

  this.log.error({
    errorType,
    'x-request-id': _request.id,
    message: error?.message,
    stack: error?.stack,
    timestamp: new Date().toISOString(),
  });

  if (error instanceof HttpError) {
    return replyHttpError(error, reply);
  }

  return replyHttpError(serverErrors.INTERNAL_SERVER_ERROR(), reply);
};
```

With standardized error classes:

```javascript
// Example from app/errors/httpError.js
class HttpError {
  constructor({ status, message, description, errorCode }) {
    this.status = status;
    this.message = message;
    this.description = description;
    this.errorCode = errorCode;
    this.timestamp = new Date().toISOString();
  }
}
```

### 6. Validation

The backend uses Zod for request validation:

```javascript
// Example from app/utils/index.js
const validatorCompiler = (schema) => {
  return (data) => {
    const result = schema.safeParse(data);

    if (!result?.success) {
      const errorDescription = result?.error?.issues?.reduce(
        (issues, current) => {
          issues[current.path[0] ?? 'error'] = current.message;
          return issues;
        },
        {},
      );

      throw clientErrors.VALIDATION_ERROR({
        message: result?.error?.issues[0]?.message,
        description: JSON.stringify(errorDescription),
      });
    }

    return data;
  };
};
```

## Framework and Libraries

The backend uses the following key technologies:

1. **Node.js** - JavaScript runtime
2. **Fastify** - Web framework
3. **Sequelize** - ORM for database access
4. **Awilix** - Dependency injection container
5. **Zod** - Schema validation
6. **PostgreSQL** - Database (inferred from Sequelize usage)

## Authentication and Authorization

The backend implements a token-based authentication system:

```javascript
// Example from app/handlers/middlewares/authenticate.js
const authenticate = async function (request, reply) {
  try {
    const authHeader = request.headers.authorization;
    
    if (!authHeader) {
      throw clientErrors.UNAUTHORIZED();
    }
    
    const token = authHeader.split(' ')[1];
    const decoded = await this.diScope.resolve('authService').verifyToken(token);
    
    request.user = decoded;
  } catch (error) {
    throw clientErrors.UNAUTHORIZED();
  }
};
```

And role-based authorization:

```javascript
// Example from app/handlers/middlewares/authorize.js
const authorize = (permission) => {
  return async (request) => {
    const { user } = request;
    
    if (!user) {
      throw clientErrors.UNAUTHORIZED();
    }
    
    const hasPermission = await checkPermission(user, permission);
    
    if (!hasPermission) {
      throw clientErrors.FORBIDDEN();
    }
    
    return true;
  };
};
```

## Architectural Strengths

1. **Clean Separation of Concerns** - Clear boundaries between layers
2. **Dependency Injection** - Loose coupling between components
3. **Repository Pattern** - Abstraction of data access
4. **Centralized Error Handling** - Consistent error responses
5. **Schema Validation** - Input validation using Zod
6. **Versioned API** - Support for API versioning (v1, v2)

## Architectural Challenges

1. **Complex Query Construction** - Some repositories contain complex SQL queries
2. **Limited Test Coverage** - Minimal test files observed
3. **Mixed Versioning Approach** - Some routes use v1, others v2
4. **Complex Business Logic** - Some services contain complex business rules

## Conclusion

The PRS-Backend follows a well-structured clean architecture approach with clear separation of concerns. The use of dependency injection, repository pattern, and centralized error handling demonstrates a mature architectural design. The codebase shows evidence of thoughtful organization and adherence to software engineering principles.

The architecture supports the complex business requirements of the purchase requisition system while maintaining maintainability and extensibility. Areas for improvement include increasing test coverage, refactoring complex queries, and standardizing the API versioning approach.
