# Company Management Workflow

This document outlines the complete workflow for managing companies and associations in the PRS system, including syncing from external sources and manual creation.

## Workflow Diagram

```mermaid
flowchart TD
    Start([Company Management]) --> A[Company Landing Page]
    
    A --> C{User Role}
    
    C -->|Purchasing Admin/IT Admin| D[Sync Companies]
    C -->|Purchasing Admin/IT Admin| E[Create Association]
    C -->|Purchasing Admin/IT Admin| F[Manage Associations]
    
    D --> G[Pull from Master File]
    G --> H[Update Company List]
    
    E --> I[Fill Association Details]
    I --> J[Add Projects to Association]
    J --> K[Submit Association]
    
    F --> L{Association Action}
    L -->|View| M[View Association Details]
    L -->|Edit| N[Edit Association]
    L -->|Delete| O[Delete Association]
    
    M --> P[Add/Remove Projects]
    P --> M
    
    N --> Q[Update Association Details]
    Q --> R[Update Projects]
    
    O --> S{Has Open RS?}
    S -->|Yes| T[Cannot Delete]
    S -->|No| U[Confirm Deletion]
```

## Entity Types

### Companies
- **Source**: Synced from Cityland's Company Master File
- **Management**: Read-only for most users, sync capability for Purchasing admin and IT admin
- **Identifier**: Tagged as "Company" in the system

### Associations
- **Source**: Manually created by Purchasing admin and IT admin
- **Management**: Full CRUD operations available to IT Admin
- **Identifier**: Tagged as "Association" in the system

## User Roles and Permissions

| Role | View | Sync Companies | Create Association | Edit Association | Delete Association | Edit Company Address/Contact |
|------|------|----------------|-------------------|------------------|-------------------|----------------------------|
| All Users | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ |
| Purchasing Staff | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ |
| IT Admin | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Purchasing Admin | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |

## Detailed Workflow Steps

### 1. Company Landing Page Access

**Actor**: All Users

**Actions**:

- Access Company menu from navigation
- View unified list of companies and associations

**Features**:

- Search functionality by company/association name
- Sortable columns (Company Code, Name, Initials, TIN, Address, Contact Number)
- Pagination (10 rows per page)
- Default sorting: Alphabetical by Company/Association Name (A-Z)

### 2. Company Data Synchronization

**Actor**: Purchasing Admin and IT Admin

**Prerequisites**:

- Cityland API integration setup

**Actions**:

- Click "Sync Company List" button
- System pulls data from Cityland's Legacy System
- Updates existing companies or adds new ones

**Synchronized Data**:

- Company Code
- Company Name
- Company Initials
- TIN
- Company Address
- Contact Number

**Business Rules**:

- Adds companies not yet in the system
- Updates existing company details from master file
- Updates filter and form options across the system
- Display last sync date and time
- Button is disabled during sync process
- Returns to page 1 after synchronization
- Synced data cannot be edited

### 3. View Company Details

**Actor**: All Users

**Actions**:

- Click on company/association name link
- View detailed information
- Companies cannot be edited
- Project list table with sorting capability
- Projects sorted by Project Name (0-9, A-Z)
- 10 rows per page for project list

### 4. Association Management

#### 4.1 Create Association

**Actor**: IT Admin, Purchasing Admin

**Actions**:

- Access creation form from Company landing page
- Fill association details
- Add projects to association (optional)
- Submit for creation

**Required Fields**:

- Association Name (alphanumeric + special characters, max 100 chars)
- Association Code (alphanumeric only, max 20 chars)
- Association Initials (alphanumeric only, max 20 chars)
- TIN (numbers only, max 20 chars)
- Address (alphanumeric + special characters, max 100 chars)
- Contact Number (numbers only, +63 placeholder, max 13 chars including +63)
- Area (dropdown selection)

**Project Linking**:

- Optional during creation
- Can add multiple projects via "Add New Project" button
- Project selection from dropdown (combobox with search)
- Auto-populate Project Code, Initials, Company, Address based on selection
- Remove projects before submission

#### 4.2 Edit Association

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**:

- Association exists in system
- User has edit permissions

**Actions**:

- Access via Edit button in Actions column or View Association modal
- Modify association details
- Update project associations
- Submit changes

**Validation**:

- Field validation on submit
- Error highlighting and messages for invalid fields
- Confirmation modal before saving changes

#### 4.3 Delete Association

**Actor**: IT Admin

**Prerequisites**:

- Association exists in system
- IT Admin permissions

**Business Rules**:

- Cannot delete if association has open requisitions
- Confirmation modal required
- If deletion blocked, display informative message
- Successful deletion removes association from system

#### 4.4 Association Project Linking

**Actor**: IT Admin, Purchasing Admin

**Actions**:

- Add projects to existing associations
- Remove projects from associations
- View project details within association context

**Add Project Process**:

1. Click "Add New Project" button
2. Select project from filtered dropdown (excludes already linked projects)
3. Review auto-populated project details
4. Confirm addition to project list

**Remove Project Process**:

1. Click Remove icon in project list
2. Confirm removal via modal
3. Project becomes available for re-linking

**Project List Features**:

- Sortable by all columns
- Pagination for large project lists
- Search within dropdown selections

## Business Rules Summary

### Company Rules
- Companies are synced from external master file
- Details or non-editable and is read-only
- Companies cannot be deleted manually

### Association Rules
- Associations are manually created and managed
- Full CRUD operations available to authorized users
- Must have unique codes and names
- Cannot be deleted if linked to open requisitions
- Projects can be linked/unlinked flexibly

### General Rules
- All users can view company/association information
- Sorting and pagination available on all list views
- Form validation prevents invalid data entry
- Confirmation modals required for destructive actions
- Success/error messaging provides user feedback

## Example Scenarios

### Scenario 1: Routine Company Sync

1. Purchasing Admin accesses Company Management
2. Clicks "Sync" button
3. System connects to Cityland Legacy System and updates/adds companies
4. Sync completion message displays with timestamp
5. Updated company list refreshes to page 1

### Scenario 2: Create New Association with Projects

1. Purchasing Admin accesses Company Management
2. Clicks "Create Association" button
3. Fills all required association details
4. Adds multiple projects using project selector
5. Reviews project list and removes one project
6. Submits association for creation
7. Success message displays and returns to company list

### Scenario 3: Edit Association Contact Information

1. Purchasing Staff views association details
2. Clicks Edit button for association
3. Updates association address and contact number
4. Confirms changes via modal
5. Success toast displays and modal closes
6. Updated information reflects in company list

### Scenario 4: Attempt to Delete Association with Open Requisitions

1. IT Admin views association details
2. Clicks Delete button
3. System checks for open requisitions
4. Deletion blocked message displays
5. User returns to association view without deletion

## Data Flow Integration

### Incoming Data
- Cityland Legacy System → Company Management Data
- User input → Association data

### Outgoing Data
- Company/Association data → Requisition forms
- Company/Association data → Filter options throughout system
- Project associations → Project-based requisition filtering

## Common Issues and Solutions

### Issue 1: Sync Button Not Responding

**Cause**: Network connectivity or master file access issues

**Solution**:

- Check network connection
- Verify master file system availability
- Contact system administrator if issue persists

### Issue 2: Cannot Create Association

**Cause**: Validation errors or duplicate codes/names

**Solution**:

- Review all field validation messages
- Ensure unique association code and name
- Check character limits and allowed characters

### Issue 3: Project Not Available for Linking

**Cause**: Project already linked to association or not synced

**Solution**:

- Verify project isn't already in association's project list
- Check if project exists in Project Management system
- Perform project sync if necessary