<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fff2cc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d0e0e3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fff2cc;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="537489688C0" style="width:103px;" class="column-headers-background">A</th><th id="537489688C1" style="width:165px;" class="column-headers-background">B</th><th id="537489688C2" style="width:252px;" class="column-headers-background">C</th><th id="537489688C3" style="width:324px;" class="column-headers-background">D</th><th id="537489688C4" style="width:362px;" class="column-headers-background">E</th><th id="537489688C5" style="width:378px;" class="column-headers-background">F</th><th id="537489688C6" style="width:184px;" class="column-headers-background">G</th><th id="537489688C7" style="width:336px;" class="column-headers-background">H</th><th id="537489688C8" style="width:100px;" class="column-headers-background">I</th><th id="537489688C9" style="width:88px;" class="column-headers-background">J</th><th id="537489688C10" style="width:159px;" class="column-headers-background">K</th><th id="537489688C11" style="width:159px;" class="column-headers-background">L</th><th id="537489688C12" style="width:159px;" class="column-headers-background">M</th><th id="537489688C13" style="width:159px;" class="column-headers-background">N</th><th id="537489688C14" style="width:274px;" class="column-headers-background">O</th><th id="537489688C15" style="width:286px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="537489688R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="537489688R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-015 - [Requisition Slip] - Updating of Requisition Slip during Approval, Cancel created RS, Update RS Number Format for RS Draft</td></tr><tr style="height: 19px"><th id="537489688R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5">PRS-015-013</td><td class="s6"><a target="_blank" href="https://www.figma.com/design/HJlEuwYiUHhWKazqTKmqP0/Cityland---PRS-(Desktop)?node-id=6073-137342&amp;node-type=frame&amp;t=MgVm5tNkV7JjoPpB-0">https://www.figma.com/design/HJlEuwYiUHhWKazqTKmqP0/Cityland---PRS-(Desktop)?node-id=6073-137342&amp;node-type=frame&amp;t=MgVm5tNkV7JjoPpB-0</a></td><td class="s5"></td><td class="s5">Verify that the “Cancel Request” button is displayed on the RS page.</td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Go to Dashboard<br>2. Open the RS page.<br>3. Check the bottom part of the page.</td><td class="s5"></td><td class="s5">1. The “Cancel Request” button is displayed at the bottom part of the RS page.</td><td class="s7" rowspan="25"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=0#gid=0">https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=0#gid=0</a></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">PRS-015-014</td><td class="s11">Cancel Created RS</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">Verify that the cancellation is allowed when the RS or Item status is “</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">For PO Review”</span><span style="font-family:Poppins,Arial;color:#000000;">.</span></td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Go to Dashboard<br>2. Open an RS with status “For PO Review”.<br>3. Click the “Cancel Request” button.<br>4. Confirm cancellation.</td><td class="s5"></td><td class="s5">1. The RS is successfully canceled, and its status is updated to “Cancelled”.</td><td class="s12">Out of Scope</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">[feb 28] 05AA00000224 from PO to canceeld<br>Out of Scope - PO Review Status not yet implemented<br></td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">PRS-015-015</td><td class="s13">Cancel Created RS</td><td class="s14"></td><td class="s14"><span style="font-family:Poppins,Arial;color:#cc0000;">Verify that the cancellation is not allowed if the RS or Item status</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#cc0000;"> is not “For PO Review”.</span></td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Go to Dashboard<br>2. Open an RS with status “Approved” or any subsequent status.<br>3. Check if the “Cancel Request” button is enabled.</td><td class="s5"></td><td class="s5">1. The “Cancel Request” button is either hidden or disabled for statuses beyond “For PO Review”.</td><td class="s12">Out of Scope</td><td class="s15">Failed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s16">Out of Scope - PO Review Status not yet implemented</td><td class="s5">CITYLANDPRS-1064 [QA BUGS][RS Approval] Able to Cancel Request After &quot;For PO Review&quot;</td></tr><tr style="height: 19px"><th id="537489688R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s5">PRS-015-016</td><td class="s11">Cancel Created RS</td><td class="s5"></td><td class="s5">Verify that a confirmation modal is displayed upon clicking the “Cancel Request” button.</td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Open an RS with status “For PO Review”.<br>2. Click the “Cancel Request” button.</td><td class="s5"></td><td class="s5">1. A confirmation modal is displayed asking the user to confirm the cancellation.</td><td class="s12">Out of Scope</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s16">Out of Scope - PO Review Status not yet implemented</td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s5">PRS-015-017</td><td class="s11">Cancel Created RS</td><td class="s5"></td><td class="s5">Verify that the RS status is updated to “Cancelled” upon confirmation of the cancellation.</td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Open an RS with status “For PO Review”.<br>2. Click the “Cancel Request” button.<br>3. Confirm the cancellation in the modal.</td><td class="s5"></td><td class="s5">1. The RS status is updated to “Cancelled”.</td><td class="s12">Out of Scope</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s16">Out of Scope - PO Review Status not yet implemented</td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s5">PRS-015-018</td><td class="s11">Cancel Created RS</td><td class="s5"></td><td class="s5">Verify that the RS does not proceed to any further processes once it is canceled.</td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Open an RS with status “Cancelled”.<br>2. Attempt to process the RS further (e.g., approve or review).</td><td class="s5"></td><td class="s5">The system prevents any further actions on the canceled RS.</td><td class="s12">Out of Scope</td><td class="s15">Failed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Feb 28 - able to approve or reject even if the status is cancelled<br>CITYLANDPRS-977 <br><br></span><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:line-through;color:#000000;">Out of Scope - PO Review Status not yet implemented</span></td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-977">CITYLANDPRS-977</a></td></tr><tr style="height: 19px"><th id="537489688R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s5">PRS-015-019</td><td class="s11">Cancel Created RS</td><td class="s5"></td><td class="s5">Verify that the requested quantity is returned to the Remaining GFQ if the request is an OFM Request.</td><td class="s5">1. A Requisition Slip has been created</td><td class="s5">1. Open an OFM Request RS with status “For PO Review”.<br>2. Cancel the RS.<br>3. Check the Remaining GFQ.</td><td class="s5"></td><td class="s5">1. The requested quantity is returned to the Remaining GFQ.</td><td class="s12">Out of Scope</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Out of Scope - PO Review Status not yet implemented</td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">PRS-015-020</td><td class="s18">Update RS Number Format for RS Draft</td><td class="s5"></td><td class="s5">Verify that the Requisition Slip can be saved as a draft.</td><td class="s5">1. Requisition Slip Form has been filled up</td><td class="s5">1. Fill up the Requisition Slip form.<br>2. Save the Requisition Slip as a draft.</td><td class="s5"></td><td class="s5">1. The RS is saved as a draft, and a temporary RS number is assigned.</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">PRS-015-021</td><td class="s18">Update RS Number Format for RS Draft</td><td class="s5"></td><td class="s5">Verify that a temporary RS number is generated when saving as a draft.</td><td class="s5">1. Requisition Slip Form has been filled up</td><td class="s5">1. Fill up the Requisition Slip form.<br>2. Save the Requisition Slip as a draft.<br>3. View the generated RS number.</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. The RS is assigned a temporary number with the format </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS-TMP-[Company Code + Alphabet[AA] + 8-incremental digit].</span></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">PRS-015-022</td><td class="s18">Update RS Number Format for RS Draft</td><td class="s5"></td><td class="s5">Verify that the temporary RS number increments correctly for subsequent drafts.</td><td class="s5">1. Requisition Slip Form has been filled up</td><td class="s5">1. Leave mandatory fields blank in the RS form.<br>2. Attempt to save the RS as a draft.</td><td class="s5"></td><td class="s5">1. Each RS draft has a unique, incrementally generated RS number.</td><td class="s8">Passed</td><td class="s8">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">PRS-015-023</td><td class="s18">Update RS Number Format for RS Draft</td><td class="s14"></td><td class="s14">Verify that the RS cannot be saved as a draft if mandatory fields are missing.</td><td class="s5">1. Requisition Slip Form has been filled up</td><td class="s19"></td><td class="s5"></td><td class="s5">1. The RS is not saved, and an error message is displayed indicating the missing fields.</td><td class="s20">Failed</td><td class="s20">Failed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">[feb 28] CITYLANDPRS-736<br>Able to save draft even Delivery To field is blank</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-736">CITYLANDPRS-736</a></td></tr><tr style="height: 19px"><th id="537489688R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">PRS-015-024</td><td class="s18">Update RS Number Format for RS Draft</td><td class="s5"></td><td class="s5">Verify that the temporary RS number format does not allow invalid characters.</td><td class="s5">1. Requisition Slip Form has been filled up</td><td class="s5">1. Fill up the RS form.<br>2. Save the RS as a draft.<br>3. Inspect the RS number for invalid characters.</td><td class="s5"></td><td class="s5">1. The RS number contains only valid characters as per the format RS-TMP-[Company Code + Alphabet[AA] + 8-incremental digit].</td><td class="s8">Passed</td><td class="s8">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="537489688R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">PRS-015-025</td><td class="s18">Update RS Number Format for RS Draft</td><td class="s5"></td><td class="s5">Verify that a draft RS cannot overwrite an existing temporary RS number.</td><td class="s5">1. Requisition Slip Form has been filled up</td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. Save an RS as a draft.<br>2. Create a new RS </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">draft with the same details.<br></span><span style="font-family:Poppins,Arial;color:#000000;">3. Check the RS numbers.</span></td><td class="s5"></td><td class="s5">1. Each draft RS has a unique temporary RS number.</td><td class="s8">Passed</td><td class="s20">Failed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">[Feb 28] duplicate rs draft CITYLANDPRS-951</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-736">CITYLANDPRS-951</a></td></tr><tr style="height: 19px"><th id="537489688R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">PRS-015-026</td><td class="s21"><a target="_blank" href="https://www.figma.com/design/HJlEuwYiUHhWKazqTKmqP0/Cityland---PRS-(Desktop)?node-id=6071-132053&amp;node-type=frame&amp;t=MgVm5tNkV7JjoPpB-0">https://www.figma.com/design/HJlEuwYiUHhWKazqTKmqP0/Cityland---PRS-(Desktop)?node-id=6071-132053&amp;node-type=frame&amp;t=MgVm5tNkV7JjoPpB-0</a></td><td class="s5"></td><td class="s5">Verify Quantity Edit  for Items Table is displayed</td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Navigate to the For My Approval/Sign tab<br>3. Click RS<br>3. Verify the Items Table Quantity column.</td><td class="s22"></td><td class="s5">1. &quot;Quantity&quot; column should be editable in the Items Table when viewed by the Approver.        </td><td class="s20">Failed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">faied due to &quot;Edit&quot; button is not visible</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s5">PRS-015-027</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5">Verify editing of Requested Item Quantity </td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Attempt to edit the quantity of requested items.</td><td class="s22"></td><td class="s5">1. The approver should be able to modify the &quot;Requested Item Quantity&quot; field.        </td><td class="s24">Blocked</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to bug raised</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s25">PRS-015-028</td><td class="s25">Update R.S during approval</td><td class="s26"></td><td class="s26">Verify that only numeric values (up to 3 digits) are accepted for Requested Item Quantity        </td><td class="s25">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s25">1. Log in as an Approver.<br>2. Clickthe &quot;Quantity&quot; field on the Items Table.<br>3. Attempt to input a non-numeric value or a value longer than 3 digits.</td><td class="s27"></td><td class="s25">1. The system should only accept numeric values up to 3 digits for the &quot;Requested Item Quantity&quot; field. <br><br>2. A validation error should occur for non-numeric or longer-than-3-digit values.        </td><td class="s24">Blocked</td><td class="s15">Failed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to bug raised</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. <br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5">PRS-015-029</td><td class="s23">Update R.S during approval</td><td class="s14"></td><td class="s14">Verify Cancel Button functionality        </td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Modify the quantity.<br>4. Click the &quot;Cancel&quot; button.</td><td class="s22"></td><td class="s5">1. A confirmation modal should appear, asking if the Approver wants to cancel the changes.</td><td class="s24">Blocked</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to bug raised</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5">PRS-015-030</td><td class="s23">Update R.S during approval</td><td class="s14"></td><td class="s14">Verify behavior after confirming Cancel Button	</td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Modify the quantity.<br>4. Click the &quot;Cancel&quot; button.<br>5. Confirm the cancellation.</td><td class="s22"></td><td class="s5">1.  No changes should be made to the Requisition Slip, and the previous data should remain intact.</td><td class="s24">Blocked</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to bug raised</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s5">PRS-015-031</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5">Verify Submit Button functionality	</td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Modify the quantity.<br>4. Click the &quot;Submit&quot; button.</td><td class="s5"></td><td class="s5">1. A confirmation modal should appear, asking if the Approver wants to submit the changes.	</td><td class="s24">Blocked</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to bug raised</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s5">PRS-015-032</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5" rowspan="3">Verify behavior after confirming Submit Button        </td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5" rowspan="3">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Modify the quantity.<br>4. Click the &quot;Submit&quot; button.<br>5. Confirm the submission.</td><td class="s5"></td><td class="s5">1. Requisition Slip should be updated with the new quantities. </td><td class="s24">Blocked</td><td class="s24">Blocked</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. </td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s5">PRS-015-033</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5"></td><td class="s5">2.The current Approver and the succeeding Approver should be marked as the ones approving this change. </td><td class="s24">Blocked</td><td class="s24">Blocked</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. </td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s5">PRS-015-034</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5"></td><td class="s5">3. The Requester should receive a notification about the update.</td><td class="s24">Blocked</td><td class="s24">Blocked</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. </td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s5">PRS-015-035</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5">Verify Approver and Succeeding Approver are updated after Submit</td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Modify the quantity.<br>4. Click the &quot;Submit&quot; button.<br>5. Confirm the submission.<br>6. Check the approval workflow for the updated requisition.</td><td class="s5"></td><td class="s5">1. The changes have been approved by both the current Approver and the succeeding Approver.</td><td class="s24">Blocked</td><td class="s24">Blocked</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. </td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s5">PRS-015-036</td><td class="s23">Update R.S during approval</td><td class="s5"></td><td class="s5">Verify Requester notification after Submit Button is clicked	</td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click the &quot;Quantity&quot; field on the Items Table.<br>3. Modify the quantity.<br>4. Click the &quot;Submit&quot; button.<br>5. Confirm the submission.<br>6. Check the Requester&#39;s notification system.</td><td class="s5"></td><td class="s28"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1188870661#gid=1188870661">1. The Requester should receive a notification that the Requisition Slip has been updated by the Approver.<br><br>https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1188870661#gid=1188870661 </a></td><td class="s24">Blocked</td><td class="s24">Blocked</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. </td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr><tr style="height: 19px"><th id="537489688R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s5">PRS-015-037</td><td class="s23">Update R.S during approval</td><td class="s14"></td><td class="s14">Verify system behavior if the Approver does not edit any values and clicks Submit	</td><td class="s5">1. Approvers has been setup<br>2. A Requisition Slip has been created</td><td class="s5">1. Log in as an Approver.<br>2. Click tthe &quot;Quantity&quot; field on the Items Table.<br>3. Do not change any values.<br>4. Click the &quot;Submit&quot; button.</td><td class="s5"></td><td class="s5">1. The system should allow submitting without making any changes, and no updates should be made to the Requisition Slip.</td><td class="s24">Blocked</td><td class="s24">Blocked</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Blocked due to MARHC 3: CITYLANDPRS-986 [QA BUGS][RS Approving] Error 403 After Entering Incorrect Quantity in Items Tab. </td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-728">https://youtrack.stratpoint.com/issue/CITYLANDPRS-728</a></td></tr></tbody></table></div>