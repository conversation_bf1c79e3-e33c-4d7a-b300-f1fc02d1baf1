<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-right:1px SOLID #000000;background-color:#ffd966;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffd966;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#f3f3f3;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffd966;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{background-color:#ffd966;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1727288844C0" style="width:103px;" class="column-headers-background">A</th><th id="1727288844C1" style="width:133px;" class="column-headers-background">B</th><th id="1727288844C2" style="width:252px;" class="column-headers-background">C</th><th id="1727288844C3" style="width:252px;" class="column-headers-background">D</th><th id="1727288844C4" style="width:233px;" class="column-headers-background">E</th><th id="1727288844C5" style="width:389px;" class="column-headers-background">F</th><th id="1727288844C6" style="width:184px;" class="column-headers-background">G</th><th id="1727288844C7" style="width:274px;" class="column-headers-background">H</th><th id="1727288844C8" style="width:100px;" class="column-headers-background">I</th><th id="1727288844C9" style="width:209px;" class="column-headers-background">J</th><th id="1727288844C10" style="width:157px;" class="column-headers-background">K</th><th id="1727288844C11" style="width:157px;" class="column-headers-background">L</th><th id="1727288844C12" style="width:157px;" class="column-headers-background">M</th><th id="1727288844C13" style="width:157px;" class="column-headers-background">N</th><th id="1727288844C14" style="width:157px;" class="column-headers-background">O</th><th id="1727288844C15" style="width:72px;" class="column-headers-background">P</th><th id="1727288844C16" style="width:100px;" class="column-headers-background">Q</th><th id="1727288844C17" style="width:100px;" class="column-headers-background">R</th><th id="1727288844C18" style="width:100px;" class="column-headers-background">S</th><th id="1727288844C19" style="width:100px;" class="column-headers-background">T</th><th id="1727288844C20" style="width:100px;" class="column-headers-background">U</th><th id="1727288844C21" style="width:100px;" class="column-headers-background">V</th><th id="1727288844C22" style="width:100px;" class="column-headers-background">W</th><th id="1727288844C23" style="width:100px;" class="column-headers-background">X</th><th id="1727288844C24" style="width:100px;" class="column-headers-background">Y</th><th id="1727288844C25" style="width:100px;" class="column-headers-background">Z</th><th id="1727288844C26" style="width:100px;" class="column-headers-background">AA</th><th id="1727288844C27" style="width:100px;" class="column-headers-background">AB</th><th id="1727288844C28" style="width:100px;" class="column-headers-background">AC</th></tr></thead><tbody><tr style="height: 42px"><th id="1727288844R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s2">STG_wk1</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1727288844R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5" colspan="16">PRS-010 - [RS Dashboard] -</td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="1727288844R2" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">3</div></th><td class="s6"></td><td class="s7">RS Dashboard</td><td class="s6">Critical</td><td class="s7">Dashboard View for Requester</td><td class="s7"></td><td class="s6">1. Login<br>2. Click Dashboard<br>3. Should be able to view 3 Tabs<br>        a. All<br>        b. For My Approval/Assigned<br>        c. My Requests<br>4. Should be able to view the Data for each Tab<br>        a. All<br>                i. Should contain All Requisition Slips created across PRS<br>        b. For My Approval/Assigned<br>                i. Should contain All Requisition Slips for current user                 approval<br>        c. My Requests Tab<br>                i. Should contain All Requisition Slips the logged in User has created across PRS<br>5. Should have a New Request Button<br>6. Should display Requisition Slip Table Columns per Tab:<br>    a. RS Number<br>        i. Should be Incremental depending on the last created Requisition Slip<br>    b. Type<br>        i. Defined during creation of the Requisition Slip<br>    c. Requestor<br>        i. User that requested the Requisition Slip<br>    d. Company<br>        i. Defined during creation of the Requisition Slip<br>    e. Project<br>        i. Defined during creation of the Requisition Slip<br>    f. Department<br>        i. Defined during creation of the Requisition Slip<br>    g. Date Requested<br>        i. Date the Requisition Slip was submitted<br>        ii. Date Format: DD MMM YYYY (10 Jul 2023)<br>    h. Last Updated<br>        i. Date the Requisition Slip was updated<br>        ii. Date Format: DD MMM YYYY (10 Jul 2023)<br>    i. Status <br>       i. Recent Status of the Requisition Slip (Submitted, Draft, Assigning, Assigned)<br>    j. History button<br>       i. History of the Requisition Slip<br>7. Should have a default sorting of RS Number by Incremental and Decremental<br>8.  Should have show entries dropdown with values of 1, 5, 10<br>9. Should have table items count. Ex: 1 to 1 of 1 items<br>10. Should have pagination, next and previous buttons</td><td class="s7"></td><td class="s6">All fields should be visible on the page</td><td class="s8" rowspan="6"></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s8">history button only visible in admin</td><td class="s8"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="1727288844R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s6"></td><td class="s7">RS Dashboard</td><td class="s6">Critical</td><td class="s6">Dashboard View for Approvers</td><td class="s7"></td><td class="s6">Login &gt; Click Dashboard<br><br>1. Should be able to view two Tabs<br>    a. All Tab<br>    b. For My Approvals/Assigned Tab<br>2. Should display Requisition Slip Table Columns per Tab:<br>    a. RS Number<br>    b. Type<br>    c. Requestor<br>    d. Company<br>    e. Project<br>    f. Department<br>    g. Date Requested<br>        ii. Date Format: DD MMM YYYY (10 Jul 2023)<br>    h. Last Updated<br>        ii. Date Format: DD MMM YYYY (10 Jul 2023)<br>    i. Status<br>       i. Recent Status of the Requisition Slip<br>    j. History<br>3. Should have a default sorting of RS Number by Incremental</td><td class="s7"></td><td class="s6">All fields should be visible on the page</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="1727288844R4" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">5</div></th><td class="s6"></td><td class="s7">RS Dashboard</td><td class="s6">Critical</td><td class="s6">Dashboard View for Requesters that are the same time Approvers</td><td class="s7"></td><td class="s6">Login &gt; Click Dashboard<br><br>1. Should be able to view two Tabs<br>    a. All Tab<br>        i. Should contain All Requisition Slips created across PRS<br>    b. For My Approvals/Assigned Tab<br>        i. Should contain All Requisition Slips that needs my Approval or is Assigned to me<br>    c. My Requests Tab<br>3. Should have a New Request Button<br>4. Should display Requisition Slip Table Columns per Tab:<br>    a. RS Number<br>    b. Type<br>    c. Requestor<br>    d. Company<br>    e. Project<br>    f. Department<br>    g. Date Requested<br>        i. Date the Requisition Slip was submitted<br>        ii. Date Format: DD MMM YYYY (10 Jul 2023)<br>    h. Last Updated<br>        i. Date the Requisition Slip was updated<br>        ii. Date Format: DD MMM YYYY (10 Jul 2023)<br>    i. Status<br>    j. History<br>5. Should have a default sorting of RS Number by Incremental</td><td class="s7"></td><td class="s6">All fields should be visible on the page</td><td class="s9">Passed</td><td class="s11">Blocked</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8">ongoing fix due to show stopeper issue<br><br>wag po muna pindutin yung tabs sa dashboard<br></td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1063">CITYLANDPRS-1063</a></td><td class="s8"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="1727288844R5" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">6</div></th><td class="s8"></td><td class="s13">RS Dashboard</td><td class="s8">Critical</td><td class="s8">Validate search works for &#39;For My Approval/Assigned&#39; tab for exact match and partial text. Should be able to Search per Tab of Data</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1. Login &gt; Click Dashboard &gt;  For My Approval/Assigned tab<br>2. Search for exact match works on<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>3.  Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>4. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>5. Search for partial/contains text works on:<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>6.  Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>7.  Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)</td><td class="s13"></td><td class="s8">Search for exact match and contains/partial should work per column of  &#39;For My Approval/Assigned&#39; tab</td><td class="s11">Blocked</td><td class="s11">Blocked</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8">ongoing fix due to show stopeper issue<br><br>wag po muna pindutin yung tabs sa dashboard<br></td><td class="s14">same issue <br>CITYLANDPRS-796</td><td class="s8"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="1727288844R6" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">7</div></th><td class="s8"></td><td class="s13">RS Dashboard</td><td class="s8">Critical</td><td class="s8">Validate search works for &#39;All&#39; tab for exact match and partial text. Should be able to Search per Tab of Data</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">         <br>1. Login &gt; Click Dashboard &gt; All tab<br>2. Search for exact match works on:<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>3.  Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>4. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>5. Search for partial/contains text works on:<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>6.  Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>7. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br></td><td class="s13"></td><td class="s8">Search for exact match and contains/partial should work per column of  &#39;All&#39; tab</td><td class="s15">Failed</td><td class="s15">Failed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s14">unable to search exact match and search form other page<br>CITYLANDPRS-799</td><td class="s8"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="1727288844R7" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">8</div></th><td class="s8"></td><td class="s13">RS Dashboard</td><td class="s8">Critical</td><td class="s8">Validate search works for &#39;My Requests&#39; tab for exact match and partial text. Should be able to Search per Tab of Data</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">         <br>1. Login &gt; Click Dashboard &gt; My Requests tab<br>2. Search for exact match works on:<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>3.  Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>4. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>5. Search for partial/contains text works on:<br>    a. RS Number<br>    b. Canvass Number<br>    c. PO Number<br>    d. DR Number<br>    e. PR Number<br>    f. Type<br>    g. Requester<br>    h. Company<br>    i. Project/Department<br>    j. Date Requested<br>    k. Last Updated<br>    l. Status<br>6.  Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>7. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br><br></td><td class="s13"></td><td class="s8">Search for exact match and contains/partial should work per column of  &#39;My Requests&#39; tab</td><td class="s15">Failed</td><td class="s11">Blocked</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8">ongoing fix due to show stopeper issue<br><br>wag po muna pindutin yung tabs sa dashboard<br></td><td class="s14">unable to search exact match and search form other page<br>CITYLANDPRS-799</td><td class="s8"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 155px"><th id="1727288844R8" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">9</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">Minor</td><td class="s8">Validate clear button works and resets data for each tab</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1. Login &gt; Click Dashboard<br>2. Search for an exact match for 1 column in &#39;For My Approval/Assigned&#39;<br>3. Table successfully returns the data<br>4. Click Clear Button <br>4. Table will reset and display all the data<br>5. Search for an exact match for 1 column in &#39;All&#39;<br>6. Table successfully returns the data<br>7. Click Clear Button <br>8. Table will reset and display all the data<br>9. Search for an exact match for 1 column in &#39;My Requests&#39;<br>10. Table successfully returns the data<br>11. Click Clear Button <br>12. Table will reset and display all the data</td><td class="s13"></td><td class="s8">Clear button should successfully clear and reset data on the table. All results on the table will return</td><td class="s8" rowspan="6"></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R9" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">10</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">High</td><td class="s8">Validate Search No match data returns &#39;No Items Found&#39;</td><td class="s8"> A Requisition Slip has been created</td><td class="s8"><br>Validate No match data returns &#39;No Items Found&#39;<br>1. Login &gt; Click Dashboard<br>2. Search for a no match for 1 column in &#39;For My Approval/Assigned&#39;<br>3. Table will display &#39;No Items Found&#39; Create a new request to add content<br>4. Search for a no match for 1 column in &#39;All&#39;<br>5. Table will display &#39;No Items Found&#39; Create a new request to add content<br>6. Search for a no match for 1 column in &#39;My Requests&#39;<br>7. Table will display &#39;No Items Found&#39; Create a new request to add content</td><td class="s13"></td><td class="s8">No data matched should successfully return &#39;No Items Found&#39;</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R10" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">11</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">High</td><td class="s8">Validate All Filter exact match works per tab</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Select value for all dropdown<br>    a. Type<br>    b. Requestor<br>    c. Company<br>    d. Department<br>    e. Project<br>    f. Status<br>4. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;All&#39; tab<br>5. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>6. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>7. Click &#39;For My Approval/Assigned&#39; tab<br>8. Click Filter Icon<br>9. Select value for all dropdown<br>    a. Type<br>    b. Requestor<br>    c. Company<br>    d. Department<br>    e. Project<br>    f. Status<br>10. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;For My Approval/Assigned&#39; tab<br>11. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>12. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>13. Click &#39;My Requests&#39; tab<br>14. Click Filter Icon<br>15. Select value for all dropdown<br>    a. Type<br>    b. Requestor<br>    c. Company<br>    d. Department<br>    e. Project<br>    f. Status<br>16. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;My Requests&#39; tab<br>17. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>18. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br></td><td class="s8"><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Filter any existing data:<br><br></span><span style="font-family:Poppins,Arial;color:#000000;">Parameter_1:<br><br>    a. Type - non-ofm<br>    b. Requestor - &lt;any&gt;<br>    c. Company - &lt;any&gt;<br>    d. Department - &lt;any&gt;<br>    e. Project - &lt;any&gt;<br>    f. Status - draft<br><br>Parameter_2:<br><br>    a. Type - non-ofm<br>    b. Requestor - &lt;any&gt;<br>    c. Company - &lt;any&gt;<br>    d. Department - &lt;any&gt;<br>    e. Project - &lt;any&gt;<br>    f. Status - assigning<br><br>Parameter_3:<br><br>    a. Type - &lt;any&gt;<br>    b. Requestor - &lt;any&gt;<br>    c. Company - &lt;any&gt;<br>    d. Department - &lt;any&gt;<br>    e. Project - &lt;any&gt;<br>    f. Status - assigned</span></td><td class="s8">Table should return the EXACT data per TAB based on selected fitler for old and newly added data</td><td class="s15">Failed</td><td class="s15">Failed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s14">filter issue on pagination<br>CITYLANDPRS-799<br>CITYLANDPRS-800 and 801<br>CITYLANDPRS-891</td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R11" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">12</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">High</td><td class="s8">Validate All Filter no match returns &#39;No Items Found&#39; per tab</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Filter a non-existing data<br>4. Table should return &#39;No Items Found&#39; Create a new request to add content<br>5.  Items count should display 0<br>6.  Click on &#39;For My Approval/Assigned&#39; tab then click Filter Icon<br>7. Filter a non-existing data<br>8. Table should return &#39;No Items Found&#39; Create a new request to add content<br>9.  Items count should display 0<br>6.  Click on &#39;My Requests&#39; tab then click Filter Icon<br>7. Filter a non-existing data<br>8. Table should return &#39;No Items Found&#39; Create a new request to add content<br>9.  Items count should display 0</td><td class="s13"></td><td class="s8">Table should return the EXACT data per TAB based on selected fitler for old and newly added data</td><td class="s11">Blocked</td><td class="s11">Blocked</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8">unable to prceed due to hidden my approvals tab</td><td class="s14">filter issue on pagination<br>CITYLANDPRS-799<br>CITYLANDPRS-800 and 801<br>CITYLANDPRS-891</td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R12" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">13</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">Critical</td><td class="s8">Validate Single filter with match works per tab</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Filter with match results<br>4. Select for single filter &gt; Search. Do this for each dropdown. Check on Parameters column.<br>5. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;All&#39; tab<br>6. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>7. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>8. Click &#39;For My Approval/Assigned&#39; tab<br>9. Click Filter Icon<br>10. Filter with match results<br>11. Select for single filter &gt; Search. Do this for each dropdown. Check on Parameters column.<br>12. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;All&#39; tab<br>13. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>14. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>15. Click &#39;My Requests&#39; tab<br>16. Filter with match results<br>17. Select for single filter &gt; Search. Do this for each dropdown. Check on Parameters column.<br>18. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;All&#39; tab<br>19. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>20. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br></td><td class="s8">Parameter_1:<br><br>    a. Type -  &lt;any&gt;<br><br>Parameter_2:<br><br>    b. Requestor - &lt;any&gt;<br><br>Parameter_3:<br><br>    c. Company - &lt;any&gt;<br><br>Parameter_3:<br><br>    d. Department - &lt;any&gt;<br><br>Parameter_3:<br><br>    e. Project - &lt;any&gt;<br><br>Parameter_3:<br><br>    f. Status - &lt;any&gt;</td><td class="s8">Single Filter for each dropdown selection should work and return correct result<br><br>    a. Type <br><br>    b. Requestor<br><br>    c. Company<br><br>    d. Department<br><br>    e. Project <br><br>    f. Status</td><td class="s11">Blocked</td><td class="s11">Blocked</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s14">filter issue on pagination<br>CITYLANDPRS-799<br>CITYLANDPRS-800 and 801<br>CITYLANDPRS-891</td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R13" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">14</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">High</td><td class="s8">Validate Multiple filter exact match works per tab</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Select 2-3 dropdown to filter an exact match. Check Parameters<br>4. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;All&#39; tab<br>5. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>6. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>7. Click &#39;For My Approval/Assigned&#39; tab<br>8. Click Filter Icon<br>9. Select 2 dropdown to filter an exact match. Check Parameters<br>10. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;For My Approval/Assigned&#39; tab<br>11. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>12. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>13. Click &#39;My Requests&#39; tab<br>14. Click Filter Icon<br>15. Select 2 dropdown to filter an exact match. Check Parameters<br>16. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;My Requests&#39; tab<br>17. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>18. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br></td><td class="s8">Parameter_1:<br>   a. Type -  &lt;any&gt;<br>   b. Status - &lt;any&gt;<br><br>Parameter_2:<br><br>    a. Requestor - &lt;any&gt;<br>    b. Project - &lt;any&gt;<br>    c. Department - &lt;any&gt;</td><td class="s8">Table should return the correct data based on 2-3 filter selection per tab </td><td class="s11">Blocked</td><td class="s11">Blocked</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R14" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">15</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">High</td><td class="s8">Validate Multiple filter no match returns &#39;No Items Found&#39; per tab</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Filter a no match. Select 2-3 dropdown to filter. Check Parameters<br>4. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;All&#39; tab<br>5. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>6. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>7. Click &#39;For My Approval/Assigned&#39; tab<br>8. Click Filter Icon<br>9. Filter a no match. Select 2-3 dropdown to filter. Check Parameters<br>10. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;For My Approval/Assigned&#39; tab<br>11. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>12. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br>13. Click &#39;My Requests&#39; tab<br>14. Click Filter Icon<br>15. Filter a no match. Select 2-3 dropdown to filter. Check Parameters<br>16. Table should return the EXACT data based on selected fitler for old and newly added data for &#39;My Requests&#39; tab<br>17. Items count should display the correct number based on filter results. Ex: 1 to 1 of 1 items<br>18. Pagination, previous and next button should work if there are more than 10 items in search results (only if applicable)<br></td><td class="s8">Parameter_1:<br>   a. Type -  &lt;any&gt;<br>   b. Status - &lt;any&gt;<br><br>Parameter_2:<br><br>    a. Requestor - &lt;any&gt;<br>    b. Project - &lt;any&gt;<br>    c. Department - &lt;any&gt;</td><td class="s8">Table should return  &#39;No Items Found&#39; Create a new request to add content per tab</td><td class="s8" rowspan="4"></td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr><tr style="height: 155px"><th id="1727288844R15" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">16</div></th><td class="s18"></td><td class="s7">RS Dashboard</td><td class="s6">Critical</td><td class="s6">Validate Download and Print Data works and contains all data</td><td class="s6"> A Requisition Slip has been created</td><td class="s6">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Should be able to click Download Button and download an Excel File of the Data<br>4. Validate file contains all data or same data count vs the table data count<br>5. Should be able to click Print Button and generate a Downloadable Excel File that will directly print out the Data from Dashboard Page<br>6. Validate file contains all data or same data count vs the table data count<br>7. Click &#39;For My Approval/Assigned&#39; tab<br>8. Should be able to click Download Button and download an Excel File of the Data<br>9. Validate file contains all data or same data count vs the table data count<br>10. Should be able to click Print Button and generate a Downloadable Excel File that will directly print out the Data from Dashboard Page<br>11. Validate file contains all data or same data count vs the table data count<br>12. Click &#39;My Requests&#39; tab<br>13. Should be able to click Download Button and download an Excel File of the Data<br>14. Validate file contains all data or same data count vs the table data count<br>15. Should be able to click Print Button and generate a Downloadable Excel File that will directly print out the Data from Dashboard Page<br>16. Validate file contains all data or same data count vs the table data count<br></td><td class="s7"></td><td class="s6">User should be able to Donwload data with Excel file and Print with downloadable excel file per tab with complete data against the table data count</td><td class="s19">Out of Scope</td><td class="s19">Out of Scope</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s20"></td><td class="s20"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td></tr><tr style="height: 155px"><th id="1727288844R16" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">17</div></th><td class="s22"></td><td class="s23">RS Dashboard</td><td class="s8">High</td><td class="s20">Validate copy works</td><td class="s20"> A Requisition Slip has been created</td><td class="s20">NOT PRESENT ON REQUIREMENT</td><td class="s23"></td><td class="s23"></td><td class="s19">Out of Scope</td><td class="s19">Out of Scope</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s20"></td><td class="s20"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s21"></td></tr><tr style="height: 155px"><th id="1727288844R17" style="height: 155px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 155px">18</div></th><td class="s16"></td><td class="s13">RS Dashboard</td><td class="s8">High</td><td class="s8">Show entries dropdown and pagination should work</td><td class="s8"> A Requisition Slip has been created</td><td class="s8">1.  Login &gt; Click Dashboard<br>2. On &#39;All&#39; tab Click Filter Icon<br>3. Select Show Entries Dropdown 1, 5 , 10<br>4. Validate table returns the results according to selected count in Show entries<br>5. Click &#39;For My Approval/Assigned&#39; tab<br>6. Select Show Entries Dropdown 1, 5 , 10<br>7. Validate table returns the results according to selected count in Show entries<br>8. Click &#39;My Requests&#39; tab<br>9. Select Show Entries Dropdown 1, 5 , 10<br>10. Validate table returns the results according to selected count in Show entries<br><br></td><td class="s8">Parameters:<br>1. 1<br>2. 5<br>3. 10</td><td class="s8">Table should return data according to selected count in Show entries</td><td class="s9">Passed</td><td class="s24">Failed</td><td class="s8"></td><td class="s10">Not Started</td><td class="s8"></td><td class="s25"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1083">CITYLANDPRS-1083</a></td><td class="s8"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td><td class="s17"></td></tr></tbody></table></div>