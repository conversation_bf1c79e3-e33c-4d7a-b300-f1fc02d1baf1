const chai = require('chai');
const { expect } = chai;

const { requisitionBusinessLogic } = require('../../../src/domain/entities/requisitionEntity');

describe('Domain :: Entities :: RequisitionBusinessLogic', () => {
  describe('canBeSubmitted()', () => {
    context('When requisition is not in DRAFT status', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionItemLists: [{ id: 1 }]
        };
        expect(requisitionBusinessLogic.canBeSubmitted(requisition)).to.be.false;
      });
    });

    context('When requisition has no items', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'DRAFT',
          requisitionItemLists: []
        };
        expect(requisitionBusinessLogic.canBeSubmitted(requisition)).to.be.false;
      });
    });

    context('When requisition is in DRAFT status and has items', () => {
      it('Should return true', () => {
        const requisition = {
          status: 'DRAFT',
          requisitionItemLists: [{ id: 1 }]
        };
        expect(requisitionBusinessLogic.canBeSubmitted(requisition)).to.be.true;
      });
    });
  });

  describe('canBeApprovedBy()', () => {
    context('When requisition is not in SUBMITTED or PARTIALLY_APPROVED status', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'DRAFT',
          requisitionApprovers: [{ userId: 1, status: 'pending' }]
        };
        expect(requisitionBusinessLogic.canBeApprovedBy(requisition, 1)).to.be.false;
      });
    });

    context('When user is not an approver', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionApprovers: [{ userId: 2, status: 'pending' }]
        };
        expect(requisitionBusinessLogic.canBeApprovedBy(requisition, 1)).to.be.false;
      });
    });

    context('When user has already approved', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionApprovers: [{ userId: 1, status: 'APPROVED' }]
        };
        expect(requisitionBusinessLogic.canBeApprovedBy(requisition, 1)).to.be.false;
      });
    });

    context('When user is an approver and has not approved yet', () => {
      it('Should return true', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionApprovers: [{ userId: 1, status: 'pending' }]
        };
        expect(requisitionBusinessLogic.canBeApprovedBy(requisition, 1)).to.be.true;
      });
    });
  });

  describe('canBeRejectedBy()', () => {
    context('When requisition is not in SUBMITTED or PARTIALLY_APPROVED status', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'DRAFT',
          requisitionApprovers: [{ userId: 1, status: 'pending' }]
        };
        expect(requisitionBusinessLogic.canBeRejectedBy(requisition, 1)).to.be.false;
      });
    });

    context('When user is not an approver', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionApprovers: [{ userId: 2, status: 'pending' }]
        };
        expect(requisitionBusinessLogic.canBeRejectedBy(requisition, 1)).to.be.false;
      });
    });

    context('When user has already rejected', () => {
      it('Should return false', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionApprovers: [{ userId: 1, status: 'REJECTED' }]
        };
        expect(requisitionBusinessLogic.canBeRejectedBy(requisition, 1)).to.be.false;
      });
    });

    context('When user is an approver and has not rejected yet', () => {
      it('Should return true', () => {
        const requisition = {
          status: 'SUBMITTED',
          requisitionApprovers: [{ userId: 1, status: 'pending' }]
        };
        expect(requisitionBusinessLogic.canBeRejectedBy(requisition, 1)).to.be.true;
      });
    });
  });

  describe('getNextStatusAfterApproval()', () => {
    context('When not all approvers have approved', () => {
      it('Should return PARTIALLY_APPROVED', () => {
        const requisition = {
          requisitionApprovers: [
            { userId: 1, status: 'pending' },
            { userId: 2, status: 'pending' }
          ]
        };
        expect(requisitionBusinessLogic.getNextStatusAfterApproval(requisition, 1)).to.equal('PARTIALLY_APPROVED');
      });
    });

    context('When all approvers have approved', () => {
      it('Should return APPROVED', () => {
        const requisition = {
          requisitionApprovers: [
            { userId: 1, status: 'pending' },
            { userId: 2, status: 'APPROVED' }
          ]
        };
        expect(requisitionBusinessLogic.getNextStatusAfterApproval(requisition, 1)).to.equal('APPROVED');
      });
    });
  });

  describe('canBeClosed()', () => {
    context('When requisition is in DRAFT or SUBMITTED status', () => {
      it('Should return false', () => {
        expect(requisitionBusinessLogic.canBeClosed({ status: 'DRAFT' })).to.be.false;
        expect(requisitionBusinessLogic.canBeClosed({ status: 'SUBMITTED' })).to.be.false;
      });
    });

    context('When requisition is already CLOSED', () => {
      it('Should return false', () => {
        expect(requisitionBusinessLogic.canBeClosed({ status: 'CLOSED' })).to.be.false;
      });
    });

    context('When requisition is in a closable status', () => {
      it('Should return true', () => {
        expect(requisitionBusinessLogic.canBeClosed({ status: 'CANVASSING' })).to.be.true;
        expect(requisitionBusinessLogic.canBeClosed({ status: 'ORDERED' })).to.be.true;
        expect(requisitionBusinessLogic.canBeClosed({ status: 'DELIVERED' })).to.be.true;
        expect(requisitionBusinessLogic.canBeClosed({ status: 'PARTIALLY_DELIVERED' })).to.be.true;
      });
    });
  });

  describe('canBeAssigned()', () => {
    context('When requisition is not in APPROVED status', () => {
      it('Should return false', () => {
        expect(requisitionBusinessLogic.canBeAssigned({ status: 'DRAFT' })).to.be.false;
        expect(requisitionBusinessLogic.canBeAssigned({ status: 'SUBMITTED' })).to.be.false;
        expect(requisitionBusinessLogic.canBeAssigned({ status: 'PARTIALLY_APPROVED' })).to.be.false;
      });
    });

    context('When requisition is in APPROVED status', () => {
      it('Should return true', () => {
        expect(requisitionBusinessLogic.canBeAssigned({ status: 'APPROVED' })).to.be.true;
      });
    });
  });

  describe('hasItemSufficientQuantity()', () => {
    context('When requested quantity exceeds available quantity', () => {
      it('Should return false', () => {
        expect(requisitionBusinessLogic.hasItemSufficientQuantity({ gfq: 5 }, 10)).to.be.false;
      });
    });

    context('When requested quantity is less than or equal to available quantity', () => {
      it('Should return true', () => {
        expect(requisitionBusinessLogic.hasItemSufficientQuantity({ gfq: 10 }, 5)).to.be.true;
        expect(requisitionBusinessLogic.hasItemSufficientQuantity({ gfq: 10 }, 10)).to.be.true;
      });
    });

    context('When remainingGfq is available', () => {
      it('Should use remainingGfq instead of gfq', () => {
        expect(requisitionBusinessLogic.hasItemSufficientQuantity({ gfq: 10, remainingGfq: 5 }, 6)).to.be.false;
        expect(requisitionBusinessLogic.hasItemSufficientQuantity({ gfq: 10, remainingGfq: 5 }, 5)).to.be.true;
      });
    });
  });
});
