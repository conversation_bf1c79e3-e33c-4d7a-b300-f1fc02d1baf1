<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON>ri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{background-color:#292a2d;text-align:left;color:#f8faff;font-family:docs-DeepSeek-CJK-patch,Arial;font-size:12pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:Arial;font-size:12pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="2116500626C0" style="width:461px;" class="column-headers-background">A</th><th id="2116500626C1" style="width:341px;" class="column-headers-background">B</th><th id="2116500626C2" style="width:341px;" class="column-headers-background">C</th><th id="2116500626C3" style="width:341px;" class="column-headers-background">D</th><th id="2116500626C4" style="width:341px;" class="column-headers-background">E</th><th id="2116500626C5" style="width:100px;" class="column-headers-background">F</th><th id="2116500626C6" style="width:100px;" class="column-headers-background">G</th><th id="2116500626C7" style="width:100px;" class="column-headers-background">H</th><th id="2116500626C8" style="width:100px;" class="column-headers-background">I</th><th id="2116500626C9" style="width:100px;" class="column-headers-background">J</th><th id="2116500626C10" style="width:100px;" class="column-headers-background">K</th><th id="2116500626C11" style="width:100px;" class="column-headers-background">L</th><th id="2116500626C12" style="width:100px;" class="column-headers-background">M</th><th id="2116500626C13" style="width:100px;" class="column-headers-background">N</th><th id="2116500626C14" style="width:100px;" class="column-headers-background">O</th><th id="2116500626C15" style="width:100px;" class="column-headers-background">P</th><th id="2116500626C16" style="width:100px;" class="column-headers-background">Q</th><th id="2116500626C17" style="width:100px;" class="column-headers-background">R</th><th id="2116500626C18" style="width:100px;" class="column-headers-background">S</th><th id="2116500626C19" style="width:100px;" class="column-headers-background">T</th><th id="2116500626C20" style="width:100px;" class="column-headers-background">U</th><th id="2116500626C21" style="width:100px;" class="column-headers-background">V</th><th id="2116500626C22" style="width:100px;" class="column-headers-background">W</th><th id="2116500626C23" style="width:100px;" class="column-headers-background">X</th><th id="2116500626C24" style="width:100px;" class="column-headers-background">Y</th><th id="2116500626C25" style="width:100px;" class="column-headers-background">Z</th></tr></thead><tbody><tr style="height: 19px"><th id="2116500626R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td class="s0">1. Canvass Sheet Creation &amp; Numbering (AC 2)</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2">TC-ID</td><td class="s3">Scenario</td><td class="s3">Steps</td><td class="s3">Expected Result</td><td class="s3">Notes</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s2">TC-2.1</td><td class="s3">Create first Canvass Sheet</td><td class="s3">1. Open approved Requisition Slip.<br>2. Click &quot;Create Canvass Sheet&quot;.</td><td class="s3">CS number auto-generated (e.g., CS-COMPAA00000001).</td><td class="s3">Format: CS-[CompanyCode]AA+8 digits.</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s2">TC-2.2</td><td class="s3">Create subsequent Canvass Sheets</td><td class="s3">1. Create first CS.<br>2. Create second CS.</td><td class="s3">Second CS number increments (e.g., CS-COMPAA00000002).</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2116500626R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s0">2. Quantity Validation (AC 3.a, 3.b, 3.c)</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s0"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s0">Key Rules:</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s0"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s0">OFM/OFM Transfer: Total quantity must = Requested Qty (no excess).</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s0"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s0">Non-OFM/Non-OFM Transfer: Allows ≥ Requested Qty.</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s2">TC-ID</td><td class="s3">Scenario</td><td class="s3">Steps</td><td class="s3">Expected Result</td><td class="s3">AC Reference</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s2">TC-3a.1</td><td class="s3">OFM: Supplier Qty &lt; Requested Qty</td><td class="s3">1. Add OFM item with Qty=80 (Requested=100).<br>2. Submit CS.</td><td class="s3">Error: &quot;Create another CS to fulfill remaining 20.&quot;</td><td class="s3">AC 3.a.i</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s2">TC-3a.2</td><td class="s3">Non-OFM: Supplier Qty &lt; Requested Qty</td><td class="s3">1. Add Non-OFM item with Qty=80 (Requested=100).<br>2. Submit CS.</td><td class="s3">Error: &quot;Create another CS to fulfill remaining 20.&quot;</td><td class="s3">AC 3.a.i</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s2">TC-3b.1</td><td class="s3">Supplier Qty = Requested Qty</td><td class="s3">1. Add item with Qty=100 (Requested=100).<br>2. Submit CS.</td><td class="s3">CS submitted successfully. No further CS required.</td><td class="s3">AC 3.b.i</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s2">TC-3c.1</td><td class="s3">Non-OFM: Supplier Qty &gt; Requested Qty</td><td class="s3">1. Add Non-OFM item with Qty=120 (Requested=100).<br>2. Submit CS.</td><td class="s3">CS submitted successfully. Excess allowed.</td><td class="s3">AC 3.c.i</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s2">TC-3c.2</td><td class="s3">OFM: Supplier Qty &gt; Requested Qty</td><td class="s3">1. Add OFM item with Qty=120 (Requested=100).<br>2. Submit CS.</td><td class="s3">Error: &quot;OFM items cannot exceed requested quantity.&quot;</td><td class="s3">AC 3.c.i (Negative Case)</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2116500626R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s0">3. Tracking Quantities Until Approval (AC 4)</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s0"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s0">Key Rule: System must track cumulative quantities across Draft/Submitted CS until final approval.</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s0"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s2">TC-ID</td><td class="s3">Scenario</td><td class="s3">Steps</td><td class="s3">Expected Result</td><td class="s3">AC Reference</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s2">TC-4a.1</td><td class="s3">Partial Fulfillment (OFM)</td><td class="s3">1. Submit CS-1 with Qty=60 (Requested=100).<br>2. Create CS-2 for Qty=40.</td><td class="s3">System shows &quot;Remaining Qty: 40&quot; in CS-2 creation.</td><td class="s3">AC 4.a.i.a</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s2">TC-4a.2</td><td class="s3">Non-OFM: Excess in Later CS</td><td class="s3">1. Submit CS-1 with Qty=80 (Requested=100).<br>2. Create CS-2 with Qty=30.</td><td class="s3">Warning: &quot;Total Qty (110) exceeds requested (100).&quot; but allows submission.</td><td class="s3">AC 4.c.i</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s2">TC-4b.1</td><td class="s3">Exact Fulfillment</td><td class="s3">1. Submit CS-1 with Qty=100 (Requested=100).</td><td class="s3">No further CS allowed. Status: &quot;Fully Fulfilled.&quot;</td><td class="s3">AC 4.b.i</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s4"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2116500626R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s0">4. Amount &amp; UI Validation (AC 5, 6)</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s2">TC-ID</td><td class="s3">Scenario</td><td class="s3">Steps</td><td class="s3">Expected Result</td><td class="s3">AC Reference</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s2">TC-5.1</td><td class="s3">Valid Decimal Amount</td><td class="s3">1. Enter Amount=450.33.</td><td class="s3">Saved as 450.33.</td><td class="s3">AC 5</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s2">TC-5.2</td><td class="s3">Invalid Decimal Amount</td><td class="s3">1. Enter Amount=450.333.</td><td class="s3">Error: &quot;Amount must have 2 decimal places.&quot;</td><td class="s3">AC 5 (Negative Case)</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s2">TC-6.1</td><td class="s3">View Related Canvass Sheets</td><td class="s3">1. Open Requisition Slip.<br>2. Navigate to &quot;Related Documents&quot; tab.</td><td class="s3">Lists all linked CS (e.g., CS-001, CS-002) with statuses (Draft/Submitted/Approved).</td><td class="s3">AC 6</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="2116500626R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s6"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s6"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s7">Scenario: Requisition Slip requests 100 units (OFM).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s7">CS-1: Adds Supplier A (Qty=60) → Draft.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s7">CS-2: Adds Supplier B (Qty=40) → Submitted.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s7">Result: Fully fulfilled. No further CS needed.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s6"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s7">Scenario 1: Non-OFM Exact Fulfillment (Multiple Suppliers)</td><td></td><td class="s7">Scenario 2: Non-OFM Over-Fulfillment (Single Supplier)</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s7">Item Type: Non-OFM</td><td></td><td class="s7">Item Type: Non-OFM</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s7">Requested Qty: 100</td><td></td><td class="s7">Requested Qty: 100</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s7">CS-1: Supplier A (Qty=50) → Submitted.</td><td></td><td class="s7">CS-1: Supplier A (Qty=120) → Submitted.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s7"></td><td></td><td class="s7">Result:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s7">CS-2: Supplier B (Qty=50) → Submitted.</td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s7">Result:</td><td></td><td class="s7">Total = 120 (exceeds request).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s7">Total = 100 (exact).</td><td></td><td class="s7">Allowed. System shows warning: &quot;Total exceeds requested quantity by 20&quot; but permits submission.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s7">No further CS needed. System marks Requisition as &quot;Fully Fulfilled.&quot;</td><td></td><td class="s7">Validation:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s7">Validation:</td><td></td><td class="s7">AC 3.c.i (Non-OFM allows excess).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s7">AC 3.b.i (equal quantity allowed).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s7">Scenario 3: OFM Partial → Full Fulfillment (Draft + Submitted)</td><td></td><td class="s7">Scenario 4: OFM Under-Fulfillment (Rejection Flow)</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s7">Item Type: OFM</td><td></td><td class="s7">Item Type: OFM</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s7">Requested Qty: 100</td><td></td><td class="s7">Requested Qty: 100</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s7">CS-1: Supplier A (Qty=60) → Draft (not yet submitted).</td><td></td><td class="s7">CS-1: Supplier A (Qty=80) → Submitted.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s7"></td><td></td><td class="s7">Result:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s7">CS-2: Supplier B (Qty=40) → Submitted.</td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s7">Result:</td><td></td><td class="s7">Error: &quot;Remaining 20 units unfulfilled. Create another CS.&quot;</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s7">System blocks CS-1 submission until total reaches 100.</td><td></td><td class="s7">User forced to create CS-2 for remaining 20.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s7">User must adjust CS-1 to Qty=60 + CS-2 to Qty=40 → Both Submitted.</td><td></td><td class="s7">Validation:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s7"></td><td></td><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R76" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">77</div></th><td class="s7">Validation:</td><td></td><td class="s7">AC 3.a.i (OFM requires exact fulfillment).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R77" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">78</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R78" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">79</div></th><td class="s7">AC 4.a.i (tracking until approval).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s7">Scenario 5: Mixed Item Types (OFM + Non-OFM)</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s7">Requested Qty: 100 (OFM) + 50 (Non-OFM)</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s7">CS-1:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s7">OFM Item: Supplier A (Qty=60) → Draft.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s7">Non-OFM Item: Supplier B (Qty=60) → Draft.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R90" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">91</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R91" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">92</div></th><td class="s7">CS-2:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R92" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">93</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R93" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">94</div></th><td class="s7">OFM Item: Supplier C (Qty=40) → Submitted.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R94" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">95</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R95" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">96</div></th><td class="s7">Non-OFM Item: Supplier D (Qty=0) → Skipped (already fulfilled).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s7">Result:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s7">OFM: Total=100 (valid).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s7">Non-OFM: Total=60 (exceeds 50 → allowed).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s7">CS-1 can only be submitted after OFM reaches 100.</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s7">Validation:</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R106" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">107</div></th><td class="s7">AC 3.c.i (Non-OFM excess) + AC 4.a.i (OFM tracking).</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="2116500626R107" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">108</div></th><td class="s7"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></tbody></table></div>