// Test script for login functionality
const { chromium } = require('playwright');

async function testLogin() {
  console.log('Starting login test...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to the login page
    await page.goto('http://localhost:3000/auth/login');
    console.log('Navigated to login page');
    
    // Test admin login
    await testUserLogin(page, 'admin', 'Admin@123', 'Admin');
    
    // Test manager login
    await testUserLogin(page, 'manager', 'Manager@123', 'Manager');
    
    // Test regular user login
    await testUserLogin(page, 'user', 'User@123', 'User');
    
    console.log('All login tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

async function testUserLogin(page, username, password, userType) {
  console.log(`Testing ${userType} login...`);
  
  // Navigate to login page
  await page.goto('http://localhost:3000/auth/login');
  
  // Fill in login form
  await page.fill('input[name="username"]', username);
  await page.fill('input[name="password"]', password);
  
  // Click login button
  await page.click('button[type="submit"]');
  
  // Wait for navigation to dashboard
  try {
    await page.waitForURL('**/app/dashboard', { timeout: 5000 });
    console.log(`${userType} login successful!`);
    
    // Check if user info is displayed correctly
    const userNameElement = await page.locator('text=' + username);
    if (await userNameElement.count() > 0) {
      console.log(`${userType} username displayed correctly`);
    } else {
      console.log(`${userType} username not found on dashboard`);
    }
    
    // Log out
    await page.click('text=Sign out');
    await page.waitForURL('**/auth/login');
    console.log(`${userType} logout successful`);
  } catch (error) {
    console.error(`${userType} login failed:`, error);
    // Take a screenshot of the error
    await page.screenshot({ path: `${userType.toLowerCase()}-login-error.png` });
  }
}

testLogin();
