const {
  createUpdateInvoiceReportSchema,
} = require('../../../domain/entities/invoiceReportEntity');
const {
  PAGINATION_DEFAULTS,
} = require('../../../domain/constants/deliveryReceiptConstants');

class InvoiceReportController {
  constructor({ invoiceReportService }) {
    this.invoiceReportService = invoiceReportService;
  }

  async createInvoiceReport(request, reply) {
    const { body, userFromToken } = request;
    createUpdateInvoiceReportSchema.parse(body);

    const invoiceReport = await this.invoiceReportService.createInvoiceReport(
      body,
      userFromToken,
    );

    return reply.status(201).send({ ...invoiceReport });
  }

  async getInvoiceReportById(request, reply) {
    const { id } = request.params;
    const invoiceReport =
      await this.invoiceReportService.getInvoiceReportById(id);

    return reply.status(200).send(invoiceReport);
  }

  async getDeliveryReportsByInvoiceReportId(request, reply) {
    const { id } = request.params;
    const deliveryReports =
      await this.invoiceReportService.getDeliveryReportsByInvoiceReportId(
        id,
        request.query,
      );

    return reply.status(200).send(deliveryReports);
  }

  async getDeliveryReportItemsByInvoiceReportId(request, reply) {
    const { id } = request.params;
    const deliveryReportItems =
      await this.invoiceReportService.getDeliveryReportItemsByInvoiceReportId(
        id,
        request.query,
      );

    return reply.status(200).send(deliveryReportItems);
  }

  async updateInvoiceReport(request, reply) {
    const { body, params, userFromToken } = request;
    createUpdateInvoiceReportSchema.parse(body);
    const invoiceReport = await this.invoiceReportService.updateInvoiceReport(
      params.id,
      body,
      userFromToken,
    );

    return reply.status(200).send({ ...invoiceReport });
  }

  async getInvoiceReportFromRequisitionId(request, reply) {
    const { query, params } = request;
    const deliveryReturns =
      await this.invoiceReportService.getInvoiceReportsFromRequisitionId(
        params.requisitionId,
        {
          search: query.search,
          page: query.page,
          limit: query.limit,
          sortBy: query.sortBy,
        },
      );

    return reply.status(200).send({ ...deliveryReturns });
  }
}

module.exports = InvoiceReportController;
