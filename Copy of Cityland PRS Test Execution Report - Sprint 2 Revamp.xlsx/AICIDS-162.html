<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{background-color:#cc4125;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1652721165C0" style="width:170px;" class="column-headers-background">A</th><th id="1652721165C1" style="width:208px;" class="column-headers-background">B</th><th id="1652721165C2" style="width:320px;" class="column-headers-background">C</th><th id="1652721165C3" style="width:270px;" class="column-headers-background">D</th><th id="1652721165C4" style="width:335px;" class="column-headers-background">E</th><th id="1652721165C5" style="width:313px;" class="column-headers-background">F</th><th id="1652721165C6" style="width:197px;" class="column-headers-background">G</th><th id="1652721165C7" style="width:154px;" class="column-headers-background">H</th><th id="1652721165C8" style="width:154px;" class="column-headers-background">I</th><th id="1652721165C9" style="width:310px;" class="column-headers-background">J</th><th id="1652721165C10" style="width:196px;" class="column-headers-background">K</th></tr></thead><tbody><tr style="height: 20px"><th id="1652721165R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">Web </td><td class="s1">Mobile</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1652721165R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="10">[MCIA - Face Authentication] UI Changes</td><td class="s2"></td></tr><tr style="height: 20px"><th id="1652721165R2" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">3</div></th><td class="s3">TC-162-01</td><td class="s4">Face Auth New UI</td><td class="s5">Verify face auth landing page</td><td class="s5" rowspan="4">1. users should already shared his travel itinerary</td><td class="s5">1. go to https://dev.identityscape.ph/face-verification<br>2. validate the new UI</td><td class="s6"><a target="_blank" href="https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0">1. New UI should have a background: <br>https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0</a></td><td class="s5"></td><td class="s7"></td><td class="s5"></td><td class="s5"></td><td class="s8"></td></tr><tr style="height: 20px"><th id="1652721165R3" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">4</div></th><td class="s3">TC-162-02</td><td class="s4">Face Auth New UI</td><td class="s5">Verify face auth new UI - No record found</td><td class="s5">1. go to https://dev.identityscape.ph/face-verification<br>2. scan invalid qr code</td><td class="s6"><a target="_blank" href="https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0">1. New UI should have a background: <br>https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0</a></td><td class="s5"></td><td class="s7"></td><td class="s5"></td><td class="s5"></td><td class="s9"></td></tr><tr style="height: 20px"><th id="1652721165R4" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">5</div></th><td class="s3">TC-162-03</td><td class="s4">Face Auth New UI</td><td class="s5">Verify face auth new UI - Regular Lane</td><td class="s5">1. go to https://dev.identityscape.ph/face-verification<br>2. scan the qr code<br>3. do some negative scenarios (e.g photo poofing)</td><td class="s6"><a target="_blank" href="https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0">1. New UI should have a background: <br>https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0</a></td><td class="s10"></td><td class="s7"></td><td class="s11"></td><td class="s11"></td><td class="s12"></td></tr><tr style="height: 20px"><th id="1652721165R5" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">6</div></th><td class="s3">TC-162-04</td><td class="s4">Face Auth New UI</td><td class="s5">Verify face auth new UI - Special Lane</td><td class="s5">1. go to https://dev.identityscape.ph/face-verification<br>2. scan the qr code</td><td class="s6"><a target="_blank" href="https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0">1. New UI should have a background: <br>https://www.figma.com/design/8hi7VzHrlPjxIBDmNhqYf1/Project-Blue-2?node-id=0-1&amp;node-type=CANVAS&amp;t=EqXJIbx276jdM74t-0</a></td><td class="s10"></td><td class="s7"></td><td class="s11"></td><td class="s11"></td><td class="s12"></td></tr><tr style="height: 20px"><th id="1652721165R6" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">7</div></th><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td><td class="s13"></td></tr></tbody></table></div>