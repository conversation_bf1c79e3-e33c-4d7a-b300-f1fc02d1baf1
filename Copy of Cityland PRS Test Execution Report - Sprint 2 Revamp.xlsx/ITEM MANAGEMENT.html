<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Inter,Arial;font-size:12pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-right:1px SOLID #000000;background-color:#f7f8fa;text-align:left;color:#000000;font-family:docs-Inter,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1082055299C0" style="width:94px;" class="column-headers-background">A</th><th id="1082055299C1" style="width:181px;" class="column-headers-background">B</th><th id="1082055299C2" style="width:63px;" class="column-headers-background">C</th><th id="1082055299C3" style="width:252px;" class="column-headers-background">D</th><th id="1082055299C4" style="width:233px;" class="column-headers-background">E</th><th id="1082055299C5" style="width:330px;" class="column-headers-background">F</th><th id="1082055299C7" style="width:258px;" class="column-headers-background">H</th><th id="1082055299C8" style="width:149px;" class="column-headers-background">I</th><th id="1082055299C9" style="width:238px;" class="column-headers-background">J</th><th id="1082055299C10" style="width:385px;" class="column-headers-background">K</th><th id="1082055299C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="1082055299R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">Status</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1082055299R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="11">Display Account Code in the Non-OFM Landing Page</td></tr><tr style="height: 19px"><th id="1082055299R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3">PRS-1271-001</td><td class="s3">Display Account Code in the Non-OFM Landing Page</td><td class="s4">Critical</td><td class="s3">Verify Account Code column visiblity in Non-OFM Items Dashboard</td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. Non-OFM Items have been created.</td><td class="s3">1. Navigate to the Non-OFM Items Dashboard page.<br>2. Verify displayed columns.</td><td class="s3">2. Non-OFM Item Dashboard should display the following columns:<br>     - Item Name<br>     - Type<br>     - Unit<br>     - Account Code<br>     -  Actions</td><td class="s5" rowspan="2"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1kvcw8QX4iARr1qFiEpBEPY3YLgSTksD_l1CIp2_QgIg/edit?usp=drive_link">Justine_Sprint1_Test Result</a></td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3">PRS-1271-002</td><td class="s3">Display Account Code in the Non-OFM Landing Page</td><td class="s4">High</td><td class="s7">Verify Non-OFM item without an Account Code</td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. Non-OFM Items have been created without an Account Code.</td><td class="s3">1. Navigate to the Non-OFM Items Dashboard page.<br>2. Verify displayed columns.<br>3. Locate an item without an Account Code.</td><td class="s3">3. The item should be listed in the dashboard. The Account Code column should show &quot;---&quot; or &quot;N/A&quot;.</td><td class="s8">Out of Scope</td><td class="s3">4/23; Account Code is automatically generated upon the creation of Non-OFM Items, as it serves as the unique identifier for each specific item.<br><br>Therefore, an item cannot be created without an Account Code. As a result, an expected result where the Account Code displays as &quot;---&quot; or &quot;N/A&quot; cannot be verified, since such a scenario will not occur in the system.</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s2" colspan="11">Update Function of Steelbar Dimension</td></tr><tr style="height: 19px"><th id="1082055299R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3">PRS-1272-001</td><td class="s3">Update Function of Steelbar Dimension</td><td class="s4">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Weight </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">autofills when Grade, Diameter, and Length are selected.</span></td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. OFM Items with Steelbar dimensions have been synced.</td><td class="s3">1. Select a Grade, Diameter, and Length for Steelbar item.<br>2. Observe the Weight field.</td><td class="s5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&amp;gid=1593686932#gid=1593686932">The Weight field should autofill based on the selected Grade, Diameter, and Length.<br><br>STEELBARS MATRIX:<br>https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&amp;gid=1593686932#gid=1593686932</a></td><td class="s5" rowspan="5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/18RFWRs6Cig6nDW1IBOGHW_IMyYinVOF2prAmAzcOw3E/edit?gid=0#gid=0">Ghienel_Sprint1_Test Result</a></td><td class="s6">Passed</td><td class="s3">4/23: Unable to view/edit ofm items<br><br>4/23: Autofill did not match based on Steelbar Matrix</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1457">4/23: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1457<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1467</a></td></tr><tr style="height: 19px"><th id="1082055299R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s3">PRS-1272-002</td><td class="s3">Update Function of Steelbar Dimension</td><td class="s4">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Diameter </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">autofills when Grade, Length, and Weigth are selected.</span></td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. OFM Items with Steelbar dimensions have been synced.</td><td class="s3">1. Select a Grade, Length, and Weight for Steelbar item.<br>2. Observe the Diameter field.</td><td class="s5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&amp;gid=1593686932#gid=1593686932">The Diameter field should autofill based on the selected Grade, Length, and Weight.<br><br>STEELBARS MATRIX:<br>https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&amp;gid=1593686932#gid=1593686932</a></td><td class="s6">Passed</td><td class="s3"></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1469">4/23<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1469</a></td></tr><tr style="height: 19px"><th id="1082055299R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s3">PRS-1272-003</td><td class="s3">Update Function of Steelbar Dimension</td><td class="s4">Critical</td><td class="s3"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Length </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> autofills when Grade, Diameter, and Weight are selected.</span></td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. OFM Items with Steelbar dimensions have been synced.</td><td class="s3">1. Select a Grade, Diameter, and Weight for Steelbar item.<br>2. Observe the Length field.</td><td class="s5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&amp;gid=1593686932#gid=1593686932">The Length field should autofill based on the selected Grade, Diameter, and Weight.<br><br><br>STEELBARS MATRIX:<br>https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&amp;gid=1593686932#gid=1593686932</a></td><td class="s6">Passed</td><td class="s3"></td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1470">4/23 <br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1470</a></td></tr><tr style="height: 19px"><th id="1082055299R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s3">PRS-1272-004</td><td class="s3">Update Function of Steelbar Dimension</td><td class="s4">High</td><td class="s3">Verify system behavior when one or more required fields (Grade, Length, Diameter or Weight) are missing.</td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. OFM Items with Steelbar dimensions have been synced.</td><td class="s3">1. Leave one or more required fields empty.<br>2. Click Save.</td><td class="s3">An error message should appear indicating that all required fields must be filled to proceed.</td><td class="s6">Passed</td><td class="s3"></td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1471">4/23<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1471</a></td></tr><tr style="height: 19px"><th id="1082055299R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s3">PRS-1272-005</td><td class="s3">Update Function of Steelbar Dimension</td><td class="s4">Minor</td><td class="s3">Verify that no fields are autofilled when two required fields (Grade, Length, Diameter or Weight) are missing.</td><td class="s3">1. User is logged in as the ff:<br>     - IT Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>     - Purchasing Admin<br>2. OFM Items with Steelbar dimensions have been synced.</td><td class="s3">1. Leave any two required fields empty.<br>2. Click Save.</td><td class="s3">No autofill should occur and the empty fields should remain blank.</td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s2" colspan="11">Allow Decimal to OFM Quanity</td></tr><tr style="height: 19px"><th id="1082055299R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s11">PRS-1480-001</td><td class="s3">Allow Decimal to OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify OFM Quantity and GFQ Allow Decimals in Item Details</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. Should have OFM items set up</td><td class="s3">1. Navigate to OFM Item Dashboard<br>2. Select an Item<br>3. Validate GFQ and Qty fields</td><td class="s3">3. Should display the OFM Quantity and the GFQ Quantity as a Decimal</td><td class="s5" rowspan="6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1YkgPxBR4oTAmadw4zMFKWzQhSyNv6YQUttRu65TPerY/edit?gid=46755566#gid=46755566">Aira_Results</a></td><td class="s12">Failed</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1696">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1696</a></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s11">PRS-1480-002</td><td class="s3">Allow Decimal to OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify Quantity with Decimal can be Entered in RS</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. Should have OFM items set up</td><td class="s3">1. Create RS<br>2. Populate fields<br>3. Select OFM as type of request<br>4. Add items<br>5. Enter decimal into quantity field<br>6. Submit RS</td><td class="s3">5. Should allow a 3-Decimal places for the OFM Quantity</td><td class="s12">Failed</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1692">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1692</a></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1082055299R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s11">PRS-1480-003</td><td class="s3">Allow Decimal to OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify Quantity with Decimal can be Entered in Canvass</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. RS should have OFM Type of Request<br>3. RS should be approved and for Canvassing</td><td class="s3">1. Assign a Purchasing Staff<br>2. Login as Purchasing Staff<br>3. Click Select Action<br>4. Click Enter Canvass<br>5. Add items<br>6. Click Enter Canvass<br>7. Populate fields<br>8. Enter quantity with decimal<br>9. Confirm Canvass</td><td class="s3">9. Should allow a 3-Decimal places for the OFM Quantity</td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s11">PRS-1480-004</td><td class="s3">Allow Decimal to OFM Quanity</td><td class="s4">Minor</td><td class="s13">Verify Quantity with more than 3-Decimal places can be Entered in RS</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. Should have OFM items set up</td><td class="s3">1. Create RS<br>2. Populate fields<br>3. Select OFM as type of request<br>4. Add items<br>5. Enter decimal with more than 3-Decimal places into quantity field<br>6. Submit RS</td><td class="s3">4. Should only allow a 3-Decimal places for the OFM Quantity<br>5. Should not be able to submit RS</td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s11">PRS-1480-005</td><td class="s3">Allow Decimal to OFM Quanity</td><td class="s4">Minor</td><td class="s13">Verify Quantity with more than 3-Decimal places can be Entered in Canvass</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. RS should have OFM Type of Request<br>3. RS should be approved and for Canvassing</td><td class="s3">1. Assign a Purchasing Staff<br>2. Login as Purchasing Staff<br>3. Click Select Action<br>4. Click Enter Canvass<br>5. Add items<br>6. Click Enter Canvass<br>7. Populate fields<br>8. Enter quantity with decimal<br>9. Confirm Canvass</td><td class="s3">8. Should only allow a 3-Decimal places for the OFM Quantity<br>9. Should not be able to submit Canvass</td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s14">PRS-1480-006</td><td class="s3">Allow Decimal to OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify OFM Quantity is Displayed as Decimals in All Documents</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. RS should have OFM Type of Request<br>3. RS should have canvass, PO, DR, and Invoice Report</td><td class="s3">1. Navigate to Dashboard<br>2. Select RS Number<br>3. Validate Qty in Item Table<br>4. Navigate to Related Documents &gt; Canvasses<br>5. Validate Qty in Item Table<br>6. Repeat steps 4-5 for Orders, Deliveries, and Invoice Reports</td><td class="s3">3. Should implement the 3-Decimal places of the Quantity across all Documents and viewing of the Item<br>- RS<br>- Canvass<br>- PO <br>- PR<br>- DR</td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s2" colspan="11">Allow Decimal to Non-OFM Quanity</td></tr><tr style="height: 19px"><th id="1082055299R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s11">PRS-1481-001</td><td class="s3">Allow Decimal to Non-OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify Non-OFM Quantity Allows Decimals in Item Details</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. Should have Non-OFM items set up</td><td class="s3">1. Navigate to Non-OFM Item Dashboard<br>2. Select an Item<br>3. Validate Qty fields</td><td class="s3">3. Should display the Non-OFM Quantity and the GFQ Quantity as a Decimal</td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1GxZe1_U3hJmTO1fPOpylkD2H0kny6HwXi2md_8LBNwM/edit?gid=768997081#gid=768997081">Gela test results</a></td><td class="s8">Out of Scope</td><td class="s3">no quantity for non-ofm items</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s11">PRS-1481-002</td><td class="s3">Allow Decimal to Non-OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify Quantity with Decimal can be Entered in RS</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. Should have Non-OFM items set up</td><td class="s3">1. Create RS<br>2. Populate fields<br>3. Select Non-OFM as type of request<br>4. Add items<br>5. Enter decimal into quantity field<br>6. Submit RS</td><td class="s3">3. Should allow a 3-Decimal places for the Non-OFM Quantity</td><td class="s16"></td><td class="s12">Failed</td><td class="s3">CITYLANDPRS-1692 [QA BUGS][REQUISUITION SLIP]Incorrect Text Field Focus on Quantity Input<br></td><td class="s10"></td></tr><tr style="height: 19px"><th id="1082055299R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s11">PRS-1481-003</td><td class="s3">Allow Decimal to Non-OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify Quantity with Decimal can be Entered in Canvass</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. RS should have Non-OFM Type of Request<br>3. RS should be approved and for Canvassing</td><td class="s3">1. Assign a Purchasing Staff<br>2. Login as Purchasing Staff<br>3. Click Select Action<br>4. Click Enter Canvass<br>5. Add items<br>6. Click Enter Canvass<br>7. Populate fields<br>8. Enter quantity with decimal<br>9. Confirm Canvass</td><td class="s3">3. Should allow a 3-Decimal places for the Non-OFM Quantity</td><td class="s16"></td><td class="s6">Passed</td><td class="s17">RS-08AA00000348</td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s11">PRS-1481-004</td><td class="s3">Allow Decimal to Non-OFM Quanity</td><td class="s4">Minor</td><td class="s13">Verify Quantity with more than 3-Decimal places can be Entered in RS</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. Should have Non-OFM items set up</td><td class="s3">1. Create RS<br>2. Populate fields<br>3. Select Non-OFM as type of request<br>4. Add items<br>5. Enter decimal with more than 3-Decimal places into quantity field<br>6. Submit RS</td><td class="s3">4. Should only allow a 3-Decimal places for the Non-OFM Quantity<br>5. Should not be able to submit RS</td><td class="s16"></td><td class="s6">Passed</td><td class="s18"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s11">PRS-1481-005</td><td class="s3">Allow Decimal to Non-OFM Quanity</td><td class="s4">Minor</td><td class="s13">Verify Quantity with more than 3-Decimal places can be Entered in Canvass</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. RS should have Non-OFM Type of Request<br>3. RS should be approved and for Canvassing</td><td class="s3">1. Assign a Purchasing Staff<br>2. Login as Purchasing Staff<br>3. Click Select Action<br>4. Click Enter Canvass<br>5. Add items<br>6. Click Enter Canvass<br>7. Populate fields<br>8. Enter quantity with decimal<br>9. Confirm Canvass</td><td class="s3">8. Should only allow a 3-Decimal places for the Non-OFM Quantity<br>9. Should not be able to submit Canvass</td><td class="s16"></td><td class="s6">Passed</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1082055299R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s11">PRS-1481-006</td><td class="s3">Allow Decimal to Non-OFM Quanity</td><td class="s4">Critical</td><td class="s3">Verify Non-OFM Quantity is Displayed as Decimals in All Documents</td><td class="s3">1. User should be logged in as:<br>     - IT Admin<br>     - Purchasing Admin<br>     - Engineers<br>     - Purchasing Staff<br>     - Purchasing Head<br>2. RS should have Non-OFM Type of Request<br>3. RS should have canvass, PO, DR, and Invoice Report</td><td class="s3">1. Navigate to Dashboard<br>2. Select RS Number<br>3. Validate Qty in Item Table<br>4. Navigate to Related Documents &gt; Canvasses<br>5. Validate Qty in Item Table<br>6. Repeat steps 4-5 for Orders, Deliveries, and Invoice Reports</td><td class="s3">3. Should implement the 3-Decimal places of the Quantity across all Documents and viewing of the Item<br>- RS<br>- Canvass<br>- PO <br>- PR<br>- DR</td><td class="s16"></td><td class="s12">Failed</td><td class="s3">CITYLANDPRS-1706</td><td class="s3"></td></tr></tbody></table></div>