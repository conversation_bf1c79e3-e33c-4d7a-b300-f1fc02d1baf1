/**
 * Example route with OpenAPI documentation
 */
const { z } = require('zod');

async function exampleDocumentedRoute(fastify) {
  const { 
    documentPublicRoute, 
    documentProtectedRoute, 
    standardResponses 
  } = fastify.diScope.resolve('utils');
  
  // Example entity schema
  const exampleSchema = z.object({
    id: z.string().uuid(),
    name: z.string().min(3).max(100),
    description: z.string().optional(),
    status: z.enum(['active', 'inactive', 'pending']),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
  });
  
  // Example request schemas
  const createExampleSchema = z.object({
    name: z.string().min(3).max(100),
    description: z.string().optional(),
    status: z.enum(['active', 'inactive', 'pending']).default('pending'),
  });
  
  const updateExampleSchema = createExampleSchema.partial();
  
  const exampleParamsSchema = z.object({
    id: z.string().uuid(),
  });
  
  const exampleQuerySchema = z.object({
    status: z.enum(['active', 'inactive', 'pending']).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  });
  
  // Generate standard responses
  const responses = standardResponses(exampleSchema);
  
  // Example controller
  const exampleController = {
    getExamples: async (request, reply) => {
      return reply.send({
        success: true,
        message: 'Examples retrieved successfully',
        data: {
          data: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              name: 'Example 1',
              description: 'This is an example',
              status: 'active',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }
          ],
          total: 1,
        },
      });
    },
    
    getExampleById: async (request, reply) => {
      return reply.send({
        success: true,
        message: 'Example retrieved successfully',
        data: {
          id: request.params.id,
          name: 'Example 1',
          description: 'This is an example',
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      });
    },
    
    createExample: async (request, reply) => {
      return reply.status(201).send({
        success: true,
        message: 'Example created successfully',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          ...request.body,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      });
    },
    
    updateExample: async (request, reply) => {
      return reply.send({
        success: true,
        message: 'Example updated successfully',
        data: {
          id: request.params.id,
          name: request.body.name || 'Example 1',
          description: request.body.description || 'This is an example',
          status: request.body.status || 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      });
    },
    
    deleteExample: async (request, reply) => {
      return reply.status(204).send();
    },
  };
  
  // Public routes
  fastify.route({
    method: 'GET',
    url: '/examples',
    ...documentPublicRoute({
      tag: 'Examples',
      summary: 'Get all examples',
      description: 'Returns a list of examples with pagination',
      querystring: exampleQuerySchema,
      response: responses.list,
    }),
    handler: exampleController.getExamples,
  });
  
  fastify.route({
    method: 'GET',
    url: '/examples/:id',
    ...documentPublicRoute({
      tag: 'Examples',
      summary: 'Get example by ID',
      description: 'Returns a single example by its ID',
      params: exampleParamsSchema,
      response: responses.get,
    }),
    handler: exampleController.getExampleById,
  });
  
  // Protected routes
  fastify.route({
    method: 'POST',
    url: '/examples',
    preHandler: fastify.authenticate,
    ...documentProtectedRoute({
      tag: 'Examples',
      summary: 'Create a new example',
      description: 'Creates a new example with the provided data',
      body: createExampleSchema,
      response: responses.create,
    }),
    handler: exampleController.createExample,
  });
  
  fastify.route({
    method: 'PUT',
    url: '/examples/:id',
    preHandler: fastify.authenticate,
    ...documentProtectedRoute({
      tag: 'Examples',
      summary: 'Update an example',
      description: 'Updates an existing example with the provided data',
      params: exampleParamsSchema,
      body: updateExampleSchema,
      response: responses.update,
    }),
    handler: exampleController.updateExample,
  });
  
  fastify.route({
    method: 'DELETE',
    url: '/examples/:id',
    preHandler: fastify.authenticate,
    ...documentProtectedRoute({
      tag: 'Examples',
      summary: 'Delete an example',
      description: 'Deletes an existing example',
      params: exampleParamsSchema,
      response: responses.delete,
    }),
    handler: exampleController.deleteExample,
  });
}

module.exports = exampleDocumentedRoute;
