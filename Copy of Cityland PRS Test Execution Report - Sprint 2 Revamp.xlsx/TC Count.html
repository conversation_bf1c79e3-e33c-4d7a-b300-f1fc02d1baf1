<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b7b7b7;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:12pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{background-color:#ea9999;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{background-color:#ffffff;text-align:left;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-right:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{background-color:#000000;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-right:1px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:left;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="836128058C0" style="width:687px;" class="column-headers-background">A</th><th id="836128058C1" style="width:100px;" class="column-headers-background">B</th><th id="836128058C3" style="width:132px;" class="column-headers-background">D</th><th id="836128058C4" style="width:106px;" class="column-headers-background">E</th><th id="836128058C5" style="width:106px;" class="column-headers-background">F</th><th id="836128058C6" style="width:106px;" class="column-headers-background">G</th></tr></thead><tbody><tr style="height: 31px"><th id="836128058R0" style="height: 31px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 31px">1</div></th><td class="s0" colspan="2"> </td><td class="s1"></td><td></td><td class="s1">new</td><td class="s1"></td></tr><tr style="height: 42px"><th id="836128058R1" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">2</div></th><td class="s2">Stories to be Tested</td><td class="s3">Total Tests</td><td class="s1"></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="836128058R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-001 - [Login] - Cityland PRS Landing Page, Login, Password Reset, Session Timeout</td><td class="s5">42</td><td class="s1"></td><td class="s6">Aira</td><td class="s6">Ghienel</td><td class="s6"></td></tr><tr style="height: 19px"><th id="836128058R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-002 - [User Types]</td><td class="s5">12</td><td class="s1"></td><td class="s6">Verna</td><td class="s6">Justine</td><td class="s6"></td></tr><tr style="height: 19px"><th id="836128058R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-003 - [User Management] - Manage IT Admin and Root User, Account Creation, User Deactivation</td><td class="s5">39</td><td class="s1"></td><td class="s6">Ann</td><td class="s6">ghienel</td><td class="s6"></td></tr><tr style="height: 30px"><th id="836128058R5" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">6</div></th><td class="s4">PRS-004 - [Manage Supplier] - View and Edit Supplier Details, Attachment, Notes, Activate/Suspend Supplier</td><td class="s5">20</td><td class="s7"></td><td class="s8">Kurt</td><td class="s6">Regel</td><td class="s6"></td></tr><tr style="height: 21px"><th id="836128058R6" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">7</div></th><td class="s4">PRS-005 - [Manage Company] - Create, View, Update and Delete of Association</td><td class="s5">16</td><td class="s7"></td><td class="s8">Jeric</td><td class="s6">Aira</td><td class="s6"></td></tr><tr style="height: 19px"><th id="836128058R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-006 - [Manage Project] - Pull data, View Project Details, Assigning/Updating Approvers, Optional Approvers, Add/Update Engineers per Trade</td><td class="s5">18</td><td class="s7"></td><td class="s6">Gela</td><td class="s6">Verna</td><td class="s6"></td></tr><tr style="height: 19px"><th id="836128058R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-007 - [Manage Department] - Pull data, Assigning &amp; Updating of Assigned Approvers, Optional Approvers for Association Department </td><td class="s5">19</td><td class="s7"></td><td class="s8">Kams</td><td class="s6">Ann</td><td class="s6"></td></tr><tr style="height: 21px"><th id="836128058R9" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">10</div></th><td class="s4">PRS-008 - [Manage Items] - View, Create, Update OFM Items, OFM List and Non-OFM Items, Search, Filter</td><td class="s5">18</td><td class="s7"></td><td class="s6">Cherry</td><td class="s6">Gela</td><td class="s6"></td></tr><tr style="height: 21px"><th id="836128058R10" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">11</div></th><td class="s4">PRS-009 - [Manage Requisition Slip] - Create Requistion Slip for Non-OFM, Transfer of Materials, OFM</td><td class="s5">6</td><td class="s7"></td><td class="s6">Ann</td><td class="s6">Kurt</td><td class="s6"></td></tr><tr style="height: 21px"><th id="836128058R11" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">12</div></th><td class="s4">PRS-010 - [RS Dashboard] - Dashboard view, Search, Filter, Pagination</td><td class="s5">16</td><td class="s7"></td><td class="s6">Cherry</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 21px"><th id="836128058R12" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">13</div></th><td class="s4">PRS-011 - [RS Creation] - Submit &amp; Draft Requisition Slip, Add notes, Upload attachment</td><td class="s5">29</td><td class="s7"></td><td class="s6">Cherry</td><td class="s1">verna</td><td class="s1"></td></tr><tr style="height: 21px"><th id="836128058R13" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">14</div></th><td class="s4">PRS-012 - [RS Viewing] - Search, Filter, View Attachment, View Notes, Related Documents, Edit Requsition Slip</td><td class="s5">22</td><td class="s7"></td><td class="s6">Cherry</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 21px"><th id="836128058R14" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">15</div></th><td class="s4">PRS-013 - [RS Approval] - Approve, Reject, Re-Submit, Re-Approve, Cancel, Approver Updates</td><td class="s5">23</td><td class="s7"></td><td class="s8">Kams</td><td class="s1">ann</td><td class="s1"></td></tr><tr style="height: 21px"><th id="836128058R15" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">16</div></th><td class="s4">PRS-014 - [RS Assigning] - Assign RS to me, Assign RS to others</td><td class="s5">5</td><td class="s7"></td><td class="s1">Gela</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-015 - [Requisition Slip] - Updating of Requisition Slip during Approval, Cancel created RS, Update RS Number Format for RS Draft</td><td class="s5">33</td><td class="s7"></td><td class="s1">Gela</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 21px"><th id="836128058R17" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">18</div></th><td class="s4">PRS-016 - [Manage Items in RS] -  Adding of Item</td><td class="s5">8</td><td class="s7"></td><td class="s8">Jeric</td><td class="s1">aira</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s9">PRS-017 - [Canvassing] - Viewing of Canvass Sheet by Approver, Approving of Assigned Purchasing Staff Supervisor , Supplier selection of Purchasing Head for OFM and Non-OFM Request Type, Approving of Management, Creating a Canvass - Attachments are clickable, Allow Manual overriding of Canvass Quantity, Assigning of Canvass Sheet Approvers with an existing Canvass Sheet, Adding of Items for Canvass Sheet, Supplier for Transfer of Materials</td><td class="s5">53</td><td class="s7"></td><td class="s8">Jeric</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 21px"><th id="836128058R19" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">20</div></th><td class="s9">PRS-018 - [History] - OFM Items History</td><td class="s5">7</td><td class="s7"></td><td class="s1">Ann</td><td class="s1">aira</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s9">PRS-019 - [Syncing] - Supplier Sync Scenario, Suspending a Supplier, Company Sync Scenario,</td><td class="s5">31</td><td class="s10"></td><td class="s1">Ann</td><td class="s1">verna</td><td class="s1">A</td></tr><tr style="height: 36px"><th id="836128058R21" style="height: 36px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 36px">22</div></th><td class="s9">PRS-020 - [Syncing] - OFM Item Sync Scenario, Assigning Alt Approver, Viewing of Leave,  Editing of Leave, Cancelling of Leave</td><td class="s5">68</td><td class="s10"></td><td class="s8">Kams</td><td class="s1">ann</td><td class="s1"></td></tr><tr style="height: 24px"><th id="836128058R22" style="height: 24px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 24px">23</div></th><td class="s9">PRS-021 - [Canvassing] Producing of PO after all Canvass Approval</td><td class="s5">6</td><td class="s10"></td><td class="s8">Jeric</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 49px"><th id="836128058R23" style="height: 49px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 49px">24</div></th><td class="s9">PRS-022 - [Delivery Receipt] Creation of Delivery Receipt per Supplier, Entering of Invoice in the Delivery Receipt, Viewing of Delivery Receipt per Supplier, Updating of Data based on Purchase Order for Delivery Receipt</td><td class="s5">25</td><td class="s10"></td><td class="s1">Cherry</td><td class="s1">kurt</td><td class="s1">D</td></tr><tr style="height: 24px"><th id="836128058R24" style="height: 24px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 24px">25</div></th><td class="s9">PRS-023 - [History] Non-OFM History, RS Related Documents, Request History</td><td class="s5">36</td><td class="s10"></td><td class="s1">Ann</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s9">PRS-024 - [Steelbars] Tagging of Steelbars in OFM Item List, Adding of Dimensions for the Steelbars, RS with Steelbars, Canvassing for Steelbars Item Group,  Purchase Order for Steelbars, Payment Request for Steelbars , Delivery Record for Steelbars</td><td class="s5">104</td><td class="s10"></td><td class="s8">Kurt</td><td class="s1">Justine</td><td></td></tr><tr style="height: 19px"><th id="836128058R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s9">PRS-025 - [Purchase Order] - Viewing of Purchase Order List, Approving of PO, Rejecting of PO, Resubmitting of rejected PO, Purchase Order View For Approval , Viewing Purchase Order for Requester, Reviewing of Purchase Order by Assigned Purchasing Staff</td><td class="s5">117</td><td class="s10"></td><td class="s1">Verna</td><td class="s1">ann</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s9">PRS-026 - [Payment Request] - Scenario per Terms of the Supplier, Entering of Payment Request per Purchase Order, Viewing of Payment Request, Payment Request Approval - OFM Request, Payment Request Approval - Non-OFM Request, Payment Request Approval - Transfer of Materials, Payment Submission to Accounting, Assigning of Payment Request Approvers with an existing Payment Request</td><td class="s5">152</td><td class="s10"></td><td class="s1">Aira</td><td class="s1">cherry</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s9">PRS-027 - [Return items] Returning an Item, Recording the Delivered Item after return, Cancelling of Request to Return Item</td><td class="s5">39</td><td class="s10"></td><td class="s1">Ann</td><td class="s1">ann</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s9">PRS-028 - [Enhancements_1]  Root User - Search Function for User Management Landing Page<br>IT Admin - Search Function for User Management Landing Page<br>User Management: IT Admin - Add User Types Management - only 2 Users will be assigned<br>Sorting for Department Columns<br>Sorting for User Type and Department Columns<br>OFM Items: Landing Page - Sorting of Table<br>Non-OFM: View Non-OFM - Enable RS Search in Item Table<br>Non-OFM: Enable Filter Function in Landing Page<br>RS Dashboard: : Update RS Number to Reference Number<br>RS Creation: Update Reference Number for Draft</td><td class="s5">20</td><td class="s10"></td><td class="s6">Cherry</td><td class="s1">Ghienel</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s9">PRS-029 - [Enhancements_2] - Manage Company: Allow Editing only for Association<br>Non-OFM: Update Dropdown Function for Units<br>RS Dashboard: RS Dashboard - Sorting of RS Table<br>RS Creation: Error Message Enhancement for required Field<br>RS Viewing: Add criteria for Searching in the Item Table<br>OFM Items: Manage of pulled Data from Cityland<br>Tagging of Steelbars in upon Syncing<br>OFM Items: Editing of OFM Item Units<br>RS Creation: Updating of Item View when adding an Item during OFM Request creation<br>Allow editing or deleting of Additional Approvers for RS Approval<br>Allow combobox Drop-down Fields for all Fields in PRS<br>Retaining of selected sorting to the Tables of PRS</td><td class="s5">54</td><td class="s10"></td><td class="s8">Jeric</td><td class="s1">justine</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s9">PRS-030 - [Remaining AC]  - Updated Attachment Filtering in Supplier Management, Download in Dashboard, Enable Searching in Dashboard Page, Selection of Date in Date Picker in RS Creation</td><td class="s5">74</td><td class="s10"></td><td class="s8">Kurt</td><td class="s1">ghienel</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s9">PRS-031- [NON-RS PAYMENT REQUEST] - Non-RS Dashboard, Mapping of Approvers for Non-RS Payment, Non-RS Payment Creation, Non-RS Payment Approval, Non-RS Payment Viewing, Editing during Non-RS Approval,  Non-RS Payment Rejecting, Resubmitting of rejected Non-RS</td><td class="s5">112</td><td class="s10"></td><td class="s1">Gela</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s9">PRS-032 - [M2 Feedback] - Mockup Data Cleanup for Company                                                <br>Update the behavior of Charge To, Company, Project and DepartmentInclude Company Intials in the Drop-down Values for Company when creating a Requisition Slip                                                <br>Update the functionality of the Drop-down Fields in Requisition Slip Creation<br>Updating of Draft Reference Number when updating the Company in the Requisition Slip<br>Updating of Draft Requisition Slip should only be done by the Requestor        </td><td class="s5">47</td><td class="s10"></td><td class="s8">Kams</td><td class="s1">gela</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s9">PRS-033 - [Enhancements_3} -  User Types: Add Management User Type, Manage Department: Enhance Error Messaging for blank Approvers during Submission, Manage Projects: Approver Assignment - Enhance Error Messaging for blank Approvers during Submission                                        </td><td class="s5">4</td><td class="s11"></td><td class="s8">Cherry</td><td class="s1">regel</td><td class="s1"></td></tr><tr style="height: 19px"><th id="836128058R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s9">PRS-031 - Defect Retest - M3 - Critical Only</td><td class="s12"></td><td class="s5">63</td><td class="s10"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="836128058R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s9">PRS-032 - Defect Retest - M2 - Critical Only</td><td class="s12"></td><td class="s5">26</td><td class="s10"></td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="836128058R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s13">TOTAL</td><td class="s14">1275</td><td class="s10"></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="836128058R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td></td><td></td><td class="s15"></td><td></td><td></td><td></td></tr><tr style="height: 63px"><th id="836128058R39" style="height: 63px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 63px">40</div></th><td class="s16">*Note: Some stories not added here as they are DEV related and no need for TC creation and QA Testing. See TC creation Status file for the list.</td><td></td><td class="s15"></td><td></td><td></td><td></td></tr></tbody></table></div>