import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/apiClient';

export const getPurchaseOrderItems = id => {
  
  return api.get(`/v1/purchase-orders/${id}/items?type=receiving`);
};

export const useGetPurchaseOrderItems = (id, config = {}) => {
  return useQuery({
    queryKey: ['PurchaseOrderItems', id],
    queryFn: () => getPurchaseOrderItems(id),
    refetchOnMount: 'always',
    enabled: !!id,
    ...config,
  });
};
