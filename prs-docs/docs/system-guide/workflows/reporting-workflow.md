# Reporting Workflow

This document outlines the complete workflow for generating and managing reports in the PRS system.

## Workflow Diagram

```
┌──────────────┐     ┌───────────┐     ┌──────────┐
│ CONFIGURATION │────▶│ GENERATION │────▶│ DELIVERY │
└──────────────┘     └───────────┘     └──────────┘
        │                  ▲
        ▼                  │
┌──────────────┐     ┌───────────┐
│  SCHEDULING  │────▶│  STORAGE  │
└──────────────┘     └───────────┘
```

## Report Types

| Report Type | Description |
|-------------|-------------|
| Requisition Reports | Reports on requisition status, volume, and trends |
| Supplier Reports | Reports on supplier performance, spending, and status |
| Purchase Order Reports | Reports on purchase orders, delivery status, and spending |
| User Activity Reports | Reports on user actions, approvals, and system usage |
| Financial Reports | Reports on spending, budgets, and financial metrics |
| Audit Reports | Reports for compliance and audit purposes |

## Detailed Workflow Steps

### 1. Report Configuration

**Actor**: Admin or User with Reporting Permissions

**Actions**:
- Select report type
- Configure report parameters
- Set filters and criteria
- Choose output format
- Set access permissions

**Business Rules**:
- User must have permissions for the selected report type
- Some report parameters may be required
- Date ranges must be valid
- Complex reports may have limits on data volume

### 2. Report Scheduling

**Actor**: Admin or User with Reporting Permissions

**Actions**:
- Set report generation schedule
- Configure recurrence pattern
- Set delivery options
- Define recipients

**Business Rules**:
- Scheduled reports must have valid parameters
- Recurrence patterns must be valid
- Recipients must have permissions to view the report
- System resources may limit concurrent scheduled reports

### 3. Report Generation

**Actor**: System or User

**Actions**:
- Process report request
- Query data based on parameters
- Apply business logic and calculations
- Format output according to specifications

**Business Rules**:
- Large reports may be processed asynchronously
- Data access is limited by user permissions
- Generation time may be limited for on-demand reports
- Failed generations should be logged and notified

### 4. Report Storage

**Actor**: System

**Actions**:
- Store generated report
- Apply retention policies
- Index for searchability
- Apply access controls

**Business Rules**:
- Reports are stored according to retention policy
- Access to stored reports is controlled by permissions
- Storage space may be limited
- Sensitive data may be redacted in stored reports

### 5. Report Delivery

**Actor**: System

**Actions**:
- Deliver report to specified recipients
- Send notifications
- Track delivery status
- Handle delivery failures

**Business Rules**:
- Delivery methods depend on report type and configuration
- Recipients must have permissions to access the report
- Large reports may be delivered as links rather than attachments
- Delivery failures should be logged and retried

## Example Scenarios

### Scenario 1: On-Demand Requisition Status Report

1. User navigates to the reporting module
2. User selects "Requisition Status Report"
3. User configures date range, departments, and status filters
4. User selects Excel format and clicks "Generate"
5. System processes the report and presents it for download
6. User downloads and views the report

### Scenario 2: Scheduled Supplier Performance Report

1. Admin configures a monthly "Supplier Performance Report"
2. Admin sets the schedule for the 1st of each month
3. Admin configures email delivery to department heads
4. On the scheduled date, system generates the report
5. System emails the report to recipients
6. System stores a copy in the report archive

### Scenario 3: Financial Audit Report

1. Auditor requests a comprehensive financial report
2. Admin configures a custom report with specific parameters
3. Admin sets high priority for generation
4. System processes the report (may take longer due to complexity)
5. System notifies admin when report is ready
6. Admin reviews and delivers the report to the auditor

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/reportController.js`
- **Service**: `src/app/services/reportService.js`
- **Repository**: `src/infra/repositories/reportRepository.js`
- **Entity**: `src/domain/entities/reportEntity.js`
- **Constants**: `src/domain/constants/reportConstants.js`

### Report Generation Implementation

```javascript
// Example report generation logic
async function generateReport(reportConfig, userId, transaction) {
  // Validate report configuration
  validateReportConfig(reportConfig);
  
  // Check user permissions
  await checkReportPermissions(reportConfig, userId);
  
  // Create report record
  const report = await reportRepository.create({
    name: reportConfig.name,
    type: reportConfig.type,
    parameters: JSON.stringify(reportConfig.parameters),
    format: reportConfig.format,
    createdBy: userId,
    status: 'PROCESSING',
  }, { transaction });
  
  // For asynchronous processing
  if (isComplexReport(reportConfig)) {
    // Queue report for background processing
    await queueReportGeneration(report.id);
    
    return {
      reportId: report.id,
      status: 'QUEUED',
      message: 'Report generation has been queued. You will be notified when it is ready.',
    };
  }
  
  // For synchronous processing
  try {
    // Generate report data
    const reportData = await generateReportData(reportConfig);
    
    // Format report
    const formattedReport = formatReport(reportData, reportConfig.format);
    
    // Store report
    const reportPath = await storeReport(report.id, formattedReport, transaction);
    
    // Update report record
    await report.update({
      status: 'COMPLETED',
      filePath: reportPath,
      completedAt: new Date(),
    }, { transaction });
    
    return {
      reportId: report.id,
      status: 'COMPLETED',
      reportPath,
    };
  } catch (error) {
    // Handle generation error
    await report.update({
      status: 'FAILED',
      errorMessage: error.message,
    }, { transaction });
    
    throw error;
  }
}

// Generate report data based on type and parameters
async function generateReportData(reportConfig) {
  switch (reportConfig.type) {
    case 'REQUISITION_STATUS':
      return generateRequisitionStatusReport(reportConfig.parameters);
    
    case 'SUPPLIER_PERFORMANCE':
      return generateSupplierPerformanceReport(reportConfig.parameters);
    
    case 'PURCHASE_ORDER_SUMMARY':
      return generatePurchaseOrderSummaryReport(reportConfig.parameters);
    
    case 'USER_ACTIVITY':
      return generateUserActivityReport(reportConfig.parameters);
    
    case 'FINANCIAL_SUMMARY':
      return generateFinancialSummaryReport(reportConfig.parameters);
    
    default:
      throw new Error(`Unsupported report type: ${reportConfig.type}`);
  }
}
```

## Common Issues and Solutions

### Issue 1: Report Generation Timeout

**Cause**: Report query is too complex or returns too much data.

**Solution**:
- Narrow the report parameters (e.g., smaller date range)
- Use filters to reduce data volume
- Schedule the report for background processing
- Break into multiple smaller reports

### Issue 2: Missing or Incorrect Data

**Cause**: Data quality issues or incorrect parameters.

**Solution**:
- Verify report parameters
- Check data sources for completeness
- Review business logic for calculations
- Consider adding data validation steps

### Issue 3: Access Denied to Reports

**Cause**: Permission issues or incorrect report configuration.

**Solution**:
- Verify user permissions for the report type
- Check report access configuration
- Ensure user has access to underlying data
- Review role-based access controls
