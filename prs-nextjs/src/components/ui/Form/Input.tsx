"use client";

import React, { useState, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { FieldWrapper } from './FieldWrapper';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  registration?: any;
  hasIcon?: boolean;
  renderIcon?: React.ElementType | null;
  iconClassName?: string;
  isSearch?: boolean;
  beforeIcon?: React.ElementType | null;
  iconHandler?: () => void;
  columnSpan?: number;
  minDate?: string;
  maxDate?: string;
  maxNumberLength?: number;
  numberOnly?: boolean;
  noNegative?: boolean;
  isParent?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      label,
      error,
      registration,
      placeholder,
      hasIcon = false,
      renderIcon: RenderIcon = null,
      iconClassName = '',
      isSearch = false,
      beforeIcon: BeforeIcon = null,
      iconHandler = () => { },
      columnSpan = 1,
      minDate,
      maxDate,
      maxNumberLength,
      numberOnly,
      noNegative = false,
      isParent = false,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [inputType, setInputType] = useState(type);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
      setInputType(showPassword ? 'password' : 'text');
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (numberOnly) {
        // Allow only numbers, backspace, delete, tab, arrows, home, end
        if (
          !/[0-9]/.test(e.key) &&
          e.key !== 'Backspace' &&
          e.key !== 'Delete' &&
          e.key !== 'Tab' &&
          e.key !== 'ArrowLeft' &&
          e.key !== 'ArrowRight' &&
          e.key !== 'ArrowUp' &&
          e.key !== 'ArrowDown' &&
          e.key !== 'Home' &&
          e.key !== 'End' &&
          e.key !== '.' &&
          e.key !== '-'
        ) {
          e.preventDefault();
        }

        // Prevent negative numbers if noNegative is true
        if (noNegative && e.key === '-') {
          e.preventDefault();
        }
      }
    };

    const inputContent = (
      <div
        className={cn(
          'relative rounded-md shadow-sm',
          error && 'border-red-500',
          isParent && 'w-full'
        )}
      >
        {BeforeIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <BeforeIcon className="h-5 w-5 text-gray-400" />
          </div>
        )}

        <input
          type={inputType}
          className={cn(
            'block w-full rounded-xl border border-gray-300 shadow-sm focus:border-[#754445] focus:ring-[#754445] text-sm py-2.5 px-3',
            BeforeIcon && 'pl-10',
            (hasIcon || type === 'password' || isSearch) && 'pr-10',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            className
          )}
          placeholder={placeholder}
          ref={ref}
          min={minDate}
          max={maxDate}
          maxLength={maxNumberLength}
          onKeyDown={handleKeyDown}
          {...registration}
          {...props}
        />

        {isSearch && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <SearchIcon className="h-5 w-5 text-gray-400" />
          </div>
        )}

        {type === 'password' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              {showPassword ? (
                <VisibleEyeIcon className="h-5 w-5" />
              ) : (
                <NonVisibleEyeIcon className="h-5 w-5" />
              )}
            </button>
          </div>
        )}

        {hasIcon && RenderIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <RenderIcon
              className={cn('h-5 w-5 text-gray-400 cursor-pointer', iconClassName)}
              onClick={iconHandler}
            />
          </div>
        )}
      </div>
    );

    return label ? (
      <FieldWrapper label={label} error={error} columnSpan={columnSpan}>
        {inputContent}
      </FieldWrapper>
    ) : (
      inputContent
    );
  }
);

Input.displayName = 'Input';

export { Input };

// Simple icon components
function SearchIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
  );
}

function VisibleEyeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
      />
    </svg>
  );
}

function NonVisibleEyeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
      />
    </svg>
  );
}
