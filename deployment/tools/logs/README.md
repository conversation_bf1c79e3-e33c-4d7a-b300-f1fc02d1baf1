# PRS Logs Viewer

A beautiful log viewer for PRS containers that makes error logs easy to read and understand.

## Features

- Color-coded log levels (ERROR, WARN, INFO, DEBUG)
- Formatted error messages with clear separation
- Shortened stack traces for better readability
- Easy filtering by container
- Follow mode for real-time log monitoring
- Line limiting to focus on recent logs

## Installation

The log viewer is already installed in the `/home/<USER>/prs-prod/deployment` directory. It consists of two scripts:

- `pretty-logs.sh`: The main script that formats and displays logs
- `logs`: A wrapper script that provides a simpler interface

## Usage

```bash
./logs [options] [container]
```

### Options

- `-h, --help`: Display help message
- `-f, --follow`: Follow log output in real-time
- `-n, --lines N`: Display the last N lines (default: 20)
- `-a, --all`: Display logs from all containers

### Containers

- `backend`: Backend container (default)
- `frontend`: Frontend container
- `db`: Database container
- `nginx`: Nginx container

### Examples

```bash
# Display backend logs (default)
./logs

# Follow backend logs
./logs -f

# Display last 50 lines of backend logs
./logs -n 50

# Display frontend logs
./logs frontend

# Follow frontend logs
./logs -f frontend

# Display logs from all containers
./logs -a
```

## Understanding the Output

The log viewer formats logs to make them easier to read:

### Regular Logs

Regular logs are displayed with a timestamp and log level:

```
[2025-05-10 03:53:01] INFO Server listening at http://127.0.0.1:4000
```

### Error Logs

Error logs are displayed with more detail:

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[2025-05-10 03:53:09] ERROR
Error Type: DATABASE_ERROR
Error Code: DATABASE_ERROR
Message: Database error during /v1/auth/login operation on v1
Request ID: 0196b853-4c75-7005-9877-739d3785cb2b
Original Error: The server does not support SSL connections
Stack Trace:
DatabaseError: Database error during /v1/auth/login operation on v1
    at ErrorTranslator.translateSequelizeError (/usr/app/src/app/errors/errorTranslator.js:97:16)
    at ErrorTranslator.translate (/usr/app/src/app/errors/errorTranslator.js:38:19)
    at Object.errorHandler (/usr/app/src/app/errors/errorHandler.js:32:32)
    at handleError (/usr/app/node_modules/fastify/lib/error-handler.js:69:20)
```

## Customization

If you need to customize the log viewer, you can edit the following files:

- `/home/<USER>/prs-prod/deployment/pretty-logs.sh`: Modify the formatting and parsing logic
- `/home/<USER>/prs-prod/deployment/logs`: Modify the command-line interface

## Troubleshooting

If you encounter any issues with the log viewer, try the following:

1. Make sure the scripts are executable:
   ```bash
   chmod +x /home/<USER>/prs-prod/deployment/pretty-logs.sh
   chmod +x /home/<USER>/prs-prod/deployment/logs
   ```

2. Check if the container is running:
   ```bash
   docker ps | grep deployment
   ```

3. Try viewing the raw logs:
   ```bash
   docker logs deployment-backend-1
   ```
