<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="875835347C0" style="width:103px;" class="column-headers-background">A</th><th id="875835347C1" style="width:98px;" class="column-headers-background">B</th><th id="875835347C2" style="width:252px;" class="column-headers-background">C</th><th id="875835347C3" style="width:252px;" class="column-headers-background">D</th><th id="875835347C4" style="width:233px;" class="column-headers-background">E</th><th id="875835347C5" style="width:330px;" class="column-headers-background">F</th><th id="875835347C6" style="width:184px;" class="column-headers-background">G</th><th id="875835347C7" style="width:346px;" class="column-headers-background">H</th><th id="875835347C8" style="width:100px;" class="column-headers-background">I</th><th id="875835347C9" style="width:137px;" class="column-headers-background">J</th><th id="875835347C10" style="width:114px;" class="column-headers-background">K</th><th id="875835347C11" style="width:114px;" class="column-headers-background">L</th><th id="875835347C12" style="width:114px;" class="column-headers-background">M</th><th id="875835347C13" style="width:114px;" class="column-headers-background">N</th><th id="875835347C14" style="width:78px;" class="column-headers-background">O</th><th id="875835347C15" style="width:72px;" class="column-headers-background">P</th><th id="875835347C16" style="width:100px;" class="column-headers-background">Q</th></tr></thead><tbody><tr style="height: 42px"><th id="875835347R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td><td class="s4"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="875835347R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s5" colspan="16">PRS-006 - [Manage Project] - View Project Details, Assigning/Updating Approvers, Add/Update Engineers per Trade</td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Projects Landing Page</td><td class="s6"></td><td class="s6">Validate Manage Project Landing Page</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Check Project list page display<br>4. Validate Search Field and Search button<br>5. Validate Clear button<br>6. Validate sorting of each columns</td><td class="s8"></td><td class="s6"><span style="font-family:Poppins,Arial;color:#000000;">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List  page <br>3. Should have the ff:<br>     a. Search Field that able to search Project Name<br>     b. &quot;Search&quot; and &quot;Clear&quot; buttons<br>     c. Should display a Table list of Projects under Cityland<br>        - Project Name<br>        - Project Code<br>        - Project Initials<br>        - Company<br>        - Project Address<br></span><span style="font-family:Poppins,Arial;text-decoration:line-through;color:#000000;">        - Actions</span><span style="font-family:Poppins,Arial;color:#000000;"><br>       i. Edit<br>    d. Should display 10 Projects per Page<br>    e. Should have Sync Button<br>4. Should be able to search Project Name<br>5. Should be able to delete entered words in Search field<br>6.. Should be able to Sort each Columns<br>    a. Project Name<br>        i. Default sorting by: A-Z<br>        ii. Should be able to sort: A-Z, Z-A<br>    b. Project Code<br>        i. Should be able to sort: 0-9, 9-0<br>    c. Project Initials<br>        i. Should be able to sort: A-Z, Z-A<br>    d. Company<br>        i. Should be able to sort: A-Z, 0-9 || Z-A,9-0<br>    e. Project Address<br>        i. Should be able to sort: A-Z, 0-9 || Z-A,9-0<br></span></td><td class="s6" rowspan="16"></td><td class="s9">Failed</td><td class="s10">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-784">https://youtrack.stratpoint.com/issue/CITYLANDPRS-784</a></td><td class="s4">kurt: action not available in projects anymore</td></tr><tr style="height: 19px"><th id="875835347R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Pull Project Data</td><td class="s6"></td><td class="s6">Validate Manage Project Pull Project data when Project is not on the list</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. Project Master File integration has been setup</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Sync&quot; button on Project List page </td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should initiate syncing of Projects from Project Master File and add the Project in the List.<br>4. Should display the Date and Time of the last triger of the Sync Button<br>5. Should return back to Page 1 of the Table after syncing<br>6. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s12">Not Run</td><td class="s13">Failed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-792">https://youtrack.stratpoint.com/issue/CITYLANDPRS-792</a></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Pull Project Data</td><td class="s6"></td><td class="s6">Validate Manage Project Pull Project data when Project has already on the file</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. Project Master File integration has been setup<br>6. An update of existing project has been made</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Sync&quot; button on Project List page </td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should initiate syncing of Projects from Project Master File and update the Project Details if an update on the existing Project has been made in the Project Master File<br>4. Should display the Date and Time of the last triger of the Sync Button<br>5. Should return back to Page 1 of the Table after syncing<br>6. Should disable Sync Button once clicked<br>    i. Disabled until fully loaded the Data<br>    ii. Enable after fully loaded and Date and Time have been updated</td><td class="s12">Not Run</td><td class="s14">Blocked</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-792">https://youtrack.stratpoint.com/issue/CITYLANDPRS-792</a></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Viewing of Projects</td><td class="s6"></td><td class="s6">Validate Manage Project Viewing of Projects</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. Project Master File integration has been setup<br>6. An update of existing company has been made</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display Project Details View page<br>4. Should display a non-editable Fields for<br>    a. Project Name<br>    b. Project Code<br>    c. Project Initials<br>    d. Project Address<br>    e. Company linked to the Project<br>5. Should have a section for Trade Management<br>    a. Should have an Edit Button that will allow the Management of Trades<br>6. Should have a section for  Requests toAssigning of Approvers<br>    a. Should have an Edit Button that will allow the Management of Approvers</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Viewing of Projects</td><td class="s6"></td><td class="s6">Validate Manage Project Viewing of Trades Trade Management</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display a two separate Tabs for Trade Management<br>    a. Major<br>        i. Civil and Arichitecture Works<br>        ii. Mechanical Works<br>        iii. Electrical Works<br>        iv. Plumbing and Sanitary<br>        v. Fire and Protection Works<br>    b. Sub<br>        i. Bored Piles Work<br>        ii. Substructure Works<br>5. Should allow viewing of Users per Trade<br>6. Should allow Searching of Users per Trade</td><td class="s12">Passed</td><td class="s13">Failed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-793">https://youtrack.stratpoint.com/issue/CITYLANDPRS-793</a></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Manage Adding of Engineers per Trade</td><td class="s6"></td><td class="s6">Validate Manage Project for Adding of Engineers per Trade</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. Projects has been synced<br>6. Trade Management has been setup per Project</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Trade Management section<br>5. Click &quot;Add Users&quot; button on Trade Management section<br>6. Select one Trade on Select Trade dropdown field<br>7. Enter a user and click Search button<br>8. Select multiple users<br>9. Click &quot;Add User/s&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display a two separate Tabs for Trade Management<br>    a. Major<br>        i. Civil and Arichitecture Works<br>        ii. Mechanical Works<br>        iii. Electrical Works<br>        iv. Plumbing and Sanitary<br>        v. Fire and Protection Works<br>    b. Sub<br>        i. Bored Piles Work<br>        ii. Substructure Works<br>5. Should display an &quot;Add Users&quot; Modal that contains the ff:<br>    a. A description &quot;Please select the user/s for canvassing from the list below. Make sure all items selected are correct. Press Add User/s to continue&quot;<br>    b. A &quot;Select Trade&quot; dropdown field<br>    c. &quot;Search User&quot; Name text field<br>       i. Search button to searched the entered user<br>       ii. Clear button to delete entered user<br>    d. Lists of users<br>    e. A number of users selected <br>    f. A &quot;Cancel&quot; and &quot;Add User/s&quot; buttons<br>6. One trade should be selected<br>7. Entered User/s should be displayed<br>8. Should allow the IT Admin to select multipe users and displayed the total counts of users selected<br>9. A success toast message should be displayed and newly add user/s should be successfully added in the Trade Management Section </td><td class="s9">Failed</td><td class="s9">Failed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-793">https://youtrack.stratpoint.com/issue/CITYLANDPRS-793</a></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Manage Adding of Engineers per Trade</td><td class="s6"></td><td class="s6">Validate Manage Project for Adding of Engineers per Trade and clicked cancel on the modal</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. Projects has been synced<br>6. Trade Management has been setup per Project</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Trade Management section<br>5. Click &quot;Add Users&quot; button on Trade Management section<br>6. Select one Trade on Select Trade dropdown field<br>7. Enter a user and click Search button<br>8. Select multiple users<br>9. Click &quot;Cancel&quot; button on the modal<br>10. Click &quot;Continue&quot; button on the cancel changes modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display a two separate Tabs for Trade Management<br>    a. Major<br>        i. Civil and Arichitecture Works<br>        ii. Mechanical Works<br>        iii. Electrical Works<br>        iv. Plumbing and Sanitary<br>        v. Fire and Protection Works<br>    b. Sub<br>        i. Bored Piles Work<br>        ii. Substructure Works<br>5. Should display an &quot;Add Users&quot; Modal that contains the ff:<br>    a. A description &quot;Please select the user/s for canvassing from the list below. Make sure all items selected are correct. Press Add User/s to continue&quot;<br>    b. A &quot;Select Trade&quot; dropdown field<br>    c. &quot;Search User&quot; Name text field<br>       i. Search button to searched the entered user<br>       ii. Clear button to delete entered user<br>    d. Lists of users<br>    e. A number of users selected <br>    f. A &quot;Cancel&quot; and &quot;Add User/s&quot; buttons<br>6. One trade should be selected<br>7. Entered User/s should be displayed<br>8. Should allow the IT Admin to select multipe users and displayed the total counts of users selected<br>9. Should display a &quot;Cancel Changes&quot; modal with contains of the ff:<br>    a. A description &quot; You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;cancel&quot; and &quot;Continue&quot; buttons<br>10. Should cancel adding of User to the Trade Management section</td><td class="s15">Blocked</td><td class="s13">Failed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-793">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1067<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-793</a></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Updating of assigned Engineers per Trade</td><td class="s6"></td><td class="s6">Validate Manage Project for Updating of assigned Engineers per Trade</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. Projects has been synced<br>6. Trade Management has been setup per Project<br>7. Engineers are already assigned in the Trade</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Trade Management section<br>5. Click &quot;x&quot;  icon button of user on Trade Management section<br>6. Click &quot;Cancel&quot; button on the modal<br>7. Click Click &quot;x&quot;  icon button again of user on Trade Management section<br>8. Click &quot;Continue&quot; button on the cancel changes modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display a two separate Tabs for Trade Management<br>    a. Major<br>        i. Civil and Arichitecture Works<br>        ii. Mechanical Works<br>        iii. Electrical Works<br>        iv. Plumbing and Sanitary<br>        v. Fire and Protection Works<br>    b. Sub<br>        i. Bored Piles Work<br>        ii. Substructure Works<br>5. Should display a &quot;Delete User&quot; Modal that contains the ff:<br>    a. A description &quot;You are about to remove this user in the selected trade. Press continue if you want to proceed with this action.&quot;<br>    b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>6. Should not be able to proceed on deleting a user <br>7. Should display a &quot;Delete User&quot; Modal again <br>8. Should displayed a success toast message and successfully deleted the User to the Trade Management section</td><td class="s15">Blocked</td><td class="s14">Blocked</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-793">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1067<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-793</a></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Assigning of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Assigning of Approvers for RS</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br><br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click Edit &gt; Add level &gt;  &quot;Add Approver&quot; button on specific level of Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Approver on Add Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected<br>6. Should display an &quot;Add Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>           i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should have a success toast message and display the newly added Approver  to the Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Assigning of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Assigning of Approvers for RS when clicked cancel on Project Approvers section</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br><br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Approvers sections<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Approver on Add Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Cancel&quot; button<br>13. Click &quot;Continue&quot; button on the modal<br>14. Validate that everytime cancel button is clicked, it should cancel the adding of approver and redirect back to Edit requests page</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected <br>6. Should display an &quot;Add Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>           i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s12">Passed</td><td class="s13">Failed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1070">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1070</a></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Viewing of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Viewing of Approvers for RS</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br><br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should have a section for Requests - Assigning of Approvers<br>    a. Should allow viewing by clicking the Accordion<br>        i. If without an Approver, should display a Note - &quot;Note: Only department heads can be assigned in this level.&quot;<br>        ii. If with an Approver, should display the Approver&#39;s Name</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Assigning of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Assigning of Optional Approvers for RS</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br><br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Optional Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level of Optional Approvers<br>9. Search and select an Approver on Add Optional Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected<br>6. Should display an &quot;Add Optional Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an optional approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>               i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Optional Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should have a success toast message and display the newly added Optional Approver to the Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="875835347R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s6"></td><td class="s6">[MANAGE PROJECT] Assigning of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Assigning of Optional Approvers for RS when clicked Cancel button</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br><br></a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Add Approver&quot; button on specific level of Optional Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Add Approver&quot; button again on same level<br>9. Search and select an Approver on Add Optional Approver modal<br>10. Click &quot;Add Approver&quot; button on the modal<br>11. Click &quot;Add Level&quot; button <br>12. Click &quot;Cancel&quot; button<br>13. Click &quot;Continue&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected<br>6. Should display an &quot;Add Optional Approval&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to add an optional approver. Please select your designated approver and press “Add Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>          i. Users with Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Add Approver&quot; buttons<br>7. Should cancel the adding of approver and redirect back to Edit requests page<br>8. Should display an &quot;Add Optional Approval&quot; Modal <br>9. Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should be able to add multiple Levels but should have only 1 approver or optional to add 1 optional approver should be added first<br>12. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>13. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s8"></td><td class="s8"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="875835347R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s8"></td><td class="s6">[MANAGE PROJECT] Updating of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Updating of Approvers for RS</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. An Approver has been already assigned</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>          i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should display a success toast message and should Save the new assigned approver to Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s8"></td><td class="s8"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="875835347R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s8"></td><td class="s6">[MANAGE PROJECT] Updating of Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Updating of Approvers for RS when clicked Cancel button</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. An Approver has been already assigned</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Cancel&quot; button<br>12. Click &quot;Continue&quot; button on the modal<br>13. Validate that everytime cancel button is clicked, it should cancel the adding of approver and redirect back to Edit requests page</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>          i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s8"></td><td class="s8"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="875835347R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s8"></td><td class="s6">[MANAGE PROJECT] Updating of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Updating of Optional Approvers for RS</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. An Optional Approver has been already assigned</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned Optional approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Optional Approver on Edit Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>          i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit Approver&quot; Modal <br>9. New Optional Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should display a success toast message and should Save the new assigned optional approver to Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s8"></td><td class="s8"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="875835347R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s17"></td><td class="s6">[MANAGE PROJECT] Updating of Optional Approvers for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Updating of Optional Approvers for RS when clicked Cancel button</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. An Optional Approver has been already assigned</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an Optional assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;Edit&quot; icon button on specific level of Optional Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Optional Approver on Edit  Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Cancel&quot; button<br>12. Click &quot;Continue&quot; button on the modal<br>13. Validate that everytime cancel button is clicked, it should cancel the adding of approver and redirect back to Edit requests page</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>         ii. Optional Approver has a One Level only<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the Optional approvers name<br>6. Should display an &quot;Edit Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>          i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit  Approver&quot; Modal <br>9. New Optional Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Cancel Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should not able to proceed to save changes and redirected back to Approvers List</td><td class="s6" rowspan="2"></td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="875835347R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s17"></td><td class="s6">[MANAGE PROJECT] Deleting of  Approvers and Level for RS</td><td class="s6"></td><td class="s6">Validate Manage Project for Deleting of  Approvers and Level for RS</td><td class="s7"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should have access to Manage Project page<br>5. An Optional Approver has been already assigned</a></td><td class="s6">1. Click &quot;Manage&quot; dropdown<br>2. Click &quot;Project&quot; sub menu<br>3. Click &quot;Project Name&quot; text link on Project List table<br>4. Click &quot;Edit&quot; button on Requests section<br>5. Select Any of the following workflow that has an assigned approver:<br>    -Requisition Slip<br>    -Canvassing<br>    -Purchase Order<br>    -Payment Request<br>6. Click &quot;X&quot; icon button on specific level of Approvers section<br>7. Click &quot;Go Back&quot; on the modal<br>8. Click &quot;Edit&quot; icon button again on same level <br>9. Search and select a new Optional Approver on Edit Approver modal<br>10. Click &quot;Update Approver&quot; button on the modal<br>11. Click &quot;Save&quot; button<br>12. Click &quot;Continue&quot; button on the modal</td><td class="s8"></td><td class="s6">1. A dropdown sub menus should be displayed:<br>    -Supplier<br>    -Company<br>    -Department<br>    -Project<br>2. Should display the Project List page <br>3. Should display sections of the Project View<br>    a. Project Details<br>    b. Trade Management<br>    c. Requests<br>4. Should display the ff:<br>    a.Add Level Button<br>       a. This adds another Level for Approving<br>         i. New Level will always be added as the Last Level<br>    b. Should display &quot;Add Approver&quot; button per Level<br>    c. Should display a Note below Level1 <br>        i. &quot;Note: Only department heads can be assigned in this level.&quot;<br>    d. Should display a Note for succeeding levels that has no approver/s <br>        i. &quot;No approvers in this level. Press add approver to assign a user.&quot;<br>    e. Should display a &quot;Cancel&quot; and &quot;Save&quot; buttons<br>5. Workflow should be selected and Should displayed an &quot;edit&quot; and &quot;delete&quot; icons beside the approvers name<br>6. Should display an &quot;Delete Approver&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to edit an approver. Please select your designated approver and press “Update Approver” if you want to proceed with this action.&quot;<br>     b. A &quot;Search Approval&quot; Search field<br>           i. Users with the Engineers User Type should be shown<br>     c. A&quot;Go Back&quot; and &quot;Update Approver&quot; buttons<br>7. Should cancel the updating of approver and redirect back to Edit requests page<br>8. Should display an &quot;Edit Approver&quot; Modal <br>9. New Optional Approver should be selected<br>10. Should be able to display the User to the Approvers List that is subject for submission<br>         i. Should not allow the same Approver and different Level on the same Workflow<br>11. Should display an &quot;Confirm Changes&quot; Modal with contains of the ff:<br>     a. A description &quot;You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>12. Should display a success toast message and should Save the new assigned optional approver to Approvers List</td><td class="s12">Passed</td><td class="s12">Passed</td><td class="s6"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s16"></td></tr></tbody></table></div>