const { ZodError } = require('zod');
const { Sequelize } = require('sequelize');
const AppError = require('./appError');
const ValidationError = require('./validationError');
const DatabaseError = require('./databaseError');
const NotFoundError = require('./notFoundError');
const BadRequestError = require('./badRequestError');
const AuthenticationError = require('./authenticationError');
const AuthorizationError = require('./authorizationError');
const ConflictError = require('./conflictError');

/**
 * Translates various error types to standardized AppError instances
 */
class ErrorTranslator {
  /**
   * Translates an error to an AppError
   * 
   * @param {Error} error - Error to translate
   * @param {Object} options - Translation options
   * @param {string} options.operation - Operation that caused the error
   * @param {string} options.resource - Resource type involved in the operation
   * @returns {AppError} - Translated error
   */
  static translate(error, options = {}) {
    // If it's already an AppError, return it
    if (error instanceof AppError) {
      return error;
    }

    // Handle Zod validation errors
    if (error instanceof ZodError) {
      return ValidationError.fromZodError(error);
    }

    // Handle Sequelize errors
    if (error instanceof Sequelize.Error) {
      return this.translateSequelizeError(error, options);
    }

    // Handle HTTP errors from the old system
    if (error.status && error.errorCode) {
      return this.translateHttpError(error);
    }

    // Handle generic errors
    return new AppError(
      error.message || 'An unknown error occurred',
      'UNKNOWN_ERROR',
      options,
      error
    );
  }

  /**
   * Translates a Sequelize error to an AppError
   * 
   * @param {Error} error - Sequelize error
   * @param {Object} options - Translation options
   * @returns {AppError} - Translated error
   */
  static translateSequelizeError(error, options = {}) {
    const { operation = 'database', resource = 'record' } = options;

    // Handle specific Sequelize error types
    switch (error.name) {
      case 'SequelizeUniqueConstraintError':
        return ConflictError.fromSequelizeUniqueConstraintError(
          error,
          resource
        );
      
      case 'SequelizeValidationError':
        const validationErrors = error.errors.reduce((acc, err) => {
          acc[err.path] = err.message;
          return acc;
        }, {});
        
        return new ValidationError(
          `Validation failed for ${resource}`,
          validationErrors,
          error
        );
      
      case 'SequelizeForeignKeyConstraintError':
        return new BadRequestError(
          `Cannot ${operation} ${resource} due to foreign key constraint`,
          { constraint: error.fields },
          error
        );
      
      case 'SequelizeEmptyResultError':
      case 'SequelizeInstanceError':
        return new NotFoundError(resource, null, null, error);
      
      default:
        return new DatabaseError(
          `Database error during ${operation} operation on ${resource}`,
          operation,
          error
        );
    }
  }

  /**
   * Translates an HTTP error from the old system to an AppError
   * 
   * @param {Object} error - HTTP error
   * @returns {AppError} - Translated error
   */
  static translateHttpError(error) {
    // Map old error codes to new error classes
    switch (error.errorCode) {
      case 'UNAUTHORIZED':
        return new AuthenticationError(error.message, 'INVALID_CREDENTIALS', error);
      
      case 'FORBIDDEN':
        return new AuthorizationError(error.message, null, error);
      
      case 'BAD_REQUEST':
        return new BadRequestError(error.message, error.description ? { description: error.description } : {}, error);
      
      case 'VALIDATION_ERROR':
      case 'UNPROCESSABLE_ENTITY':
        return new ValidationError(error.message, error.description ? { description: error.description } : {}, error);
      
      case 'NOT_FOUND':
        return new NotFoundError('Resource', null, error.message, error);
      
      case 'DATABASE_ERROR':
        return new DatabaseError(error.message, null, error);
      
      default:
        return new AppError(
          error.message || 'An unknown error occurred',
          error.errorCode || 'UNKNOWN_ERROR',
          error.description ? { description: error.description } : {},
          error
        );
    }
  }
}

module.exports = ErrorTranslator;
