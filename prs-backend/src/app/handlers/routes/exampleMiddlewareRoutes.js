/**
 * Example routes demonstrating the middleware composition system
 */
async function exampleMiddlewareRoutes(fastify, options) {
  const { 
    verifyAccessToken,
    verifyOTPToken,
    checkPermission,
    authenticateAndAuthorize,
    utils: { 
      compose, 
      conditional, 
      withLogging,
      with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    }
  } = fastify.diScope.resolve('middlewares');
  
  // Example controller
  const exampleController = {
    getPublicData: async (request, reply) => {
      return reply.send({ message: 'This is public data' });
    },
    
    getProtectedData: async (request, reply) => {
      const { userFromToken } = request;
      return reply.send({ 
        message: 'This is protected data',
        user: {
          id: userFromToken.id,
          username: userFromToken.username
        }
      });
    },
    
    getAdminData: async (request, reply) => {
      const { userFromToken } = request;
      return reply.send({ 
        message: 'This is admin data',
        user: {
          id: userFromToken.id,
          username: userFromToken.username,
          role: userFromToken.role?.name
        }
      });
    },
    
    verifyOtp: async (request, reply) => {
      const { userFromToken } = request;
      return reply.send({ 
        message: 'OTP verified successfully',
        user: {
          id: userFromToken.id,
          username: userFromToken.username
        }
      });
    },
    
    conditionalAccess: async (request, reply) => {
      const { userFromToken } = request;
      return reply.send({ 
        message: 'Conditional access granted',
        user: {
          id: userFromToken.id,
          username: userFromToken.username,
          role: userFromToken.role?.name
        }
      });
    }
  };
  
  // Define mock permissions for example
  const PERMISSIONS = {
    VIEW_ADMIN_DATA: { module: 'admin', action: 'view' },
    MANAGE_ADMIN_DATA: { module: 'admin', action: 'manage' }
  };
  
  // Example of a conditional middleware
  const isAdminUser = async function(request) {
    return request.userFromToken?.role?.name === 'admin';
  };
  
  const skipForAdmins = conditional(
    async function(request) {
      return !(await isAdminUser(request));
    },
    checkPermission(PERMISSIONS.VIEW_ADMIN_DATA)
  );
  
  // Example of a logging middleware
  const logAccess = withLogging(
    async function(request, reply) {
      const { userFromToken } = request;
      const logger = this.diScope.resolve('logger') || this.log;
      
      logger.info('Resource accessed', {
        userId: userFromToken?.id,
        username: userFromToken?.username,
        role: userFromToken?.role?.name,
        path: request.url
      });
    },
    { name: 'logAccess' }
  );
  
  // Example of an error handling middleware
  const handleCustomErrors = withErrorHandler(
    async function(request, reply) {
      // This middleware might throw errors
      const { query } = request;
      
      if (query.error === 'true') {
        throw new Error('This is a test error');
      }
    },
    async function(error, request, reply) {
      // Custom error handling
      const logger = this.diScope.resolve('logger') || this.log;
      
      logger.error('Custom error in middleware', {
        error: {
          message: error.message,
          stack: error.stack
        }
      });
      
      // Don't throw, just continue
    }
  );
  
  // Public route - no middleware
  fastify.get('/examples/public', {
    schema: {
      tags: ['Examples'],
      summary: 'Get public data',
      description: 'Returns public data without authentication',
    }
  }, exampleController.getPublicData);
  
  // Protected route - basic authentication
  fastify.get('/examples/protected', {
    preHandler: verifyAccessToken,
    schema: {
      tags: ['Examples'],
      summary: 'Get protected data',
      description: 'Returns protected data with authentication',
    }
  }, exampleController.getProtectedData);
  
  // Admin route - authentication and authorization
  fastify.get('/examples/admin', {
    preHandler: authenticateAndAuthorize(PERMISSIONS.VIEW_ADMIN_DATA),
    schema: {
      tags: ['Examples'],
      summary: 'Get admin data',
      description: 'Returns admin data with authentication and authorization',
    }
  }, exampleController.getAdminData);
  
  // OTP verification route
  fastify.post('/examples/verify-otp', {
    preHandler: verifyOTPToken,
    schema: {
      tags: ['Examples'],
      summary: 'Verify OTP',
      description: 'Verifies a one-time password token',
    }
  }, exampleController.verifyOtp);
  
  // Conditional access route
  fastify.get('/examples/conditional', {
    preHandler: compose(
      verifyAccessToken,
      skipForAdmins,
      logAccess,
      handleCustomErrors
    ),
    schema: {
      tags: ['Examples'],
      summary: 'Conditional access',
      description: 'Demonstrates conditional middleware execution',
    }
  }, exampleController.conditionalAccess);
}

module.exports = exampleMiddlewareRoutes;
