{"name": "prs-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^1.0.12", "@headlessui/react": "^2.2.2", "@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.7.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "^15.0.0", "next-auth": "^4.24.5", "otpauth": "^9.2.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.49.2", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^20.10.5", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.11", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "15.0.0", "playwright": "^1.52.0", "postcss": "^8.5.3", "prisma": "^5.7.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}