# Requisition Workflow

This document outlines the complete workflow for requisitions in the PRS system, from creation to closure.

## Workflow Diagram

```mermaid
  flowchart TD
    Start([Create or Edit RS]) -->|isDraft| S1[Draft]
    S1 -->|Edit| Start
    Start -->|Submitted| S2[For RS Approval]
    S2 -->|Fully Approved| S3[Assigning]
    S2 -->|Cancel RS| S9[RS Cancelled]
    S2 -->|Rejected| S4[RS Rejected]
    S4 -->|Return| Start
    S3 -->|Assigned| S5[Assigned]
    S5 -->|Canvass Entered| S6[RS in Progress]
    S6 -->|RS fulfilled| S8[Closed RS]
    S6 -->|Cancel RS| S7[RS Cancelled]
    S3 -->|Cancel RS| S10[RS Cancelled]
    S5 -->|Cancel RS| S11[RS Cancelled]

    %%S12[RS Cancellation is allowed if all PO latest status is still PO review or PO approval]
```

- RS status will remain FOR_RS_APPROVAL until fully approved

- RS Cancellation is allowed if all PO latest status is still PO review or PO approval


## Status Definitions

| Status | Description |
|--------|-------------|
| DRAFT | Initial status when a requisition is created but not submitted |
| FOR RS APPROVAL | Requisition has been submitted for approval |
| RS REJECTED | Requisition has been rejected by an approver during approval |
| ASSIGNING | All approvers have approved the requisition and will undergo purchasing staff assignment|
| ASSIGNED | Requisition has been assigned to a purchasing staff |
| RS IN PROGRESS | Requisition has atleast 1 canvass sheet submitted; will retain this status until closed or cancelled |
| CLOSED RS | All Purchase Orders are closed and no items left to canvass then requisition will be closed |
| RS CANCELLED | Requester has cancelled the request |


## Detailed Workflow Steps

### 1. Requisition Creation

**Actor**: Requester (All users except Root User)

**Actions**:

- Create a new requisition
- Fill in required fields (category, type, company, project, department, purpose, etc.)
- Add items to the requisition
- Save as draft or submit immediately
- Creator is set to the current user


**Status Transitions**:

- New → DRAFT (if saved as draft)
- New → FOR RS APPROVAL (if submitted immediately)

**Business Rules**:

- If submitted, all required fields must be filled
- If submitted, at least one item must be added
- RS Number is auto-generated when submitted
- Adding of items will be based on RS_TYPE
- Items must have a valid quantity, unit, and description
- OFM Items are filtered based on user's trade and project
- OFM Items quantity should not exceed remaining GFQ
- Items can have notes (optional)
- Items can be added, updated, or deleted while the requisition is in DRAFT status
- Steelbars are shown in a seperate tab
- Requested quantity will be deducted from item's Remaining GFQ once submitted

### 2. Requisition Submission

**Actor**: Requester

**Actions**:

- Update a draft or rejected requisition
- Submit the requisition for approval

**Status Transitions**:
    
- DRAFT → FOR RS APPROVAL
- REJECTED RS → FOR RS APPROVAL

**Business Rules**:
    
- All required fields must be filled
- At least one item must be added
- Approvers are assigned based on category, project, department and quantities (GFQs)
- Optional approvers are added if quantity is >80% of GFQ

### 3. Requisition Approval

**Actor**: Approvers

**Actions**:

- Review the requisition
- Approve or reject the requisition
- Add note of approval (optional) or rejection (required)
- Add additional approvers (optional)

**Status Transitions**:

- FOR RS APPROVAL → ASSIGNING (if all approvers approve)
- FOR RS APPROVAL → REJECTED RS (if any approver rejects)

**Business Rules**:

- Approvers must be assigned to the requisition
- Approvers are processed in order by level
- Rejection requires a reason/comment
- Status will remain as FOR RS APPROVAL until fully approved
- Additional approvers can be added during the approval process
- Approvers can input a note during approval
- Approvers can edit item quantity or delete an item from the table
- If there are quantity changes, these should reflect to the remaining GFQ. 
- Optional approvers should be added/removed based on new approved quantity (>80% of GFQ)


### 4. Requisition Assignment

**Actor**: Purchasing Head, Purchasing Admin, and Purchasing Staff

**Actions**:

- Assign the requisition to oneself or another purchasing staff
- For Purchasing Head -> Re-assign requisition to another purchasing staff

**Status Transitions**:

- ASSIGNING → ASSIGNED

**Business Rules**:

- Only APPROVED requisitions can be assigned
- Assignment must be to a Purchasing Staff or Purchasing Head
- Only Purchasing Head can update assignments

### 5. Requisition Monitoring

**Actor**: System (PRS)

**Actions**:

- Monitor canvass sheet statuses to change status
- Monitor purchase order statuses to close RS

**Status Transitions**:

- ASSIGNED → RS IN PROGRESS
- RS IN PROGRESS → CLOSED RS

**Business Rules**:

- Status will be RS IN PROGRESS when atleast 1 canvass sheet has been created
- Will retain RS IN PROGRESS status until RS is closed or cancelled
- RS will be CLOSED if all purchase orders are closed and no items left to canvass

### 5. Requisition Cancellation

**Actor**: Requester

**Actions**:

- Requester cancels the requisition during any stage of RS

**Status Transitions**:
    
- FOR RS APPROVAL → RS CANCELLED
- ASSIGNING → RS CANCELLED
- ASSIGNED → RS CANCELLED
- RS IN PROGRESS → RS CANCELLED
- RS REJECTED → RS CANCELLED

**Business Rules**:

- Cancellation is allowed during all RS stages except CLOSED RS and if all PO latest status is still FOR PO REVIEW or FOR PO APPROVAL - This means no purchase has been made yet
- Cancelling the RS will cancel all Purchase Orders - status PO_CANCELLED
- No new canvasses can be created
- Existing canvasses cannot be edited
- Canvass statuses will be CS Cancelled, except CS with CS_Approved status
- Remaining GFQs will be adjusted after cancellation - return approved quantity to remaining GFQ

## Example Scenarios

### Scenario 1: Standard Requisition Flow

1. Requester creates and submits a requisition
2. Approvers approve the requisition
3. Purchasing Head assigns the requisition to a Purchasing Staff
4. Purchasing Staff creates a canvass and submits it for approval
6. Purchase orders are closed
8. Requisition is closed

### Scenario 2: Requisition with Rejection

1. Requester creates and submits a requisition
2. An approver rejects the requisition with a reason
3. Requester updates the requisition and resubmits it
4. Approvers approve the requisition
5. Continue with standard flow

### Scenario 3: Cancellation of Requisition

1. Requester creates and submits a requisition
2. Requester cancels the requisition
4. Purchase orders and Canvass Sheets are cancelled
5. Requisition is cancelled

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/requisitionController.js`
- **Service**: `src/app/services/requisitionService.js`
- **Repository**: `src/infra/repositories/requisitionRepository.js`
- **Entity**: `src/domain/entities/requisitionEntity.js`
- **Constants**: `src/domain/constants/requisitionConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic
async function updateRequisitionStatus(requisitionId, newStatus, transaction) {
  const requisition = await requisitionRepository.findOne({
    where: { id: requisitionId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'DRAFT': ['SUBMITTED'],
    'SUBMITTED': ['PARTIALLY_APPROVED', 'APPROVED', 'REJECTED'],
    'PARTIALLY_APPROVED': ['APPROVED', 'REJECTED'],
    'APPROVED': ['ASSIGNED'],
    'ASSIGNED': ['CANVASSING'],
    'CANVASSING': ['FOR_PO_REVIEW'],
    'FOR_PO_REVIEW': ['FOR_DELIVERY'],
    'FOR_DELIVERY': ['CLOSED'],
  };
  
  if (!validTransitions[requisition.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${requisition.status} to ${newStatus}`);
  }
  
  // Update status
  await requisition.update({ status: newStatus }, { transaction });
  
  return requisition;
}
```

## Common Issues and Solutions

### Issue 1: Requisition Stuck in FOR_RS_APPROVAL Status

**Cause**: Some approvers have not yet approved the requisition.

**Solution**: 

- Check which approvers have not yet approved
- Send reminders to pending approvers
- Consider adding alternative approvers if original approvers are unavailable

### Issue 2: Cannot Submit Requisition

**Cause**: Missing required fields or validation errors.

**Solution**:

- Check validation error messages
- Ensure all required fields are filled
- Ensure at least one item is added

### Issue 3: Cannot Assign Requisition

**Cause**: Requisition is not in ASSIGNING status.

**Solution**:

- Check current status of requisition
- Ensure all approvers have approved
- If rejected, address rejection reason and resubmit
