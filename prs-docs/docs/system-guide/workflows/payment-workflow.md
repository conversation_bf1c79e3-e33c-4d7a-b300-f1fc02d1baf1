# Payment Request Workflow

This document outlines the complete workflow for the payment request process in the PRS system, from payment request creation to approval.

## Workflow Diagram

```mermaid
flowchart TD
  Start([Create or Edit
  Payment Request]) -->|isDraft| S1[Draft]
  S1 -->|Edit| Start
  Start -->|Submitted| S2[For PR Approval]
  S2 -->|Fully Approved| S3[Closed PR]
  S2 -->|Rejected| S4[PR Rejected]
  S4 -->|Return| Start
```

## Status Definitions

| Status | Description |
|--------|-------------|
| DRAFT | Initial status when a payment request is created but not submitted |
| FOR_PR_APPROVAL | Payment request has been submitted for approval |
| CLOSED_PR | Payment request has been approved and submitted to finance |
| PR_REJECTED | Payment request has been rejected by an approver |

## Detailed Workflow Steps

### 1. Payment Request Creation

**Actor**: Assigned Purchasing Staff

**Actions**:

- Create a new payment request for a purchase order
- Attach invoice related to purchase order
- Save as draft or submit for approval

**Status Transitions**:

- New → DRAFT (if saved as draft)
- New → FOR_PR_APPROVAL (if submitted for approval)
- REJECTED_PR → FOR_PR_APPROVAL (if resubmitted after rejection)


**Business Rules**:

- Only Assigned Purchasing Staff can create PR
- Payment request must be linked to a valid purchase order
- Payment Requests can only be created for approved Purchase Orders with Invoice Report
- An PR can be created as a draft or submitted immediately
- PR Number is auto-generated
- PR Letter is auto-generated
- A Purchase Order can have multiple payment requests (partial payments)
- The sum of all payment requests (PR) for a Purchase Order cannot exceed the total PO amount
- Each payment must reference the Purchase Order and related invoices
- Sum of selected invoice amount would be the PR amount
- Payment terms must comply with the terms specified in the Purchase Order

### 2. Payment Request Approval

**Actor**: Approvers

**Actions**:

- Review the payment request
- Approve or reject the payment request
- Add note of approval (optional) or rejection (required)
- Add additional approvers (optional)

**Status Transitions**:

- FOR_PR_APPROVAL → CLOSED_PR (if all approvers approve)
- FOR_PR_APPROVAL → REJECTED_PR (if any approver rejects)

**Business Rules**:

- Only FOR_PR_APPROVAL or REJECTED_PR payment requests can be approved
- User must be an assigned approver for the payment request
- Status will remain as FOR APPROVAL until fully approved
- Approvers are processed in order by level
- Additional approvers can be added during the approval process
- Approvers can input a note during approval
- If all approvers approve, status changes to CLOSED_PR
- Rejection requires a reason/comment

### 3. Payment Request Rejection

**Actor**: Approvers

**Actions**:

- Review the payment request
- Reject the payment request
- Provide rejection reason

**Status Transitions**:

- FOR_PR_APPROVAL → REJECTED

**Business Rules**:

- Only FOR_PR_APPROVAL payment requests can be rejected
- User must be an assigned approver for the payment request
- Rejection requires a reason/comment
- Upon rejection, status changes to REJECTED_PR

### 4. Payment Request Processing Rules

- If PR is fully approved, system to post payment request to Cityland’s legacy system via an API

## Example Scenarios

### Scenario 1: Standard Payment Flow

1. Purchasing Staff creates a payment request for a purchase order
2. Purchasing Staff adds and attaches invoice/s information and submits for approval
3. Approvers review and approve the payment request
4. Payment request is submitted Cityland Legacy System via API
6. Payment request is marked as closed

### Scenario 2: Payment Request with Rejection

1. Purchasing Staff creates a payment request and submits for approval
2. An approver rejects the payment request with a reason
3. Purchasing Staff updates the payment request and resubmits it
4. Approvers approve the payment request
5. Continue with standard flow

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/paymentRequestController.js`
- **Service**: `src/app/services/paymentRequestService.js`
- **Repository**: `src/infra/repositories/paymentRequestRepository.js`
- **Entity**: `src/domain/entities/paymentRequestEntity.js`
- **Constants**: `src/domain/constants/paymentConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic
async function updatePaymentRequestStatus(paymentRequestId, newStatus, transaction) {
  const paymentRequest = await paymentRequestRepository.findOne({
    where: { id: paymentRequestId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'DRAFT': ['FOR_PR_APPROVAL'],
    'FOR_PR_APPROVAL': ['SUBMITTED', 'REJECTED'],
    'REJECTED': ['FOR_PR_APPROVAL'],
    'SUBMITTED': ['APPROVED'],
  };
  
  if (!validTransitions[paymentRequest.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${paymentRequest.status} to ${newStatus}`);
  }
  
  // Update status
  await paymentRequest.update({ status: newStatus }, { transaction });
  
  return paymentRequest;
}
```

### Payment Request Approval

```javascript
// Example approval logic
async function approvePaymentRequest({
  paymentRequestId,
  approverId,
  transaction,
}) {
  const paymentRequest = await paymentRequestRepository.findOne({
    where: { id: paymentRequestId },
    include: [{ association: 'paymentRequestApprovers' }],
    transaction,
  });
  
  if (paymentRequest.status !== 'FOR_PR_APPROVAL' && paymentRequest.status !== 'REJECTED') {
    throw new Error('Payment request cannot be approved');
  }
  
  const approver = paymentRequest.paymentRequestApprovers.find(a => a.userId === approverId);
  
  if (!approver) {
    throw new Error('User is not an approver for this payment request');
  }
  
  if (approver.status === 'APPROVED') {
    throw new Error('User has already approved this payment request');
  }
  
  await approver.update({ status: 'APPROVED' }, { transaction });
  
  const allApprovers = paymentRequest.paymentRequestApprovers;
  const pendingApprovers = allApprovers.filter(a => a.status !== 'APPROVED');
  
  if (pendingApprovers.length === 0) {
    await paymentRequest.update({ status: 'SUBMITTED' }, { transaction });
    return true; // All approved
  }
  
  return false; // Not all approved yet
}
```

## Common Issues and Solutions

### Issue 1: Cannot Create Payment Request

**Cause**: Purchase Order is not in the correct status or missing invoice.

**Solution**:

- Ensure the purchase order is in FOR_DELIVERY status
- Check that purchase order has invoice report created

### Issue 2: Payment Request Approval Issues

**Cause**: Approvers not assigned correctly or missing permissions.

**Solution**:

- Check if approvers are assigned correctly
- Verify approver permissions
- Ensure approvers are active users
