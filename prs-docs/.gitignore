# MkDocs build directory
site/
public/

# Python virtual environment
venv/
env/
.env/
.venv/
ENV/

# Python cache files
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.coverage
htmlcov/

# Distribution / packaging
dist/
build/
*.egg-info/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.settings/

# Original documentation files outside of docs directory
/system-guide/
/how-to/
/refactoring-guide/
/enhancement/
*.js
!docs/javascripts/*.js
*.css
!docs/stylesheets/*.css

# Temporary files
*.log
*.tmp
*.bak
.*.swp
.~lock.*

# Local configuration
.env.local
.env.development.local
.env.test.local
.env.production.local
