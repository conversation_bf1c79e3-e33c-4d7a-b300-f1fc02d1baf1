<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="221373429C0" style="width:103px;" class="column-headers-background">A</th><th id="221373429C1" style="width:98px;" class="column-headers-background">B</th><th id="221373429C2" style="width:252px;" class="column-headers-background">C</th><th id="221373429C3" style="width:252px;" class="column-headers-background">D</th><th id="221373429C4" style="width:233px;" class="column-headers-background">E</th><th id="221373429C5" style="width:460px;" class="column-headers-background">F</th><th id="221373429C6" style="width:184px;" class="column-headers-background">G</th><th id="221373429C7" style="width:477px;" class="column-headers-background">H</th><th id="221373429C8" style="width:100px;" class="column-headers-background">I</th><th id="221373429C9" style="width:250px;" class="column-headers-background">J</th><th id="221373429C10" style="width:197px;" class="column-headers-background">K</th><th id="221373429C11" style="width:197px;" class="column-headers-background">L</th><th id="221373429C12" style="width:197px;" class="column-headers-background">M</th><th id="221373429C13" style="width:197px;" class="column-headers-background">N</th><th id="221373429C14" style="width:197px;" class="column-headers-background">O</th><th id="221373429C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="221373429R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="221373429R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="16">PRS-014 - [RS Assigning] - Assign RS to me, Assign RS to others</td></tr><tr style="height: 393px"><th id="221373429R2" style="height: 393px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 393px">3</div></th><td class="s4"></td><td class="s4">ASSIGNING RS</td><td class="s4">Critical</td><td class="s4">Verify Assigning of a Purchasing Staff to a Requisition Slip</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Purchasing Head has been declared<br>5. A Purchasing Staff has been assigned to a User<br>6. Approved Requisition Slip that has yet to be assigned</a></td><td class="s4">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click Assign Action<br>4. Select Assign to Others<br>5. Select any of Purchasing Staff listed<br>6. Click &quot;Add User&quot;<br>7. Log In using Purchasing Staff account that selected previously in assigning RS<br>8. Check Notification bell<br>9. Check the RS dashboard My Approval/Assigned Tab<br>10. Click RS details to view RS details and check &quot;Assigned to&quot; right beside the RS Details<br>11. Check the RS Status<br>12. Click Select Action button<br>13. Click &quot;Enter Canvass&quot;<br>14. Login as different Purchasing Staff account<br>15.On RS Dashboard, Check the same RS</td><td class="s6"></td><td class="s4">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Assign<br>        iii. Cancel Request<br>3. Should display a Modal to select the Assignment<br>        i. Assign to me<br>        ii. Assign to others<br>4. Should display a Search Field for all of the Users with a User Type of Purchasing Staff<br>5. Purchasing Staff should be selected<br>6. Should be able to add in Assigned To the selected Purhasing staff and displayed a success toast message<br>7. Purchasing Staff should be login<br>8.  Should Notify the assigned Purchasing Staff of their Assignment<br>Sample Notification:<br>Title: <br>New Requisition Slip Assignment<br><br>Content:<br>A New Requisition Slip has been assigned to you. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024<br>9. Should reflected the RS to For My Approval/Assigned Tab<br>10. Should assign the Requisition Slip to the Purchasing Staff.<br>11. Should update the RS Status to Assigned<br>12. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Enter Canvass<br>        iii. Cancel Request<br>13. Should be able to proceed with canvassing<br>14. Should be able to login <br>15. Should not allow the other Purchasing Staff to get the Requisition Slip</td><td class="s4" rowspan="5"></td><td class="s7">Failed</td><td class="s7">Failed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s8"></td><td class="s4">no push notification<br>not on the lsit of My approval tab<br>CITYLANDPRS-796 <br><br>[Feb 28] same issue CITYLANDPRS-796 </td><td class="s4"></td></tr><tr style="height: 211px"><th id="221373429R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s4"></td><td class="s4">ASSIGNING RS</td><td class="s4">Critical</td><td class="s4">Verify Assigning of Requisition Slip by the Purchasing Staff when clicked Assigned to me</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Purchasing or Procurement Staff has been assigned to a User<br>5. Approved Requisition Slip that has yet to be assigned</a></td><td class="s4">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click Assign Action<br>4. Select Assign to Me<br>5. Click &quot;Add User&quot;<br>6. Check the RS dashboard My Approval/Assigned Tab<br>7. Click RS details to view RS details and check &quot;Assigned to&quot; right beside the RS Details<br>8. Check the RS Status<br>9. Click Select Action button<br>10. Click &quot;Enter Canvass&quot;<br>11. Login as different Purchasing Staff account<br>12.On RS Dashboard, Check the same RS</td><td class="s6"></td><td class="s4">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Assign<br>        iii. Cancel Request<br>3. Should display a Modal to select the Assignment<br>        i. Assign to me<br>        ii. Assign to others<br>4. The Assigned to me should be selected<br>5. The Requisition Slip should be assigned to the logged in Purchasing Staff and displayed a success toast message<br>6. Should reflected the RS to For My Approval/Assigned Tab<br>7. Should assign the Requisition Slip to the Purchasing Staff.<br>8. Should update the RS Status to Assigned<br>9. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Enter Canvass<br>        iii. Cancel Request<br>10. Should be able to proceed with canvassing<br>11. Should be able to login <br>12. Should not allow the other Purchasing Staff to get the Requisition Slip</td><td class="s10">Passed</td><td class="s10">Passed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s8"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="221373429R4" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">5</div></th><td class="s4"></td><td class="s4">ASSIGNING RS</td><td class="s4">Critical</td><td class="s4">Verify Assigning of Requisition Slip by the Purchasing Staff when clicked Assigned to others</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Purchasing or Procurement Staff has been assigned to a User<br>5. Approved Requisition Slip that has yet to be assigned</a></td><td class="s4">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click Assign Action<br>4. Select Assign to Others<br>5. Select any of Purchasing Staff listed<br>6. Click &quot;Add User&quot;<br>7. Log In using Purchasing Staff account that selected previously in assigning RS<br>8. Check Notification bell<br>9. Check the RS dashboard My Approval/Assigned Tab<br>10. Click RS details to view RS details and check &quot;Assigned to&quot; right beside the RS Details<br>11. Check the RS Status<br>12. Click Select Action button<br>13. Click &quot;Enter Canvass&quot;<br>14. Login as different Purchasing Staff account<br>15.On RS Dashboard, Check the same RS</td><td class="s6"></td><td class="s4">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Assign<br>        iii. Cancel Request<br>3. Should display a Modal to select the Assignment<br>        i. Assign to me<br>        ii. Assign to others<br>4. Should display a Search Field for all of the Users with a User Type of Purchasing Staff<br>5. Purchasing Staff should be selected<br>6. Should be able to add in Assigned To the selected Purhasing staff and displayed a success toast message<br>7. Purchasing Staff should be login<br>8.  Should Notify the assigned Purchasing Staff of their Assignment<br>Sample Notification:<br>Title: <br>New Requisition Slip Assignment<br><br>Content:<br>A New Requisition Slip has been assigned to you. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024<br>9. Should reflected the RS to For My Approval/Assigned Tab<br>10. Should assign the Requisition Slip to the Purchasing Staff.<br>11. Should update the RS Status to Assigned<br>12. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Enter Canvass<br>        iii. Cancel Request<br>13. Should be able to proceed with canvassing<br>14. Should be able to login <br>15. Should not allow the other Purchasing Staff to get the Requisition Slip</td><td class="s7">Failed</td><td class="s7">Failed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s8"></td><td class="s4">no push notification<br>not on the lsit of My approval tab<br>CITYLANDPRS-796<br><br>[Feb 28] same issue CITYLANDPRS-796 </td><td class="s4"></td></tr><tr style="height: 211px"><th id="221373429R5" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">6</div></th><td class="s4"></td><td class="s4">ASSIGNING RS</td><td class="s4">Critical</td><td class="s4">Verify Updating of assigned Purchasing Staff when clicked assigned to me</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Purchasing Head has been declared<br>5. A Purchasing or Procurement Staff has been assigned to a User<br>6. Approved Requisition Slip that has yet to be assigned</a></td><td class="s4">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click Assign Action<br>4. Select Assign to Me<br>5. Click &quot;Add User&quot;<br>6. Check the RS dashboard My Approval/Assigned Tab<br>7. Click RS details to view RS details and check &quot;Assigned to&quot; right beside the RS Details<br>8. Check the RS Status<br>9. Click Select Action button<br>10. Click &quot;Enter Canvass&quot;<br></td><td class="s6"></td><td class="s4">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Assign<br>        iii. Cancel Request<br>        iv. Enter Canvass Sheet<br>3. Should display a Modal to select the Assignment<br>        i. Assign to me<br>        ii. Assign to others<br>4. The Assigned to me should be selected<br>5. The Requisition Slip should be assigned to the logged in Purchasing Head and displayed a success toast message<br>6. Should reflected the RS to For My Approval/Assigned Tab<br>7. Should assign the Requisition Slip to the Purchasing Head.<br>8. Should retained the RS Status to Assigned<br>9. Should display a Modal of Actions including<br>         i. Add Items<br>        ii. Assign<br>        iii. Cancel Request<br>        iv. Enter Canvass Sheet<br>10. Should be able to proceed with canvassing<br></td><td class="s10">Passed</td><td class="s10">Passed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s8"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 211px"><th id="221373429R6" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">7</div></th><td class="s4"></td><td class="s4">ASSIGNING RS</td><td class="s4">Critical</td><td class="s4">Verify Updating of assigned Purchasing Staff when clicked assigned to others</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Purchasing Head has been declared<br>5. A Purchasing or Procurement Staff has been assigned to a User<br>6. Approved Requisition Slip that has yet to be assigned</a></td><td class="s4">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click Assign Action<br>4. Select Assign to Others<br>5. Select any of Purchasing Staff listed<br>6. Click &quot;Add User&quot;<br>7. Log In using Purchasing Staff account that selected previously in assigning RS<br>8. Check Notification bell<br>9. Check the RS dashboard My Approval/Assigned Tab<br>10. Click RS details to view RS details and check &quot;Assigned to&quot; right beside the RS Details<br>11. Check the RS Status<br>12. Click Select Action button<br>13. Click &quot;Enter Canvass&quot;<br>14. Login as different Purchasing Staff account<br>15.On RS Dashboard, Check the same RS</td><td class="s6"></td><td class="s4">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Assign<br>        iii. Cancel Request<br>        iv. Enter Canvass Sheet<br>3. Should display a Modal to select the Assignment<br>        i. Assign to me<br>        ii. Assign to others<br>4. Should display a Search Field for all of the Users with a User Type of Purchasing Staff<br>5. Purchasing Staff should be selected<br>6. Should be able to add in Assigned To the selected Purhasing staff and displayed a success toast message<br>7. Purchasing Staff should be login<br>8.  Should Notify the assigned Purchasing Staff of their Assignment<br>Sample Notification:<br>Title: <br>Updated Requisition Slip Assignment<br><br>Content:<br>Purchasing Head has updated the assigned Requisition Slip to another Purchasing Staff. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024<br>9. Should reflected the RS to For My Approval/Assigned Tab<br>10. Should assign the Requisition Slip to the Purchasing Staff.<br>11. Should retained the RS Status to Assigned<br>12. Should display a Modal of Actions including<br>        i. Add Items<br>        ii. Enter Canvass<br>        iii. Cancel Request<br>13. Should be able to proceed with canvassing<br>14. Should be able to login <br>15. Should not allow the other Purchasing Staff to get the Requisition Slip</td><td class="s7">Failed</td><td class="s7">Failed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s8"></td><td class="s4">no push notification<br>not on the lsit of My approval tab<br>CITYLANDPRS-796<br><br>[Feb 28] same issue CITYLANDPRS-796 </td><td class="s4"></td></tr></tbody></table></div>