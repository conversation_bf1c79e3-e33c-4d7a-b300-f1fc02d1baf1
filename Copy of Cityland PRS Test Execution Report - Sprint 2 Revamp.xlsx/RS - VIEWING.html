<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:do<PERSON>-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="386102430C0" style="width:103px;" class="column-headers-background">A</th><th id="386102430C1" style="width:219px;" class="column-headers-background">B</th><th id="386102430C2" style="width:72px;" class="column-headers-background">C</th><th id="386102430C3" style="width:252px;" class="column-headers-background">D</th><th id="386102430C4" style="width:233px;" class="column-headers-background">E</th><th id="386102430C5" style="width:330px;" class="column-headers-background">F</th><th id="386102430C7" style="width:258px;" class="column-headers-background">H</th><th id="386102430C8" style="width:250px;" class="column-headers-background">I</th><th id="386102430C9" style="width:164px;" class="column-headers-background">J</th></tr></thead><tbody><tr style="height: 42px"><th id="386102430R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s2">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td></tr><tr style="height: 19px"><th id="386102430R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="9">CITYLANDPRS-1482        [REQUISITION SLIP - VIEWING] Requisition Slip Download to PDF</td></tr><tr style="height: 19px"><th id="386102430R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-1482-001</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">Critical</td><td class="s4">Verify that a Download Button is visible when viewing a Requisition Slip.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Open a submitted Requisition Slip.</td><td class="s4">1. Download Button is visible.</td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-1482-002</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">Minor</td><td class="s4">Verify that a Download Button is not visible when viewing a Draft Requisition Slip.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been save as draft.</td><td class="s4">1. Open a drafted Requisition Slip.</td><td class="s4">1. Download Button should not be visible.</td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-1482-003</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">Critical</td><td class="s4">Verify that Download Button is not visible for unauthorized users.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Log in as unauthorized user.<br>2. View Requesition Slip.</td><td class="s4">2. Download button should not be visible.</td><td class="s6"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">PRS-1482-004</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">Critical</td><td class="s4">Verify that clicking the Download Button downloads the Requisition Slip as a PDF.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Open a submitted Requisition Slip.<br>2. Click Download Button</td><td class="s4">2. PDF file is downloaded successfully.</td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-1482-005</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">High</td><td class="s4">Verify that downloaded PDF contains the correct RS Number and RS Status.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Download RS PDF.<br>2. Open the file.<br>3. Verify RS Number and Status.</td><td class="s4">3. RS Number and RS Status is accurate and correctly shown at top of PDF.</td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-1482-006</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">High</td><td class="s4">Verify that Request Details are properly displayed in the downloaded PDF.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Download RS PDF.<br> 2. Open and review the followin fields: <br>     - Type of Request<br>     - Date Required<br>     - Company<br>     - Project<br>     - Department<br>     - Purpose<br>     - Deliver To<br>     - Charge To</td><td class="s4"><span style="font-family:Poppins,Arial;color:#000000;">2. All fields are properly displayed and filled with correct data:<br>     a. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Type of Request</span><span style="font-family:Poppins,Arial;color:#000000;"> - Type of Request of the Requisition Slip<br>     b. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Date Required</span><span style="font-family:Poppins,Arial;color:#000000;"> - Date indicated in the Requisition Slip<br>     c. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Company </span><span style="font-family:Poppins,Arial;color:#000000;">- Company selected in the Requisition Slip<br>     d. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Project</span><span style="font-family:Poppins,Arial;color:#000000;"> - Project selected in the Requisition Slip<br>     e. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Department</span><span style="font-family:Poppins,Arial;color:#000000;"> - Department selected in the Requisition Slip<br>     f. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Purpose </span><span style="font-family:Poppins,Arial;color:#000000;">- Purpose indicated in the Requisition Slip<br>     g. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Deliver To </span><span style="font-family:Poppins,Arial;color:#000000;">- selected Address in the Requisition Slip<br>     h. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Charge To</span><span style="font-family:Poppins,Arial;color:#000000;"> - selected Charge To in the Requisition Slip<br><br>2. If &quot;</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Charge To</span><span style="font-family:Poppins,Arial;color:#000000;">&quot; is empty, should show &quot;---&quot;.</span></td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-1482-007</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">High</td><td class="s4">Verify that List of Items Requested are properly shown in the PDF with correct details.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Download RS PDF. <br>2. Verify the following Items columns:<br>     - Items Name<br>     - Units<br>     - Remaining GFQ<br>     - Quantity<br>     - Notes</td><td class="s4"><span style="font-family:Poppins,Arial;color:#000000;">2. Items are correctly listed with complete details per item:<br>     a. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Item </span><span style="font-family:Poppins,Arial;color:#000000;">- Items included in the Requisition Slip<br>     b. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Unit </span><span style="font-family:Poppins,Arial;color:#000000;">- Unit of the Item<br>     c. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Remaining GFQ </span><span style="font-family:Poppins,Arial;color:#000000;">- Most latest Remaining GFQ<br>     d. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Quantity </span><span style="font-family:Poppins,Arial;color:#000000;">- Requested Quantity by the Requestor<br>     e. </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Notes </span><span style="font-family:Poppins,Arial;color:#000000;">- Latest Notes of the item in the Requisition Slip</span></td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">PRS-1482-008</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">Minor</td><td class="s4">Verify that Page Number is displayed in the downloaded PDF if multiple pages.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.<br>3. Requisition Slip has 2 pages.</td><td class="s4">1. Download RS PDF.<br>2. Scroll pages.<br>3. Verify that each page displays the page number in the header on the right side.</td><td class="s4">2. Each page of the RS PDF shows the page count in the format &quot;Page X of XX&quot; in the right-side header.</td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-1482-009</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">High</td><td class="s4">Verify that Non-Steelbar items appear first before Steelbar items in PDF.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted with both Steelbar and Non-Steelbar items.</td><td class="s4">1. Download RS PDF.<br>2. Verify items order list.</td><td class="s4">2. Non-Steelbar items should be listed first, then Steelbars.</td><td class="s4"></td><td class="s5">Not Started</td></tr><tr style="height: 19px"><th id="386102430R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">PRS-1482-010</td><td class="s4">Requisition Slip - Download PDF</td><td class="s4">High</td><td class="s4">Verify that the PDF file name format is correct when downloading.</td><td class="s4">1. Logged in as the ff:<br>     - Purchasing Staff<br>     - Purchasing Admin<br>     - Purchasing Head<br>     - Requester<br>2. Requisition Slip has been created and submitted.</td><td class="s4">1. Download RS PDF.<br>2. Verify the RS downloaded file name.</td><td class="s4"><span style="font-family:Poppins,Arial;color:#000000;">2. Should have a File Name combination of Document Prefix+Date Extracted[YYYYMMDD]+Time Extracted[HHMMSS]<br></span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">SAMPLE:</span><span style="font-family:Poppins,Arial;color:#000000;"> RS-20240728-102013</span></td><td class="s4"></td><td class="s5">Not Started</td></tr></tbody></table></div>