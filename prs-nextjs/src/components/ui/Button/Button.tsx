"use client";

import React, { forwardRef } from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  'w-full inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 transition duration-300 rounded-xl py-2.5 px-4',
  {
    variants: {
      variant: {
        submit: 'bg-primary-700 text-white font-medium',
        submitAlt: 'bg-modal-header text-white font-medium',
        secondary: 'bg-secondary-600 text-white',
        outline: 'text-primary-700 border border-primary-700',
        danger: 'text-danger-500 border border-danger-500',
        back: 'flex w-10 h-10 rounded-lg bg-gray-100 shadow-sm',
        action: 'bg-gray-200 rounded-lg font-semibold',
        icon: 'w-auto bg-sky-500 text-white rounded-lg font-semibold',
        paginationSelected:
          'flex items-center justify-center rounded-xl bg-white text-[#306F4E] border border-[#306F4E]',
        paginationNormal:
          'flex items-center justify-center rounded-xl bg-white text-gray-500 border border-gray-200',
        noColor: 'bg-transparent text-black',
        file: 'text-gray-500 bg-white border border-gray-200 font-normal',
        link: 'text-blue-600 underline underline-offset-4 font-semibold p-0 m-0',
      },
      hover: {
        submit: 'hover:bg-primary-800',
        submitAlt: 'hover:bg-modal-bg',
        action: 'hover:bg-slate-200 hover:text-gray-800',
        highlight: 'hover:bg-sky-400 hover:text-gray-100',
        danger: 'hover:text-danger-600 hover:border-danger-600',
        pagination: 'hover:border-1 hover:border-[#306F4E] hover:text-[#306F4E] hover:bg-[#f3fff8]',
        darkGlow: 'hover:text-gray-800',
        outline: 'hover:text-primary-800 hover:border-primary-800 hover:bg-rose-50',
        file: 'hover:border-gray-200 hover:bg-gray-50',
        link: 'hover:text-blue-700',
        secondary: 'hover:bg-secondary-700',
      },
    },
    defaultVariants: {
      variant: 'submit',
      hover: 'submit',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  isLoading?: boolean;
  icon?: React.ElementType;
  iconPosition?: 'L' | 'R';
  iconSize?: string;
  label?: string | React.ReactNode;
  as?: React.ElementType;
}

const Spinner = ({ size = "md", className = "" }: { size?: string, className?: string }) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-10 w-10",
  };

  return (
    <div className={cn("animate-spin rounded-full border-t-2 border-b-2 border-current", sizeClasses[size as keyof typeof sizeClasses], className)}></div>
  );
};

const FieldWrapper = ({ label, children }: { label: string | React.ReactNode, children: React.ReactNode }) => {
  return (
    <div className="flex flex-col space-y-2">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      {children}
    </div>
  );
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      hover,
      as: Component = 'button',
      children,
      isLoading,
      icon: Icon,
      iconPosition = 'R',
      iconSize,
      label = '',
      ...props
    },
    ref
  ) => {
    const buttonContent = (
      <Component
        className={cn(buttonVariants({ variant, hover }), className)}
        ref={ref}
        {...props}
      >
        {isLoading && <Spinner size="sm" className="text-current mr-2" />}
        {Icon && iconPosition === 'L' && (
          <Icon className={cn('mr-2 h-4 w-4', iconSize)} />
        )}
        {children && (
          <span className="truncate overflow-hidden">{children}</span>
        )}
        {Icon && iconPosition !== 'L' && (
          <Icon className={cn('ml-2 h-4 w-4', iconSize)} />
        )}
      </Component>
    );

    return !!label ? (
      <FieldWrapper label={label}>{buttonContent}</FieldWrapper>
    ) : (
      buttonContent
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
