<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1026275990C0" style="width:103px;" class="column-headers-background">A</th><th id="1026275990C3" style="width:252px;" class="column-headers-background">D</th><th id="1026275990C4" style="width:233px;" class="column-headers-background">E</th><th id="1026275990C5" style="width:330px;" class="column-headers-background">F</th><th id="1026275990C7" style="width:258px;" class="column-headers-background">H</th><th id="1026275990C10" style="width:140px;" class="column-headers-background">K</th><th id="1026275990C11" style="width:140px;" class="column-headers-background">L</th><th id="1026275990C14" style="width:245px;" class="column-headers-background">O</th><th id="1026275990C15" style="width:133px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1026275990R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Expected Results</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1026275990R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="9">PRS-002 - User Types</td></tr><tr style="height: 19px"><th id="1026275990R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">TC-002-01</td><td class="s4">Verify access of Root User</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a.User Management<br>    -Create a User<br>    -Edit a User<br>    -Active/Inactive Users <br>    -View User Types<br>    -View Users<br>   b. Department Management<br>     -View Department<br>   c.Others<br>     -User Profile<br>     -Notification Bell</td><td class="s5">Failed</td><td class="s6" rowspan="12"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1FMAF-B1B5TCNb0giaq9PgEDcMyUPwoGaTkQ0vji-xHg/edit?gid=0#gid=0&amp;range=1:1">Verna Test Results</a></td><td class="s4"></td><td class="s6"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-802">https://youtrack.stratpoint.com/issue/CITYLANDPRS-802<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-803</a></td></tr><tr style="height: 19px"><th id="1026275990R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">TC-002-02</td><td class="s4">Verify access of IT Admin User</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. User should be able to login<br>2. User should have an access to the ff:<br>   a.User Management<br>    -Create a User<br>    -Edit a User<br>    -Active/Inactive Users<br>    -View User Types<br>    -View Users<br>   b. Company Management<br>     -Sync Company<br>     -View Company<br>   c.Association Managementt<br>     -Edit Association<br>     -Delete Association<br>     -View Association<br>   d.Project Management<br>     -Sync Project<br>     -View Project<br>     -Assign Approvers<br>     -Update Approvers<br>     -View Approvers<br>     -Trade Management<br>   e Department Management<br>     -Sync Department<br>     -View Department<br>     -Assign Approvers<br>     -Update Approvers<br>     -View Approvers<br>   f. Supplier Management<br>     -Sync Supplier<br>     -View Supplier<br>     -Attachment and Notes<br>   g. OFM Items<br>      -Sync Items<br>     -View Items<br>     -Item History<br>   h. OFM List<br>     -Categorizing OFM List<br>     -Viewing OFM List<br>   I. Non-OFM Items<br>     -Create Non-OFM<br>     -Edit Non-OFM<br>     -View Non-OFM<br>   j. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>     -RS History<br>   k. Manage Canvass<br>     -View Canvass</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#351c75;"><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   l. Manage Purchase Orders<br>     -Create Purchase Order</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#351c75;"><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   m. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#351c75;"><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   n. Manage Payment Request<br>     -View Payment  Request</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#351c75;"><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   o. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#351c75;"><br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">   p. Others<br>      -System Audit Logs<br>      -User Profile<br>     -Notification Bell</span></td><td class="s7">Passed</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1026275990R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">TC-002-03</td><td class="s4">Verify access of Engineers</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. OFM Items<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">      -Sync Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>     -View Items<br>     -Item History<br>   b. OFM List<br>     -Categorizing OFM List<br>     -Viewing OFM List<br>   c. Non-OFM Items<br>     -View Non-OFM<br>   d. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>   e. Manage Canvass<br>     -View Canvass<br>   f. Manage Purchase Orders<br>     -View Purchase Order<br>   g. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   h. Manage Payment Request<br>     -View Payment  Request<br>   i. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   j. Others<br>      -User Profile<br>     -Notification Bell</span></td><td class="s7">Passed</td><td class="s4"></td><td class="s8"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1106">ITYLANDPRS-1106</a></td></tr><tr style="height: 19px"><th id="1026275990R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">TC-002-04</td><td class="s4">Verify access of Supervisor</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>     -RS Approval<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -Create Purchase Order<br>     -View Purchase Order<br>     -Save Purchase Order as draft<br>     -Purchase Order Submission<br>     -Purchase Order Approval<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>     - Payment Request Approval<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4"></td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1124">Double approver error: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1124<br><br><br><br></a></td></tr><tr style="height: 19px"><th id="1026275990R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">TC-002-05</td><td class="s4">Verify access of Assistant Manager</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>     - Payment Request Approval<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1026275990R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">TC-002-06</td><td class="s4">Verify access of Department Head</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>     -RS Approval<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>     -Purchase Order Approval<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>     - Payment Request Approval<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1026275990R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s10">TC-002-07</td><td class="s10">Verify access of Department Secretary</td><td class="s10">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s10">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s10">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>   b. Manage Canvass<br>     -View Canvass<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>   d. Manage Delivery Record<br>     -View Delivery Record<br>   e. Manage Payment Request<br>     -View Payment  Request<br>   f. Non-RS Payment<br>     -View Non-Payment  Request<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1026275990R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">TC-002-08</td><td class="s4">Verify access of Division Head</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>     -RS Approval<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>     -Purchase Order Approval<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>     - Payment Request Approval<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1026275990R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s10">TC-002-09</td><td class="s10">Verify access of Area Staff</td><td class="s10">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s10">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s10">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>     -RS Approval<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>     -Purchase Order Approval<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>     - Payment Request Approval<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4"></td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1106">CITYLANDPRS-1106</a></td></tr><tr style="height: 19px"><th id="1026275990R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">TC-002-10</td><td class="s4">Verify access of Purchasing Staff</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. OFM Items<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">     -Sync Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>     -View Items<br>     -Item History<br>   b.OFM Lists<br>     -Categorizing Item list<br>     -Viewing OFM list<br>   c. Non-OFM Items<br>     -Create Non-OFM<br>     -Edit Non-OFM<br>     -View Non-OFM<br>   d. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>   e. Manage Canvass<br>     -View Canvass<br>     -Supplier Entry<br>     -Save Canvass as Draft<br>     -Canvass Submission<br>   f. Manage Purchase Orders<br>     -View Purchase Order<br>   g. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   h. Manage Payment Request<br>     -Create Payment Request<br>     -View Payment  Request<br>     -Save  Payment Request as Draft<br>     - Payment Request Submission<br>   i. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   j. Others<br>      -User Profile<br>     -Notification Bell</span></td><td class="s5">Failed</td><td class="s9"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1000">The ff are only accesible by the Purchasing staff/ Assigned To issue: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1000</a></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1026275990R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">TC-002-11</td><td class="s4">Verify access of Purchasing Head</td><td class="s4">1. User accounts are created<br>2. Refere on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. OFM Items<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">     -Sync Items</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>     -View Items<br>     -Item History<br>   b.OFM Lists<br>     -Categorizing Item list<br>     -Viewing OFM list<br>   c. Non-OFM Items<br>     -Create Non-OFM<br>     -Edit Non-OFM<br>     -View Non-OFM<br>   d. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">     -RS Approval</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br>   e. Manage Canvass<br>     -View Canvass<br>     -Supplier Entry<br>     -Save Canvass as Draft<br>     -Canvass Submission<br>     -Canvass Approval<br>   f. Manage Purchase Orders<br>     -View Purchase Order<br>     -Purchase Order Submission<br>     -Purchase Order Approval<br>   g. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   h. Manage Payment Request<br>     -Create Payment Request<br>     -View Payment  Request<br>     -Save  Payment Request as Draft<br>     - Payment Request Submission<br>     - Payment Request Approval<br>   i. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>     -Non-Payment  Request Approval<br>   j. Others<br>      -User Profile<br>     -Notification Bell</span></td><td class="s5">Failed</td><td class="s4">User cannot be added as approver<br>- RS Approval</td><td class="s6"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-827">https://youtrack.stratpoint.com/issue/CITYLANDPRS-827<br><br><br>Sync Error:<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1106</a></td></tr><tr style="height: 19px"><th id="1026275990R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">TC-002-12</td><td class="s4">Verify access of Management</td><td class="s6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********">1. User accounts are created<br>2. Refer on user types for access of each rold<br>User Types Reference:<br>https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=**********#gid=**********</a></td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s4">1/31: Not implemented for in progress featues<br>Purchase order<br>Delivery Record<br>Payment Request<br>Non-RS Payment</td><td class="s4">CITYLANDPRS-818<br><br>CITYLANDPRS-703<br>CITYLANDPRS-823</td></tr></tbody></table></div>