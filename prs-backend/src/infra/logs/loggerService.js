/**
 * Centralized Logger Service
 * 
 * This service provides structured logging capabilities with consistent
 * formatting and context enrichment.
 */
const { requestContext } = require('@fastify/request-context');

/**
 * Log levels in order of increasing severity
 */
const LOG_LEVELS = {
  TRACE: 'trace',
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  FATAL: 'fatal',
};

/**
 * Log categories for different parts of the application
 */
const LOG_CATEGORIES = {
  API: 'api',
  DATABASE: 'database',
  SECURITY: 'security',
  PERFORMANCE: 'performance',
  BUSINESS: 'business',
  INTEGRATION: 'integration',
  SYSTEM: 'system',
};

/**
 * Logger Service that provides structured logging capabilities
 */
class LoggerService {
  /**
   * Creates a new LoggerService
   * 
   * @param {Object} options - Logger options
   * @param {Object} options.logger - Pino logger instance
   * @param {string} options.serviceName - Name of the service
   * @param {Object} options.defaultContext - Default context to include in all logs
   */
  constructor(options = {}) {
    const { logger, serviceName = 'prs-backend', defaultContext = {} } = options;
    
    this.logger = logger;
    this.serviceName = serviceName;
    this.defaultContext = defaultContext;
  }

  /**
   * Gets the current request context
   * 
   * @returns {Object} - Request context
   */
  getRequestContext() {
    try {
      const requestId = requestContext.get('x-request-id');
      const user = requestContext.get('user');
      
      return {
        requestId,
        userId: user?.id,
        username: user?.username,
      };
    } catch (error) {
      return {};
    }
  }

  /**
   * Enriches log data with standard context
   * 
   * @param {Object} data - Log data
   * @returns {Object} - Enriched log data
   */
  enrichLogData(data = {}) {
    const timestamp = new Date().toISOString();
    const requestContext = this.getRequestContext();
    
    return {
      timestamp,
      service: this.serviceName,
      ...this.defaultContext,
      ...requestContext,
      ...data,
    };
  }

  /**
   * Logs a message at the specified level
   * 
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {Object} data - Additional log data
   */
  log(level, message, data = {}) {
    const enrichedData = this.enrichLogData({
      message,
      ...data,
    });
    
    this.logger[level](enrichedData);
  }

  /**
   * Logs a trace message
   * 
   * @param {string} message - Log message
   * @param {Object} data - Additional log data
   */
  trace(message, data = {}) {
    this.log(LOG_LEVELS.TRACE, message, data);
  }

  /**
   * Logs a debug message
   * 
   * @param {string} message - Log message
   * @param {Object} data - Additional log data
   */
  debug(message, data = {}) {
    this.log(LOG_LEVELS.DEBUG, message, data);
  }

  /**
   * Logs an info message
   * 
   * @param {string} message - Log message
   * @param {Object} data - Additional log data
   */
  info(message, data = {}) {
    this.log(LOG_LEVELS.INFO, message, data);
  }

  /**
   * Logs a warning message
   * 
   * @param {string} message - Log message
   * @param {Object} data - Additional log data
   */
  warn(message, data = {}) {
    this.log(LOG_LEVELS.WARN, message, data);
  }

  /**
   * Logs an error message
   * 
   * @param {string|Error} messageOrError - Log message or error object
   * @param {Object} data - Additional log data
   */
  error(messageOrError, data = {}) {
    let message = messageOrError;
    let errorData = {};
    
    if (messageOrError instanceof Error) {
      message = messageOrError.message;
      errorData = {
        error: {
          name: messageOrError.name,
          message: messageOrError.message,
          stack: messageOrError.stack,
          ...(messageOrError.code && { code: messageOrError.code }),
        },
      };
    }
    
    this.log(LOG_LEVELS.ERROR, message, {
      ...errorData,
      ...data,
    });
  }

  /**
   * Logs a fatal message
   * 
   * @param {string|Error} messageOrError - Log message or error object
   * @param {Object} data - Additional log data
   */
  fatal(messageOrError, data = {}) {
    let message = messageOrError;
    let errorData = {};
    
    if (messageOrError instanceof Error) {
      message = messageOrError.message;
      errorData = {
        error: {
          name: messageOrError.name,
          message: messageOrError.message,
          stack: messageOrError.stack,
          ...(messageOrError.code && { code: messageOrError.code }),
        },
      };
    }
    
    this.log(LOG_LEVELS.FATAL, message, {
      ...errorData,
      ...data,
    });
  }

  /**
   * Logs an API request
   * 
   * @param {Object} request - Fastify request object
   * @param {Object} data - Additional log data
   */
  logRequest(request, data = {}) {
    const { method, url, id, ip, headers } = request;
    
    this.info('API Request', {
      category: LOG_CATEGORIES.API,
      request: {
        method,
        url,
        id,
        ip,
        userAgent: headers['user-agent'],
      },
      ...data,
    });
  }

  /**
   * Logs an API response
   * 
   * @param {Object} request - Fastify request object
   * @param {Object} reply - Fastify reply object
   * @param {Object} data - Additional log data
   */
  logResponse(request, reply, data = {}) {
    const { method, url, id } = request;
    const { statusCode } = reply;
    const responseTime = reply.getResponseTime();
    
    this.info('API Response', {
      category: LOG_CATEGORIES.API,
      request: {
        method,
        url,
        id,
      },
      response: {
        statusCode,
        responseTime,
      },
      ...data,
    });
  }

  /**
   * Logs a database operation
   * 
   * @param {string} operation - Database operation
   * @param {string} model - Database model
   * @param {Object} data - Additional log data
   */
  logDatabase(operation, model, data = {}) {
    this.debug(`DB ${operation}`, {
      category: LOG_CATEGORIES.DATABASE,
      database: {
        operation,
        model,
      },
      ...data,
    });
  }

  /**
   * Logs a business event
   * 
   * @param {string} event - Business event
   * @param {Object} data - Additional log data
   */
  logBusinessEvent(event, data = {}) {
    this.info(event, {
      category: LOG_CATEGORIES.BUSINESS,
      ...data,
    });
  }
}

module.exports = {
  LoggerService,
  LOG_LEVELS,
  LOG_CATEGORIES,
};
