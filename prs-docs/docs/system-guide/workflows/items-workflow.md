# Item Management Workflow

This document outlines the complete workflow for items management in the PRS system, including OFM Items, OFM Lists, and Non-OFM Items.

## Workflow Diagram

```mermaid
flowchart TD
    Start([OFM Items Management]) --> S1[Sync OFM Items]
    S1 -->|Units Required| S2[Add Units to New Items]
    S1 -->|Steelbar Tagging| S3[Tag Items as Steelbar]
    S3 -->|Add Dimensions| S4[Add Steelbar Dimensions]
    S2 --> S5[OFM Items Updated]
    S4 --> S5
    S5 --> S6[Auto-Categorize by Project/Trade]
    S6 --> S7[OFM Lists Created]
    
    Start2([Non-OFM Items Management]) --> N1[Create Non-OFM Item]
    N1 --> N2[Non-OFM Item Created]
    N2 -->|Edit| N3[Update Non-OFM Item]
    
    RS1([Requisition Integration]) --> R1[Create RS with Items]
    R1 -->|OFM Item| R2[Select from OFM List]
    R1 -->|Non-OFM Item| R3[Select from Non-OFM List]
    R2 -->|RS Used Item| N4[Update Item History <br> OFM: Adjust Remaining GFQ]
    R3 -->|RS Used Item| N4
```

## Item Types

### OFM Items
- Official Fixed Materials from the master list
- Synced with Cityland's master list
- Categorized by company, project, and trade
- Includes account codes and GFQ (Guaranteed Fixed Quantity)

### Non-OFM Items
- Custom items created by users
- Not part of the master list
- Can be goods or services
- Account code generated by PRS

## User Access Levels

| User Type | OFM Items | Non-OFM Items |
|-----------|-----------|---------------|
| IT Admin | View Access | Full Access |
| Purchasing Admin | View Access | Full Access |
| Engineers | View Access | View Access |
| Purchasing Staff | View Access | Full Access |
| Purchasing Head | View Access | Full Access |

## OFM Items Management

### 1. OFM Items Synchronization

**Actor**: IT Admin, Purchasing Admin, Purchasing Staff, Purchasing Head

**Actions**:

- Access the OFM Items landing page
- Initiate sync with Cityland's Items Master List via Sync Button
- System pulls latest item data including:
  - Item Description
  - Account Code
  - Company and Trade (derived from Account Code)
  - GFQ (Guaranteed Fixed Quantity)

**Business Rules**:

- Sync button is disabled during synchronization
- Last sync date/time is displayed
- ItemCD is mapped to Account Code
- Units are initially set to "---" for new items
- Items are categorized by trade code in account code (positions 10-11)
  - 11xxx - Civil and Architectural Works
  - 12xxx - Mechanical Works
  - 13xxx - Electrical Works
  - 14xxx - Plumbing & Sanitary Works
  - 15xxx - Fire Protection Works
  - 16xxx - Bored Piles Work
  - 17xxx - Substructure Works

### 2. Units Assignment for New Items

**Actor**: Purchasing Staff, IT Admin, Purchasing Admin

**Actions**:

- After synchronization, system identifies items without units
- User prompted to assign units via modal
- User selects from standard unit options or adds custom unit

**Business Rules**:

- Units selection uses standardized options:
    - pc, lot, pack, unit, set, m, gal, liter, bundle, kilo, yard, ream, box, bottle, pair, roll, dozen, can, unit, tin
- Custom units can be added and saved for future use
- All items must have a unit assigned

### 3. Steelbar Tagging and Configuration

**Actor**: Purchasing Staff, IT Admin, Purchasing Admin

**Actions**:

- Tag items as steelbars during/after synchronization 
- Configure steelbar-specific attributes:
  - Grade (40, 60, 80)
  - Diameter (varies by grade)
  - Length (6.0m, 7.5m, 9.0m, 10.5m, 12.0m)
  - Weight

**Business Rules**:

- Default steelbar setting is "No"
- Steelbar items get special handling in requisitions
- Steelbar dimensions affect available quantity calculations
- Weight is measured and tracked in kg

### 4. OFM Item Editing

**Actor**: Purchasing Staff, IT Admin, Purchasing Admin

**Actions**:

- Edit units for existing OFM items
- Update steelbar configurations
- View item history including requisitions and deliveries

**Business Rules**:

- Most item fields are non-editable (Item Code, Description, Account Code, etc.)
- Units can be updated using standard options or custom entries
- Updates to items should cascade to related requisitions

### 5. OFM Lists Auto-Creation

**Actor**: System (PRS)

**Actions**:

- Categorize OFM items by Company, Project, and Trade
- Create pre-defined OFM lists based on these categories
- Distribute lists to users based on assigned Project and Trade

**Business Rules**:

- Lists are automatically maintained based on synchronized items
- Each OFM List contains items matching specific Company/Project/Trade combination
- Lists are visible to all users with matching Project/Trade assignments

### 6. Remaining GFQ Calculation

**Actor**: System (PRS)

**Actions**:

- Track initial GFQ for each item
- Calculate Remaining GFQ based on requisition usage
- Update Remaining GFQ after the following events:
    - RS Submission
    - RS Approval (quantity changes)
    - RS Cancellation
    - PO Force Close
    - RS Force Close

**Business Rules**:

- For first request: GFQ - Request Quantity = Remaining GFQ
- For subsequent requests: Last Remaining GFQ - Request Quantity = New Remaining GFQ
- For quantity changes during approval: depends on quantity change (additive or deductive to GFQ)
- For cancelled RS: Last Remaining GFQ + Cancelled Quantity = New Remaining GFQ
- Remaining GFQ is visible item details

## Non-OFM Items Management

### 1. Non-OFM Item Creation

**Actor**: Engineering Group, Purchasing Staff, IT Admin, Purchasing Admin

**Actions**:

- Access Non-OFM Items landing page
- Create new Non-OFM items with required details
- Assign item type, unit, and description

**Business Rules**:

- Item Name: Alphanumeric and special characters, max 100 characters
- Item Type: Goods or Service
- Unit: Selected from standardized list
- Description: Max 100 characters
- Account code is auto-generated during creation

### 2. Non-OFM Item Editing

**Actor**: Engineering Group, Purchasing Staff, IT Admin, Purchasing Admin

**Actions**:

- Edit existing Non-OFM items
- Update item details

**Business Rules**:

- Same validation rules as creation apply
- Field updates are validated before saving
- History of changes is maintained

## Integration with Requisition System

### 1. OFM Items in Requisitions

**Actor**: Requisition Users

**Actions**:

- Select OFM items from applicable OFM Lists based on user's Project/Trade
- Specify required quantity

**Business Rules**:

- Only OFM Lists matching user's Project/Trade are available
- Selected quantity is validated against Remaining GFQ
- Requested quantity is deducted from Remaining GFQ upon submission
- For steelbars, special handling applies based on dimensions
- Item history is updated to reflect usage in requisitions

### 2. Non-OFM Items in Requisitions

**Actor**: Requisition Users

**Actions**:

- Select from available Non-OFM items

**Business Rules**:

- Non-OFM items are available to all users regardless of Project/Trade
- Item history is updated to reflect usage in requisitions

## Item History Tracking

**Actor**: System (PRS)

**Actions**:

- Track all requisitions and deliveries for each item
- Maintain history of quantity changes
- Display history in item details view

**Business Rules**:

- History shows RS Number, Price, Request/Delivery dates, and quantities
- Default sorting is by RS Number
- History data is read-only

## Example Scenarios

### Scenario 1: OFM Item Management
1. IT Admin syncs items from master list
2. System creates OFM lists based on company/project/trade
3. Engineers view and use items in requisitions
4. Purchasing staff track item history

### Scenario 2: Non-OFM Item Creation
1. Engineering Group creates new non-OFM item
2. Item is associated with company and project
3. Item becomes available for requisitions
4. Purchasing staff can track item usage

## Common Issues and Solutions

### Issue 1: Missing Units for OFM Items

**Cause**: New items were synchronized without units being assigned.

**Solution**: 

- Access OFM Items landing page
- Identify items with "---" as unit
- Assign proper units through the edit functionality
- Complete all missing units before using in requisitions

### Issue 2: Cannot Find OFM List

**Cause**: User's Project/Trade assignment doesn't match any available OFM List.

**Solution**:

- Verify user's Project and Trade assignments in user management
- Check if OFM items exist for the user's Project/Trade combination
- If no items exist, either create Non-OFM items or have admin update Project/Trade assignments

### Issue 3: Item Sync Failures

**Solution**:

- Ensure proper API connection with Cityland
- Verify network connectivity
- Check API credentials

### Issue 4: Missing Item History

**Solution**:

- Verify item has been used in requisitions
- Check if item is properly linked to company/project
- Ensure proper tracking of deliveries 

## Implementation Details

### Key Files

- **Controllers**: 
  - `src/app/handlers/controllers/ofmItemController.js`
  - `src/app/handlers/controllers/nonOfmItemController.js`
  - `src/app/handlers/controllers/ofmListController.js`

- **Services**: 
  - `src/app/services/ofmItemService.js`
  - `src/app/services/nonOfmItemService.js` 
  - `src/app/services/ofmListService.js`

- **Repositories**: 
  - `src/infra/repositories/ofmItemRepository.js`
  - `src/infra/repositories/nonOfmItemRepository.js`
  - `src/infra/repositories/ofmListRepository.js`

- **Entities**: 
  - `src/domain/entities/ofmItemEntity.js`
  - `src/domain/entities/nonOfmItemEntity.js`
  - `src/domain/entities/ofmListEntity.js`

- **Constants**: 
  - `src/domain/constants/itemConstants.js`
