const BaseController = require('./baseController');

/**
 * Example controller that extends BaseController
 */
class ExampleController extends BaseController {
  /**
   * Creates a new ExampleController instance
   * 
   * @param {Object} container - Dependency injection container
   */
  constructor(container) {
    super(container);
    
    const { exampleService, entities } = container;
    
    this.exampleService = exampleService;
    this.exampleEntity = entities.example;
  }

  /**
   * Creates a new example
   * 
   * @param {Object} request - Fastify request
   * @param {Object} reply - Fastify reply
   * @returns {Promise<Object>} - Fastify reply
   */
  async createExample(request, reply) {
    return this.executeAction(async () => {
      const { body, userFromToken } = request;
      
      // Validate request body
      const validatedData = this.validate(
        this.exampleEntity.createExampleSchema,
        body
      );
      
      // Add user ID to data
      const data = {
        ...validatedData,
        createdBy: userFromToken.id
      };
      
      // Create example
      const example = await this.exampleService.createExample(data);
      
      // Return created response
      return this.sendCreated(reply, example, 'Example created successfully');
    }, request, reply);
  }

  /**
   * Updates an existing example
   * 
   * @param {Object} request - Fastify request
   * @param {Object} reply - Fastify reply
   * @returns {Promise<Object>} - Fastify reply
   */
  async updateExample(request, reply) {
    return this.executeAction(async () => {
      const { body, params, userFromToken } = request;
      const { id } = params;
      
      // Validate request body
      const validatedData = this.validate(
        this.exampleEntity.updateExampleSchema,
        body
      );
      
      // Add updated by user ID
      const data = {
        ...validatedData,
        updatedBy: userFromToken.id
      };
      
      // Update example
      await this.exampleService.updateExample(id, data);
      
      // Get updated example
      const example = await this.exampleService.getExampleById(id);
      
      // Return success response
      return this.sendSuccess(reply, example, 200, 'Example updated successfully');
    }, request, reply);
  }

  /**
   * Deletes an example
   * 
   * @param {Object} request - Fastify request
   * @param {Object} reply - Fastify reply
   * @returns {Promise<Object>} - Fastify reply
   */
  async deleteExample(request, reply) {
    return this.executeAction(async () => {
      const { params } = request;
      const { id } = params;
      
      // Delete example
      await this.exampleService.deleteExample(id);
      
      // Return no content response
      return this.sendNoContent(reply);
    }, request, reply);
  }

  /**
   * Gets an example by ID
   * 
   * @param {Object} request - Fastify request
   * @param {Object} reply - Fastify reply
   * @returns {Promise<Object>} - Fastify reply
   */
  async getExampleById(request, reply) {
    return this.executeAction(async () => {
      const { params } = request;
      const { id } = params;
      
      // Get example
      const example = await this.exampleService.getExampleById(id);
      
      // Return success response
      return this.sendSuccess(reply, example);
    }, request, reply);
  }

  /**
   * Gets all examples with pagination
   * 
   * @param {Object} request - Fastify request
   * @param {Object} reply - Fastify reply
   * @returns {Promise<Object>} - Fastify reply
   */
  async getAllExamples(request, reply) {
    return this.executeAction(async () => {
      const { query } = request;
      
      // Get examples
      const examples = await this.exampleService.getAllExamples(query);
      
      // Return success response
      return this.sendSuccess(reply, examples);
    }, request, reply);
  }
}

module.exports = ExampleController;
