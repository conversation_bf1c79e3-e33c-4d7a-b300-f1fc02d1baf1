"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { z } from "zod";
import { Form, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/Form/Form";
import { Input } from "@/components/ui/Form/Input";
import { Button } from "@/components/ui/Button/Button";
import { AuthLayout } from "@/components/layouts/AuthLayout";
import { AuthService } from "@/lib/auth/auth-service";
import { ActionModal } from "@/components/ui/Modal";

// Validation schemas
const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

const otpSchema = z.object({
  otp: z.string().min(1, "OTP code is required"),
});

const forgotPasswordSchema = z.object({
  forgotPasswordUsername: z.string().min(1, "Username is required"),
});

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/app/dashboard";
  const error = searchParams.get("error");

  // State management
  const [authState, setAuthState] = useState<{
    showOtpInput: boolean;
    showOtpSetup: boolean;
    showPasswordUpdate: boolean;
    loading: boolean;
    errorMessage: string;
    credentials: { username: string; password: string };
    otpSecret?: string;
    otpAuthUrl?: string;
  }>({
    showOtpInput: false,
    showOtpSetup: false,
    showPasswordUpdate: false,
    loading: false,
    errorMessage: error || "",
    credentials: { username: "", password: "" },
  });

  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  // Handle regular login
  const handleLogin = async (values: z.infer<typeof loginSchema>) => {
    setAuthState({
      ...authState,
      loading: true,
      errorMessage: "",
      credentials: { username: values.username, password: values.password },
    });

    try {
      const response = await AuthService.login(values.username, values.password);

      if (!response.success) {
        setAuthState({
          ...authState,
          loading: false,
          errorMessage: response.error || "Failed to sign in",
          credentials: { username: values.username, password: values.password },
        });
        return;
      }

      // Handle different auth flows based on response
      if (response.requireOTPVerification) {
        setAuthState({
          ...authState,
          loading: false,
          showOtpInput: true,
          credentials: { username: values.username, password: values.password },
        });
      } else if (response.requireOTPSetup) {
        setAuthState({
          ...authState,
          loading: false,
          showOtpSetup: true,
          otpSecret: response.otpSecret,
          otpAuthUrl: response.otpAuthUrl,
          credentials: { username: values.username, password: values.password },
        });
        // In a real implementation, we would redirect to the OTP setup page
        router.push("/auth/setup-otp");
      } else if (response.requireUpdatePassword) {
        setAuthState({
          ...authState,
          loading: false,
          showPasswordUpdate: true,
          credentials: { username: values.username, password: values.password },
        });
        // In a real implementation, we would redirect to the password update page
        router.push("/auth/create-password");
      } else {
        // Successful login
        router.push(callbackUrl);
      }
    } catch (error) {
      console.error("Login error:", error);
      setAuthState({
        ...authState,
        loading: false,
        errorMessage: "An unexpected error occurred",
        credentials: { username: values.username, password: values.password },
      });
    }
  };

  // Handle OTP verification
  const handleOtpVerification = async (values: z.infer<typeof otpSchema>) => {
    setAuthState({
      ...authState,
      loading: true,
      errorMessage: "",
    });

    try {
      const response = await AuthService.verifyOTP(
        authState.credentials.username,
        authState.credentials.password,
        values.otp
      );

      if (!response.success) {
        setAuthState({
          ...authState,
          loading: false,
          errorMessage: response.error || "Failed to verify OTP",
        });
        return;
      }

      // Successful verification
      router.push(callbackUrl);
    } catch (error) {
      console.error("OTP verification error:", error);
      setAuthState({
        ...authState,
        loading: false,
        errorMessage: "An unexpected error occurred",
      });
    }
  };

  // Handle forgot password
  const handleForgotPassword = async (values: z.infer<typeof forgotPasswordSchema>) => {
    try {
      const response = await AuthService.forgotPassword(values.forgotPasswordUsername);

      setNotificationMessage({
        type: response.success ? "success" : "error",
        message: response.message,
      });

      setShowForgotPasswordModal(false);
    } catch (error) {
      console.error("Forgot password error:", error);
      setNotificationMessage({
        type: "error",
        message: "An unexpected error occurred",
      });
      setShowForgotPasswordModal(false);
    }
  };

  // Login form content
  const LoginFormContent = (
    <>
      <div className="flex flex-col gap-1 mb-4">
        <h2 className="text-2xl font-bold text-red-950">Welcome User</h2>
        <p className="text-sm text-gray-600">Please Login to continue</p>
      </div>

      {authState.errorMessage && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 my-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{authState.errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      {notificationMessage && (
        <div className={`p-4 my-4 border-l-4 ${notificationMessage.type === "success"
          ? "bg-green-50 border-green-500"
          : "bg-red-50 border-red-500"
          }`}>
          <div className="flex">
            <div className="ml-3">
              <p className={`text-sm ${notificationMessage.type === "success"
                ? "text-green-700"
                : "text-red-700"
                }`}>
                {notificationMessage.message}
              </p>
            </div>
          </div>
        </div>
      )}

      {!authState.showOtpInput ? (
        <Form
          schema={loginSchema}
          onSubmit={handleLogin}
          className="mt-3"
          options={{
            shouldUnregister: true,
          }}
          watchFields={['username', 'password']}
        >
          {({ register, formState, disabled }) => (
            <div className="flex flex-col gap-5">
              <Input
                type="text"
                label="Username"
                error={formState.errors['username']?.message}
                registration={register('username')}
                placeholder="johndoe3245"
                disabled={authState.loading}
              />
              <div>
                <Input
                  type="password"
                  label="Password"
                  error={formState.errors['password']?.message}
                  registration={register('password')}
                  placeholder="1234"
                  disabled={authState.loading}
                />
                <div className="flex justify-end">
                  <Button
                    type="button"
                    hover="link"
                    variant="link"
                    onClick={() => setShowForgotPasswordModal(true)}
                    className="justify-end font-normal text-sm p-0 mt-0.5"
                  >
                    Forgot Password?
                  </Button>
                </div>
              </div>

              <Button
                disabled={disabled || authState.loading}
                isLoading={authState.loading}
                className="mt-1"
                type="submit"
                variant="submit"
                hover="submit"
              >
                Log in
              </Button>
            </div>
          )}
        </Form>
      ) : (
        <Form
          schema={otpSchema}
          onSubmit={handleOtpVerification}
          className="mt-4"
          options={{
            shouldUnregister: true,
          }}
          watchFields={['otp']}
        >
          {({ register, formState, disabled }) => (
            <div className="flex flex-col gap-4">
              <Input
                type="text"
                label="OTP Code"
                error={formState.errors['otp']?.message}
                registration={register('otp')}
                placeholder="Enter your OTP code"
                disabled={authState.loading}
              />

              <Button
                disabled={disabled || authState.loading}
                isLoading={authState.loading}
                className="my-2"
                type="submit"
              >
                Verify OTP
              </Button>
            </div>
          )}
        </Form>
      )}
    </>
  );

  return (
    <AuthLayout>
      {LoginFormContent}

      {/* Forgot Password Modal */}
      {showForgotPasswordModal && (
        <div
          className={`fixed inset-0 z-50 flex items-center justify-center ${showForgotPasswordModal ? 'block' : 'hidden'}`}
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setShowForgotPasswordModal(false)}></div>
          <div className="relative w-[550px] rounded-md bg-white shadow-xl overflow-hidden">
            <div className="bg-[#7a5c5f] text-white py-3 px-4 text-center">
              <h3 className="text-base font-medium">Forgot Password</h3>
            </div>
            <div className="flex flex-col gap-4 p-6">
              <p className="text-sm text-gray-700 mb-4">
                You are about to reset your password. If you want to proceed with
                this action please enter your username and press continue.
              </p>
              <Form
                schema={forgotPasswordSchema}
                onSubmit={handleForgotPassword}
                options={{
                  shouldUnregister: true,
                }}
                watchFields={['forgotPasswordUsername']}
                hasErrorSpace={false}
              >
                {({ register, formState, disabled }) => (
                  <div className="flex flex-col gap-4">
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                      <input
                        type="text"
                        className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 text-sm"
                        {...register('forgotPasswordUsername')}
                      />
                      {formState.errors['forgotPasswordUsername']?.message && (
                        <p className="mt-1 text-sm text-red-500">{formState.errors['forgotPasswordUsername']?.message}</p>
                      )}
                    </div>

                    <div className="flex gap-3 justify-end">
                      <button
                        type="button"
                        onClick={() => setShowForgotPasswordModal(false)}
                        className="w-40 bg-white border border-gray-300 text-gray-700 rounded-md py-2 px-4 text-sm font-medium hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={disabled}
                        className="w-40 rounded-md py-2 px-4 bg-[#7a5c5f] hover:bg-[#797a7b] text-white text-sm font-medium"
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                )}
              </Form>
            </div>
          </div>
        </div>
      )}
    </AuthLayout>
  );
}
