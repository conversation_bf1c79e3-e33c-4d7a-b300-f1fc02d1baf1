'use client';

import React from 'react';
import Link from 'next/link';

interface DocPageProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  lastUpdated?: string;
  prevPage?: {
    title: string;
    href: string;
  };
  nextPage?: {
    title: string;
    href: string;
  };
}

export function DocPage({
  title,
  description,
  children,
  lastUpdated,
  prevPage,
  nextPage,
}: DocPageProps) {
  return (
    <div className="min-h-full">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-primary-700 mb-4">{title}</h1>
        {description && <p className="text-lg text-gray-600 mb-6">{description}</p>}
        {lastUpdated && (
          <p className="text-sm text-gray-500">Last updated: {lastUpdated}</p>
        )}
      </div>

      <div className="prose prose-lg max-w-none">{children}</div>

      {(prevPage || nextPage) && (
        <div className="mt-12 pt-6 border-t border-gray-200 flex justify-between">
          {prevPage ? (
            <Link
              href={prevPage.href}
              className="inline-flex items-center text-primary-600 hover:text-primary-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              {prevPage.title}
            </Link>
          ) : (
            <div />
          )}

          {nextPage && (
            <Link
              href={nextPage.href}
              className="inline-flex items-center text-primary-600 hover:text-primary-800"
            >
              {nextPage.title}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 ml-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
