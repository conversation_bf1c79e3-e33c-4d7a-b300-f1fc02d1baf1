/**
 * Application Logger
 * 
 * A simplified logger for application events that produces clean,
 * readable logs focused on business events rather than HTTP requests.
 */

const { logger } = require('./index');

/**
 * Log an application event
 * @param {string} event - The event name
 * @param {object} data - Additional data to log
 */
const logEvent = (event, data = {}) => {
  logger.info({
    event,
    ...data,
    timestamp: new Date().toISOString(),
  });
};

/**
 * Log an error event
 * @param {string} event - The event name
 * @param {Error|string} error - The error object or message
 * @param {object} data - Additional data to log
 */
const logError = (event, error, data = {}) => {
  const errorObj = error instanceof Error 
    ? { message: error.message, stack: error.stack }
    : { message: error };
    
  logger.error({
    event,
    error: errorObj,
    ...data,
    timestamp: new Date().toISOString(),
  });
};

/**
 * Log a user action
 * @param {string} action - The action performed
 * @param {string} userId - The user ID
 * @param {object} data - Additional data to log
 */
const logUserAction = (action, userId, data = {}) => {
  logger.info({
    action,
    userId,
    ...data,
    timestamp: new Date().toISOString(),
  });
};

module.exports = {
  logEvent,
  logError,
  logUserAction,
};
