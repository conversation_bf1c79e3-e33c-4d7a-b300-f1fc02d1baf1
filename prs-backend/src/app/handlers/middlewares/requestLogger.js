/**
 * Request Logger Middleware
 *
 * This middleware implements production-grade logging for API requests and responses:
 * 1. Selectively logs requests based on endpoint and importance
 * 2. Implements sampling for high-volume endpoints
 * 3. Always logs errors and slow responses
 * 4. Adds business context to logs
 * 5. Protects sensitive information
 */
const { formatRequestLog, formatResponseLog, sanitizeBody } = require('../../../infra/logs/logFormatter');
const { LOG_CATEGORIES } = require('../../../infra/logs/loggerService');

// High-volume endpoints that should use log sampling
const HIGH_VOLUME_ENDPOINTS = [
  '/health',
  '/healthz',
  '/v1/health',
  '/v1/metrics',
  '/v1/status',
];

// Sensitive endpoints that should have extra logging
const SENSITIVE_ENDPOINTS = [
  '/v1/auth/login',
  '/v1/auth/refresh',
  '/v1/users',
  '/v1/requisitions/submit',
  '/v1/requisitions/approve',
];

// Sampling rates for different types of endpoints
const SAMPLING_RATES = {
  high_volume: 0.05,  // Log 5% of high-volume endpoints
  normal: 0.2,        // Log 20% of normal endpoints
  sensitive: 1.0,     // Log 100% of sensitive endpoints
};

// Performance thresholds for response times (in ms)
const PERFORMANCE_THRESHOLDS = {
  slow: 500,          // Responses over 500ms are considered slow
  very_slow: 2000,    // Responses over 2000ms are considered very slow
};

/**
 * Determines if a request should be sampled based on its URL
 *
 * @param {string} url - Request URL
 * @returns {boolean} - Whether to log this request
 */
function shouldSampleRequest(url) {
  // Always log sensitive endpoints
  if (SENSITIVE_ENDPOINTS.some(endpoint => url.startsWith(endpoint))) {
    return Math.random() < SAMPLING_RATES.sensitive;
  }

  // Sample high-volume endpoints at a lower rate
  if (HIGH_VOLUME_ENDPOINTS.some(endpoint => url === endpoint || url.startsWith(endpoint))) {
    return Math.random() < SAMPLING_RATES.high_volume;
  }

  // Sample normal endpoints
  return Math.random() < SAMPLING_RATES.normal;
}

/**
 * Gets the endpoint type based on URL
 *
 * @param {string} url - Request URL
 * @returns {string} - Endpoint type
 */
function getEndpointType(url) {
  if (SENSITIVE_ENDPOINTS.some(endpoint => url.startsWith(endpoint))) {
    return 'sensitive';
  }

  if (HIGH_VOLUME_ENDPOINTS.some(endpoint => url === endpoint || url.startsWith(endpoint))) {
    return 'high_volume';
  }

  return 'normal';
}

/**
 * Middleware to log incoming requests and responses
 *
 * @param {Object} request - Fastify request object
 * @param {Object} reply - Fastify reply object
 * @param {Function} done - Callback function
 */
const requestLogger = function (request, reply, done) {
  const logger = this.diScope.resolve('logger');
  const startTime = Date.now();

  // Skip logging for health check endpoints entirely in production
  if ((request.url === '/health' || request.url === '/healthz') &&
      process.env.NODE_ENV === 'production') {
    done();
    return;
  }

  // Get user from token if available
  const user = request.userFromToken || {};

  // Store user in request context for later use in logs
  request.requestContext.set('user', user);

  // Store request start time for calculating duration
  request.requestContext.set('requestStartTime', startTime);

  // Determine if this request should be sampled for logging
  const shouldSample = shouldSampleRequest(request.url);
  request.requestContext.set('logSampled', shouldSample);

  // Get endpoint type for context
  const endpointType = getEndpointType(request.url);

  // Log the request (if sampled)
  if (shouldSample) {
    // Create a more descriptive message that includes the HTTP method and URL
    const logMessage = `${request.method} ${request.url}`;

    // Enhanced request logging with more metadata
    logger.info(logMessage, {
      category: LOG_CATEGORIES.API,
      event: 'request',
      ...formatRequestLog(request),
      // Enhanced user context
      user: user.id ? {
        id: user.id,
        username: user.username,
        role: user.role?.name,
        department: user.department?.name,
      } : undefined,
      // Enhanced request metadata
      endpointType,
      sampled: true,
      reqId: request.id, // Add request ID for correlation
      // Add geo information if available
      geo: {
        ip: request.headers['x-forwarded-for'] || request.ip,
        // These fields would be populated by a geo IP service if implemented
        country: request.headers['cf-ipcountry'] || undefined, // Cloudflare header
        region: undefined,
        city: undefined,
      },
      // Add performance tracking
      performance: {
        startTime: Date.now(),
      },
    });
  }

  // Log the response when it's sent
  if (typeof reply.onSend === 'function') {
    reply.onSend((request, reply, payload, done) => {
      const responseTime = reply.getResponseTime();
      const shouldSample = request.requestContext.get('logSampled');
      const statusCode = reply.statusCode;
      const isError = statusCode >= 400;
      const isSlow = responseTime > PERFORMANCE_THRESHOLDS.slow;
      const isVerySlow = responseTime > PERFORMANCE_THRESHOLDS.very_slow;

      // Always log errors and slow responses, otherwise respect sampling
      if (isError || isSlow || shouldSample) {
        // Determine log level based on status and performance
        let logLevel = 'info';
        let logMessage;

        // Create a descriptive message that includes method, URL, status code and time
        if (isError) {
          logLevel = 'error';
          logMessage = `${request.method} ${request.url} ${statusCode} ERROR (${responseTime.toFixed(2)}ms)`;
        } else if (isVerySlow) {
          logLevel = 'warn';
          logMessage = `${request.method} ${request.url} ${statusCode} VERY SLOW (${responseTime.toFixed(2)}ms)`;
        } else if (isSlow) {
          logLevel = 'warn';
          logMessage = `${request.method} ${request.url} ${statusCode} SLOW (${responseTime.toFixed(2)}ms)`;
        } else {
          logMessage = `${request.method} ${request.url} ${statusCode} (${responseTime.toFixed(2)}ms)`;
        }

        // Create the log entry with enhanced metadata
        const logEntry = {
          category: LOG_CATEGORIES.API,
          event: 'response',
          request: {
            id: request.id,
            method: request.method,
            url: request.url,
            path: request.routerPath || request.url.split('?')[0],
            ip: request.headers['x-forwarded-for'] || request.ip,
            userAgent: request.headers['user-agent'],
            referer: request.headers['referer'],
            host: request.headers['host'],
          },
          ...formatResponseLog(reply, isError ? payload : undefined), // Only include payload for errors
          responseTime,
          // Enhanced performance tracking
          performance: {
            slow: isSlow,
            verySlow: isVerySlow,
            ...(isSlow && { threshold: PERFORMANCE_THRESHOLDS.slow }),
            startTime: request.requestContext.get('requestStartTime'),
            endTime: Date.now(),
            totalTime: Date.now() - (request.requestContext.get('requestStartTime') || Date.now()),
          },
          // Enhanced user context
          user: user.id ? {
            id: user.id,
            username: user.username,
            role: user.role?.name,
            department: user.department?.name,
          } : undefined,
          // Enhanced request metadata
          endpointType,
          sampled: shouldSample,
          reqId: request.id, // Add request ID for correlation
          // Add geo information if available
          geo: {
            ip: request.headers['x-forwarded-for'] || request.ip,
            country: request.headers['cf-ipcountry'] || undefined, // Cloudflare header
          },
        };

        // Add business context for specific endpoints
        if (request.url.includes('/requisitions/')) {
          try {
            const requisitionId = request.params?.requisitionId ||
                                 (request.body && request.body.requisitionId);
            if (requisitionId) {
              logEntry.business = {
                ...logEntry.business,
                requisitionId,
              };
            }
          } catch (e) {
            // Ignore errors in extracting business context
          }
        }

        // Log with appropriate level
        logger[logLevel](logMessage, logEntry);
      }

      done(null, payload);
    });
  } else {
    // Log a warning if onSend is not available (only in development)
    if (process.env.NODE_ENV !== 'production') {
      logger.debug('reply.onSend is not available for this route', {
        category: LOG_CATEGORIES.API,
        route: request.routerPath || request.url
      });
    }
  }

  done();
};

module.exports = requestLogger;
