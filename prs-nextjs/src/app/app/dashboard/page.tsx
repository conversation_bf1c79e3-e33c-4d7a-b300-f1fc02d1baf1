"use client";

import { useSession } from "next-auth/react";
import Link from "next/link";
import { Button } from "@/components/ui/Button";

export default function DashboardPage() {
  const { data: session } = useSession();

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-500">Welcome to the Purchase Requisition System</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-custom p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">My Requisitions</h2>
            <span className="bg-primary-700 text-white text-sm py-1 px-3 rounded-full">12</span>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-500">Draft</span>
              <span className="font-medium">3</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-500">For Approval</span>
              <span className="font-medium">5</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-500">Approved</span>
              <span className="font-medium">4</span>
            </div>
          </div>
          <div className="mt-6">
            <Link href="/app/requisitions">
              <Button variant="outline" hover="outline" className="w-full">
                View All Requisitions
              </Button>
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-custom p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Purchase Orders</h2>
            <span className="bg-secondary-600 text-white text-sm py-1 px-3 rounded-full">8</span>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-500">Draft</span>
              <span className="font-medium">2</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-500">For Approval</span>
              <span className="font-medium">3</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-500">Approved</span>
              <span className="font-medium">3</span>
            </div>
          </div>
          <div className="mt-6">
            <Link href="/app/purchase-orders">
              <Button variant="outline" hover="outline" className="w-full">
                View All Purchase Orders
              </Button>
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-custom p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Payments</h2>
            <span className="bg-success-500 text-white text-sm py-1 px-3 rounded-full">5</span>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-500">Pending</span>
              <span className="font-medium">2</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-500">For Approval</span>
              <span className="font-medium">1</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-500">Completed</span>
              <span className="font-medium">2</span>
            </div>
          </div>
          <div className="mt-6">
            <Link href="/app/payments">
              <Button variant="outline" hover="outline" className="w-full">
                View All Payments
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-custom p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
          <div className="space-y-4">
            <div className="border-l-4 border-primary-700 pl-4 py-1">
              <p className="font-medium">Requisition #RS-2023-0042 approved</p>
              <p className="text-sm text-gray-500">2 hours ago</p>
            </div>
            <div className="border-l-4 border-secondary-600 pl-4 py-1">
              <p className="font-medium">Purchase Order #PO-2023-0038 created</p>
              <p className="text-sm text-gray-500">Yesterday at 3:45 PM</p>
            </div>
            <div className="border-l-4 border-success-500 pl-4 py-1">
              <p className="font-medium">Payment #PR-2023-0021 completed</p>
              <p className="text-sm text-gray-500">2 days ago</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-custom p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">User Information</h2>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Name</p>
              <p className="font-medium">{session?.user?.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Email</p>
              <p className="font-medium">{session?.user?.email}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Role</p>
              <p className="font-medium">{session?.user?.role}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Department</p>
              <p className="font-medium">Finance Department</p>
            </div>
          </div>
          <div className="mt-6">
            <Link href="/app/profile">
              <Button variant="outline" hover="outline" className="w-full">
                View Profile
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
