import { prisma } from "@/lib/db";

interface NotificationData {
  title: string;
  message: string;
  type: string;
  referenceId?: number;
  referenceType?: string;
}

export async function sendNotification(
  userId: number,
  data: NotificationData
) {
  try {
    await prisma.notification.create({
      data: {
        userId,
        title: data.title,
        message: data.message,
        type: data.type,
        referenceId: data.referenceId,
        referenceType: data.referenceType,
        read: false
      }
    });
  } catch (error) {
    console.error("Error sending notification:", error);
    throw error;
  }
}

export async function sendNotificationToMultipleUsers(
  userIds: number[],
  data: NotificationData
) {
  try {
    await prisma.notification.createMany({
      data: userIds.map(userId => ({
        userId,
        title: data.title,
        message: data.message,
        type: data.type,
        referenceId: data.referenceId,
        referenceType: data.referenceType,
        read: false
      }))
    });
  } catch (error) {
    console.error("Error sending notifications to multiple users:", error);
    throw error;
  }
}

export async function getUnreadNotificationsCount(userId: number) {
  try {
    return await prisma.notification.count({
      where: {
        userId,
        read: false
      }
    });
  } catch (error) {
    console.error("Error getting unread notifications count:", error);
    throw error;
  }
}
