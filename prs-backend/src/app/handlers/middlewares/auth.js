/**
 * Authentication and authorization middleware
 * 
 * This module provides middleware for authentication and authorization
 */
const {
  TOKEN_TYPES,
  createTokenVerifier,
  createPermissionChecker,
  compose,
  withLogging,
} = require('./utils');

/**
 * Middleware to verify access token and authenticate user
 */
const verifyAccessToken = withLogging(
  createTokenVerifier(TOKEN_TYPES.ACCESS),
  { name: 'verifyAccessToken' }
);

/**
 * Middleware to verify OTP token
 */
const verifyOTPToken = withLogging(
  createTokenVerifier(TOKEN_TYPES.OTP),
  { name: 'verifyOTPToken' }
);

/**
 * Middleware to verify temporary password token
 */
const verifyTempPassToken = withLogging(
  createTokenVerifier(TOKEN_TYPES.TEMP_PASS),
  { name: 'verifyTempPassToken' }
);

/**
 * Middleware to verify refresh token
 */
const verifyRefreshToken = withLogging(
  createTokenVerifier(TOKEN_TYPES.REFRESH, {
    userFetcher: async (userRepository, userId, options) => {
      return await userRepository.getById(userId, userRepository.getUserOption);
    }
  }),
  { name: 'verifyRefreshToken' }
);

/**
 * Creates a middleware to check user permissions
 * 
 * @param {Object|Array} requiredPermissions - Required permissions
 * @param {Object} options - Options
 * @param {boolean} options.requireAll - Whether all permissions are required
 * @returns {Function} - Permission checker middleware
 */
const checkPermission = (requiredPermissions, options = {}) => {
  return withLogging(
    createPermissionChecker(requiredPermissions, options),
    { name: 'checkPermission' }
  );
};

/**
 * Middleware to authenticate user and check permissions
 * 
 * @param {Object|Array} requiredPermissions - Required permissions
 * @param {Object} options - Options
 * @param {boolean} options.requireAll - Whether all permissions are required
 * @returns {Function} - Composed middleware
 */
const authenticateAndAuthorize = (requiredPermissions, options = {}) => {
  return compose(
    verifyAccessToken,
    checkPermission(requiredPermissions, options)
  );
};

module.exports = {
  verifyAccessToken,
  verifyOTPToken,
  verifyTempPassToken,
  verifyRefreshToken,
  checkPermission,
  authenticateAndAuthorize,
};
