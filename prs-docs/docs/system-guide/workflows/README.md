# PRS Workflow Documentation

This directory contains documentation for the key workflows in the PRS system. Each workflow document outlines the complete process flow, status definitions, business rules, and implementation details.

## Core Workflows

Prereq??
- item management

### 1. [Requisition Workflow](./requisition-workflow.md)

The requisition workflow covers the process of creating, submitting, approving, and assigning requisitions. It is the starting point of the procurement process.

**Key Statuses**: DRAFT, SUBMITTED, PARTIALLY_APPROVED, APPROVED, REJECTED, ASSIGNED, CANVASSING, FOR_PO_REVIEW, FOR_DELIVERY, CLOSED

### 2. [Canvass Workflow](./canvass-workflow.md)

The canvass workflow covers the process of collecting and comparing supplier quotations for items in a requisition.

**Key Statuses**: DRAFT, PARTIALLY_CANVASSED, FOR_APPROVAL, APPROVED, REJECTED

### 3. [Purchase Order Workflow](./purchase-order-workflow.md)

The purchase order workflow covers the process of creating, reviewing, approving, and delivering purchase orders.

**Key Statuses**: FOR_PO_REVIEW, FOR_PO_APPROVAL, FOR_DELIVERY, REJECT_PO, CANCELLED_PO

### 4. [Delivery Workflow](./delivery-workflow.md)

The delivery workflow covers the process of receiving goods, creating delivery receipts, and processing invoices.

**Key Statuses**: DRAFT, SUBMITTED, FOR_INVOICE_RECEIVING

### 5. [Payment Workflow](./payment-workflow.md)

The payment workflow covers the process of creating, approving, and processing payment requests.

**Key Statuses**: DRAFT, FOR_PR_APPROVAL, SUBMITTED, APPROVED, REJECTED

### 6. [Supplier Management Workflow](./supplier-workflow.md)

The supplier management workflow covers the process of creating, updating, and managing suppliers.

**Key Statuses**: DRAFT, ACTIVE, INACTIVE, SUSPENDED

### 7. [User Management Workflow](./user-management-workflow.md)

The user management workflow covers the process of creating, updating, and managing user accounts and permissions.

**Key Statuses**: PENDING, ACTIVE, INACTIVE, SUSPENDED

### 8. [Reporting Workflow](./reporting-workflow.md)

The reporting workflow covers the process of configuring, generating, and delivering reports.

**Key Processes**: CONFIGURATION, SCHEDULING, GENERATION, STORAGE, DELIVERY

### 9. [Master Data Management Workflow](./master-data-workflow.md)

The master data management workflow covers the process of creating, updating, and managing reference data such as companies, departments, projects, and other master data.

**Key Processes**: CREATION, VALIDATION, ACTIVATION, MAINTENANCE, DEACTIVATION

### 10. [Non-Requisition Workflow](./non-requisition-workflow.md)

The non-requisition workflow covers the process of creating, approving, and processing payment requests that don't follow the standard requisition process.

**Key Statuses**: DRAFT, FOR_APPROVAL, CLOSED, REJECTED, CANCELLED

### 11. [Notification System](./notification-workflow.md)

The notification system handles the creation and management of system notifications for various events.

**Key Components**: Notification Types, Notification Structure, Event Triggers

### 12. [Attachment System](./attachment-workflow.md)

The attachment system handles the management of files attached to various entities in the system.

**Key Components**: Attachment Models, Attachment Structure, Entity Associations

## Cross-Cutting Workflows

### 1. [Approval Workflow](./approval-workflow.md)

The approval workflow is a general workflow that applies to multiple entities in the system, including requisitions, canvasses, purchase orders, and payment requests.

**Key Concepts**: Approver Levels, Approver Types, Approval Status, Entity Status

## Complete Process Flow

The complete procurement process flow in the PRS system is as follows:

1. **Requisition**: User creates and submits a requisition
2. **Approval**: Requisition is approved by appropriate approvers
3. **Assignment**: Requisition is assigned to a Purchasing Staff
4. **Canvassing**: Purchasing Staff creates a canvass and collects supplier quotations
5. **Approval**: Canvass is approved by appropriate approvers
6. **Purchase Order**: System creates purchase orders for selected suppliers
7. **Approval**: Purchase orders are approved by appropriate approvers
8. **Delivery**: Goods are delivered and delivery receipts are created
9. **Payment**: Payment requests are created, approved, and processed

## Status Flow Diagram

```mermaid
flowchart LR
    DRAFT --> SUBMITTED
    SUBMITTED --> PARTIALLY_APPROVED
    PARTIALLY_APPROVED --> APPROVED
    APPROVED --> ASSIGNED
    SUBMITTED --> REJECTED1[REJECTED]
    PARTIALLY_APPROVED --> REJECTED2[REJECTED]
    ASSIGNED --> CANVASSING
    CANVASSING --> FOR_APPROVAL
    FOR_APPROVAL --> FOR_PO_REVIEW
    APPROVED --> FOR_PO_REVIEW
    FOR_PO_REVIEW --> FOR_DELIVERY
    FOR_DELIVERY --> CLOSED
```

## Implementation Notes

Each workflow document includes implementation details and code examples to help developers understand how the workflows are implemented in the system. These examples are meant to be illustrative and may not match the exact implementation in the codebase.

For the actual implementation, refer to the corresponding controller, service, repository, and entity files in the codebase.
