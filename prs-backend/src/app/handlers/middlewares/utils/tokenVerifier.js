/**
 * Token verification utility functions
 * 
 * This module provides reusable functions for token verification
 */
const { ErrorFactory } = require('../../../errors');

/**
 * Default error message for invalid tokens
 */
const DEFAULT_AUTH_ERROR_MSG = 'Invalid authorization token. Please provide a valid token';

/**
 * Token types
 */
const TOKEN_TYPES = {
  ACCESS: 'ACCESS',
  OTP: 'OTP',
  TEMP_PASS: 'TEMP_PASS',
  REFRESH: 'REFRESH',
};

/**
 * Verifies a JWT token and returns the decoded payload
 * 
 * @param {Object} request - Fastify request object
 * @param {Object} options - Verification options
 * @param {string} options.errorMessage - Custom error message
 * @returns {Promise<Object>} - Decoded token payload
 * @throws {Error} - If token verification fails
 */
async function verifyToken(request, options = {}) {
  const { errorMessage = DEFAULT_AUTH_ERROR_MSG } = options;
  
  try {
    return await request.jwtVerify();
  } catch (error) {
    throw ErrorFactory.authentication(errorMessage);
  }
}

/**
 * Validates token payload based on token type
 * 
 * @param {Object} payload - Token payload
 * @param {string} tokenType - Expected token type
 * @param {string} errorMessage - Error message for invalid token
 * @returns {boolean} - True if valid, throws error if invalid
 * @throws {Error} - If token payload is invalid
 */
function validateTokenPayload(payload, tokenType, errorMessage = DEFAULT_AUTH_ERROR_MSG) {
  const { id, isForOTP, isForTempPass, isForRefresh } = payload;
  
  // Check if ID exists
  if (!id) {
    throw ErrorFactory.authentication(errorMessage);
  }
  
  // Validate based on token type
  switch (tokenType) {
    case TOKEN_TYPES.ACCESS:
      if (isForOTP || isForTempPass || isForRefresh) {
        throw ErrorFactory.authentication(errorMessage);
      }
      break;
      
    case TOKEN_TYPES.OTP:
      if (!isForOTP || isForTempPass || isForRefresh) {
        throw ErrorFactory.authentication(errorMessage);
      }
      break;
      
    case TOKEN_TYPES.TEMP_PASS:
      if (isForOTP || !isForTempPass || isForRefresh) {
        throw ErrorFactory.authentication(errorMessage);
      }
      break;
      
    case TOKEN_TYPES.REFRESH:
      if (isForOTP || isForTempPass || !isForRefresh) {
        throw ErrorFactory.authentication(errorMessage);
      }
      break;
      
    default:
      throw ErrorFactory.authentication(errorMessage);
  }
  
  return true;
}

/**
 * Fetches user from database based on token ID
 * 
 * @param {Object} userRepository - User repository
 * @param {string} userId - User ID from token
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - User object
 * @throws {Error} - If user not found
 */
async function fetchUserFromToken(userRepository, userId, options = {}) {
  const user = await userRepository.getById(userId, options);
  
  if (!user) {
    throw ErrorFactory.authentication(DEFAULT_AUTH_ERROR_MSG);
  }
  
  return user;
}

/**
 * Creates a token verification function for a specific token type
 * 
 * @param {string} tokenType - Type of token to verify
 * @param {Object} options - Verification options
 * @param {string} options.errorMessage - Custom error message
 * @param {Function} options.userFetcher - Custom function to fetch user
 * @returns {Function} - Token verification function
 */
function createTokenVerifier(tokenType, options = {}) {
  const { 
    errorMessage = DEFAULT_AUTH_ERROR_MSG,
    userFetcher = fetchUserFromToken,
  } = options;
  
  return async function verifyTokenMiddleware(request, _reply) {
    const userRepository = this.diScope.resolve('userRepository');
    const logger = this.diScope.resolve('logger') || this.log;
    
    try {
      // Verify token
      const payload = await verifyToken(request, { errorMessage });
      
      // Validate payload for token type
      validateTokenPayload(payload, tokenType, errorMessage);
      
      // Fetch user
      const user = await userFetcher(userRepository, payload.id, options);
      
      // Attach user to request
      request.userFromToken = user;
      
    } catch (error) {
      logger.error(`[Token Verification Error]: Token type: ${tokenType}, User ID: ${request.body?.id}`, {
        error: {
          message: error.message,
          stack: error.stack,
        },
      });
      
      throw error;
    }
  };
}

module.exports = {
  TOKEN_TYPES,
  DEFAULT_AUTH_ERROR_MSG,
  verifyToken,
  validateTokenPayload,
  fetchUserFromToken,
  createTokenVerifier,
};
