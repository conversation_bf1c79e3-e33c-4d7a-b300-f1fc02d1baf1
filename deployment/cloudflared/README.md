# Cloudflared Configuration Directory

This directory is used by the Cloudflared service to store its configuration files. It is mounted as a volume in the Docker Compose configuration.

When you run Cloudflared for the first time with a tunnel token, it will automatically create the necessary configuration files in this directory.

Do not delete this directory, even if it's empty, as it's required for the Cloudflared service to function properly.

For more information on how to set up and configure Cloudflared, see the `cloudflared-guide.md` file in the parent directory.
