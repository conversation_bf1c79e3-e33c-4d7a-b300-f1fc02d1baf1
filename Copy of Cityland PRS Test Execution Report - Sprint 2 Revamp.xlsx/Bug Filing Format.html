<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON>ri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON>ri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="559159556C0" style="width:100px;" class="column-headers-background">A</th><th id="559159556C1" style="width:450px;" class="column-headers-background">B</th><th id="559159556C2" style="width:100px;" class="column-headers-background">C</th></tr></thead><tbody><tr style="height: 19px"><th id="559159556R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="559159556R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s0"></td><td class="s0"></td><td class="s0"></td></tr><tr style="height: 19px"><th id="559159556R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s1">Format</td><td class="s2">[BUG] Feature Name-Case Number - Issue Summary</td><td class="s3" rowspan="2">For Bug Summary</td></tr><tr style="height: 19px"><th id="559159556R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s1">Example</td><td class="s2">[BUG] [SW1C-01-01] Voucher Categories Display-002 - Header is missing</td></tr></tbody></table></div>