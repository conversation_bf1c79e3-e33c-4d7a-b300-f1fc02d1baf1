# Error Handling and Validation

## Overview

Error handling and validation are critical aspects of any robust application. This document provides a detailed analysis of how the PRS-Backend handles errors and validates input data.

## Error Handling Architecture

The PRS-Backend implements a centralized error handling system with custom error classes for different types of errors.

### Error Classes

The system defines several error classes:

```javascript
// From src/app/errors/httpError.js
class HttpError {
  constructor({ status, message, description, errorCode }) {
    this.status = status;
    this.message = message;
    this.description = description;
    this.errorCode = errorCode;
    this.timestamp = new Date().toISOString();
  }
}
```

### Error Types

The system defines different types of errors:

```javascript
// From src/app/errors/clientErrors.js
const UNAUTHORIZED = (payload = {}) => {
  const { message = 'Unauthorized', description } = payload;

  return new HttpError({
    message,
    status: 401,
    description,
    errorCode: 'UNAUTHORIZED',
  });
};

const FORBIDDEN = (payload = {}) => {
  const { message = 'Forbidden', description } = payload;

  return new HttpError({
    message,
    status: 403,
    description,
    errorCode: 'FORBIDDEN',
  });
};

// ... other client errors

// From src/app/errors/serverErrors.js
const INTERNAL_SERVER_ERROR = (payload = {}) => {
  const { message = 'Something went wrong', description } = payload;

  return new HttpError({
    status: 500,
    message,
    description,
    errorCode: 'INTERNAL_SERVER_ERROR',
  });
};

// From src/app/errors/infraError.js
const DATABASE_ERROR = (payload = {}) => {
  const {
    description,
    message = 'Something went wrong',
    status = 500,
  } = payload;

  return new HttpError({
    status,
    message,
    description,
    errorCode: 'DATABASE_ERROR',
  });
};
```

### Error Handler

The system implements a global error handler:

```javascript
// From src/app/errors/errorHandler.js
const replyHttpError = (httpError, reply) => {
  const statusCode = httpError.status;
  return reply.status(statusCode).send(httpError);
};

const errorhandler = function (error, _request, reply) {
  const errorType =
    error instanceof Sequelize.Error
      ? 'DATABASE ERROR'
      : error?.errorCode || 'INTERNAL SERVER ERROR';

  this.log.error({
    errorType,
    'x-request-id': _request.id,
    message: error?.message,
    stack: error?.stack,
    timestamp: new Date().toISOString(),
  });

  if (error instanceof HttpError) {
    return replyHttpError(error, reply);
  }

  return replyHttpError(serverErrors.INTERNAL_SERVER_ERROR(), reply);
};
```

## Validation Architecture

The PRS-Backend uses Zod for input validation.

### Validation Schemas

Validation schemas are defined in the domain entities:

```javascript
// From src/domain/entities/requisitionEntity.js
const createRequisitionSchema = z
  .object({
    isDraft: z
      .enum(['true'], {
        required_error: 'Draft status is required',
        invalid_type_error: 'Invalid draft status',
      })
      .transform((value) => value === 'true'),
    type: z.enum(['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], {
      message: REQUIRED_FIELD_ERROR,
    }),
    // ... other fields
  })
  .strict();
```

### Validation Compiler

A validation compiler is used to apply validation schemas to routes:

```javascript
// From src/app/utils/index.js
const validatorCompiler = (schema) => {
  return (data) => {
    const result = schema.safeParse(data);

    if (!result?.success) {
      const errorDescription = result?.error?.issues?.reduce(
        (issues, current) => {
          issues[current.path[0] ?? 'error'] = current.message;
          return issues;
        },
        {},
      );

      throw clientErrors.VALIDATION_ERROR({
        message: result?.error?.issues[0]?.message,
        description: JSON.stringify(errorDescription),
      });
    }

    return data;
  };
};
```

### Validation Middleware

Validation is applied to all routes using a hook:

```javascript
// From src/interfaces/router/index.js
serverContext.addHook('onRoute', (routeOptions) => {
  routeOptions.validatorCompiler = (data) =>
    utils.validatorCompiler(data.schema);
});
```

## Error Handling in Practice

### Controller Error Handling

Controllers typically handle errors by:
1. Catching specific errors
2. Logging errors
3. Throwing appropriate HTTP errors

```javascript
// From src/app/handlers/controllers/requisitionController.js
async createRequisition(request, reply) {
  // ... other code
  
  try {
    // ... operations
  } catch (error) {
    this.fastify.log.info(
      `Requisition create error - ${JSON.stringify(error)}`,
    );

    await transaction.rollback();

    throw this.clientErrors.BAD_REQUEST({
      message: 'Requisition creation failed',
    });
  }
  
  // ... other code
}
```

### Service Error Handling

Services typically handle errors by:
1. Validating input
2. Checking business rules
3. Throwing appropriate domain errors

```javascript
// From src/app/services/canvassService.js
async approveCanvass(payload = {}) {
  const { existingCanvass, approver, transaction, canvassSuppliers = [] } =
    payload;

  const { CANVASS_STATUS, CANVASS_APPROVER_STATUS } = this.constants.canvass;

  const isReadyForApproval =
    existingCanvass.status !== CANVASS_STATUS.FOR_APPROVAL;

  if (isReadyForApproval) {
    throw this.clientErrors.BAD_REQUEST({
      message: `Canvass sheet is not for approval`,
    });
  }

  const approvers = await this.canvassApproverRepository.findAll({
    where: { canvassRequisitionId: existingCanvass.id },
    paginate: false,
    order: [
      ['level', 'ASC'],
      ['isAdhoc', 'ASC'],
    ],
  });

  if (!approvers.total) {
    throw this.clientErrors.BAD_REQUEST({
      message: `No approvers found for this canvass`,
    });
  }
  
  // ... other code
}
```

### Repository Error Handling

Repositories typically handle errors by:
1. Catching database errors
2. Translating them to domain errors
3. Logging detailed error information

```javascript
// Example repository error handling
async findById(id) {
  try {
    const result = await this.model.findByPk(id);
    if (!result) {
      throw this.clientErrors.NOT_FOUND({
        message: `Record not found with ID: ${id}`,
      });
    }
    return result;
  } catch (error) {
    if (error instanceof this.clientErrors.NOT_FOUND) {
      throw error;
    }
    this.logger.error(`Database error: ${error.message}`);
    throw this.infraError.DATABASE_ERROR({
      message: 'Error retrieving record',
      description: error.message,
    });
  }
}
```

## Validation in Practice

### Input Validation

Input validation is applied to request bodies, query parameters, and URL parameters:

```javascript
// From src/interfaces/router/private/requisitionRoute.js
fastify.route({
  method: 'POST',
  url: '/',
  schema: {
    body: entities.requisition.createRequisitionSchema,
  },
  handler: requisitionController.createRequisition.bind(requisitionController),
});
```

### Business Rule Validation

Business rule validation is implemented in services:

```javascript
// From src/app/services/canvassService.js
async canvassCreateValidation(payload = {}) {
  const { canvassId, requisitionId, creatorId, isDraft = true } = payload;
  const { CANVASS_STATUS } = this.constants.canvass;

  const existingRequisition = await this.requisitionRepository.findOne({
    where: { id: requisitionId },
  });

  if (!existingRequisition) {
    throw this.clientErrors.NOT_FOUND({
      message:
        'Requisition not found. Cannot create canvass without a valid requisition',
    });
  }

  if (!existingRequisition.assignedTo) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Cannot create canvass. No assigned purchasing staff.',
    });
  }

  if (existingRequisition.assignedTo !== creatorId) {
    throw this.clientErrors.FORBIDDEN({
      message:
        'Cannot create canvass. Requisition is assigned to another user',
    });
  }
  
  // ... other validation
}
```

### Data Validation

Data validation is implemented in models using Sequelize validators:

```javascript
// From src/infra/database/models/userModel.js
username: {
  type: Sequelize.STRING(255),
  allowNull: false,
  unique: true,
  validate: {
    len: [5, 50],
  },
  indexes: [{ unique: true, fields: ['username'] }],
},
email: {
  type: Sequelize.STRING(255),
  allowNull: true,
  unique: true,
  validate: {
    isEmail: { ignore_max_length: true },
    len: { args: [1, 100], msg: 'Email must be at most 100 characters' },
  },
  indexes: [{ unique: true, fields: ['email'] }],
},
```

## Error Handling and Validation Issues

### 1. Inconsistent Error Handling

Error handling is inconsistent across the codebase:

```javascript
// Example 1: Specific error with message
if (!selectedSuppliers.total) {
  throw this.clientErrors.BAD_REQUEST({
    message: 'No selected suppliers found for this canvass requisition',
  });
}

// Example 2: Generic error with no specific message
try {
  // ... operations
} catch (error) {
  this.fastify.log.error('[ERROR] Creating PO for supplier');
  this.fastify.log.error(error);
  throw error;
}
```

### 2. Missing Error Context

Many errors lack context that would help with debugging:

```javascript
// Missing context in error
throw this.clientErrors.BAD_REQUEST({
  message: 'Requisition creation failed',
});
```

### 3. Incomplete Transaction Rollback

Some error handlers don't properly roll back transactions:

```javascript
// Missing transaction rollback
try {
  // ... operations using transaction
} catch (error) {
  // No transaction rollback
  throw error;
}
```

### 4. Inconsistent Validation Messages

Validation messages are inconsistent across the codebase:

```javascript
// Example 1: Detailed validation message
z.string({
  required_error: 'Username is required',
  invalid_type_error: 'Username must be a string',
})

// Example 2: Generic validation message
z.string().min(1, 'Invalid input')
```

### 5. Missing Business Rule Validation

Some operations lack proper business rule validation:

```javascript
// From src/app/services/requisitionApproverService.js
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  // ... other code
  
  // TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error
  await this.requisitionRepository.update(
    { id: requisitionId },
    { status: 'assigning' },
    { transaction },
  );
  
  // ... other code
}
```

## Recommendations

### 1. Standardize Error Handling

Implement a consistent error handling pattern:

```javascript
// Domain-specific error classes
class DomainError extends Error {
  constructor(message, code = 'DOMAIN_ERROR') {
    super(message);
    this.code = code;
  }
}

class ValidationError extends DomainError {
  constructor(message, validationErrors) {
    super(message, 'VALIDATION_ERROR');
    this.validationErrors = validationErrors;
  }
}

class BusinessRuleError extends DomainError {
  constructor(message) {
    super(message, 'BUSINESS_RULE_ERROR');
  }
}

// Error handler
function handleError(error, logger) {
  logger.error({
    code: error.code || 'UNKNOWN_ERROR',
    message: error.message,
    stack: error.stack,
  });
  
  if (error instanceof ValidationError) {
    return {
      status: 422,
      body: {
        code: error.code,
        message: error.message,
        validationErrors: error.validationErrors,
      },
    };
  }
  
  if (error instanceof BusinessRuleError) {
    return {
      status: 400,
      body: {
        code: error.code,
        message: error.message,
      },
    };
  }
  
  // ... handle other error types
  
  return {
    status: 500,
    body: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
    },
  };
}
```

### 2. Add Error Context

Include context in all errors:

```javascript
// Before
throw this.clientErrors.BAD_REQUEST({
  message: 'Requisition creation failed',
});

// After
throw this.clientErrors.BAD_REQUEST({
  message: 'Requisition creation failed',
  description: `Failed to create requisition with ID: ${requisitionId}. Error: ${error.message}`,
});
```

### 3. Implement Consistent Transaction Management

Ensure all operations that use transactions properly handle rollbacks:

```javascript
// Transaction manager utility
async function withTransaction(db, callback) {
  const transaction = await db.sequelize.transaction();
  try {
    const result = await callback(transaction);
    await transaction.commit();
    return result;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// Usage in service
async createDeliveryReceipt(data, userFromToken) {
  return await withTransaction(this.db, async (transaction) => {
    // ... operations using transaction
    return result;
  });
}
```

### 4. Standardize Validation Messages

Implement consistent validation messages:

```javascript
// Validation message utility
const validationMessages = {
  required: (field) => `${field} is required`,
  string: (field) => `${field} must be a string`,
  number: (field) => `${field} must be a number`,
  min: (field, min) => `${field} must be at least ${min} characters`,
  max: (field, max) => `${field} must be at most ${max} characters`,
  email: (field) => `${field} must be a valid email address`,
  // ... other validation messages
};

// Usage in schema
z.string({
  required_error: validationMessages.required('Username'),
  invalid_type_error: validationMessages.string('Username'),
}).min(5, validationMessages.min('Username', 5))
```

### 5. Implement Comprehensive Business Rule Validation

Add comprehensive business rule validation:

```javascript
// State validation utility
function validateState(entity, expectedStates, errorMessage) {
  if (!expectedStates.includes(entity.status)) {
    throw new BusinessRuleError(
      errorMessage || `Invalid state: ${entity.status}. Expected one of: ${expectedStates.join(', ')}`
    );
  }
}

// Usage in service
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  // ... other code
  
  const requisition = await this.requisitionRepository.getById(requisitionId);
  validateState(requisition, ['submitted'], 'Requisition must be in submitted state to be assigned');
  
  await this.requisitionRepository.update(
    { id: requisitionId },
    { status: 'assigning' },
    { transaction },
  );
  
  // ... other code
}
```

### 6. Implement Validation Middleware

Implement validation middleware for common validation tasks:

```javascript
// Validation middleware
function validateRequest(schema) {
  return (request, reply, next) => {
    try {
      const result = schema.safeParse(request.body);
      if (!result.success) {
        const validationErrors = result.error.issues.reduce(
          (acc, issue) => {
            acc[issue.path.join('.')] = issue.message;
            return acc;
          },
          {}
        );
        throw new ValidationError('Validation failed', validationErrors);
      }
      request.validatedBody = result.data;
      next();
    } catch (error) {
      const { status, body } = handleError(error, request.log);
      reply.status(status).send(body);
    }
  };
}

// Usage in route
app.post('/requisitions', validateRequest(createRequisitionSchema), (request, reply) => {
  // Request body is already validated
  const { validatedBody } = request;
  // ... handle request
});
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
