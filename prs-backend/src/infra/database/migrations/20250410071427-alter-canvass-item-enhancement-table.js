'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('canvass_items', 'requisition_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'requisitions',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addIndex('canvass_items', ['requisition_id'], {
      name: 'idx_canvass_items_requisition_id',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(
      'canvass_items',
      'idx_canvass_items_requisition_id',
    );

    await queryInterface.removeColumn('canvass_items', 'requisition_id');
  },
};
