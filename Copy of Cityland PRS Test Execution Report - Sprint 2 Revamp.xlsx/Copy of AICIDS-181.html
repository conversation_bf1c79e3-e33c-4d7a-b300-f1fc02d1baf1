<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1652242266C0" style="width:170px;" class="column-headers-background">A</th><th id="1652242266C1" style="width:208px;" class="column-headers-background">B</th><th id="1652242266C2" style="width:253px;" class="column-headers-background">C</th><th id="1652242266C3" style="width:222px;" class="column-headers-background">D</th><th id="1652242266C4" style="width:342px;" class="column-headers-background">E</th><th id="1652242266C5" style="width:331px;" class="column-headers-background">F</th><th id="1652242266C6" style="width:197px;" class="column-headers-background">G</th><th id="1652242266C7" style="width:154px;" class="column-headers-background">H</th><th id="1652242266C8" style="width:154px;" class="column-headers-background">I</th><th id="1652242266C11" style="width:310px;" class="column-headers-background">L</th><th id="1652242266C12" style="width:196px;" class="column-headers-background">M</th><th id="1652242266C13" style="width:196px;" class="column-headers-background">N</th></tr></thead><tbody><tr style="height: 20px"><th id="1652242266R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Type of Check</td><td class="s1">Decision Type</td><td class="s1">Label</td><td class="s1">Description</td><td class="s1">Allow Retry?</td><td class="s1">Add to Holding List?</td><td class="s1">Postman</td><td class="s1"></td><td class="s1">Remarks</td><td class="s1">Defects</td><td class="s2"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 20px"><th id="1652242266R1" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">2</div></th><td class="s3" colspan="11">[Lot C - IDS Holding List] Retry limits (Development)</td><td class="s4"></td></tr><tr style="height: 20px"><th id="1652242266R2" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">3</div></th><td class="s5">TC-207-02</td><td class="s5">Usability</td><td class="s5">NOT_EXECUTED</td><td class="s5">TECHNICAL_ERROR</td><td class="s5">An error prevented execution.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R3" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">4</div></th><td class="s5">TC-207-03</td><td class="s5">Usability</td><td class="s5">NOT_EXECUTED</td><td class="s5">NOT_UPLOADED</td><td class="s5">The image was not uploaded.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R4" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">5</div></th><td class="s5">TC-207-04</td><td class="s5">Extraction</td><td class="s5">NOT_EXECUTED</td><td class="s5">PRECONDITION_NOT_FULFILLED</td><td class="s5">Required data from another capability is not available.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R5" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">6</div></th><td class="s5">TC-207-05</td><td class="s5">Extraction</td><td class="s5">NOT_EXECUTED</td><td class="s5">TECHNICAL_ERROR</td><td class="s5">An error prevented execution.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R6" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">7</div></th><td class="s5">TC-207-06</td><td class="s5">Image Check</td><td class="s5">NOT_EXECUTED</td><td class="s5">PRECONDITION_NOT_FULFILLED</td><td class="s5">Required data from another capability is not available.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R7" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">8</div></th><td class="s5">TC-207-07</td><td class="s5">Image Check</td><td class="s5">NOT_EXECUTED</td><td class="s5">TECHNICAL_ERROR</td><td class="s5">An error prevented execution.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R8" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">9</div></th><td class="s5">TC-207-08</td><td class="s5">Data Check</td><td class="s5">NOT_EXECUTED</td><td class="s5">PRECONDITION_NOT_FULFILLED</td><td class="s5">Required data from another capability is not available.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R9" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">10</div></th><td class="s5">TC-207-09</td><td class="s5">Data Check</td><td class="s5">NOT_EXECUTED</td><td class="s5">TECHNICAL_ERROR</td><td class="s5">An error prevented execution.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R10" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">11</div></th><td class="s5">TC-207-10</td><td class="s5">Liveness</td><td class="s5">NOT_EXECUTED</td><td class="s5">PRECONDITION_NOT_FULFILLED</td><td class="s5">Required data from another capability is not available.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R11" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">12</div></th><td class="s5">TC-207-11</td><td class="s5">Liveness</td><td class="s5">NOT_EXECUTED</td><td class="s5">TECHNICAL_ERROR</td><td class="s5">An error prevented execution.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R12" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">13</div></th><td class="s5">TC-207-12</td><td class="s5">Similarity</td><td class="s5">NOT_EXECUTED</td><td class="s5">PRECONDITION_NOT_FULFILLED</td><td class="s5">Required data from another capability is not available.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R13" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">14</div></th><td class="s5">TC-207-13</td><td class="s5">Similarity</td><td class="s5">NOT_EXECUTED</td><td class="s5">TECHNICAL_ERROR</td><td class="s5">An error prevented execution.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R14" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">15</div></th><td class="s5">TC-207-14</td><td class="s5">Usability</td><td class="s5">PASSED</td><td class="s5">OK</td><td class="s5">The images of the ID document are of sufficient quality to complete the transaction.</td><td class="s5">n/a</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R15" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">16</div></th><td class="s5">TC-207-15</td><td class="s5">Extraction</td><td class="s5">PASSED</td><td class="s5">OK</td><td class="s5">All required data values were successfully extracted from the image of the ID.</td><td class="s5">n/a</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R16" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">17</div></th><td class="s5">TC-207-16</td><td class="s5">Image Check</td><td class="s5">PASSED</td><td class="s5">OK</td><td class="s5">The images of the ID document passed all image checks.</td><td class="s5">n/a</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R17" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">18</div></th><td class="s5">TC-207-17</td><td class="s5">Data Check</td><td class="s5">PASSED</td><td class="s5">OK</td><td class="s5">Fraud was not detected during the analysis of the data extracted from the ID image.</td><td class="s5">n/a</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R18" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">19</div></th><td class="s5">TC-207-18</td><td class="s5">Liveness</td><td class="s5">PASSED</td><td class="s5">OK</td><td class="s5">Analysis determined that the end user was physically present during the verification process.</td><td class="s5">n/a</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R19" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">20</div></th><td class="s5">TC-207-19</td><td class="s5">Similarity</td><td class="s5">PASSED</td><td class="s5">MATCH</td><td class="s5">The faces in the images being compared belong to the same person.</td><td class="s5">n/a</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R20" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">21</div></th><td class="s5">TC-207-20</td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">BLACK_WHITE</td><td class="s5">Black and white images are not supported.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R21" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">22</div></th><td class="s5">TC-207-21</td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">MISSING_SIGNATURE</td><td class="s5">No signature detected.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R22" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">23</div></th><td class="s5">TC-207-22</td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">MISSING_PAGE</td><td class="s5">The front and back pages of the document were required, but the user uploaded only one of them.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R23" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">24</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">NOT_A_DOCUMENT</td><td class="s5">The image is not a recognized document.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R24" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">25</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">PHOTOCOPY</td><td class="s5">The document in the image is a photocopy and not the original document.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R25" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">26</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">BAD_QUALITY_IMAGE</td><td class="s5">The image is of insufficient quality for processing.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R26" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">27</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">BLURRED</td><td class="s5">Blurry image.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R27" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">28</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">GLARE</td><td class="s5">The ID is obscured by glare.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R28" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">29</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">PART_OF_DOCUMENT_MISSING</td><td class="s5">Part of the document was excluded from the image.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R29" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">30</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">PART_OF_DOCUMENT_HIDDEN</td><td class="s5">Part of the ID is hidden from view in the uploaded image.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R30" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">31</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">DAMAGED_DOCUMENT</td><td class="s5">The document is damaged to the extent that the data is difficult to extract or the security features are difficult to check.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R31" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">32</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">MISSING_MANDATORY_DATAPOINTS</td><td class="s5">One or more of the key data fields, such as First Name, Last Name or DOB, cannot be extracted. This rejection reason will only happen for Jumio Go transactions.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R32" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">33</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">DOCUMENT_CONTENT_NOT_SUPPORTED</td><td class="s5">Only used for Document credentials, when the document content does not conform to the type specified in the account request.</td><td class="s5">N/A</td><td class="s5">N/A</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R33" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">34</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">REJECTED</td><td class="s5">DIGITAL_COPY</td><td class="s5">ID image is of a screen.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R34" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">35</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">FAILED</td><td class="s5">The images of the ID document did not pass all image checks.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R35" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">36</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">WATERMARK</td><td class="s5">The image of the ID contains a watermark (for example, &quot;Sample&quot;).</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R36" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">37</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">OTHER_REJECTION</td><td class="s5">The image of the ID was rejected for a reason that does not fall under the other decision labels.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R37" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">38</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">GHOST_IMAGE_DIFFERENT</td><td class="s5">There is a mismatch between the ID&#39;s main image and ghost image</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R38" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">39</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">PUNCHED</td><td class="s5">The ID has been hole-punched.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R39" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">40</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">SAMPLE</td><td class="s5">The ID is a known sample document.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R40" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">41</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">FAKE</td><td class="s5">The ID is fake. Possible reasons include:<br> <br><br> The ID is a well known fake.<br> <br><br> The image is available on the public internet.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R41" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">42</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">CHIP_MISSING</td><td class="s5">The ID has a missing chip.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R42" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">43</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">DIGITAL_MANIPULATION</td><td class="s5">The image of the ID was manipulated before being uploaded.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R43" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">44</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MISMATCH_FRONT_BACK</td><td class="s5">The country shown on the front of the ID does not match the one shown on the back.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R44" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">45</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT</td><td class="s5">The ID shows a physically superimposed photo or text.<br> <br><br> Note: In most cases one of the more detailed labels shown below will be returned.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R45" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">46</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_PHOTO</td><td class="s5">The ID shows a physically superimposed photo or text.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R46" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">47</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_DOCUMENT_NUMBER</td><td class="s5">The document number on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R47" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">48</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_EXPIRY</td><td class="s5">The expiration date on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R48" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">49</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_DOB</td><td class="s5">The date of birth on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R49" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">50</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_NAME</td><td class="s5">The name on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R50" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">51</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_ADDRESS</td><td class="s5">The address on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R51" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">52</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_SECURITY_CHECKS</td><td class="s5">A back-office agent reviewed the scan and rejected it based on an issue with the security features. For example, the background microprint or pattern does not match the sample image, or a logo or image is missing or not inline with the sample.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R52" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">53</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_SIGNATURE</td><td class="s5">The signature on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R53" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">54</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_PERSONAL_NUMBER</td><td class="s5">The personal number on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R54" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">55</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_PLACE_OF_BIRTH</td><td class="s5">The place of birth on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R55" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">56</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_GENDER</td><td class="s5">The gender on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R56" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">57</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED_DOCUMENT_ISSUING_DATE</td><td class="s5">The issue date on the ID shows visible physical or digital manipulations.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R57" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">58</div></th><td class="s5"></td><td class="s5">Data Check</td><td class="s5">REJECTED</td><td class="s5">NFC_CERTIFICATE</td><td class="s5">There is a mismatch between the data extracted from the microchip on the passport and the data extracted from the document using OCR. This rejection reason only applies to transactions using NFC on a mobile device.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R58" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">59</div></th><td class="s5"></td><td class="s5">Data Check</td><td class="s5">REJECTED</td><td class="s5">MISMATCHING_DATAPOINTS</td><td class="s5">There is a mismatch between different occurrences of a repeating datapoint on a document. For example, the data extracted from the barcode does not match the printed data extracted from the ID.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R59" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">60</div></th><td class="s5"></td><td class="s5">Data Check</td><td class="s5">REJECTED</td><td class="s5">MRZ_CHECKSUM</td><td class="s5">The machine-readable zone (MRZ) checksum has an unexpected value.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R60" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">61</div></th><td class="s5"></td><td class="s5">Data Check</td><td class="s5">REJECTED</td><td class="s5">MISMATCHING_DATA_REPEATED_FACE</td><td class="s5">The ID or selfie matches a previously uploaded selfie or ID with mismatching data.</td><td class="s5">No</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R61" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">62</div></th><td class="s5"></td><td class="s5">Data Check</td><td class="s5">REJECTED</td><td class="s5">MISMATCH_HRZ_MRZ_DATA</td><td class="s5">The data in the human-readable zone (HRZ) and machine-readable zone (MRZ) of the ID do not match.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R62" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">63</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">LIVENESS_UNDETERMINED</td><td class="s5">Liveness cannot be determined.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R63" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">64</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">ID_USED_AS_SELFIE</td><td class="s5">An ID photo was used for the selfie.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R64" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">65</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">MULTIPLE_PEOPLE</td><td class="s5">More than one person appears in the selfie.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R65" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">66</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">DIGITAL_COPY</td><td class="s5">The selfie was captured from a screen.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R66" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">67</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">PHOTOCOPY</td><td class="s5">The selfie was captured from a paper printout.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R67" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">68</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">MANIPULATED</td><td class="s5">The selfie was manipulated prior to uploading (for example, a background swap).</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R68" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">69</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">NO_FACE_PRESENT</td><td class="s5">A face does not appear in the selfie.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R69" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">70</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">FACE_NOT_FULLY_VISIBLE</td><td class="s5">The face is only partially visible in the selfie.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R70" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">71</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">REJECTED</td><td class="s5">BLACK_WHITE</td><td class="s5">The selfie image is black and white.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R71" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">72</div></th><td class="s5"></td><td class="s5">Similarity</td><td class="s5">REJECTED</td><td class="s5">NO_MATCH</td><td class="s5">The faces in the selfie and ID do not match.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R72" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">73</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">WARNING</td><td class="s5">UNSUPPORTED_COUNTRY</td><td class="s5">The document was issued by a country that is not configured for your organization.</td><td class="s5">N/A</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R73" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">74</div></th><td class="s5"></td><td class="s5">Usability</td><td class="s5">WARNING</td><td class="s5">UNSUPPORTED_DOCUMENT_TYPE</td><td class="s5">The document type is not supported.</td><td class="s5">N/A</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R74" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">75</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">WARNING</td><td class="s5">DIFFERENT_PERSON</td><td class="s5">When the user submits an image that includes both a face and an ID, the face does not match the person on the ID.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R75" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">76</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">WARNING</td><td class="s5">REPEATED_FACE</td><td class="s5">The same face and data occur multiple times, pointing to the potential opening of multiple accounts.</td><td class="s5">N/A</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R76" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">77</div></th><td class="s5"></td><td class="s5">Image Check</td><td class="s5">WARNING</td><td class="s5">GHOST_IMAGE_QUALITY_INSUFFICIENT</td><td class="s5">The ghost image is too faded for our models to confidently determine if it is available.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R77" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">78</div></th><td class="s5"></td><td class="s5">Data Check</td><td class="s5">WARNING</td><td class="s5">DOCUMENT_EXPIRY_WITHIN_CONFIGURED_LIMIT</td><td class="s5">The document is nearing its expiration date.</td><td class="s5">N/A</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R78" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">79</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">WARNING</td><td class="s5">AGE_DIFFERENCE</td><td class="s5">There is a large difference between the estimated selfie age and the date of birth on the ID.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R79" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">80</div></th><td class="s5"></td><td class="s5">Liveness</td><td class="s5">WARNING</td><td class="s5">BAD_QUALITY</td><td class="s5">The selfie is of bad quality.</td><td class="s5">Yes</td><td class="s5">No</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 20px"><th id="1652242266R80" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">81</div></th><td class="s5"></td><td class="s5">Similarity</td><td class="s5">WARNING</td><td class="s5">NOT_POSSIBLE</td><td class="s5">Similarity cannot be determined.</td><td class="s5">Yes (Max of 3 only)</td><td class="s5">Yes</td><td class="s6">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s5"></td><td class="s5"></td></tr></tbody></table></div>