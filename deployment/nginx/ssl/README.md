# SSL Certificates Directory

This directory is used to store SSL certificates for HTTPS connections. It is mounted as a volume in the Docker Compose configuration.

For production deployments, you should place your SSL certificate files in this directory:
- `cert.pem`: The SSL certificate file
- `key.pem`: The SSL private key file

If you are using Cloudflared for all external access, you may not need to configure SSL certificates directly in Nginx, as Cloudflare provides SSL termination.

Do not delete this directory, even if it is empty, as it is referenced in the Nginx configuration.
