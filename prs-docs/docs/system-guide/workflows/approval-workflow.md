# Approval Workflow

This document outlines the general approval workflow that applies to multiple entities in the PRS system, including requisitions, canvasses, purchase orders, and payment requests.

## General Approval Flow

```mermaid
flowchart TD
  Start([Submit Document]) -->S2[Pending]
  S2 -->|Approve| S3[Approved]
  S2 -->|Reject| S4[Rejected]
```

## Approval Concepts

### 1. Approver Levels

Approvers are assigned levels that determine the order in which they must approve:

- **Level 1**: First level approvers (e.g., Supervisor)
- **Level 2**: Second level approvers (e.g., Project In Charge)
- **Level 3**: Third level approvers (e.g., Department Head)
- **Level 4**: Fourth level approvers (e.g., Management)

Approval proceeds in order of level, from lowest to highest.

### 2. Approver Types

There are three types of approvers:

- **Regular Approvers**: Assigned automatically based on document type, category, department, project, role, or quantities (GFQ)
- **Adhoc Approvers**: Added manually during the approval process
- **Alt Approvers**: Added manually during leave setup
- ALL USER TYPES EXCEPT Root User are allowed to be assigned as approver 

### 3. Approval Status

Each approver has an approval status:

- **PENDING**: Approval is pending
- **APPROVED**: Approver has approved
- **REJECTED**: Approver has rejected

### 4. Entity Status

The status of the entity being approved changes based on approver actions:

- **FOR APPROVAL**: Entity has been submitted for approval, will retain as long as there is pending approval
- **APPROVED**: All approvers have approved - will vary per document also
- **REJECTED**: At least one approver has rejected

### 5. Approver Leave Set Up

An approver can set up leaves and assign alternate approvers during the leave

**Actor**: Approvers

**Actions**:

- Set up leave date
- View affected approval workflows and assign alternate approver/s for all workflow
or
- Edit leave date or alt approver
or
- Cancel leave

**Business Rules**:

- Approver is required to assign alt approvers for all affected workflows
- All users are eligible as alt approver except root user
- Alt approvers will be also visible in approver setup pages
- Documents that were not approved before the leave date will be approved by the Alt Approver
- Documents submitted during the leave date will be Approved by the Alt Approver
- Updates and notifications during the leave date will be received by the Alt Approver
- Implementation of Alt Approver starts at 12AM of the start date until 11:59PM of the end date
- After end date, Alt Approver access and setup will be cleared; approvals will be to the Approver again

## Detailed Approval Steps

### 1. Approver Assignment

**When**: When an entity is submitted for approval

**Actions**:

- System assigns regular approvers based on rules
- Adhoc approvers can be added manually

**Business Rules**:

- Approvers are assigned based on document type, category, department, project, role, or quantities (GFQ)
- Approvers are assigned levels that determine approval order

### 2. Approval Process

**When**: After approvers are assigned

**Actions**:

- Approvers review the entity
- Approvers approve or reject
- Approvers can add comments - required for rejection

**Business Rules**:

- Approvers must approve in order of level
- Higher level approvers cannot approve until all lower level approvers have approved
- If any approver rejects, the entity is rejected
- Adhoc approvers can be added during the approval process

### 3. Approval Completion

**When**: After all approvers have acted

**Actions**:

- System updates entity status
- System triggers next steps in workflow

**Business Rules**:

- If all approvers approve, entity status changes to APPROVED (depends on document)
- If any approver rejects, entity status changes to REJECTED
- When entity is approved, next steps in workflow are triggered

## Entity-Specific Approval Rules

### 1. Requisition Approval

- **Approvers and Levels**
    - If category = company
        - L1 requestor's supervisor
        - L2 requestor's dept approvers"	
    - If category = project
        - L1 requestor's supervisor
        - L2 requestor's project approvers
        - L3 requestor's dept approvers"	
    - If category = association
        - L1 requestor's supervisor
        - L2 association dept approvers"	
- **Thresholds**: Additional approval level based on quantity - GFQ

    - if requested quantity exceeds 80% of GFQ - an approver is added

- **Next Steps**: After approval, requisition is assigned to Purchasing Staff

### 2. Canvass Approval

- **Approvers and Levels**:
    - L1 assigned purchasing staff's supervisor
    - L2 purchasing head
    - L3 management 1
    - L4 management 2
	
- **Next Steps**: After approval, Purchase Orders are generated

### 3. Purchase Order Approval

- **Approvers and Levels**:
    - L1 assigned purchasing staff's supervisor
    - L2 purchasing head"	
	
- **Next Steps**: After approval, PO status changes to FOR_SENDING

### 4. Payment Request Approval

- **Approvers if OFM Type**: 
    - L1 assigned purchasing staff's supervisor
    - L2 requestor's supervisor
    - L3 purchasing head

- **Approvers if Non-OFM Type**: 
    - L1 assigned purchasing staff's supervisor
    - L2 purchasing head"

- **Next Steps**: After approval, payment request is submitted to Cityland Legacy System

### 5. Non-RS Payment Request Approval

- **Approvers**:
    - L1 requestor's supervisor
    - L2 requestor's dept approvers"

- **Next Steps**: After approval, status will be closed

## Implementation Details

### Approver Assignment

```javascript
// Example approver assignment logic for requisitions
async function assignRequisitionApprovers(requisitionId, transaction) {
  const requisition = await requisitionRepository.findOne({
    where: { id: requisitionId },
    include: [
      { association: 'department' },
      { association: 'company' },
    ],
    transaction,
  });
  
  // Get department head
  const departmentHead = await userRepository.findOne({
    where: {
      departmentId: requisition.departmentId,
      roleId: roles.DEPARTMENT_HEAD,
    },
    transaction,
  });
  
  if (departmentHead) {
    await requisitionApproverRepository.create({
      requisitionId,
      userId: departmentHead.id,
      level: 1,
      status: 'PENDING',
      isAdhoc: false,
    }, { transaction });
  }
  
  // Get management approvers based on amount
  const amount = await calculateRequisitionAmount(requisitionId, transaction);
  
  if (amount > 10000) {
    // Get management approvers
    const managementApprovers = await userRepository.findAll({
      where: {
        roleId: roles.MANAGEMENT,
      },
      transaction,
    });
    
    for (const approver of managementApprovers) {
      await requisitionApproverRepository.create({
        requisitionId,
        userId: approver.id,
        level: 2,
        status: 'PENDING',
        isAdhoc: false,
      }, { transaction });
    }
  }
  
  if (amount > 50000) {
    // Get senior management approvers
    const seniorManagementApprovers = await userRepository.findAll({
      where: {
        roleId: roles.SENIOR_MANAGEMENT,
      },
      transaction,
    });
    
    for (const approver of seniorManagementApprovers) {
      await requisitionApproverRepository.create({
        requisitionId,
        userId: approver.id,
        level: 3,
        status: 'PENDING',
        isAdhoc: false,
      }, { transaction });
    }
  }
}
```

### Approval Process

```javascript
// Example approval logic
async function approveEntity({
  entityId,
  approverId,
  entityType,
  transaction,
}) {
  // Get entity and approvers
  const { entity, approvers } = await getEntityWithApprovers(entityId, entityType, transaction);
  
  // Check if entity can be approved
  if (!canEntityBeApproved(entity)) {
    throw new Error(`${entityType} cannot be approved`);
  }
  
  // Find the approver
  const approver = approvers.find(a => a.userId === approverId);
  
  if (!approver) {
    throw new Error(`User is not an approver for this ${entityType}`);
  }
  
  if (approver.status === 'APPROVED') {
    throw new Error(`User has already approved this ${entityType}`);
  }
  
  // Check if all lower level approvers have approved
  const lowerLevelApprovers = approvers.filter(a => a.level < approver.level);
  const allLowerLevelApproved = lowerLevelApprovers.every(a => a.status === 'APPROVED');
  
  if (!allLowerLevelApproved) {
    throw new Error(`Cannot approve until all lower level approvers have approved`);
  }
  
  // Update approver status
  await updateApproverStatus(approver.id, 'APPROVED', entityType, transaction);
  
  // Check if all approvers have approved
  const allApprovers = approvers;
  const pendingApprovers = allApprovers.filter(a => a.status !== 'APPROVED');
  
  if (pendingApprovers.length === 0) {
    // All approved, update entity status
    await updateEntityStatus(entityId, 'APPROVED', entityType, transaction);
    
    // Trigger next steps
    await triggerNextSteps(entityId, entityType, transaction);
    
    return true; // All approved
  } else {
    // Not all approved, update entity status to PARTIALLY_APPROVED
    await updateEntityStatus(entityId, 'PARTIALLY_APPROVED', entityType, transaction);
    
    return false; // Not all approved yet
  }
}
```

### Rejection Process

```javascript
// Example rejection logic
async function rejectEntity({
  entityId,
  approverId,
  entityType,
  reason,
  transaction,
}) {
  // Get entity and approvers
  const { entity, approvers } = await getEntityWithApprovers(entityId, entityType, transaction);
  
  // Check if entity can be rejected
  if (!canEntityBeRejected(entity)) {
    throw new Error(`${entityType} cannot be rejected`);
  }
  
  // Find the approver
  const approver = approvers.find(a => a.userId === approverId);
  
  if (!approver) {
    throw new Error(`User is not an approver for this ${entityType}`);
  }
  
  if (approver.status === 'REJECTED') {
    throw new Error(`User has already rejected this ${entityType}`);
  }
  
  // Check if all lower level approvers have approved
  const lowerLevelApprovers = approvers.filter(a => a.level < approver.level);
  const allLowerLevelApproved = lowerLevelApprovers.every(a => a.status === 'APPROVED');
  
  if (!allLowerLevelApproved) {
    throw new Error(`Cannot reject until all lower level approvers have approved`);
  }
  
  // Update approver status
  await updateApproverStatus(approver.id, 'REJECTED', entityType, transaction);
  
  // Update entity status
  await updateEntityStatus(entityId, 'REJECTED', entityType, transaction);
  
  // Add rejection reason
  await addRejectionReason(entityId, approverId, reason, entityType, transaction);
  
  return true; // Rejected
}
```

## Common Issues and Solutions

### Issue 1: Approver Not Available

**Cause**: Assigned approver is on leave or unavailable.

**Solution**:
- Use alternate approver functionality
- Assign adhoc approver
- Reassign to another approver with same role

### Issue 2: Approval Order Issues

**Cause**: Higher level approvers trying to approve before lower level approvers.

**Solution**:
- Ensure all lower level approvers have approved
- Send reminders to pending lower level approvers
- Consider adding adhoc approvers at lower levels if needed

### Issue 3: Rejection Handling

**Cause**: Entity rejected but needs to be resubmitted.

**Solution**:
- Address rejection reason
- Update entity with necessary changes
- Resubmit for approval (status changes back to SUBMITTED)
- Approvers will need to approve again
