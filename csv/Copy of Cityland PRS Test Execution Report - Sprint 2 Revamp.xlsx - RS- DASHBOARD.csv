Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,Status,Remarks,Defects
Tab Updates,,,,,,,,,,,
1312-001,"RS Dashboard Updates
1. Tab Updates",Minor,Verify All Tab are displayed in IT admin and Purchasing admin,1. User is Logged in as IT admin or Purchasing admin,1. Verify all tab are displayed,,"1. Users of IT admin and Purchasing admin are expected to see in the Nav Bar: Tabs
a. Dashboard
b. Non-RS
c. Request Flow
d. Items
e. Manage
f. Admin
g. Audit Log

H. My Requests Tab
I. For My Approval/Assigned Tab
 (Should be displayed and highlighted upon viewing of Dashboard Page)
J. All Tab",,Not Started,,
1312-002,"RS Dashboard Updates
1. Tab Updates",Minor,Verify If all User Types are not an approver,"1. User has successfully logged in to their Account
2. All Users except Root User, IT admin and Purchasing admin",1. Verify if User type cannot approve,,1. All User Types are expected to cannot approve,,Not Started,,
1312-003,"RS Dashboard Updates
1. Tab Updates",Minor,Verify my Request Tabs in User Account,,1. Verify if My Request tab is Visible,,1. My Request Tab is expected to be visible,,Not Started,,
1312-004,"RS Dashboard Updates
1. Tab Updates",Minor,Verify displayed and highlighted upon viewing of Request Tabs and For my Approval/Assigned,,1. Verify if Request Tab is highlighted upon viewing,,1. Request Tab is expected tod be highlighted upon viewing the Request tabs and For my Approval/Assigned,,Not Started,,
1312-005,"RS Dashboard Updates
1. Tab Updates",High,Verify all User Types as an approver,,1. Verify if Users type can approve ,,1. All User Types are able to approve,,Not Started,,
Update Create New Button,,,,,,,,,,,
1312-006,"RS Dashboard Updates
1. Update Create New Button",Critical,Verify if User has successfully logged in to their Account,1. All User Types except Root User,1. Login User account,,1. Verify if User is succesfully logged in,,Not Started,,
1312-007,"RS Dashboard Updates
1. Update Create New Button",High,Verify if Dashboard tabs are displayed,"1. User has successfully logged in to their Account
2. All User Types except Root User","1. Login User account
2. Verify if Dashboard tab is visible",,2. Dashboard Tab is expected to be visible,,Not Started,,
1312-008,"RS Dashboard Updates
1. Update Create New Button",High,Verify if updated Dashboard tab is visible,,"1. Login User account
2. Verify Dashboard tab contents",,"2. Dashboard tab is expected to display the updated Columns of the Dashboard Page
    a. Ref. Number
    b. Document Type
    c. Requester
    d. Company
    e. Proj/Department
    f. Last Updated
    g. Status",,Not Started,,
1312-009,"RS Dashboard Updates
1. Update Create New Button",Minor,Verify if the following contents for the updated Columns of the Dashboard Page is displayed,"1. User has successfully logged in to their Account
2. All User Types except Root User","1. Login User account
2. Verify Dashboard tab contents",,"2.  Should have the following contents for the updated Columns of the Dashboard Page
    a. Ref. Number
        i. Reference Number of all Documents
    b. Document Type
        i. RS
        ii. Canvassing
        iii. Order
        iv. Delivery
        v. Invoice
        vi. Voucher
        vii. Non-RS
    c. Requester
        i. Requester's Name that created the Document
           i) Can be the Requester of Requisition Slip
               a) For Requisition Slip, Delivery Receipt, and Non-RS
           ii) Can be the Assigned Purchasing Staff's Name
               a) For Canvass Sheet, Purchase Order, Invoice, and Payment Request
    d. Company
        i. Company indicated in the Requisition Slip
    e. Proj/Department
        i. Project/Department indicated in the Requisition Slip
    f. Last Updated
        i. Date that the Document was last updated
    g. Status
        i. Status of the Document",,Not Started,,
Dashboard Table Contents,,,,,,,,,,,
1312-010,"RS Dashboard Updates
1. Dashboard Table Contents",Critical,Verify if User has successfully logged in to their Account,1. All User Types except Root User,1. Login User account,,1. Verify if User is succesfully logged in,,Not Started,,
1312-011,,High,Verify if Dashboard tabs are displayed,"1. User has successfully logged in to their Account
2. Requisition Slip or any of the Documents has been created
3. All Users except Root User","1. Login User account
2. Verify if Dashboard tab is visible",,2. Dashboard Tab is expected to be visible,,Not Started,,
1312-012,,High,Verify if updated Dashboard tab is visible,,"1. Login User account
2. Verify Dashboard tab contents",,"2. Dashboard tab is expected to display the updated Columns of the Dashboard Page
    a. Ref. Number
    b. Document Type
    c. Requester
    d. Company
    e. Proj/Department
    f. Last Updated
    g. Status",,Not Started,,
1312-013,,Minor,Verify if the following contents for the updated Columns of the Dashboard Page is displayed,"1. User has successfully logged in to their Account
2. All User Types except Root User","1. Login User account
2. Verify Dashboard tab contents",,"2.  Should have the following contents for the updated Columns of the Dashboard Page
    a. Ref. Number
        i. Reference Number of all Documents
    b. Document Type
        i. RS
        ii. Canvassing
        iii. Order
        iv. Delivery
        v. Invoice
        vi. Voucher
        vii. Non-RS
    c. Requester
        i. Requester's Name that created the Document
           i) Can be the Requester of Requisition Slip
               a) For Requisition Slip, Delivery Receipt, and Non-RS
           ii) Can be the Assigned Purchasing Staff's Name
               a) For Canvass Sheet, Purchase Order, Invoice, and Payment Request
    d. Company
        i. Company indicated in the Requisition Slip
    e. Proj/Department
        i. Project/Department indicated in the Requisition Slip
    f. Last Updated
        i. Date that the Document was last updated
    g. Status
        i. Status of the Document",,Not Started,,
RS Dashboard Updates - Sorting of Closed RS and related Documents,,,,,,,,,,,
,RS Dashboard Updates - Sorting of Closed RS and related Documents,High,Verify dashboard page with Tabs displaying Requisition Slip data,"1. Logged in as a User (except Root User)
2. At least one of the Requisition Slip has been Closed",1. Navigate to the Dashboard page,,2. Dashboard page loads successfully with Tabs displaying Requisition Slip data,,Not Started,,
,RS Dashboard Updates - Sorting of Closed RS and related Documents,High,Verify that all Tabs and their data are sorted by the Latest Updated Date descending (newest first),"1. Logged in as a User (except Root User)
2. At least one of the Requisition Slip has been Closed",1. Observe the order of the tabs/documents,,2. Tabs should be listed in order of the most recently updated Requisition Slip or its related document,,Not Started,,
,RS Dashboard Updates - Sorting of Closed RS and related Documents,High,Verify that Active or Not Yet Closed Requisition Slips are listed first ,"1. Logged in as a User (except Root User)
2. At least one of the Requisition Slip has been Closed",1. Observe the first section of the table displaying Requisition Slips,,2. All Requisition Slips and their Related Documents that are Active or Not Yet Closed are listed first,,Not Started,,
,RS Dashboard Updates - Sorting of Closed RS and related Documents,High,Verify that Closed Requisition Slips and their related documents appear after all active ones,"1. Logged in as a User (except Root User)
2. At least one of the Requisition Slip has been Closed",1. Scroll to the bottom of the Requisition Slip list,,"2. Closed Requisition Slips appear at the bottom portion of the list, even if their updated date is more recent than an Active one",,Not Started,,
,RS Dashboard Updates - Sorting of Closed RS and related Documents,Critical ,Validate that the sorting remains consistent after a page refresh,"1. Logged in as a User (except Root User)
2. At least one of the Requisition Slip has been Closed","1. Click Refresh button 
2. Observe Requisition Slip list",,2. Order of documents/tabs remains sorted by Latest Updated Date and categorized by status (Active/Closed) after page reload,,Not Started,,