const BaseService = require('./baseService');
const { LOG_CATEGORIES } = require('../../infra/logs/loggerService');

/**
 * Example service demonstrating the new logging system
 */
class ExampleLoggingService extends BaseService {
  /**
   * Creates a new ExampleLoggingService
   * 
   * @param {Object} container - Dependency injection container
   */
  constructor(container) {
    super(container);
    
    const { exampleRepository, logger } = container;
    
    this.exampleRepository = exampleRepository;
    this.logger = logger;
  }

  /**
   * Creates an example with logging
   * 
   * @param {Object} data - Example data
   * @returns {Promise<Object>} - Created example
   */
  async createExample(data) {
    this.logger.info('Creating new example', {
      category: LOG_CATEGORIES.BUSINESS,
      data: {
        name: data.name,
        type: data.type,
      },
    });
    
    try {
      // Validate required fields
      if (!data.name) {
        this.logger.warn('Validation failed: Missing name', {
          category: LOG_CATEGORIES.BUSINESS,
          validation: {
            field: 'name',
            error: 'Required field missing',
          },
        });
        
        throw this.ErrorFactory.validation('Validation failed', {
          name: 'Name is required',
        });
      }
      
      // Check for duplicate
      const existing = await this.exampleRepository.findOne({
        where: { name: data.name },
      });
      
      if (existing) {
        this.logger.warn('Duplicate example detected', {
          category: LOG_CATEGORIES.BUSINESS,
          duplicate: {
            field: 'name',
            value: data.name,
          },
        });
        
        throw this.ErrorFactory.conflict(
          'Example with this name already exists',
          'Example',
          'name',
          data.name
        );
      }
      
      // Create example
      const startTime = Date.now();
      const example = await this.exampleRepository.create(data);
      const duration = Date.now() - startTime;
      
      // Log success
      this.logger.info('Example created successfully', {
        category: LOG_CATEGORIES.BUSINESS,
        example: {
          id: example.id,
          name: example.name,
        },
        performance: {
          duration,
        },
      });
      
      return example;
    } catch (error) {
      // Log error
      this.logger.error('Failed to create example', {
        category: LOG_CATEGORIES.BUSINESS,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        data: {
          name: data.name,
          type: data.type,
        },
      });
      
      // Rethrow for global error handler
      throw error;
    }
  }

  /**
   * Gets an example by ID with logging
   * 
   * @param {string} id - Example ID
   * @returns {Promise<Object>} - Found example
   */
  async getExampleById(id) {
    this.logger.debug('Getting example by ID', {
      category: LOG_CATEGORIES.BUSINESS,
      example: { id },
    });
    
    try {
      const startTime = Date.now();
      const example = await this.exampleRepository.getById(id);
      const duration = Date.now() - startTime;
      
      if (!example) {
        this.logger.warn('Example not found', {
          category: LOG_CATEGORIES.BUSINESS,
          example: { id },
        });
        
        throw this.ErrorFactory.notFound('Example', id);
      }
      
      // Log performance if slow
      if (duration > 100) {
        this.logger.info('Slow example retrieval', {
          category: LOG_CATEGORIES.PERFORMANCE,
          example: { id },
          performance: {
            duration,
            slow: true,
          },
        });
      }
      
      return example;
    } catch (error) {
      // Log error
      this.logger.error('Failed to get example', {
        category: LOG_CATEGORIES.BUSINESS,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        example: { id },
      });
      
      // Rethrow for global error handler
      throw error;
    }
  }

  /**
   * Demonstrates logging for a complex business process
   * 
   * @param {Object} data - Process data
   * @returns {Promise<Object>} - Process result
   */
  async complexBusinessProcess(data) {
    const processId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    
    this.logger.info('Starting complex business process', {
      category: LOG_CATEGORIES.BUSINESS,
      process: {
        id: processId,
        type: 'complex-example',
        data: {
          name: data.name,
          items: data.items?.length,
        },
      },
    });
    
    try {
      // Log each step of the process
      this.logger.debug('Process step 1: Validation', {
        category: LOG_CATEGORIES.BUSINESS,
        process: { id: processId, step: 1 },
      });
      
      // Simulate process steps
      await this.simulateProcessStep('Validation', 100);
      
      this.logger.debug('Process step 2: Processing', {
        category: LOG_CATEGORIES.BUSINESS,
        process: { id: processId, step: 2 },
      });
      
      await this.simulateProcessStep('Processing', 200);
      
      this.logger.debug('Process step 3: Finalization', {
        category: LOG_CATEGORIES.BUSINESS,
        process: { id: processId, step: 3 },
      });
      
      await this.simulateProcessStep('Finalization', 150);
      
      // Log success
      this.logger.info('Complex business process completed', {
        category: LOG_CATEGORIES.BUSINESS,
        process: {
          id: processId,
          status: 'completed',
          duration: 450,
        },
      });
      
      return {
        processId,
        status: 'completed',
        result: 'Success',
      };
    } catch (error) {
      // Log error
      this.logger.error('Complex business process failed', {
        category: LOG_CATEGORIES.BUSINESS,
        process: {
          id: processId,
          status: 'failed',
        },
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
      });
      
      // Rethrow for global error handler
      throw error;
    }
  }

  /**
   * Simulates a process step with a delay
   * 
   * @param {string} step - Step name
   * @param {number} delay - Delay in milliseconds
   * @returns {Promise<void>} - Promise that resolves after the delay
   */
  async simulateProcessStep(step, delay) {
    return new Promise((resolve) => {
      setTimeout(resolve, delay);
    });
  }
}

module.exports = ExampleLoggingService;
