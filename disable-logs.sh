#!/bin/bash

# Create a script to modify the logs configuration in the container
cat > modify-logs.js << 'EOF'
// This script modifies the logs/index.js file to completely disable logs in production
const fs = require('fs');
const path = require('path');

const logsIndexPath = '/usr/app/src/infra/logs/index.js';

// Read the current file
const content = fs.readFileSync(logsIndexPath, 'utf8');

// Replace the production log settings
const updatedContent = content.replace(
  /if \(process\.env\.NODE_ENV === 'production'\) \{[\s\S]*?logSettings = \{[\s\S]*?};\s*}/m,
  `if (process.env.NODE_ENV === 'production') {
  // Production: Completely disable logs except for startup messages
  logSettings = {
    level: 'error',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: false,
        translateTime: 'yyyy-mm-dd HH:MM:ss',
        ignore: 'pid,hostname,req,res,responseTime',
        messageFormat: (log, messageKey) => {
          const message = log[messageKey];
          // Only show server listening messages
          if (message && (
            message.includes('Server listening at') ||
            message.includes('Starting application') ||
            message.includes('Error')
          )) {
            return message;
          }
          return ''; // Hide all other messages
        },
        singleLine: true,
      }
    },
    // Disable all request logging
    disableRequestLogging: true,
  };
}`
);

// Write the updated content back to the file
fs.writeFileSync(logsIndexPath, updatedContent);

// Also update the cleanLogs middleware
const cleanLogsPath = '/usr/app/src/app/handlers/middlewares/cleanLogs.js';
const cleanLogsContent = fs.readFileSync(cleanLogsPath, 'utf8');

const updatedCleanLogsContent = cleanLogsContent.replace(
  /const cleanLogsMiddleware = \(req, reply, done\) => \{[\s\S]*?done\(\);\s*};/m,
  `const cleanLogsMiddleware = (req, reply, done) => {
  // In production, completely disable all logs
  if (process.env.NODE_ENV === 'production') {
    // Store the original logger methods
    const originalError = req.log.error;
    
    // Override all logger methods to do nothing
    req.log.info = () => {};
    req.log.warn = () => {};
    req.log.debug = () => {};
    req.log.trace = () => {};
    
    // Only keep error logging
    req.log.error = (obj, msg, ...args) => {
      if (typeof obj === 'string' && obj.includes('Error')) {
        originalError.call(req.log, obj);
      } else if (obj && obj.message && obj.message.includes('Error')) {
        originalError.call(req.log, obj, msg, ...args);
      }
    };
  }
  
  done();
};`
);

fs.writeFileSync(cleanLogsPath, updatedCleanLogsContent);

console.log('Successfully updated logs configuration');
EOF

# Copy the script to the container
docker cp modify-logs.js deployment-backend-1:/tmp/

# Execute the script in the container
docker exec -it deployment-backend-1 node /tmp/modify-logs.js

# Restart the container
docker restart deployment-backend-1

# Clean up
rm modify-logs.js

echo "Logs have been disabled in production mode"
