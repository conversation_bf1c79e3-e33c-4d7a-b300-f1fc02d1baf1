<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-right:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s36{border-bottom:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s37{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s35{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#4f81bd;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#4f81bd;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s11{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#4f81bd;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s38{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1864623824C0" style="width:117px;" class="column-headers-background">A</th><th id="1864623824C1" style="width:174px;" class="column-headers-background">B</th><th id="1864623824C2" style="width:72px;" class="column-headers-background">C</th><th id="1864623824C3" style="width:252px;" class="column-headers-background">D</th><th id="1864623824C4" style="width:233px;" class="column-headers-background">E</th><th id="1864623824C5" style="width:330px;" class="column-headers-background">F</th><th id="1864623824C7" style="width:388px;" class="column-headers-background">H</th><th id="1864623824C8" style="width:154px;" class="column-headers-background">I</th><th id="1864623824C9" style="width:174px;" class="column-headers-background">J</th><th id="1864623824C10" style="width:369px;" class="column-headers-background">K</th><th id="1864623824C11" style="width:169px;" class="column-headers-background">L</th><th id="1864623824C12" style="width:206px;" class="column-headers-background">M</th><th id="1864623824C13" style="width:133px;" class="column-headers-background">N</th><th id="1864623824C14" style="width:133px;" class="column-headers-background">O</th><th id="1864623824C15" style="width:133px;" class="column-headers-background">P</th><th id="1864623824C16" style="width:133px;" class="column-headers-background">Q</th><th id="1864623824C17" style="width:133px;" class="column-headers-background">R</th><th id="1864623824C18" style="width:133px;" class="column-headers-background">S</th><th id="1864623824C19" style="width:133px;" class="column-headers-background">T</th><th id="1864623824C20" style="width:133px;" class="column-headers-background">U</th><th id="1864623824C21" style="width:133px;" class="column-headers-background">V</th><th id="1864623824C22" style="width:133px;" class="column-headers-background">W</th><th id="1864623824C23" style="width:133px;" class="column-headers-background">X</th><th id="1864623824C24" style="width:133px;" class="column-headers-background">Y</th><th id="1864623824C25" style="width:133px;" class="column-headers-background">Z</th></tr></thead><tbody><tr style="height: 42px"><th id="1864623824R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Assign</td><td class="s0">Defects</td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s2"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1864623824R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s3" colspan="6">Entering of OFM Quantity per Supplier</td><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s5"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1864623824R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s6">PRS-1300-023</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if User can click Enter Canvass option in Select Actions</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button</td><td class="s6">3. Enter Canvas button is verified in Select Actions</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s10"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s6">PRS-1300-024</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify the Selection of items for Canvassing</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s</td><td class="s6">4. Items are visible and available for the User</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s6">PRS-1300-025</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verifiy if User is able to select Supplier per item in Canvassing</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Select Item/s in the list<br>8. Click Enter Canvass<br>9. Select Supplier</td><td class="s6">9. User is able to select Supplier per item</td><td class="s6"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s6">PRS-1300-026</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if the Quantity entered per Supplier is greater than to the Quantity Requested or Remaining Quantity for Canvassing<br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Submit Canvass</td><td class="s6">8 Canvass should not be able to save draft and submit canvass</td><td class="s6"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s6">PRS-1300-027</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify if the Quantity entered per Supplier is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when save as canvass draft</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Save as draft</td><td class="s6">8. User can enter quantity per Supplier more than or equal to the Quantity Requested</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s6">PRS-1300-028</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify if the Quantity entered per Supplier is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when submitting canvass</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Submit Canvass</td><td class="s6">8. Canvass creation should proceed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s6">PRS-1300-029</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Quantity is more than 3 Decimal Places in Canvass Creation<br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Input more than 3 decimal places quantity</td><td class="s6">9. Canvass creation should not proceed if quantity is more than 3 decimal places</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s6">PRS-1300-030</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if Quantity is only at 3 Decimal Places in Canvass Creation</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. User can input maximum of 3 Decimal Places for Quantity</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s6">PRS-1300-031</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Quantity is more than 3 Decimal Places Steelbars item<br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Input more than 3 decimal places quantity</td><td class="s6">9. Canvass creation should not proceed if quantity is more than 3 decimal places</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s6">PRS-1300-032</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verifiy if decimal is allowed and up to 3 decimal in the quantity per Supplier is available in SteelBars</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. Decimal is allowed in steelbars and up to 3 decimal</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s6">PRS-1300-033</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if the Quantity entered per Supplier in Steel bars is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when save as canvass draft</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Save as draft</td><td class="s6">10. User can enter quantity per Supplier more than or equal to the Quantity Requested</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s6">PRS-1300-034</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if the Quantity entered per Supplier in Steel bars is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when submit</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submit</td><td class="s6">10. Canvass creation should proceed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s6">PRS-1300-035</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if the Maximum Discounts and Prices are more than 2 Decimal Places <br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. Discounts and Prices should not be more than 2 decimal places.</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s6">PRS-1300-036</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify if the Maximum Discounts and Prices are in 2 Decimal Places </td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. Maximum 2 Decimal in Discounts and Prices should not be more than 2 decimal places</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s6">PRS-1300-037</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if Canvassing is allowed if the Supplier Quantity is less or equal to the Requested Quantity</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Enter Quantity less than requested quantity</td><td class="s6">9. Canvassing should only allow if the Supplier Quantity is less or equal to the Requested Quantity</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s6">PRS-1300-038</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify Canvass draft open when Item quantity that was approved and has been completed but there is still a draft cs with the same item</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Save as draft<br>11. Open Canvass draft</td><td class="s6">11. User should be able to open the drafted Canvass Sheet</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s6">PRS-1300-039</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify the retain same Item that was completed on other Canvass Sheets</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Save as draft<br>11. Open the other Canvass draft</td><td class="s6">11. Should retain the same Item that was completed on other Canvass Sheets</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s6">PRS-1300-040</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify if a display Warning Modal when the Canvass Sheet will be Submitted</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. A display warning modal is expected to show when submitting Canvass sheet<br>a. This warning will tell the User that the Item will be removed as this has been completed to a different Canvass Sheet</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s6">PRS-1300-041</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify display of a Toast Message that it was successfully Submitted and the Item is removed</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. A message is expected to display when an item is successfully submited and removed</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s6">PRS-1300-042</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify if User is allowed to do CS Approve when item quantity that was approved has been completed but there is still an ongoing Canvass sheet with the same item </td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. User is expected to be able to do CS Approval </td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s6">PRS-1300-043</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify the display Error Message that the Item has been completed and the Approval will not be allowed to proceed</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. Should display an Error Message that the Item has been completed and the Approval will not be allowed to proceed.<br>a. Approver&#39;s Action is to only Reject the Canvass Sheet</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s6">PRS-1300-044</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify the required Assigned Purchasing Staff to edit the Canvass Sheet</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. Should require the Assigned Purchasing Staff to edit the Canvass Sheet<br>a. Remove Option will be displayed under Actions Button only for the Item/s that was Completed.<br>b. Require removal of the Item</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s6">PRS-1300-045</td><td class="s6">Entering of OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify resubmitting and repeating the Approval Process for the Approver that has rejected the Canvass Sheet</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be OFM or OFM Transfer of Materials<br>2. Canvass Sheet is created but rejected</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. User should be able to resubmit and repeat the Approval process</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s13" colspan="7">Entering of Non OFM Quantity per Supplier</td><td class="s5"></td><td class="s14" colspan="4"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="1864623824R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s6">PRS-1300-035</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if User can click Enter Canvass option in Select Actions</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button</td><td class="s6">3. Enter Canvas button is verified in Select Actions</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6">NON-OFM<br>NON-OFM TRANSFER<br><br><br>RS-01AA00000271</td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s6">PRS-1300-036</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">HIgh</td><td class="s6">Verify the Selection of items for Canvassing</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s</td><td class="s6">4. Items are visible and available for the User</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s6">PRS-1300-037</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verifiy if User is able to select Supplier per item in Canvassing</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Select Item/s in the list<br>8. Click Enter Canvass<br>9. Select Supplier</td><td class="s6">9. User is able to select Supplier per item</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s10"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s6">PRS-1300-038</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify if the Quantity entered per Supplier is less than or more than to the Quantity Requested or Remaining Quantity for Canvassing when save as draft</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Verify the Quantity</td><td class="s6">8. User can enter quantity per Supplier less than or more than the Quantity Requested</td><td class="s6"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s6">PRS-1300-039</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify if the Quantity entered per Supplier is less than or more than to the Quantity Requested or Remaining Quantity for Canvassing when submitting canvas</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Input Quantity<br>9. Submit Canvass</td><td class="s6">9. Canvass should proceed when quantity per Supplier less than or more than the Quantity Requested</td><td class="s8"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s6">PRS-1300-040</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if Quantity is more than 3 Decimal Places in Canvass Creation<br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Input more than 3 decimal places quantity</td><td class="s6">9. Canvass creation should not proceed if quantity is more than 3 decimal places</td><td class="s16"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s6">PRS-1300-041</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">HIgh</td><td class="s6">Verify if Quantity is only at 3 Decimal Places in Canvass Creation</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. User can input maximum of 3 Decimal Places for Quantity</td><td class="s17"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s6">PRS-1300-042</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if Canvassing is allowed even if the Supplier Quantity is equal or greater than to the Requested Quantity</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Enter Quantity equal or more than requested quantity</td><td class="s6">7. User can enter quantity per Supplier more than or equal to the Quantity Requested</td><td class="s6" rowspan="12"></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s6">PRS-1300-043</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify if the Maximum Discounts and Prices are more than 2 Decimal Places <br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. Discounts and Prices should not be more than 2 decimal places.</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s6">PRS-1300-044</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify if the Maximum Discounts and Prices are in 2 Decimal Places </td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity</td><td class="s6">9. Maximum 2 Decimal in Discounts and Prices should not be more than 2 decimal places</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s6">PRS-1300-045</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify if highlight in Requested Quantity is visible if one of the Suppliers has a Higher Quantity than Requested</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Enter Quantity equal or more than requested quantity</td><td class="s6">9. Highlight in Requested Quantity is not verified</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s6">PRS-1300-046</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify Canvass draft open when Item quantity that was approved and has been completed but there is still a draft cs with the same item</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Save as draft<br>11. Open Canvass draft</td><td class="s6">11. User should be able to open the drafted Canvass Sheet</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s18"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td><td class="s19"></td></tr><tr style="height: 19px"><th id="1864623824R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s6">PRS-1300-047</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify the retain same Item that was completed on other Canvass Sheets</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Save as draft<br>11. Open the other Canvass draft</td><td class="s6">11. Should retain the same Item that was completed on other Canvass Sheets</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s6">PRS-1300-048</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify if a display Warning Modal when the Canvass Sheet will be Submitted</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. A display warning modal is expected to show when submitting Canvass sheet<br>a. This warning will tell the User that the Item will be removed as this has been completed to a different Canvass Sheet</td><td class="s9">Not Started</td><td class="s6">spillover sprint 2</td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s6">PRS-1300-049</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify display of a Toast Message that it was successfully Submitted and the Item is removed</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. A message is expected to display when an item is successfully submited and removed</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s6">PRS-1300-050</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Critical</td><td class="s6">Verify if User is allowed to do CS Approve when item quantity that was approved has been completed but there is still an ongoing Canvass sheet with the same item </td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. User is expected to be able to do CS Approval </td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s6">PRS-1300-051</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">Minor</td><td class="s6">Verify the display Error Message that the Item has been completed and the Approval will not be allowed to proceed</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. Should display an Error Message that the Item has been completed and the Approval will not be allowed to proceed.<br>a. Approver&#39;s Action is to only Reject the Canvass Sheet</td><td class="s9">Not Started</td><td class="s6">spillover sprint 2</td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s6">PRS-1300-052</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify the required Assigned Purchasing Staff to edit the Canvass Sheet</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is to be created</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. Should require the Assigned Purchasing Staff to edit the Canvass Sheet<br>a. Remove Option will be displayed under Actions Button only for the Item/s that was Completed.<br>b. Require removal of the Item</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s6"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="1864623824R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s6">PRS-1300-053</td><td class="s6">Entering of Non OFM Quantity per Supplier</td><td class="s7">High</td><td class="s6">Verify resubmitting and repeating the Approval Process for the Approver that has rejected the Canvass Sheet</td><td class="s6">1. Requisition Slip has been fully Approved<br>    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials<br>2. Canvass Sheet is created but rejected</td><td class="s6">1. Select an RS<br>2. Navigate to Select Actions Modal<br>3. Verify the Enter Canvass button<br>4. Click Add item/s<br>5. Select item/s in the list<br>6. Click Enter Canvass<br>7. Create Canvass<br>8. Populate the requirements<br>9. Verify the Quantity<br>10. Submitt canvass</td><td class="s6">10. User should be able to resubmit and repeat the Approval process</td><td class="s9">Not Started</td><td class="s6">CITYLANDPRS-1536</td><td class="s6"></td><td class="s10"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s20" colspan="12">[CANVASS CREATION] Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="1864623824R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s6">PRS-1315-015</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify RS Number column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if RS Number column sorts accordingly:<br>    i) RS Number<br>        a. Sort by 0-9, 9-0 || Default </td><td class="s6">2. RS Number column sorts accordingly:<br>    i) RS Number<br>        a. Should be sorted by 0-9, 9-0 || Default</td><td class="s21" rowspan="8"><a target="_blank" href="https://docs.google.com/spreadsheets/u/0/d/1n2WB-K-1nxYqd9cAlgiOiyarEoIDESbfFFibDRD33-w/edit">Me-Ann_Sprint1_Test Result</a></td><td class="s9">Not Started</td><td class="s22"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1524">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1534<br> <br>04/24 : Devs : currently , mock data and for now is the sorting is not working</a></td><td class="s22"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s6">PRS-1315-016</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Supplier column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Supplier sorts accordingly:<br>    ii) Supplier<br>        a. Sort by 0-9, A-Z || 9-0, Z-A || Default </td><td class="s6">2. Supplier column sorts accordingly:<br>    ii) Supplier<br>        a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default<br></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s6">PRS-1315-017</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Price Per Unit column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Price Per Unit column sorts accordingly:<br>    iii) Price Per Unit<br>        a. Sort by 0-9, 9-0 || Default</td><td class="s6">2. Price Per Unit column sorts accordingly:<br>    iii) Price Per Unit<br>        a. Should be sorted by 0-9, 9-0 || Default</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s6">PRS-1315-018</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Qty column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Qty column sorts accordingly:<br>    iv) Qty<br>        a. Sort by 0-9, 9-0 || Default</td><td class="s6">2. Qty column sorts accordingly:<br>    iv) Qty<br>        a. Should be sorted by 0-9, 9-0 || Default</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s6">PRS-1315-019</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Date Purchased column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Date Purchased column sorts accordingly:<br>    v) Date Purchased<br>       a. Sort by Oldest Date-Latest Date, Latest Date-Oldest Date || Default</td><td class="s6">2. Date Purchased column sorts accordingly:<br>    v) Date Purchased<br>       a. Should be sorted by Oldest Date-Latest Date, Latest Date-Oldest Date || Default</td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s6">PRS-1315-020</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that &quot;---&quot; is displayed for no Supplier</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Observe no values for Supplier<br>2. Validate values can be displayed as &quot;---&quot;</td><td class="s6">2. Values can be displayed as<br>    ii) Supplier<br>        a. Can be displayed as &quot;---&quot;<br></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s6">PRS-1315-021</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that &quot;---&quot; is displayed for no Price Per Unit</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Observe no values for Price per Unit<br>2. Validate values can be displayed as &quot;---&quot;</td><td class="s6">2. Values can be displayed as<br>    iii) Price Per Unit<br>        a. Can be displayed as &quot;---&quot;<br></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s23"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s6">PRS-1315-022</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that &quot;---&quot; is displayed for no Date Purchased values</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Observe no values for Date Purchased<br>2. Validate values can be displayed as &quot;---&quot;</td><td class="s6">2. Values can be displayed as<br>    v) Date Purchased<br>       a. Can be displayed as &quot;---&quot;<br></td><td class="s9">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 19px"><th id="1864623824R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s25" colspan="12">[CANVASS - CREATION] Canvass Status updates</td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td></tr><tr style="height: 19px"><th id="1864623824R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s7">PRS-1495-001</td><td class="s6">Canvass Status Updates</td><td class="s6">High</td><td class="s6">Verify Canvass Status updates to &quot;CS Draft&quot; upon drafting a sheet.</td><td class="s6">1. Logged as any type of user.<br>2. Canvass Sheet created.</td><td class="s6">1. Draft a Canvass Sheet.<br>2. Save the draft.</td><td class="s6">2. Status through Related Document, Canvass Sheet and Request History should update to:<br><br>a. CS Draft<br>BG - #5F636833<br>Text - #5F6368</td><td class="s27" rowspan="6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1fWxylEk5sjSxKLOzN5LGRif1sdroVGgNb1WuqebYTGs/edit?gid=0#gid=0">Verna Sprint 2 Test Results</a></td><td class="s28">Passed</td><td class="s29"></td><td class="s30">Verna</td><td class="s31"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s7">PRS-1495-002</td><td class="s6">Canvass Status Updates</td><td class="s6">High</td><td class="s6">Verify Canvass Status updates to &quot;For CS Approval&quot; upon submission.</td><td class="s6">1. Logged as any type of user.<br>2. Canvass Sheet drafted.</td><td class="s6">1. Submit the drafted Canvass Sheet.</td><td class="s6">1. Status through Related Document, Canvass Sheet and Request History should update to:<br><br>b. For CS Approval<br>BG - #F0963D33<br>Text - #F0963D</td><td class="s32">Failed</td><td class="s29" rowspan="4">Correct canvass status are only updated on the Canvass Tab.</td><td class="s30">Verna</td><td class="s33" rowspan="4"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1687">CITYLANDPRS-1687</a></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s7">PRS-1495-003</td><td class="s6">Canvass Status Updates</td><td class="s6">High</td><td class="s6">Verify Canvass Status updates to &quot;CS Rejected&quot; if any approver rejects.</td><td class="s6">1. Logged in as the current approver.<br>2. Canvass Sheet submitted for approval.</td><td class="s6">1. Approver rejects the Canvass Sheet.</td><td class="s6">1. Status through Related Document, Canvass Sheet and Request History should update to:<br><br>c. CS Rejected<br>BG - #DC433B33<br>Text - #DC433B</td><td class="s32">Failed</td><td class="s30">Verna</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s7">PRS-1495-004</td><td class="s6">Canvass Status Updates</td><td class="s6">High</td><td class="s6">Verify Canvass Status updates to &quot;CS Approved&quot; when all approvers approve.</td><td class="s6">1. Logged in as the current approver.<br>2. Canvass Sheet submitted for approval.</td><td class="s6">1. All approvers approve the Canvass Sheet.</td><td class="s6">1. Status through Related Document, Canvass Sheet and Request History should update to:<br><br>d. CS Approved<br>BG - #1EA52B33<br>Text - #1EA52B</td><td class="s32">Failed</td><td class="s30">Verna</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s7">PRS-1495-005</td><td class="s6">Canvass Status Updates</td><td class="s6">Critical</td><td class="s6">Verify Canvass Sheet returns to &quot;For CS Approval&quot; after resubmission from rejection.</td><td class="s6">1. Logged in as the current approver.<br>2. Canvass Sheet rejected previously.</td><td class="s6">1. Resubmit the rejected Canvass Sheet.</td><td class="s6">1. Status through Related Document, Canvass Sheet and Request History should update to:<br><br>For CS Approval<br>BG - #F0963D33<br>Text - #F0963D</td><td class="s32">Failed</td><td class="s30">Verna</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s7">PRS-1495-006</td><td class="s6">Canvass Status Updates</td><td class="s6">High</td><td class="s6">Verify re-submission flow updates canvass approver status to &quot;Pending&quot;.</td><td class="s6">1. Logged in as the current approver.<br>2. Canvass Sheet resubmitted after rejection.</td><td class="s6">1. Resubmit the rejected Canvass Sheet.</td><td class="s6">1. Approver Status updates to &quot;Pending&quot;.</td><td class="s28">Passed</td><td class="s10"></td><td class="s30">Verna</td><td class="s31"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s25" colspan="12">[CANVASS - CREATION] Item Group Canvassing</td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td><td class="s26"></td></tr><tr style="height: 19px"><th id="1864623824R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s7">PRS-1646-001</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify checkbox column appears as the first column in the Table</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Go to Canvass Sheet and click the Steel Bars Tab<br>2. Observe the checkbox column in the Table</td><td class="s6">2. Should add a Checkbox Column per Item as the first Column in the Table</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s7">PRS-1646-002</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s35">Verify checkbox column appears only for Steelbar items</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Go to Canvass Sheet and click the Items Tab<br>2. Check if there is no column for checkbox</td><td class="s6">2. Should only display the Checkbox Column for Steelbar Items</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R106" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">107</div></th><td class="s7">PRS-1646-003</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify checkbox is enabled if item has no entered Canvass</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Check Steelbar items with no Canvass<br>2. Observe Checkbox</td><td class="s6">2. Should enable the Checkbox for the Item if it has no any Canvass Entered</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R107" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">108</div></th><td class="s7">PRS-1646-004</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify if user can select two or more items in the Table</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click two or more checkboxes<br>2. Validate that it allows selection of two or more Steelbar Items</td><td class="s6">2. Should allow selection of two or more Items in the Table</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R108" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">109</div></th><td class="s7">PRS-1646-005</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify selection of two or more items enables &quot;Item Group Canvass&quot; button</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click two or more checkboxes<br>2. Check if the Item Group Canvass button enables</td><td class="s6">2. Should display a Button at the Top Left of the Table with a Label of Item Group Canvass<br>        i. Should only be enabled if the selected Checkbox is two or more</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R109" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">110</div></th><td class="s7">PRS-1646-006</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s35">Verify If  &quot;Item Group Canvass&quot; button is initially disabled when there is no or there is only one item selected</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click only one checkbox <br>2. Check if the Item Group Canvass button is initially disabled when there is no checkbox clicked and if there is only one selected</td><td class="s6">2. Should be intially disabled when there is no checkbox selected and if only one checkbox is selected</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R110" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">111</div></th><td class="s7">PRS-1646-007</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify &quot;Item Group Canvass&quot; page opens after clicking button</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click the Item Group Canvass Button<br>2. Validate display of Item Group Canvass Page details</td><td class="s6">2. Should display a Page for entering the Details of the Item Group</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R111" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">112</div></th><td class="s7">PRS-1646-008</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify fields displayed in Item Group Canvass page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Observe the Item Group Canvass Page<br>2. Check if it has the all the necessary fields for details</td><td class="s6">2.  Should have the following Details<br>        i. Supplier<br>        ii. Terms<br>        iii. Discount<br>        iv. Items Table</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R112" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">113</div></th><td class="s7">PRS-1646-009</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify Discount field allows negative sign</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input negative sign or value to the Discount field (e.g., -10)<br>2. Validate if it allows Negative sign input</td><td class="s6">2.   Discount<br>             i) Should allow entering of Negative Sign or - once Discount Percentage is clicked</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R113" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">114</div></th><td class="s7">PRS-1646-010</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s35">Verify invalid placement of negative sign is not allowed</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input negative sign between numbers or at the end (e.g.,  10-500 or 100-)<br>2. Validate if it does not allow the input</td><td class="s6">2.  Should not allow invalid placement of negative sign</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R114" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">115</div></th><td class="s7">PRS-1646-011</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify negative discount in Percentage increase the amount of the items included in the Item Group</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1.  Populate all the fields and input negative discount in Percentage<br>2. Validate if it will increase the Amounts of the Items included in the Item Group based on the negative Percentage entered</td><td class="s6">2.  Negative discount will increase the Amounts of the Items included in the Item Group<br>     Example: Discount to be entered: -10%<br>        - Item 1 Price: ₱100.00 -&gt; ₱110.00<br>        - Item 2 Price: ₱200.00 -&gt; ₱210.00<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R115" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">116</div></th><td class="s7">PRS-1646-012</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify negative discount in Fixed Amount increase the amount of the items included in the Item Group</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1.  Populate all the fields and input negative discount in Fixed Amount<br>2. Validate if it will increase the Amounts of the Items included in the Item Group based on the negative Fixed Amount entered</td><td class="s6">2.  Negative discount will increase the Amounts of the Items included in the Item Group<br>     Example: Discount to be entered: -50<br>        - Item 1 Price: ₱100.00 -&gt; ₱150.00<br>        - Item 2 Price: ₱200.00 -&gt; ₱250.00<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R116" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">117</div></th><td class="s7">PRS-1646-013</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Item Name Column</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Observe the Items Table in the Item Group Canvass Page<br>2. Validate if the table is sorted properly</td><td class="s6">2.  Should be sorted Alphabetically by Item Name - Special Charactes + 0-9 + A-Z<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R117" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">118</div></th><td class="s7">PRS-1646-014</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify Items Table displays selected items</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Check items in the Items Table in the Item Group Canvass Page<br>2. Validate if the items displayed are the selected items</td><td class="s6">2.  Should display the selected Items for the Item Group<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R118" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">119</div></th><td class="s7">PRS-1646-015</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify if maximum of 10 items per Page is displayed</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Select more than 10 items and go to the Item Group Canvass Page<br>2. Check if the Items Table display 10 items per Page</td><td class="s6">2.  Should display 10 Items per Page<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R119" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">120</div></th><td class="s7">PRS-1646-016</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Item Column	</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Item Column<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 a) Item - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R120" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">121</div></th><td class="s7">PRS-1646-017</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Unit Column</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Unit Column<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                  b) Unit - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R121" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">122</div></th><td class="s7">PRS-1646-018</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Quantity Column</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Quantity Column<br>2. Validate if sorting works properly and allow 3-Decimal Places</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 c) Quantity - 0-9, 9-0 || Default<br>                     1) Should allow 3-Decimal Places</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R122" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">123</div></th><td class="s7">PRS-1646-019</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Weight Column</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Weight Column<br>2. Validate if sorting works properly and allow 3-Decimal Places</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 d) Weight - 0-9, 9-0 || Default<br>                     1) Should allow 3-Decimal Places</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R123" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">124</div></th><td class="s7">PRS-1646-020</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Unit Price Column</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Unit Price Column<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                  e) Unit Price - 0-9, 9-0 || Default</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R124" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">125</div></th><td class="s7">PRS-1646-021</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify Unit Price allows only valid input</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input Numbers and Dot in the Unit Price fields<br>2. Validate if it allows Numbers and Dot as inputs</td><td class="s6">2.   Should allow Numbers and Dot Only<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R125" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">126</div></th><td class="s7">PRS-1646-022</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s35">Verify Unit Price does not allow invalid input</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input any characters except for Numbers and Dot in the Unit Price fields<br>2. Validate if it does not allow any characters except for Numbers and Dot as inputs</td><td class="s6">2.   Should not allow other characters except for Numbers and Dot <br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R126" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">127</div></th><td class="s7">PRS-1646-023</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify Unit Price allows only 2-Decimal Places</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input amount with 2-Decimal Places in the Unit Price fields<br>2.Validate if it allows 2-Decimal Places inputs</td><td class="s6">2.   Should allow 2-Decimal Places<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R127" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">128</div></th><td class="s7">PRS-1646-024</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s35">Verify Unit Price does not allow more than 2-Decimal Places</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input amount withmore than  2-Decimal Places in the Unit Price fields<br>2.Validate if it does not allow more than 2-Decimal Places inputs</td><td class="s6">2.   Should not allow 2-Decimal Places<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R128" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">129</div></th><td class="s7">PRS-1646-025</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify Peso sign placeholder in Unit Price</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. View Unit Price fields<br>2. Check if they have a Peso Sign Placeholder</td><td class="s6">2.   Should have a Peso Sign Placeholder<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R129" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">130</div></th><td class="s7">PRS-1646-026</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify Maximum of 10 Character Input in Unit Price</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input 10 Characters or less in the Unit Price field<br>2. Validate if it accepts 10 characters or less</td><td class="s6">2.   Should have a Maximum of  10 Characters<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R130" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">131</div></th><td class="s7">PRS-1646-027</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s35">Verify Behavior when more than 10 Characters are entered in Unit Price</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Input more than 10 Characters or less in the Unit Price field<br>2. Validate if it does not accept more than 10 characters</td><td class="s6">2.   Should only accept maximum of  10 Characters<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R131" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">132</div></th><td class="s7">PRS-1646-028</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify if Unit Price is a required Field</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Leave the Unit Price fields blank and Confirm<br>2. Validate if it requires input for the Unit Price fields</td><td class="s6">2.   Should require the Field<br></td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R132" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">133</div></th><td class="s7">PRS-1646-029</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify Cancel Button behavior in Item Group Canvass Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click the Cancel button in the Item Group Canvass Page<br>2. Click Continue upon display of Cancel Canvas Modal and validate if it returns to Canvass Form</td><td class="s6">2.   If Cancel Button is clicked, should display a Confirmation Modal<br>           i) Once Confirmed, should return to Canvass Form</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R133" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">134</div></th><td class="s7">PRS-1646-030</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify Confirm Button behavior in Item Group Canvass Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click the Confirm button in the Item Group Canvass Page<br>2. Click Submit upon display of Confirm Canvas Modal and validate if it applies changes to all the selected items</td><td class="s6">2.   If Confirm Button is clicked, should display a Confirmation Modal<br>           i) Once Confirmed, should apply the changes to all of the selected Items<br>              a. Selected Supplier<br>              b. Supplier Terms<br>              c. Discount<br>              d. Item Quantity <br>              e. Item Unit Price</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R134" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">135</div></th><td class="s7">PRS-1646-031</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify Confirm Button behavior in Item Group Canvass Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click the Confirm button in the Item Group Canvass Page<br>2. Click Submit upon display of Confirm Canvas Modal and validate if it applies changes to all the selected items</td><td class="s6">2.   If Confirm Button is clicked, should display a Confirmation Modal<br>           i) Once Confirmed, should apply the changes to all of the selected Items<br>              a. Selected Supplier<br>              b. Supplier Terms<br>              c. Discount<br>              d. Item Quantity <br>              e. Item Unit Price</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R135" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">136</div></th><td class="s7">PRS-1646-032</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify Negative discount is also applicable when creating a Canvass per Steelbar item</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click 3 dotted icon in any Steelbar Item and enter Canvass<br>2. Validate if it allows Negative sign input in Discount</td><td class="s6">2.  Should also include the Negative Discount when creating a Canvass per Item<br>    a. Should only be implemented for Steelbar Items<br>        i. Should allow entering of Negative Sign or - once Discount Percentage is clicked</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R136" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">137</div></th><td class="s7">PRS-1646-033</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify negative discount in Percentage increase the amount of the specific Steelbar item canvassed</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1.  Populate all the fields and input negative discount in Percentage<br>2. Validate if it will increase the Amount of the Item based on the negative Percentage entered</td><td class="s6">2.  Negative discount will increase the Amount of the Item<br>     Example: Item Price: ₱100.00 -&gt; ₱110.00<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R137" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">138</div></th><td class="s7">PRS-1646-034</td><td class="s6">Item Group Canvassing</td><td class="s6">High</td><td class="s6">Verify if maximum of 10 items per Page is displayed in Canvass Sheet Items Table</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. More than 10 items was requested and go to the Canvass Sheet Page<br>2. Check if the Items Table in Canvass Sheet display 10 items per Page</td><td class="s6">2.  Should display 10 Items per Page<br>  </td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R138" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">139</div></th><td class="s7">PRS-1646-035</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Item Column in Canvass Sheet Items Table     </td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Item Column<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 i) Item - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R139" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">140</div></th><td class="s7">PRS-1646-036</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Unit Column in Canvass Sheet Items Table     </td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Unit Column in Canvass Sheet Items Table<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 ii) Unit - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R140" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">141</div></th><td class="s7">PRS-1646-037</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Requested Quantity Column in Canvass Sheet Items Table     </td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Requested Quantity Column  in Canvass Sheet Items Table<br>2. Validate if sorting works properly and allow 3-Decimal Places</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 iii) Requested Quantity - 0-9, 9-0 || Default<br>                     a) Should allow 3-Decimal Places</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R141" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">142</div></th><td class="s7">PRS-1646-038</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Weight Column in Canvass Sheet Items Table     </td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Weight Column  in Canvass Sheet Items Table<br>2. Validate if sorting works properly and allow 3-Decimal Places</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 iv) Weight - 0-9, 9-0 || Default<br>                     a) Should allow 3-Decimal Places</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R142" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">143</div></th><td class="s7">PRS-1646-039</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Approved Quantity Column in Canvass Sheet Items Table     </td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Approved Quantity Column in Canvass Sheet Items Table<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 v) Approved Quantity - 0-9, 9-0 || Default<br>                     a) Should allow 3-Decimal Places</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R143" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">144</div></th><td class="s7">PRS-1646-040</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify sorting of Remaining GFQ Column in Canvass Sheet Items Table     </td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click sorting buttons for Remaining GFQ Column in Canvass Sheet Items Table<br>2. Validate if sorting works properly</td><td class="s6">2.   Should have the following Columns and sorting per Column<br>                 vi) Remaining GFQ - 0-9, 9-0 || Default<br>                     a) Should allow 3-Decimal Places</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R144" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">145</div></th><td class="s7">PRS-1646-041</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify changes not saved until Save Draft in Canvass Sheet Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click Save Draft in the Canvass Sheet after Item Group Canvassing<br>2. Validate if the changes has been saved</td><td class="s6">2.   Should Save the changes when Save Draft Button in the Canvass Form is clicked</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R145" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">146</div></th><td class="s7">PRS-1646-042</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify changes not saved until Submit in Canvass Sheet Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click Save Draft in the Canvass Sheet after Item Group Canvassing<br>2. Validate if the changes has been saved</td><td class="s6">2.   Should Save the changes when Submit Button in the Canvass Form is clicked</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R146" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">147</div></th><td class="s7">PRS-1646-043</td><td class="s6">Item Group Canvassing</td><td class="s6">Critical</td><td class="s6">Verify changes not saved when Cancel button is clicked in Canvass Sheet Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click Cancel button in the Canvass Sheet after Item Group Canvassing<br>2. Validate if the changes has not been saved</td><td class="s6">2.   Should not save the changes when Cancel Button in the Canvass Form is clicked</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R147" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">148</div></th><td class="s7">PRS-1646-044</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify pagination works in Canvass Sheet Items Table</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click numbered pagination buttons and arrows left and right below the Items Table in Canvass Sheet<br>2. Validate if the buttons work and successfully navigate to other pages of the Items Table</td><td class="s6">2.   Pagination should function as intended</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R148" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">149</div></th><td class="s7">PRS-1646-045</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify pagination works in Item Group Canvass Items Table</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click numbered pagination buttons and arrows left and right below the Items Table in Item Group Canvass Page<br>2. Validate if the buttons work and successfully navigate to other pages of the Items Table</td><td class="s6">2.   Pagination should function as intended</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R149" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">150</div></th><td class="s7">PRS-1646-046</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify Go back Button works as intended in Item Group Canvass Page</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click Go back button in Item Group Canvass Page<br>2. Validate if it returns to the Canvass Sheet</td><td class="s6">2.   Should return to the Canvass Sheet</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R150" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">151</div></th><td class="s7">PRS-1646-047</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify X Button works as intended in Confirm Canvas Modal</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click X button in Confirm Canvas Modal<br>2. Validate if it closes the modal</td><td class="s6">2.   Should close the Confirm Canvas Modal</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R151" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">152</div></th><td class="s7">PRS-1646-048</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify X Button works as intended in Cancel Canvas Modal</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click X button in Cancel Canvas Modal<br>2. Validate if it closes the modal</td><td class="s6">2.   Should close the Cancel Canvas Modal</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R152" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">153</div></th><td class="s7">PRS-1646-049</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify Cancel Action Button works as intended in Confirm Canvas Modal</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click Cancel Action button in Confirm Canvas Modal<br>2. Validate if it closes the modal and does not apply any changes</td><td class="s6">2.   Should close the Confirm Canvas Modal and should not apply any changes and return to Item Group Canvas Page</td><td class="s16"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R153" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">154</div></th><td class="s7">PRS-1646-050</td><td class="s6">Item Group Canvassing</td><td class="s6">Medium</td><td class="s6">Verify Cancel Button works as intended in Cancel Canvas Modal</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Created Requisition Slip must have a Steelbar as an Item</td><td class="s6">1. Click X button in Cancel Canvas Modal<br>2. Validate if it closes the modal</td><td class="s6">2.   Should close the Cancel Canvas Modal and return to Item Group Canvas Page</td><td class="s17"></td><td class="s34">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s21"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="1864623824R154" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">155</div></th><td class="s20" colspan="15">[CANVASS CREATION] Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R155" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">156</div></th><td class="s6">PRS-1390-001</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify that Account Code, Supplier, and Canvass Status columns are removed from the Items Table</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click Canvass Sheet Number<br>2. Observe the Items Table</td><td class="s6">1. Should update the Items Table Column<br>2. Should remove the <br>    a. Account Code Column<br>    b. Supplier Column<br>    c. Canvass Status Column<br>        i. Should be implemented in Canvass Creation, Canvass Viewing, Approver View of Canvass</td><td class="s37" rowspan="26"></td><td class="s34">Not Started</td><td class="s10"></td><td class="s37" rowspan="26"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R156" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">157</div></th><td class="s6">PRS-1390-002</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s38">High</td><td class="s6">Verify that Canvass Status column removed from Items Table in Canvass Creation, Viewing, and Approver View</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Go to Canvass Creation, Canvass Viewing, Approver View of Canvass<br>2. Check Canvass Status Column</td><td class="s6">2. Canvass Status Column<br>        i. Should be implemented in Canvass Creation, Canvass Viewing, Approver View of Canvass</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R157" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">158</div></th><td class="s6">PRS-1390-003</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify that full item names are displayed in the Item column</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Navigate to Items Table in Canvass Sheet<br>2. Check the Item Name displayed in the Item Column</td><td class="s6">2. Should display the Full Item name in the Item Column</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R158" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">159</div></th><td class="s6">PRS-1390-004</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Critical</td><td class="s6">Verify the presence of Item Name, Unit, Requested Qty, Approved Qty, Remaining GFQ, and Actions</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Navigate to Items Table in Canvass Sheet<br>2. Observe all the columns in Items Table</td><td class="s6">2 Should have the following Columns:<br>    a. Item Name<br>    b. Unit<br>    c. Requested Qty<br>    d. Approved Quantity<br>    e. Remaining GFQ<br>    f. Actions<br>      </td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R159" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">160</div></th><td class="s6">PRS-1390-005</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Item Name column of the table is sortable (default, ascending, &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2 Observe sorting of Item Name<br>       a. Sort by 0-9, A-Z || 9-0, Z-A || Default<br></td><td class="s6">2. Item Name sorts accordingly<br>       a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R160" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">161</div></th><td class="s6">PRS-1390-006</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Unit column of the table is sortable (default, ascending, &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2 Observe sorting of Unit<br>    ii. Unit<br>       a. Sort by A-Z || Z-A || Default<br></td><td class="s6">2. Unit sorts accordingly:<br>    ii. Unit<br>       a.Should be sorted by A-Z || Z-A || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R161" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">162</div></th><td class="s6">PRS-1390-007</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Requested Qty column of the table is sortable (default, ascending, &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2  Observe sorting of Requested Qty<br>    iii. Requested Qty<br>       a.Sort by 0-9, 9-0 || Default<br></td><td class="s6">2. Requested Qty sorts accordingly:<br>    iii. Requested Qty<br>       a.Should be sorted by 0-9, 9-0 || Default<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R162" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">163</div></th><td class="s6">PRS-1390-008</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Approved Quantity of the table is sortable (default, ascending, &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2 Observe sorting of Approved Quantity<br>    iv. Approved Quantity<br>       a.Sort by 0-9, 9-0 || Default<br></td><td class="s6">2. Approved Quantity sorts accordingly:<br>    iv. Approved Quantity<br>       a.Should be sorted by 0-9, 9-0 || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R163" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">164</div></th><td class="s6">PRS-1390-009</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Remaining GFQ column of the table is sortable (default, ascending, &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe sorting of Approved Quantity<br>    v. Remaining GFQ<br>        a.Sort by 0-9, 9-0 || Default<br></td><td class="s6">2. Remaining GFQ sorts accordingly:<br>    v. Remaining GFQ<br>        a.Should be sorted by 0-9, 9-0 || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R164" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">165</div></th><td class="s6">PRS-1390-010</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that Approved Quantity shows &quot;---&quot; when not yet approved</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Navigate to Items Table in Canvass Sheet<br>2. Check Approved Quantity column</td><td class="s6">2. Should display as &quot;---&quot; if not yet Approved by any of the Approvers</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R165" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">166</div></th><td class="s6">PRS-1390-011</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Mnor</td><td class="s6">Verify Remaining GFQ column shows &quot;---&quot; for Non-OFM and Non-OFM Transfer of Materials request types</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Navigate to Items Table in Canvass Sheet<br>2. Check Remaining GFQ Column</td><td class="s6">2.  For OFM and OFM Transfer of Materials Type of Request<br>      ii. Should display as &quot;---&quot; fo Non-OFM and Non-OFM Transfer of Materials</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R166" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">167</div></th><td class="s6">PRS-1390-012</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Citical</td><td class="s6">Verify Enter Canvass button displays Enter Canvass Modal if there are no suppliers</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click &quot;Enter Canvass&quot; Button under Actions for Item without Suppliers yet<br>2. Validate display of Enter Canvass Modal</td><td class="s6">2. If Enter Canvass Button is clicked<br>    a) If without Suppliers yet, should display Enter Canvass Modal</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R167" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">168</div></th><td class="s6">PRS-1390-013</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Critical</td><td class="s6">Verify Enter Canvass button displays View Modal if suppliers already exist</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click &quot;Enter Canvass&quot; Button under Actions for Item with Suppliers<br>2. Validate display View Modal that will allow Editing of the Suppliers</td><td class="s6">2. If Enter Canvass Button is clicked<br>    b) If with Suppliers, should display View Modal that will allow Editing of the Suppliers</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R168" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">169</div></th><td class="s6">PRS-1390-014</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify that clicking &quot;View Purchase History&quot; opens a modal with correct data</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click &quot;View Purchase History&quot;<br>2. Check modal column fields</td><td class="s6"> 2. If View Purchase History is clicked<br>         a) Should display a Modal that contains the following<br>                   1) RS Number<br>                        a. RS Numbers that has used the Item in their Requests<br>                   2) Supplier<br>                        a. Supplier of the Item per Requisition Slip<br>                   3) Price Per Unit<br>                        a. Price declared for the Item<br>                   4) Qty<br>                        a. Requested Quantity for the Item<br>                   5) Date Purchased<br>                        a1. Date that the Pruchase Order for the Item has been Approved<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R169" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">170</div></th><td class="s6">PRS-1390-015</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify RS Number column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if RS Number column sorts accordingly:<br>    i) RS Number<br>        a. Sort by 0-9, 9-0 || Default </td><td class="s6">2. RS Number column sorts accordingly:<br>    i) RS Number<br>        a. Should be sorted by 0-9, 9-0 || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R170" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">171</div></th><td class="s6">PRS-1390-016</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Supplier column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Supplier sorts accordingly:<br>    ii) Supplier<br>        a. Sort by 0-9, A-Z || 9-0, Z-A || Default </td><td class="s6">2. Supplier column sorts accordingly:<br>    ii) Supplier<br>        a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R171" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">172</div></th><td class="s6">PRS-1390-017</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Price Per Unit column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Price Per Unit column sorts accordingly:<br>    iii) Price Per Unit<br>        a. Sort by 0-9, 9-0 || Default</td><td class="s6">2. Price Per Unit column sorts accordingly:<br>    iii) Price Per Unit<br>        a. Should be sorted by 0-9, 9-0 || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R172" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">173</div></th><td class="s6">PRS-1390-018</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Qty column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Qty column sorts accordingly:<br>    iv) Qty<br>        a. Sort by 0-9, 9-0 || Default</td><td class="s6">2. Qty column sorts accordingly:<br>    iv) Qty<br>        a. Should be sorted by 0-9, 9-0 || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R173" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">174</div></th><td class="s6">PRS-1390-019</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify Date Purchased column inside Purchase History modal is sortable (default, ascending &amp; descending)</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click the following column headers arrow buttons<br>2. Observe if Date Purchased column sorts accordingly:<br>    v) Date Purchased<br>       a. Sort by Oldest Date-Latest Date, Latest Date-Oldest Date || Default</td><td class="s6">2. Date Purchased column sorts accordingly:<br>    v) Date Purchased<br>       a. Should be sorted by Oldest Date-Latest Date, Latest Date-Oldest Date || Default</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R174" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">175</div></th><td class="s6">PRS-1390-020</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that &quot;---&quot; is displayed for no Supplier</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Observe no values for Supplier<br>2. Validate values can be displayed as &quot;---&quot;</td><td class="s6">2. Values can be displayed as<br>    ii) Supplier<br>        a. Can be displayed as &quot;---&quot;<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R175" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">176</div></th><td class="s6">PRS-1390-021</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that &quot;---&quot; is displayed for no Price Per Unit</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Observe no values for Price per Unit<br>2. Validate values can be displayed as &quot;---&quot;</td><td class="s6">2. Values can be displayed as<br>    iii) Price Per Unit<br>        a. Can be displayed as &quot;---&quot;<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R176" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">177</div></th><td class="s6">PRS-1390-022</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that &quot;---&quot; is displayed for no Date Purchased values</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Observe no values for Date Purchased<br>2. Validate values can be displayed as &quot;---&quot;</td><td class="s6">2. Values can be displayed as<br>    v) Date Purchased<br>       a. Can be displayed as &quot;---&quot;<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R177" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">178</div></th><td class="s6">PRS-1390-023</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify only 10 rows of data are shown per page in Purchase History modal</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Open Purchase History modal<br>2. Validate pagination</td><td class="s6">2. Should display 10 Rows of Data per Page</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R178" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">179</div></th><td class="s6">PRS-1390-024</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify that changes to the Items Table are also applied to Steelbars</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Open Canvass with Steelbar items<br>2. Verify columns and behavior</td><td class="s6">2. Should implement the changes to Steelbars<br></td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s10"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R179" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">180</div></th><td class="s6">PRS-1390-025</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify clicking the whole row expands Supplier Summary View</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click any item row<br>2. Check if it expands and shows Suppliers Summary View</td><td class="s6">2. Should click the whole row for the Item to expand the Suppliers Summary View</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R180" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">181</div></th><td class="s6">PRS-1390-026</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">High</td><td class="s6">Verify that supplier names are shown instead of generic labels in Supplier Summary View</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Click any item row<br>2. Check if Supplier&#39;s Names are displayed instead of Supplier 1, etc.</td><td class="s6">2. Should display the Supplier&#39;s Names instead of Supplier 1, etc.</td><td class="s34">Not Started</td><td class="s10"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R181" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">182</div></th><td class="s6">PRS-1390-027</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that the Items Table adjusts its spacing for single item</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Open RS or Enter Canvass Sheet with one item<br>2. Observe table spacing</td><td class="s6">2. Should view the Requisition Slip, and Enter a Canvass Sheet<br>     a. Observe the current Items Table, even if the Requested Item is only one Item, the Spacing on the Items Table is using too much space</td><td class="s37"></td><td class="s34">Not Started</td><td class="s10"></td><td class="s37"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1864623824R182" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">183</div></th><td class="s6">PRS-1390-028</td><td class="s6">Update Table Behavior for Canvass Sheet[for Create and Viewing]</td><td class="s7">Minor</td><td class="s6">Verify that the Items Table adjusts properly for multiple items</td><td class="s6">1. Logged in as Assigned Purchasing Staff<br>2. Requisition Slip is approved and Assigned</td><td class="s6">1. Open RS or Enter Canvass Sheet with multiple items<br>2. Observe table spacing</td><td class="s6">2. Should adjust the Items Table sizing depending on the selected Number of Items</td><td class="s37"></td><td class="s34">Not Started</td><td class="s10"></td><td class="s37"></td><td class="s10"></td><td class="s10"></td><td class="s37"></td><td class="s37"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr></tbody></table></div>