site_name: PRS Developer Documentation
site_description: Comprehensive documentation for the PRS system
site_author: Stratpoint Team
site_url: https://gitlab.stratpoint.dev/prs-documentation

# Copyright
copyright: Copyright &copy; 2025 Stratpoint

# Configuration
theme:
  name: material
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.top
    - navigation.tracking
    - navigation.indexes
    - navigation.expand
    - search.highlight
    - search.share
    - search.suggest
    - content.code.copy
    - content.tabs.link
    - toc.follow
    - toc.integrate
  palette:
    # Dark mode as default (no media query)
    - scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: Switch to light mode
    # Light mode as secondary option
    - scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: Switch to dark mode
  font:
    text: Work Sans
    code: Roboto Mono
  icon:
    repo: fontawesome/brands/github
    admonition:
      note: octicons/tag-16
      abstract: octicons/checklist-16
      info: octicons/info-16
      tip: octicons/squirrel-16
      success: octicons/check-16
      question: octicons/question-16
      warning: octicons/alert-16
      failure: octicons/x-circle-16
      danger: octicons/zap-16
      bug: octicons/bug-16
      example: octicons/beaker-16
      quote: octicons/quote-16
  # Custom CSS and JavaScript
  custom_dir: docs/overrides
  extra_css:
    - stylesheets/extra.css
    - stylesheets/sidebar.css
  extra_javascript:
    - javascripts/extra.js

# Extensions
markdown_extensions:
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
      linenums: true
      linenums_style: table
      use_pygments: true
      auto_title: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg

# Plugins
plugins:
  - search
  - glightbox
  - mermaid2

# Exclude patterns
exclude_docs: |
  refactoring-guide/README.md
  system-guide/README.md
  system-guide/workflows/README.md

# Navigation structure
nav:
  - Home: index.md
  - System Guide:
    - Overview: system-guide/index.md
    - Architecture:
      - Backend Overview: system-guide/architecture/overview.md
      - Frontend Overview: system-guide/architecture/frontend-overview.md
      - Diagrams: system-guide/architecture/diagrams.md
    - Business Rules:
      - Overview: system-guide/business-rules/overview.md
      - Requisition: system-guide/business-rules/requisition.md
      - Canvass: system-guide/business-rules/canvass.md
      - Purchase Order: system-guide/business-rules/purchase-order.md
      - Delivery Report: system-guide/business-rules/delivery.md
      - Invoice Report: system-guide/business-rules/invoice.md
      - Payment Request: system-guide/business-rules/payment.md
    - Development Guide:
      - Backend Getting Started: system-guide/development-guide/getting-started.md
      - Frontend Getting Started: system-guide/development-guide/frontend-getting-started.md
      - Local Production Setup: system-guide/development-guide/local-production-setup.md
    - Workflows:
      - Overview: system-guide/workflows/index.md
      - Frontend Implementation: system-guide/workflows/frontend-implementation.md
      - Frontend-Backend Alignment: system-guide/workflows/frontend-backend-alignment.md
      - Status Transition Matrix: system-guide/workflows/status-transition-matrix.md
      - API Contract: system-guide/workflows/api-contract.md
      - Improving Alignment: system-guide/workflows/improving-alignment.md
      - Frontend-Backend Workflow Alignment: system-guide/workflows/frontend-backend-workflow-alignment.md
      - Requisition: system-guide/workflows/requisition-workflow.md
      - Canvass: system-guide/workflows/canvass-workflow.md
      - Purchase Order: system-guide/workflows/purchase-order-workflow.md
      - Delivery Report: system-guide/workflows/delivery-workflow.md
      - Invoice Report: system-guide/workflows/invoice-workflow.md
      - Payment Request: system-guide/workflows/payment-workflow.md
      - Non-RS Payment Request: system-guide/workflows/non-requisition-workflow.md
      - Approval: system-guide/workflows/approval-workflow.md
      - Notification: system-guide/workflows/notification-workflow.md
      - Attachment: system-guide/workflows/attachment-workflow.md
      - Company Management: system-guide/workflows/company-workflow.md
      - Department Management: system-guide/workflows/department-workflow.md
      - Project Management: system-guide/workflows/project-workflow.md
      - Item Management: system-guide/workflows/items-workflow.md
      - Item Management Workflow: system-guide/workflows/item-management-workflow.md
      - Master Data Workflow: system-guide/workflows/master-data-workflow.md
      - Supplier Management: system-guide/workflows/supplier-workflow.md
      - User Management: system-guide/workflows/user-management-workflow.md
      - Reporting: system-guide/workflows/reporting-workflow.md
    - Templates:
      - Controller: system-guide/templates/controller.md
      - Entity: system-guide/templates/entity.md
      - Repository: system-guide/templates/repository.md
      - Service: system-guide/templates/service.md
      - Test: system-guide/templates/test.md
  - How-To Guides:
    - Implement New Feature: how-to/implement-new-feature.md
    - API Documentation: how-to/api-documentation.md
    - Base Classes: how-to/base-classes.md
    - Error Handling: how-to/error-handling.md
    - Layered Architecture: how-to/layered-architecture.md
    - Middleware Composition: how-to/middleware-composition.md
    - Backend Structured Logging: how-to/structured-logging.md
    - Frontend Structured Logging: how-to/frontend-structured-logging.md
  - Refactoring Guide:
    - Overview: refactoring-guide/index.md
    - Current Architecture: refactoring-guide/01-current-architecture.md
    - Bounded Contexts: refactoring-guide/02-bounded-contexts.md
    - Domain Model: refactoring-guide/03-domain-model.md
    - Repository Pattern: refactoring-guide/04-repository-pattern.md
    - Application Services: refactoring-guide/05-application-services.md
    - Domain Events: refactoring-guide/06-domain-events.md
    - Implementation Strategy: refactoring-guide/07-implementation-strategy.md
    - DDD Refactoring Strategy: refactoring-guide/ddd-refactoring-strategy.md
    - Practical DDD Refactoring: refactoring-guide/practical-ddd-refactoring.md
    - Simple DDD Approach: refactoring-guide/simple-ddd-approach.md
    - DDD Guide: refactoring-guide/ddd-guide.md
  - Enhancement:
    - Code Quality Improvements: enhancement/code-quality-improvements.md
  - Standards & Conventions:
    - Coding Standards: system-guide/development-guide/coding-standards.md
    - Status Naming Convention: development-guide/status-naming-convention.md
    - Status Standardization: development-guide/status-standardization.md
    - Status Standardization PR Template: development-guide/status-standardization-pr-template.md
