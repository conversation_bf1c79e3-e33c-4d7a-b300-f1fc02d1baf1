#!/bin/bash

# Fix Frontend Deployment Script
# This script rebuilds the frontend container and restarts the services

set -e

echo "🔧 Fixing frontend deployment issue..."

# Navigate to deployment directory
cd "$(dirname "$0")"

echo "📦 Stopping current containers..."
docker compose down

echo "🏗️  Rebuilding frontend container..."
docker compose build frontend --no-cache

echo "🚀 Starting all containers..."
docker compose up -d

echo "⏳ Waiting for services to start..."
sleep 10

echo "🔍 Checking container status..."
docker compose ps

echo "📋 Checking frontend logs..."
docker compose logs frontend --tail=20

echo "🌐 Testing frontend health..."
if docker compose exec frontend curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ Frontend health check passed"
else
    echo "❌ Frontend health check failed"
fi

echo "🔗 Testing nginx proxy..."
if docker compose exec nginx curl -f http://frontend:80/health > /dev/null 2>&1; then
    echo "✅ Nginx proxy to frontend working"
else
    echo "❌ Nginx proxy to frontend failed"
fi

echo "📊 Container resource usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo ""
echo "🎉 Frontend deployment fix completed!"
echo ""
echo "📝 Next steps:"
echo "1. Test the application at https://prs.stratpoint.io"
echo "2. Check browser console for any remaining errors"
echo "3. If issues persist, check logs with: docker compose logs frontend"
echo ""
