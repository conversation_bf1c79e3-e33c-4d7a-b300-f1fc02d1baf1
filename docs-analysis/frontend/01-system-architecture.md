# System Architecture

## Overview

The PRS-Frontend is built using modern React practices and follows a feature-based architecture. This document provides a detailed analysis of the system's architecture, including its structure, key technologies, and architectural patterns.

## Technology Stack

The PRS-Frontend is built with the following technologies:

- **React**: Core UI library
- **Vite**: Build tool and development server
- **React Router**: Client-side routing
- **Zustand**: State management
- **React Query (TanStack Query)**: Data fetching and caching
- **Axios**: HTTP client
- **Zod**: Schema validation
- **Tailwind CSS**: Utility-first CSS framework
- **React Error Boundary**: Error handling
- **React Helmet**: Document head management
- **React Toastify**: Toast notifications

## Project Structure

The project follows a feature-based architecture with the following directory structure:

```
prs-frontend/
├── public/                # Static assets
├── src/
│   ├── app/               # Application core
│   │   ├── provider.jsx   # App providers
│   │   ├── router.jsx     # Router configuration
│   │   └── routes/        # Route components
│   ├── assets/            # Static assets (images, icons)
│   ├── components/        # Shared UI components
│   │   ├── errors/        # Error components
│   │   ├── layouts/       # Layout components
│   │   └── ui/            # UI components
│   ├── config/            # Configuration files
│   ├── contexts/          # React contexts
│   ├── features/          # Feature modules
│   │   ├── auth/          # Authentication feature
│   │   ├── dashboard/     # Dashboard feature
│   │   ├── company/       # Company management feature
│   │   └── ...            # Other features
│   ├── helpers/           # Helper functions
│   ├── hooks/             # Custom hooks
│   ├── lib/               # Library code
│   │   ├── apiClient.js   # API client configuration
│   │   ├── auth.jsx       # Authentication utilities
│   │   └── reactQuery.js  # React Query configuration
│   ├── schema/            # Validation schemas
│   ├── store/             # State management
│   └── utils/             # Utility functions
├── index.html             # HTML entry point
└── main.jsx               # JavaScript entry point
```

## Architectural Patterns

### Feature-Based Architecture

The application is organized around features rather than technical concerns. Each feature module contains all the necessary components, API calls, and business logic related to that feature.

```
features/
├── auth/                  # Authentication feature
│   ├── api/               # API calls
│   ├── components/        # Feature-specific components
│   └── hooks/             # Feature-specific hooks
├── dashboard/             # Dashboard feature
│   ├── api/               # API calls
│   ├── components/        # Feature-specific components
│   └── hooks/             # Feature-specific hooks
```

This approach:
- Improves code organization and discoverability
- Enables better code splitting and lazy loading
- Makes it easier to understand and maintain features

### Component Composition

The application uses component composition to build complex UIs from smaller, reusable components:

```jsx
// Example of component composition
export const AppProvider = ({ children }) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: queryConfig,
      }),
  );

  return (
    <Suspense fallback={<Spinner size="xl" />}>
      <ErrorBoundary FallbackComponent={MainErrorFallback}>
        <HelmetProvider>
          <QueryClientProvider client={queryClient}>
            <AuthLoader renderLoading={() => <Spinner size="xl" />}>
              {children}
              <ToastContainer theme="colored" />
            </AuthLoader>
          </QueryClientProvider>
        </HelmetProvider>
      </ErrorBoundary>
    </Suspense>
  );
};
```

### State Management

The application uses Zustand for state management, with stores organized by domain:

```javascript
// Example of a Zustand store
const useUserStore = create(
  persist(
    set => ({
      user: null,
      otp: null,
      secret: null,
      currentRoute: null,
      setUser: user => set({ user }),
      setOTP: ({ otp, secret }) => set({ otp, secret }),
      setCurrentRoute: route => set({ currentRoute: route }),
      removeUser: () => set({ user: null }),
      removeOtp: () => set({ secret: null, otp: null }),
      removeAll: () =>
        set({ user: null, otp: null, secret: null, currentRoute: null }),
    }),
    {
      name: 'user-storage',
    },
  ),
);
```

### Data Fetching

The application uses React Query for data fetching, caching, and synchronization:

```javascript
// Example of a React Query hook
export const useGetCompanies = (params, config = {}) => {
  return useQuery({
    queryKey: ['companies', params],
    queryFn: () => getCompanies(params),
    ...config,
  });
};
```

### Routing

The application uses React Router for client-side routing, with routes organized by feature:

```javascript
// Example of route configuration
export const createAppRouter = (queryClient, availableRoutes) =>
  createBrowserRouter([
    {
      path: '/',
      lazy: async () => {
        const { LandingRoute } = await import('./routes/Landing');
        return { Component: LandingRoute };
      },
    },
    {
      path: '/app',
      element: (
        <ProtectedRoute>
          <AppRoot />
        </ProtectedRoute>
      ),
      children: [...availableRoutes],
    },
    // ... other routes
  ]);
```

## Application Flow

### Initialization Flow

1. The application starts at `main.jsx`, which renders the `App` component
2. The `App` component renders the `AppProvider` and `AppRouter`
3. The `AppProvider` sets up the application context and providers
4. The `AppRouter` configures the routing based on user permissions
5. The `AuthLoader` checks if the user is authenticated and loads user data

```jsx
// main.jsx
createRoot(root).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);

// app/index.jsx
export const App = () => {
  return (
    <AppProvider>
      <AppRouter />
    </AppProvider>
  );
};
```

### Authentication Flow

1. User enters credentials on the login page
2. The application sends a login request to the API
3. If successful, the API returns tokens and user information
4. The application stores the tokens and user information in Zustand stores
5. The application redirects the user to the appropriate page based on the response

```jsx
// Example of login flow
const handleLoginSubmit = async values => {
  try {
    const routeMap = {
      requireUpdatePassword: '/auth/create-password',
      requireOTPSetup: '/auth/setup-otp',
      requireOTPVerification: '/auth/verify-otp',
    };

    const response = await login.mutateAsync(values);
    const nextRoute = Object.keys(routeMap).find(key => response[key]);
    const { accessToken } = response;

    setToken(accessToken);
    setCurrentRoute(nextRoute);

    if (accessToken && !nextRoute) {
      const { refreshToken, expiredAt } = response;
      setRefreshToken({ refreshToken, expiredAt });
      setType('auth');
      navigate('/', { replace: true });
    }

    if (nextRoute === 'requireOTPSetup') {
      const { otpAuthUrl, otpSecret, accessToken } = response;
      setOTP({ otp: otpAuthUrl, secret: otpSecret });
      setToken(accessToken);
    }
  } catch (error) {
    // Error handling
  }
};
```

### Data Flow

1. Components request data using React Query hooks
2. React Query checks if the data is in the cache
3. If not, it fetches the data from the API
4. The API client adds authentication headers to the request
5. The API returns the data, which is cached by React Query
6. The component receives the data and renders it

```jsx
// Example of data flow
const { data: companies, isLoading } = useGetCompanies({
  page,
  limit,
  paginate: true,
  filterBy,
  sortBy,
});

if (isLoading) {
  return <Spinner />;
}

return (
  <Table data={companies.data} />
);
```

## Architectural Strengths

1. **Feature-Based Organization**: The code is organized around features, making it easier to understand and maintain.
2. **Component Composition**: The application uses component composition to build complex UIs from smaller, reusable components.
3. **State Management**: Zustand provides a simple and effective state management solution with persistence.
4. **Data Fetching**: React Query simplifies data fetching, caching, and synchronization.
5. **Code Splitting**: The application uses dynamic imports and lazy loading to improve performance.
6. **Type Safety**: Zod provides runtime type checking and validation.

## Architectural Weaknesses

1. **Inconsistent Feature Organization**: Some features are not fully encapsulated, with logic spread across multiple directories.
2. **Prop Drilling**: Some components pass props through multiple levels of the component tree.
3. **Duplicate Code**: There is some duplication of code across features, particularly in API calls and form handling.
4. **Complex Component Logic**: Some components contain complex business logic that could be extracted to custom hooks or services.
5. **Limited Test Coverage**: The application appears to have limited test coverage.

## Recommendations

1. **Consistent Feature Organization**: Ensure all feature-related code is encapsulated within the feature directory.
2. **Extract Complex Logic**: Move complex business logic from components to custom hooks or services.
3. **Reduce Prop Drilling**: Use context or state management for deeply nested component trees.
4. **Improve Code Reuse**: Create more shared utilities and hooks to reduce duplication.
5. **Increase Test Coverage**: Add unit and integration tests for critical components and business logic.

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
