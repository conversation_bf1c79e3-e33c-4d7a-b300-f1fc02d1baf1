const { z } = require('zod');
const { createIdParamsSchema, createNumberSchema } = require('../../app/utils');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');

const createDeliveryReceiptSchema = z
  .object({
    requisitionId: createIdParamsSchema('Requisition ID'),
    poId: createIdParamsSchema('Purchase Order ID'),
    supplier: z.string(),
    isDraft: z.string().refine((val) => val === 'true' || val === 'false', {
      message: 'isDraft must be a string that is either "true" or "false"',
    }),
    note: z
      .string()
      .max(500, 'Delivery report note must not exceed 500 characters')
      .regex(
        /^$|^[a-zA-Z0-9Ññ!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\/?`~ \n]+$/,
        'Notes can only contain letters, numbers, spaces and special characters',
      ).optional(),
    invoiceNumber: z.string().optional(),
    supplierDeliveryIssuedDate: z.string().date().optional(),
    issuedDate: z.string().date(),
    items: z.array(
      z
        .object({
          poItemId: z.number({ required_error: 'PO Item ID is required' }),
          itemId: z.number({ required_error: 'Item ID is required' }),
          accountCode: z.string().optional(),
          poId: createIdParamsSchema('Purchase Order ID'),
          itemDes: z.string(),
          qtyOrdered: z
            .number()
            .min(1, 'Quantity ordered must be more than 1')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyDelivered: z
            .number()
            .min(0, 'Quantity delivered must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyReturned: z
            .number()
            .min(0, 'Quantity returned must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          dateDelivered: z.string(),
          unit: z.string(),
          notes: z
            .string()
            .max(500, 'Delivery item note must not exceed 500 characters')
            .regex(
              /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
              'Delivery item note contains invalid characters.',
            ).optional(),
        })
        .strict()
        .refine((data) => data.qtyDelivered <= data.qtyOrdered, {
          message:
            'Quantity delivered must be less than or equal to the quantity ordered.',
        })
        .refine(
          (data) => data.qtyReturned <= data.qtyOrdered - data.qtyDelivered,
          {
            message: 'Quantity returned is invalid.',
          },
        ),
    ),
    attachmentIds: z.array(createNumberSchema('Attachment ID')).optional(),
  })
  .strict()
  .refine((data) => data.items.every((item) => item.poId == data.poId), {
    message: "Each item's poId must match the main poId.",
    path: ['items'],
  });

const getDeliveryReceiptByIdSchema = z.object({
  id: createIdParamsSchema('Delivery report ID'),
});

const updateDeliveryReceiptSchema = z
  .object({
    requisitionId: createIdParamsSchema('Requisition ID'),
    poId: createIdParamsSchema('Purchase Order ID'),
    supplier: z.string(),
    isDraft: z.string().refine((val) => val === 'true' || val === 'false', {
      message: 'isDraft must be a string that is either "true" or "false"',
    }),
    note: z
      .string()
      .max(500, 'Delivery report note must not exceed 500 characters')
      .regex(
        /^$|^[a-zA-Z0-9Ññ!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\/?`~ \n]+$/,
        'Notes can only contain letters, numbers, spaces and special characters',
      ).optional(),
    invoiceNumber: z.string().optional(),
    supplierDeliveryIssuedDate: z.string().date().optional(),
    issuedDate: z.string().date(),
    items: z.array(
      z
        .object({
          id: z.number({
            required_error: 'ID for delivery item record is required',
          }),
          poItemId: z.number({ required_error: 'PO Item ID is required' }),
          itemId: z.number({ required_error: 'Item ID is required' }),
          accountCode: z.string().optional(),
          poId: createIdParamsSchema('Purchase Order ID'),
          itemDes: z.string(),
          qtyOrdered: z
            .number()
            .min(1, 'Quantity ordered must be more than 1')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyDelivered: z
            .number()
            .min(0, 'Quantity delivered must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyReturned: z
            .number()
            .min(0, 'Quantity returned must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          dateDelivered: z.string(),
          unit: z.string(),
          notes: z
            .string()
            .max(500, 'Delivery item note must not exceed 500 characters')
            .regex(
              /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
              'Delivery item note contains invalid characters.',
            ).optional(),
        })
        .strict()
        .refine((data) => data.qtyDelivered <= data.qtyOrdered, {
          message:
            'Quantity delivered must be less than or equal to the quantity ordered.',
        })
        .refine(
          (data) => data.qtyReturned <= data.qtyOrdered - data.qtyDelivered,
          {
            message: 'Quantity returned is invalid.',
          },
        ),
    ),
    currentAttachments: z
      .array(
        z
          .object({
            id: createIdParamsSchema('Attachment ID'),
            modelId: createIdParamsSchema('Model ID'),
            fileName: z.string(),
          })
          .strict(),
      )
      .optional(),
    attachmentIds: z.array(createNumberSchema('Attachment ID')).optional(),
  })
  .strict()
  .refine((data) => data.items.every((item) => item.poId == data.poId), {
    message: "Each item's poId must match the main poId.",
    path: ['items'],
  })
  .refine((data) => data.items.every((item) => item.poId == data.poId), {
    message: "Each item's poId must match the main poId.",
    path: ['items'],
  });

const getDeliveryReceiptsFromRequisitionParamsSchema = z.object({
  requisitionId: createIdParamsSchema('Requisition ID'),
});

const getDeliveryReceiptsFromRequisitionQuerySchema = z.object({
  search: z.string().optional(),
  sortBy: sortSchema(sort.DELIVERY_RECEIPT_SORT_COLUMNS).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const getDeliveryReceiptItemsQuerySchema = z
  .object({
    sortBy: sortSchema(sort.DELIVERY_RECEIPT_ITEMS_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    paginate: z.union([z.boolean(), z.enum(['true', 'false'])]).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

const getDeliveryReceiptsFromPurchaseOrderQuerySchema = z
  .object({
    noInvoice: z.string()
      .refine((val) => val === 'true' || val === 'false', {
        message: 'noInvoice must be a string that is either "true" or "false"',
      })
      .optional(),
  })
  .strict();

module.exports = {
  createDeliveryReceiptSchema,
  getDeliveryReceiptByIdSchema,
  updateDeliveryReceiptSchema,
  getDeliveryReceiptsFromRequisitionParamsSchema,
  getDeliveryReceiptsFromRequisitionQuerySchema,
  getDeliveryReceiptItemsQuerySchema,
  getDeliveryReceiptsFromPurchaseOrderQuerySchema,
};
