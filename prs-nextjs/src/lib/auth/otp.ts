import * as OTPAuth from 'otpauth';

export function generateOTPSecret(): string {
  const totp = new OTPAuth.TOTP({
    issuer: 'PRS',
    label: 'PRS Authentication',
    algorithm: 'SHA1',
    digits: 6,
    period: 30,
  });
  
  return totp.secret.base32;
}

export async function verifyOTP(
  token: string,
  secret: string
): Promise<boolean> {
  try {
    const totp = new OTPAuth.TOTP({
      issuer: 'PRS',
      label: 'PRS Authentication',
      algorithm: 'SHA1',
      digits: 6,
      period: 30,
      secret
    });
    
    // Delta of 1 allows for a 30-second window on either side of the current time
    const delta = totp.validate({ token, window: 1 });
    
    return delta !== null;
  } catch (error) {
    console.error('OTP verification error:', error);
    return false;
  }
}
