<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="2089059147C0" style="width:103px;" class="column-headers-background">A</th><th id="2089059147C1" style="width:167px;" class="column-headers-background">B</th><th id="2089059147C2" style="width:252px;" class="column-headers-background">C</th><th id="2089059147C3" style="width:252px;" class="column-headers-background">D</th><th id="2089059147C4" style="width:233px;" class="column-headers-background">E</th><th id="2089059147C5" style="width:330px;" class="column-headers-background">F</th><th id="2089059147C6" style="width:184px;" class="column-headers-background">G</th><th id="2089059147C7" style="width:298px;" class="column-headers-background">H</th><th id="2089059147C8" style="width:100px;" class="column-headers-background">I</th><th id="2089059147C9" style="width:114px;" class="column-headers-background">J</th><th id="2089059147C10" style="width:114px;" class="column-headers-background">K</th><th id="2089059147C11" style="width:114px;" class="column-headers-background">L</th><th id="2089059147C12" style="width:114px;" class="column-headers-background">M</th><th id="2089059147C13" style="width:114px;" class="column-headers-background">N</th><th id="2089059147C14" style="width:78px;" class="column-headers-background">O</th><th id="2089059147C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="2089059147R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s2">STG_wk4+5</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="2089059147R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-022 - [Delivery Receipt] Creation of Delivery Receipt per Supplier, Entering of Invoice in the Delivery Receipt, Viewing of Delivery Receipt per Supplier</td></tr><tr style="height: 19px"><th id="2089059147R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-022-001</td><td class="s5">Creation of Delivery Receipt per Supplier</td><td class="s5">Critical</td><td class="s5">Verify Creation of Delivery Receipt per Supplier in Purchase Order Section</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Check Purchase Details Section<br>6. Validate Purchase Order field <br>7. Validate Supplier Field <br>8. Validate Invoice button<br>9. Validate Attachments <br>10. Validate Notes field</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5.  Should display section for Purchase Details<br>    a. Purchase Order Number<br>    b. Supplier<br>    c. Should have Button for Invoice<br>    d. Attachments and Notes<br>6. Should display a Drop-down for Purchase Order Number field<br>        i. Should display the Values of Purchase Orders Produced in the Requisition Slip<br>           I) Should check if the Purchase Order has an existing Delivery Receipt, may it be Draft or Submitted<br>              a) If with existing, should not display in the List of PO Numbers<br>7. Supplier field should get the Supplier indicated in the Purchase Order<br>8. Should have clickable Button for Invoice<br>9. Uploading of Attachment should met the ff;<br>           i) Should allow Maximum File Size of 25MB per File<br>           ii) Should be able to upload File Types of PDF, Doc, JPG, JPEG, PNG, CSV, Excel<br>           iii)  Should click Submit Button to Confirm submission of Files<br>10. Adding of Notes should met the ff:<br>           i) Should Alphanumeric and Special Characters except Emojis<br>           ii) Should allow maximum of 100 Characters<br>           iii) Should click Submit Button to Confirm adding of Note</td><td class="s7" rowspan="25"></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10" rowspan="25"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=588139293#gid=588139293">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=588139293#gid=588139293</a></td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: issue with notes that accepts emoji</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="2089059147R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-022-002</td><td class="s5">Creation of Delivery Receipt per Supplier</td><td class="s5">Critical</td><td class="s5">Verify Creation of Delivery Receipt per Supplier in Items Section for items  thas has no steelbars</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Check Items Section<br>6. Search an Item Name keyword<br>7. Search an Item name that is not existing on the table list<br>8. Click &quot;Clear&quot; Button on Items Section</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5.   Should display section for item details<br>    a. Search field and button<br>    b, Clear Button<br>    c. Should have Tabs for Items and Steelbars<br>         i. Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>         ii. Else display either the Table for other OFM Items or Table for Steelbars<br>     d. Should have Item Table with the following Columns<br>         i) Should display Columns for other OFM Items<br>            a) Item<br>            b) Quantity Ordered<br>            c) Quantity Delivered<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>            d) Unit<br>            e) Date Delivered<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>            f) Delivery Status<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>                     a1) ---<br>                     a2) Partially Delivered<br>                     a3) Partially Delivered w/ Return<br>                     a4) Fully Delivered<br>                     a5) Fullt Delivered w/ Return<br>            g) Notes<br>            h)  Actions<br>                 1) Edit<br>     e. Should have 10 Rows per Page<br>6. Should display matched Item even with Keyword only<br>7. Should display No Data if there is no available Data matching the Searched Item<br>8. Should clear the Search Item and reset the Table once Clear Button is clicked</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Unable to test pagination due to issue for creating RS with more than 10 items</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1036">3/8: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1036</a></td></tr><tr style="height: 19px"><th id="2089059147R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">PRS-022-003</td><td class="s5">Creation of Delivery Receipt per Supplier</td><td class="s5">Critical</td><td class="s5">Verify Creation of Delivery Receipt per Supplier in Items Section  for items  thas has a steelbars</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Check Items Section<br>6. Search an Item Name keyword<br>7. Search an Item name that is not existing on the table list<br>8. Click &quot;Clear&quot; Button on Items Section</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5.   Should display section for item details<br>    a. Search field and button<br>    b, Clear Button<br>    c. Should have Tabs for Items and Steelbars<br>         i. Should only display the Tabs if the Purchase Order consists of other OFM Items and Steelbars<br>         ii. Else display either the Table for other OFM Items or Table for Steelbars<br>     d. Should have Item Table with the following Columns<br>         i) Should display Columns for other OFM Items<br>            a) Item<br>            b) Quantity Ordered<br>            c) Quantity Delivered<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>            d) Unit<br>            e) Date Delivered<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>            f) Delivery Status<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>                     a1) ---<br>                     a2) Partially Delivered<br>                     a3) Partially Delivered w/ Return<br>                     a4) Fully Delivered<br>                     a5) Fullt Delivered w/ Return<br>            g) Notes<br>            h)  Actions<br>                 1) Edit<br>         ii) Should display Columns for Steelbars<br>            a) Item<br>            b) Quantity Ordered<br>            c) Quantity Delivered<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>            d) Unit<br>            e) Date Delivered<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>            f) Delivery Status<br>                1) Should display &quot;---&quot; if no Data has been indicated yet<br>                     a1) ---<br>                     a2) Partially Delivered<br>                     a3) Partially Delivered w/ Return<br>                     a4) Fully Delivered<br>                     a5) Fullt Delivered w/ Return<br>            g) Notes<br>            h)  Actions<br>                 1) Edit<br>     e. Should have 10 Rows per Page<br>6. Should display matched Item even with Keyword only<br>7. Should display No Data if there is no available Data matching the Searched Item<br>8. Should clear the Search Item and reset the Table once Clear Button is clicked</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s14"></td><td class="s7">3/10: Unable to test pagination due to issue for creating RS with more than 10 items</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1036">3/10: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1036<br>3/10: <br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1107<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1108</a></td></tr><tr style="height: 19px"><th id="2089059147R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">PRS-022-004</td><td class="s5">Creation of Delivery Receipt per Supplier</td><td class="s5">Critical</td><td class="s5">Verify Creation of Delivery Receipt per Supplier when clicked Edit Icon for the Item to enter the Delivery Details </td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Check Items Section<br>6. Click &quot;Edit&quot; icon<br>7. Check Item Modal edit details<br>8. Validate all fields in the modal<br>9. Click the &quot;Cancel&quot; button on the modal<br>10. Click &quot;Edit&quot; Icon again on the tems section in Actions column table list<br>11. Populate all fields<br>12. Clck &quot;Save&quot; button on the modal<br>13. Check if still allow to enter delivery details when quantity ordered and quantity delivered is not equal<br>14. Click &quot;&quot;History&quot; per item<br>15. Chek default Sorting</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5. Should display section for item table details<br>6. Should display a modal for the Item to enter the Delivery Details<br>7.  Should display a Modal with the following Fields and buttons:<br>        i. Item Name<br>        ii. Date Delivered<br>        iii. Qty Orderd<br>        iv. Qty Delivered<br>        v. Qty. for Return - if existing<br>        vi. Notes<br>     .  vii. Should have Save and Cancel Buttons<br>8.  Should met the validations for the ff fields:<br>        i. Item Name<br>          i) Non-editable Field, should display the Item selected for Edit<br>        ii. Date Delivered<br>          i) Should be Date Picker<br>          ii) Date should be the Current Date or less than the Current Date<br>        iii. Quantity Orderd<br>          i) Non-editable Field, should display the Item selected for Edit<br>        iv. Quantity Delivered<br>          i) Should have the maximum of the Quantity Ordered of the Items<br>             a) Numbers only, maximum of 3 Characters<br>                 1) Should validate if the entered value is equal or less than the Current Quantity Ordered<br>        v. Quantity For Return<br>          i.) Should display number of return if  delivery status is w/ return<br>        vi. Notes<br>          i) Should be a Text Area<br>          ii) Should have a maximum of 100 Characters<br>          iii) Should accept Alphanumeric and Special Characters except for Emojis<br>9. Should close the Modal and Return to Delivery Receipt Form<br>10. Should display a modal for the Item to enter the Delivery Details<br>11. Fields should be populated<br>12. If Save Button is clicked, Should met the ff:<br>           i) Should update the following in the Table<br>              a) Quantity Delivered<br>              b) Date Delivered<br>              c) Delivery Status<br>           ii) Should check if the entered Quantity Ordered and Quantity Delivered are equal<br>              a) If Equal<br>                  1) Should update the:<br>                      a.1) Delivery Status to Delivered<br>                      a.2) Edit Button should be enabled when the Delivery Receipt is not yet Submitted<br>           iii) Should record the Delivery Details in History<br>13. Should allow entering of Delivery Receipt per Item until the Quantity Ordered and Quantity Delivered became equal<br>14. Should View what were the Deliveries made to the Item and display a Modal with a Table of<br>           i. Quantity Ordered<br>           ii. Quantity Delivered<br>           iii. Quantity Returned<br>           iv. Date of Delivery<br>           v. Status<br>15. Should be sorted by Latest Date of Delivery</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: issue with notes that accepts emoji</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="2089059147R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">PRS-022-005</td><td class="s5">Creation of Delivery Receipt per Supplier</td><td class="s5">Critical</td><td class="s5">Verify Creation of Delivery Receipt per Supplier when clicked Save Draft first before submit</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Select a Supplier and Populate Purchase Order Section<br>6. Check Items Section<br>7. Click &quot;Edit&quot; eye icon<br>8. Check Item Modal edit details<br>9. Validate all fields in the modal<br>10. Click the &quot;Cancel&quot; button on the modal<br>11. Click &quot;Edit&quot; Icon again on the tems section in Actions column table list<br>12. Populate all fields<br>13. Clck &quot;Save&quot; button on the modal<br>14. Check if still allow to enter delivery details when quantity ordered and quantity delivered is not equal<br>15. Click &quot;Save draft&quot; on Delivery Receipt form<br>16. Click &quot;Submit&quot; button on Delivery Receipt form</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5.  Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should display section for item table details<br>7. Should display a modal for the Item to enter the Delivery Details<br>8.  Should display a Modal with the following Fields and buttons:<br>        i. Item Name<br>        ii. Date Delivered<br>        iii. Qty Orderd<br>        iv. Qty Delivered<br>        v. Qty for Return - if existing<br>        vi. Notes<br>     .  vii. Should have Save and Cancel Buttons<br>9.  Should met the validaations for the ff fields:<br>        i. Item Name<br>          i) Non-editable Field, should display the Item selected for Edit<br>        ii. Date Delivered<br>          i) Should be Date Picker<br>          ii) Date should be the Current Date or less than the Current Date<br>        iii. Quantity Orderd<br>          i) Non-editable Field, should display the Item selected for Edit<br>        iv. Quantity Delivered<br>          i) Should have the maximum of the Quantity Ordered of the Items<br>             a) Numbers only, maximum of 3 Characters<br>                 1) Should validate if the entered value is equal or less than the Current Quantity Ordered<br>        v. Quantity For Return<br>          i.) Should display number of return if  delivery status is w/ return</span><span style="font-family:Poppins,Arial;color:#ff0000;"><br></span><span style="font-family:Poppins,Arial;color:#000000;">        vi. Notes<br>          i) Should be a Text Area<br>          ii) Should have a maximum of 100 Characters<br>          iii) Should accept Alphanumeric and Special Characters except for Emojis<br>10. Should close the Modal and Return to Delivery Receipt Form<br>11. Should display a modal for the Item to enter the Delivery Details<br>12. Fields should be populated<br>13. If Save Button is clicked, Should met the ff:<br>           i) Should update the following in the Table<br>              a) Quantity Delivered<br>              b) Date Delivered<br>              c) Delivery Status<br>           ii) Should check if the entered Quantity Ordered and Quantity Delivered are equal<br>              a) If Equal<br>                  1) Should update the:<br>                      a.1) Delivery Status to Delivered<br>                      a.2) Edit Button should be enabled when the Delivery Receipt is not yet Submitted<br>           iii) Should record the Delivery Details in History<br>14. Should allow entering of Delivery Receipt per Item until the Quantity Ordered and Quantity Delivered became equal<br>15. Should successfully save a draft and  create a temporary Delivery Receipt<br>16. Should Successfully Submit Delivery Receipt with valid receipt number</span></td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="2089059147R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">PRS-022-006</td><td class="s5">Creation of Delivery Receipt per Supplier</td><td class="s5">Critical</td><td class="s5">Verify Creation of Delivery Receipt per Supplier when clicked Submit directly</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number <br>5.Select a Supplier and Populate Purchase Order Section<br>6. Check Items Section<br>7. Click &quot;Edit&quot; eye icon<br>8. Check Item Modal edit details<br>9. Validate all fields in the modal<br>10. Click the &quot;Cancel&quot; button on the modal<br>11. Click &quot;Edit&quot; Icon again on the tems section in Actions column table list<br>12. Populate all fields<br>13. Clck &quot;Save&quot; button on the modal<br>14. Check if still allow to enter delivery details when quantity ordered and quantity delivered is not equal<br>15. Click &quot;Submit&quot; button on Delivery Receipt form</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5.Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should display section for item table details<br>7. Should display a modal for the Item to enter the Delivery Details<br>8.  Should display a Modal with the following Fields and buttons:<br>        i. Item Name<br>        ii. Date Delivered<br>        iii. Qty Orderd<br>        iv. Qty Delivered<br>        v. Qty for Return - if existing<br>        vi. Notes<br>     .  vii. Should have Save and Cancel Buttons<br>9.  Should met the validaations for the ff fields:<br>        i. Item Name<br>          i) Non-editable Field, should display the Item selected for Edit<br>        ii. Date Delivered<br>          i) Should be Date Picker<br>          ii) Date should be the Current Date or less than the Current Date<br>        iii. Quantity Orderd<br>          i) Non-editable Field, should display the Item selected for Edit<br>        iv. Quantity Delivered<br>          i) Should have the maximum of the Quantity Ordered of the Items<br>             a) Numbers only, maximum of 3 Characters<br>                 1) Should validate if the entered value is equal or less than the Current Quantity Ordered<br>        v. Quantity For Return<br>          i.) Should display number of return if  delivery status is w/ return<br>        vi. Notes<br>          i) Should be a Text Area<br>          ii) Should have a maximum of 100 Characters<br>          iii) Should accept Alphanumeric and Special Characters except for Emojis<br>10. Should close the Modal and Return to Delivery Receipt Form<br>11. Should display a modal for the Item to enter the Delivery Details<br>12. Fields should be populated<br>13. If Save Button is clicked, Should met the ff:<br>           i) Should update the following in the Table<br>              a) Quantity Delivered<br>              b) Date Delivered<br>              c) Delivery Status<br>           ii) Should check if the entered Quantity Ordered and Quantity Delivered are equal<br>              a) If Equal<br>                  1) Should update the:<br>                      a.1) Delivery Status to Delivered<br>                      a.2) Edit Button should be enabled when the Delivery Receipt is not yet Submitted<br>           iii) Should record the Delivery Details in History<br>14. Should allow entering of Delivery Receipt per Item until the Quantity Ordered and Quantity Delivered became equal<br>15. Should Successfully Submit Delivery Receipt with valid receipt number</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="2089059147R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">PRS-022-007</td><td class="s5">Entering of Invoice in the Delivery Receipt</td><td class="s5">Critical</td><td class="s5">Verify Entering of Invoice in the Delivery Receipt when Clicked Save Draft first before submit</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Select a Supplier and Populate Purchase Order Section<br>6. Click the &quot;Select Invoice&quot; button<br>7. Validate all additional fields for Invoice<br>8. Populate all fields in Invoice and delivery receipts<br>9. Click &quot;Save Draft&quot; on Delivery Receipt form<br>10. Clck &quot;Continue&quot; on the Confirmation Modal<br>11. Check Status of Delivery</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5. Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should open or display additional Following Fields for the Invoice:<br>   a. Uploading Button for Invoice<br>    b. Invoice Number<br>    c. Issued Invoice Date<br>    d. Total Sales<br>    e. VAT Amount<br>    f. Zero Rated Sales Amount<br>7.  Should met the ff:<br>   a. Uploading Button for Invoice<br>        i. Should allow Maximum File Size of 25MB per File<br>        ii. Should be able to upload File Types of PDF, JPG, JPEG, PNG<br>    b. Invoice Number<br>        i. Should be Alphanumeric Only<br>        ii. Should have a maximum of 50 Characters<br>    c. Issued Invoice Date<br>        i. Should be a Date Picker<br>        ii. Should be equal or less than the Current Date<br>    d. Total Sales<br>        i. Should be Numbers only, allow Decimal<br>    e. VAT Amount<br>        i. Should be Numbers only, allow Decimal<br>    f. Zero Rated Sales Amount<br>        i. Should be Numbers only, allow Decimal<br>8. All fields should be populated<br>9. Should Display a confirmation modal<br>10. Should assign a Temporary DR Number with a Fomat of:<br>             i) DR-TMP-[12Code]<br>11. Should have a Status of Draft Delivery</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="2089059147R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5">PRS-022-008</td><td class="s5">Entering of Invoice in the Delivery Receipt</td><td class="s5">Critical</td><td class="s5">Verify Entering of Invoice in the Delivery Receipt when directly clicked Submit when all items are fully delivered</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5 .Select a Supplier and Populate Purchase Order Section<br>6. Click the &quot;Select Invoice&quot; button<br>7. Validate all additional fields for Invoice<br>8. Populate all fields in Invoice and delivery receipts<br>9. Click &quot;Submit&quot; button on Delivery Receipt form<br>10. Clck &quot;Continue&quot; button on the Confirmation Modal<br>11. Check the documents in delivery tab of Related Documents when in RS Viewing<br>12. Check the status when all items are all delivered</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5. Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should open or display additional Following Fields for the Invoice:<br>   a. Uploading Button for Invoice<br>    b. Invoice Number<br>    c. Issued Invoice Date<br>    d. Total Sales<br>    e. VAT Amount<br>    f. Zero Rated Sales Amount<br>7.  Should met the ff:<br>   a. Uploading Button for Invoice<br>        i. Should allow Maximum File Size of 25MB per File<br>        ii. Should be able to upload File Types of PDF, JPG, JPEG, PNG<br>    b. Invoice Number<br>        i. Should be Alphanumeric Only<br>        ii. Should have a maximum of 50 Characters<br>    c. Issued Invoice Date<br>        i. Should be a Date Picker<br>        ii. Should be equal or less than the Current Date<br>    d. Total Sales<br>        i. Should be Numbers only, allow Decimal<br>    e. VAT Amount<br>        i. Should be Numbers only, allow Decimal<br>    f. Zero Rated Sales Amount<br>        i. Should be Numbers only, allow Decimal<br>8. All fields should be populated<br>9. Should Display a confirmation modal<br>10.  Should create the Delivery Receipt with a DR Number<br>11. Should add the Document to Delivery Tab of Related Documents<br>12. Should update the status to &quot;For Payment Request&quot;</td><td class="s13"></td><td class="s15">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Incorrect status displayed</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1071">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1071</a></td></tr><tr style="height: 19px"><th id="2089059147R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s5">PRS-022-009</td><td class="s5">Entering of Invoice in the Delivery Receipt</td><td class="s5">Critical</td><td class="s5">Verify Entering of Invoice in the Delivery Receipt when directly clicked Submit when all items are partially delivered</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Select a Supplier and Populate Purchase Order Section<br>6. Click the &quot;Select Invoice&quot; button<br>7. Validate all additional fields for Invoice<br>8. Populate all fields in Invoice and delivery receipts<br>9. Click &quot;Submit&quot; button on Delivery Receipt form<br>10. Clck &quot;Continue&quot; button on the Confirmation Modal<br>11. Check the documents in delivery tab of Related Documents when in RS Viewing<br>12. Check the status when not all items are delivered</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5. Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should open or display additional Following Fields for the Invoice:<br>   a. Uploading Button for Invoice<br>    b. Invoice Number<br>    c. Issued Invoice Date<br>    d. Total Sales<br>    e. VAT Amount<br>    f. Zero Rated Sales Amount<br>7.  Should met the ff:<br>   a. Uploading Button for Invoice<br>        i. Should allow Maximum File Size of 25MB per File<br>        ii. Should be able to upload File Types of PDF, JPG, JPEG, PNG<br>    b. Invoice Number<br>        i. Should be Alphanumeric Only<br>        ii. Should have a maximum of 50 Characters<br>    c. Issued Invoice Date<br>        i. Should be a Date Picker<br>        ii. Should be equal or less than the Current Date<br>    d. Total Sales<br>        i. Should be Numbers only, allow Decimal<br>    e. VAT Amount<br>        i. Should be Numbers only, allow Decimal<br>    f. Zero Rated Sales Amount<br>        i. Should be Numbers only, allow Decimal<br>8. All fields should be populated<br>9. Should Display a confirmation modal<br>10.  Should create the Delivery Receipt with a DR Number<br>11. Should add the Document to Delivery Tab of Related Documents<br>12. Should update the status to &quot;Partially Delivered&quot;</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="2089059147R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5">PRS-022-010</td><td class="s5">Entering of Invoice in the Delivery Receipt</td><td class="s5">Critical</td><td class="s5">Verify Entering of Invoice in the Delivery Receipt when clicked Cancel button</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Select a Supplier and Populate Purchase Order Section<br>6. Click the &quot;Select Invoice&quot; button<br>7. Validate all additional fields for Invoice<br>8. Populate all fields in Invoice and delivery receipts<br>9. Click &quot;Cancel&quot; button on Delivery Receipt form<br>10. Clck &quot;Continue&quot; button on the Confirmation Modal</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5. Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should open or display additional Following Fields for the Invoice:<br>   a. Uploading Button for Invoice<br>    b. Invoice Number<br>    c. Issued Invoice Date<br>    d. Total Sales<br>    e. VAT Amount<br>    f. Zero Rated Sales Amount<br>7.  Should met the ff:<br>   a. Uploading Button for Invoice<br>        i. Should allow Maximum File Size of 25MB per File<br>        ii. Should be able to upload File Types of PDF, JPG, JPEG, PNG<br>    b. Invoice Number<br>        i. Should be Alphanumeric Only<br>        ii. Should have a maximum of 50 Characters<br>    c. Issued Invoice Date<br>        i. Should be a Date Picker<br>        ii. Should be equal or less than the Current Date<br>    d. Total Sales<br>        i. Should be Numbers only, allow Decimal<br>    e. VAT Amount<br>        i. Should be Numbers only, allow Decimal<br>    f. Zero Rated Sales Amount<br>        i. Should be Numbers only, allow Decimal<br>8. All fields should be populated<br>9. Should Display a confirmation modal<br>10.  Should not be able to create the Delivery Receipt </td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="2089059147R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">PRS-022-011</td><td class="s5">Entering of Invoice in the Delivery Receipt</td><td class="s5">Critical</td><td class="s5">Verify Entering of Invoice in the Delivery Receipt when click &quot;Remove invoice&quot;</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Requester<br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. Status of the Purchase Order is For Delivery</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Select Action&quot; button<br>3. Click &quot;Record Delivery Receipt&quot;<br>4. Check the DR Number<br>5. Select a Supplier and Populate Purchase Order Section<br>6. Click the &quot;Select Invoice&quot; button<br>7. Validate all additional fields for Invoice<br>8. Click &quot;Remove Invoice&quot; button<br>9. Click &quot;Submit&quot; button on Delivery Receipt form<br>10. Clck &quot;Continue&quot; button on the Confirmation Modal<br></td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display a Modal of Actions Options<br>        i. Record Delivery Receipt<br>3. Should display Delivery Receipt Form<br>4. Should display blank DR Number and the RS Number<br>5. Items should be added in the table list based on the selected supplier and Purchase Order section should be populated<br>6. Should open or display additional Following Fields for the Invoice:<br>   a. Uploading Button for Invoice<br>    b. Invoice Number<br>    c. Issued Invoice Date<br>    d. Total Sales<br>    e. VAT Amount<br>    f. Zero Rated Sales Amount<br>7.  Should met the ff:<br>   a. Uploading Button for Invoice<br>        i. Should allow Maximum File Size of 25MB per File<br>        ii. Should be able to upload File Types of PDF, JPG, JPEG, PNG<br>    b. Invoice Number<br>        i. Should be Alphanumeric Only<br>        ii. Should have a maximum of 50 Characters<br>    c. Issued Invoice Date<br>        i. Should be a Date Picker<br>        ii. Should be equal or less than the Current Date<br>    d. Total Sales<br>        i. Should be Numbers only, allow Decimal<br>    e. VAT Amount<br>        i. Should be Numbers only, allow Decimal<br>    f. Zero Rated Sales Amount<br>        i. Should be Numbers only, allow Decimal<br>8. Should allow Removing of Invoice, and hide the Invoice Fields<br>9. Should Display a confirmation modal<br>10.  Should not be able to create Delivery Receipt as Invoice fields are required</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/10: logged bug for no error message - minor</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1111">3/10: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1111</a></td></tr><tr style="height: 19px"><th id="2089059147R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s7">PRS-022-012</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">Critical</td><td class="s7">Verify Viewing of Delivery receipt per supplier on View Attachments Section when filtering attachment</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On the &quot;Attachment&quot; Section, Click Check Attachment button<br>6. Populate a keyword<br>7. Click &quot;Search&quot;<br>8. Click &quot;Clear&quot;&#39; Button</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Detailsa and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should be able to view the List of Attachments<br>6. Should be able to populate keyword<br>7. Should display a file name related to keyword<br>8. Should reset all files</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Bug for attachment UI</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1073">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1073</a></td></tr><tr style="height: 19px"><th id="2089059147R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s7">PRS-022-013</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">High</td><td class="s7">Verify Viewing of Delivery receipt per supplier on View Attachments Section when filtering attachments with no data available</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On the &quot;Attachment&quot; Section, Click Check Attachment button<br>6. Populate a keyword that isnot existing on the list<br>7. Click &quot;Search&quot;<br>8. Click &quot;Clear&quot;&#39; Button</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Detailsa and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should be able to view the List of Attachments<br>6. Should be able to populate keyword<br>7. Should Display &quot;No data available&quot;<br>8. Should reset all files<br></td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Bug for attachment UI</td><td class="s7">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1074</td></tr><tr style="height: 19px"><th id="2089059147R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s7">PRS-022-014</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">Critical</td><td class="s7">Verify Viewing of Delivery receipt per supplier on Upload Attachments Section</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On the &quot;Attachment&quot; Section, Upload an Attachment <br>6. Upload an attachment that has a greater than 25mb file size<br>7. Upload an attachment that has .Note, GIF, PPT file types<br>8. Upload an attachment that has 25mb and below file size<br>9. Upload an attachment that has  PDF, Doc, JPG, JPEG, PNG, CSV, Excel file types<br>10 Click Submit button<br>11. Click Attachment button</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4. Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should be able to upload an attachment from local file<br>6. Should not be able to accept the attachment and displayed an error message<br>7. Should not be able to accept the attachment and displayed an error message<br>8. Should be able to accept the attachment<br>9. Should be able to accept the attachment<br>10. Attachment should be successfully added<br>11. Should be able to view the List of Attachments and have a Badge of New Attachment if a New Attachment has been attached<br>                 a. Should only clear the Badge if the Check Attachments button was opened</td><td class="s13"></td><td class="s15">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: No error message w/ different file types</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1074">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1074</a></td></tr><tr style="height: 19px"><th id="2089059147R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s7">PRS-022-015</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">Critical</td><td class="s7">Verify Viewing of Delivery receipt per supplier on View Attachments Section when remove an attachment</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. Clck &#39;x&#39; icon on the attached file in Attachment section<br>6. On the &quot;Attachment&quot; Section, Click Attachment button<br>7. Clck &#39;x&#39; icon on the attached file in View attachment</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Detailsa and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should removed/delete the attachment<br>6. Should be able to view the List of Attachments<br>7. Should removed/delete the attachment</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="2089059147R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s7">PRS-022-016</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">Critical</td><td class="s7">Verify Viewing of Delivery receipt per supplier on View Notes Section when filtering notes</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On the &quot;Notes&quot; Section, Click Notes button<br>6. Select/populate future date in &quot;From&quot; date<br>7. Select/populate future date in &quot;To&quot; date<br>8. Select/Populate past date up to current date in &quot;From&quot; date<br>9.  Select/Populate past date up to current date in &quot;To&quot; date<br>10. Click Filter button</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Detailsa and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should be able to view the List of Notes<br>6. Should not be able to select/populate future date in &quot;From&quot; date<br>7. Should not be able to select/populate future date in &quot;To&quot; date<br>8. Should be able to select past date up to current date in &quot;From&quot; date<br>9. Should be able to select past date up to current date on &quot;To&quot; date<br>10. Should Display all notes that has been added based on selected filter date</td><td class="s13"></td><td class="s15">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Unable to add notes</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1076">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1076</a></td></tr><tr style="height: 19px"><th id="2089059147R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s7">PRS-022-017</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">High</td><td class="s7">Verify Viewing of Delivery receipt per supplier on View Notes Section when filtering Notes with no data available</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On the &quot;Notes&quot; Section, Click Notes button<br>6. Select/Populate past date up to current date in &quot;From&quot; date that no available file uploaded<br>7.  Select/Populate past date up to current date in &quot;To&quot; date that no available file uploaded<br>8. Click Filter button</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Detailsa and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should be able to view the List of Notes<br>6. Should be able to select past date up to current date in &quot;From&quot; date<br>7. Should be able to select past date up to current date on &quot;To&quot; date<br>8. Should Display &quot;No data available&quot;</td><td class="s13"></td><td class="s15">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Unable to add notes</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1076">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1076</a></td></tr><tr style="height: 19px"><th id="2089059147R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s7">PRS-022-018</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">Critical</td><td class="s7">Verify Viewing of Delivery receipt per supplier on Upload Notes Section</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On the &quot;Notes&quot; Section, Populate a notes that has an Emojis<br>6. Populate a notes that has a greater than 100 characters<br>7. Click Submit button<br>8. Populate a notes that has Alphanumeric and Special Characters only.<br>9. Populate a notes that has maximum of 100 Characters<br>10 Click Submit button<br>11. Click &quot;Notes&quot; button</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4. Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Notes with emojis should be populated<br>6. Notes with greater than 100 characters should be populated<br>7. Should not be able to accept populated notes and displayed an error message<br>8. Notes with Alphanumeric and special characters should be populated<br>9. Notes with maximum of 100 characters should be populated<br>10. Notes should be successfully added<br>11. Should be able to view the List of Notes and have a Badge of New Notes if a New Notes has been added<br>                 a. Should only clear the Badge if the Check Notes button was opened</td><td class="s13"></td><td class="s15">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/6: Unable to add notes</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1076">3/6: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1076</a></td></tr><tr style="height: 19px"><th id="2089059147R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s7">PRS-022-019</td><td class="s7">Viewing of Delivery Receipt per Supplier</td><td class="s18">High</td><td class="s7">Verify Viewing of Delivery receipt per supplier when searching and pagination are working as expected in Items Table</td><td class="s10"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s7">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, search an Item name<br>6. Search a Keyword of Item Name<br>7. Search an Item that is not existing on ther table<br>8. Click &quot;Clear&quot; button on the Items section<br>9. Check Display of Items per page of the table<br>10. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>11. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination<br>12. Check Default sorting of  the Items in the list table</td><td class="s7"></td><td class="s7">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Detailsa and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should be able to search the item Name<br>6. Should be able to search  an item name using a keyword<br>7. Should display No Data if there is no available Data matching the Searched Item<br>8. Should clear the Search Item and reset the Table <br>9. Should display 10 Rows per Page<br>10. Should display the previous or next page of the table<br>11. Should display the specific page selected with correct number of item entries in the table<br>12.Should be sorted by Latest Updated Date</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/10: Unable to test pagination due to issue for creating RS with more than 10 items</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1036">3/10: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1036<br></a></td></tr><tr style="height: 19px"><th id="2089059147R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">PRS-022-020</td><td class="s5">Viewing of Delivery Receipt per Supplier</td><td class="s19">Critical</td><td class="s5">Verify Viewing of Delivery receipt per supplier of Items in the Items table</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, click an &quot;Item&quot; Link<br>6. Check Viewing of Item details modal</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should open the Modal for Viewing the Details of the Item&#39;s Delivery<br>6. Should displayed the following:<br>         i. Should display non-editable Fields for:<br>            i) Item<br>            ii) Date Delivered<br>            iii) Quantity Ordered<br>            iv) Quantity Delivered<br>            v) Quantity for Return<br>            vi) Notes<br>         ii. Should have Delivery Status<br>            i) Should display the Delivery Status<br>               a) Should have a Cancel Return Button if the Status is with Return<br>         iii. Should have Edit Button<br>            i) Should open Edit Modal for the Item<br>         iv. Should have Close Window Button<br>            i) Should close the Modal</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="2089059147R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">PRS-022-021</td><td class="s5">Viewing of Delivery Receipt per Supplier</td><td class="s19">High</td><td class="s5">Verify Viewing of Delivery receipt per supplier  when sorting each columns of the Items table</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts <br>4. A Requisition Slip has been created<br>5. Items in the Requisition Slip has been Ordered<br>6. A Delivery Receipt has been created</a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, Click Sorting for Item column<br>6.  Click Sorting on Qty Ordered column<br>7. Click Sorting on Qty Delivered column<br>8. Click Sorting on Unit column<br>9. Click Sorting on Date Delivered column<br>10. Click Sorting on Delivery Status column<br>11 Click Sorting on Returns column<br></td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should sort the &quot;Item&quot; to - 0-9, A-Z || 9-0, Z-A<br>6. Should sort the &quot;Qty Ordered&quot; to - 0-9, 9-0<br>7. Should sort the &quot;Qty Delivered&quot; to  - 0-9, 9-0<br>8. Should sort the &quot;Unit&quot; to - A-Z, Z-A<br>9. Should sort the &quot;Date Delivered&quot; to  - Oldest Date-Latest Date, Latest Date-Oldest Date<br>10. Should sort the &quot;Delivery Status&quot; to  - A-Z, Z-A<br>11. Should sort the &quot;Returns&quot; to - A-Z, Z-A<br></td><td class="s13"></td><td class="s15">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/11: Unable to sort all columns</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1122">3/11: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1122</a></td></tr><tr style="height: 19px"><th id="2089059147R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">PRS-022-022</td><td class="s5">Viewing of Delivery Receipt per Supplier</td><td class="s19">Critical</td><td class="s5">Verify Viewing of Delivery receipt per supplier  when Edit thru Actions column</td><td class="s5"></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, Check list of items that has a &quot;Partially Delivered&quot; or &quot;Partially Delivered w/ Return&quot; Status<br>6. Check Items that has a &quot;Fully Delivered&quot; Status<br>7. Click Edit icon in Actions column of item that has a &quot;Partially Delivered&quot; or &quot;Partially Delivered w/ Return&quot; Status<br>8. Validate non-editable fields in Edit modaL<br>9. Validate all editable fields in Edit modal<br>10. Populate all editable fields<br>11, Click &quot;Save&quot; button <br>12. Click &quot;Continue&quot; on Confirmation modal<br>13. Check History of the Items in the Item Management</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Edit icon in Actions column should be enabled<br>6. Edit icon in Actions column should be disabled<br>7. Should display an edit modal<br>8. Should display non-editable Fields for:<br>          a.Item<br>          b. Quantity Ordered<br>9. Should display editable Fields for:<br>            i) Date Delivered<br>               a) Should be Current Date or less than the Current Date<br>            ii) Quantity Delivered<br>                a) Should be the maximum of the Remaining undelivered Quantity<br>                b) Numbers only, maximum of 3 Characters<br>            iii) Quantity for Return<br>                a) Should be the maximum of the Remaining undelivered Quantity and Quantity Delivered<br>                b) Numbers only, maximum of 3 Characters<br>            iv) Notes<br>                a) Alpahnumeric with Special Characters except Emojis<br>                b) Maximum of 100 Characters<br>10. All fields shoud be populated <br>11. Should display a &quot;Save Changes&quot; Confirmation Modal<br>12.  Should update the following:<br>                a) Delivery Date<br>                b) Quantity Delivered<br>                c) Status should depend on the Quantity Delivered and Quantity for Return<br>                    1) If Quantity Delivered and Quantity for Return has Values, should have a Status of Delivered w/ Return<br>                    2) If only the Quantity for Return has Value, should have a Status of Returned<br>                    3) If all of the Ordered Quantity has been Delivered should have a Status of For Payment Request<br>13.  Should add the updates of the Item as an Entry in the Item History</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/11: Notes accept emojis</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">3/11: https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="2089059147R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">PRS-022-023</td><td class="s5">Viewing of Delivery Receipt per Supplier</td><td class="s19">High</td><td class="s5">Verify Viewing of Delivery receipt per supplier  when Edit thru Actions column and clicked cancel</td><td class="s5"></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, Check list of items that has a &quot;Partially Delivered&quot; or &quot;Partially Delivered w/ Return&quot; Status<br>6. Check Items that has a &quot;Fully Delivered&quot; Status<br>7. Click Edit icon in Actions column of item that has a &quot;Partially Delivered&quot; or &quot;Partially Delivered w/ Return&quot; Status<br>8. Validate non-editable fields in Edit modaL<br>9. Validate all editable fields in Edit modal<br>10. Populate all editable fields<br>11, Click &quot;Cancel&quot; button <br>12. Click &quot;Continue&quot; on Confirmation modal<br>13. Check History of the Items in the Item Management</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Edit icon in Actions column should be enabled<br>6. Edit icon in Actions column should be disabled<br>7. Should display an edit modal<br>8. Should display non-editable Fields for:<br>          a.Item<br>          b. Quantity Ordered<br>9. Should display editable Fields for:<br>            i) Date Delivered<br>               a) Should be Current Date or less than the Current Date<br>            ii) Quantity Delivered<br>                a) Should be the maximum of the Remaining undelivered Quantity<br>                b) Numbers only, maximum of 3 Characters<br>            iii) Quantity for Return<br>                a) Should be the maximum of the Remaining undelivered Quantity and Quantity Delivered<br>                b) Numbers only, maximum of 3 Characters<br>            iv) Notes<br>                a) Alpahnumeric with Special Characters except Emojis<br>                b) Maximum of 100 Characters<br>10. All fields shoud be populated <br>11. Should display a &quot;Cancel Changes&quot; Confirmation Modal<br>12. Should cacel the changes<br>13.  Should not add any updates of the Item in the Item History</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s16"></td><td class="s16"></td></tr><tr style="height: 19px"><th id="2089059147R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">PRS-022-024</td><td class="s5">Viewing of Delivery Receipt per Supplier</td><td class="s19">Critical</td><td class="s5">Verify Viewing of Delivery receipt per supplier  when Edit  thru viewing of Item details</td><td class="s5"></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, click an &quot;Item&quot; Link<br>6. Check Viewing of Item details modal<br>7. Click Edit button in View Modal<br>8. Validate non-editable fields in Edit modaL<br>9. Validate all editable fields in Edit modal<br>10. Populate all editable fields<br>11, Click &quot;Save&quot; button <br>12. Click &quot;Continue&quot; on Confirmation modal<br>13. Check History of the Items in the Item Management</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should open the Modal for Viewing the Details of the Item&#39;s Delivery<br>6. Should displayed the following:<br>         i. Should display non-editable Fields for:<br>            i) Item<br>            ii) Date Delivered<br>            iii) Quantity Ordered<br>            iv) Quantity Delivered<br>            v) Quantity for Return<br>            vi) Notes<br>         ii. Should have Delivery Status<br>            i) Should display the Delivery Status<br>               a) Should have a Cancel Return Button if the Status is with Return<br>         iii. Should have Edit Button<br>            i) Should open Edit Modal for the Item<br>         iv. Should have Close Window Button<br>            i) Should close the Modal<br>7. Should display an edit modal<br>8. Should display non-editable Fields for:<br>          a.Item<br>          b. Quantity Ordered<br>9. Should display editable Fields for:<br>            i) Date Delivered<br>               a) Should be Current Date or less than the Current Date<br>            ii) Quantity Delivered<br>                a) Should be the maximum of the Remaining undelivered Quantity<br>                b) Numbers only, maximum of 3 Characters<br>            iii) Quantity for Return<br>                a) Should be the maximum of the Remaining undelivered Quantity and Quantity Delivered<br>                b) Numbers only, maximum of 3 Characters<br>            iv) Notes<br>                a) Alpahnumeric with Special Characters except Emojis<br>                b) Maximum of 100 Characters<br>10. All fields shoud be populated <br>11. Should display a &quot;Save Changes&quot; Confirmation Modal<br>12.  Should update the following:<br>                a) Delivery Date<br>                b) Quantity Delivered<br>                c) Status should depend on the Quantity Delivered and Quantity for Return<br>                    1) If Quantity Delivered and Quantity for Return has Values, should have a Status of Delivered w/ Return<br>                    2) If only the Quantity for Return has Value, should have a Status of Returned<br>                    3) If all of the Ordered Quantity has been Delivered should have a Status of For Payment Request<br>13.  Should add the updates of the Item as an Entry in the Item History</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s7">3/11: Notes accept emojis</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-805">3/11: https://youtrack.stratpoint.com/issue/CITYLANDPRS-805</a></td></tr><tr style="height: 19px"><th id="2089059147R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">PRS-022-025</td><td class="s5">Viewing of Delivery Receipt per Supplier</td><td class="s19">High</td><td class="s5">Verify Viewing of Delivery receipt per supplier  when Edit  thru viewing of Item details and clicked cancel</td><td class="s5"></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click the &quot;Deliveries&quot; Tab<br>4. Click Any of &quot;Delivery No.&quot; link on the table<br>5. On Items table, click an &quot;Item&quot; Link<br>6. Check Viewing of Item details modal<br>7. Click Edit button in View Modal<br>8. Validate non-editable fields in Edit modaL<br>9. Validate all editable fields in Edit modal<br>10. Populate all editable fields<br>11, Click &quot;Cancel&quot; button <br>12. Click &quot;Continue&quot; on Confirmation modal<br>13. Check History of the Items in the Item Management</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display all the Delivery lists on the Table<br>4.  Should display the Delivery receipt Details and a Section for the ff:<br>           i) Purchase Order Details<br>           ii) Invoice Details - if added<br>           iii) Attachments and Notes Section<br>           iv) Items Table<br>5. Should open the Modal for Viewing the Details of the Item&#39;s Delivery<br>6. Should displayed the following:<br>         i. Should display non-editable Fields for:<br>            i) Item<br>            ii) Date Delivered<br>            iii) Quantity Ordered<br>            iv) Quantity Delivered<br>            v) Quantity for Return<br>            vi) Notes<br>         ii. Should have Delivery Status<br>            i) Should display the Delivery Status<br>               a) Should have a Cancel Return Button if the Status is with Return<br>         iii. Should have Edit Button<br>            i) Should open Edit Modal for the Item<br>         iv. Should have Close Window Button<br>            i) Should close the Modal<br>7. Should display an edit modal<br>8. Should display non-editable Fields for:<br>          a.Item<br>          b. Quantity Ordered<br>9. Should display editable Fields for:<br>            i) Date Delivered<br>               a) Should be Current Date or less than the Current Date<br>            ii) Quantity Delivered<br>                a) Should be the maximum of the Remaining undelivered Quantity<br>                b) Numbers only, maximum of 3 Characters<br>            iii) Quantity for Return<br>                a) Should be the maximum of the Remaining undelivered Quantity and Quantity Delivered<br>                b) Numbers only, maximum of 3 Characters<br>            iv) Notes<br>                a) Alpahnumeric with Special Characters except Emojis<br>                b) Maximum of 100 Characters<br>10. All fields shoud be populated <br>11. Should display a &quot;Cancel Changes&quot; Confirmation Modal<br>12. Should cacel the changes<br>13.  Should not add any updates of the Item in the Item History</td><td class="s13"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s16"></td><td class="s16"></td></tr></tbody></table></div>