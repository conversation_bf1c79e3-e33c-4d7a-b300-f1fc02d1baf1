// Legacy middleware (for backward compatibility)
const authenticate = require('./authenticate');
const authorize = require('./authorize');
const verifyOTPToken = require('./verifyOTPToken');
const verifyPassToken = require('./verifyPassToken');
const verifyRefreshToken = require('./verifyRefreshToken');

// File handling middleware
const uploadFile = require('./uploadFile');
const uploadLimit = require('./uploadLimit');

// Logging middleware
const auditLogs = require('./auditLogs');
const requestLogger = require('./requestLogger');
const databaseLogger = require('./databaseLogger');
const cleanLogs = require('./cleanLogs');

// New modular middleware
const auth = require('./auth');
const utils = require('./utils');

module.exports = {
  // Legacy middleware (for backward compatibility)
  authenticate,
  authorize,
  verifyOTPToken,
  verifyPassToken,
  verifyRefreshToken,

  // File handling middleware
  uploadFile,
  uploadLimit,

  // Logging middleware
  auditLogs,
  requestLogger,
  databaseLogger,
  cleanLogs,

  // New modular middleware
  ...auth,

  // Utility functions for creating custom middleware
  utils,
};
