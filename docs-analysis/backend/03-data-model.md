# Data Model and Relationships

## Overview

The PRS-Backend uses Sequelize as its ORM and PostgreSQL as the database. This document provides an analysis of the key data models, their relationships, and how they support the business flows.

## Core Entities

### 1. User and Role Models

Users are the actors in the system with different roles determining their permissions.

**Key Fields:**
- `id`: Primary key
- `username`: Unique username
- `email`: User's email address
- `password`: Hashed password
- `roleId`: Foreign key to roles
- `departmentId`: Foreign key to departments
- `otpSecret`: Secret for OTP authentication
- `status`: User status (active, inactive, on-leave)

**Relationships:**
- User belongs to Role
- User belongs to Department
- User has many Requisitions (as creator)
- User has many Notifications

### 2. Requisition Model

Requisitions are the central entity in the system, representing purchase requests.

**Key Fields:**
- `id`: Primary key
- `rsNumber`: Requisition slip number
- `rsLetter`: Requisition slip letter code
- `type`: Type of requisition (ofm, non-ofm, etc.)
- `status`: Current status of the requisition
- `companyId`: Foreign key to companies
- `projectId`: Foreign key to projects
- `departmentId`: Foreign key to departments
- `createdBy`: Foreign key to users (creator)
- `assignedTo`: Foreign key to users (assignee)
- `deliveryAddress`: Delivery address for items

**Relationships:**
- Requisition belongs to Company
- Requisition belongs to Project
- Requisition belongs to Department
- Requisition belongs to User (creator)
- Requisition belongs to User (assignee)
- Requisition has many RequisitionItemLists
- Requisition has many RequisitionApprovers
- Requisition has many CanvassRequisitions
- Requisition has many PurchaseOrders
- Requisition has many DeliveryReceipts
- Requisition has many RSPaymentRequests

### 3. RequisitionItemList Model

Items included in a requisition.

**Key Fields:**
- `id`: Primary key
- `requisitionId`: Foreign key to requisitions
- `itemId`: Foreign key to items
- `quantity`: Quantity requested
- `notes`: Notes about the item

**Relationships:**
- RequisitionItemList belongs to Requisition
- RequisitionItemList belongs to Item
- RequisitionItemList has many CanvassItems

### 4. RequisitionApprover Model

Approvers assigned to a requisition.

**Key Fields:**
- `id`: Primary key
- `requisitionId`: Foreign key to requisitions
- `approverId`: Foreign key to users (approver)
- `level`: Approval level (determines order)
- `status`: Approval status (pending, approved, rejected)
- `isAltApprover`: Whether this is an alternative approver
- `isAdhoc`: Whether this is an ad-hoc approver

**Relationships:**
- RequisitionApprover belongs to Requisition
- RequisitionApprover belongs to User (approver)

### 5. CanvassRequisition Model

Canvass sheets created for requisitions.

**Key Fields:**
- `id`: Primary key
- `requisitionId`: Foreign key to requisitions
- `csNumber`: Canvass sheet number
- `csLetter`: Canvass sheet letter code
- `status`: Status of the canvass (draft, for_approval, etc.)

**Relationships:**
- CanvassRequisition belongs to Requisition
- CanvassRequisition has many CanvassItems
- CanvassRequisition has many CanvassApprovers

### 6. CanvassItem Model

Items included in a canvass sheet.

**Key Fields:**
- `id`: Primary key
- `canvassRequisitionId`: Foreign key to canvass requisitions
- `requisitionItemListId`: Foreign key to requisition item lists
- `status`: Status of the canvass item

**Relationships:**
- CanvassItem belongs to CanvassRequisition
- CanvassItem belongs to RequisitionItemList
- CanvassItem has many CanvassItemSuppliers

### 7. CanvassItemSupplier Model

Suppliers and their pricing for canvass items.

**Key Fields:**
- `id`: Primary key
- `canvassItemId`: Foreign key to canvass items
- `supplierId`: Foreign key to suppliers
- `supplierType`: Type of supplier (supplier, project, company)
- `unitPrice`: Unit price offered by the supplier
- `discountType`: Type of discount (percent, fixed)
- `discountValue`: Value of the discount
- `isSelected`: Whether this supplier is selected for the item

**Relationships:**
- CanvassItemSupplier belongs to CanvassItem
- CanvassItemSupplier belongs to Supplier (if supplierType is 'supplier')
- CanvassItemSupplier belongs to Project (if supplierType is 'project')
- CanvassItemSupplier belongs to Company (if supplierType is 'company')

### 8. PurchaseOrder Model

Purchase orders generated from approved canvass sheets.

**Key Fields:**
- `id`: Primary key
- `requisitionId`: Foreign key to requisitions
- `canvassRequisitionId`: Foreign key to canvass requisitions
- `poNumber`: Purchase order number
- `poLetter`: Purchase order letter code
- `supplierId`: Foreign key to suppliers
- `supplierType`: Type of supplier
- `status`: Status of the purchase order
- `terms`: Payment terms
- `totalAmount`: Total amount before discount
- `totalDiscount`: Total discount amount
- `totalDiscountedAmount`: Total amount after discount

**Relationships:**
- PurchaseOrder belongs to Requisition
- PurchaseOrder belongs to CanvassRequisition
- PurchaseOrder belongs to Supplier (if supplierType is 'supplier')
- PurchaseOrder has many PurchaseOrderItems
- PurchaseOrder has many PurchaseOrderApprovers
- PurchaseOrder has many DeliveryReceipts

### 9. DeliveryReceipt Model

Delivery receipts for received items.

**Key Fields:**
- `id`: Primary key
- `requisitionId`: Foreign key to requisitions
- `poId`: Foreign key to purchase orders
- `drNumber`: Delivery receipt number
- `supplier`: Supplier name
- `invoiceNumber`: Invoice number
- `issuedDate`: Date issued
- `status`: Status of the delivery receipt

**Relationships:**
- DeliveryReceipt belongs to Requisition
- DeliveryReceipt belongs to PurchaseOrder
- DeliveryReceipt has many DeliveryReceiptItems

### 10. RSPaymentRequest Model

Payment requests for delivered items.

**Key Fields:**
- `id`: Primary key
- `requisitionId`: Foreign key to requisitions
- `purchaseOrderId`: Foreign key to purchase orders
- `prNumber`: Payment request number
- `prLetter`: Payment request letter code
- `status`: Status of the payment request
- `termsData`: Payment terms data

**Relationships:**
- RSPaymentRequest belongs to Requisition
- RSPaymentRequest belongs to PurchaseOrder
- RSPaymentRequest has many RSPaymentRequestApprovers

## Key Relationships

### Requisition Flow Relationships

The following relationships support the requisition flow:

1. **Creation Phase**:
   - User (creator) → Requisition
   - Requisition → RequisitionItemList → Item

2. **Approval Phase**:
   - Requisition → RequisitionApprover → User (approver)

3. **Assignment Phase**:
   - Requisition → User (assignee)

4. **Canvassing Phase**:
   - Requisition → CanvassRequisition → CanvassItem → CanvassItemSupplier
   - CanvassRequisition → CanvassApprover → User (approver)

5. **Purchase Order Phase**:
   - CanvassRequisition → PurchaseOrder → PurchaseOrderItem
   - PurchaseOrder → PurchaseOrderApprover → User (approver)

6. **Delivery Phase**:
   - PurchaseOrder → DeliveryReceipt → DeliveryReceiptItem

7. **Payment Phase**:
   - PurchaseOrder → RSPaymentRequest → RSPaymentRequestApprover → User (approver)

## Database Design Issues

### 1. Polymorphic Relationships

The system uses polymorphic relationships in several places, which can be problematic:

```javascript
// Example from CanvassItemSupplier model
CanvassItemSupplier.belongsTo(models.supplierModel, {
  foreignKey: 'supplierId',
  constraints: false,
  as: 'supplier',
});

CanvassItemSupplier.belongsTo(models.projectModel, {
  foreignKey: 'supplierId',
  constraints: false,
  as: 'project',
});

CanvassItemSupplier.belongsTo(models.companyModel, {
  foreignKey: 'supplierId',
  constraints: false,
  as: 'company',
});
```

This approach:
- Disables foreign key constraints (`constraints: false`)
- Makes it difficult to enforce data integrity
- Complicates queries and relationships

### 2. Inconsistent Naming Conventions

The database uses inconsistent naming conventions:

- Some tables use snake_case (e.g., `purchase_orders`)
- Some foreign keys use camelCase (e.g., `supplierId`)
- Some fields use underscores (e.g., `company_code`)

This inconsistency makes it harder to understand and maintain the database schema.

### 3. Redundant Data

There's redundant data across multiple tables:

- Requisition status is duplicated in related entities
- Supplier information is duplicated in PurchaseOrder and DeliveryReceipt
- Letter and number codes are stored separately but used together

### 4. Missing Indexes

Some frequently queried fields lack proper indexes:

- Status fields used for filtering
- Foreign keys used in joins
- Fields used in WHERE clauses

### 5. Complex Query Requirements

The data model requires complex queries for common operations:

- Finding all requisitions for a user (as creator, approver, or assignee)
- Determining the current state of a requisition
- Calculating totals across related entities

## Recommendations

### 1. Standardize Relationship Patterns

Replace polymorphic relationships with more explicit designs:

```javascript
// Instead of polymorphic relationships, use separate tables
CanvassItemSupplier.belongsTo(models.supplierModel, {
  foreignKey: 'supplierId',
  as: 'supplier',
});

CanvassProjectSupplier.belongsTo(models.projectModel, {
  foreignKey: 'projectId',
  as: 'project',
});

CanvassCompanySupplier.belongsTo(models.companyModel, {
  foreignKey: 'companyId',
  as: 'company',
});
```

### 2. Normalize Database Schema

Normalize the database schema to reduce redundancy:

- Create a separate table for tracking requisition status history
- Use lookup tables for common values
- Ensure each piece of data is stored in only one place

### 3. Implement Consistent Naming Conventions

Adopt consistent naming conventions:

- Use snake_case for all table and column names
- Follow a consistent pattern for foreign keys
- Document naming conventions for future development

### 4. Add Appropriate Indexes

Add indexes to frequently queried fields:

- Status fields
- Foreign keys
- Fields used in WHERE clauses and joins

### 5. Implement Database Constraints

Add proper constraints to ensure data integrity:

- Foreign key constraints
- Check constraints for valid status values
- Unique constraints for business identifiers

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
