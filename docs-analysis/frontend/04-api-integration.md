# API Integration

## Overview

The PRS-Frontend integrates with the backend API using a combination of Axios for HTTP requests and React Query for data fetching, caching, and synchronization. This document provides a detailed analysis of the API integration approach, including the API client configuration, data fetching patterns, and error handling.

## API Client Configuration

The API client is configured in `src/lib/apiClient.js` using Axios:

```javascript
// From src/lib/apiClient.js
import axios from 'axios';
import { env } from '@config/env';
import { useTokenStore, useUserStore, usePermissionStore } from '@store';

function authRequestInterceptor(config) {
  const { getState } = useTokenStore;

  const token = getState().token;
  if (!config.headers.has('Authorization') && token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  config.headers.Accept = 'application/json';
  return config;
}

export const api = axios.create({
  baseURL: env.API_URL,
});

api.interceptors.request.use(authRequestInterceptor);
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    const { setState } = useTokenStore;
    const { setState: setUserState } = useUserStore;
    const { setState: setPermissionState } = usePermissionStore;

    if (error.response && error.response.status === 401) {
      setState({
        token: null,
        type: null,
        refreshToken: null,
        expiredAt: null,
      });
      setUserState({ user: null, otp: null, secret: null, currentRoute: null });
      setPermissionState({ permissions: null });
      sessionStorage.removeItem('timeoutState');
    } else {
      console.error('API Error:', error);
    }

    return Promise.reject(error);
  },
);
```

The API client includes:

1. **Base URL Configuration**: The base URL is configured from environment variables
2. **Request Interceptor**: Adds authentication headers to requests
3. **Response Interceptor**: Extracts data from responses and handles errors

## API Environment Configuration

The API URL is configured in `src/config/env.js`:

```javascript
// From src/config/env.js
import * as z from 'zod';

const createEnv = () => {
  const EnvSchema = z.object({
    API_URL: z.string().default('http://localhost:4000'),
    UPLOAD_URL: z.string().default('http://localhost:4000/upload'),
    ENABLE_API_MOCKING: z
      .string()
      .refine(s => s === 'true' || s === 'false')
      .transform(s => s === 'true')
      .optional(),
  });

  const envVars = Object.entries(import.meta.env).reduce((acc, curr) => {
    const [key, value] = curr;
    if (key.startsWith('VITE_APP_')) {
      acc[key.replace('VITE_APP_', '')] = value;
    }
    return acc;
  }, {});

  const mutatedEnvVars = {
    ...envVars,
    UPLOAD_URL: `${envVars.API_URL}/upload`,
  };

  // ... validation and return
};

export const env = createEnv();
```

This approach:
1. Uses Zod for schema validation
2. Extracts environment variables from Vite's `import.meta.env`
3. Provides default values for missing variables
4. Validates the environment configuration

## API Integration Patterns

### 1. Feature-Based API Organization

API functions are organized by feature, with each feature having its own API directory:

```
features/
├── auth/
│   └── api/
│       ├── login.js
│       ├── refresh-token.js
│       ├── verify-otp.js
│       └── index.js
├── dashboard/
│   └── api/
│       ├── get-requisition-slip.js
│       ├── get-requisition-items.js
│       └── index.js
├── company/
│   └── api/
│       ├── get-companies.js
│       ├── sync-companies.js
│       └── index.js
```

This organization makes it easy to find and maintain API functions related to specific features.

### 2. API Function Structure

API functions follow a consistent structure:

```javascript
// From src/features/company/api/get-companies.js
import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/apiClient';
import { formatSortParams } from '@utils/query';
import {
  convertObjectToString,
  removeEmptyStringFromObject,
} from '@utils/stringCleanup';

export const getCompanies = ({
  page = 1,
  limit = 10,
  paginate,
  filterBy,
  sortBy,
}) => {
  return api.get('/v1/companies', {
    params: {
      page,
      limit,
      paginate,
      filterBy: convertObjectToString(removeEmptyStringFromObject(filterBy)),
      ...formatSortParams(sortBy),
    },
  });
};

export const useGetCompanies = (params, config = {}) => {
  return useQuery({
    queryKey: ['companies', params],
    queryFn: () => getCompanies(params),
    ...config,
  });
};
```

This structure includes:
1. **API Function**: A function that makes the API request
2. **React Query Hook**: A hook that wraps the API function with React Query

### 3. Mutation Function Structure

Mutation functions follow a similar structure:

```javascript
// From src/features/notes/api/create-notes.js
import { submitNoteSchema } from '@schema/canvass.schema';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@lib/apiClient';

export const createNotes = ({
  model,
  modelId,
  userType,
  commentType,
  note,
}) => {
  const { success, error, data } = submitNoteSchema.safeParse({ notes: note });

  if (!success) {
    throw new Error(error.format().notes._errors[0]);
  }

  return api.post('/v1/notes/', {
    model,
    modelId: parseInt(modelId),
    userType,
    commentType,
    note: data.notes,
  });
};

export const useCreateNotes = (config = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createNotes,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
    ...config,
  });
};
```

This structure includes:
1. **Validation**: Using Zod to validate input data
2. **API Function**: A function that makes the API request
3. **React Query Hook**: A hook that wraps the API function with React Query
4. **Cache Invalidation**: Invalidating related queries on success

### 4. API Index Files

Each feature's API directory includes an index file that exports all API functions:

```javascript
// From src/features/company/api/index.js
export * from './get-company-details';
export * from './get-companies';
export * from './sync-companies';
export * from './update-company';
export * from './create-association';
export * from './get-areas';
export * from './delete-association';
export * from './get-projects';
export * from './untag-project';
```

This makes it easy to import multiple API functions from a single feature.

## Authentication Flow

The authentication flow is implemented in `src/lib/auth.jsx` using the `react-query-auth` library:

```javascript
// From src/lib/auth.jsx
import React from 'react';
import PropTypes from 'prop-types';
import { api } from './apiClient';
import { configureAuth } from 'react-query-auth';
import { Navigate } from 'react-router-dom';
import { useTokenStore, useUserStore, usePermissionStore } from '@store';
import { transformPermissions } from '@utils/permissions';

const getUser = async () => {
  const { getState, setState: setTokenState } = useTokenStore;
  const { setState } = useUserStore;
  const { setState: setPermissionState } = usePermissionStore;
  const { token, type } = getState();

  if (token && type === 'auth') {
    try {
      const user = await api.get('/v1/users/me');

      setState({ user });
      setPermissionState({
        permissions: transformPermissions(user?.role?.permissions),
      });

      return user;
    } catch (error) {
      setState({ user: null, otp: null, secret: null, currentRoute: null });
      setTokenState({
        token: null,
        type: null,
        refreshToken: null,
        expiredAt: null,
      });
      setPermissionState({ permissions: null });
      sessionStorage.removeItem('timeoutState');

      return null;
    }
  }

  return null;
};

const logout = async () => {
  const { setState } = useTokenStore;
  const { setState: setUserState } = useUserStore;
  sessionStorage.removeItem('timeoutState');
  setState({ token: null, type: null, refreshToken: null, expiredAt: null });
  setUserState({ user: null, otp: null, secret: null });
};

const loginWithEmailAndPassword = async data => {
  const response = await api.post('/v1/auth/login', data);
  return response;
};

const changeUserPassword = data => {
  return api.post('/v1/auth/update-temp-password', data);
};

const authConfig = {
  userFn: getUser,
  loginFn: async data => {
    const response = await loginWithEmailAndPassword(data);
    return response;
  },
  registerFn: async data => {
    const response = await changeUserPassword(data);
    return response.user;
  },
  logoutFn: logout,
};

export const { useUser, useLogin, useLogout, useRegister, AuthLoader } =
  configureAuth(authConfig);

export const ProtectedRoute = ({ children }) => {
  const { token, type } = useTokenStore();

  if (!token && type !== 'auth') {
    return <Navigate to="/login" replace />;
  }

  return children;
};
```

This implementation:
1. **Gets the User**: Fetches the user's information using the stored token
2. **Handles Login**: Sends login credentials to the API and stores the response
3. **Handles Logout**: Clears authentication state
4. **Protects Routes**: Redirects unauthenticated users to the login page

## Token Refresh

Token refresh is implemented in `src/features/auth/api/refresh-token.js`:

```javascript
// From src/features/auth/api/refresh-token.js
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@lib/apiClient';
import { useTokenStore } from '@src/store/authStore';

export const refreshToken = async () => {
  const { getState, setState } = useTokenStore;
  const { refreshToken } = getState();

  const response = await api.post(
    '/v1/auth/token',
    {},
    {
      headers: {
        Authorization: `Bearer ${refreshToken}`,
      },
    },
  );

  const { accessToken, refreshToken: newRefreshToken, expiredAt } = response;

  setState({
    token: accessToken,
    refreshToken: newRefreshToken,
    expiredAt,
  });

  return response;
};
```

This function:
1. Gets the refresh token from the store
2. Sends a request to the API to get a new access token
3. Updates the store with the new tokens

## Error Handling

Error handling is implemented at multiple levels:

### 1. API Client Error Handling

The API client includes a response interceptor that handles errors:

```javascript
// From src/lib/apiClient.js
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    const { setState } = useTokenStore;
    const { setState: setUserState } = useUserStore;
    const { setState: setPermissionState } = usePermissionStore;

    if (error.response && error.response.status === 401) {
      setState({
        token: null,
        type: null,
        refreshToken: null,
        expiredAt: null,
      });
      setUserState({ user: null, otp: null, secret: null, currentRoute: null });
      setPermissionState({ permissions: null });
      sessionStorage.removeItem('timeoutState');
    } else {
      console.error('API Error:', error);
    }

    return Promise.reject(error);
  },
);
```

This interceptor:
1. Handles 401 Unauthorized errors by clearing authentication state
2. Logs other errors to the console
3. Propagates the error to the caller

### 2. React Query Error Handling

React Query provides error handling through its hooks:

```javascript
// Example of React Query error handling
const { data, error, isLoading } = useGetCompanies(params);

if (isLoading) {
  return <Spinner />;
}

if (error) {
  return <ErrorMessage error={error} />;
}

return <Table data={data} />;
```

### 3. Component-Level Error Handling

Components can handle errors from API calls:

```javascript
// Example of component-level error handling
const handleSubmit = async values => {
  try {
    await createCompany.mutateAsync(values);
    showNotification({
      type: 'success',
      message: 'Company created successfully',
    });
  } catch (error) {
    showNotification({
      type: 'error',
      message: error.response?.data?.message || 'Failed to create company',
    });
  }
};
```

### 4. Global Error Handling

The application includes a global error boundary:

```jsx
// From src/app/provider.jsx
<ErrorBoundary FallbackComponent={MainErrorFallback}>
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      {/* ... */}
    </QueryClientProvider>
  </HelmetProvider>
</ErrorBoundary>
```

This error boundary catches unhandled errors and displays a fallback UI.

## API Integration Issues

### 1. Inconsistent Error Handling

Error handling is inconsistent across the application, with some components handling errors differently than others.

### 2. Limited API Documentation

API functions lack documentation explaining their purpose, parameters, and return values.

### 3. Duplicate API Functions

Some API functions are duplicated across features, leading to code duplication.

### 4. Limited API Mocking

The application has limited support for API mocking, making it difficult to develop and test without a backend.

### 5. Inconsistent Parameter Handling

API functions handle parameters inconsistently, with some using objects and others using individual parameters.

## Recommendations

### 1. Standardize Error Handling

Implement a consistent error handling approach:

```javascript
// API error handling utility
export const handleApiError = (error, options = {}) => {
  const { showNotification, defaultMessage = 'An error occurred' } = options;
  
  const message = error.response?.data?.message || defaultMessage;
  
  if (showNotification) {
    showNotification({
      type: 'error',
      message,
    });
  }
  
  return {
    message,
    status: error.response?.status,
    data: error.response?.data,
  };
};

// Usage in components
try {
  await createCompany.mutateAsync(values);
  showNotification({
    type: 'success',
    message: 'Company created successfully',
  });
} catch (error) {
  handleApiError(error, { showNotification });
}
```

### 2. Add API Documentation

Document each API function with its purpose, parameters, and return values:

```javascript
/**
 * Get companies
 * 
 * Fetches a list of companies with pagination, filtering, and sorting.
 * 
 * @param {Object} params - The request parameters
 * @param {number} [params.page=1] - The page number
 * @param {number} [params.limit=10] - The number of items per page
 * @param {boolean} [params.paginate] - Whether to paginate the results
 * @param {Object} [params.filterBy] - The filter criteria
 * @param {Object} [params.sortBy] - The sort criteria
 * @returns {Promise<Object>} The companies data
 */
export const getCompanies = ({
  page = 1,
  limit = 10,
  paginate,
  filterBy,
  sortBy,
}) => {
  // ... implementation
};
```

### 3. Create API Service Layer

Create an API service layer to centralize API functions and reduce duplication:

```javascript
// API service for companies
export const companyService = {
  /**
   * Get companies
   * 
   * @param {Object} params - The request parameters
   * @returns {Promise<Object>} The companies data
   */
  getCompanies: (params) => {
    return api.get('/v1/companies', { params });
  },
  
  /**
   * Get company details
   * 
   * @param {number} id - The company ID
   * @returns {Promise<Object>} The company data
   */
  getCompanyDetails: (id) => {
    return api.get(`/v1/companies/${id}`);
  },
  
  /**
   * Update company
   * 
   * @param {number} id - The company ID
   * @param {Object} data - The company data
   * @returns {Promise<Object>} The updated company
   */
  updateCompany: (id, data) => {
    return api.put(`/v1/companies/${id}`, data);
  },
};

// React Query hooks
export const useGetCompanies = (params, config = {}) => {
  return useQuery({
    queryKey: ['companies', params],
    queryFn: () => companyService.getCompanies(params),
    ...config,
  });
};
```

### 4. Implement API Mocking

Implement a comprehensive API mocking solution:

```javascript
// API mocking setup
import { setupWorker, rest } from 'msw';
import { env } from '@config/env';

const handlers = [
  rest.get(`${env.API_URL}/v1/companies`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: [
          { id: 1, name: 'Company 1' },
          { id: 2, name: 'Company 2' },
        ],
        total: 2,
        page: 1,
        limit: 10,
      }),
    );
  }),
  
  // ... other handlers
];

export const startMockServer = () => {
  if (env.ENABLE_API_MOCKING) {
    const worker = setupWorker(...handlers);
    worker.start();
    console.log('Mock server started');
  }
};
```

### 5. Standardize Parameter Handling

Adopt a consistent approach to parameter handling:

```javascript
// Before: Inconsistent parameter handling
export const getCompany = (id) => {
  return api.get(`/v1/companies/${id}`);
};

export const getCompanies = ({
  page,
  limit,
  paginate,
  filterBy,
  sortBy,
}) => {
  return api.get('/v1/companies', { params: { page, limit, paginate, filterBy, sortBy } });
};

// After: Consistent parameter handling
export const getCompany = (params) => {
  const { id, ...queryParams } = params;
  return api.get(`/v1/companies/${id}`, { params: queryParams });
};

export const getCompanies = (params) => {
  return api.get('/v1/companies', { params });
};
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
