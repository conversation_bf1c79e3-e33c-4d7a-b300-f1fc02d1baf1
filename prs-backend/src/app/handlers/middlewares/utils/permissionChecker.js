/**
 * Permission checker utility
 * 
 * This module provides functions for checking user permissions
 */
const { ErrorFactory } = require('../../../errors');

/**
 * Default error message for permission denied
 */
const DEFAULT_PERMISSION_ERROR_MSG = 'You do not have permission to perform this action';

/**
 * Checks if a user has the required permissions
 * 
 * @param {Object} user - User object with role and permissions
 * @param {Array|Object} requiredPermissions - Required permissions
 * @param {boolean} requireAll - Whether all permissions are required
 * @returns {boolean} - True if user has permissions, false otherwise
 */
function hasPermission(user, requiredPermissions, requireAll = false) {
  // Handle case where user has no role or permissions
  if (!user || !user.role || !user.role.permissions) {
    return false;
  }
  
  // Convert single permission to array
  const permissions = Array.isArray(requiredPermissions)
    ? requiredPermissions
    : [requiredPermissions];
  
  // Support OR & AND permission logic
  const checkPermissions = requireAll ? 'every' : 'some';
  
  return permissions[checkPermissions]((requiredPermission) =>
    user.role.permissions.some(
      (permission) =>
        permission.module === requiredPermission.module &&
        permission.action === requiredPermission.action,
    ),
  );
}

/**
 * Creates a permission checker middleware
 * 
 * @param {Array|Object} requiredPermissions - Required permissions
 * @param {Object} options - Options
 * @param {boolean} options.requireAll - Whether all permissions are required
 * @param {string} options.errorMessage - Custom error message
 * @returns {Function} - Permission checker middleware
 */
function createPermissionChecker(requiredPermissions, options = {}) {
  const { 
    requireAll = false,
    errorMessage = DEFAULT_PERMISSION_ERROR_MSG,
  } = options;
  
  return async function checkPermissionMiddleware(request, _reply) {
    const logger = this.diScope.resolve('logger') || this.log;
    const { userFromToken } = request;
    
    try {
      // Check if user exists and has required permissions
      if (!hasPermission(userFromToken, requiredPermissions, requireAll)) {
        throw ErrorFactory.authorization(errorMessage);
      }
    } catch (error) {
      logger.warn('Permission denied', {
        userId: userFromToken?.id,
        username: userFromToken?.username,
        requiredPermissions,
        requireAll,
      });
      
      throw error;
    }
  };
}

module.exports = {
  DEFAULT_PERMISSION_ERROR_MSG,
  hasPermission,
  createPermissionChecker,
};
