const { ZodError } = require('zod');
const {
  PAGINATION_DEFAULTS,
} = require('../../../domain/constants/deliveryReceiptConstants');
const {
  createDeliveryReceiptSchema,
} = require('../../../domain/entities/deliveryReceiptEntity');

class DeliveryReceiptController {
  constructor({ deliveryReceiptService, clientErrors }) {
    this.deliveryReceiptService = deliveryReceiptService;
    this.clientErrors = clientErrors;
  }

  async createDeliveryReceipt(request, reply) {
    const { body, userFromToken } = request;
    createDeliveryReceiptSchema.parse(body);
    const deliveryReceipt = await this.deliveryReceiptService.createDeliveryReceipt(
      body, userFromToken, request.transaction
    );

    return reply.status(201).send({ ...deliveryReceipt });
  }

  async getDeliveryReceiptById(request, reply) {
    const { type } = request.query;
    const id = parseInt(request.params.id);

    const deliveryReceipt =
      await this.deliveryReceiptService.getDeliveryReceiptById(
        id,
        type
      );

    if (!deliveryReceipt) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt with id of ${request.params.id} not found`,
      });
    }

    return reply.status(200).send({ ...deliveryReceipt });
  }

  async getDeliveryReceiptsFromRequisitionId(request, reply) {
    const { query, params } = request;
    const deliveryReceipts = await this.deliveryReceiptService.getDeliveryReceiptsFromRequisitionId(
      params.requisitionId,
      {
        search: query.search,
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
      }
    );

    return reply.status(200).send({ ...deliveryReceipts });
  }

  async getDeliveryReceiptsFromPurchaseOrderId(request, reply) {
    const { params, query } = request;
    const { noInvoice } = query;
    
    const deliveryReceipts = await this.deliveryReceiptService.getDeliveryReceiptsFromPurchaseOrderId(
      params.purchaseOrderId,
      { 
        noInvoice: noInvoice === 'true'
      }
    );

    return reply.status(200).send({ ...deliveryReceipts });
  }

  async getDeliveryReturnsFromRequisitionId(request, reply) {
    const { query, params } = request;
    const deliveryReturns = await this.deliveryReceiptService.getDeliveryReturnsFromRequisitionId(
      params.requisitionId,
      {
        search: query.search,
        page: query.page,
        limit: query.limit,
        sortBy: query.sortBy,
      }
    );

    return reply.status(200).send({ ...deliveryReturns });
  }

  async updateDeliveryReceiptById(request, reply) {
    const { body, params, userFromToken } = request;
    const deliveryReceipt =
      await this.deliveryReceiptService.updateDeliveryReceiptById(
        params.id,
        body,
        userFromToken,
        request.transaction
      );

    if (!deliveryReceipt) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt with id of ${request.params.id} not found`,
      });
    }

    return reply.status(200).send({ 
      id: deliveryReceipt.id
    });
  }

  async getDeliveryReceiptItemHistory(request, reply) {
    const historyRecords =
      await this.deliveryReceiptService.getDeliveryReceiptItemHistory(
        request.params.itemId,
        {
          page: request.query.page || PAGINATION_DEFAULTS.PAGE,
          limit: request.query.limit || PAGINATION_DEFAULTS.LIMIT,
        },
      );

    return reply.status(200).send({ ...historyRecords });
  }

  async getDeliveryReceiptItems(request, reply) {
    const { query, params } = request;
    const deliveryReceiptItems = await this.deliveryReceiptService.getDeliveryReceiptItems(
      params.id,
      query
    );

    return reply.status(200).send({ ...deliveryReceiptItems });
  }
}

module.exports = DeliveryReceiptController;
