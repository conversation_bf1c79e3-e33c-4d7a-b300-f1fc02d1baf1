Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,STATUS,Remarks,Defects
Update Non-RS Form,,,,,,,,,,,
1306-001,Update Non-RS Form,Critical,Verify Non-RS Form Page,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Go to Dashboard
2. Click Create New Non-RS button
3. Validate if page loads",,3. Page should load without any errors,Ghienel_Sprint1_Test Result,Passed,,
1306-002,Update Non-RS Form,Critical,Verify Section Sequence,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate sections displayed",,"2. Section sequence should be displayed as:
    a. Non-RS Details
    b. Charge To
    c. Items Table
    d. Attachment and Notes Section",,Passed,,
1306-003,Update Non-RS Form,Critical,Verify Category field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Category field displayed in the Non-RS Details Section",,"2. a. Should be a Drop-down Field with the following Options
           i) Company
           ii) Association
           iii) Project
    b. Field is Required",,Passed,,
1306-004,Update Non-RS Form,Critical,Verify Company field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Company field displayed in the Non-RS Details Section",,"2. a. List of Companies Synced in Company Management if Company is selected in the Category
    b. List of Associations created in Company Management if Association is selected in the Category
    c. Field is Required",,Passed,,
1306-005,Update Non-RS Form,Critical,Verify Project field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Project field displayed in the Non-RS Details Section",,"2. a. List of Projects Synced in Project Management
          i) Should be related to the Company selected in the Company Field
             a) If selected before the Company Field, should autofill the Company Field once the Project has been selected
    b. Field is Required if the selected Category is Project",,Passed,,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1484
1306-006,Update Non-RS Form,Critical,Verify Department field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Department field displayed in the Non-RS Details Section",,"2. a. List of Departments Synced in Department Management
          i) Should be autofilled by the User's Department
          ii) Should allow the User to change the select Department
    b. Field is Required",,Passed,,
1306-007,Update Non-RS Form,Critical,Verify Supplier field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Supplier field displayed in the Non-RS Details Section",,"2. a. List of Active Suppliers from Supplier Management
    b. Field is Required",,Passed,,
1306-008,Update Non-RS Form,Critical,Verify Payable To field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Payable To field displayed in the Non-RS Details Section",,"2. a. Should be Alphanumeric and Special Characters except Emojis
    b. Should allow maximum of 100 Characters
    c. Field is Required",,Passed,,
1306-009,Update Non-RS Form,Critical,Verify Invoice No field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Invoice No field displayed in the Non-RS Details Section",,"2. a. Should be Alphanumeric and Special Characters except Emojis
    b. Should allow maximum of 100 Characters
    c. Field is Required",,Passed,,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1486
1306-010,Update Non-RS Form,Critical,Verify Supplier Invoice Date field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Supplier Invoice Date  field displayed in the Non-RS Details Section",,"2. a. Should be a Date Picker that will allow Dates for the Current and Previous Dates
    b. Field is Required",,Passed,,
1306-011,Update Non-RS Form,Critical,Verify Supplier Invoice Amount field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Supplier Invoice Amount field displayed in the Non-RS Details Section",,"2. a. Should have a Peso Sign Placeholder
    b. Should allow numeric values of 2-Decimal Places
    c. Field is Required",,Passed,,
1306-012,Update Non-RS Form,Critical,Verify Group Discount field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Group Discount field displayed in the Non-RS Details Section",,"2. a. Should have an Option if it is a Fixed Amount or Percentage
    b. Should deduct to the Total of the Non-RS Payment Form
    c. Field is Optional",,Passed,,
1306-013,Update Non-RS Form,Critical,Verify Invoice Attachment field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Invoice Attachment field displayed in the Non-RS Details Section",,"2. a. Should allow to upload a maximum of 25MB per File
    b. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV
    c. Field is Required",,Passed,,
1306-014,Update Non-RS Form,Critical,Verify Invoice Note field in Non-RS Details Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Notes field displayed in the Non-RS Details Section",,"2. a. Should allow Alphanumeric Characters and Special Characters except Emojis
    b. Should have a Maximum of 100 Characters
    c. Field is Optional",,Passed,,
1306-015,Update Non-RS Form,Critical,Verify Charge To Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate fields displayed in the Charge To Section",,"2. Should display the following:
    a. Should set the Charge To Fields as Optional
    b. Should allow the User to select a different Charge To depending on the situation
    c. Should update the behavior of the Charge To(Category)
       i. If Charge To(Category) is Company
          i) Should get the selected Company from Request Details
       ii. If Charge To(Category) is Association
          i) Should get the selected Company-Association from Request Details
       iii. If Charge To(Category) is Project
          i) Should get the selected Project from Request Details
       iv. If Charge To(Category) is Supplier
          i) Should require the Requester to select a specific Supplier
    d. Should retain the filtering of Options of the Charge To based on the Charge To(Category)
    e. Should rename the Charge To(Client) to Charge To",,Passed,,
1306-016,Update Non-RS Form,Critical,Verify Search field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Search field displayed in the Item/s Section",,"2. a. Allow searching for added Item Name in the Items Table
    b. Allow searching for a Keyword
    c. Triggered by clicking Search Button
    d. Should clear the Search Field and Filtering of Table by clicking Clear Button",,Passed,,
1306-017,Update Non-RS Form,Critical,Verify Item field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Item field displayed in the Item/s Section",,"2. a. Should be Alphanumeric and Special Character except Emojis
    b. Maximum of 100 Characters",,Passed,,
1306-018,Update Non-RS Form,Critical,Verify Unit field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Unit field displayed in the Item/s Section",,"2. a. Should display the default Options for the Unit
               a) Should allow Searching for the Options
               b) pc
               c) lot
               d) pack
               e) unit
               f) set
               g) m
               h) gal
               i) liter
               j) bundle
               k) kilo
               l) yard
               m) ream
               n) box
               o) bottle
               p) pair
               q) roll
               r) dozen
               s) can
               t) unit
               u) tin",,Passed,,
1306-019,Update Non-RS Form,Critical,Verify Qty field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Qty field displayed in the Item/s Section",,"2. a. Numbers Only
    b. Maximum of 5 Characters",,Passed,,
1306-020,Update Non-RS Form,Critical,Verify Amount field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Amount field displayed in the Item/s Section",,"2. a. Numbers only with 2 Decimal Places
    b. Maximum of 10 Characters",,Passed,,
1306-021,Update Non-RS Form,Critical,Verify Discount field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Discount field displayed in the Item/s Section",,"2. a. Can only choose between fixed or percentage button
    b. Text field beside button",,Passed,,
1306-022,Update Non-RS Form,Critical,Verify Add Item Button in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Populate fields
3. Click Add Item button",,3. Should add item upon clicking,,Passed,,
1306-023,Update Non-RS Form,Critical,Verify Item field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Validate Item field displayed in the Item Table Section",,"2. a. Should display Item name
    b. Should sort the ""Items"" to - A-Z, Z-A when sorting is clicked",,Passed,,
1306-024,Update Non-RS Form,Critical,Verify Unit field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Validate Unit field displayed in the Item Table Section",,2. Should display chosen unit in Item/s section,,Passed,,
1306-025,Update Non-RS Form,Critical,Verify Qty field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Validate Qty field displayed in the Item Table Section",,"2. a. Should display the correct quantity of the item
    b. Should sort in ascending or descending order based on Quantity",,Passed,,
1306-026,Update Non-RS Form,Critical,Verify Amount field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Validate Amountfield displayed in the Item Table Section",,"2. a. Should display the correct price of the item
    b. Should sort in ascending or descending order based on Amount",,Passed,,
1306-027,Update Non-RS Form,Critical,Verify Discount field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Validate Discount field displayed in the Item Table Section",,"2. a. Should display the correct discount of the item
    b. Should sort in ascending or descending order based on Discount",,Passed,CITYLANDPRS-1508,
1306-028,Update Non-RS Form,Critical,Verify Discounted Price field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Validate Discounted Price field displayed in the Item Table Section",,"2. a. Should display the correct discounted price of the item computed depending on the Amount and the entered Discount
    b. Should sort in ascending or descending order based on Price",,Passed,,
1306-029,Update Non-RS Form,Critical,Verify Actions field in Item Table Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. An item has been added","1. Navigate to Item Table
2. Click trash button",,2. Should be able to remove item upon clicking button,,Passed,,
1306-030,Update Non-RS Form,Critical,Verify Item Table Pagination,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form
4. More than 10 items have been added","1. Navigate to Item Table
2. Validate Item Table Section
3. Click Page numbers",,"2. Max number of items display per page is 10
3. a. Should be able to click page numbers
    b. Displays different items per different page numbers",,Passed,,
1306-031,Update Non-RS Form,Critical,Verify Additional Attachment field in Attachment and Notes Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Additional Attachment field displayed in the Attachment and Notes Section",,"2. a. Should allow to upload a maximum of 25MB per File
    b. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV
    c. Field is Optional",,Passed,,
1306-032,Update Non-RS Form,Critical,Verify Additional Notes field in Attachment and Notes Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Validate Additional Notes field displayed in the Attachment and Notes Section",,"2. a. Should allow Alphanumeric Characters and Special Characters except Emojis
    b. Should have a Maximum of 100 Characters
    c. Field is Optional",,Passed,,
1306-033,Update Non-RS Form,Critical,Verify Non-RS Payment Request Computation,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Populate fields
3. Add items (more than 1 item) to Item Table
4. Add Group Discount
5. Validate Computed Amount
6. Click Submit",,"5. Should update the Non-RS Payment Request Computation
    a. Amount (+Other Charges) - Sum of the (Price per Unit x Quantity)
    b. Discounts - Sum of all of the Discounts and the Group Discount if available
    c. Total Amount - Amount (+Other Charge) - Discounts(Item and Group Discount)

6. Should validate if the Total Amount Computation is equal with the Supplier Invoice Amount before Submission of the Non-RS, may it be Draft or not
     a. If not equal, should not allow the User to proceed and have them prompted of the discrepancy",,Passed,CITYLANDPRS-1490,
1306-034,Update Non-RS Form,Minor,Verify if Invoice No Accepts Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Invoice No
2. Enter emojis into the field
3. Attempt to submit the form",,"2. Emojis should not be allowed as input
3. Should not be allowed to submit form",,Passed,,
1306-035,Update Non-RS Form,Minor,Verify Invoice No maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Invoice No
2. Enter more than 100 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 100 characters
3. Should not be allowed to submit form",,Failed,CITYLANDPRS-1686,
1306-036,Update Non-RS Form,Minor,Verify if Payable To Accepts Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Payable To
2. Enter emojis into the field
3. Attempt to submit the form",,"2. Emojis should not be allowed as input
3. Should not be allowed to submit form",,Passed,,
1306-037,Update Non-RS Form,Minor,Verify Payable To maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Payable To
2. Enter more than 100 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 100 characters
3. Should not be allowed to submit form",,Passed,,
1306-038,Update Non-RS Form,High,Verify if Future Dates can be Selected,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Supplier Invoice Date
2. Click on Date Picker
3. Attempt to select date later than current date",,"2. Date Picker should display
3. Should be a Date Picker that will allow Dates for the Current and Previous Dates",,Passed,,
1306-039,Update Non-RS Form,High,Verify if Supplier Invoice Amount Accepts Letters and Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Supplier Invoice Amount
2. Enter letters and emojis into the field
3. Attempt to submit the form",,"2. Should only allow numbers
3. Should not be allowed to submit form",,Passed,,
1306-040,Update Non-RS Form,High,Verify Decimals in Supplier Invoice Amount,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Supplier Invoice Amount
2. Enter more than 2 decimal places
3. Attempt to submit the form",,"2. Should only allow 2 decimal places
3. Should not be allowed to submit form",,Passed,,
1306-041,Update Non-RS Form,High,Verify if Group Discount Field in Item/s Section Accepts Letters and Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Group Discount
2. Enter letters and emojis into the field
3. Attempt to submit the form",,"2. Should only allow numbers
3. Should not be allowed to submit form",,Passed,,
1306-042,Update Non-RS Form,High,Verify Decimals in Group Discount field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Group Discount
2. Select Fixed Discount
3. Enter more than 2 decimal places
4. Attempt to submit the form",,"3. Should only allow 2 decimal places
4. Should not be allowed to submit form",,Passed,,
1306-043,Update Non-RS Form,Minor,Verify if Invoice Attachment field Accepts more than 25MB per file,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Invoice Attachment
2. Click Select Attachments
3. Select a file larger than 25MB",,3. Should allow to upload a maximum of 25MB per File,,Passed,,
1306-044,Update Non-RS Form,Minor,Verify if Invoice Attachment field Accepts Other File Formats,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Invoice Attachment
2. Click Select Attachments
3. Select a file that is not a PNG, JPG, JPEG, PDF, Excel, CSV",,"3. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV",,Passed,,
1306-045,Update Non-RS Form,Minor,Verify if Invoice Note Accepts Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Invoice Note
2. Enter emojis into the field
3. Attempt to submit the form",,"2. Emojis should not be allowed as input
3. Should not be allowed to submit form",,Passed,,
1306-046,Update Non-RS Form,Minor,Verify Invoice Note maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Invoice Note
2. Enter more than 100 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 100 characters
3. Should not be allowed to submit form",,Passed,,
1306-047,Update Non-RS Form,Minor,Verify if Item field in Item/s Section Accepts Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Item
2. Enter emojis into the field
3. Attempt to submit the form",,"2. Emojis should not be allowed as input
3. Should not be allowed to submit form",,Passed,,
1306-048,Update Non-RS Form,Minor,Verify Item field in Item/s Section maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Item
2. Enter more than 100 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 100 characters
3. Should not be allowed to submit form",,Passed,,
1306-049,Update Non-RS Form,High,Verify if Qty Field in Item/s Section Accepts Letters and Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Qty
2. Enter letters and emojis into the field
3. Attempt to submit the form",,"2. Should only allow numbers
3. Should not be allowed to submit form",,Passed,,
1306-050,Update Non-RS Form,MInor,Verify Qty field in Item/s Section maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Qty
2. Enter more than 5 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 5 characters
3. Should not be allowed to submit form",,Passed,,
1306-051,Update Non-RS Form,High,Verify if Amount Field in Item/s Section Accepts Letters and Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Amount
2. Enter letters and emojis into the field
3. Attempt to submit the form",,"2. Should only allow numbers
3. Should not be allowed to submit form",,Passed,,
1306-052,Update Non-RS Form,Minor,Verify Amount field in Item/s Section maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Amount
2. Enter more than 10 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 10 characters
3. Should not be allowed to submit form",,Passed,,
1306-053,Update Non-RS Form,High,Verify Decimals in Amount field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Amount
2. Enter more than 2 decimal places
3. Attempt to submit the form",,"2. Should only allow 2 decimal places
3. Should not be allowed to submit form",,Passed,,
1306-054,Update Non-RS Form,High,Verify if Discount Field in Item/s Section Accepts Letters and Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Discount
2. Enter letters and emojis into the field
3. Attempt to submit the form",,"2. Should only allow numbers
3. Should not be allowed to submit form",,Passed,,
1306-055,Update Non-RS Form,High,Verify Decimals in Discount field in Item/s Section,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Item/s Section > Discount
2. Select Fixed Discount
3. Enter more than 2 decimal places
4. Attempt to submit the form",,"3. Should only allow 2 decimal places
4. Should not be allowed to submit form",,Passed,,
1306-056,Update Non-RS Form,Minor,Verify if Additional Attachments field Accepts more than 25MB per file,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Attachment and Notes Section > Additional Attachments
2. Click Select Attachments
3. Select a file larger than 25MB",,3. Should allow to upload a maximum of 25MB per File,,Passed,,
1306-057,Update Non-RS Form,Minor,Verify if Additional Attachments field Accepts Other File Formats,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Attachment and Notes Section > Additional Attachments
2. Click Select Attachments
3. Select a file that is not a PNG, JPG, JPEG, PDF, Excel, CSV",,"3. Should only allow the following File Types to be uploaded PNG, JPG, JPEG, PDF, Excel, CSV",,Passed,,
1306-058,Update Non-RS Form,Minor,Verify if Additional Notes Accepts Emojis,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Attachment and Notes Section > Additional Notes
2. Enter emojis into the field
3. Attempt to submit the form",,"2. Emojis should not be allowed as input
3. Should not be allowed to submit form",,Passed,,
1306-059,Update Non-RS Form,Minor,Verify Additional Notes maximum characters,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Attachment and Notes Section > Additional Notes
2. Enter more than 100 characters into the field
3. Attempt to submit the form",,"2. Should only allow maximum of 100 characters
3. Should not be allowed to submit form",,Passed,,
1306-060,Update Non-RS Form,Critical,Verify Adding of New Unit,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Item/s Section > Unit
2. Enter unit not included in options
3. Verify if unit is added to options",,"2. Should be allowed to add new unit
3. Unit should be added to options",,Passed,,
1306-061,Update Non-RS Form,Minor,"Verify if Unit field Accepts Numbers, Special Characters, and Emojis","1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Navigate to Non-RS Details > Item/s Section > Unit
2. Enter a number
3. Attempt to add the item
4. Enter a special character
5. Attempt to add the item
6. Enter a emoji
7. Attempt to add the item",,3. Should only accept letters,,Failed,,CITYLANDPRS-1505
1306-062,Update Non-RS Form,Critical,Verify if Charge To is Optional,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS Details button
2. Populate all fields but leave Charge To blank
3. Click Submit button",,3. Should be submitted successfully with empty Charge To field,,Passed,,
1306-063,Update Non-RS Form,Critical,Verify Non-RS Submission when Supplier Invoice Amount and Total are not Equal,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department","1. Click New Non-RS button
2. Populate fields
3. Add items to Item Table to make up a higher total than Supplier Invoice Amount 
4. Click Submit",,"4. If not equal, should not allow the User to proceed and have them prompted of the discrepancy",,Passed,,CITYLANDPRS-1490
1306-064,Update Non-RS Form,High,Verify if Attachment and Notes section is Optional,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Populate all required fields
2. Leave attachments and notes section blank
3. Attempt to submit",,3. Should be able to submit successfully,,Passed,,CITYLANDPRS-1488
1306-065,Update Non-RS Form,High,Verify  text label of Attachment and Notes section i,"1. User is logged in as any user except Root User
2. Should have set-up the Company, Association, Project, and Department
3. Must be in Non-RS Form","1. Populate all required fields
2. Check text label of attachment and notes",,"3. Should display as ""Additional Attachment"" and Additional Notes""",Cherry Sprint 2 Test Results,Passed,,CITYLANDPRS-1488
Update set of Non-RS Approvers,,,,,,,,,,,
PRS-1307-001,Update set of Non-RS Approvers,High,Verify if users can update Non-RS Approvers.,"1. Non-RS has been created and Submitted.
2. Logged in as any user.
","1. Click Add button in Approvers section.
2. Update Non-RS approvers with the necessary changes.
3. Verify that the Requester's Supervisor is correctly mapped to the Non-RS.
4. Verify that the Department Approvers are updated based on the department selected in the created Non-RS.
",,"2. Should update Non-RS Approvers into:
    a. Requester's Supervisor
    b. Department Approvers
        i. Depending on the Department selected in the created Non-RS

3. The Requester's Supervisor is accurately updated in the Non-RS.
4. The correct Department Approvers are mapped based on the department selected in the Non-RS.",,Passed,,
PRS-1307-002,Update set of Non-RS Approvers,High,Verify that users cannot proceed with Non-RS Approval if the selected department has no approver.,"1. Non-RS has been created and Submitted.
2. Selected department has no assigned approvers.",1. Verify if user can proceed with approvals.,,1. Approvals will not be able to proceed if selected department has no assigned approvers.,,Passed,,
PRS-1307-003,Update set of Non-RS Approvers,Minor,Verify that optional department approvers are not displayed in Non-RS Approvers.,"1. Non-RS has been created and Submitted.
2. Logged in as approver.
3. Selected department has optional approvers only.","1. Click Add button in Approvers section.
2. View the approvers list.",,1. Optional approvers are not shown in Non-RS.,,Passed,,
PRS-1307-004,Update set of Non-RS Approvers,High,Verify that multiple assigned department approvers are displayed in Non-RS Approvers.,"1. Non-RS has been created and submitted.
2. Logged in as Dev.
3. Selected department has 5 or more assigned approvers.",1. View the approvers list.,,1.All assigned approvers for the department should be listed in Non-RS Approvers section.,,Passed,,
Update Adding of Approver behavior for Non-RS Approval,,,,,,,,,,,
PRS-1309-001,Update Adding of Approver behavior for Non-RS Approval,High,Verify clicking through Dashboard page to access the Non-RS Payment Request.,1. User is a Non-RS Approver and the current Approver.,"1. Navigate to Dashboard.
2. Click on Non-RS",,2. Non-RS Payment Request page is displayed.,Test results,Passed,,
PRS-1309-002,Update Adding of Approver behavior for Non-RS Approval,High,"Verify display of Non-Rs Details, Items, and Approvers section.","1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.","1. Check the layout of the page.
2. Navigate to Approvers section.",,"2. Section for Non-RS details, items, and approvers are displayed.",,Passed,,
PRS-1309-003,Update Adding of Approver behavior for Non-RS Approval,High,Verify visibility and Functionality of Add button in the Approvers section.,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Approvers section is visible.",1. Locate and click Add button.,,1. Add Approver modal is displayed.,,Passed,,
PRS-1309-004,Update Adding of Approver behavior for Non-RS Approval,High,Verify Modal displays Search User Field and valid User Types.,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Add Approver Modal is displayed.","1. Locate the Search User field.
2. Fill in the field.
3. Observe the search results.",,"3. Only user with these following roles are shown:
     - Supervisor
     - Assistant Manager
     - Department Head
     - Division Head
     -  Area Staff/Department Secretary",,Passed,,
PRS-1309-005,Update Adding of Approver behavior for Non-RS Approval,Minor,Verify Additional Approvers appears below current Approver with asterist (*).,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Approver is successfully added.","1. Observe the Approver list.
2. Confirm new entry placement and asterisk label.",,"2. New Approver is listed below current Approver with a ""*"" label.",,Passed,,
PRS-1309-006,Update Adding of Approver behavior for Non-RS Approval,Critical,Verify Additional Approver is reflected in the list of Approvers immediately.,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Approver was just added.",1. Check the Approver list after adding.,,1. Added Approver appears immediately.,,Passed,,
PRS-1309-007,Update Adding of Approver behavior for Non-RS Approval,Critical,Verify if adding additional Approver after deletion is allowed.,"1. User is on Non-RS page.
2. Logged in as current Approver.
3. An existing Additonal Approver has been removed.","1. Click Add approver.
2. Add a new user as an approver.",,2. New Additional Approver is successfully added and displayed on the Approvers list.,,Passed,,
PRS-1309-008,Update Adding of Approver behavior for Non-RS Approval,High,Verify Additional Approver can still be added after Non-RS has already been approved by current approver.,"1. User is on Non-RS page.
2. Logged in as current Approver.
3. Non-RS has been approved.","1. Observe the Approvers section.
2. Verify if Add button is displayed.",,2. Add button should no longer be visible.,,Passed,,
PRS-1309-009,Update Adding of Approver behavior for Non-RS Approval,High,Verify Add Approver button is removed if same Additional Approver is already added.,"1. User is on Non-RS page.
2. Logged in as current Approver.
3. Additional Approver already exist on the list.","1. Click Add approver.
2. Search the same user that is already on the list.",,2. Add button should be removed to prevent duplicate addition.,,Passed,,
PRS-1309-010,Update Adding of Approver behavior for Non-RS Approval,High,Verify Default Approver can Edit or Delete Added Approver before approval.,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Added Approver has not approved the request.","1. Click ellipsis  to display Edit/Delete icon.
2. Perform edit or delete action.",,2. Added Approver details can be edited or deleted successfully.,,Failed,CITYLANDPRS-1685,
PRS-1309-011,Update Adding of Approver behavior for Non-RS Approval,Critical,Verify Non-RS cannot proceed to Additional Approver until current Approver approves.,"1. User is a Non-RS Approver and logged in as Added Approver.
2. User is on Non-RS page.
3. Both Approvers added.",1. Attempt to approves as Added Approver before Default Approver approves.,,1. System blocks approval.,,Passed,,
PRS-1309-012,Update Adding of Approver behavior for Non-RS Approval,Critical,Verify next level of approval is blocked until Additional Approver Approves.,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Default Approver has approved, Added Approver has not.",1. Attempt to proceed to next level approval.,,1. Approval to next level is blocked until Added Approver approves.,,Passed,,
PRS-1309-013,Update Adding of Approver behavior for Non-RS Approval,Minor,Verify Add Approver button appears in confirmation message if none is added.,"1. User is a Non-RS Approver and the current Approver.
2. User is on Non-RS page.
3. Ready to approve, no Additional Approver added.","1. Click Approve.
2. Observe confirmation modal.",,1. Add Approver button is displayed in confirmation message.,,Passed,,
PRS-1309-014,Update Adding of Approver behavior for Non-RS Approval,High,Verify Notification is sent to the Added Approver.,"1. User is on Non-RS page.
2. Approver is added successfully.
3. Logged in as Added Approver.",1. Click on Notification Bell.,,"1. Additional Approver should receive the notificaiton with the ff format:

1. Notification with title ""Assigned as an Additional Approver"" is shown with accurate content and date.

Title: 
Assigned as an Additional Approver

Content:
[DEFAULT APPROVER'S NAME] has added you to review the Non-RS Payment Request and have it Approved. Click here or access the Dashboard to proceed in reviewing the Purchase Order.

Date of the Notification: [MMM-DD-YYY]
Nov 08 2024",,Failed,CITYLANDPRS-1682,
PRS-1309-015,Update Adding of Approver behavior for Non-RS Approval,High,Verify correct notification when Non-RS is rejected.,"1.User is on Non-RS page.
2. A Non-RS Payment Request is rejected by an Approver.
3. Logged in as Requestor.",1. Check Notifications.,,"1. Notification with title ""Non-RS Payment Request Rejected"" and correct content is shown.

Title: 
Non-RS Payment Request Rejected

Content:
Non-RS Payment Request has been Rejected by one of the Approvers. Click here to proceed in reviewing the Non-RS Payment Request.

Date of the Notification: [MMM-DD-YYY]
Nov 08 2024",,Failed,CITYLANDPRS-1682,
PRS-1309-016,Update Adding of Approver behavior for Non-RS Approval,High,Verify correct notification when Non-RS is updated by Approver.,"1.User is on Non-RS page.
2. A Non-RS Payment Request is updated by an Approver.
3. Logged in as Requestor.",1. Check Notifications.,,"1. Notification with title ""Non-RS Payment Request updated by an Approver"" is shown.

Title: 
Non-RS Payment Request updated by an Approver

Content:
[NON-RS NUMBER] has been updated by one of the Approvers. Click here to view the updates on the Non-RS Payment Request.

Date of the Notification: [MMM-DD-YYY]
Nov 08 2024",,Failed,CITYLANDPRS-1682,
Allow Adding of Notes before Approval of Non-RS,,,,,,,,,,,
1310-001,Allow Adding of Notes before Approval of Non-RS,Minor,Verify click Non-RS Payment Request Number,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""",,Non-RS Payment Request Number should be clickable,,Not Started,,
1310-002,Allow Adding of Notes before Approval of Non-RS,High,Verify sticky Confirmation Message for Approval,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Verify Sticky Approval Message",,User should see a Sticky Confirmation Message for Approval,,Not Started,,
1310-003,Allow Adding of Notes before Approval of Non-RS,Critical,Verify if Approve Button is clickable,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Verify Approve Button is clickable",,Approve Button should be clickable,,Not Started,,
1310-004,Allow Adding of Notes before Approval of Non-RS,High,Verify if Confirmation Modal is displayed ,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select between Approve or Reject",,Confirmation Modal should be displayed,,Not Started,,
1310-005,Allow Adding of Notes before Approval of Non-RS,Critical,Verify if the Approver is allowed to enter a Notes before they are allowed to Approve,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select Approve Button",,Approver should be allowed to enter a Notes before they are allowed to Approve,,Not Started,,
1310-006,Allow Adding of Notes before Approval of Non-RS,High,Verify if Alphanumeric and Special Characters except Emojis are accepted in Notes,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select Approve",,Alphanumeric and Special Characters except Emojis should be accepted in Notes,,Not Started,,
1310-007,Allow Adding of Notes before Approval of Non-RS,High,Verify if Notes is Maximum of 100 Characters,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select Approve",,Maximum characters Notes should be 100 Characters,,Not Started,,
1310-008,Allow Adding of Notes before Approval of Non-RS,High,Verify if Note before Approval is not Required,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select Approve",,Note before Approval requirement should not be required,,Not Started,,
1310-009,Allow Adding of Notes before Approval of Non-RS,High,Verify click Approve Button to proceed with their Approval,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select Approve",,User should click Approve button to proceed approval,,Not Started,,
1310-010,Allow Adding of Notes before Approval of Non-RS,Minor,"Verify If Cancel Button is clicked, should close the Modal and return back to Non-RS Payment Request Form","1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval","1.  Select ""Non-RS"" to the Nav bar
2. Click ""Non-RS Payment Request Number""
3. Select Approve
4. Click Cancel Button",,Should close the Modal and return back to Non-RS Payment Request Form when cancel button is clicked,,Not Started,,
1310-011,Allow Adding of Notes before Approval of Non-RS,High,Verify if the Approval Note is displayed to the Non-RS's Check Notes Button,"1. User is a Non-RS Approver and the current Approver
2. A Non-RS Payment Request has been submitted for Approval with notes","1. Verify the presence of the “New Attachment” badge.
2. Click on “Check Notes” of the approved Canvass Sheet.
3. Verify visibility of approver.
4. Verify the badge is cleared.",,"1. “New Attachment” badge is displayed.
2. Entered Approval Notes are displayed correctly.
3. Approver name should be displayed correctly.
4. “New Attachment” badge is cleared when viewed.",,Not Started,,
Non-RS Payment Request Download to PDF,,,,,,,,,,,
PRS-066-001,Non-RS Payment Request Download to PDF,Critical,Verify if the Download button is displayed on the Non-RS Payment Request Page,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Click a Non-RS Payment Request Number
2. Validate if a Download Button is visible on the page 
",,"
2. Should have a Download Button when viewing a Non-RS Payment Request",,Not Started,,
PRS-066-001,Non-RS Payment Request Download to PDF,Critical,Verify if the Download button is not visible on the Non-RS Payment Request Page when the Request is in Draft,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Click a Non-RS Payment Request Number which status is in Draft
2. Validate if the Download Button is not visible on the page 
",,"
2. Should not have a Download Button when viewing a Non-RS Payment Request Draft",,Not Started,,
PRS-066-002,Non-RS Payment Request Download to PDF,Critical,Verify if the Print Preview of the Non-RS Payment appears upon clicking the Download Button,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Click the Download Button
2. The Print Preview of the Non-RS Payment Request should appear after clicking
",,"
2. The Print Preview of the Non-RS Payment Request appeared after clicking",,Not Started,,
PRS-066-003,Non-RS Payment Request Download to PDF,High,Verify if the P.R.# Number is visible on the upper left corner of the page,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Print Preview
2. Validate if the correct PR Number is visible on the upper left corner of the page
",,"
2. The correct PR Number was visible on the upper left corner of the page",,Not Started,,
PRS-066-004,Non-RS Payment Request Download to PDF,High,"Verify if a NOTE is visible below the PR Number, indicating processing days of the request ","
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Print Preview
2. Validate if there is a note below the PR Number indicating processing days of the request
",,"
2.  A note below the PR Number, indicating processing days of the request was visible
",,Not Started,,
PRS-066-005,Non-RS Payment Request Download to PDF,High,"Verify if the Date Prepared, Date Needed, and Time Needed is visible on the upper right corner of the page","
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Print Preview
2. Validate if the correct Date Prepared, Date Needed, and Time Needed is visible on the upper right corner of the page
",,"
2. The correct Date Prepared, Date Needed, and Time Needed was visible on the upper right corner of the page
    Date Prepared: XX Mmm YYYY
    Date Needed: XX Mmm YYYY
    Time Needed: 
",,Not Started,,
PRS-066-006,Non-RS Payment Request Download to PDF,High,Verify if all the Request Details Labels and Value Fields are visible on the Print Preview,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Print Preview
2. Validate if all the labels and fields are displayed on the Print Preview inside Request Details

",,"
2. The following labels and fields were displayed on the Print Preview under Request Details
    a. Non-RS Number
    b. Non-RS Status
    c. Category - Category indicated in the Non-RS Payment Request
    d. Date Required - Date indicated in the Non-RS Payment Request
    e.Company - Company indicated in the Non-RS Payment Request
    f.Project - Project indicated in the Non-RS Payment Request
       i. Should display as ""---"" if no Data is indicated
    g. Department - Depatment indicated in the Non-RS Payment Request
    h. Invoice Number - Invoice Number indicated in the Non-RS Payment Request
    h. Payable To
        i. Payable To indicated in the Non-RS Payment Request
",,Not Started,,
PRS-066-007,Non-RS Payment Request Download to PDF,High,Verify if the Items Table is visible on the Print Preview with all the necessary columns,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Print Preview
2. Validate if the Items Table is visible on the Print Preview under the 'Items' label with all the necessary columns
",,"
2. The Items Table was displayed under the 'Items' label with the following Columns
    a. Sequence Number - Incremental depending on the Number of Items Added
    b. Item Name - Item Name added in the Non-RS Payment Request
    c. Quantity- Requested Quantity of the Item
    d. Unit - Unit of the Item
    e. Unit Price - Unit Price indicated for the Item
    f. Discount - Discount indicated for the Item
    g. Total Price - Discounted Unit Price x Requested Quantity
",,Not Started,,
PRS-066-008,Non-RS Payment Request Download to PDF,High,Verify if the items table creates another page when the content exceeds the limit,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Items Table
2.  Validate if another page/s is created and the items table continue on another page
",,"
2. Another page/s was created and the items table continued on another page",,Not Started,,
PRS-066-009,Non-RS Payment Request Download to PDF,Minor,Verify if the items table pagination number is visible with the format 'X - XX of XXXX' and the correct item number is displayed on documents with multiple items,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Items Table
2. Validate that the items table pagination number is visible on the Print review and the correct item number is displayed with the proper format 
(eg., ""1 - 10  of 9999"")
",,"
2. The items table pagination number was visible on the Print review and the correct item number was displayed with the proper format 
(eg., ""1 - 10  of 9999"")
",,Not Started,,
PRS-066-010,Non-RS Payment Request Download to PDF,High,Verify if the order of items displayed have the Non-Steel bar Items first to be followed by the Steelbar Items if the Request is a mix of Steelbars and other OFM Items,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Items Table
2. Validate if the order of items in the Items Table have the Non-Steel bar Items first to be followed by the Steelbar Items if the Request is a mix of Steelbars and other OFM Items
",,"
2. The order of items in the Items Table  have the Non-Steel bar Items first to be followed by the Steelbar Items if the Request is a mix of Steelbars and other OFM Items",,Not Started,,
PRS-066-011,Non-RS Payment Request Download to PDF,Medium,Verify if the order of items with only the steel bar items is displayed properly,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Items Table
2. Validate if the order of steel bars items in the Items Table is sorted properly
",,"
2. The order of steel bars items in the Items Table is sorted properly",,Not Started,,
PRS-066-012,Non-RS Payment Request Download to PDF,Medium,Verify if the order of items with only the non-steel bar items is displayed properly,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Items Table
2. Validate if the order of non-steel bars items in the Items Table is sorted properly
",,"
2. The order of non-steel bars items in the Items Table is sorted properly",,Not Started,,
PRS-066-013,Non-RS Payment Request Download to PDF,High,Verify if the Sum of all of the Items is at the last Row of the Items Table,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Items Table
2. Validate if the correct Sum of all of the Items is at the last Row of the Items Table",,"
2. Should display the correct Sum of all of the Items at the last Row of the Items Table",,Not Started,,
PRS-066-014,Non-RS Payment Request Download to PDF,High,Verify if the Word Format of the Total Amount is correct and corresponds with the Total amount in Number format,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Items Table
2. Validate if the word Format of the Total Amount is correct and corresponds with the Total amount in Number format",,"
2. Should indicate the correct and corresponding Word Format of the Total Amount",,Not Started,,
PRS-066-015,Non-RS Payment Request Download to PDF,High,Verify if there is a section for Checkboxes with all the necessary options,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Print Preview
2. Validate that a section for Checkboxes with all the necessary options is visible on the page below the Items Table",,"
2. Should have a section for Checkboxes with Options for
    a. Pay to Cash
    b. For Encashment
    c. Uncrossed Check
    d. Manager's Check
",,Not Started,,
PRS-066-016,Non-RS Payment Request Download to PDF,High,Verify if there is a section for Supporting Documents with all the necessary options,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Print Preview
2. Validate that a section for Supporting Documents with all the necessary options is visible on the page",,"
2. Should have a section for Supporting Documents with Options for
    a. Attached
    b. To Follow
    c. None Available
    d. Orig RS/OS/CS
",,Not Started,,
PRS-066-017,Non-RS Payment Request Download to PDF,High,Verify if there is a section for Signatures,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1. Observe the Print Preview
2. Validate that a section for Signatures is visible on the page",,"
2. Should have a section for Signatures
    a. Requested By
    b. Endorsed By
    c. Approved By
    d. Countersigned By
",,Not Started,,
PRS-066-018,Non-RS Payment Request Download to PDF,Minor,Verify if the page number is visible with the format 'Page X of X' and the correct page number is displayed on documents with multiple pages,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","
1.  Observe the Print Preview
2. Validate that the page number is visible on the Print review and the correct page number is displayed
",,"
2.  Should have a Page indicator
    e.g. No. of printing made: 2",,Not Started,,
PRS-066-019,Non-RS Payment Request Download to PDF,Medium,Verify the format of the File Name ,"
1. User is logged in as any user type except Root User
2. Non-RS Payment Request has been created and submitted
","

1. Download the file
2. Validate if the File Name combination is correct
",,"
2. Should have a File Name combination of Document Prefix+Date Extracted[YYYYMMDD]+Time Extracted[HHMMSS]
    SAMPLE: NRS-20240728-102039",,Not Started,,