# Domain-Driven Design Workshop Exercises

## Introduction

This document contains exercises designed to help you understand and apply Domain-Driven Design concepts to the PRS (Purchase Requisition System) project. Work through these exercises individually or in groups to deepen your understanding of DDD principles and how they apply to our specific business domain.

## Section 1: Understanding Basic DDD Concepts

### Exercise 1.1: Ubiquitous Language

**Question:** What is the "Ubiquitous Language" in DDD, and why is it important?

**Answer:** Ubiquitous Language is a common language used by both developers and domain experts. It's important because it ensures everyone has the same understanding of domain concepts, reduces misunderstandings, and makes the code more reflective of the business domain.

**Task:** Identify 5 terms in our current codebase that violate the ubiquitous language principle and suggest standardized terms that should be used instead.

| Current Term | Suggested Standardized Term |
|--------------|----------------------------|
| | |
| | |
| | |
| | |
| | |

### Exercise 1.2: Bounded Contexts

**Question:** What is a "Bounded Context" in DDD, and how does it help manage complexity?

**Answer:** A Bounded Context is a boundary within which a particular domain model applies. It helps manage complexity by dividing a large system into smaller, more manageable parts with clear boundaries and responsibilities.

**Task:** For the term "Approval" in our system, describe how its meaning might differ across different bounded contexts:

1. In the Requisition context, "Approval" means: 
2. In the Purchase Order context, "Approval" means:
3. In the Payment context, "Approval" means:

### Exercise 1.3: Entities vs. Value Objects

**Question:** What is the difference between an Entity and a Value Object in DDD?

**Answer:** An Entity has a distinct identity that persists over time, even when its attributes change. A Value Object is defined only by its attributes and has no identity - two value objects with the same attributes are considered equal.

**Task:** Classify each of the following as either an Entity or a Value Object and explain your reasoning:

1. Requisition:
2. Money amount:
3. User:
4. Address:
5. LineItem in a requisition:
6. DeliveryDate:

## Section 2: Identifying Domain Elements in PRS

### Exercise 2.1: Domain Events

**Question:** What are Domain Events in DDD, and why are they useful?

**Answer:** Domain Events are significant occurrences within a domain that domain experts care about. They're useful for capturing business processes, enabling loose coupling between components, and facilitating event-driven architectures.

**Task:** Identify 5 important domain events in the PRS system:

1.
2.
3.
4.
5.

### Exercise 2.2: Aggregates and Aggregate Roots

**Question:** What is an Aggregate and an Aggregate Root in DDD?

**Answer:** An Aggregate is a cluster of associated objects treated as a unit for data changes. An Aggregate Root is the main entity in the group that controls access to all other objects inside the aggregate.

**Task:** For each of the following aggregates in our system, identify the aggregate root and its members:

1. Requisition Aggregate:
   - Root:
   - Members:

2. Purchase Order Aggregate:
   - Root:
   - Members:

3. Supplier Aggregate:
   - Root:
   - Members:

### Exercise 2.3: Domain Services

**Question:** What is a Domain Service in DDD, and when should you use one?

**Answer:** A Domain Service is an operation that doesn't naturally belong to any entity or value object. You should use one when an operation involves multiple domain objects or represents a business process that doesn't fit within a single entity.

**Task:** Identify 3 operations in our system that would be best implemented as domain services:

1.
2.
3.

## Section 3: Implementing DDD Patterns

### Exercise 3.1: Repositories

**Question:** What is a Repository in DDD, and how does it differ from a traditional data access layer?

**Answer:** A Repository provides a collection-like interface for accessing domain objects, hiding the details of data access. Unlike a traditional data access layer, repositories work with domain entities rather than data models, and they focus on the aggregate as a whole rather than individual tables.

**Task:** Write pseudocode for a `RequisitionRepository` interface with methods for the following operations:

```javascript
// Your RequisitionRepository interface here
```

### Exercise 3.2: Factories

**Question:** What is a Factory in DDD, and when would you use one?

**Answer:** A Factory is an object or method responsible for creating complex objects or aggregates. You would use one when the creation logic is complex, involves multiple steps, or needs to enforce invariants.

**Task:** Write pseudocode for a `RequisitionFactory` that creates a new Requisition with items:

```javascript
// Your RequisitionFactory here
```

### Exercise 3.3: Refactoring to Rich Domain Model

**Question:** What is the difference between an Anemic Domain Model and a Rich Domain Model?

**Answer:** An Anemic Domain Model has entities that are little more than data containers with getters and setters, with business logic elsewhere. A Rich Domain Model has entities that contain both data and behavior, encapsulating business rules within the entities themselves.

**Task:** Refactor the following anemic code to use a rich domain model:

```javascript
// Anemic model
class Requisition {
  constructor(id, requesterId, items, status) {
    this.id = id;
    this.requesterId = requesterId;
    this.items = items;
    this.status = status;
  }
  
  getId() { return this.id; }
  getRequesterId() { return this.requesterId; }
  getItems() { return this.items; }
  getStatus() { return this.status; }
  setStatus(status) { this.status = status; }
}

// Service with business logic
class RequisitionService {
  submitRequisition(requisition) {
    if (requisition.getItems().length === 0) {
      throw new Error("Cannot submit empty requisition");
    }
    
    if (requisition.getStatus() !== "DRAFT") {
      throw new Error("Only draft requisitions can be submitted");
    }
    
    requisition.setStatus("PENDING_APPROVAL");
    return requisitionRepository.save(requisition);
  }
}

// Your rich domain model refactoring here
```

## Section 4: Security in DDD

### Exercise 4.1: Security as a Bounded Context

**Question:** How can security be implemented as a bounded context in DDD?

**Answer:** Security can be implemented as its own bounded context with specific models, language, and rules. This separates security concerns from business logic while still allowing them to interact through well-defined interfaces.

**Task:** Design a simple security bounded context for the PRS system, including key entities and their relationships:

```javascript
// Your security bounded context design here
```

### Exercise 4.2: Domain-Specific Access Control

**Question:** What is domain-specific access control, and how does it differ from traditional role-based access control?

**Answer:** Domain-specific access control embeds access rules directly in domain entities based on business rules, rather than relying solely on generic role-based permissions. It considers the state and relationships of domain objects when making access decisions.

**Task:** Implement domain-specific access control for the Requisition entity:

```javascript
// Your domain-specific access control implementation here
```

### Exercise 4.3: Security Mistakes in DDD

**Question:** What are some common security mistakes when implementing DDD?

**Answer:** Common mistakes include mixing security and business logic, ignoring security in the domain model, hardcoding security rules, and treating security as purely an infrastructure concern.

**Task:** Identify and fix the security issues in the following code:

```javascript
// Code with security issues
class Requisition {
  approve(approverId) {
    // Security check in business logic
    if (!this.userHasPermission(approverId, 'approve_requisitions')) {
      throw new Error("User does not have permission to approve");
    }
    
    // Business logic
    this.status = "APPROVED";
  }
  
  userHasPermission(userId, permission) {
    // Database call in domain entity
    return db.UserPermission.findOne({
      where: { userId, permission }
    });
  }
}

// Your fixed code here
```

## Section 5: Refactoring Exercises

### Exercise 5.1: Refactoring Database-Driven Code

**Question:** What are the problems with database-driven design in DDD?

**Answer:** Database-driven design focuses on the database structure rather than the business domain, leading to anemic domain models, tight coupling to the database, and difficulty in expressing complex business rules.

**Task:** Refactor the following database-driven code to follow DDD principles:

```javascript
// Database-driven code
async function approveRequisition(id, approverId) {
  const requisition = await db.Requisition.findByPk(id);
  
  if (requisition.status !== "PENDING") {
    throw new Error("Cannot approve non-pending requisition");
  }
  
  await db.RequisitionApproval.create({
    requisitionId: id,
    approverId,
    date: new Date()
  });
  
  const approvals = await db.RequisitionApproval.findAll({
    where: { requisitionId: id }
  });
  
  if (approvals.length === requisition.requiredApprovals) {
    requisition.status = "APPROVED";
    await requisition.save();
  }
  
  return requisition;
}

// Your DDD refactoring here
```

### Exercise 5.2: Implementing Aggregates

**Question:** How do aggregates help maintain data consistency in DDD?

**Answer:** Aggregates define clear boundaries for data consistency, ensuring that all changes to related objects are made through the aggregate root. This helps maintain invariants and business rules across related objects.

**Task:** Implement a Purchase Order aggregate with items, ensuring that business rules are enforced:

```javascript
// Your Purchase Order aggregate implementation here
```

### Exercise 5.3: Value Objects for Validation

**Question:** How can value objects help with validation in DDD?

**Answer:** Value objects can encapsulate validation logic, ensuring that they are always in a valid state. This moves validation from services to the domain model, making it more consistent and reusable.

**Task:** Implement a Money value object with validation:

```javascript
// Your Money value object implementation here
```

## Section 6: Case Studies and Scenarios

### Exercise 6.1: Requisition Approval Workflow

**Scenario:** The PRS system needs to implement a multi-level approval workflow for requisitions. Different departments have different approval requirements, and approvals must happen in a specific sequence.

**Task:** Design a domain model for this approval workflow using DDD principles:

```javascript
// Your domain model design here
```

### Exercise 6.2: Integrating with External Systems

**Scenario:** The PRS system needs to integrate with an external accounting system. When a purchase order is approved, it needs to be sent to the accounting system.

**Task:** Design a solution using DDD principles that maintains the integrity of both systems:

```javascript
// Your integration design here
```

### Exercise 6.3: Handling Complex Business Rules

**Scenario:** The business has the following rule: "A requisition can only be converted to a purchase order if all items have approved suppliers, the total amount is within the requester's spending limit, and the requisition has been approved by all required approvers."

**Task:** Implement this complex business rule using DDD principles:

```javascript
// Your implementation here
```

## Conclusion

These exercises are designed to help you apply Domain-Driven Design principles to the PRS project. By working through them, you'll gain a deeper understanding of how DDD can help manage complexity, improve communication, and create a more maintainable codebase.

Remember that DDD is not just about coding patterns - it's about understanding the business domain and reflecting that understanding in your code. The most important aspect is the continuous dialogue between developers and domain experts to refine the model and the ubiquitous language.
