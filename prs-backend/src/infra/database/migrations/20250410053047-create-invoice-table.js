'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('invoice_reports', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      ir_number: {
        type: Sequelize.STRING,
      },
      ir_draft_number: {
        type: Sequelize.STRING,
      },
      is_draft: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      requisition_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      purchase_order_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
      },
      company_code: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      supplier_invoice_no: {
        type: Sequelize.STRING,
      },
      issued_invoice_date: {
        type: Sequelize.DATE,
      },
      invoice_amount: {
        type: Sequelize.DECIMAL(10, 2),
      },
      note: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      },
    });

    await queryInterface.addIndex('invoice_reports', ['company_code', 'ir_draft_number'], {
      name: 'invoice_reports_company_code_ir_draft_number_idx',
    });

    await queryInterface.addIndex('invoice_reports', ['company_code', 'ir_number'], {
      name: 'invoice_reports_company_code_ir_number_idx',
    });

    await queryInterface.addColumn('delivery_receipts', 'invoice_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'invoice_reports',
        key: 'id',
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('delivery_receipts', 'invoice_id');
    await queryInterface.removeIndex('invoice_reports', 'invoice_reports_company_code_ir_draft_number_idx');
    await queryInterface.removeIndex('invoice_reports', 'invoice_reports_company_code_ir_number_idx');
    await queryInterface.dropTable('invoice_reports');
  },
};
