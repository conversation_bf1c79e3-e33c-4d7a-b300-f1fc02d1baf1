'use strict';

const { NON_RS_STATUS } = require('../../../domain/constants/nonRSConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('non_requisitions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRsLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'non_rs_letter',
      },
      nonRsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'non_rs_number',
      },
      draftNonRsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_non_rs_number',
      },
      chargeTo: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'charge_to',
      },
      chargeToId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'charge_to_id',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        field: 'created_by',
      },
      dateNeeded: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_needed',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(50),
        defaultValue: NON_RS_STATUS.DRAFT,
      },
      invoiceNo: {
        allowNull: false,
        type: Sequelize.STRING(255),
        field: 'invoice_no',
      },
      payableTo: {
        allowNull: false,
        type: Sequelize.STRING(255),
        field: 'payable_to',
      },
      deliveryFee: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'delivery_fee',
      },
      totalAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_amount',
      },
      totalDiscount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_discount',
      },
      totalDiscountedAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_discounted_amount',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      },
    });

    await queryInterface.addIndex('non_requisitions', ['non_rs_letter'], {
      name: 'non_requisitions_non_rs_letter_index',
    });

    await queryInterface.addIndex('non_requisitions', ['non_rs_number'], {
      name: 'non_requisitions_non_rs_number_index',
    });

    await queryInterface.addIndex('non_requisitions', ['draft_non_rs_number'], {
      name: 'non_requisitions_draft_non_rs_number_index',
    });

    await queryInterface.addIndex(
      'non_requisitions',
      ['charge_to', 'charge_to_id'],
      {
        name: 'non_requisitions_charge_to_index',
      },
    );

    await queryInterface.addIndex('non_requisitions', ['created_by'], {
      name: 'non_requisitions_created_by_index',
    });

    await queryInterface.addIndex('non_requisitions', ['status'], {
      name: 'non_requisitions_status_index',
    });

    await queryInterface.addIndex('non_requisitions', ['invoice_no'], {
      name: 'non_requisitions_invoice_no_index',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('non_requisitions');
  },
};
