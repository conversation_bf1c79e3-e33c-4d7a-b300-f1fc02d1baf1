# Item Management Workflow

This document describes the item management workflow in the PRS system, including the creation, update, and management of items in the system catalog.

## Overview

The Item Management workflow in PRS allows users to:

1. Create new items in the system catalog
2. Update existing items
3. Categorize items
4. Manage item attributes
5. Search and filter items
6. Import and export item data

## Item Creation Workflow

```mermaid
graph TD
    Start([Start]) --> CheckPermission{Has Create Permission?}
    CheckPermission -->|No| End([End])
    CheckPermission -->|Yes| FillForm[Fill Item Form]
    FillForm --> ValidateForm{Form Valid?}
    ValidateForm -->|No| ShowErrors[Show Validation Errors]
    ShowErrors --> FillForm
    ValidateForm -->|Yes| CheckDuplicate{Duplicate Item?}
    CheckDuplicate -->|Yes| ShowDuplicateWarning[Show Duplicate Warning]
    ShowDuplicateWarning --> ConfirmCreate{Confirm Create?}
    ConfirmCreate -->|No| FillForm
    ConfirmCreate -->|Yes| CreateItem[Create Item]
    CheckDuplicate -->|No| CreateItem
    CreateItem --> UpdateCatalog[Update Item Catalog]
    UpdateCatalog --> NotifyUsers[Notify Relevant Users]
    NotifyUsers --> End
```

### Steps in Item Creation

1. **Permission Check**: User must have the `create_item` permission
2. **Form Completion**: User fills out the item creation form with the following information:
   - Item Name
   - Description
   - Category
   - Unit of Measure
   - Specifications
   - Attributes
3. **Validation**: System validates the form data
4. **Duplicate Check**: System checks for duplicate items
5. **Creation**: Item is created in the database
6. **Catalog Update**: Item catalog is updated
7. **Notification**: Relevant users are notified of the new item

## Item Update Workflow

```mermaid
graph TD
    Start([Start]) --> CheckPermission{Has Update Permission?}
    CheckPermission -->|No| End([End])
    CheckPermission -->|Yes| SelectItem[Select Item to Update]
    SelectItem --> LoadItemData[Load Item Data]
    LoadItemData --> EditForm[Edit Item Form]
    EditForm --> ValidateForm{Form Valid?}
    ValidateForm -->|No| ShowErrors[Show Validation Errors]
    ShowErrors --> EditForm
    ValidateForm -->|Yes| CheckChanges{Any Changes?}
    CheckChanges -->|No| End
    CheckChanges -->|Yes| UpdateItem[Update Item]
    UpdateItem --> UpdateCatalog[Update Item Catalog]
    UpdateCatalog --> NotifyUsers[Notify Relevant Users]
    NotifyUsers --> End
```

### Steps in Item Update

1. **Permission Check**: User must have the `update_item` permission
2. **Item Selection**: User selects the item to update
3. **Data Loading**: System loads the current item data
4. **Form Editing**: User edits the item information
5. **Validation**: System validates the form data
6. **Change Detection**: System checks if any changes were made
7. **Update**: Item is updated in the database
8. **Catalog Update**: Item catalog is updated
9. **Notification**: Relevant users are notified of the item update

## Item Categorization Workflow

```mermaid
graph TD
    Start([Start]) --> CheckPermission{Has Categorize Permission?}
    CheckPermission -->|No| End([End])
    CheckPermission -->|Yes| SelectItems[Select Items to Categorize]
    SelectItems --> SelectCategory[Select Category]
    SelectCategory --> ConfirmCategorization{Confirm Categorization?}
    ConfirmCategorization -->|No| End
    ConfirmCategorization -->|Yes| UpdateCategories[Update Item Categories]
    UpdateCategories --> UpdateCatalog[Update Item Catalog]
    UpdateCatalog --> End
```

### Steps in Item Categorization

1. **Permission Check**: User must have the `categorize_item` permission
2. **Item Selection**: User selects one or more items to categorize
3. **Category Selection**: User selects the category to assign
4. **Confirmation**: User confirms the categorization
5. **Update**: Item categories are updated in the database
6. **Catalog Update**: Item catalog is updated

## Item Import Workflow

```mermaid
graph TD
    Start([Start]) --> CheckPermission{Has Import Permission?}
    CheckPermission -->|No| End([End])
    CheckPermission -->|Yes| UploadFile[Upload Item File]
    UploadFile --> ValidateFile{File Valid?}
    ValidateFile -->|No| ShowErrors[Show Validation Errors]
    ShowErrors --> UploadFile
    ValidateFile -->|Yes| ParseFile[Parse File Data]
    ParseFile --> ValidateData{Data Valid?}
    ValidateData -->|No| ShowDataErrors[Show Data Validation Errors]
    ShowDataErrors --> UploadFile
    ValidateData -->|Yes| CheckDuplicates[Check for Duplicates]
    CheckDuplicates --> ShowSummary[Show Import Summary]
    ShowSummary --> ConfirmImport{Confirm Import?}
    ConfirmImport -->|No| End
    ConfirmImport -->|Yes| ImportItems[Import Items]
    ImportItems --> UpdateCatalog[Update Item Catalog]
    UpdateCatalog --> NotifyUsers[Notify Relevant Users]
    NotifyUsers --> End
```

### Steps in Item Import

1. **Permission Check**: User must have the `import_item` permission
2. **File Upload**: User uploads a file containing item data
3. **File Validation**: System validates the file format
4. **Data Parsing**: System parses the file data
5. **Data Validation**: System validates the item data
6. **Duplicate Check**: System checks for duplicate items
7. **Summary Display**: System displays an import summary
8. **Confirmation**: User confirms the import
9. **Import**: Items are imported into the database
10. **Catalog Update**: Item catalog is updated
11. **Notification**: Relevant users are notified of the import

## Frontend Implementation

The Item Management workflow is implemented in the frontend using the following components:

1. **Item List Page**: Displays a list of items with search and filter options
2. **Item Create Form**: Form for creating new items
3. **Item Edit Form**: Form for editing existing items
4. **Item Import Component**: Component for importing items from a file
5. **Item Export Component**: Component for exporting items to a file
6. **Item Category Management**: Component for managing item categories

## Backend Implementation

The Item Management workflow is implemented in the backend using the following components:

1. **Item Controller**: Handles HTTP requests for item management
2. **Item Service**: Contains business logic for item management
3. **Item Repository**: Handles data access for items
4. **Item Entity**: Represents an item in the database
5. **Item Validator**: Validates item data
6. **Item Import/Export Service**: Handles item import and export

## API Endpoints

The following API endpoints are used for item management:

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/items` | GET | Get a list of items |
| `/api/items/{id}` | GET | Get a specific item |
| `/api/items` | POST | Create a new item |
| `/api/items/{id}` | PUT | Update an existing item |
| `/api/items/{id}` | DELETE | Delete an item |
| `/api/items/categories` | GET | Get a list of item categories |
| `/api/items/import` | POST | Import items from a file |
| `/api/items/export` | GET | Export items to a file |

## Permission Requirements

The following permissions are required for item management:

| Permission | Description |
|------------|-------------|
| `view_item` | View items in the catalog |
| `create_item` | Create new items |
| `update_item` | Update existing items |
| `delete_item` | Delete items |
| `categorize_item` | Categorize items |
| `import_item` | Import items from a file |
| `export_item` | Export items to a file |

## Related Workflows

The Item Management workflow is related to the following workflows:

1. **Requisition Workflow**: Items are selected from the catalog when creating requisitions
2. **Canvass Workflow**: Items are included in canvass requests
3. **Purchase Order Workflow**: Items are included in purchase orders
4. **Delivery Workflow**: Items are received and tracked in delivery receipts
