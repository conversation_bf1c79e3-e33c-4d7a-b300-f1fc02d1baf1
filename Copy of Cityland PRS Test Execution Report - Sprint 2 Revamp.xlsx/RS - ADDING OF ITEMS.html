<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1755445270C0" style="width:86px;" class="column-headers-background">A</th><th id="1755445270C1" style="width:219px;" class="column-headers-background">B</th><th id="1755445270C2" style="width:65px;" class="column-headers-background">C</th><th id="1755445270C3" style="width:252px;" class="column-headers-background">D</th><th id="1755445270C4" style="width:233px;" class="column-headers-background">E</th><th id="1755445270C5" style="width:330px;" class="column-headers-background">F</th><th id="1755445270C7" style="width:258px;" class="column-headers-background">H</th><th id="1755445270C8" style="width:100px;" class="column-headers-background">I</th><th id="1755445270C9" style="width:164px;" class="column-headers-background">J</th><th id="1755445270C10" style="width:245px;" class="column-headers-background">K</th><th id="1755445270C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="1755445270R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s2">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1755445270R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="11">REQUISITION SLIP - Adding of Non-OFM Items for existing Non-OFM Items</td></tr><tr style="height: 19px"><th id="1755445270R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">1493-001</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">Critical</td><td class="s4">Verify Clicking a Requisition Slip to display the Requisition Slip Details</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. On the Requisition Slips list page, click on a Requisition Slip row.</td><td class="s4">3. User is navigated to the Requisition Slip Details page.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">1493-002</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Click “Select Actions” Button</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Click the &quot;Select Actions&quot; button.</td><td class="s4">3. A Context menu appears.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">1493-003</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify &quot;Add Items&quot; Option is Not Visible to Non-Purchasing Staff</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Log in as a user who is not assigned as purchasing staff.<br>2. Observe if &quot;Add Items&quot; Button is visible </td><td class="s4">3. &quot;Add Items&quot; Button should not be available</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1755445270R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">1493-004</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Click “Add Items” Option</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1.  Select the “Add Items” option from the actions list.</td><td class="s4">3. The Items Table becomes editable; the &quot;Add Item&quot; button becomes visible and enabled.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="1755445270R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">1493-005</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Editing of Items Table is enabled </td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Confirm the Items Table is now editable.</td><td class="s4">3. Editable controls appear (e.g., remove icon, quantity input, editable fields).</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">1493-006</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">Critical</td><td class="s4">Verify Click “Add Item” Button</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Click the “Add Item” button.</td><td class="s4">3. A modal appears for “Add Items”</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">1493-007</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">Critical</td><td class="s4">Verify if &quot;Add Item&quot; Button is displayed for OFM and OFM Transfer of Materials Type of Request</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Observe if the &quot;Add Item&quot; button is available for OFM and OFM Transfer of Materials Type of Request</td><td class="s4">3. &quot;Add Item&quot; Button for OFM and OFM Transfer of Materials Type of Request should not be available</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">1493-008</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Modal display Non-OFM Items</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Click the “Add Item” button.<br>2. Observe Modal for Non-OFM Items</td><td class="s4">3. List of existing Non-OFM items is displayed.<br></td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">1493-009</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify that the system should NOT allow adding of non-existing Non-OFM Items</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Try searching for a Non-OFM item that doesn’t exist.</td><td class="s4">3. No result is shown.<br>4. “Add” or “Select” button is disabled.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">1493-010</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify  Selected Items should be shown as selected and NOT removable</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Observe Selected Items</td><td class="s4">3. Already selected items from the current Requisition Slip are pre-selected (checkbox ticked).<br>4. Their checkboxes or “Remove” options are disabled.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">1493-011</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Selected Items for the Requisition Slip should be displayed as an Option but are selected</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Observe the item list in the modal.<br>2. Attempt to uncheck or deselect any of the pre-selected items (those already in the RS).</td><td class="s4">3.  Selected Items for the Requisition Slip should be displayed as selected</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">1493-012</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">Minor</td><td class="s4">Verify Untick Checkbox to Remove Item</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1.  Untick a selected checkbox for an item that was not originally selected but was added in the current session.</td><td class="s4">3. The item is removed from the Items Table.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">1493-013</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify  if item is removed using Remove Icon in Items Table, Modal should reflect the unselected state</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Close modal, click “Remove” icon on an item in the Items Table.<br>2.  Reopen the “Add Items” modal.</td><td class="s4">3. The previously removed item is now unselected in the modal.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">1493-014</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">Critical</td><td class="s4">Verify Updated Items should be included in Canvass Sheet creation</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials</td><td class="s4">1. Save changes in the Items Table.</td><td class="s4">3. Items now appear under the list of requisition items eligible for canvassing.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">1493-015</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Update Items in the Table for Canvass Sheet Creation</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. Navigate to the Items Table.<br>2. Perform an action that updates the items (e.g., edit, refresh).</td><td class="s4">4. Items should update correctly and reflect the latest data that can now be included in the creation of a Canvass Sheet.	</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4">1493-016</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Update Items in the Table</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. Navigate to the Items Table.<br>2. Trigger a general update (refresh, sorting, filtering).</td><td class="s4">4. Table should display the latest version of the Items without any stale data.	</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4">1493-017</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Display new Item at the last row of Items List</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. Add a new Item via Add Item flow.<br>2. Observe the Items Table/List.</td><td class="s4">4. Newly added item appears as the last row in the Items Table.	</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s4">1493-018</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Presence of Cancel and Submit Buttons</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. Enter Add Item flow.<br>2. Check for action buttons at the bottom.</td><td class="s4">4. Buttons for &quot;Cancel&quot; and &quot;Submit&quot; are visible and enabled.	</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s4">1493-019</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Submit Button triggers Confirmation Modal</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. Click the &quot;Submit&quot; Button.<br>2. Observe the behavior.</td><td class="s4">4. A Confirmation Modal should pop up asking to confirm addition.</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s4">1493-020</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify clicking Submit adds Items to Requisition Slip</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. In the Confirmation Modal, click &quot;Confirm&quot;.<br>2. Observe post-action behavior.</td><td class="s4">4. Items should be added to the Requisition Slip and marked for Canvass Sheet creation.	</td><td class="s6"></td><td class="s7">Not Started</td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1755445270R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s4">1493-021</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Cancel Button triggers Confirmation Modal</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. Click the &quot;Cancel&quot; Button.<br>2. Observe the behavior.</td><td class="s4">4. A Confirmation Modal should pop up asking to confirm cancellation.	</td><td class="s4"></td><td class="s7">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1755445270R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s4">1493-022</td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verify Confirming Cancel cancels addition of items</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. In the Cancellation Modal, click &quot;Cancel&quot; or &quot;Confirm Cancellation&quot;.<br>2. Observe post-action behavior.</td><td class="s4">4. Add Item operation is aborted; no items are added to the Requisition Slip.	</td><td class="s4"></td><td class="s7">Not Started</td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1755445270R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s4">1493-023</td><td class="s5"><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">REQUISITION SLIP</span><span style="font-family:Poppins,Arial;color:#000000;"> - Adding of Non-OFM Items for existing Non-OFM Items</span></td><td class="s4">High</td><td class="s4">Verifty Hide “Add Item” Button if any Item is already in Canvassing</td><td class="s4">1. Requisition Slip is Approved and has been Assigned to a Purchasing Staff<br>2. Request Type is either Non-OFM or Non-OFM Transfer of Materials<br>3. One of the items in the Requisition Slip is already added to a Canvass Sheet.</td><td class="s4">1. View the Requisition Slip Details.</td><td class="s4">4. “Add Item” button is not visible.</td><td class="s4"></td><td class="s7">Not Started</td><td class="s4"></td><td class="s4"></td></tr></tbody></table></div>