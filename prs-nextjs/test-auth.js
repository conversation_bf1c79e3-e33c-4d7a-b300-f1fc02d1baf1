/**
 * This is a simple test script to verify the authentication setup.
 * It checks if the necessary files and configurations are in place.
 */

const fs = require('fs');
const path = require('path');

// Define the files to check
const filesToCheck = [
  { path: '.env', required: true, description: 'Environment variables' },
  { path: 'prisma/schema.prisma', required: true, description: 'Prisma schema' },
  { path: 'prisma/seed.ts', required: true, description: 'Database seed script' },
  { path: 'src/lib/auth/config.ts', required: true, description: 'NextAuth configuration' },
  { path: 'src/lib/auth/password.ts', required: true, description: 'Password utilities' },
  { path: 'src/lib/auth/otp.ts', required: true, description: 'OTP utilities' },
  { path: 'src/app/api/auth/[...nextauth]/route.ts', required: true, description: 'NextAuth API route' },
  { path: 'src/app/auth/login/page.tsx', required: true, description: 'Login page' },
  { path: 'src/app/app/layout.tsx', required: true, description: 'App layout with authentication check' },
  { path: 'src/components/providers/AuthProvider.tsx', required: true, description: 'Auth provider component' },
];

// Check if files exist
console.log('Checking authentication setup...\n');
let allFilesExist = true;

filesToCheck.forEach(file => {
  const filePath = path.join(__dirname, file.path);
  const exists = fs.existsSync(filePath);
  
  console.log(`${exists ? '✅' : '❌'} ${file.path} - ${file.description}`);
  
  if (!exists && file.required) {
    allFilesExist = false;
  }
});

console.log('\n');

// Check .env file for required variables
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredEnvVars = [
    { name: 'DATABASE_URL', description: 'Database connection string' },
    { name: 'NEXTAUTH_URL', description: 'NextAuth URL' },
    { name: 'NEXTAUTH_SECRET', description: 'NextAuth secret key' },
  ];
  
  console.log('Checking environment variables...\n');
  let allEnvVarsExist = true;
  
  requiredEnvVars.forEach(envVar => {
    const exists = envContent.includes(`${envVar.name}=`);
    console.log(`${exists ? '✅' : '❌'} ${envVar.name} - ${envVar.description}`);
    
    if (!exists) {
      allEnvVarsExist = false;
    }
  });
  
  console.log('\n');
  
  if (allEnvVarsExist) {
    console.log('✅ All required environment variables are set.');
  } else {
    console.log('❌ Some required environment variables are missing.');
  }
} else {
  console.log('❌ .env file not found.');
}

console.log('\n');

// Final result
if (allFilesExist) {
  console.log('✅ Authentication setup is complete. You can now test the login functionality.');
  console.log('   1. Start the development server with: npm run dev');
  console.log('   2. Open the application in your browser at: http://localhost:3000');
  console.log('   3. You should be redirected to the login page.');
  console.log('   4. Try logging in with the test users created by the seed script.');
} else {
  console.log('❌ Authentication setup is incomplete. Please check the missing files.');
}

console.log('\n');
console.log('For more detailed testing instructions, see the test-login.md file.');
