'use client';

import React from 'react';
import { Prism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface CodeBlockProps {
  code: string;
  language?: string;
  filename?: string;
}

export function CodeBlock({ code, language = 'javascript', filename }: CodeBlockProps) {
  return (
    <div className="rounded-lg overflow-hidden mb-6">
      {filename && (
        <div className="bg-gray-800 text-gray-200 px-4 py-2 text-sm font-mono">
          {filename}
        </div>
      )}
      <SyntaxHighlighter
        language={language}
        style={vscDarkPlus}
        customStyle={{
          margin: 0,
          borderRadius: filename ? '0 0 0.5rem 0.5rem' : '0.5rem',
        }}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  );
}
