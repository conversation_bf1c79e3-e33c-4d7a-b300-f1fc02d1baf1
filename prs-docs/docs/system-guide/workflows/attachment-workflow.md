# Attachment System

This document outlines the attachment system in the PRS backend, which handles the management of files attached to various entities.

## Attachment Models

The system defines several attachment models in `attachmentConstants.js`:

| Model | Description |
|-------|-------------|
| CANVASS_SUPPLIER | Attachments for canvass suppliers |
| DELIVERY_RECEIPT | Attachments for delivery receipts |
| DELIVERY_RECEIPT_INVOICE | Attachments for delivery receipt invoices |
| CANVASS | Attachments for canvass sheets |
| CANVASS_REJECT | Attachments for canvass rejections |
| CANVASS_ITEM_SUPPLIERS | Attachments for canvass item suppliers |
| PURCHASE_ORDER | Attachments for purchase orders |
| RS_PAYMENT_REQUEST | Attachments for requisition payment requests |
| RS_PAYMENT_REQUEST_REJECT | Attachments for rejected payment requests |
| REQUISITION | Attachments for requisitions |
| NON_RS | Attachments for non-requisition payments |
| NON_RS_INVOICE | Attachments for non-requisition invoices |
| NON_RS_REJECT | Attachments for non-requisition rejections |
| SUPPLIERS | Attachments for suppliers |
| INVOICE | Attachments for invoices |

## Attachment Structure

Based on the database model (`attachmentModel.js`), attachments have the following structure:

- **Model**: Type of entity the attachment belongs to (from MODELS)
- **Model ID**: ID of the specific entity
- **User ID**: User who uploaded the attachment
- **File Name**: Original name of the file
- **Path**: Storage path of the file
- **Created At**: When the attachment was created
- **Updated At**: When the attachment was last updated

## Entity-Specific Attachment Rules

### Requisition Attachments

- Supporting documents for requisition items
- Approval documentation
- Rejection documentation

### Canvass Attachments

- Supplier quotations
- Product specifications
- Comparative analysis documents

### Purchase Order Attachments

- Signed purchase orders
- Terms and conditions
- Supplier confirmations

### Delivery Report Attachments

- Delivery notes
- Receiving documentation - Supplier's DR

### Invoice Report Attachments

- Invoice notes
- Supplier Invoice

### Payment Request Attachments

- Invoices
- Payment proofs
- Tax documents

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/attachmentController.js`
- **Service**: `src/app/services/attachmentService.js`
- **Repository**: `src/infra/repositories/attachmentRepository.js`
- **Model**: `src/infra/database/models/attachmentModel.js`
- **Constants**: `src/domain/constants/attachmentConstants.js`

### Attachment Upload Implementation

```javascript
// Example attachment upload based on actual code
async function createAttachments({
  model,
  userId,
  modelId,
  parentPath,
  attachments,
}) {
  // Validate model type
  if (!Object.values(MODELS).includes(model)) {
    throw new Error(`Invalid model type: ${model}`);
  }

  // Create directory if it doesn't exist
  const uploadPath = path.join('./upload', parentPath);
  if (!fs.existsSync(uploadPath)) {
    fs.mkdirSync(uploadPath, { recursive: true });
  }

  const results = [];

  // Process each attachment
  for (const attachment of attachments) {
    const { filename, content } = attachment;

    // Generate unique filename
    const uniqueFilename = `${Date.now()}-${filename}`;
    const filePath = path.join(uploadPath, uniqueFilename);

    // Write file to disk
    fs.writeFileSync(filePath, Buffer.from(content, 'base64'));

    // Create database record
    const attachmentRecord = await attachmentRepository.create({
      model,
      modelId,
      userId,
      fileName: filename,
      path: path.join(parentPath, uniqueFilename),
    });

    results.push(attachmentRecord);
  }

  return results;
}
```

### Attachment Retrieval Implementation

```javascript
// Example attachment retrieval based on actual code
async function getAttachments(model, modelId) {
  // Validate model type
  if (!Object.values(MODELS).includes(model)) {
    throw new Error(`Invalid model type: ${model}`);
  }

  // Get attachments from database
  const attachments = await attachmentRepository.findAll({
    where: {
      model,
      modelId,
    },
    order: [['createdAt', 'DESC']],
  });

  // Enhance with full URLs
  return attachments.map(attachment => ({
    ...attachment.toJSON(),
    url: `/api/v1/attachments/${attachment.id}/download`,
  }));
}

// Example file download implementation
async function downloadAttachment(attachmentId) {
  // Get attachment from database
  const attachment = await attachmentRepository.findOne({
    where: { id: attachmentId },
  });

  if (!attachment) {
    throw new Error('Attachment not found');
  }

  // Get file path
  const filePath = path.join('./upload', attachment.path);

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    throw new Error('Attachment file not found');
  }

  return {
    filePath,
    fileName: attachment.fileName,
  };
}
```

## Common Issues and Solutions

### Issue 1: File Upload Failures

**Cause**: File size too large, invalid file type, or storage issues.

**Solution**:
- Check file size limits and validate before upload
- Verify file type restrictions
- Ensure sufficient storage space
- Implement chunked uploads for large files

### Issue 2: Missing Attachments

**Cause**: Files deleted from storage but database records remain, or vice versa.

**Solution**:
- Implement transaction handling for file and database operations
- Run periodic consistency checks
- Implement soft deletion to prevent accidental loss
- Create backup procedures for attachments

### Issue 3: Permission Issues

**Cause**: Users attempting to access attachments they don't have permission for.

**Solution**:
- Implement proper permission checks for attachment operations
- Validate entity access before allowing attachment access
- Log unauthorized access attempts
- Consider implementing attachment-specific permissions
