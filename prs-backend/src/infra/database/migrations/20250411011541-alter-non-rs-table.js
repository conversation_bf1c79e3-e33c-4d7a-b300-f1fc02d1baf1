'use strict';

const {
  NON_RS_DISCOUNT_TYPE,
} = require('../../../domain/constants/nonRSConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('non_requisitions', 'company_id', {
      type: Sequelize.INTEGER,
      // references: {
      //   model: 'companies',
      //   key: 'id',
      // },
      defaultValue: 1, //added default value no need to refresh data
      allowNull: false,
    });
    await queryInterface.addColumn('non_requisitions', 'project_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('non_requisitions', 'department_id', {
      type: Sequelize.INTEGER,
      // references: {
      //   model: 'departments',
      //   key: 'id',
      // },
      defaultValue: 1, //added default value no need to refresh data
      allowNull: false,
    });
    await queryInterface.addColumn('non_requisitions', 'supplier_id', {
      type: Sequelize.INTEGER,
      // references: {
      //   model: 'suppliers',
      //   key: 'id',
      // },
      defaultValue: 1, //added default value no need to refresh data
      allowNull: false,
    });
    await queryInterface.addColumn('non_requisitions', 'category', {
      type: Sequelize.STRING(255),
      defaultValue: 'company', //added default value no need to refresh data
      allowNull: false,
    });

    await queryInterface.changeColumn('non_requisitions', 'charge_to', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.changeColumn('non_requisitions', 'charge_to_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.renameColumn(
      'non_requisitions',
      'date_needed',
      'invoice_date',
    );

    await queryInterface.addColumn('non_requisitions', 'group_discount_type', {
      type: Sequelize.ENUM,
      allowNull: true,
      values: Object.values(NON_RS_DISCOUNT_TYPE),
      field: 'discount_type',
    });

    await queryInterface.addColumn('non_requisitions', 'group_discount_price', {
      type: Sequelize.DOUBLE,
      allowNull: true,
      field: 'group_discount_price',
    });

    await queryInterface.addColumn(
      'non_requisitions',
      'supplier_invoice_amount',
      {
        type: Sequelize.DOUBLE,
        allowNull: false,
        defaultValue: 0.0, //added default value no need to refresh data
        field: 'supplier_invoice_amount',
      },
    );
    await queryInterface.removeColumn('non_requisitions', 'delivery_fee');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('non_requisitions', 'company_id');
    await queryInterface.removeColumn('non_requisitions', 'department_id');
    await queryInterface.removeColumn('non_requisitions', 'project_id');
    await queryInterface.removeColumn('non_requisitions', 'supplier_id');
    await queryInterface.removeColumn('non_requisitions', 'category');
    await queryInterface.removeColumn(
      'non_requisitions',
      'group_discount_price',
    );
    await queryInterface.removeColumn(
      'non_requisitions',
      'supplier_invoice_amount',
    );
    await queryInterface.removeColumn(
      'non_requisitions',
      'group_discount_type',
    );
    await queryInterface.changeColumn('non_requisitions', 'charge_to', {
      type: Sequelize.STRING(255),
      defaultValue: 'company', //added default value no need to refresh data
      allowNull: false,
    });

    await queryInterface.changeColumn('non_requisitions', 'charge_to_id', {
      type: Sequelize.INTEGER,
      defaultValue: 1, //added default value no need to refresh data
      allowNull: false,
    });
    await queryInterface.renameColumn(
      'non_requisitions',
      'invoice_date',
      'date_needed',
    );
    await queryInterface.addColumn('non_requisitions', 'delivery_fee', {
      type: Sequelize.DOUBLE,
      allowNull: true,
      field: 'delivery_fee',
    });
  },
};
