{% extends "base.html" %}

{% block extrahead %}
{{ super() }}
<meta name="theme-color" content="#3f51b5">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta name="description" content="Comprehensive documentation for the PRS (Purchase Requisition System)">
<meta name="author" content="Stratpoint Team">
<meta name="generator" content="MkDocs">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link
  href="https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
  rel="stylesheet">
<style>
  /* Ensure Work Sans is applied everywhere */
  body,
  input {
    font-family: "Work Sans", -apple-system, BlinkMacSystemFont, Helvetica, Arial, sans-serif;
  }

  /* Improve readability */
  .md-typeset {
    font-size: 0.75rem;
    line-height: 1.6;
  }

  .md-typeset h1 {
    font-weight: 700;
    color: var(--md-primary-fg-color);
  }

  .md-typeset h2 {
    font-weight: 600;
  }

  /* Improve table styling */
  .md-typeset table:not([class]) {
    font-size: 0.7rem;
    border: 1px solid var(--md-sidebar-border-color);
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .md-typeset table:not([class]) th {
    background-color: rgba(0, 0, 0, 0.025);
    font-weight: 600;
  }

  [data-md-color-scheme="slate"] .md-typeset table:not([class]) th {
    background-color: rgba(255, 255, 255, 0.05);
  }

  /* Improve admonitions */
  .md-typeset .admonition {
    font-size: 0.7rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .md-typeset .admonition-title {
    font-weight: 600;
  }

  /* Simple sidebar styling */
  .md-nav__link {
    transition: color 0.2s;
  }

  .md-nav__link:hover {
    color: #3f51b5;
  }

  /* ===== STICKY FOOTER IMPLEMENTATION ===== */
  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
  }

  body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .md-container {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
  }

  .md-main {
    flex: 1 0 auto;
  }

  .md-footer {
    flex-shrink: 0;
    position: sticky !important;
    bottom: 0 !important;
    margin-top: auto !important;
    z-index: 1000 !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid var(--md-default-fg-color--lightest) !important;
    background-color: var(--md-footer-bg-color) !important;
  }

  .md-footer-meta {
    padding: 6px 16px !important;
    background-color: var(--md-footer-bg-color) !important;
    min-height: auto !important;
  }

  .md-footer-copyright {
    font-size: 0.65rem !important;
    text-align: center !important;
    margin: 0 !important;
    line-height: 1.2 !important;
  }

  /* Hide navigation footer to make it smaller */
  .md-footer__inner {
    display: none !important;
  }

  [data-md-color-scheme="slate"] .md-footer {
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3) !important;
    border-top: 1px solid var(--md-default-fg-color--lightest) !important;
    background-color: var(--md-footer-bg-color) !important;
  }

  /* Force the layout structure */
  .md-grid {
    max-width: none !important;
  }

  /* Ensure proper spacing with more left/right padding */
  .md-main__inner {
    padding-bottom: 2rem;
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }

  /* Additional spacing for content */
  .md-content {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Responsive padding adjustments */
  @media screen and (max-width: 76.1875em) {
    .md-main__inner {
      padding-left: 1.5rem !important;
      padding-right: 1.5rem !important;
    }

    .md-content {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }
  }

  @media screen and (max-width: 44.9375em) {
    .md-main__inner {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    .md-content {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  /* ===== SIMPLE SIDEBAR FIX ===== */

  /* Make sidebar sticky and appear above footer */
  .md-sidebar {
    position: sticky !important;
    top: 0 !important;
    z-index: 1001 !important;
    height: calc(100vh - 30px) !important;
    /* Account for small footer */
    overflow: hidden !important;
  }

  .md-sidebar--primary {
    z-index: 1001 !important;
  }

  .md-sidebar--secondary {
    z-index: 1001 !important;
  }

  /* Make sidebar content scrollable */
  .md-sidebar__scrollwrap {
    height: 100% !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    z-index: 1001 !important;
    padding-bottom: 20px !important;
  }

  /* Ensure header stays on top */
  .md-header {
    z-index: 1002 !important;
  }

  /* Ensure search and other header elements stay on top */
  .md-search {
    z-index: 1003 !important;
  }

  .md-search__overlay {
    z-index: 1003 !important;
  }

  .md-search__form {
    z-index: 1003 !important;
  }

  /* ===== BACK TO TOP BUTTON ===== */

  /* Back to top button styling */
  .back-to-top {
    position: fixed !important;
    bottom: 50px !important;
    /* Above the footer */
    right: 30px !important;
    width: 50px !important;
    height: 50px !important;
    background-color: var(--md-primary-fg-color) !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    z-index: 999 !important;
    /* Below footer but above content */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(20px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    line-height: 1 !important;
  }

  /* Show button when scrolled */
  .back-to-top.visible {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
  }

  /* Hover effects */
  .back-to-top:hover {
    background-color: var(--md-accent-fg-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
  }

  /* Dark mode adjustments */
  [data-md-color-scheme="slate"] .back-to-top {
    background-color: var(--md-primary-fg-color) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  [data-md-color-scheme="slate"] .back-to-top:hover {
    background-color: var(--md-accent-fg-color) !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4) !important;
  }

  /* Mobile adjustments */
  @media screen and (max-width: 76.1875em) {
    .back-to-top {
      bottom: 40px !important;
      right: 20px !important;
      width: 45px !important;
      height: 45px !important;
      font-size: 18px !important;
    }
  }

  @media screen and (max-width: 44.9375em) {
    .back-to-top {
      bottom: 35px !important;
      right: 15px !important;
      width: 40px !important;
      height: 40px !important;
      font-size: 16px !important;
    }
  }
</style>
{% endblock %}

{% block announce %}
PRS Developer Documentation - Comprehensive guide for developers
{% endblock %}

{% block content %}
{{ super() }}
<script>
  // Add any custom JavaScript here
  document.addEventListener('DOMContentLoaded', function () {
    // Force light mode as default
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (!prefersDark && !localStorage.getItem('theme')) {
      document.body.setAttribute('data-md-color-scheme', 'default');
    }

    // Ensure sticky footer implementation
    function ensureStickyFooter() {
      const body = document.body;
      const footer = document.querySelector('.md-footer');

      if (footer && !body.classList.contains('sticky-footer-applied')) {
        body.style.display = 'flex';
        body.style.flexDirection = 'column';
        body.style.minHeight = '100vh';

        const container = document.querySelector('.md-container');
        if (container) {
          container.style.flex = '1 0 auto';
          container.style.display = 'flex';
          container.style.flexDirection = 'column';
        }

        const main = document.querySelector('.md-main');
        if (main) {
          main.style.flex = '1 0 auto';
        }

        footer.style.flexShrink = '0';
        footer.style.marginTop = 'auto';

        body.classList.add('sticky-footer-applied');
      }
    }

    // Apply immediately and after any theme changes
    ensureStickyFooter();

    // Watch for theme changes
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-md-color-scheme') {
          setTimeout(ensureStickyFooter, 100);
        }
      });
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-md-color-scheme']
    });

    // ===== BACK TO TOP BUTTON FUNCTIONALITY =====

    // Create back to top button
    function createBackToTopButton() {
      const button = document.createElement('button');
      button.className = 'back-to-top';
      button.innerHTML = '↑';
      button.setAttribute('aria-label', 'Back to top');
      button.setAttribute('title', 'Back to top');

      // Add click handler
      button.addEventListener('click', function () {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });

      document.body.appendChild(button);
      return button;
    }

    // Show/hide button based on scroll position
    function handleBackToTopVisibility() {
      const button = document.querySelector('.back-to-top');
      if (!button) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const showThreshold = 300; // Show after scrolling 300px

      if (scrollTop > showThreshold) {
        button.classList.add('visible');
      } else {
        button.classList.remove('visible');
      }
    }

    // Initialize back to top button
    const backToTopButton = createBackToTopButton();

    // Add scroll listener with throttling for performance
    let scrollTimeout;
    window.addEventListener('scroll', function () {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(handleBackToTopVisibility, 10);
    });

    // Initial check
    handleBackToTopVisibility();
  });
</script>
{% endblock %}
