/**
 * <PERSON><PERSON><PERSON> to add OpenAPI documentation to existing routes
 * 
 * Usage: node scripts/add-documentation.js <route-file-path>
 * Example: node scripts/add-documentation.js src/interfaces/router/public/authRoute.js
 */
const fs = require('fs');
const path = require('path');
const { parse } = require('@babel/parser');
const generate = require('@babel/generator').default;
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');

// Get the route file path from command line arguments
const routeFilePath = process.argv[2];

if (!routeFilePath) {
  console.error('Please provide a route file path');
  process.exit(1);
}

// Read the route file
const routeFile = fs.readFileSync(routeFilePath, 'utf8');

// Parse the route file
const ast = parse(routeFile, {
  sourceType: 'module',
  plugins: ['jsx', 'typescript'],
});

// Determine the route tag from the file name
const fileName = path.basename(routeFilePath, '.js');
const tag = fileName.replace(/Route$/, '');
const tagCapitalized = tag.charAt(0).toUpperCase() + tag.slice(1);

// Track if we need to add the utils import
let needsUtilsImport = false;

// Traverse the AST to find route definitions
traverse(ast, {
  CallExpression(path) {
    // Check if it's a route definition
    if (
      path.node.callee.property &&
      (path.node.callee.property.name === 'route' || 
       path.node.callee.property.name === 'get' || 
       path.node.callee.property.name === 'post' || 
       path.node.callee.property.name === 'put' || 
       path.node.callee.property.name === 'delete')
    ) {
      // Get the route options
      const routeOptions = path.node.arguments[0];
      
      if (!routeOptions || routeOptions.type !== 'ObjectExpression') {
        return;
      }
      
      // Check if the route already has documentation
      const hasDocumentation = routeOptions.properties.some(prop => 
        prop.key.name === 'schema' && 
        prop.value.properties && 
        prop.value.properties.some(p => p.key.name === 'tags' || p.key.name === 'summary' || p.key.name === 'description')
      );
      
      if (hasDocumentation) {
        return;
      }
      
      // Get the route method and URL
      const methodProp = routeOptions.properties.find(prop => prop.key.name === 'method');
      const urlProp = routeOptions.properties.find(prop => prop.key.name === 'url');
      
      if (!methodProp || !urlProp) {
        return;
      }
      
      const method = methodProp.value.value;
      const url = urlProp.value.value;
      
      // Check if the route has authentication
      const hasAuth = routeOptions.properties.some(prop => 
        prop.key.name === 'preHandler' && 
        prop.value.type === 'ArrayExpression' && 
        prop.value.elements.some(el => 
          el.type === 'MemberExpression' && 
          el.property.name === 'authenticate'
        )
      );
      
      // Generate a summary and description based on the method and URL
      let summary = '';
      let description = '';
      
      const resourceName = url.split('/').pop().replace(/s$/, '');
      const resourceNameCapitalized = resourceName.charAt(0).toUpperCase() + resourceName.slice(1);
      
      switch (method) {
        case 'GET':
          if (url.includes(':')) {
            summary = `Get ${resourceNameCapitalized} by ID`;
            description = `Returns a single ${resourceName} by its ID`;
          } else {
            summary = `Get all ${resourceName}s`;
            description = `Returns a list of ${resourceName}s with pagination`;
          }
          break;
        case 'POST':
          summary = `Create a new ${resourceName}`;
          description = `Creates a new ${resourceName} with the provided data`;
          break;
        case 'PUT':
          summary = `Update a ${resourceName}`;
          description = `Updates an existing ${resourceName} with the provided data`;
          break;
        case 'DELETE':
          summary = `Delete a ${resourceName}`;
          description = `Deletes an existing ${resourceName}`;
          break;
      }
      
      // Create the documentation object
      const docMethod = hasAuth ? 'documentProtectedRoute' : 'documentPublicRoute';
      needsUtilsImport = true;
      
      const docObject = t.objectExpression([
        t.objectProperty(t.identifier('tag'), t.stringLiteral(tagCapitalized)),
        t.objectProperty(t.identifier('summary'), t.stringLiteral(summary)),
        t.objectProperty(t.identifier('description'), t.stringLiteral(description)),
      ]);
      
      const docExpression = t.spreadElement(
        t.callExpression(
          t.identifier(docMethod),
          [docObject]
        )
      );
      
      // Add the documentation to the route options
      routeOptions.properties.push(docExpression);
    }
  },
});

// Add the utils import if needed
if (needsUtilsImport) {
  traverse(ast, {
    Program(path) {
      // Check if utils import already exists
      const hasUtilsImport = path.node.body.some(node => 
        node.type === 'VariableDeclaration' && 
        node.declarations.some(decl => 
          decl.id.type === 'ObjectPattern' && 
          decl.id.properties.some(prop => 
            prop.key.name === 'documentProtectedRoute' || 
            prop.key.name === 'documentPublicRoute'
          )
        )
      );
      
      if (!hasUtilsImport) {
        // Add the utils import
        const utilsImport = t.variableDeclaration('const', [
          t.variableDeclarator(
            t.objectPattern([
              t.objectProperty(t.identifier('documentProtectedRoute'), t.identifier('documentProtectedRoute')),
              t.objectProperty(t.identifier('documentPublicRoute'), t.identifier('documentPublicRoute')),
            ]),
            t.memberExpression(
              t.memberExpression(
                t.identifier('fastify'),
                t.identifier('diScope')
              ),
              t.callExpression(
                t.identifier('resolve'),
                [t.stringLiteral('utils')]
              )
            )
          )
        ]);
        
        // Find the function declaration
        const functionDeclaration = path.node.body.find(node => 
          node.type === 'FunctionDeclaration' || 
          (node.type === 'VariableDeclaration' && 
           node.declarations[0].init && 
           node.declarations[0].init.type === 'ArrowFunctionExpression')
        );
        
        if (functionDeclaration) {
          // Add the import at the beginning of the function body
          if (functionDeclaration.type === 'FunctionDeclaration') {
            functionDeclaration.body.body.unshift(utilsImport);
          } else {
            functionDeclaration.declarations[0].init.body.body.unshift(utilsImport);
          }
        }
      }
    },
  });
}

// Generate the updated code
const { code } = generate(ast, { retainLines: true });

// Write the updated code back to the file
fs.writeFileSync(routeFilePath, code);

console.log(`Added documentation to ${routeFilePath}`);
