FROM node:20-alpine AS builder

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm ci

COPY . .

ARG VITE_APP_API_URL

ENV VITE_APP_API_URL=$VITE_APP_API_URL

RUN npm run build:prod

FROM nginx:alpine

# Copy built files to nginx html directory
COPY --from=builder /usr/src/app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
