# Business Logic Implementation

## Overview

The business logic in the PRS-Backend is primarily implemented in service classes. This document provides a detailed analysis of how the core business logic is implemented, including patterns, issues, and recommendations.

## Service Layer Architecture

The service layer follows these general patterns:

1. **Dependency Injection**: Services receive dependencies through constructor injection
2. **Repository Pattern**: Services use repositories for data access
3. **Transaction Management**: Services manage database transactions
4. **Domain Logic**: Services implement business rules and workflows

### Service Structure

A typical service class has this structure:

```javascript
class SomeService {
  constructor(container) {
    // Extract dependencies from container
    const {
      db,
      utils,
      someRepository,
      otherRepository,
      // ... many other dependencies
    } = container;
    
    // Initialize service properties
    this.db = db;
    this.utils = utils;
    this.someRepository = someRepository;
    this.otherRepository = otherRepository;
    // ... many other assignments
  }
  
  async someBusinessOperation(data) {
    // Implement business logic
  }
  
  // ... other methods
}
```

## Key Business Logic Implementations

### 1. Requisition Creation

The requisition creation logic is implemented in `RequisitionService`:

```javascript
// From src/app/services/requisitionService.js
async createRequisition(data, transaction) {
  // Generate requisition number
  const { rsNumber, rsLetter } = await this.generateRSNumberCode(data.isDraft);
  
  // Determine number field based on draft status
  const numberField = data.isDraft ? 'draftRsNumber' : 'rsNumber';
  
  // Create requisition
  const requisition = await this.requisitionRepository.create(
    {
      ...data,
      rsLetter,
      [numberField]: rsNumber,
      status: data.isDraft ? 'draft' : 'submitted',
    },
    { transaction }
  );
  
  return requisition;
}
```

### 2. Approval Workflow

The approval workflow is implemented across multiple services:

```javascript
// From src/app/services/requisitionApproverService.js
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  if (!requisitionId) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Requisition ID is required in setting RS status to assigning',
    });
  }

  // skip RS assigning flow
  // since there is a new approver
  if (approverId) {
    return false;
  }

  const [rsForApprovalCount, approvedRsCount] = await Promise.all([
    this.requisitionApproverRepository.count({
      where: { requisitionId },
      transaction,
    }),
    this.requisitionApproverRepository.count({
      where: { requisitionId, status: 'approved' },
      transaction,
    }),
  ]);

  if (rsForApprovalCount === approvedRsCount) {
    this.fastify.log.info(
      'All requisition approvers have approved the requisition, updating requisition status to assigning',
    );

    // TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error

    await this.requisitionRepository.update(
      { id: requisitionId },
      { status: 'assigning' },
      { transaction },
    );

    return true;
  }

  return false;
}
```

### 3. Canvass Creation

The canvass creation logic is implemented in `CanvassService`:

```javascript
// From src/app/services/canvassService.js
async createCanvass(payload = {}) {
  const {
    isDraft,
    assignedTo,
    transaction,
    requisitionId,
    addItems = [],
    updateItems = [],
    deleteItems = [],
    id: canvassId,
    userFromToken,
    canvassType,
  } = payload;

  const { CANVASS_STATUS, CANVASS_ITEM_STATUS } = this.constants.canvass;
  const numberField = isDraft ? 'draftCsNumber' : 'csNumber';
  let canvassData =
    await this.canvassRequisitionRepository.getExistingCanvass({
      canvassId,
      requisitionId,
    });

  const { csLetter, csNumber } = await this.#generateCSNumberCode(
    isDraft,
    canvassData?.id,
  );

  let isPartial = !isDraft;
  if (!isDraft) {
    isPartial = await this.#checkPartialCanvass({
      addItems,
      updateItems,
      deleteItems,
      requisitionId,
      canvassId: canvassData?.id,
    });
  }

  if (isPartial) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Every canvassed item must have at least one supplier.',
    });
  }

  const nonDraftStatus = isPartial
    ? CANVASS_STATUS.PARTIAL
    : CANVASS_STATUS.FOR_APPROVAL;

  const csStatus = isDraft ? CANVASS_STATUS.DRAFT : nonDraftStatus;
  const isCanvassNew = !canvassData;

  // ... more processing
}
```

### 4. Purchase Order Creation

The purchase order creation logic is implemented in `PurchaseOrderService`:

```javascript
// From src/app/services/purchaseOrderService.js
async createPurchaseOrder(payload = {}) {
  const { existingCanvass, transaction } = payload;
  const { PO_STATUS } = this.constants.purchaseOrder;
  const canvassItems = await this.canvassItemRepository.findAll({
    where: { canvassRequisitionId: existingCanvass.id },
    attributes: ['id'],
    paginate: false,
  });

  const selectedSuppliers =
    await this.canvassItemSupplierRepository.getSelectedSupplierByCanvassId(
      canvassItems?.data?.map((item) => item.id) || [],
    );

  if (!selectedSuppliers.total) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'No selected suppliers found for this canvass requisition',
    });
  }

  const supplierGroups = this.#groupBySupplier(selectedSuppliers.data);
  for (const supplierGroup of supplierGroups) {
    try {
      await this.#createPOForSupplier({
        transaction,
        supplierGroup,
        requisition: existingCanvass.requisition,
        canvassRequisitionId: existingCanvass.id,
      });
    } catch (error) {
      this.fastify.log.error('[ERROR] Creating PO for supplier');
      this.fastify.log.error(error);
      throw error;
    }
  }

  await this.requisitionRepository.update(
    { id: existingCanvass.requisition.id },
    { status: PO_STATUS.FOR_PO_REVIEW },
    { transaction },
  );
}
```

### 5. Delivery Receipt Creation

The delivery receipt creation logic is implemented in `DeliveryReceiptService`:

```javascript
// From src/app/services/deliveryReceiptService.js
async createDeliveryReceipt(data, userFromToken) {
  const checkIfPoNumberAlreadyTaken =
    await this.deliveryReceiptRepository.findOne({
      where: {
        poId: data.poId,
      },
    });

  if (checkIfPoNumberAlreadyTaken) {
    throw this.clientErrors.BAD_REQUEST({
      message: 'Delivery receipt with PO number already exists.',
    });
  }

  const isUserCreatorOrAssignee = await this.requisitionService.isUserCreatorOrAssignee(data.requisitionId, userFromToken.id);

  if (!isUserCreatorOrAssignee) {
    throw this.clientErrors.UNAUTHORIZED({
      message: 'You are not authorized to create a delivery receipt.',
    });
  }

  const transaction = await this.db.sequelize.transaction();
  try {
    // create delivery receipt
    const deliveryReceipt = await this.deliveryReceiptRepository.create(
      {
        ...data,
        attachmentIds: undefined,
      },
      {
        transaction,
        userId: userFromToken.id,
      },
    );

    // ... more processing
    
    await transaction.commit();
    
    // ... more processing
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

### 6. Payment Request Creation

The payment request creation logic is implemented in `RSPaymentRequestService`:

```javascript
// From src/app/services/rsPaymentRequestService.js
async createRsPaymentRequest(request) {
  const { transaction, userFromToken, ...payload } = request;
  const numberField = payload.isDraft === true ? 'prDraftNumber' : 'prNumber';
  const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

  const { prLetter, prNumber } = await this.generatePRNumberCode(
    payload.isDraft,
  );

  // comment for testing
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });

  const result = await this.rsPaymentRequestRepository.create(
    {
      ...payload,
      prLetter,
      //comment for testing
      termsData,
      [numberField]: prNumber,
      status:
        payload.isDraft === true
          ? RS_PAYMENT_REQUEST_STATUS.DRAFT
          : RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
    },
    transaction,
  );

  if (payload.isDraft === false) {
    await this.requisitionRepository.update(
      { id: payload.requisitionId },
      { status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL },
      { transaction },
    );
  }

  return result;
}
```

## Business Logic Issues

### 1. Excessive Service Dependencies

Many services have an excessive number of dependencies, making them difficult to understand and maintain:

```javascript
// From src/app/services/requisitionService.js
constructor(container) {
  const {
    db,
    utils,
    entities,
    userRepository,
    requisitionRepository,
    requisitionItemListRepository,
    tomItemRepository,
    commentRepository,
    attachmentRepository,
    supplierRepository,
    projectRepository,
    companyRepository,
    constants,
    projectApprovalRepository,
    clientErrors,
    requisitionApproverRepository,
    departmentApprovalRepository,
    fastify,
    itemRepository,
    nonOfmItemRepository,
    notificationRepository,
    historyRepository,
    canvassRequisitionRepository,
    purchaseOrderRepository,
    deliveryReceiptRepository,
    rsPaymentRequestRepository,
    notificationService,
    departmentRepository,
    departmentAssociationApprovalRepository,
  } = container;
  
  // ... many property assignments
}
```

This violates the Single Responsibility Principle and makes the services difficult to test and maintain.

### 2. Inconsistent Transaction Management

Transaction management is inconsistent across services:

```javascript
// Example 1: Transaction passed from controller
async createRequisition(data, transaction) {
  // Use transaction passed from controller
}

// Example 2: Transaction created in service
async createDeliveryReceipt(data, userFromToken) {
  const transaction = await this.db.sequelize.transaction();
  try {
    // ... operations
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

This inconsistency can lead to issues with transaction boundaries and error handling.

### 3. Complex Conditional Logic

Many services contain complex conditional logic that is difficult to understand and maintain:

```javascript
// From src/app/services/requisitionService.js
async rsApproversV2({
  category,
  projectId,
  departmentId,
  userFromToken,
  transaction,
  companyId,
}) {
  const { APPROVAL_TYPES } = this.constants.approval;
  const { REQUISITION_CATEGORIES } = this.constants.requisition;
  const approvalTypeCode = APPROVAL_TYPES.REQUISITION_SLIP.code;

  let approvers = [];
  let optionalApprovers = [];

  if (category === REQUISITION_CATEGORIES[0]) {
    // Association
    // ... logic for association
  } else if (category === REQUISITION_CATEGORIES[1]) {
    // Company
    // ... logic for company
  } else if (category === REQUISITION_CATEGORIES[2]) {
    // Project
    // ... logic for project
  }

  return {
    approvers,
    optionalApprovers,
  };
}
```

This type of code is difficult to understand, test, and maintain.

### 4. Incomplete Validation

Many operations lack comprehensive validation:

```javascript
// From src/app/services/requisitionApproverService.js
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  // ... other code
  
  // TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error
  await this.requisitionRepository.update(
    { id: requisitionId },
    { status: 'assigning' },
    { transaction },
  );
  
  // ... other code
}
```

This can lead to data inconsistency and unexpected behavior.

### 5. Commented-Out Code and TODOs

The codebase contains numerous commented-out code blocks and TODO comments:

```javascript
// From src/app/services/rsPaymentRequestService.js
async createRsPaymentRequest(request) {
  // ... other code
  
  // comment for testing
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });
  
  // ... other code
}
```

This indicates incomplete refactoring and unresolved issues.

### 6. Inconsistent Error Handling

Error handling is inconsistent across services:

```javascript
// Example 1: Specific error with message
if (!selectedSuppliers.total) {
  throw this.clientErrors.BAD_REQUEST({
    message: 'No selected suppliers found for this canvass requisition',
  });
}

// Example 2: Generic error with no specific message
try {
  // ... operations
} catch (error) {
  this.fastify.log.error('[ERROR] Creating PO for supplier');
  this.fastify.log.error(error);
  throw error;
}
```

This inconsistency makes it difficult to provide meaningful error messages to users.

## Recommendations

### 1. Reduce Service Dependencies

Refactor services to have fewer, more focused dependencies:

```javascript
// Before
class RequisitionService {
  constructor(container) {
    const {
      db, utils, entities, userRepository, requisitionRepository,
      // ... many other dependencies
    } = container;
    // ... many property assignments
  }
}

// After
class RequisitionService {
  constructor({
    requisitionRepository,
    requisitionItemRepository,
    approvalService,
    notificationService,
    db,
    logger,
  }) {
    this.requisitionRepository = requisitionRepository;
    this.requisitionItemRepository = requisitionItemRepository;
    this.approvalService = approvalService;
    this.notificationService = notificationService;
    this.db = db;
    this.logger = logger;
  }
}
```

### 2. Implement Consistent Transaction Management

Standardize transaction management across services:

```javascript
// Transaction manager utility
async function withTransaction(db, callback) {
  const transaction = await db.sequelize.transaction();
  try {
    const result = await callback(transaction);
    await transaction.commit();
    return result;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// Usage in service
async createDeliveryReceipt(data, userFromToken) {
  return await withTransaction(this.db, async (transaction) => {
    // ... operations using transaction
    return result;
  });
}
```

### 3. Simplify Conditional Logic

Refactor complex conditional logic using design patterns:

```javascript
// Strategy pattern for approval logic
class ApprovalStrategyFactory {
  static getStrategy(category) {
    switch (category) {
      case 'association':
        return new AssociationApprovalStrategy();
      case 'company':
        return new CompanyApprovalStrategy();
      case 'project':
        return new ProjectApprovalStrategy();
      default:
        throw new Error(`Unknown category: ${category}`);
    }
  }
}

// Usage in service
async getApprovers(category, context) {
  const strategy = ApprovalStrategyFactory.getStrategy(category);
  return await strategy.getApprovers(context);
}
```

### 4. Implement Comprehensive Validation

Add comprehensive validation for all operations:

```javascript
// State validation utility
function validateState(entity, expectedStates, errorMessage) {
  if (!expectedStates.includes(entity.status)) {
    throw new Error(errorMessage || `Invalid state: ${entity.status}. Expected one of: ${expectedStates.join(', ')}`);
  }
}

// Usage in service
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  // ... other code
  
  const requisition = await this.requisitionRepository.getById(requisitionId);
  validateState(requisition, ['submitted'], 'Requisition must be in submitted state to be assigned');
  
  await this.requisitionRepository.update(
    { id: requisitionId },
    { status: 'assigning' },
    { transaction },
  );
  
  // ... other code
}
```

### 5. Clean Up Code

Remove commented-out code and resolve TODOs:

```javascript
// Before
async createRsPaymentRequest(request) {
  // ... other code
  
  // comment for testing
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });
  
  // ... other code
}

// After
async createRsPaymentRequest(request) {
  // ... other code
  
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });
  
  // ... other code
}
```

### 6. Standardize Error Handling

Implement consistent error handling across services:

```javascript
// Domain-specific error classes
class RequisitionError extends Error {
  constructor(message, code = 'REQUISITION_ERROR') {
    super(message);
    this.code = code;
  }
}

class InvalidStateError extends RequisitionError {
  constructor(entity, currentState, expectedStates) {
    super(
      `Invalid state: ${currentState}. Expected one of: ${expectedStates.join(', ')}`,
      'INVALID_STATE'
    );
    this.entity = entity;
    this.currentState = currentState;
    this.expectedStates = expectedStates;
  }
}

// Usage in service
if (requisition.status !== 'submitted') {
  throw new InvalidStateError('requisition', requisition.status, ['submitted']);
}
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
