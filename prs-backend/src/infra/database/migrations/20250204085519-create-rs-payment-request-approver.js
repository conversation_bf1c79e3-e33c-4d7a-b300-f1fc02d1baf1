'use strict';

const {
  PR_APPROVER_STATUS,
} = require('../../../domain/constants/rsPaymentRequestConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('rs_payment_request_approvers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      paymentRequestId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'rs_payment_requests',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'payment_request_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: PR_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('rs_payment_request_approvers');
  },
};
