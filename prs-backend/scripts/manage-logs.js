#!/usr/bin/env node
/**
 * Log Management Utility
 * 
 * This script provides utilities for managing logs in production environments.
 * It can be used to:
 * - List all log files
 * - Clean up old logs
 * - Force log rotation
 * - View log statistics
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Base log directory
const LOG_DIR = path.join(__dirname, '..', 'logs');

// Ensure log directory exists
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

/**
 * Lists all log files with their sizes
 */
function listLogFiles() {
  console.log('\n=== Log Files ===');
  
  try {
    const files = fs.readdirSync(LOG_DIR);
    
    if (files.length === 0) {
      console.log('No log files found.');
      return;
    }
    
    // Get file stats and sort by size (largest first)
    const fileStats = files
      .map(file => {
        const filePath = path.join(LOG_DIR, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          isCompressed: file.endsWith('.gz')
        };
      })
      .sort((a, b) => b.size - a.size);
    
    // Calculate total size
    const totalSize = fileStats.reduce((sum, file) => sum + file.size, 0);
    
    // Display files
    console.log(`Found ${fileStats.length} log files (${formatSize(totalSize)} total):`);
    console.log('');
    console.log('Size      | Modified           | Name');
    console.log('----------|--------------------|-----------------');
    
    fileStats.forEach(file => {
      console.log(
        `${formatSize(file.size).padEnd(10)} | ` +
        `${file.modified.toISOString().replace('T', ' ').substr(0, 19)} | ` +
        `${file.name}${file.isCompressed ? ' (compressed)' : ''}`
      );
    });
  } catch (error) {
    console.error(`Error listing log files: ${error.message}`);
  }
}

/**
 * Cleans up old log files beyond retention period
 */
function cleanupOldLogs() {
  console.log('\n=== Cleaning Up Old Logs ===');
  
  try {
    const files = fs.readdirSync(LOG_DIR);
    const now = new Date();
    let deletedCount = 0;
    let deletedSize = 0;
    
    files.forEach(file => {
      // Skip current log files
      if (file === 'app.log' || file === 'error.log' || file === 'audit.log') {
        return;
      }
      
      const filePath = path.join(LOG_DIR, file);
      const stats = fs.statSync(filePath);
      const fileAgeDays = (now - stats.mtime) / (1000 * 60 * 60 * 24);
      
      // Delete files older than retention period
      // Error logs: 30 days, Audit logs: 90 days, Regular logs: 14 days
      let shouldDelete = false;
      
      if (file.includes('error') && fileAgeDays > 30) {
        shouldDelete = true;
      } else if (file.includes('audit') && fileAgeDays > 90) {
        shouldDelete = true;
      } else if (fileAgeDays > 14) {
        shouldDelete = true;
      }
      
      if (shouldDelete) {
        console.log(`Deleting ${file} (${formatSize(stats.size)}, ${Math.floor(fileAgeDays)} days old)`);
        fs.unlinkSync(filePath);
        deletedCount++;
        deletedSize += stats.size;
      }
    });
    
    if (deletedCount === 0) {
      console.log('No old log files to clean up.');
    } else {
      console.log(`Cleaned up ${deletedCount} old log files (${formatSize(deletedSize)} freed).`);
    }
  } catch (error) {
    console.error(`Error cleaning up logs: ${error.message}`);
  }
}

/**
 * Forces log rotation
 */
function forceRotation() {
  console.log('\n=== Forcing Log Rotation ===');
  
  try {
    // Get the process ID of the Node.js application
    const pid = findAppPid();
    
    if (!pid) {
      console.error('Could not find the application process ID.');
      return;
    }
    
    // Send SIGHUP signal to the process to trigger log rotation
    console.log(`Sending SIGHUP signal to process ${pid}...`);
    process.kill(pid, 'SIGHUP');
    console.log('Signal sent. Log rotation should occur shortly.');
  } catch (error) {
    console.error(`Error forcing log rotation: ${error.message}`);
  }
}

/**
 * Displays log statistics
 */
function showStatistics() {
  console.log('\n=== Log Statistics ===');
  
  try {
    const files = fs.readdirSync(LOG_DIR);
    
    if (files.length === 0) {
      console.log('No log files found.');
      return;
    }
    
    // Group files by type
    const stats = {
      app: { count: 0, size: 0 },
      error: { count: 0, size: 0 },
      audit: { count: 0, size: 0 },
      other: { count: 0, size: 0 }
    };
    
    files.forEach(file => {
      const filePath = path.join(LOG_DIR, file);
      const fileSize = fs.statSync(filePath).size;
      
      if (file.includes('app')) {
        stats.app.count++;
        stats.app.size += fileSize;
      } else if (file.includes('error')) {
        stats.error.count++;
        stats.error.size += fileSize;
      } else if (file.includes('audit')) {
        stats.audit.count++;
        stats.audit.size += fileSize;
      } else {
        stats.other.count++;
        stats.other.size += fileSize;
      }
    });
    
    console.log(`Total log files: ${files.length}`);
    console.log(`Application logs: ${stats.app.count} files (${formatSize(stats.app.size)})`);
    console.log(`Error logs: ${stats.error.count} files (${formatSize(stats.error.size)})`);
    console.log(`Audit logs: ${stats.audit.count} files (${formatSize(stats.audit.size)})`);
    console.log(`Other logs: ${stats.other.count} files (${formatSize(stats.other.size)})`);
    console.log(`Total size: ${formatSize(stats.app.size + stats.error.size + stats.audit.size + stats.other.size)}`);
  } catch (error) {
    console.error(`Error showing statistics: ${error.message}`);
  }
}

/**
 * Formats a size in bytes to a human-readable string
 */
function formatSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
}

/**
 * Finds the process ID of the Node.js application
 */
function findAppPid() {
  try {
    // This works on Linux/Unix systems
    const output = execSync('ps aux | grep node | grep -v grep').toString();
    const lines = output.split('\n');
    
    for (const line of lines) {
      if (line.includes('prs-backend') && !line.includes('manage-logs')) {
        const pid = line.trim().split(/\s+/)[1];
        return parseInt(pid, 10);
      }
    }
    
    return null;
  } catch (error) {
    console.error(`Error finding app PID: ${error.message}`);
    return null;
  }
}

/**
 * Main menu
 */
function showMenu() {
  console.log('\n=== Log Management Utility ===');
  console.log('1. List all log files');
  console.log('2. Clean up old logs');
  console.log('3. Force log rotation');
  console.log('4. Show log statistics');
  console.log('5. Exit');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('\nSelect an option (1-5): ', (answer) => {
    switch (answer.trim()) {
      case '1':
        listLogFiles();
        rl.close();
        setTimeout(showMenu, 1000);
        break;
      case '2':
        cleanupOldLogs();
        rl.close();
        setTimeout(showMenu, 1000);
        break;
      case '3':
        forceRotation();
        rl.close();
        setTimeout(showMenu, 1000);
        break;
      case '4':
        showStatistics();
        rl.close();
        setTimeout(showMenu, 1000);
        break;
      case '5':
        console.log('Exiting...');
        rl.close();
        break;
      default:
        console.log('Invalid option. Please try again.');
        rl.close();
        setTimeout(showMenu, 500);
    }
  });
}

// If script is run directly, show the menu
if (require.main === module) {
  showMenu();
}

module.exports = {
  listLogFiles,
  cleanupOldLogs,
  forceRotation,
  showStatistics
};
