<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="972363514C0" style="width:103px;" class="column-headers-background">A</th><th id="972363514C1" style="width:98px;" class="column-headers-background">B</th><th id="972363514C2" style="width:252px;" class="column-headers-background">C</th><th id="972363514C3" style="width:252px;" class="column-headers-background">D</th><th id="972363514C4" style="width:233px;" class="column-headers-background">E</th><th id="972363514C5" style="width:330px;" class="column-headers-background">F</th><th id="972363514C6" style="width:184px;" class="column-headers-background">G</th><th id="972363514C7" style="width:344px;" class="column-headers-background">H</th><th id="972363514C8" style="width:100px;" class="column-headers-background">I</th><th id="972363514C9" style="width:88px;" class="column-headers-background">J</th><th id="972363514C10" style="width:140px;" class="column-headers-background">K</th><th id="972363514C11" style="width:140px;" class="column-headers-background">L</th><th id="972363514C12" style="width:140px;" class="column-headers-background">M</th><th id="972363514C13" style="width:140px;" class="column-headers-background">N</th><th id="972363514C14" style="width:78px;" class="column-headers-background">O</th><th id="972363514C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="972363514R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="972363514R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-016 - [Manage Items in RS] -  Adding of Item</td></tr><tr style="height: 19px"><th id="972363514R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-016-001</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Adding of Item in Requisition Slip </td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br><br></td><td class="s6"></td><td class="s5">1. Able to select Requisition Slip<br>2. Able to Click Action button<br>3. Able to Click Add Items<br>4. Able to Click Add Item button in the Item/s part<br></td><td class="s7" rowspan="8"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=*********#gid=*********">https://docs.google.com/spreadsheets/d/1LRu0STas8JUvhz18IjgYIrni42mMCNnbZpahimqtVNo/edit?gid=*********#gid=*********</a></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="972363514R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-016-002</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Fields for Adding of Item</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name<br>7. Click on Item Type Dropdown<br>8. Select from dropdown<br>9. Click on Unit Dropdown<br>10. Select from dropdown<br>11. Click Input Quantity field<br>12. Input Quantity</td><td class="s6"></td><td class="s5">1. Item Field only allows:<br>        i. Alphanumeric and Special Characters except Emojis<br>        ii. Maximum of 100 Characters<br>2. Item Type dropdown only has 2 values:<br>        i) Goods<br>        ii) Service<br>3. Unit dropdown only displays what is on Non-OFM Items Creation<br>4. Quantity Field only allows:<br>        i. Numbers only</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="972363514R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">PRS-016-003</td><td class="s5">Adding of Item</td><td class="s12"></td><td class="s12">Verify Fields for Adding of Item - Negative Scenario</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name with Emoji or no input<br>7. Input Item name more than 100 characters<br>8. Click Input Quantity field<br>9. Input Letters in Quantity<br>10. Input 4 digit numbers in Quantity<br>11. Don&#39;t Select from the dropdowns of Item Type and Unit</td><td class="s6"></td><td class="s5">1. Item Field only allows:<br>        i. Alphanumeric and Special Characters except Emojis<br>        ii. Maximum of 100 Characters<br>2. Quantity Field only allows:<br>        i. Numbers only<br>      <br>3. Must Select from dropdowns in Item Type and Unit?</td><td class="s13">Failed</td><td class="s14">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Failed due to issue raised<br><br>[QA BUGS] [Adding of Items] - Incorrect behavior of Item Name and Quantity Fields</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-744">CITYLANDPRS-744<br><br><br>CITYLANDPRS-745<br></a></td></tr><tr style="height: 19px"><th id="972363514R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">PRS-016-004</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Delete button in Item List</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name<br>7. Click on Item Type Dropdown<br>8. Select from dropdown<br>9. Click on Unit Dropdown<br>10. Select from dropdown<br>11. Click Input Quantity field<br>12. Input Quantity<br>13. Click on Delete button on the row of item user wants to delete </td><td class="s6"></td><td class="s5">1. Whole row of Item will be deleted</td><td class="s8">Passed</td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Unable to add Non-OFM Item and &quot;request.files.map is not a function&quot; error is displayed</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-890">CITYLANDPRS-890</a></td></tr><tr style="height: 19px"><th id="972363514R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s5">PRS-016-005</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Saving of Items - Saved as Draft</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name<br>7. Click on Item Type Dropdown<br>8. Select from dropdown<br>9. Click on Unit Dropdown<br>10. Select from dropdown<br>11. Click Input Quantity field<br>12. Input Quantity<br>13. Click Save</td><td class="s6"></td><td class="s5">1. New Item should not be created until user submitted</td><td class="s13">Failed</td><td class="s17">Out of Scope</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Failed due to issue raised<br><br>No Save Draft in Adding of Item</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-746">https://youtrack.stratpoint.com/issue/CITYLANDPRS-746<br><br></a></td></tr><tr style="height: 19px"><th id="972363514R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s5">PRS-016-006</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Savings of Items - Submitted - Non Existing Non-OFM Item</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name<br>7. Click on Item Type Dropdown<br>8. Select from dropdown<br>9. Click on Unit Dropdown<br>10. Select from dropdown<br>11. Click Input Quantity field<br>12. Input Quantity<br>13. Click Save<br>14. Click Continue</td><td class="s6"></td><td class="s5">1. should display a Confirmation Modal<br>       i) Once Confirmed, should add the Item to the Request and as one of the Choices for Non-OFM Items Masterlist<br>       ii) Should autogenerate the Account Code of the Item</td><td class="s18">Blocked</td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Blocked due to bug raised<br><br>Unable to add Non-OFM Item and &quot;request.files.map is not a function&quot; error is displayed</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-746">https://youtrack.stratpoint.com/issue/CITYLANDPRS-746<br><br>CITYLANDPRS-890</a></td></tr><tr style="height: 19px"><th id="972363514R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s5">PRS-016-007</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Savings of Items - Cancelling Scenario</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name<br>7. Click on Item Type Dropdown<br>8. Select from dropdown<br>9. Click on Unit Dropdown<br>10. Select from dropdown<br>11. Click Input Quantity field<br>12. Input Quantity<br>13. Click Save<br>14. Click Cancel<br>15. Click Continue</td><td class="s6"></td><td class="s5">1. should display a Confirmation Modal<br>2. Should display a cancel changes modal<br>3. Should not save any progress</td><td class="s18">Blocked</td><td class="s14">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Blocked due to bug raised<br><br>[Adding of Item] Cancel button is not working</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-746">https://youtrack.stratpoint.com/issue/CITYLANDPRS-746<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1101</a></td></tr><tr style="height: 19px"><th id="972363514R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s5">PRS-016-008</td><td class="s5">Adding of Item</td><td class="s5"></td><td class="s5">Verify Savings of Items - Submitted - Existing Non-OFM Item</td><td class="s5">1. Request type is Non-OFM<br>2. Requisition Slip has been created<br>3. Must be the assigned Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, scroll down and click a Requisition Slip with status of Assigned(Check different pages if non existent on page 1)<br>2. Click Select Action Button<br>3. Click Add Items<br>4. Click Add Item in the Item/s part<br>5. Click on Input Item Field<br>6. Input Item Name<br>7. Click on Item Type Dropdown<br>8. Select from dropdown<br>9. Click on Unit Dropdown<br>10. Select from dropdown<br>11. Click Input Quantity field<br>12. Input Quantity<br>13. Click Save<br>14. Click Continue<br>15. item Already Exist modal shows up<br>16. Click close window</td><td class="s6"></td><td class="s5">1. should not allow to continue with the Submission and should require the User to remove the Item to proceed<br>2. should be able to close window</td><td class="s18">Blocked</td><td class="s16">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s5">Blocked due to bug raised<br><br>Unable to add Non-OFM Item and &quot;request.files.map is not a function&quot; error is displayed</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-746">https://youtrack.stratpoint.com/issue/CITYLANDPRS-746<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-890</a></td></tr></tbody></table></div>