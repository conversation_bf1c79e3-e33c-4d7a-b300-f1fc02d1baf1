'use strict';

const {
  NON_RS_APPROVER_STATUS,
} = require('../../../domain/constants/nonRSConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('non_requisition_approvers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'non_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'non_requisition_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: NON_RS_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      },
    });

    await queryInterface.addIndex(
      'non_requisition_approvers',
      ['non_requisition_id'],
      {
        name: 'non_requisition_approvers_non_requisition_id_index',
      },
    );

    await queryInterface.addIndex('non_requisition_approvers', ['user_id'], {
      name: 'non_requisition_approvers_user_id_index',
    });

    await queryInterface.addIndex('non_requisition_approvers', ['role_id'], {
      name: 'non_requisition_approvers_role_id_index',
    });

    await queryInterface.addIndex('non_requisition_approvers', ['status'], {
      name: 'non_requisition_approvers_status_index',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('non_requisition_approvers');
  },
};
