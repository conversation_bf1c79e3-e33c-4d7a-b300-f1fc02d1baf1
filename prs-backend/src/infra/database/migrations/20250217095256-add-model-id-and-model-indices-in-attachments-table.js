'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'attachments';

    const indices = [
      {
        name: 'attachments_model_model_id_index',
        fields: ['model', 'model_id'],
      },
    ];

    for (const index of indices) {
      const indexExists = await queryInterface.sequelize.query(
        `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${index.name}'`,
      );

      if (indexExists[0].length === 0) {
        await queryInterface.addIndex(tableName, index.fields, {
          name: index.name,
        });
      }
    }
  },

  async down(queryInterface, Sequelize) {
    const tableName = 'attachments';

    const indicesToRemove = [
      'attachments_model_model_id_index',
    ];

    for (const indexName of indicesToRemove) {
      const indexExists = await queryInterface.sequelize.query(
        `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${indexName}'`,
      );

      if (indexExists[0].length > 0) {
        await queryInterface.removeIndex(tableName, indexName);
      }
    }
  }
};

