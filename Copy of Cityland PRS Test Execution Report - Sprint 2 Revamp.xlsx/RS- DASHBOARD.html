<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="783449655C0" style="width:63px;" class="column-headers-background">A</th><th id="783449655C1" style="width:184px;" class="column-headers-background">B</th><th id="783449655C2" style="width:63px;" class="column-headers-background">C</th><th id="783449655C3" style="width:252px;" class="column-headers-background">D</th><th id="783449655C4" style="width:233px;" class="column-headers-background">E</th><th id="783449655C5" style="width:330px;" class="column-headers-background">F</th><th id="783449655C7" style="width:258px;" class="column-headers-background">H</th><th id="783449655C8" style="width:100px;" class="column-headers-background">I</th><th id="783449655C9" style="width:178px;" class="column-headers-background">J</th><th id="783449655C10" style="width:245px;" class="column-headers-background">K</th><th id="783449655C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="783449655R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">Status</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="783449655R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="11">Tab Updates</td></tr><tr style="height: 19px"><th id="783449655R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3">1312-001</td><td class="s3">RS Dashboard Updates<br>1. Tab Updates</td><td class="s4">Minor</td><td class="s3">Verify All Tab are displayed in IT admin and Purchasing admin</td><td class="s3">1. User is Logged in as IT admin or Purchasing admin</td><td class="s3">1. Verify all tab are displayed</td><td class="s5"><a target="_blank" href="https://www.figma.com/design/UNpEbFVvETKilgpf7m4d8e/Cityland---PRS-(Desktop)-(Copy)?node-id=16401-131821&amp;t=nqWAwrO4w3vAL9bN-0">1. Users of IT admin and Purchasing admin are expected to see in the Nav Bar: Tabs<br>a. Dashboard<br>b. Non-RS<br>c. Request Flow<br>d. Items<br>e. Manage<br>f. Admin<br>g. Audit Log<br><br>H. My Requests Tab<br>I. For My Approval/Assigned Tab<br> (Should be displayed and highlighted upon viewing of Dashboard Page)<br>J. All Tab</a></td><td class="s3" rowspan="5"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3">1312-002</td><td class="s3">RS Dashboard Updates<br>1. Tab Updates</td><td class="s4">Minor</td><td class="s3">Verify If all User Types are not an approver</td><td class="s3" rowspan="4">1. User has successfully logged in to their Account<br>2. All Users except Root User, IT admin and Purchasing admin</td><td class="s3">1. Verify if User type cannot approve</td><td class="s3">1. All User Types are expected to cannot approve</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s3">1312-003</td><td class="s3">RS Dashboard Updates<br>1. Tab Updates</td><td class="s4">Minor</td><td class="s3">Verify my Request Tabs in User Account</td><td class="s3">1. Verify if My Request tab is Visible</td><td class="s3">1. My Request Tab is expected to be visible</td><td class="s6">Not Started</td><td class="s3"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="783449655R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3">1312-004</td><td class="s3">RS Dashboard Updates<br>1. Tab Updates</td><td class="s4">Minor</td><td class="s3">Verify displayed and highlighted upon viewing of Request Tabs and For my Approval/Assigned</td><td class="s3">1. Verify if Request Tab is highlighted upon viewing</td><td class="s3">1. Request Tab is expected tod be highlighted upon viewing the Request tabs and For my Approval/Assigned</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s3">1312-005</td><td class="s3">RS Dashboard Updates<br>1. Tab Updates</td><td class="s4">High</td><td class="s3">Verify all User Types as an approver</td><td class="s3">1. Verify if Users type can approve </td><td class="s3">1. All User Types are able to approve</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s2" colspan="11">Update Create New Button</td></tr><tr style="height: 19px"><th id="783449655R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s3">1312-006</td><td class="s3">RS Dashboard Updates<br>1. Update Create New Button</td><td class="s4">Critical</td><td class="s3">Verify if User has successfully logged in to their Account</td><td class="s3">1. All User Types except Root User</td><td class="s3">1. Login User account</td><td class="s3">1. Verify if User is succesfully logged in</td><td class="s3" rowspan="4"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s3">1312-007</td><td class="s3">RS Dashboard Updates<br>1. Update Create New Button</td><td class="s4">High</td><td class="s3">Verify if Dashboard tabs are displayed</td><td class="s3" rowspan="2">1. User has successfully logged in to their Account<br>2. All User Types except Root User</td><td class="s3">1. Login User account<br>2. Verify if Dashboard tab is visible</td><td class="s3">2. Dashboard Tab is expected to be visible</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s3">1312-008</td><td class="s3">RS Dashboard Updates<br>1. Update Create New Button</td><td class="s4">High</td><td class="s3">Verify if updated Dashboard tab is visible</td><td class="s3">1. Login User account<br>2. Verify Dashboard tab contents</td><td class="s3">2. Dashboard tab is expected to display the updated Columns of the Dashboard Page<br>    a. Ref. Number<br>    b. Document Type<br>    c. Requester<br>    d. Company<br>    e. Proj/Department<br>    f. Last Updated<br>    g. Status</td><td class="s6">Not Started</td><td class="s3"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="783449655R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s3">1312-009</td><td class="s3">RS Dashboard Updates<br>1. Update Create New Button</td><td class="s4">Minor</td><td class="s3">Verify if the following contents for the updated Columns of the Dashboard Page is displayed</td><td class="s3">1. User has successfully logged in to their Account<br>2. All User Types except Root User</td><td class="s3">1. Login User account<br>2. Verify Dashboard tab contents</td><td class="s3">2.  Should have the following contents for the updated Columns of the Dashboard Page<br>    a. Ref. Number<br>        i. Reference Number of all Documents<br>    b. Document Type<br>        i. RS<br>        ii. Canvassing<br>        iii. Order<br>        iv. Delivery<br>        v. Invoice<br>        vi. Voucher<br>        vii. Non-RS<br>    c. Requester<br>        i. Requester&#39;s Name that created the Document<br>           i) Can be the Requester of Requisition Slip<br>               a) For Requisition Slip, Delivery Receipt, and Non-RS<br>           ii) Can be the Assigned Purchasing Staff&#39;s Name<br>               a) For Canvass Sheet, Purchase Order, Invoice, and Payment Request<br>    d. Company<br>        i. Company indicated in the Requisition Slip<br>    e. Proj/Department<br>        i. Project/Department indicated in the Requisition Slip<br>    f. Last Updated<br>        i. Date that the Document was last updated<br>    g. Status<br>        i. Status of the Document</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s2" colspan="11">Dashboard Table Contents</td></tr><tr style="height: 19px"><th id="783449655R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s3">1312-010</td><td class="s3">RS Dashboard Updates<br>1. Dashboard Table Contents</td><td class="s4">Critical</td><td class="s3">Verify if User has successfully logged in to their Account</td><td class="s3">1. All User Types except Root User</td><td class="s3">1. Login User account</td><td class="s3">1. Verify if User is succesfully logged in</td><td class="s3" rowspan="4"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s3">1312-011</td><td class="s3"></td><td class="s4">High</td><td class="s3">Verify if Dashboard tabs are displayed</td><td class="s3" rowspan="2">1. User has successfully logged in to their Account<br>2. Requisition Slip or any of the Documents has been created<br>3. All Users except Root User</td><td class="s3">1. Login User account<br>2. Verify if Dashboard tab is visible</td><td class="s3">2. Dashboard Tab is expected to be visible</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s3">1312-012</td><td class="s3"></td><td class="s4">High</td><td class="s3">Verify if updated Dashboard tab is visible</td><td class="s3">1. Login User account<br>2. Verify Dashboard tab contents</td><td class="s3">2. Dashboard tab is expected to display the updated Columns of the Dashboard Page<br>    a. Ref. Number<br>    b. Document Type<br>    c. Requester<br>    d. Company<br>    e. Proj/Department<br>    f. Last Updated<br>    g. Status</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s3">1312-013</td><td class="s3"></td><td class="s4">Minor</td><td class="s3">Verify if the following contents for the updated Columns of the Dashboard Page is displayed</td><td class="s3">1. User has successfully logged in to their Account<br>2. All User Types except Root User</td><td class="s3">1. Login User account<br>2. Verify Dashboard tab contents</td><td class="s3">2.  Should have the following contents for the updated Columns of the Dashboard Page<br>    a. Ref. Number<br>        i. Reference Number of all Documents<br>    b. Document Type<br>        i. RS<br>        ii. Canvassing<br>        iii. Order<br>        iv. Delivery<br>        v. Invoice<br>        vi. Voucher<br>        vii. Non-RS<br>    c. Requester<br>        i. Requester&#39;s Name that created the Document<br>           i) Can be the Requester of Requisition Slip<br>               a) For Requisition Slip, Delivery Receipt, and Non-RS<br>           ii) Can be the Assigned Purchasing Staff&#39;s Name<br>               a) For Canvass Sheet, Purchase Order, Invoice, and Payment Request<br>    d. Company<br>        i. Company indicated in the Requisition Slip<br>    e. Proj/Department<br>        i. Project/Department indicated in the Requisition Slip<br>    f. Last Updated<br>        i. Date that the Document was last updated<br>    g. Status<br>        i. Status of the Document</td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s2" colspan="11">RS Dashboard Updates - Sorting of Closed RS and related Documents</td></tr><tr style="height: 19px"><th id="783449655R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s8"></td><td class="s9"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS Dashboard Updates</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Sorting of Closed RS and related Documents</span></td><td class="s8">High</td><td class="s8">Verify dashboard page with Tabs displaying Requisition Slip data</td><td class="s8">1. Logged in as a User (except Root User)<br>2. At least one of the Requisition Slip has been Closed</td><td class="s8">1. Navigate to the Dashboard page</td><td class="s8">2. Dashboard page loads successfully with Tabs displaying Requisition Slip data</td><td class="s3"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s8"></td><td class="s9"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS Dashboard Updates</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Sorting of Closed RS and related Documents</span></td><td class="s8">High</td><td class="s8">Verify that all Tabs and their data are sorted by the Latest Updated Date descending (newest first)</td><td class="s8">1. Logged in as a User (except Root User)<br>2. At least one of the Requisition Slip has been Closed</td><td class="s8">1. Observe the order of the tabs/documents</td><td class="s8">2. Tabs should be listed in order of the most recently updated Requisition Slip or its related document</td><td class="s3"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s8"></td><td class="s9"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS Dashboard Updates</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Sorting of Closed RS and related Documents</span></td><td class="s8">High</td><td class="s8">Verify that Active or Not Yet Closed Requisition Slips are listed first </td><td class="s8">1. Logged in as a User (except Root User)<br>2. At least one of the Requisition Slip has been Closed</td><td class="s8">1. Observe the first section of the table displaying Requisition Slips</td><td class="s8">2. All Requisition Slips and their Related Documents that are Active or Not Yet Closed are listed first</td><td class="s3"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="783449655R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s8"></td><td class="s9"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS Dashboard Updates</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Sorting of Closed RS and related Documents</span></td><td class="s8">High</td><td class="s8">Verify that Closed Requisition Slips and their related documents appear after all active ones</td><td class="s8">1. Logged in as a User (except Root User)<br>2. At least one of the Requisition Slip has been Closed</td><td class="s8">1. Scroll to the bottom of the Requisition Slip list</td><td class="s8">2. Closed Requisition Slips appear at the bottom portion of the list, even if their updated date is more recent than an Active one</td><td class="s3"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s8"></td><td class="s9"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">RS Dashboard Updates</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> - Sorting of Closed RS and related Documents</span></td><td class="s8">Critical </td><td class="s8">Validate that the sorting remains consistent after a page refresh</td><td class="s8">1. Logged in as a User (except Root User)<br>2. At least one of the Requisition Slip has been Closed</td><td class="s8">1. Click Refresh button <br>2. Observe Requisition Slip list</td><td class="s8">2. Order of documents/tabs remains sorted by Latest Updated Date and categorized by status (Active/Closed) after page reload</td><td class="s3"></td><td class="s6">Not Started</td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s10"></td><td class="s11"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s10"></td><td class="s11"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s10"></td><td class="s11"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s10"></td><td class="s11"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s10"></td><td class="s11"></td><td class="s3"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="783449655R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s10"></td><td class="s11"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="783449655R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s3"></td><td class="s3"></td><td class="s4"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s11"></td><td class="s3"></td><td class="s3"></td></tr></tbody></table></div>