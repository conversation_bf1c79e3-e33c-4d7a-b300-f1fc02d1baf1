const BaseRepository = require('./baseRepository');

class RequisitionInvoiceHistoryRepository extends BaseRepository {
  constructor ({ db, clientErrors, utils }) {
    super(db.invoiceReportHistoryModel);
    this.clientErrors = clientErrors;
    this.Sequelize = db.Sequelize;
    this.db = db;
    this.utils = utils;
  }

  async getRequisitionHistory(payload) {
    const {
      requisitionId,
      limit = 10,
      page = 1,
      sort = [['updatedAt', 'DESC']],
      search = null,
    } = payload;

    const offset = (page - 1) * limit;

    const { count, rows } = await this.tableName.findAndCountAll({
      where: {
        [this.db.Sequelize.Op.and]: [
          {
            requisitionId,
          },
          search
            ? {
              irNumber: {
                [this.db.Sequelize.Op.iLike]: `%${search}%`,
              },
            }
            : null,
        ],
      },
      order: sort,
      limit,
      offset,
    });

    return {
      data: rows,
      total: count,
    };
  }
}

module.exports = RequisitionInvoiceHistoryRepository;
