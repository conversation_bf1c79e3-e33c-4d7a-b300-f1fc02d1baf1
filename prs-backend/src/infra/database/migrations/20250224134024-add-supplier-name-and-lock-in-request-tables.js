'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tables = ['canvass_item_suppliers', 'purchase_orders', 'delivery_receipts'];

    for (const table of tables) {
      if (table !== 'delivery_receipts') {
        await queryInterface.addColumn(table, 'supplier_name', {
          type: Sequelize.STRING,
          allowNull: true,
        });
      }

      await queryInterface.addColumn(table, 'supplier_name_locked', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      });
    }
  },

  async down(queryInterface, Sequelize) {
    const tables = ['canvass_item_suppliers', 'purchase_orders', 'delivery_receipts'];

    for (const table of tables) {
      if (table !== 'delivery_receipts') {
        await queryInterface.removeColumn(table, 'supplier_name');
      }
      await queryInterface.removeColumn(table, 'supplier_name_locked');
    }
  }
};

