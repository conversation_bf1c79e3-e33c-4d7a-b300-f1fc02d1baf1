'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async down(queryInterface, Sequelize) {},
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('purchase_orders', 'total_amount', {
      type: Sequelize.DECIMAL(20, 2),
      allowNull: true,
    });

    await queryInterface.addColumn('purchase_orders', 'total_discount', {
      type: Sequelize.DECIMAL(20, 2),
      allowNull: true,
    });

    await queryInterface.addColumn(
      'purchase_orders',
      'total_discounted_amount',
      {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
      },
    );
  },
};
