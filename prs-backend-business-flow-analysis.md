# PRS-Backend Business Flow Analysis and Recommendations

## 1. Executive Summary

This document provides a thorough analysis of the business flows and logic in the PRS-Backend system, which is a Purchase Requisition System for Cityland. The analysis identifies key issues in the current implementation and provides recommendations for improvements.

The system follows a complex workflow from requisition creation through approval, canvassing, purchase order generation, delivery receipt management, and payment processing. While the core architecture is sound, there are several areas where the business logic could be improved for better reliability, maintainability, and performance.

## 2. Core Business Flow Analysis

### 2.1 Requisition Flow

The requisition process is the foundation of the entire system and follows these steps:

1. **Creation Phase**
   - Users create requisitions with items (OFM or non-OFM)
   - Requisitions can be saved as drafts or submitted
   - Submitted requisitions enter the approval workflow

2. **Approval Phase**
   - Multiple approvers review the requisition based on department/project
   - Approvers can approve, reject, or add comments
   - All approvers must approve for the requisition to proceed

3. **Assignment Phase**
   - Approved requisitions are assigned to purchasing staff
   - Assignment triggers notifications to the assignee

4. **Canvassing Phase**
   - Assigned staff create canvass sheets for requisition items
   - Multiple suppliers can be added with pricing information
   - Canvass sheets go through their own approval workflow

5. **Purchase Order Phase**
   - Approved canvass sheets generate purchase orders
   - Purchase orders are grouped by supplier
   - POs have their own approval workflow

6. **Delivery Phase**
   - Approved POs move to delivery status
   - Delivery receipts are created when items are received
   - Partial deliveries are supported

7. **Payment Phase**
   - Payment requests are created for delivered items
   - Payment requests go through approval
   - Approved payments close the requisition cycle

### 2.2 Status Flow Analysis

The requisition status flow is complex and involves multiple state transitions:

```
DRAFT → SUBMITTED → APPROVED → ASSIGNING → ASSIGNED → 
PARTIALLY_CANVASSED/CANVASS_FOR_APPROVAL → FOR_PO_REVIEW → 
FOR_PO_APPROVAL → FOR_DELIVERY → FOR_PR_APPROVAL → CLOSED
```

At any point, a requisition can also be REJECTED, requiring revisions and resubmission.

### 2.3 Approval Workflows

The system implements multiple approval workflows:

1. **Requisition Approval**
   - Based on department and project settings
   - Multi-level approval hierarchy
   - Optional approvers can be configured

2. **Canvass Approval**
   - Purchasing supervisors and heads approve canvass sheets
   - Supplier selection is validated

3. **Purchase Order Approval**
   - PO approvers validate terms, pricing, and delivery details
   - Multi-level approval similar to requisition approval

4. **Payment Request Approval**
   - Finance department approvers validate payment details
   - Integration with accounting system

## 3. Critical Issues Identified

### 3.1 Business Logic Issues

1. **Inconsistent Status Management**
   - Status transitions are not consistently enforced
   - Missing validation in several status change operations
   - Example: In `requisitionApproverService.js`, the function `setRSStatusToAssigningWhenFullyApproved` has a TODO comment about checking the requisition status before updating

   ```javascript
   // TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error
   await this.requisitionRepository.update(
     { id: requisitionId },
     { status: 'assigning' },
     { transaction },
   );
   ```

2. **Incomplete Transaction Management**
   - Some operations lack proper transaction handling
   - Potential for data inconsistency if operations fail partially
   - Example: Missing transaction rollback in some error scenarios

3. **Authorization Bypass**
   - Permission checking is disabled in the `authorize.js` middleware:

   ```javascript
   return async function (request, _reply) {
     // disable permission checking, kasi may bug
     return true;
     // ... actual authorization code never runs
   };
   ```

4. **Hardcoded Business Rules**
   - Business rules are scattered throughout the codebase
   - Some rules are hardcoded rather than configurable
   - Example: Approval levels and workflows have hardcoded elements

5. **Incomplete Validation**
   - Some business operations lack comprehensive validation
   - Example: In `purchaseOrderService.js`, the `submitPurchaseOrder` function has a TODO comment:

   ```javascript
   // TODO: do now allow if status is not for (status before for_po_approval)
   ```

### 3.2 Code Structure Issues

1. **Excessive Service Dependencies**
   - Services have too many dependencies
   - Example: `RequisitionService` constructor has over 25 dependencies

2. **Inconsistent Error Handling**
   - Error handling patterns vary across the codebase
   - Some errors are logged but not properly propagated

3. **Commented-Out Code**
   - Several instances of commented-out code indicate incomplete refactoring
   - Example in `requisitionController.js`:

   ```javascript
   try {
     // try { // for degging will refactor tom
     requisition = await this.requisitionRepository.create(body, {
       transaction,
     });
     // } catch (error) {
     //   console.log(error);
     // }
   ```

4. **Duplicate Business Logic**
   - Similar business logic is implemented in multiple places
   - Example: Status validation logic is duplicated across services

## 4. Detailed Flow Analysis

### 4.1 Requisition Creation and Submission

The requisition creation flow is implemented in `requisitionService.js` and `requisitionController.js`. Key observations:

- The system supports multiple requisition types: 'ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'
- Requisitions can be associated with companies, projects, or departments
- Items can be added, updated, or removed from requisitions
- Attachments can be added to requisitions for documentation

**Issues:**
- The item validation logic varies by requisition type, leading to complex conditional code
- The requisition number generation logic is complex and could be simplified
- There's no clear validation of the overall requisition state before submission

### 4.2 Approval Process

The approval process is managed by `requisitionApproverService.js`, `projectApprovalService.js`, and `departmentApprovalService.js`. Key observations:

- Approvers are determined based on department, project, and requisition category
- Approvers are assigned levels to determine the approval sequence
- Optional approvers can be added to the workflow
- Rejection by any approver stops the workflow

**Issues:**
- The logic to determine approvers is complex and spread across multiple services
- There's no clear mechanism to handle approver unavailability (e.g., on leave)
- The approval level determination is not clearly documented

### 4.3 Canvassing Process

The canvassing process is implemented in `canvassService.js` and `canvassController.js`. Key observations:

- Canvass sheets can be created as drafts or submitted directly
- Multiple suppliers can be added for each item
- Suppliers can be of different types (supplier, project, company)
- Pricing, discounts, and terms are captured for each supplier

**Issues:**
- The supplier selection validation is complex and could be simplified
- The canvass item status management is not consistently enforced
- The partial canvassing logic is complex and error-prone

### 4.4 Purchase Order Generation

Purchase orders are managed by `purchaseOrderService.js`. Key observations:

- POs are generated automatically after canvass approval
- POs are grouped by supplier
- Multiple items can be included in a single PO
- POs have their own approval workflow

**Issues:**
- The PO number generation logic is complex and could be simplified
- The PO approval workflow is not clearly separated from the PO creation
- There's incomplete validation when submitting POs for approval

### 4.5 Delivery Receipt Management

Delivery receipts are managed by `deliveryReceiptService.js`. Key observations:

- Delivery receipts are created for approved POs
- Partial deliveries are supported
- Returns can be processed
- Attachments (like invoices) can be added to delivery receipts

**Issues:**
- The delivery item status management is complex
- The return process is not clearly separated from the delivery process
- The validation of delivered quantities against ordered quantities is complex

### 4.6 Payment Request Processing

Payment requests are managed by `rsPaymentRequestService.js`. Key observations:

- Payment requests are created for delivered items
- Payment terms are applied based on PO terms
- Payment requests have their own approval workflow
- Approved payments close the requisition cycle

**Issues:**
- The payment request approval workflow is not clearly separated from the creation
- The integration with the accounting system is not well-defined
- The requisition closure logic is not consistently applied

## 5. Recommendations for Improvement

### 5.1 Business Logic Improvements

1. **Implement State Machine Pattern for Status Management**
   - Define clear state transitions for requisitions and related entities
   - Implement validation for each state transition
   - Centralize state transition logic

   ```javascript
   // Example implementation
   class RequisitionStateMachine {
     constructor(requisition) {
       this.requisition = requisition;
     }
     
     canTransitionTo(targetStatus) {
       const currentStatus = this.requisition.status;
       const validTransitions = {
         'draft': ['submitted'],
         'submitted': ['approved', 'rejected'],
         'approved': ['assigning'],
         // ... other valid transitions
       };
       
       return validTransitions[currentStatus]?.includes(targetStatus) || false;
     }
     
     transitionTo(targetStatus, transaction) {
       if (!this.canTransitionTo(targetStatus)) {
         throw new Error(`Invalid status transition from ${this.requisition.status} to ${targetStatus}`);
       }
       
       // Perform the transition
       return requisitionRepository.update(
         { id: this.requisition.id },
         { status: targetStatus },
         { transaction }
       );
     }
   }
   ```

2. **Improve Transaction Management**
   - Ensure all multi-step operations use transactions
   - Implement consistent error handling with proper rollbacks
   - Consider using a transaction manager utility

   ```javascript
   // Example transaction manager
   async function withTransaction(db, callback) {
     const transaction = await db.sequelize.transaction();
     try {
       const result = await callback(transaction);
       await transaction.commit();
       return result;
     } catch (error) {
       await transaction.rollback();
       throw error;
     }
   }
   ```

3. **Fix Authorization System**
   - Re-enable permission checking in the `authorize.js` middleware
   - Implement proper role-based access control
   - Add comprehensive permission validation

4. **Extract Business Rules to Configuration**
   - Move hardcoded business rules to configuration files or database
   - Implement a rule engine for complex business rules
   - Make approval workflows configurable

5. **Implement Comprehensive Validation**
   - Add pre-condition and post-condition validation for all business operations
   - Centralize validation logic for reuse
   - Implement domain-specific validation rules

### 5.2 Code Structure Improvements

1. **Reduce Service Dependencies**
   - Refactor services to have fewer dependencies
   - Use composition over inheritance
   - Implement domain-driven design patterns

   ```javascript
   // Before
   class RequisitionService {
     constructor(container) {
       const {
         db, utils, entities, userRepository, requisitionRepository,
         requisitionItemListRepository, tomItemRepository, commentRepository,
         attachmentRepository, supplierRepository, projectRepository,
         companyRepository, constants, projectApprovalRepository,
         clientErrors, requisitionApproverRepository, departmentApprovalRepository,
         fastify, itemRepository, nonOfmItemRepository, notificationRepository,
         historyRepository, canvassRequisitionRepository, purchaseOrderRepository,
         deliveryReceiptRepository, rsPaymentRequestRepository,
         notificationService, departmentRepository,
         departmentAssociationApprovalRepository,
       } = container;
       // ... many assignments
     }
   }
   
   // After
   class RequisitionService {
     constructor({
       requisitionRepository,
       requisitionItemRepository,
       approvalService,
       notificationService,
       db,
       logger,
     }) {
       this.requisitionRepository = requisitionRepository;
       this.requisitionItemRepository = requisitionItemRepository;
       this.approvalService = approvalService;
       this.notificationService = notificationService;
       this.db = db;
       this.logger = logger;
     }
   }
   ```

2. **Standardize Error Handling**
   - Implement consistent error handling patterns
   - Use custom error classes for domain-specific errors
   - Ensure proper error propagation

   ```javascript
   // Example domain-specific error classes
   class RequisitionError extends Error {
     constructor(message, code = 'REQUISITION_ERROR') {
       super(message);
       this.code = code;
     }
   }
   
   class InvalidStatusTransitionError extends RequisitionError {
     constructor(currentStatus, targetStatus) {
       super(`Invalid status transition from ${currentStatus} to ${targetStatus}`, 'INVALID_STATUS_TRANSITION');
       this.currentStatus = currentStatus;
       this.targetStatus = targetStatus;
     }
   }
   ```

3. **Clean Up Commented-Out Code**
   - Remove all commented-out code
   - Add proper documentation for complex logic
   - Use version control for code history instead of comments

4. **Eliminate Duplicate Business Logic**
   - Extract common logic to shared utilities
   - Implement the DRY (Don't Repeat Yourself) principle
   - Use composition to share behavior

### 5.3 Specific Flow Improvements

1. **Requisition Creation and Submission**
   - Simplify item validation logic
   - Implement a cleaner requisition number generation strategy
   - Add comprehensive validation before submission

2. **Approval Process**
   - Centralize approver determination logic
   - Implement a clear mechanism for approver delegation
   - Document the approval level determination logic

3. **Canvassing Process**
   - Simplify supplier selection validation
   - Implement consistent canvass item status management
   - Refactor partial canvassing logic for clarity

4. **Purchase Order Generation**
   - Simplify PO number generation logic
   - Clearly separate PO creation from approval workflow
   - Add comprehensive validation for PO submission

5. **Delivery Receipt Management**
   - Simplify delivery item status management
   - Clearly separate delivery and return processes
   - Improve validation of delivered quantities

6. **Payment Request Processing**
   - Clearly separate payment request creation from approval
   - Define a clear integration strategy with the accounting system
   - Ensure consistent requisition closure logic

## 6. Implementation Roadmap

### 6.1 Phase 1: Critical Fixes (1-2 weeks)
1. Fix the authorization system by re-enabling permission checking
2. Implement proper transaction management for all critical operations
3. Add missing validations for status transitions
4. Clean up commented-out code and fix obvious bugs

### 6.2 Phase 2: Business Logic Refactoring (2-4 weeks)
1. Implement state machine pattern for status management
2. Extract business rules to configuration
3. Standardize error handling
4. Eliminate duplicate business logic

### 6.3 Phase 3: Code Structure Improvements (3-6 weeks)
1. Reduce service dependencies
2. Implement domain-driven design patterns
3. Improve test coverage
4. Refactor complex methods for clarity

### 6.4 Phase 4: Flow-Specific Improvements (4-8 weeks)
1. Refactor requisition creation and submission
2. Improve approval process
3. Enhance canvassing process
4. Optimize purchase order generation
5. Streamline delivery receipt management
6. Refine payment request processing

## 7. Conclusion

The PRS-Backend system has a solid foundation but suffers from several issues in its business logic implementation. By addressing the identified issues and implementing the recommended improvements, the system can become more reliable, maintainable, and performant.

The most critical issues to address are:
1. The disabled permission checking in the authorization middleware
2. Inconsistent status management across the workflow
3. Incomplete transaction management
4. Excessive service dependencies
5. Duplicate business logic

By following the proposed implementation roadmap, these issues can be systematically addressed while minimizing disruption to the existing system.
