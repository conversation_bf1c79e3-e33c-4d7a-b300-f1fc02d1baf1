# Payment Business Rules

This document outlines the business rules for the payment/voucher request process in the PRS system.

## Payment Request Entity

A payment request (PR) is a document that requests for payment of purchased items

### Payment Request Status Flow

```
DRAFT → FOR_PR_APPROVAL → CLOSED_PR;
PR_REJECTED

```

- PR can be REJECTED during FOR_PR_APPROVAL status

## Business Rules

### Payment Creation Rules

- Only Assigned Purchasing Staff can create PR
- An PR can be created as a draft or submitted immediately
- PR Number is auto-generated
- PR Letter is auto-generated
- Payment Requests can only be created for approved Purchase Orders with Invoice
- A Purchase Order can have multiple payment requests (partial payments)
- The sum of all payment requests (PR) for a Purchase Order cannot exceed the total PO amount
- Each payment must reference the Purchase Order and related invoices
- Sum of selected invoice amount would be the PR amount
- Payment terms must comply with the terms specified in the Purchase Order


### Payment Approval Rules

- Only FOR_PR_APPROVAL payment requests can be approved
- User must be an assigned approver for the payment request
- Approvers are processed in order by level
- Status will remain as FOR APPROVAL until fully approved
- Additional approvers can be added during the approval process
- Approvers can input a note during approval
- If all approvers approve, status changes to CLOSED_PR


### Payment Processing Rules

- If PR is fully approved, system to post payment request to Cityland’s legacy system via an API

### Payment Rejection Rules

- Only FOR_PR_APPROVAL payment request can be rejected
- User must be an assigned approver for the payment request
- Rejection requires a reason/comment
- Upon rejection, status changes to PR_REJECTED
- Assigned Purchasing Staff should be able to edit rejected PR
- Can resubmit and will undergo approval process again with status FOR_PR_APPROVAL

