<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{background-color:#ffffff;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-left:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-right:none;border-bottom:1px SOLID #000000;background-color:#f7f8fa;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Inter,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="450853347C0" style="width:120px;" class="column-headers-background">A</th><th id="450853347C1" style="width:219px;" class="column-headers-background">B</th><th id="450853347C2" style="width:78px;" class="column-headers-background">C</th><th id="450853347C3" style="width:252px;" class="column-headers-background">D</th><th id="450853347C4" style="width:233px;" class="column-headers-background">E</th><th id="450853347C5" style="width:330px;" class="column-headers-background">F</th><th id="450853347C7" style="width:258px;" class="column-headers-background">H</th><th id="450853347C8" style="width:132px;" class="column-headers-background">I</th><th id="450853347C9" style="width:164px;" class="column-headers-background">J</th><th id="450853347C10" style="width:245px;" class="column-headers-background">K</th><th id="450853347C11" style="width:133px;" class="column-headers-background">L</th></tr></thead><tbody><tr style="height: 42px"><th id="450853347R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s1">Priority</td><td class="s2">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="450853347R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="11">Allow Assigned Purchasing Staff to create a Delivery Rport on behalf of the Requester</td></tr><tr style="height: 19px"><th id="450853347R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-1498-001</td><td class="s5">Allow Assigned Purchasing Staff to create a Delivery Report on behalf of the Requester</td><td class="s4">Critical</td><td class="s4">Verify if Record Delivery Receipt is displayed for Assigned Purchasing Staff</td><td class="s4">1. User should be logged in as Assigned Purchasing Staff<br>2. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Action<br>3. Validate Record Delivery Receipt button</td><td class="s4">3. Should display and be able to click Record Delivery Receipt button</td><td class="s6" rowspan="3"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1UktWmmqiQy6KGGv70_W1vLeROpEKuopd1FCPkY3IHGk/edit?usp=sharing">Test results<br><br><br><br>Justine Sprint 2 Test Results</a></td><td class="s7">Passed</td><td class="s8 softmerge"><div class="softmerge-inner" style="width:280px;left:-1px"><a target="_blank" href="http://**************/app/requisition-slip/760">RS-11AA00000343<br>http://**************/app/requisition-slip/760<br><br>RS-08AA00000342<br>http://**************/app/requisition-slip/759</a></div></td><td class="s9"></td></tr><tr style="height: 19px"><th id="450853347R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-1498-002</td><td class="s5">Allow Assigned Purchasing Staff to create a Delivery Report on behalf of the Requester</td><td class="s4">Critical</td><td class="s4">Verify if Assigned Purchasing Staff is able to create Delivery Report</td><td class="s4">1. User should be logged in as Assigned Purchasing Staff<br>2. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Action<br>3. Click Record Delivery Receipt<br>4. Populate fields<br>5. Submit Delivery Receipt</td><td class="s4">5. Should allow the Assigned Purchasing Staff of the Requisition Slip to create a Delivery Report for Orders that are to be fulfilled<br>     a. As long as the logged in User is the Assigned Purchasing Staf of the Requisition Slip, they should be allowed to create the Delivery Report</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-1498-003</td><td class="s5">Allow Assigned Purchasing Staff to create a Delivery Report on behalf of the Requester</td><td class="s4">High</td><td class="s4">Verify if Other Purchasing Staff is able to create Delivery Report </td><td class="s4">1. User should be logged in as Purchasing Staff but not assigned in RS<br>2. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Action<br>3. Verify Record Delivery Receipt button</td><td class="s4">3. Should not display the Record Delivery Receipt button</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3" colspan="11">DELIVERY RECEIPT - Update Delivery Receipt Form</td></tr><tr style="height: 19px"><th id="450853347R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-1472-001</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify Select Actions button to access Delivery Report form</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Navigate to the right side<br>3. Verify the Select Actions button </td><td class="s4">3. User should be able to click Select Actions Button in Requisition Slip</td><td class="s11" rowspan="22"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1FyKHlVSw7jtjfYy2psQXvx-22pFuGEgmrTH_5dsJLQ4/edit?gid=1883190894#gid=1883190894">Cherry Sprint 2 Test Results</a></td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-1472-002</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify Floating Banner in the purchase order</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Go to Related Documents<br>3. Click Orders<br>4. Select PO number<br>5. Verify Floating banner</td><td class="s12"><a target="_blank" href="https://www.figma.com/design/UNpEbFVvETKilgpf7m4d8e/Cityland---PRS-(Desktop)-(Copy)?node-id=16436-156027&amp;t=Y7Utdl5blk1weSBY-0">5. Floating banner should be visible and displayed up until all items in the PO has been fully delivered<br><br>Floating Banner</a></td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-1472-003</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify Floating Banner in the purchase order is not visible</td><td class="s4">1. Purchase Order has a status of not yet For delivery &quot;For PO Review&quot;</td><td class="s4">1.  Select RS Number<br>2. Go to Related Documents<br>3. Click Orders<br>4. Select Deliveries<br>5. Input amounts for deliveries<br>6. Verify floating banner</td><td class="s4">6. Floating banner should not be visible after Purchase order is not for delivery</td><td class="s7">Passed</td><td class="s13"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">PRS-1472-004</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify the display of Delivery Report form with all fields required except for Notes </td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. click Select Actions<br>3. click Record Delivery Receipt<br>4. Verify the display of Delivery Report Form</td><td class="s4">4. Delivery report form should be visible with all the fields required except Notes</td><td class="s7">Passed</td><td class="s10">5/2: only DR Fields are testable, Items section are not covered yet</td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-1472-005</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify the display of DR Number</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. click Select Actions<br>3. click Record Delivery Receipt<br>4. Navigate to the top left corner<br>5. Verify DR Number</td><td class="s4">5. DR number should be visible.<br><br>a. Should be displayed as &quot;---&quot; if not yet created</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">PRS-1472-006</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify the display of RS Number</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. click Select Actions<br>3. click Record Delivery Receipt<br>4. Navigate to the top left corner<br>5. Verify RS Number</td><td class="s4">5. RS Number should be displayed as being the reference for the Delivery Report</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">PRS-1472-007</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify the display of DR status</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Go to Related Documents<br>3. Click Deliveries<br>4. Verify the display of DR status</td><td class="s4">5. DR should be displayed<br><br>a. Should displayed as draft if not yet created</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">PRS-1472-008</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify the display of Purchase order drop-down field </td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Go to Related Documents<br>3. Click Orders<br>4. Select PO number<br>5. Verify Purchase order drop-down field</td><td class="s12"><a target="_blank" href="https://www.figma.com/design/UNpEbFVvETKilgpf7m4d8e/Cityland---PRS-(Desktop)-(Copy)?node-id=16436-156027&amp;t=Y7Utdl5blk1weSBY-0">5. Purchase order drop-down field should be visible.<br><br>a. display a purchase order numbers that is now for delivery<br>drop-down field</a></td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">PRS-1472-009</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify the DR issued Date Picker</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Input date</td><td class="s4">5. User should be able to pick and select current and previous dates</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">PRS-1472-010</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify the Delivery Invoice No</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Verify Delivery Invoice No.</td><td class="s4">5. Delivery Invoice No. should be visible and accept Alphanumeric and Special characters except emojis</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-1472-011</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify the Maximum characters Delivery Invoice No </td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Input Delivery Invoice No.</td><td class="s4">5. Delivery Invoice No. should only have a maximum of 50 characters</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4">PRS-1472-012</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify Supplier Delivery issued date</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Verify Supplier Delivery Issued Date</td><td class="s4">5. User should be able to select and pick current date and previous date</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4">PRS-1472-013</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify file attachment</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Verify Attachment/s</td><td class="s4">5. User should be able to attach file.<br><br>a. Should allow maximum of 25 MB per file with File Types of JPG, JPEG, PNG, Excel, and CSV</td><td class="s14">Failed</td><td class="s10">5/2: unable to save attachment during draft</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1711">5/2: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1711</a></td></tr><tr style="height: 19px"><th id="450853347R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s4">PRS-1472-014</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify the display and functionality of Notes</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Verify Notes</td><td class="s4">5. User should be able to input Notes.<br><br>a. should be alphanumeric and special characters except emojis</td><td class="s14">Failed</td><td class="s10">5/2 Unable to accept special characters</td><td class="s15"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1712">5/2:: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1712</a></td></tr><tr style="height: 19px"><th id="450853347R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s4">PRS-1472-015</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify the Maximum characters in Notes</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Populate required fields<br>5. Input Notes</td><td class="s4">5. Notes should have a maximum of 100 characters</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s4">PRS-1472-016</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify the display of Items section</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Scroll down<br>5. Verify Item/s</td><td class="s4">5. Items section should be visible </td><td class="s16">Out of Scope</td><td class="s10">5/2: only DR Fields are testable, Items section are not covered yet</td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s4">PRS-1472-017</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Critical</td><td class="s4">Verify the Save Draft Buttons</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify Save draft button</td><td class="s4">4. Delivery Report will be save as Draft</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s4">PRS-1472-018</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Critical</td><td class="s4">Verify the Submit Buttons</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify submit button</td><td class="s4">4. Delivery Report will be save and be submitted</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s4">PRS-1472-019</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Critical</td><td class="s4">Verify the Cancel Buttons</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify cancel button</td><td class="s4">4. Delivery Report will be cancelled</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s4">PRS-1472-020</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">Minor</td><td class="s4">Verify the implementation of the updated fields when viewing the submitted created delivery report</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Created and Submitted Delivery Report</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify the implementation of the updated fields</td><td class="s12"><a target="_blank" href="https://www.figma.com/design/UNpEbFVvETKilgpf7m4d8e/Cityland---PRS--Desktop---UI-Changes-?node-id=16555-56345&amp;t=oTeUHTk6CarC75p2-4">4. Should implement the updated Fields when Viewing the submitted created Delivery Report<br><br>Updated fields of Delivery Report <br></a></td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s4">PRS-1472-021</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify submitting Delivery Report</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Created Delivery Report</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Create Delivery Report<br>5. Submit Delivery Report</td><td class="s4">4. Status will be change to For invoice receiving and a modal will appear confirming delivery upon submitting the Delivery report</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s4">PRS-1472-022</td><td class="s5">DELIVERY RECEIPT - Update Delivery Receipt Form</td><td class="s4">High</td><td class="s4">Verify Cancelling Delivery Report</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Created Delivery Report</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Create Delivery Report<br>5. Cancel Delivery Report</td><td class="s4">5. A modal will appear confirming if user want to continue cancellation or cancel<br><br>a. All changes will not be saved</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s3" colspan="11">DELIVERY RECEIPT - Multiple DR per PO</td></tr><tr style="height: 19px"><th id="450853347R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s4">PRS-002-001</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO</td><td class="s4">High</td><td class="s4">Verify the allowing of user to select the purchase order number that they will be creating a delivery report</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Logged in as the Requester</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify the implementation of the creating delivery report</td><td class="s4">4. User should be able to select PO number that they will creating a delivery report<br><br>a. Only the Purchase Orders with a status for delivery should be displayed as an option<br>(Field is required)</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s4">PRS-002-002</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO</td><td class="s4">High</td><td class="s4">Verify creating multiple DR for non Requester</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Logged in as the non Requester</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify the implementation of the creating delivery report</td><td class="s4">4. the non requester user cannot create a multiple DR</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s4">PRS-002-003</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO</td><td class="s4">High</td><td class="s4">Verify selecting a DR Issued Date</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Logged in as the Requester</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify the implementation of the creating delivery report</td><td class="s4">User must be able to select DR Issued date<br><br>a. Date should not be greater than the current date<br>(Field is required)</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s4">PRS-002-004</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO</td><td class="s4">High</td><td class="s4">Verify retaining of the behavior of the Attachment/s</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. a File was attached</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Attached file<br>5. Verify the implementation of the Attachment/s</td><td class="s4">5. File attached should be retained<br>(Field is required)<br><br>The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s4">PRS-002-005</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO</td><td class="s4">High</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify Attachment/s<br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. a File was attached</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Attached file bigger than 25mb</td><td class="s4">4. File attachment should not proceed<br><br>The maximum size for each file is 25 MB. File formats - PNG, JPG, JPEG, PDF, Excel, CSV.</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s4">PRS-002-006</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO</td><td class="s4">High</td><td class="s4">Verify allowing the requester to input a Note </td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Input Note<br>5. Verify the implementation of the Note</td><td class="s4">5. Requester should be able to input Notes<br>(Field is optional)<br><br>Notes should be maximum of 50 characters and should be Alphanumeric and Special Characters except Emojis</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s4">PRS-002-007</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify Add item/s button in item section if clickable</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify the implementation of the Add item/s button</td><td class="s4">4. Add item/s button should be clickable, displaying a Modal with the following field:<br>a. Item<br>b. Date Delivered<br>c. Qty Ordered<br>d. Qty Delivered<br>e. Qty Returned<br>f. Notes</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s4">PRS-002-008</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Item&quot; from the field of Items section</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify the implementation of the Items field</td><td class="s4">4. Displays the Drop-down of Items from the Purchase Order that are not yet added to the Delivery Report.</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s4">PRS-002-009</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Date Delivered&quot; from the field of Items section</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify the Date Delivered</td><td class="s4">5. Date should be autofilled to the current Date.<br>a. Should be disabled</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s4">PRS-002-010</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Qty Ordered&quot; from the field of items section</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify the Qty Delivered</td><td class="s4">5. Qty Ordered field should be automatically filled once item has been selected</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s4">PRS-002-011</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Qty Delivered&quot; from the field of items section </td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify the Qty Delivered</td><td class="s4">5. Qty Delivered field should be disabled until an Item has been selected</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s4">PRS-002-012</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Qty Returned&quot; from the field of items section</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify the Qty Returned</td><td class="s4">5. Qty Returned field should be disabled until an item has been selected</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s4">PRS-002-013</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Notes&quot; from the field of items section</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify the Notes</td><td class="s4">5. User should be able to input Notes<br>a. Should be disabled until an item has been selected<br>(Field is optional)<br></td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s4">PRS-002-014</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify &quot;Notes&quot; maximum characters</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Select Notes<br>6. Input 500 characters</td><td class="s4">6. Notes should have a Maximum of 500 characters</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s4">PRS-002-015</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Verify &quot;Notes&quot; acceptable characters<br>[</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#ff0000;">Negative Scenario</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">]</span></td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Select Notes<br>6. Input special characters</td><td class="s4">6. Notes should be should be Alphanumeric and Special Characters except Emojis</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s4">PRS-002-016</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify field validation for Qty Delivered and Qty Returned</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. verify Qty Delivered and Qty Returned</td><td class="s4">5. User should see a Field for Validation for items delivered and items returned</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s4">PRS-002-017</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify Qty Delivered and Qty Returned Values</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Input Qty Delivered and Qty Returned</td><td class="s4">5. Qty Delivered and Qty Returned Values should only be less than or equal to the Qty Ordered</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s4">PRS-002-018</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify allowing the User to enter the Value only for Qty Delivered</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Input Qty Delivered</td><td class="s4">5. Should allow the User to enter the Value only for Qty Delivered</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s4">PRS-002-019</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify allowing the User to enter the Value only in Qty Returned</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Input Qty Returned</td><td class="s4">5. Should allow the User to enter the Value only in Qty Returned</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s4">PRS-002-020</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify allowing of 3 decimal places for Qty Delivered</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Input 3 decimal in Qty Deliverd</td><td class="s4">5. User is allowed to enter 3 decimal places fro Qty Delivered</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s4">PRS-002-021</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify allowing of 3 decimal places for Qty Returned</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Input 3 decimal in Qty Returned</td><td class="s4">5. User is allowed to enter 3 decimal plces fro Qty Returned</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s4">PRS-002-022</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify Cancel button </td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify Cancel Button</td><td class="s4">5. Should close the Modal and not Add the Item to the Delivery Report </td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s4">PRS-002-023</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify Save button</td><td class="s4">1. Purchase Order has a Status of For Delivery</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Verify Save Button</td><td class="s4">5. Should close the Modal and Add the Item in the Items Table</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s4">PRS-002-024</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify allowing the User to add as much Items as needed for the Delivery Report </td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Add another Item/s</td><td class="s4">4. should allow the User to add as much Items as needed for the Delivery Report </td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s4">PRS-002-025</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify the Default Item sorting </td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify default item sorting</td><td class="s4">4. Items should be sorted in 0-9 and A-Z order</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s4">PRS-002-026</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify Item Sorting in Table columns</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify item sorting</td><td class="s4">4. Items sorting can be 0-9, A-Z || 9-0, Z-A</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s4">PRS-002-027</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify Date Delivered sorting in Table columns</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify Date Delivered sorting</td><td class="s4">4. Date Delivered sorting can be Oldest Date-Latest Date or Latest Date-Oldest Date</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s4">PRS-002-028</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify Qty Ordered sorting in Table columns</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify Qty ordered sorting</td><td class="s4">4. Qty Ordered sorting can be 0-9 or 9-0</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s4">PRS-002-029</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify Qty Delivered sorting in Table columns</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify Qty delivered sorting</td><td class="s4">4. Qty Delivered sorting can be 0-9 or 9-1</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s4">PRS-002-030</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify Qty Returned sorting in Table columns</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify Qty Returned sorting</td><td class="s4">4. Qty Returned sorting can be 0-9 or 9-2</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s4">PRS-002-031</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify Notes sorting in Table columns</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Verify Notes sorting</td><td class="s4">4. Notes sorting can display ---, 0-9, A-Z || Z-A, 9-0, ---</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s4">PRS-002-032</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify allowing the User to Save Delivery Report as Draft</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Verify saving DR as draft</td><td class="s4">6. User should be able to save draft<br><br>DR format: D.R. Number (draft):<br>DR-TMP-32AA00000001<br></td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s4">PRS-002-033</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify allowing the User to Submit Delivery Report </td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Submit Delivery Report</td><td class="s4">6. User shoulf be able to proceed afte submitting Delivery Report<br><br>DR format: D.R. Number:<br>DR-05AA00000023</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s4">PRS-002-034</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify the entered Quantity Delivered deduction</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Input amount in Qty Delivered<br>7. Verify Pending Quality</td><td class="s4">7. The entered Quantity Delivered must be deducted Pending Quantity to be fulfilled for the Item</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s4">PRS-002-035</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify the Value for the Qty Returned should not be deducted to the Pending Quantity to be fulfilled for the Item</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Input amount in Qty Returned<br>7. Verify Pending Quality</td><td class="s4">7. the Value for the Qty Returned should not be deducted to the Pending Quantity to be fulfilled for the Item</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s4">PRS-002-036</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify if Quantity Returned are included for the next Delivery Report </td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table<br>3. Delivery Report is already submitted</td><td class="s4">1. Select Item <br>2. Populate Item details<br>3. Verify the Qty Returned</td><td class="s4">3. Quantity Returned should be included in the next report</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s4">PRS-002-037</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify allowing the User to Edit the Delivery Report up until it has an Invoice created to it</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table<br>3. Delivery Report is already made</td><td class="s4">1. Edit Item details<br></td><td class="s4">1. User should be able to edit the Delivery report </td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s4">PRS-002-038</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Critical</td><td class="s4">Verify requiring the User to fulfill the Remaining Quantity Ordered</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Input amount in the Remaining Quantity Ordered</td><td class="s4">6. User is required to fulfill remaining Quantity ordered</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s4">PRS-002-039</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify the Purchase Order Cancelation for the Remaining Quantity Ordered</td><td class="s4">1. Purchase Order has been cancelled</td><td class="s4">1. Verify Item/s section if user can proceed to input</td><td class="s4">1. The item is not required to be fullfill if the Purchase order has been cancelled</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s4">PRS-002-040</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">High</td><td class="s4">Verify allowing the User to create an Invoice for the Item even if it has a Return or the Quantity Delivered is less than the Quantity Ordered</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Input Remaining Qty Delivered</td><td class="s4">6. Should allow the User to create an Invoice for the Item even if it has a Return or the Quantity Delivered is less than the Quantity Ordered</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s4">PRS-002-041</td><td class="s5">DELIVERY RECEIPT - Multiple DR per PO - ITEMS SECTION</td><td class="s4">Minor</td><td class="s4">Verify the display a floating Banner once Submitted</td><td class="s4">1. Purchase Order has a Status of For Delivery<br>2. Item/s are added in the Item/s Table</td><td class="s4">1.  Select RS Number<br>2. Click Select Actions<br>3. Click Record Delivery Receipt<br>4. Select Item<br>5. Populate Item details<br>6. Save and Submit<br>7. Verify if floating banner is visible</td><td class="s4">7. Floating banner should be visible</td><td class="s10"></td><td class="s17">Not Started</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s3" colspan="11">[DELIVERY REPORT] Update Delivery Receipt Items Table (Create and Viewing)</td></tr><tr style="height: 19px"><th id="450853347R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s10">PRS-1458-001</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">Critical</td><td class="s10">Verify that Root User cannot create or view Delivery Reports</td><td class="s10">1. Logged in as Root user.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.</td><td class="s10">1. Attempt to create or view Delivery Report.</td><td class="s10">1. Root User should not have access to Delivery Reports.</td><td class="s11" rowspan="13"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1fWxylEk5sjSxKLOzN5LGRif1sdroVGgNb1WuqebYTGs/edit?gid=514465796#gid=514465796&amp;range=A1:Z1">Verna Sprint 2 Test Results</a></td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s10">PRS-1458-002</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">Critical</td><td class="s10">Verify update on Items Table Columns when creating a Delivery Report.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Verify updated Items table columns.</td><td class="s10">1. Should update the Items Table Columns when creating a Delivery Report.</td><td class="s7">Passed</td><td class="s4" rowspan="2">Values for Delivered Qty and Returned Qty updates upon submission of Delivery Receipt.</td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s10">PRS-1458-003</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">Critical</td><td class="s10">Verify update on Items Table Columns when viewing a Delivery Report.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. View existing delivery report.<br>2. Verify updated Items table columns.</td><td class="s10">1. Should update the Items Table Columns when viewing the created Delivery Report.</td><td class="s7">Passed</td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s10">PRS-1458-004</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify display of Full Name in Item Column.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Observe item name.</td><td class="s10">1. Navigate to Items table.<br>2. Observe item name.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s10">PRS-1458-005</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify Item Table necessary columns.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Verify column names and data displayed.</td><td class="s10">2. Should have the following Columns:<br>a. Item Name<br>b. Requested Qty<br>c. Delivered Qty<br>d. Returned Qty<br>e. Unit<br>f. Actions</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R76" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">77</div></th><td class="s10">PRS-1458-006</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify Delivered Qty display without a value.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.<br>4. Delivered Qty field has no value or is equal to 0.</td><td class="s10">1. Navigate to Items table.<br>2. Observe Delivered Qty field.</td><td class="s10">2.  Should display as &quot;0.000&quot; or &quot;---&quot; if the Quantity for Delivered has no Value.</td><td class="s7">Passed</td><td class="s10">Delivered Qty is required and supposed to be a number. (0.000 is displayed)</td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R77" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">78</div></th><td class="s10">PRS-1458-007</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify Returned Qty display without a value.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Observe and Returned Qty fields.</td><td class="s10">2.  Should display as &quot;---&quot; if the Quantity for Return has no Value.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R78" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">79</div></th><td class="s10">PRS-1458-008</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify Delivered Qty displays 3-Decimal places.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Observe Delivered Qty field.</td><td class="s10">2.  Delivered Qty should display with 3-Decimal Places.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s10">PRS-1458-009</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify Returned Qty displays 3-Decimal places.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Observe and Returned Qty fields.</td><td class="s10">2.  Returned Qty should display with 3-Decimal Places.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td class="s10">PRS-1458-010</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">Minor</td><td class="s10">Verify that input without 3 decimal places is automatically formatted.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Attempt to input Requested/Delivered/Returned Qty without decimals.</td><td class="s10">2. System should format decimal places automatically.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s10">PRS-1458-011</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">Critical</td><td class="s10">Verify Action column functionality.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Navigate to Action column and click the Trash button.</td><td class="s10">2. Should allow updating of the Item Delivery Details.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s10">PRS-1458-012</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify that Steelbar Items follow the updated Columns.</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.</td><td class="s10">1. Navigate to Items table.<br>2. Verify column names and data displayed.</td><td class="s10">2. Should have the following Columns:<br>i. Item Name<br>ii. Requested Qty<br>iii. Delivered Qty<br>iv. Returned Qty Value<br>v. Unit<br>vi. Actions</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s10">PRS-1458-013</td><td class="s10">Update Delivery Receipt Items Table</td><td class="s10">High</td><td class="s10">Verify that Returned Quantity for Steelbars displays &quot;---&quot; if no return value exists</td><td class="s10">1. Logged in as Requester or Assigned Purchasing Staff.<br>2. Purchase Order has a Status of &quot;For Delivery&quot;.<br>3. User must be on Delivery Report page.<br>4. Returned Qty has no value.</td><td class="s10">1. Navigate to Items table.<br>2. Observe and Returned Qty fields.</td><td class="s10">2. Display &quot;---&quot; if no returned quantity for Steelbars.</td><td class="s7">Passed</td><td class="s10"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="450853347R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s18"></td><td class="s19"></td><td class="s18"></td><td class="s18"></td></tr></tbody></table></div>