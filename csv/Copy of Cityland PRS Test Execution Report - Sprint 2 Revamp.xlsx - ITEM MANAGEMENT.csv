Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,Status,Remarks,Defects
Display Account Code in the Non-OFM Landing Page,,,,,,,,,,,
PRS-1271-001,Display Account Code in the Non-OFM Landing Page,Critical,Verify Account Code column visiblity in Non-OFM Items Dashboard,"1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. Non-OFM Items have been created.","1. Navigate to the Non-OFM Items Dashboard page.
2. Verify displayed columns.",,"2. Non-OFM Item Dashboard should display the following columns:
     - Item Name
     - Type
     - Unit
     - Account Code
     -  Actions",Justine_Sprint1_Test Result,Passed,,
PRS-1271-002,Display Account Code in the Non-OFM Landing Page,High,Verify Non-OFM item without an Account Code,"1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. Non-OFM Items have been created without an Account Code.","1. Navigate to the Non-OFM Items Dashboard page.
2. Verify displayed columns.
3. Locate an item without an Account Code.",,"3. The item should be listed in the dashboard. The Account Code column should show ""---"" or ""N/A"".",,Out of Scope,"4/23; Account Code is automatically generated upon the creation of Non-OFM Items, as it serves as the unique identifier for each specific item.

Therefore, an item cannot be created without an Account Code. As a result, an expected result where the Account Code displays as ""---"" or ""N/A"" cannot be verified, since such a scenario will not occur in the system.",
Update Function of Steelbar Dimension,,,,,,,,,,,
PRS-1272-001,Update Function of Steelbar Dimension,Critical,"Verify Weight autofills when Grade, Diameter, and Length are selected.","1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. OFM Items with Steelbar dimensions have been synced.","1. Select a Grade, Diameter, and Length for Steelbar item.
2. Observe the Weight field.",,"The Weight field should autofill based on the selected Grade, Diameter, and Length.

STEELBARS MATRIX:
https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&gid=**********#gid=**********",Ghienel_Sprint1_Test Result,Passed,"4/23: Unable to view/edit ofm items

4/23: Autofill did not match based on Steelbar Matrix","4/23: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1457

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1467"
PRS-1272-002,Update Function of Steelbar Dimension,Critical,"Verify Diameter autofills when Grade, Length, and Weigth are selected.","1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. OFM Items with Steelbar dimensions have been synced.","1. Select a Grade, Length, and Weight for Steelbar item.
2. Observe the Diameter field.",,"The Diameter field should autofill based on the selected Grade, Length, and Weight.

STEELBARS MATRIX:
https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&gid=**********#gid=**********",,Passed,,"4/23

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1469"
PRS-1272-003,Update Function of Steelbar Dimension,Critical,"Verify Length  autofills when Grade, Diameter, and Weight are selected.","1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. OFM Items with Steelbar dimensions have been synced.","1. Select a Grade, Diameter, and Weight for Steelbar item.
2. Observe the Length field.",,"The Length field should autofill based on the selected Grade, Diameter, and Weight.


STEELBARS MATRIX:
https://docs.google.com/spreadsheets/d/1SO8WgjSKZHP_cN3Cw9iboPiWhp44B0Hv1fo-rR23bvc/edit?pli=1&gid=**********#gid=**********",,Passed,,"4/23 

https://youtrack.stratpoint.com/issue/CITYLANDPRS-1470"
PRS-1272-004,Update Function of Steelbar Dimension,High,"Verify system behavior when one or more required fields (Grade, Length, Diameter or Weight) are missing.","1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. OFM Items with Steelbar dimensions have been synced.","1. Leave one or more required fields empty.
2. Click Save.",,An error message should appear indicating that all required fields must be filled to proceed.,,Passed,,"4/23
https://youtrack.stratpoint.com/issue/CITYLANDPRS-1471"
PRS-1272-005,Update Function of Steelbar Dimension,Minor,"Verify that no fields are autofilled when two required fields (Grade, Length, Diameter or Weight) are missing.","1. User is logged in as the ff:
     - IT Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
     - Purchasing Admin
2. OFM Items with Steelbar dimensions have been synced.","1. Leave any two required fields empty.
2. Click Save.",,No autofill should occur and the empty fields should remain blank.,,Passed,,
Allow Decimal to OFM Quanity,,,,,,,,,,,
PRS-1480-001,Allow Decimal to OFM Quanity,Critical,Verify OFM Quantity and GFQ Allow Decimals in Item Details,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. Should have OFM items set up","1. Navigate to OFM Item Dashboard
2. Select an Item
3. Validate GFQ and Qty fields",,3. Should display the OFM Quantity and the GFQ Quantity as a Decimal,Aira_Results,Failed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1696,
PRS-1480-002,Allow Decimal to OFM Quanity,Critical,Verify Quantity with Decimal can be Entered in RS,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. Should have OFM items set up","1. Create RS
2. Populate fields
3. Select OFM as type of request
4. Add items
5. Enter decimal into quantity field
6. Submit RS",,5. Should allow a 3-Decimal places for the OFM Quantity,,Failed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1692,
PRS-1480-003,Allow Decimal to OFM Quanity,Critical,Verify Quantity with Decimal can be Entered in Canvass,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. RS should have OFM Type of Request
3. RS should be approved and for Canvassing","1. Assign a Purchasing Staff
2. Login as Purchasing Staff
3. Click Select Action
4. Click Enter Canvass
5. Add items
6. Click Enter Canvass
7. Populate fields
8. Enter quantity with decimal
9. Confirm Canvass",,9. Should allow a 3-Decimal places for the OFM Quantity,,Passed,,
PRS-1480-004,Allow Decimal to OFM Quanity,Minor,Verify Quantity with more than 3-Decimal places can be Entered in RS,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. Should have OFM items set up","1. Create RS
2. Populate fields
3. Select OFM as type of request
4. Add items
5. Enter decimal with more than 3-Decimal places into quantity field
6. Submit RS",,"4. Should only allow a 3-Decimal places for the OFM Quantity
5. Should not be able to submit RS",,Passed,,
PRS-1480-005,Allow Decimal to OFM Quanity,Minor,Verify Quantity with more than 3-Decimal places can be Entered in Canvass,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. RS should have OFM Type of Request
3. RS should be approved and for Canvassing","1. Assign a Purchasing Staff
2. Login as Purchasing Staff
3. Click Select Action
4. Click Enter Canvass
5. Add items
6. Click Enter Canvass
7. Populate fields
8. Enter quantity with decimal
9. Confirm Canvass",,"8. Should only allow a 3-Decimal places for the OFM Quantity
9. Should not be able to submit Canvass",,Passed,,
PRS-1480-006,Allow Decimal to OFM Quanity,Critical,Verify OFM Quantity is Displayed as Decimals in All Documents,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. RS should have OFM Type of Request
3. RS should have canvass, PO, DR, and Invoice Report","1. Navigate to Dashboard
2. Select RS Number
3. Validate Qty in Item Table
4. Navigate to Related Documents > Canvasses
5. Validate Qty in Item Table
6. Repeat steps 4-5 for Orders, Deliveries, and Invoice Reports",,"3. Should implement the 3-Decimal places of the Quantity across all Documents and viewing of the Item
- RS
- Canvass
- PO 
- PR
- DR",,Passed,,
Allow Decimal to Non-OFM Quanity,,,,,,,,,,,
PRS-1481-001,Allow Decimal to Non-OFM Quanity,Critical,Verify Non-OFM Quantity Allows Decimals in Item Details,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. Should have Non-OFM items set up","1. Navigate to Non-OFM Item Dashboard
2. Select an Item
3. Validate Qty fields",,3. Should display the Non-OFM Quantity and the GFQ Quantity as a Decimal,Gela test results,Out of Scope,no quantity for non-ofm items,
PRS-1481-002,Allow Decimal to Non-OFM Quanity,Critical,Verify Quantity with Decimal can be Entered in RS,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. Should have Non-OFM items set up","1. Create RS
2. Populate fields
3. Select Non-OFM as type of request
4. Add items
5. Enter decimal into quantity field
6. Submit RS",,3. Should allow a 3-Decimal places for the Non-OFM Quantity,,Failed,"CITYLANDPRS-1692 [QA BUGS][REQUISUITION SLIP]Incorrect Text Field Focus on Quantity Input
",
PRS-1481-003,Allow Decimal to Non-OFM Quanity,Critical,Verify Quantity with Decimal can be Entered in Canvass,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. RS should have Non-OFM Type of Request
3. RS should be approved and for Canvassing","1. Assign a Purchasing Staff
2. Login as Purchasing Staff
3. Click Select Action
4. Click Enter Canvass
5. Add items
6. Click Enter Canvass
7. Populate fields
8. Enter quantity with decimal
9. Confirm Canvass",,3. Should allow a 3-Decimal places for the Non-OFM Quantity,,Passed,RS-08AA00000348,
PRS-1481-004,Allow Decimal to Non-OFM Quanity,Minor,Verify Quantity with more than 3-Decimal places can be Entered in RS,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. Should have Non-OFM items set up","1. Create RS
2. Populate fields
3. Select Non-OFM as type of request
4. Add items
5. Enter decimal with more than 3-Decimal places into quantity field
6. Submit RS",,"4. Should only allow a 3-Decimal places for the Non-OFM Quantity
5. Should not be able to submit RS",,Passed,,
PRS-1481-005,Allow Decimal to Non-OFM Quanity,Minor,Verify Quantity with more than 3-Decimal places can be Entered in Canvass,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. RS should have Non-OFM Type of Request
3. RS should be approved and for Canvassing","1. Assign a Purchasing Staff
2. Login as Purchasing Staff
3. Click Select Action
4. Click Enter Canvass
5. Add items
6. Click Enter Canvass
7. Populate fields
8. Enter quantity with decimal
9. Confirm Canvass",,"8. Should only allow a 3-Decimal places for the Non-OFM Quantity
9. Should not be able to submit Canvass",,Passed,,
PRS-1481-006,Allow Decimal to Non-OFM Quanity,Critical,Verify Non-OFM Quantity is Displayed as Decimals in All Documents,"1. User should be logged in as:
     - IT Admin
     - Purchasing Admin
     - Engineers
     - Purchasing Staff
     - Purchasing Head
2. RS should have Non-OFM Type of Request
3. RS should have canvass, PO, DR, and Invoice Report","1. Navigate to Dashboard
2. Select RS Number
3. Validate Qty in Item Table
4. Navigate to Related Documents > Canvasses
5. Validate Qty in Item Table
6. Repeat steps 4-5 for Orders, Deliveries, and Invoice Reports",,"3. Should implement the 3-Decimal places of the Quantity across all Documents and viewing of the Item
- RS
- Canvass
- PO 
- PR
- DR",,Failed,CITYLANDPRS-1706,