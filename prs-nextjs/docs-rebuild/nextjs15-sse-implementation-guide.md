# Next.js 15 Full Stack Implementation Guide with SSE

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Core Technologies](#core-technologies)
4. [Project Structure](#project-structure)
5. [Database Setup](#database-setup)
6. [Authentication Implementation](#authentication-implementation)
7. [Server-Sent Events (SSE) Implementation](#server-sent-events-sse-implementation)
8. [Core Business Workflows](#core-business-workflows)
9. [Deployment Strategy](#deployment-strategy)
10. [Knowledge Transfer Guidelines](#knowledge-transfer-guidelines)

## Introduction

This guide outlines the implementation approach for rebuilding the PRS (Purchase Requisition System) using Next.js 15 full stack with Server-Sent Events (SSE) for real-time functionality. The goal is to create a simplified architecture that eliminates the complexity of maintaining separate frontend and backend codebases while preserving all existing functionality.

### Key Objectives

- Simplify the architecture by unifying frontend and backend
- Implement real-time updates using Server-Sent Events
- Create a system that is easy to maintain and transfer knowledge
- Preserve all existing business workflows and rules
- Improve performance and user experience

## Architecture Overview

The new architecture leverages Next.js 15's full stack capabilities to create a unified codebase:

```
┌─────────────────────────────────────────────────────┐
│                   Next.js 15 App                    │
├─────────────────┬───────────────┬──────────────────┤
│  UI Components  │  Server-Side  │   Client-Side    │
│  (React)        │  (Node.js)    │   (Browser)      │
├─────────────────┼───────────────┼──────────────────┤
│                 │               │                  │
│ Server          │ Server        │ Client           │
│ Components      │ Actions       │ Components       │
│                 │               │                  │
├─────────────────┴───────────────┴──────────────────┤
│                  Database Layer                     │
│                  (Prisma ORM)                       │
├─────────────────────────────────────────────────────┤
│                  PostgreSQL                         │
└─────────────────────────────────────────────────────┘
```

### Key Architecture Benefits

1. **Unified Codebase**: Single repository for all code
2. **Simplified Data Flow**: Direct database access from server components
3. **Real-Time Updates**: SSE for live notifications and updates
4. **Type Safety**: End-to-end TypeScript for better reliability
5. **Improved Performance**: Server Components for data-heavy pages

## Core Technologies

The implementation will use the following core technologies:

1. **Next.js 15**: Full stack React framework
2. **TypeScript**: Type-safe programming language
3. **Prisma**: Type-safe ORM for database access
4. **PostgreSQL**: Relational database
5. **NextAuth.js**: Authentication solution
6. **Server-Sent Events**: For real-time updates
7. **Tailwind CSS**: For styling
8. **Zod**: For validation

## Project Structure

The project will follow a feature-based structure:

```
prs-nextjs/
├── app/                    # Next.js App Router
│   ├── api/                # API routes (including SSE endpoints)
│   ├── auth/               # Authentication pages
│   ├── dashboard/          # Dashboard pages
│   ├── requisitions/       # Requisition management
│   ├── purchase-orders/    # PO management
│   ├── delivery-receipts/  # Delivery management
│   ├── payments/           # Payment management
│   └── admin/              # Admin pages
├── components/             # Shared UI components
├── lib/                    # Shared utilities
│   ├── db/                 # Database access
│   ├── auth/               # Authentication utilities
│   ├── sse/                # SSE utilities
│   └── utils/              # Helper functions
├── models/                 # Data models and validation
├── prisma/                 # Prisma schema and migrations
└── public/                 # Static assets
```

## Database Setup

The database will be managed using Prisma ORM:

### Prisma Schema Example

```prisma
// Key models for the PRS system

model User {
  id            Int      @id @default(autoincrement())
  username      String   @unique
  email         String?  @unique
  password      String
  name          String?
  roleId        Int
  departmentId  Int?
  status        String   @default("active")
  otpSecret     String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  role          Role     @relation(fields: [roleId], references: [id])
  department    Department? @relation(fields: [departmentId], references: [id])

  requisitions  Requisition[] @relation("CreatedBy")
  approvals     Approval[]
  notifications Notification[]
}

model Role {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users       User[]
  permissions RolePermission[]
}

model Requisition {
  id            Int      @id @default(autoincrement())
  rsNumber      String?  @unique
  draftRsNumber String?  @unique
  type          String   // ofm, non-ofm, etc.
  status        String   // draft, submitted, approved, etc.
  departmentId  Int
  projectId     Int?
  companyId     Int
  createdById   Int
  dateRequired  DateTime?
  deliverTo     String?
  purpose       String?
  category      String   // company, association, project
  chargeTo      String?
  chargeToId    Int?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  department    Department @relation(fields: [departmentId], references: [id])
  project       Project?   @relation(fields: [projectId], references: [id])
  company       Company    @relation(fields: [companyId], references: [id])
  createdBy     User       @relation("CreatedBy", fields: [createdById], references: [id])

  items         RequisitionItem[]
  approvals     Approval[]
  purchaseOrders PurchaseOrder[]
}

// Additional models would be defined here
```

## Authentication Implementation

Authentication will be implemented using NextAuth.js with JWT and OTP support:

### Auth Configuration

```typescript
// lib/auth/config.ts
import { NextAuthOptions } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/db"
import { verifyPassword } from "@/lib/auth/password"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
        otp: { label: "OTP Code", type: "text" }
      },
      async authorize(credentials) {
        // Verify credentials against database
        const user = await prisma.user.findUnique({
          where: { username: credentials.username },
          include: { role: true }
        })

        if (!user) return null

        // Verify password
        const isValidPassword = await verifyPassword(
          credentials.password,
          user.password
        )

        if (!isValidPassword) return null

        // Verify OTP if required
        if (user.otpSecret && !credentials.otp) {
          // Return partial auth to trigger OTP verification
          return {
            id: user.id.toString(),
            requireOTP: true
          }
        }

        if (user.otpSecret && credentials.otp) {
          const isValidOTP = verifyOTP(
            credentials.otp,
            user.otpSecret
          )

          if (!isValidOTP) return null
        }

        return {
          id: user.id.toString(),
          name: user.name,
          email: user.email,
          role: user.role.name,
          departmentId: user.departmentId
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.role = user.role
        token.departmentId = user.departmentId
      }
      return token
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id
        session.user.role = token.role
        session.user.departmentId = token.departmentId
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  }
}
```

## Server-Sent Events (SSE) Implementation

SSE will be used for real-time updates throughout the application:

### SSE API Route

```typescript
// app/api/sse/notifications/route.ts
import { NextRequest } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth/config"
import { prisma } from "@/lib/db"

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions)
  if (!session) {
    return new Response("Unauthorized", { status: 401 })
  }

  const userId = parseInt(session.user.id)

  // Set up SSE response headers
  const response = new Response(
    new ReadableStream({
      start(controller) {
        // Function to send notifications
        const sendNotifications = async () => {
          try {
            // Get new notifications for user
            const notifications = await prisma.notification.findMany({
              where: {
                userId,
                read: false
              },
              orderBy: { createdAt: "desc" },
              take: 10
            })

            // Send as SSE event
            controller.enqueue(`data: ${JSON.stringify(notifications)}\n\n`)

            // Schedule next check
            setTimeout(sendNotifications, 5000)
          } catch (error) {
            console.error("SSE error:", error)
            controller.error(error)
          }
        }

        // Start sending notifications
        sendNotifications()
      }
    }),
    {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive"
      }
    }
  )

  return response
}
```

### Client-Side SSE Component

```tsx
// components/SSEListener.tsx
"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"

export function NotificationListener({ onNotification }) {
  const { data: session } = useSession()
  const [eventSource, setEventSource] = useState(null)

  useEffect(() => {
    if (!session) return

    // Create EventSource connection
    const sse = new EventSource("/api/sse/notifications")

    // Handle incoming events
    sse.onmessage = (event) => {
      const notifications = JSON.parse(event.data)
      onNotification(notifications)
    }

    // Handle connection error
    sse.onerror = (error) => {
      console.error("SSE error:", error)
      sse.close()

      // Attempt to reconnect after a delay
      setTimeout(() => {
        setEventSource(new EventSource("/api/sse/notifications"))
      }, 5000)
    }

    setEventSource(sse)

    // Clean up on unmount
    return () => {
      sse.close()
    }
  }, [session, onNotification])

  return null
}
```
