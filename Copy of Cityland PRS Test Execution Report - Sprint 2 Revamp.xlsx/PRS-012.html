<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f3f3f3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="837048502C0" style="width:103px;" class="column-headers-background">A</th><th id="837048502C1" style="width:98px;" class="column-headers-background">B</th><th id="837048502C2" style="width:252px;" class="column-headers-background">C</th><th id="837048502C3" style="width:252px;" class="column-headers-background">D</th><th id="837048502C4" style="width:233px;" class="column-headers-background">E</th><th id="837048502C5" style="width:330px;" class="column-headers-background">F</th><th id="837048502C6" style="width:184px;" class="column-headers-background">G</th><th id="837048502C7" style="width:144px;" class="column-headers-background">H</th><th id="837048502C8" style="width:100px;" class="column-headers-background">I</th><th id="837048502C9" style="width:88px;" class="column-headers-background">J</th><th id="837048502C10" style="width:116px;" class="column-headers-background">K</th><th id="837048502C11" style="width:116px;" class="column-headers-background">L</th><th id="837048502C12" style="width:116px;" class="column-headers-background">M</th><th id="837048502C13" style="width:116px;" class="column-headers-background">N</th><th id="837048502C14" style="width:78px;" class="column-headers-background">O</th><th id="837048502C15" style="width:72px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="837048502R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="837048502R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-012 - [RS Viewing] - Search, Filter, View Attachment, View Notes, Related Documents, Edit Requsition Slip</td></tr><tr style="height: 19px"><th id="837048502R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-012-001</td><td class="s5">RS VIEWING</td><td class="s5">Critical</td><td class="s5">Validate Requistion Slip - RS Main page</td><td class="s6"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. Login &gt; Click Dashboard &gt; Click 1 RS Ref Number with submitted status<br>2. Should display<br>    a. RS Number<br>    b. Request History Button<br>    c. Select Action Button<br>    d. Two Tabs for<br>        i. RS Main<br>        ii. Related Documents<br>    e. Print Buttons<br>3. On RS Main Tab<br>    a. Should display the Requisition Slip Details<br>        i. Type of Request<br>        ii. Date Required </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;"> (Sample Format: 14 February 2025)</span><span style="font-family:Poppins,Arial;color:#000000;"><br>        iii. Company<br>        iv. Project<br>        v. Department<br>        vi. Purpose<br>        vii. Deliver To<br>        viii. Charge To <br>4. Status Section<br>    a. Current Status<br>    b. List of Approvers and Approver Status<br>5. Should display a section for Attachment and Notes<br>6. Should display a Table for Items that is declared in the Request<br>    a. Should have Columns ofr<br>        i. Item<br>        ii. Account Code<br>        iii. Unit<br>        iv. Remaining GFQ<br>        v. Quantity<br>        vi. Notes</span></td><td class="s6"></td><td class="s6"></td><td class="s7" rowspan="6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1ug3uLcS5U98nqLNkK3K42dEfDT0LlmF6KJFpflfxnno/edit?gid=**********#gid=**********&amp;range=A1">https://docs.google.com/spreadsheets/d/1ug3uLcS5U98nqLNkK3K42dEfDT0LlmF6KJFpflfxnno/edit?gid=**********#gid=**********&amp;range=A1</a></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s10" rowspan="22"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=**********#gid=**********">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=**********#gid=**********</a></td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 211px"><th id="837048502R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s13">PRS-012-002</td><td class="s13">RS VIEWING</td><td class="s13">Minor</td><td class="s13">Items Search and Clear buttons on RS Main tab should work</td><td class="s14"></td><td class="s13">1. Login &gt; Dashboard<br>2. Click a submitted status Ref Number<br>3. Find items search input<br>4. Input account code<br>5. Click Search<br>6. Validate match results on the table<br>7. Click clear button<br>8. Validate search input is cleared<br>9. Search for Item<br>10. Validate match results on the table</td><td class="s14"></td><td class="s13">User should be able to search and clear the input search field</td><td class="s15">Failed</td><td class="s16">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s13">Search and Clear Button Doesn&#39;t Work<br><br>3/5: Search still doesn&#39;t work</td><td class="s17"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-811">https://youtrack.stratpoint.com/issue/CITYLANDPRS-811</a></td></tr><tr style="height: 211px"><th id="837048502R4" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">5</div></th><td class="s5">PRS-012-003</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Validate view attachment on requistion slip works</td><td class="s6"></td><td class="s5">1. Dashboard &gt; New Request<br>2. Populate all required fields<br>3. Add 6 attachments with file format:  PNG, JPG, JPEG, PDF, Excel, CSV<br>4. Click Save Draft<br>5. Click Submit<br>6. Open submitted RS Ref Number<br>7. Click Check attachments<br>8. Validate modal with &quot;New Attachment/s&quot; message<br>9. Validate that files were displayed as links<br>10. Click each files with PNG, JPG, JPEG, PDF, Excel, CSV format<br>11. Validate that the file will open in new tab or file will automatically downloaded<br>12. User should be able to view the file content<br>13. Validate close button works to exit attachment modal</td><td class="s6"></td><td class="s5">File will be opened in new tab or automatically downloaded. User should be able to view the file content</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 211px"><th id="837048502R5" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">6</div></th><td class="s5">PRS-012-004</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate attachment displayed per upload date</td><td class="s6"></td><td class="s5">Requirement:     a. Display a Modal with all of the Uploaded Documents per Uploaded Date<br><br>1. Open a submitted RS with attachment or create one<br>2. Click Check attachments<br>3. Validate attachment is displayed per upload date</td><td class="s6"></td><td class="s5">Should upload document per Uploaded Date</td><td class="s8">Passed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">3/35 tagged as deprecated diue to enhancements</td><td class="s5"></td></tr><tr style="height: 211px"><th id="837048502R6" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">7</div></th><td class="s5">PRS-012-005</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate searching of filename works</td><td class="s6"></td><td class="s5">Requirement: Allow Searching of File Name<br><br>1. Open a submitted RS with attachment or create one<br>2. Click Check attachments<br>3. Validate search is present and allows user to search for filename for partial text and full text</td><td class="s6"></td><td class="s5">Should allow user to search for filename</td><td class="s18">Blocked</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">For upcoming enhancements</td><td class="s5"></td></tr><tr style="height: 211px"><th id="837048502R7" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">8</div></th><td class="s5">PRS-012-006</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Validate Filter by today&#39;s date works on Attachments</td><td class="s6"></td><td class="s5">1. Open a submitted RS with attachment or create one<br>2. Click Check attachments<br>3. Filter today&#39;s date (if attachment was added today)<br>4. Match files should return</td><td class="s6"></td><td class="s5">Filtering files for today&#39;s date should successfully returns matched files</td><td class="s8">Passed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1032">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1032<br><br>3/5: tagged as deprecated diue to enhancements</a></td><td class="s5"></td></tr><tr style="height: 229px"><th id="837048502R8" style="height: 229px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 229px">9</div></th><td class="s5">PRS-012-007</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate Filter by past date works on Attachments</td><td class="s6"></td><td class="s5">*Only applicable when user is able to edit the RS and add new attachment. There is no requirement to add attachment but there is a requirement to edit RS and it was missed on implementation bug - 723. If edit was allowing user to add additional attachment then this scenario is testable else tag it as not implemented&quot;<br><br>1. Open RS created from the past with attachment<br>2. Filter the attachment with past date (file upload date of the file from the past)<br>3. Filter should return match files</td><td class="s6"></td><td class="s5">Filter attachment by past date works</td><td class="s5" rowspan="6"></td><td class="s8">Passed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">3/5: tagged as deprecated diue to enhancements</td><td class="s5"></td></tr><tr style="height: 106px"><th id="837048502R9" style="height: 106px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 106px">10</div></th><td class="s5">PRS-012-008</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate Filter by future date should not work on Attachments</td><td class="s6"></td><td class="s5">1. Open RS created with existing attachment or create one. Click check attachments<br>2. Filter by future date<br>3. User should not be able to select future date</td><td class="s6"></td><td class="s5">User should not be able to select future date</td><td class="s15">Failed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">Able to Select Future Date<br><br>3/5: tagged as deprecated diue to enhancements</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-816">https://youtrack.stratpoint.com/issue/CITYLANDPRS-816</a></td></tr><tr style="height: 138px"><th id="837048502R10" style="height: 138px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 138px">11</div></th><td class="s5">PRS-012-009</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Validate Filter by date range works on Attachments</td><td class="s6"></td><td class="s5">1. Open old/existing RS with existing attachment or you can create one. Click check attachments<br>2. Filter an attachment for 2 weeks should return match results<br>3. Filter an attachment for 3 days should return match results<br>4. Filter an attachment for a month should return match results</td><td class="s5">Example data only<br><br>Attachment date is 1/20/2025<br><br>Filter by month Filter 12/20/2024 - 12/20/2025<br>Filter by week 1/14/2025 - 1/20/2025<br>Filter for 3 days 1/18/2025 - 1/20/2025</td><td class="s5">Filter by Month, days and weeks should return match results</td><td class="s8">Passed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">3/5: tagged as deprecated diue to enhancements</td><td class="s5"></td></tr><tr style="height: 108px"><th id="837048502R11" style="height: 108px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 108px">12</div></th><td class="s5">PRS-012-010</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Validate &quot;No Data Available&quot; message when there is no data available as per Filter date on Attachment</td><td class="s6"></td><td class="s5">1.Open an existing RS with existing attachment or create submitted RS with attachment<br>2. Click check attachments <br>3. Filter attachment against the file upload date of existing attachment<br>4. Validate &quot;No Data Available&quot; message returns</td><td class="s6"></td><td class="s5">When no match found, validate message returns &quot;No Data Available&quot;</td><td class="s8">Passed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">3/5: tagged as deprecated diue to enhancements</td><td class="s5"></td></tr><tr style="height: 108px"><th id="837048502R12" style="height: 108px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 108px">13</div></th><td class="s5">PRS-012-011</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate clear filter works on Attachment</td><td class="s6"></td><td class="s5">1.Open an existing RS with existing attachment or create submitted RS with attachment<br>2. Click check attachments<br>3. Filter any date<br>4. Click clear button<br>5. Clear button should work<br></td><td class="s6"></td><td class="s5">Should be able to clear filter</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 107px"><th id="837048502R13" style="height: 107px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 107px">14</div></th><td class="s5">PRS-012-012</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate close modal and close window works</td><td class="s6"></td><td class="s5">1.Open an existing RS with existing attachment or create submitted RS with attachment<br>2. Click check attachments<br>3. Click &#39;x&#39; to close the attachment modal<br>4. Click &#39;Close window&#39; to close the attachment  modal</td><td class="s6"></td><td class="s5">Should be able to close the modal</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 142px"><th id="837048502R14" style="height: 142px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 142px">15</div></th><td class="s5">PRS-012-013</td><td class="s5">RS VIEWING</td><td class="s5">Critical</td><td class="s5">Validate user was able to view notes added</td><td class="s6"></td><td class="s5">1. Submit a RS with notes populated. Click Save Draft &gt; Submit<br>2. Open submitted RS,  click RS Main Tab<br>3. Click Check notes<br>5. Validate that name of the user who added the note will be displayed<br>6. Click the name<br>7. Validate the note added by the user appears</td><td class="s6"></td><td class="s5">Note added by the user should appear</td><td class="s5" rowspan="4"></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 91px"><th id="837048502R15" style="height: 91px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 91px">16</div></th><td class="s5">PRS-012-014</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Validate Filter notes by today&#39;s date works</td><td class="s6"></td><td class="s5">1. Open a submitted RS with notes or create one<br>2. Click Check notes<br>3. Filter today&#39;s date (if notes was added today)<br>4. Match files should return</td><td class="s6"></td><td class="s5">Filtering notes for today&#39;s date should successfully returns matched notes</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027">3/5: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027</a></td></tr><tr style="height: 202px"><th id="837048502R16" style="height: 202px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 202px">17</div></th><td class="s5">PRS-012-015</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate Filter notes by past date works</td><td class="s6"></td><td class="s5">*Only applicable when user is able to edit the RS and add new notes. There is no requirement to add new notes but there is a requirement to edit RS and it was missed on implementation bug - 723. If edit was allowing user to add additional notes then this scenario is testable else tag it as not implemented&quot;<br><br>1. Open RS created from the past with notes<br>2. Filter the notes with past date (notes added from the past)<br>3. Filter should return match files</td><td class="s6"></td><td class="s5">Filter notes by past date works</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 75px"><th id="837048502R17" style="height: 75px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 75px">18</div></th><td class="s5">PRS-012-016</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Validate Filter notes by future date should not work</td><td class="s6"></td><td class="s5">1. Open RS created with existing notes or create one. Click check notes<br>2. Filter by future date<br>3. User should not be able to select future date</td><td class="s6"></td><td class="s5">User should not be able to select future date</td><td class="s15">Failed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">Able to filter Future Date</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-816">https://youtrack.stratpoint.com/issue/CITYLANDPRS-816</a></td></tr><tr style="height: 172px"><th id="837048502R18" style="height: 172px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 172px">19</div></th><td class="s5">PRS-012-017</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Validate Filter notes by date range works</td><td class="s6"></td><td class="s5">1. Open old/existing RS with existing notes or you can create one. Click check notes <br>2. Filter notes for 2 weeks should return match results<br>3. Filter notes for 3 days should return match results<br>4. Filter notes for a month should return match results</td><td class="s5">Example data only<br><br>notes date is 1/20/2025<br><br>Filter by month Filter 12/20/2024 - 12/20/2025<br>Filter by week 1/14/2025 - 1/20/2025<br>Filter for 3 days 1/18/2025 - 1/20/2025</td><td class="s5">Filter by Month, days and weeks should return match results</td><td class="s5" rowspan="4"></td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 167px"><th id="837048502R19" style="height: 167px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 167px">20</div></th><td class="s5">PRS-012-018</td><td class="s5">RS VIEWING</td><td class="s5">Critical</td><td class="s5">Validate print Requisition Slip works</td><td class="s6"></td><td class="s5">1. Click an existing RS with submitted status<br>2. Click Print button on top right<br>3. Validate user should be able to print Requisition Slip details<br>4. Validate file to include printing of<br>    a. Requisition Slip Details<br>    b. RS Approvers<br>    c. Requested Items<br>    d. All of the Related Documents to the Requisition Slip</td><td class="s6"></td><td class="s5">User should be able to print Requisition Slip details</td><td class="s15">Failed</td><td class="s16">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">Print button to be removed according to Ms Irene<br><br>3/5: Print button still displayed</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-821">https://youtrack.stratpoint.com/issue/CITYLANDPRS-821</a></td></tr><tr style="height: 227px"><th id="837048502R20" style="height: 227px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 227px">21</div></th><td class="s5">PRS-012-019</td><td class="s5">RS VIEWING</td><td class="s5">Critical</td><td class="s5">Validate Requisition Slip - Related Documents page</td><td class="s6"></td><td class="s5">1. Click an existing RS with submitted status<br>2. Click Related Documents Tab<br>3. Should display Tabs for<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br></td><td class="s6"></td><td class="s5">All fields, table columns and link should appear and work as expected</td><td class="s8">Passed</td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">Dummy Data for now on the columns</td><td class="s5"></td></tr><tr style="height: 19px"><th id="837048502R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">PRS-012-020</td><td class="s5">RS VIEWING</td><td class="s5">Minor</td><td class="s5">Go back button from Requisition Slip - Related Documents page should work</td><td class="s6"></td><td class="s5">1. Click an existing RS with submitted status<br>2. Click Related Documents Tab<br>3. Click Go back button</td><td class="s6"></td><td class="s5">Go back button should work and redirect to dashboard page</td><td class="s15">Failed</td><td class="s16">Failed</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">Go Back button doesn&#39;t work when user is in the orders and deliveries tab.<br><br>3/5: Incorrect redirection when click back button</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-822">https://youtrack.stratpoint.com/issue/CITYLANDPRS-822</a></td></tr><tr style="height: 392px"><th id="837048502R22" style="height: 392px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 392px">23</div></th><td class="s5">PRS-012-021</td><td class="s5">RS VIEWING</td><td class="s5">High</td><td class="s5">Edit draft requisition slip should work</td><td class="s6"></td><td class="s5">4. Should not allow updating of RS Details except<br>    a. Deliver To<br>    b. Charge To<br>5. Should allow updating of the Requested Items<br>    a. User can Add an Item<br>    b. User can Remove an Item<br>    c. User can update the Quantity of the Requested Items<br>6. Should have Cancel Button, Save as Draft Button, and Submit Button<br>    a. If Cancel Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should cancel any changes made to the Requisition Slip<br>    b. If Save as Draft Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should save the Requisition Slip as Draft with the same Temporary RS Number<br>    c. If Submit Button is clicked, should display a Confirmation Modal<br>        i. Once Confirmed, should submit the Requisition Slip for Approval and have a dedicated RS Number</td><td class="s6"></td><td class="s5">Should not allow user to edit <br>    a. Deliver To<br>    b. Charge To<br><br>Should allow edit on <br>    a. Add an Item<br>    b. Remove an Item<br>    c. Update the Quantity of the Requested Items</td><td class="s5"></td><td class="s15">Failed</td><td class="s9"> Deprecated</td><td class="s11">Not Started</td><td class="s12"></td><td class="s5">Any one can edit draft RS<br><br>3/5: Tagged as deprecated as Edit draft is covered in m2 TCs</td><td class="s7"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-824">https://youtrack.stratpoint.com/issue/CITYLANDPRS-824</a></td></tr><tr style="height: 19px"><th id="837048502R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">PRS-012-022</td><td class="s19"></td><td class="s19">High</td><td class="s20">Validate user should be able to delete attachment when viewing RS</td><td class="s19"></td><td class="s20">1. View RS submitted<br>2. Click check attachment<br>3. Delete attachment<br>4. Validate successful deletion of attachment<br>5. View same RS and check attachment again and verify if deleted</td><td class="s19"></td><td class="s20">4. Validate successful deletion of attachment<br>5. View same RS and check attachment again and verify if deleted</td><td class="s19"></td><td class="s21"></td><td class="s9">Passed</td><td class="s11">Not Started</td><td class="s19"></td><td class="s19"></td><td class="s19"></td></tr></tbody></table></div>