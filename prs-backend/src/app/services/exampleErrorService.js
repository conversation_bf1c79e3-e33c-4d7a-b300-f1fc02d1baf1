const { ErrorFactory, ErrorTranslator } = require('../errors');
const BaseService = require('./baseService');

/**
 * Example service demonstrating the new error handling system
 */
class ExampleErrorService extends BaseService {
  /**
   * Creates a new ExampleErrorService
   * 
   * @param {Object} container - Dependency injection container
   */
  constructor(container) {
    super(container);
    
    const { exampleRepository } = container;
    
    this.exampleRepository = exampleRepository;
  }

  /**
   * Gets an example by ID, demonstrating NotFoundError
   * 
   * @param {string} id - Example ID
   * @returns {Promise<Object>} - Found example
   */
  async getExampleById(id) {
    const example = await this.exampleRepository.getById(id);
    
    if (!example) {
      throw ErrorFactory.notFound('Example', id);
    }
    
    return example;
  }

  /**
   * Creates an example, demonstrating ValidationError and ConflictError
   * 
   * @param {Object} data - Example data
   * @returns {Promise<Object>} - Created example
   */
  async createExample(data) {
    // Validate required fields
    if (!data.name) {
      throw ErrorFactory.validation('Validation failed', {
        name: 'Name is required'
      });
    }
    
    // Check for duplicate
    const existing = await this.exampleRepository.findOne({
      where: { name: data.name }
    });
    
    if (existing) {
      throw ErrorFactory.conflict(
        'Example with this name already exists',
        'Example',
        'name',
        data.name
      );
    }
    
    try {
      return await this.exampleRepository.create(data);
    } catch (error) {
      // Translate and rethrow any database errors
      throw ErrorTranslator.translate(error, {
        operation: 'create',
        resource: 'Example'
      });
    }
  }

  /**
   * Updates an example, demonstrating error translation
   * 
   * @param {string} id - Example ID
   * @param {Object} data - Example data
   * @returns {Promise<Object>} - Updated example
   */
  async updateExample(id, data) {
    try {
      // Check if example exists
      const example = await this.getExampleById(id);
      
      // Update example
      await this.exampleRepository.update({ id }, data);
      
      // Return updated example
      return await this.exampleRepository.getById(id);
    } catch (error) {
      // If it's already an AppError, rethrow it
      if (error.name && error.name.endsWith('Error') && error.getHttpStatus) {
        throw error;
      }
      
      // Otherwise, translate and rethrow
      throw ErrorTranslator.translate(error, {
        operation: 'update',
        resource: 'Example'
      });
    }
  }

  /**
   * Deletes an example, demonstrating error handling in transactions
   * 
   * @param {string} id - Example ID
   * @returns {Promise<boolean>} - True if deleted
   */
  async deleteExample(id) {
    return await this.withTransaction(async (transaction) => {
      try {
        // Check if example exists
        const example = await this.exampleRepository.getById(id, {
          transaction
        });
        
        if (!example) {
          throw ErrorFactory.notFound('Example', id);
        }
        
        // Delete example
        await this.exampleRepository.destroy({ id }, { transaction });
        
        return true;
      } catch (error) {
        // Translate and rethrow
        throw ErrorTranslator.translate(error, {
          operation: 'delete',
          resource: 'Example'
        });
      }
    });
  }

  /**
   * Demonstrates authentication error
   * 
   * @param {Object} credentials - User credentials
   * @returns {Promise<Object>} - User object
   */
  async login(credentials) {
    const { username, password } = credentials;
    
    if (!username || !password) {
      throw ErrorFactory.badRequest('Username and password are required');
    }
    
    // Simulate authentication failure
    throw ErrorFactory.authentication(
      'Invalid username or password',
      'INVALID_CREDENTIALS'
    );
  }

  /**
   * Demonstrates authorization error
   * 
   * @param {Object} user - User object
   * @param {string} action - Action to perform
   * @returns {Promise<boolean>} - True if authorized
   */
  async checkPermission(user, action) {
    // Simulate authorization failure
    throw ErrorFactory.authorization(
      `You do not have permission to ${action}`,
      action
    );
  }
}

module.exports = ExampleErrorService;
