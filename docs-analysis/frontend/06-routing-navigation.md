# Routing and Navigation

## Overview

The PRS-Frontend uses React Router for client-side routing and implements a custom navigation system based on user permissions. This document provides a detailed analysis of the routing and navigation architecture, including route configuration, navigation components, and permission-based routing.

## Routing Architecture

The routing system is built around several key components:

1. **Router Configuration**: Defined in `src/app/router.jsx`
2. **Route Components**: Located in `src/app/routes/`
3. **Protected Routes**: Implemented using the `ProtectedRoute` component
4. **Navigation Configuration**: Defined in `src/config/navigation.js`
5. **Navigation Components**: Located in `src/components/layouts/`

## Router Configuration

The router is configured in `src/app/router.jsx` using React Router's `createBrowserRouter`:

```jsx
// From src/app/router.jsx
export const createAppRouter = (queryClient, availableRoutes) =>
  createBrowserRouter([
    {
      path: '/',
      lazy: async () => {
        const { LandingRoute } = await import('./routes/Landing');
        return { Component: LandingRoute };
      },
    },
    {
      path: '/login',
      lazy: async () => {
        const { LoginRoute } = await import('./routes/Login');
        return { Component: LoginRoute };
      },
    },
    {
      path: '/auth/verify-otp',
      lazy: async () => {
        const { VerifyOTPRoute } = await import('./routes/VerifyOTP');
        return { Component: VerifyOTPRoute };
      },
    },
    {
      path: '/auth/setup-otp',
      lazy: async () => {
        const { SetupOTPRoute } = await import('./routes/SetupOTP');
        return { Component: SetupOTPRoute };
      },
    },
    {
      path: '/auth/create-password',
      lazy: async () => {
        const { CreatePasswordRoute } = await import('./routes/CreatePassword');
        return { Component: CreatePasswordRoute };
      },
    },
    {
      path: '/app',
      element: (
        <ProtectedRoute>
          <AppRoot />
        </ProtectedRoute>
      ),
      children: [...availableRoutes],
    },
    {
      path: '*',
      element: <NotFound />,
    },
  ]);
```

This configuration:
1. Defines the main routes of the application
2. Uses lazy loading for route components
3. Protects the `/app` route with the `ProtectedRoute` component
4. Includes a catch-all route for 404 pages

## Route Components

Route components are located in `src/app/routes/` and define the content of each route:

```jsx
// Example route component from src/app/routes/Dashboard.jsx
export const DashboardRoute = () => {
  return (
    <div>
      <Helmet>
        <title>Dashboard | PRS</title>
      </Helmet>
      <Dashboard />
    </div>
  );
};
```

These components:
1. Set the page title using React Helmet
2. Render the main content of the route
3. May include additional logic for data fetching or state management

## Protected Routes

Protected routes are implemented using the `ProtectedRoute` component in `src/lib/auth.jsx`:

```jsx
// From src/lib/auth.jsx
export const ProtectedRoute = ({ children }) => {
  const { token, type } = useTokenStore();

  if (!token && type !== 'auth') {
    return <Navigate to="/login" replace />;
  }

  return children;
};
```

This component:
1. Checks if the user is authenticated
2. Redirects to the login page if not
3. Renders the protected content if authenticated

## Navigation Configuration

The navigation configuration is defined in `src/config/navigation.js`:

```javascript
// From src/config/navigation.js
const navigationConfig = {
  mainNavigation: [
    dashboardView && {
      label: 'Dashboard',
      hasDropdown: false,
      path: '/app/dashboard',
    },
    dashboardView && {
      label: 'Non-RS',
      hasDropdown: false,
      path: '/app/non-requisition-slip/dashboard',
    },
    (ofmItemsView || nonOfmItemsView || ofmListsView) && {
      label: 'Items',
      hasDropdown: true,
      dropdownItems: [
        ofmItemsView && { label: 'OFM', path: '/app/items/ofm' },
        ofmListsView && { label: 'OFM List', path: '/app/items/ofm-list' },
        nonOfmItemsView && { label: 'Non-OFM', path: '/app/items/non-ofm' },
      ].filter(Boolean),
    },
    // ... other navigation items
  ].filter(Boolean),
};
```

This configuration:
1. Defines the main navigation items
2. Conditionally includes items based on user permissions
3. Supports nested dropdown menus
4. Filters out null or undefined items

## Navigation Components

### Sidebar

The sidebar is implemented in `src/components/layouts/Sidebar/Sidebar.jsx`:

```jsx
// From src/components/layouts/Sidebar/Sidebar.jsx
export const Sidebar = () => {
  const { mainNavigation } = navigationConfig;
  const { permissions } = usePermissionStore();
  const location = useLocation();

  return (
    <aside className="fixed top-0 left-0 z-40 w-64 h-screen pt-20 transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0">
      <div className="h-full px-3 pb-4 overflow-y-auto bg-white">
        <ul className="space-y-2 font-medium">
          {mainNavigation.map((item, index) => {
            if (item.hasDropdown) {
              return (
                <SidebarDropdown
                  key={index}
                  label={item.label}
                  items={item.dropdownItems}
                  location={location}
                />
              );
            }

            return (
              <SidebarItem
                key={index}
                label={item.label}
                path={item.path}
                location={location}
              />
            );
          })}
        </ul>
      </div>
    </aside>
  );
};
```

This component:
1. Renders the main navigation items
2. Supports both regular items and dropdown menus
3. Highlights the active item based on the current location

### Header

The header is implemented in `src/components/layouts/Header/Header.jsx`:

```jsx
// From src/components/layouts/Header/Header.jsx
export const Header = () => {
  const { user } = useUserStore();
  const logout = useLogout();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <nav className="fixed top-0 z-50 w-full bg-white border-b border-gray-200">
      <div className="px-3 py-3 lg:px-5 lg:pl-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center justify-start">
            <button
              data-drawer-target="logo-sidebar"
              data-drawer-toggle="logo-sidebar"
              aria-controls="logo-sidebar"
              type="button"
              className="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg sm:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
            >
              <span className="sr-only">Open sidebar</span>
              <svg
                className="w-6 h-6"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clipRule="evenodd"
                  fillRule="evenodd"
                  d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z"
                ></path>
              </svg>
            </button>
            <Link to="/" className="flex ml-2 md:mr-24">
              <img src="/logo.png" className="h-8 mr-3" alt="Logo" />
              <span className="self-center text-xl font-semibold sm:text-2xl whitespace-nowrap">
                PRS
              </span>
            </Link>
          </div>
          <div className="flex items-center">
            <div className="flex items-center ml-3">
              <div>
                <button
                  type="button"
                  className="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300"
                  aria-expanded="false"
                  onClick={toggleDropdown}
                >
                  <span className="sr-only">Open user menu</span>
                  <img
                    className="w-8 h-8 rounded-full"
                    src="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
                    alt="user photo"
                  />
                </button>
              </div>
              {isDropdownOpen && (
                <div className="absolute top-10 right-0 z-50 my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow">
                  <div className="px-4 py-3">
                    <span className="block text-sm text-gray-900">
                      {user?.name}
                    </span>
                    <span className="block text-sm text-gray-500 truncate">
                      {user?.email}
                    </span>
                  </div>
                  <ul className="py-1">
                    <li>
                      <button
                        onClick={() => logout.mutate()}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Sign out
                      </button>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};
```

This component:
1. Renders the application logo and title
2. Displays the user's profile picture and name
3. Provides a dropdown menu with a logout option

### App Layout

The app layout is implemented in `src/components/layouts/AppLayout/AppLayout.jsx`:

```jsx
// From src/components/layouts/AppLayout/AppLayout.jsx
export const AppLayout = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <Sidebar />
      <div className="p-4 sm:ml-64 pt-20">{children}</div>
    </div>
  );
};
```

This component:
1. Renders the header and sidebar
2. Provides a container for the main content
3. Applies appropriate spacing and positioning

## Permission-Based Routing

The application implements permission-based routing using a combination of the navigation configuration and the `hasPermission` function:

```javascript
// From src/utils/permissions.js
export const hasPermission = (route, permission) => {
  if (!permission) {
    return null;
  }

  return route;
};

// From src/config/navigation.js
const navigationConfig = {
  mainNavigation: [
    dashboardView && {
      label: 'Dashboard',
      hasDropdown: false,
      path: '/app/dashboard',
    },
    // ... other navigation items
  ].filter(Boolean),
};
```

This approach:
1. Conditionally includes navigation items based on user permissions
2. Filters out items for which the user doesn't have permission
3. Provides a clean way to define permission-based navigation

## Available Routes

The available routes are defined in `src/app/index.jsx`:

```jsx
// From src/app/index.jsx
const availableRoutes = [
  {
    path: 'dashboard',
    lazy: async () => {
      const { DashboardRoute } = await import('./routes/Dashboard');
      return { Component: DashboardRoute };
    },
  },
  {
    path: 'items/ofm',
    lazy: async () => {
      const { OFMItemsRoute } = await import('./routes/OFMItems');
      return { Component: OFMItemsRoute };
    },
  },
  // ... other routes
];
```

These routes:
1. Define the paths available under the `/app` route
2. Use lazy loading for route components
3. Are protected by the `ProtectedRoute` component

## Routing Issues

### 1. Limited Route-Level Permissions

The application doesn't have a robust mechanism for route-level permissions:

```jsx
// No route-level permission checking
{
  path: 'dashboard',
  lazy: async () => {
    const { DashboardRoute } = await import('./routes/Dashboard');
    return { Component: DashboardRoute };
  },
}
```

This approach:
1. Relies on navigation-level permission checking
2. Doesn't prevent direct access to routes via URL
3. Doesn't support fine-grained permission checking

### 2. Inconsistent Route Naming

Route naming is inconsistent across the application:

```jsx
// Inconsistent route naming
{
  path: 'dashboard',
  // ...
},
{
  path: 'items/ofm',
  // ...
},
{
  path: 'non-requisition-slip/dashboard',
  // ...
}
```

This approach:
1. Uses different naming conventions for similar routes
2. Makes it harder to understand the route hierarchy
3. Can lead to confusion when navigating the application

### 3. Limited Route Documentation

Routes lack documentation explaining their purpose and requirements:

```jsx
// No route documentation
{
  path: 'dashboard',
  lazy: async () => {
    const { DashboardRoute } = await import('./routes/Dashboard');
    return { Component: DashboardRoute };
  },
}
```

This approach:
1. Makes it harder to understand the purpose of each route
2. Doesn't provide information about required permissions
3. Doesn't document the expected parameters or state

### 4. Limited Route Error Handling

The application has limited error handling for routes:

```jsx
// Limited route error handling
{
  path: '*',
  element: <NotFound />,
}
```

This approach:
1. Only handles 404 errors
2. Doesn't handle other types of errors (e.g., permission errors, data loading errors)
3. Doesn't provide a way to recover from errors

## Recommendations

### 1. Implement Route-Level Permissions

Implement a robust mechanism for route-level permissions:

```jsx
// Route-level permission checking
const PermissionRoute = ({ permission, fallback = <NotFound />, children }) => {
  const hasPermission = checkPermission(permission);
  
  if (!hasPermission) {
    return fallback;
  }
  
  return children;
};

// Usage in routes
{
  path: 'dashboard',
  element: (
    <PermissionRoute permission="dashboard.view">
      <DashboardRoute />
    </PermissionRoute>
  ),
}
```

This approach:
1. Checks permissions at the route level
2. Prevents direct access to routes via URL
3. Supports fine-grained permission checking

### 2. Standardize Route Naming

Adopt a consistent route naming convention:

```jsx
// Consistent route naming
{
  path: 'dashboard',
  // ...
},
{
  path: 'items/ofm',
  // ...
},
{
  path: 'items/non-ofm',
  // ...
}
```

This approach:
1. Uses consistent naming conventions for similar routes
2. Makes it easier to understand the route hierarchy
3. Reduces confusion when navigating the application

### 3. Add Route Documentation

Document each route with its purpose and requirements:

```jsx
/**
 * Dashboard Route
 * 
 * Displays the main dashboard with requisition statistics and recent activity.
 * 
 * Permissions:
 * - dashboard.view
 * 
 * Parameters:
 * - None
 */
{
  path: 'dashboard',
  element: (
    <PermissionRoute permission="dashboard.view">
      <DashboardRoute />
    </PermissionRoute>
  ),
}
```

This approach:
1. Makes it easier to understand the purpose of each route
2. Provides information about required permissions
3. Documents the expected parameters or state

### 4. Implement Route Error Boundaries

Implement error boundaries for routes:

```jsx
// Route error boundary
const RouteErrorBoundary = ({ children }) => {
  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <div className="p-4">
          <h1 className="text-2xl font-bold text-red-500">Error</h1>
          <p className="text-gray-700">{error.message}</p>
          <button
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
            onClick={resetErrorBoundary}
          >
            Try again
          </button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
};

// Usage in routes
{
  path: 'dashboard',
  element: (
    <RouteErrorBoundary>
      <PermissionRoute permission="dashboard.view">
        <DashboardRoute />
      </PermissionRoute>
    </RouteErrorBoundary>
  ),
}
```

This approach:
1. Catches errors at the route level
2. Provides a way to recover from errors
3. Improves the user experience when errors occur

### 5. Implement Route Guards

Implement route guards for common route requirements:

```jsx
// Route guard for data loading
const DataLoadingGuard = ({ queryFn, loadingComponent = <Spinner />, children }) => {
  const { data, isLoading, error } = useQuery(queryFn);
  
  if (isLoading) {
    return loadingComponent;
  }
  
  if (error) {
    return <ErrorMessage error={error} />;
  }
  
  return children(data);
};

// Usage in routes
{
  path: 'dashboard',
  element: (
    <RouteErrorBoundary>
      <PermissionRoute permission="dashboard.view">
        <DataLoadingGuard
          queryFn={() => getDashboardData()}
          loadingComponent={<DashboardSkeleton />}
        >
          {data => <DashboardRoute data={data} />}
        </DataLoadingGuard>
      </PermissionRoute>
    </RouteErrorBoundary>
  ),
}
```

This approach:
1. Handles common route requirements (e.g., data loading, permission checking)
2. Provides a consistent way to handle loading and error states
3. Improves the user experience by showing appropriate loading and error states

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
