#!/bin/bash
# Test Production Logging
# This script tests the enhanced logging features in a production-like environment

# Configuration
LOG_DIR="../logs"
APP_LOG="$LOG_DIR/app.log"
ERROR_LOG="$LOG_DIR/error.log"

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Clear existing logs for clean test
echo "" > "$APP_LOG"
echo "" > "$ERROR_LOG"

echo "=== Testing Enhanced Production Logging ==="
echo "Log directory: $LOG_DIR"

# Set production environment
export NODE_ENV=production
export LOG_LEVEL=info

# Start the application in the background
echo "Starting application in production mode..."
node ../index.js > /dev/null 2>&1 &
APP_PID=$!

# Wait for the application to start
echo "Waiting for application to start..."
sleep 5

# Function to make a test request
make_request() {
  local method=$1
  local endpoint=$2
  local headers=$3
  local data=$4
  
  echo "Making $method request to $endpoint"
  
  if [ "$method" == "GET" ]; then
    curl -s -X "$method" \
      -H "User-Agent: LoggingTest/1.0" \
      -H "Content-Type: application/json" \
      $headers \
      "http://localhost:4000$endpoint"
  else
    curl -s -X "$method" \
      -H "User-Agent: LoggingTest/1.0" \
      -H "Content-Type: application/json" \
      $headers \
      -d "$data" \
      "http://localhost:4000$endpoint"
  fi
  
  echo ""
}

# Make test requests with different headers and methods
echo "Sending test requests..."

# Test 1: Standard request
make_request "GET" "/health" "" ""

# Test 2: Request with X-Forwarded-For
make_request "GET" "/health" "-H \"X-Forwarded-For: *************\"" ""

# Test 3: Request with multiple X-Forwarded-For values
make_request "GET" "/health" "-H \"X-Forwarded-For: *************, *************\"" ""

# Test 4: POST request with body
make_request "POST" "/v1/auth/login" "" '{"username":"<EMAIL>","password":"testpassword"}'

# Test 5: Request with referer and origin
make_request "GET" "/health" "-H \"Referer: https://example.com/dashboard\" -H \"Origin: https://example.com\"" ""

# Wait for logs to be written
sleep 2

# Check log files
echo -e "\nChecking log files..."

# Check app.log
if [ -f "$APP_LOG" ]; then
  APP_LOG_SIZE=$(wc -c < "$APP_LOG")
  echo "app.log exists ($APP_LOG_SIZE bytes)"
  
  # Check for IP addresses in logs
  if grep -q "\"ip\":" "$APP_LOG" || grep -q "\"remoteAddress\":" "$APP_LOG"; then
    echo "  Contains IP addresses: Yes"
  else
    echo "  Contains IP addresses: No"
  fi
  
  # Check for forwarded IPs
  if grep -q "\"forwardedIp\":" "$APP_LOG" || grep -q "\"x-forwarded-for\":" "$APP_LOG" || grep -q "\"forwardedFor\":" "$APP_LOG"; then
    echo "  Contains forwarded IPs: Yes"
  else
    echo "  Contains forwarded IPs: No"
  fi
  
  # Check for user agent
  if grep -q "\"userAgent\":" "$APP_LOG" || grep -q "\"user-agent\":" "$APP_LOG"; then
    echo "  Contains User-Agent: Yes"
  else
    echo "  Contains User-Agent: No"
  fi
  
  # Check for referer/origin
  if grep -q "\"referer\":" "$APP_LOG" || grep -q "\"origin\":" "$APP_LOG"; then
    echo "  Contains referer/origin: Yes"
  else
    echo "  Contains referer/origin: No"
  fi
  
  # Check for request path
  if grep -q "\"path\":" "$APP_LOG"; then
    echo "  Contains request path: Yes"
  else
    echo "  Contains request path: No"
  fi
  
  # Check for timestamp
  if grep -q "\"timestamp\":" "$APP_LOG" || grep -q "\"requestTime\":" "$APP_LOG"; then
    echo "  Contains timestamp: Yes"
  else
    echo "  Contains timestamp: No"
  fi
  
  # Display sample log entries
  echo -e "\nSample log entries:"
  grep -m 3 "\"ip\":" "$APP_LOG" | sed 's/^/  /'
else
  echo "app.log does not exist"
fi

# Test log rotation
echo -e "\nTesting log rotation..."
../scripts/rotate-logs.sh

# Check for rotated logs
ROTATED_LOGS=$(find "$LOG_DIR" -name "*.gz" | wc -l)
echo "Found $ROTATED_LOGS rotated log files"

# Stop the application
echo -e "\nStopping application..."
kill $APP_PID

echo -e "\nTest completed!"
