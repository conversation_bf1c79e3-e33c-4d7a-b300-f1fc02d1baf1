# API Endpoints and Controllers

## Overview

The PRS-Backend exposes a RESTful API for client applications to interact with the system. This document provides a detailed analysis of the API endpoints, their controllers, and how they implement the business flows.

## API Structure

The API is organized into public and private routes:

- **Public Routes**: Accessible without authentication (login, token refresh)
- **Private Routes**: Require authentication and appropriate permissions

Routes are versioned (e.g., `/v1/users`, `/v2/requisitions`) to support API evolution.

## Public Routes

### Authentication Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| POST | `/login` | Authenticate user and get tokens | AuthController.login |
| POST | `/token` | Refresh access token | AuthController.refreshUserToken |
| POST | `/verify-otp` | Verify OTP code | AuthController.verifyOTP |
| POST | `/setup-otp` | Set up OTP for a user | AuthController.setupOTP |
| POST | `/update-temp-password` | Update temporary password | AuthController.updateTempPassword |
| POST | `/forgot-password` | Request password reset | AuthController.forgotPassword |

**Example Implementation:**
```javascript
// From src/interfaces/router/public/authRoute.js
async function authRoute(fastify) {
  const authController = fastify.diScope.resolve('authController');
  const entities = fastify.diScope.resolve('entities');

  fastify.route({
    method: 'POST',
    url: '/login',
    schema: {
      body: entities.user.loginRequest,
    },
    handler: authController.login.bind(authController),
  });

  // ... other routes
}
```

## Private Routes

All private routes require authentication via the `authenticate` middleware.

### User Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| GET | `/v1/users` | Get all users | UserController.getUserList |
| GET | `/v1/users/:id` | Get user by ID | UserController.getUserDetails |
| POST | `/v1/users` | Create a new user | UserController.createUser |
| PUT | `/v1/users/:id` | Update a user | UserController.updateUser |
| PUT | `/v1/users/update-password` | Update user password | UserController.updatePassword |

### Requisition Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| GET | `/v1/requisitions` | Get all requisitions | RequisitionController.getAllRequisitions |
| GET | `/v1/requisitions/:id` | Get requisition by ID | RequisitionController.getRequisitionById |
| POST | `/v1/requisitions` | Create a requisition | RequisitionController.createRequisition |
| PUT | `/v1/requisitions/:id` | Update a requisition | RequisitionController.updateRequisition |
| POST | `/v1/requisitions/submit` | Submit a requisition | RequisitionController.submitRequisition |
| POST | `/v1/requisitions/:id/approve` | Approve a requisition | RequisitionController.approveRequisition |
| POST | `/v1/requisitions/:id/reject` | Reject a requisition | RequisitionController.rejectRequisition |
| POST | `/v1/requisitions/:id/assign` | Assign a requisition | RequisitionController.assignRequisition |
| GET | `/v1/requisitions/:id/items` | Get requisition items | RequisitionController.getRequisitionItems |
| POST | `/v1/requisitions/:id/items` | Add items to requisition | RequisitionController.addRequisitionItems |
| PUT | `/v1/requisitions/:id/items/:itemId` | Update requisition item | RequisitionController.updateRequisitionItem |
| DELETE | `/v1/requisitions/:id/items/:itemId` | Delete requisition item | RequisitionController.deleteRequisitionItem |

### Canvass Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| GET | `/v1/canvass` | Get all canvass sheets | CanvassController.getAllCanvass |
| GET | `/v1/canvass/:id` | Get canvass by ID | CanvassController.getCanvassById |
| POST | `/v1/canvass` | Create a canvass sheet | CanvassController.createCanvass |
| POST | `/v1/canvass/:id/approve` | Approve a canvass | CanvassController.approveCanvass |
| POST | `/v1/canvass/:id/comment` | Add comment to canvass | CanvassController.addCanvassComment |

### Purchase Order Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| GET | `/v1/purchase-orders` | Get all purchase orders | PurchaseOrderController.getAllPurchaseOrders |
| GET | `/v1/purchase-orders/:id` | Get purchase order by ID | PurchaseOrderController.getPurchaseOrderById |
| PUT | `/v1/purchase-orders/:id/submit` | Submit a purchase order | PurchaseOrderController.submitPurchaseOrder |
| POST | `/v1/purchase-orders/:id/approve` | Approve a purchase order | PurchaseOrderController.approvePurchaseOrder |
| POST | `/v1/purchase-orders/:id/reject` | Reject a purchase order | PurchaseOrderController.rejectPurchaseOrder |

### Delivery Receipt Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| GET | `/v1/delivery-receipts` | Get all delivery receipts | DeliveryReceiptController.getAllDeliveryReceipts |
| GET | `/v1/delivery-receipts/:id` | Get delivery receipt by ID | DeliveryReceiptController.getDeliveryReceiptById |
| POST | `/v1/delivery-receipts` | Create a delivery receipt | DeliveryReceiptController.createDeliveryReceipt |
| PUT | `/v1/delivery-receipts/:id` | Update a delivery receipt | DeliveryReceiptController.updateDeliveryReceipt |
| GET | `/v1/requisitions/:id/delivery-receipts` | Get delivery receipts for requisition | DeliveryReceiptController.getDeliveryReceiptsFromRequisitionId |

### Payment Request Routes

| Method | Endpoint | Description | Controller |
|--------|----------|-------------|------------|
| GET | `/v1/rs-payment-request` | Get all payment requests | RSPaymentRequestController.getAllPaymentRequests |
| GET | `/v1/rs-payment-request/:id` | Get payment request by ID | RSPaymentRequestController.getPaymentRequestById |
| POST | `/v1/rs-payment-request` | Create a payment request | RSPaymentRequestController.createPaymentRequest |
| PUT | `/v1/rs-payment-request/:id/submit` | Submit a payment request | RSPaymentRequestController.submitPaymentRequest |
| POST | `/v1/rs-payment-request/:id/approve` | Approve a payment request | RSPaymentRequestController.approvePurchaseRequest |
| POST | `/v1/rs-payment-request/:id/reject` | Reject a payment request | RSPaymentRequestController.rejectPurchaseRequest |

## Controller Implementation

Controllers follow a consistent pattern:

1. Receive HTTP request
2. Validate input
3. Delegate to service
4. Return HTTP response

### Example Controller Implementation

```javascript
// From src/app/handlers/controllers/requisitionController.js
async createRequisition(request, reply) {
  this.fastify.log.info(`Initiating Creation of Requisition Slip`);
  const { body, userFromToken, params, counter = false } = request;
  const {
    departmentId,
    chargeTo,
    chargeToId,
    comment,
    isDraft = true,
    projectId,
    companyId,
    category,
  } = body;
  const { REQUISITION_STATUS } = this.constants.requisition;
  const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

  const numberField = isDraft === true ? 'draftRsNumber' : 'rsNumber';

  if (!(await this.departmentRepository.getById(body.departmentId))) {
    throw this.clientErrors.NOT_FOUND({ message: 'Department not found' });
  }

  // ... more validation and processing
  
  const transaction = await this.db.sequelize.transaction();
  let requisition;

  try {
    requisition = await this.requisitionRepository.create(body, {
      transaction,
    });
    
    // ... more processing
    
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw this.clientErrors.BAD_REQUEST({
      message: 'Requisition creation failed',
    });
  }

  return reply.status(201).send({
    message: `Successfully ${
      isDraft === true ? 'saved draft' : 'created'
    } Requisition Slip`,
    data: requisition,
  });
}
```

## Input Validation

Input validation is implemented using Zod schemas defined in the domain entities:

```javascript
// From src/domain/entities/requisitionEntity.js
const createRequisitionSchema = z
  .object({
    isDraft: z
      .enum(['true'], {
        required_error: 'Draft status is required',
        invalid_type_error: 'Invalid draft status',
      })
      .transform((value) => value === 'true'),
    type: z.enum(['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], {
      message: REQUIRED_FIELD_ERROR,
    }),
    // ... other fields
  })
  .strict();
```

Validation is applied to all routes using a validator compiler:

```javascript
// From src/interfaces/router/index.js
serverContext.addHook('onRoute', (routeOptions) => {
  routeOptions.validatorCompiler = (data) =>
    utils.validatorCompiler(data.schema);
});
```

## Authentication and Authorization

Authentication is implemented using JWT tokens:

```javascript
// From src/app/handlers/middlewares/authenticate.js
const authenticate = async function (request, _reply) {
  const clientErrors = this.diScope.resolve('clientErrors');
  const userRepository = this.diScope.resolve('userRepository');
  const authErrorMsg =
    'Invalid authorization token. Please provide a valid token';

  try {
    const { id, isForOTP, isForTempPass, isForRefresh } =
      await request.jwtVerify();

    const isInvalidTokenPayload =
      !id || isForOTP || isForTempPass || isForRefresh;

    if (isInvalidTokenPayload) {
      throw new Error(authErrorMsg);
    }

    const userFromToken = await userRepository.getUserById(id);

    request.userFromToken = userFromToken;
  } catch (error) {
    throw clientErrors.UNAUTHORIZED({
      message: authErrorMsg,
    });
  }
};
```

Authorization is currently disabled due to a bug:

```javascript
// From src/app/handlers/middlewares/authorize.js
return async function (request, _reply) {
  // disable permission checking, kasi may bug
  return true;
  // ... actual authorization code never runs
};
```

## API Issues

### 1. Inconsistent Route Naming

The API uses inconsistent route naming conventions:

- Some routes use plural nouns (`/users`)
- Some routes use singular nouns (`/canvass`)
- Some routes use hyphenated names (`/rs-payment-request`)

### 2. Inconsistent Response Formats

Response formats vary across endpoints:

- Some return `{ message, data }`
- Some return the data directly
- Some include metadata like `total` and `page`

### 3. Missing API Documentation

The API lacks comprehensive documentation:

- No OpenAPI/Swagger specification
- Incomplete JSDoc comments
- No clear documentation of request/response formats

### 4. Disabled Authorization

As noted, authorization is completely disabled, which is a critical security issue.

### 5. Inconsistent Error Handling

Error handling varies across controllers:

- Some use custom error classes
- Some return generic errors
- Some include stack traces in production

## Recommendations

### 1. Standardize Route Naming

Adopt a consistent route naming convention:

- Use plural nouns for resources (`/users`, `/requisitions`)
- Use consistent casing (kebab-case is recommended for URLs)
- Follow RESTful principles for CRUD operations

### 2. Implement Consistent Response Format

Standardize response formats:

```javascript
// Example standardized response format
{
  "success": true,
  "message": "Resource created successfully",
  "data": { ... },
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

### 3. Add API Documentation

Implement comprehensive API documentation:

- Add OpenAPI/Swagger specification
- Document request/response formats
- Include examples for each endpoint

### 4. Fix Authorization System

Re-enable and fix the authorization system:

- Fix the bug in the `authorize.js` middleware
- Implement proper role-based access control
- Add comprehensive permission validation

### 5. Standardize Error Handling

Implement consistent error handling:

- Use custom error classes for all errors
- Include appropriate error codes
- Exclude sensitive information in production

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
