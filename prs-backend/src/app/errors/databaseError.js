const AppError = require('./appError');

/**
 * Error class for database errors
 */
class DatabaseError extends AppError {
  /**
   * Creates a new DatabaseError
   * 
   * @param {string} message - Error message
   * @param {string} operation - Database operation that failed
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(
    message = 'Database operation failed',
    operation = null,
    originalError = null
  ) {
    super(message, 'DATABASE_ERROR', { operation }, originalError);
    this.operation = operation;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 500; // Internal Server Error
  }

  /**
   * Creates a DatabaseError from a Sequelize error
   * 
   * @param {Error} error - Sequelize error
   * @param {string} operation - Database operation that failed
   * @param {string} message - Optional message override
   * @returns {DatabaseError} - New DatabaseError instance
   */
  static fromSequelizeError(error, operation, message = null) {
    // Handle specific Sequelize error types
    if (error.name === 'SequelizeUniqueConstraintError') {
      const ConflictError = require('./conflictError');
      return ConflictError.fromSequelizeUniqueConstraintError(
        error,
        operation.split(' ')[1] || 'Resource',
        message
      );
    }
    
    return new DatabaseError(
      message || `Database ${operation} operation failed: ${error.message}`,
      operation,
      error
    );
  }
}

module.exports = DatabaseError;
