# Cloudflared (Cloudflare Tunnel) Setup Guide

This guide will help you set up Cloudflared to securely expose your PRS services to the internet without opening inbound ports on your firewall.

## Prerequisites

1. A Cloudflare account
2. A domain registered with Cloudflare (or transferred to Cloudflare)
3. <PERSON><PERSON> and <PERSON><PERSON> Compose installed on your server

## Step 1: Add Cloudflared to Docker Compose

First, add the Cloudflared service to your `compose.yml` file:

```yaml
# Cloudflare Tunnel
cloudflared:
  image: cloudflare/cloudflared:latest
  restart: always
  command: tunnel run
  environment:
    - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
  volumes:
    - ./cloudflared:/etc/cloudflared
  networks:
    - app_network
```

Then, add the `CLOUDFLARE_TUNNEL_TOKEN` variable to your `.env` file:

```
# Cloudflare Configuration
CLOUDFLARE_TUNNEL_TOKEN=your-tunnel-token-here
```

## Step 2: Create a Cloudflare Tunnel

1. Log in to your Cloudflare account at https://dash.cloudflare.com
2. Navigate to "Access" > "Tunnels"
3. Click "Create a tunnel"
4. Give your tunnel a name (e.g., "prs-tunnel")
5. Click "Save tunnel"
6. On the next screen, you'll see your tunnel token. Copy this token and add it to your `.env` file as `CLOUDFLARE_TUNNEL_TOKEN`

## Step 3: Configure Tunnel Routes

1. In the Cloudflare dashboard, go to the "Public Hostname" tab for your tunnel
2. Click "Add a public hostname"
3. Configure the following settings:
   - **Subdomain**: Choose a subdomain (e.g., "prs")
   - **Domain**: Select your domain
   - **Path**: Leave empty for the root path
   - **Service**: Select "HTTP"
   - **URL**: Enter `http://nginx:80`
4. Click "Save"

Repeat this process to create additional public hostnames for specific services if needed:

| Subdomain | Service URL | Description |
|-----------|-------------|-------------|
| prs.yourdomain.com | http://nginx:80 | Main PRS application |
| adminer.yourdomain.com | http://nginx:80 | Database management interface |
| portainer.yourdomain.com | http://nginx:80 | Container management interface (requires additional security) |

## Step 4: Create a Cloudflared Configuration Directory

```bash
mkdir -p deployment/cloudflared
```

## Step 5: Start the Services

```bash
docker compose up -d
```

## Step 6: Verify the Connection

1. Check the logs to ensure Cloudflared is connected:
   ```bash
   docker compose logs cloudflared
   ```

2. Visit your domain (e.g., https://prs.yourdomain.com) to verify that your services are accessible

## Security Considerations

1. **Access Policies**: Consider setting up Cloudflare Access policies to restrict who can access your services
2. **WAF Rules**: Configure Cloudflare WAF (Web Application Firewall) rules for additional protection
3. **API Shield**: If exposing APIs, consider using Cloudflare API Shield
4. **Portainer Security**: Since Portainer provides full access to your Docker environment:
   - NEVER expose Portainer directly to the internet, even through Cloudflared
   - Use VPN, SSH tunneling, or internal network access only
   - If absolutely necessary to expose, use Cloudflare Access with multi-factor authentication
   - Use strong passwords for Portainer admin accounts
   - Regularly update Portainer to the latest version
   - Monitor access logs for unauthorized access attempts

## Troubleshooting

1. **Connection Issues**:
   - Check the Cloudflared logs: `docker compose logs cloudflared`
   - Verify your tunnel token is correct
   - Ensure your domain's DNS is properly configured in Cloudflare

2. **Service Not Accessible**:
   - Verify the service is running: `docker compose ps`
   - Check the Nginx configuration
   - Ensure the service is properly connected to the Docker network

3. **Certificate Errors**:
   - Cloudflare manages certificates automatically
   - If you see certificate errors, check your Cloudflare SSL/TLS settings

4. **Portainer Connection Issues**:
   - Verify Portainer is running: `docker compose ps portainer`
   - Check Nginx logs for proxy errors: `docker compose logs nginx`
   - Ensure WebSocket support is properly configured in the Nginx server block
   - Try accessing Portainer directly (if possible) to verify it's working correctly

## Additional Configuration

### Restricting Access with Cloudflare Access

1. Go to "Access" > "Applications" in your Cloudflare dashboard
2. Click "Add an application"
3. Select "Self-hosted"
4. Configure the application:
   - **Application name**: e.g., "PRS Admin"
   - **Session duration**: How long the session should last
   - **Application domain**: e.g., npm.yourdomain.com
5. Set up access policies to control who can access the application

### Using Custom Domains

You can use multiple domains or subdomains for different services:

1. Add each domain/subdomain in the "Public Hostname" tab of your tunnel
2. Point each domain/subdomain to the appropriate service
3. Update your Nginx configuration to handle the different hostnames

### Secure Portainer Access with Cloudflared

While exposing Portainer to the internet carries security risks, if you need to access it remotely, Cloudflared provides some security benefits. Here's how to set it up with enhanced security:

1. **Add a dedicated server block in your Nginx configuration**:

```nginx
# Server block for portainer.yourdomain.com with enhanced security
server {
    listen 80;
    server_name portainer.yourdomain.com;

    location / {
        proxy_pass http://portainer:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # For WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Enhanced security headers
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' wss:;";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    }

    # Restrict access to Cloudflare IPs and internal networks
    allow ************/20;
    allow ************/22;
    # ... other Cloudflare IPs ...

    # Also allow internal networks
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;

    # Deny all other IPs
    deny all;
}
```

2. **Add a public hostname in Cloudflare Tunnel configuration**:
   - **Subdomain**: portainer
   - **Domain**: yourdomain.com
   - **Service**: http://nginx:80

3. **Implement additional security measures**:
   - Set up Cloudflare Access to require authentication before users can reach Portainer
   - Enable multi-factor authentication in Cloudflare Access
   - Configure Cloudflare WAF rules to block suspicious traffic
   - Use Cloudflare rate limiting to prevent brute force attacks
   - Set up Cloudflare Firewall Rules to restrict access by country or other criteria
   - Use strong passwords for Portainer admin accounts
   - Regularly update Portainer to the latest version
   - Monitor access logs for unauthorized access attempts

4. **Alternative secure access methods** (recommended for higher security):
   - **VPN Access**: Set up a VPN server and only allow Portainer access through the VPN
   - **SSH Tunneling**: Use SSH port forwarding to access Portainer:
     ```bash
     ssh -L 9000:localhost:9000 user@your-server
     ```
     Then access Portainer at http://localhost:9000 in your browser

Remember: Portainer has full access to your Docker environment, so even with Cloudflared, additional security measures are strongly recommended.

## Conclusion

By using Cloudflared, you've securely exposed your PRS services to the internet without opening inbound ports on your firewall. This approach provides enhanced security, DDoS protection, and automatic TLS encryption for all your services.
