# Business Logic Implementation

## Overview

This document analyzes the business logic implementation in the PRS-Backend, examining how the system handles the complex rules and workflows of a purchase requisition system. The business logic is primarily implemented in the service layer, with additional logic in controllers and repositories.

## Service Layer

The service layer is the primary location for business logic implementation. Services are organized by domain and follow a consistent pattern:

```javascript
// Example service structure
class AuthService {
  constructor({ userRepository, jwtUtils, bcryptUtils }) {
    this.userRepository = userRepository;
    this.jwtUtils = jwtUtils;
    this.bcryptUtils = bcryptUtils;
  }

  async login(credentials) {
    // Business logic for authentication
  }

  async verifyToken(token) {
    // Business logic for token verification
  }
}
```

Services are injected with their dependencies through the dependency injection container, promoting loose coupling and testability.

## Core Business Processes

### 1. Authentication and Authorization

The authentication process is handled by the `AuthService`:

```javascript
// Example authentication logic
async login(credentials) {
  const { username, password } = credentials;
  
  const user = await this.userRepository.findByUsername(username);
  
  if (!user) {
    throw clientErrors.UNAUTHORIZED({ message: 'Invalid credentials' });
  }
  
  const isPasswordValid = await this.bcryptUtils.compare(password, user.password);
  
  if (!isPasswordValid) {
    throw clientErrors.UNAUTHORIZED({ message: 'Invalid credentials' });
  }
  
  const token = this.jwtUtils.generateToken({
    id: user.id,
    username: user.username,
    roleId: user.roleId,
  });
  
  return { token, user };
}
```

Authorization is implemented through middleware that checks user permissions:

```javascript
// Example authorization logic
const authorize = (permission) => {
  return async (request) => {
    const { user } = request;
    
    if (!user) {
      throw clientErrors.UNAUTHORIZED();
    }
    
    const hasPermission = await checkPermission(user, permission);
    
    if (!hasPermission) {
      throw clientErrors.FORBIDDEN();
    }
    
    return true;
  };
};
```

### 2. Requisition Management

The requisition process involves multiple steps and status transitions:

```javascript
// Example requisition creation logic
async createRequisition(payload) {
  const { userId, departmentId, projectId, items, ...requisitionData } = payload;
  
  // Validate user and department
  const user = await this.userRepository.findById(userId);
  const department = await this.departmentRepository.findById(departmentId);
  
  if (!user || !department) {
    throw clientErrors.BAD_REQUEST({ message: 'Invalid user or department' });
  }
  
  // Generate requisition number
  const requisitionNumber = await this.generateRequisitionNumber(department.code);
  
  // Create requisition
  const requisition = await this.requisitionRepository.create({
    ...requisitionData,
    requisitionNumber,
    userId,
    departmentId,
    projectId,
    status: 'draft',
  });
  
  // Create requisition items
  await Promise.all(
    items.map(item => this.requisitionItemRepository.create({
      ...item,
      requisitionId: requisition.id,
    }))
  );
  
  return requisition;
}
```

### 3. Approval Workflow

The approval workflow involves multiple approvers and status transitions:

```javascript
// Example approval logic
async approveRequisition(payload) {
  const { requisitionId, userId, comments } = payload;
  
  // Get requisition and current approver
  const requisition = await this.requisitionRepository.findById(requisitionId);
  const currentApprover = await this.approvalRepository.getCurrentApprover(requisitionId);
  
  if (!requisition || !currentApprover) {
    throw clientErrors.NOT_FOUND({ message: 'Requisition or approver not found' });
  }
  
  // Validate approver
  if (currentApprover.userId !== userId) {
    throw clientErrors.FORBIDDEN({ message: 'Not authorized to approve this requisition' });
  }
  
  // Update approval status
  await this.approvalRepository.updateStatus(currentApprover.id, 'approved', comments);
  
  // Get next approver
  const nextApprover = await this.approvalRepository.getNextApprover(requisitionId);
  
  if (nextApprover) {
    // Update requisition status to next approval level
    await this.requisitionRepository.updateStatus(requisitionId, 'pending_approval');
    
    // Notify next approver
    await this.notificationService.notifyApprover(nextApprover.userId, requisitionId);
  } else {
    // All approvals complete, update requisition status
    await this.requisitionRepository.updateStatus(requisitionId, 'approved');
    
    // Notify requisition creator
    await this.notificationService.notifyCreator(requisition.userId, requisitionId, 'approved');
  }
  
  return { requisition, nextApprover };
}
```

### 4. Purchase Order Management

The purchase order process involves creating orders based on approved requisitions:

```javascript
// Example purchase order creation logic
async createPurchaseOrder(payload) {
  const { requisitionId, supplierId, items, ...poData } = payload;
  
  // Validate requisition
  const requisition = await this.requisitionRepository.findById(requisitionId);
  
  if (!requisition || requisition.status !== 'approved') {
    throw clientErrors.BAD_REQUEST({ message: 'Invalid or unapproved requisition' });
  }
  
  // Generate PO number
  const poNumber = await this.generatePONumber();
  
  // Create purchase order
  const purchaseOrder = await this.purchaseOrderRepository.create({
    ...poData,
    poNumber,
    requisitionId,
    supplierId,
    status: 'draft',
  });
  
  // Create purchase order items
  await Promise.all(
    items.map(item => this.purchaseOrderItemRepository.create({
      ...item,
      purchaseOrderId: purchaseOrder.id,
    }))
  );
  
  return purchaseOrder;
}
```

### 5. Delivery Receipt Management

The delivery receipt process involves recording received items:

```javascript
// Example delivery receipt creation logic
async createDeliveryReceipt(payload) {
  const { purchaseOrderId, items, ...drData } = payload;
  
  // Validate purchase order
  const purchaseOrder = await this.purchaseOrderRepository.findById(purchaseOrderId);
  
  if (!purchaseOrder || purchaseOrder.status !== 'approved') {
    throw clientErrors.BAD_REQUEST({ message: 'Invalid or unapproved purchase order' });
  }
  
  // Generate DR number
  const drNumber = await this.generateDRNumber();
  
  // Create delivery receipt
  const deliveryReceipt = await this.deliveryReceiptRepository.create({
    ...drData,
    drNumber,
    purchaseOrderId,
    status: 'received',
  });
  
  // Create delivery receipt items
  await Promise.all(
    items.map(item => this.deliveryReceiptItemRepository.create({
      ...item,
      deliveryReceiptId: deliveryReceipt.id,
    }))
  );
  
  // Update inventory
  await Promise.all(
    items.map(item => this.inventoryService.updateStock(item.itemId, item.quantity))
  );
  
  return deliveryReceipt;
}
```

### 6. Payment Request Management

The payment request process involves creating payment requests for delivered items:

```javascript
// Example payment request creation logic
async createPaymentRequest(payload) {
  const { purchaseOrderId, ...prData } = payload;
  
  // Validate purchase order
  const purchaseOrder = await this.purchaseOrderRepository.findById(purchaseOrderId);
  
  if (!purchaseOrder || purchaseOrder.status !== 'delivered') {
    throw clientErrors.BAD_REQUEST({ message: 'Invalid or undelivered purchase order' });
  }
  
  // Generate PR number
  const prNumber = await this.generatePRNumber();
  
  // Create payment request
  const paymentRequest = await this.paymentRequestRepository.create({
    ...prData,
    prNumber,
    purchaseOrderId,
    status: 'pending',
  });
  
  // Create approval workflow
  await this.createPaymentApprovalWorkflow(paymentRequest.id);
  
  return paymentRequest;
}
```

## Business Rules Implementation

### 1. Validation Rules

Business validation rules are implemented using Zod schemas:

```javascript
// Example validation schema
const createUserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(8),
  roleId: z.number().int().positive(),
});
```

And custom validation functions:

```javascript
// Example custom validation
const validateRequisition = (requisition) => {
  if (!requisition.items || requisition.items.length === 0) {
    throw clientErrors.VALIDATION_ERROR({ message: 'Requisition must have at least one item' });
  }
  
  if (requisition.status === 'draft' && requisition.submittedAt) {
    throw clientErrors.VALIDATION_ERROR({ message: 'Draft requisition cannot have a submission date' });
  }
  
  return true;
};
```

### 2. Status Transitions

Status transitions are enforced through business logic:

```javascript
// Example status transition validation
const validateStatusTransition = (currentStatus, newStatus) => {
  const allowedTransitions = {
    'draft': ['pending_approval', 'cancelled'],
    'pending_approval': ['approved', 'rejected', 'cancelled'],
    'approved': ['in_progress', 'cancelled'],
    'in_progress': ['completed', 'cancelled'],
    'rejected': ['draft', 'cancelled'],
    'completed': [],
    'cancelled': [],
  };
  
  if (!allowedTransitions[currentStatus].includes(newStatus)) {
    throw clientErrors.UNPROCESSABLE_ENTITY({
      message: `Cannot transition from ${currentStatus} to ${newStatus}`,
    });
  }
  
  return true;
};
```

### 3. Permission Checks

Permission checks are implemented through the authorization middleware:

```javascript
// Example permission check
const checkPermission = async (user, permission) => {
  const userRoles = await getUserRoles(user.id);
  
  for (const role of userRoles) {
    const rolePermissions = await getRolePermissions(role.id);
    
    if (rolePermissions.includes(permission)) {
      return true;
    }
  }
  
  return false;
};
```

### 4. Business Calculations

Business calculations are implemented in services and repositories:

```javascript
// Example business calculation
const calculateTotalAmount = (items) => {
  return items.reduce((total, item) => {
    const itemPrice = item.price;
    const itemQuantity = item.quantity;
    let itemDiscount = 0;
    
    if (item.discountType === 'fixed') {
      itemDiscount = item.discountValue * itemQuantity;
    } else if (item.discountType === 'percent') {
      itemDiscount = (itemPrice * item.discountValue / 100) * itemQuantity;
    }
    
    return total + (itemPrice * itemQuantity) - itemDiscount;
  }, 0);
};
```

## Error Handling

Business logic errors are handled through standardized error classes:

```javascript
// Example error handling
try {
  const result = await this.someBusinessOperation(payload);
  return result;
} catch (error) {
  if (error instanceof HttpError) {
    throw error;
  }
  
  this.logger.error({
    message: error.message,
    stack: error.stack,
    payload,
  });
  
  throw serverErrors.INTERNAL_SERVER_ERROR();
}
```

## Business Logic Strengths

1. **Clear Separation of Concerns** - Business logic is primarily in services
2. **Dependency Injection** - Services receive dependencies through DI
3. **Comprehensive Validation** - Input validation using Zod
4. **Standardized Error Handling** - Consistent error responses
5. **Complex Workflow Support** - Handles multi-step approval workflows

## Business Logic Challenges

1. **Complex Business Rules** - Some services contain very complex business rules
2. **Limited Documentation** - Minimal documentation of business rules
3. **Some Logic in Repositories** - Some business logic leaks into repositories
4. **Mixed Validation Approaches** - Uses both Zod and custom validation

## Recommendations

1. **Document Business Rules** - Create comprehensive documentation of business rules
2. **Refactor Complex Logic** - Break down complex business logic into smaller, reusable parts
3. **Implement Domain Events** - Use domain events for cross-service communication
4. **Standardize Validation** - Adopt a consistent approach to validation
5. **Add Business Rule Tests** - Implement comprehensive tests for business rules

## Conclusion

The PRS-Backend implements comprehensive business logic that effectively handles the complex rules and workflows of a purchase requisition system. The use of services with dependency injection, validation schemas, and standardized error handling demonstrates a mature approach to business logic implementation.

The business logic supports the complex requirements while maintaining maintainability and extensibility. Areas for improvement include documenting business rules, refactoring complex logic, and implementing domain events for cross-service communication.
