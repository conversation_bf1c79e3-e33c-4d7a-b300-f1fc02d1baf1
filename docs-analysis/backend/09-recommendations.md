# Recommendations and Improvements

## Overview

Based on the comprehensive analysis of the PRS-Backend codebase, this document provides detailed recommendations for improvements. These recommendations are organized by priority and include specific code examples and implementation strategies.

## Critical Issues

### 1. Fix Authorization System

The most critical issue is the disabled authorization system, which is a significant security vulnerability.

**Current Implementation:**
```javascript
// From src/app/handlers/middlewares/authorize.js
return async function (request, _reply) {
  // disable permission checking, ka<PERSON> may bug
  return true;
  // ... actual authorization code never runs
};
```

**Recommended Implementation:**
```javascript
// Fixed authorization middleware
return async function (request, _reply) {
  const clientErrors = this.diScope.resolve('clientErrors');
  const errorMessage = 'You do not have permission to perform this action';
  const { userFromToken } = request;

  const isUserWithoutRole =
    !userFromToken ||
    !userFromToken?.role ||
    !userFromToken?.role?.permissions;

  if (isUserWithoutRole) {
    throw clientErrors.FORBIDDEN({
      message: errorMessage,
    });
  }

  /* Support OR & AND permission logic */
  const checkPermissions = requireAll ? 'every' : 'some';
  const hasPermission = permissions[checkPermissions]((requiredPermission) =>
    userFromToken.role.permissions.some(
      (permission) =>
        permission.module === requiredPermission.module &&
        permission.action === requiredPermission.action,
    ),
  );

  if (!hasPermission) {
    throw clientErrors.FORBIDDEN({
      message: errorMessage,
    });
  }
};
```

**Implementation Steps:**
1. Identify and fix the bug in the authorization middleware
2. Re-enable permission checking
3. Add comprehensive tests for the authorization middleware
4. Review all routes to ensure they have appropriate authorization

### 2. Implement State Machine Pattern for Status Management

Status transitions are not consistently enforced, leading to potential data inconsistency.

**Current Implementation:**
```javascript
// From src/app/services/requisitionApproverService.js
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  // ... other code
  
  // TODO: Check status of requisition before updating, if it's not '<the correct status before assigning>', throw an error
  await this.requisitionRepository.update(
    { id: requisitionId },
    { status: 'assigning' },
    { transaction },
  );
  
  // ... other code
}
```

**Recommended Implementation:**
```javascript
// State machine implementation
class RequisitionStateMachine {
  constructor(requisition) {
    this.requisition = requisition;
    this.validTransitions = {
      'draft': ['submitted'],
      'submitted': ['approved', 'rejected'],
      'approved': ['assigning'],
      'assigning': ['assigned'],
      'assigned': ['partially_canvassed', 'canvass_for_approval'],
      'partially_canvassed': ['canvass_for_approval'],
      'canvass_for_approval': ['for_po_review', 'rejected'],
      'for_po_review': ['for_po_approval'],
      'for_po_approval': ['for_delivery', 'rejected'],
      'for_delivery': ['for_pr_approval'],
      'for_pr_approval': ['closed', 'rejected'],
      'rejected': ['submitted'],
    };
  }
  
  canTransitionTo(targetStatus) {
    const currentStatus = this.requisition.status;
    return this.validTransitions[currentStatus]?.includes(targetStatus) || false;
  }
  
  async transitionTo(targetStatus, repository, transaction) {
    if (!this.canTransitionTo(targetStatus)) {
      throw new Error(`Invalid status transition from ${this.requisition.status} to ${targetStatus}`);
    }
    
    await repository.update(
      { id: this.requisition.id },
      { status: targetStatus },
      { transaction }
    );
    
    this.requisition.status = targetStatus;
    return this.requisition;
  }
}

// Usage in service
async setRSStatusToAssigningWhenFullyApproved({
  requisitionId,
  transaction,
  approverId,
}) {
  // ... other code
  
  const requisition = await this.requisitionRepository.findById(requisitionId);
  const stateMachine = new RequisitionStateMachine(requisition);
  
  await stateMachine.transitionTo('assigning', this.requisitionRepository, transaction);
  
  // ... other code
}
```

**Implementation Steps:**
1. Create a state machine class for each entity with state transitions
2. Define valid state transitions for each entity
3. Update services to use the state machine for state transitions
4. Add comprehensive tests for state transitions

### 3. Improve Transaction Management

Transaction management is inconsistent across the codebase, leading to potential data inconsistency.

**Current Implementation:**
```javascript
// Example 1: Transaction passed from controller
async createRequisition(data, transaction) {
  // Use transaction passed from controller
}

// Example 2: Transaction created in service
async createDeliveryReceipt(data, userFromToken) {
  const transaction = await this.db.sequelize.transaction();
  try {
    // ... operations
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

**Recommended Implementation:**
```javascript
// Transaction manager utility
async function withTransaction(db, callback) {
  const transaction = await db.sequelize.transaction();
  try {
    const result = await callback(transaction);
    await transaction.commit();
    return result;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// Usage in service
async createDeliveryReceipt(data, userFromToken) {
  return await withTransaction(this.db, async (transaction) => {
    // ... operations using transaction
    return result;
  });
}
```

**Implementation Steps:**
1. Create a transaction manager utility
2. Update services to use the transaction manager
3. Ensure all multi-step operations use transactions
4. Add comprehensive tests for transaction management

## High-Priority Improvements

### 1. Reduce Service Dependencies

Many services have an excessive number of dependencies, making them difficult to understand and maintain.

**Current Implementation:**
```javascript
// From src/app/services/requisitionService.js
constructor(container) {
  const {
    db,
    utils,
    entities,
    userRepository,
    requisitionRepository,
    requisitionItemListRepository,
    tomItemRepository,
    commentRepository,
    attachmentRepository,
    supplierRepository,
    projectRepository,
    companyRepository,
    constants,
    projectApprovalRepository,
    clientErrors,
    requisitionApproverRepository,
    departmentApprovalRepository,
    fastify,
    itemRepository,
    nonOfmItemRepository,
    notificationRepository,
    historyRepository,
    canvassRequisitionRepository,
    purchaseOrderRepository,
    deliveryReceiptRepository,
    rsPaymentRequestRepository,
    notificationService,
    departmentRepository,
    departmentAssociationApprovalRepository,
  } = container;
  
  // ... many property assignments
}
```

**Recommended Implementation:**
```javascript
// Service facade pattern
class RequisitionServiceFacade {
  constructor({
    requisitionService,
    requisitionApproverService,
    notificationService,
    logger,
  }) {
    this.requisitionService = requisitionService;
    this.requisitionApproverService = requisitionApproverService;
    this.notificationService = notificationService;
    this.logger = logger;
  }
  
  async createRequisition(data, userFromToken) {
    this.logger.info('Creating requisition');
    
    return await withTransaction(this.db, async (transaction) => {
      const requisition = await this.requisitionService.createRequisition(data, transaction);
      
      if (!data.isDraft) {
        await this.requisitionApproverService.assignApprovers(requisition.id, data, transaction);
        await this.notificationService.sendApprovalNotifications(requisition.id, userFromToken.id, transaction);
      }
      
      return requisition;
    });
  }
  
  // ... other methods
}

// Focused service implementation
class RequisitionService {
  constructor({
    requisitionRepository,
    requisitionItemRepository,
    db,
    logger,
  }) {
    this.requisitionRepository = requisitionRepository;
    this.requisitionItemRepository = requisitionItemRepository;
    this.db = db;
    this.logger = logger;
  }
  
  // ... methods
}
```

**Implementation Steps:**
1. Identify service responsibilities and dependencies
2. Create focused services with minimal dependencies
3. Create service facades for orchestration
4. Update container configuration to register new services
5. Update controllers to use service facades

### 2. Extract Business Rules to Configuration

Business rules are scattered throughout the codebase and often hardcoded.

**Current Implementation:**
```javascript
// From src/app/services/requisitionService.js
async rsApproversV2({
  category,
  projectId,
  departmentId,
  userFromToken,
  transaction,
  companyId,
}) {
  const { APPROVAL_TYPES } = this.constants.approval;
  const { REQUISITION_CATEGORIES } = this.constants.requisition;
  const approvalTypeCode = APPROVAL_TYPES.REQUISITION_SLIP.code;

  let approvers = [];
  let optionalApprovers = [];

  if (category === REQUISITION_CATEGORIES[0]) {
    // Association
    // ... logic for association
  } else if (category === REQUISITION_CATEGORIES[1]) {
    // Company
    // ... logic for company
  } else if (category === REQUISITION_CATEGORIES[2]) {
    // Project
    // ... logic for project
  }

  return {
    approvers,
    optionalApprovers,
  };
}
```

**Recommended Implementation:**
```javascript
// Configuration-driven approach
// src/domain/config/approvalWorkflows.js
const approvalWorkflows = {
  'requisition': {
    'association': {
      repository: 'departmentAssociationApprovalRepository',
      filters: {
        departmentId: 'departmentId',
        approvalTypeCode: 'approvalTypeCode',
        companyId: 'companyId',
      },
    },
    'company': {
      repository: 'departmentApprovalRepository',
      filters: {
        departmentId: 'departmentId',
        approvalTypeCode: 'approvalTypeCode',
      },
    },
    'project': {
      repository: 'projectApprovalRepository',
      filters: {
        projectId: 'projectId',
        approvalTypeCode: 'approvalTypeCode',
      },
    },
  },
  // ... other workflows
};

// Usage in service
async getApprovers(entityType, category, context) {
  const workflow = approvalWorkflows[entityType][category];
  if (!workflow) {
    throw new Error(`No approval workflow defined for ${entityType}/${category}`);
  }
  
  const repository = this[workflow.repository];
  const filters = {};
  
  for (const [filterKey, contextKey] of Object.entries(workflow.filters)) {
    filters[filterKey] = context[contextKey];
  }
  
  const approvals = await repository.findAll({
    where: filters,
    paginate: false,
    transaction: context.transaction,
  });
  
  return {
    approvers: approvals.data
      .filter(approver => !approver.isOptional)
      .map(approver => ({
        approverId: approver.approverId,
        level: approver.level,
      })),
    optionalApprovers: approvals.data
      .filter(approver => approver.isOptional)
      .map(approver => ({
        approverId: approver.approverId,
        level: approver.level,
      })),
  };
}
```

**Implementation Steps:**
1. Identify business rules that can be extracted to configuration
2. Create configuration files for business rules
3. Update services to use configuration-driven approach
4. Add comprehensive tests for business rules

### 3. Standardize Error Handling

Error handling is inconsistent across the codebase.

**Current Implementation:**
```javascript
// Example 1: Specific error with message
if (!selectedSuppliers.total) {
  throw this.clientErrors.BAD_REQUEST({
    message: 'No selected suppliers found for this canvass requisition',
  });
}

// Example 2: Generic error with no specific message
try {
  // ... operations
} catch (error) {
  this.fastify.log.error('[ERROR] Creating PO for supplier');
  this.fastify.log.error(error);
  throw error;
}
```

**Recommended Implementation:**
```javascript
// Domain-specific error classes
class DomainError extends Error {
  constructor(message, code = 'DOMAIN_ERROR') {
    super(message);
    this.code = code;
  }
}

class ValidationError extends DomainError {
  constructor(message, validationErrors) {
    super(message, 'VALIDATION_ERROR');
    this.validationErrors = validationErrors;
  }
}

class BusinessRuleError extends DomainError {
  constructor(message) {
    super(message, 'BUSINESS_RULE_ERROR');
  }
}

// Error handler
function handleError(error, logger) {
  logger.error({
    code: error.code || 'UNKNOWN_ERROR',
    message: error.message,
    stack: error.stack,
  });
  
  if (error instanceof ValidationError) {
    return {
      status: 422,
      body: {
        code: error.code,
        message: error.message,
        validationErrors: error.validationErrors,
      },
    };
  }
  
  if (error instanceof BusinessRuleError) {
    return {
      status: 400,
      body: {
        code: error.code,
        message: error.message,
      },
    };
  }
  
  // ... handle other error types
  
  return {
    status: 500,
    body: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
    },
  };
}

// Usage in service
if (!selectedSuppliers.total) {
  throw new BusinessRuleError('No selected suppliers found for this canvass requisition');
}

try {
  // ... operations
} catch (error) {
  this.logger.error({
    message: 'Error creating PO for supplier',
    error: error.message,
    stack: error.stack,
  });
  
  if (error instanceof DomainError) {
    throw error;
  }
  
  throw new BusinessRuleError('Failed to create purchase order');
}
```

**Implementation Steps:**
1. Create domain-specific error classes
2. Create error handler utility
3. Update services to use domain-specific errors
4. Update controllers to use error handler
5. Add comprehensive tests for error handling

## Medium-Priority Improvements

### 1. Implement Repository Pattern Consistently

The repository pattern is not consistently applied across the codebase.

**Current Implementation:**
```javascript
// Example 1: Direct model access
const users = await this.db.userModel.findAll({
  where: { status: 'active' },
});

// Example 2: Repository pattern
const users = await this.userRepository.findAll({
  where: { status: 'active' },
});
```

**Recommended Implementation:**
```javascript
// Base repository
class BaseRepository {
  constructor({ db, model, logger }) {
    this.db = db;
    this.model = model;
    this.logger = logger;
  }
  
  async findAll(options = {}) {
    try {
      const { where, order, limit, offset, include, attributes, paginate = true } = options;
      
      const query = {
        where,
        order,
        include,
        attributes,
      };
      
      if (paginate) {
        query.limit = limit || 10;
        query.offset = offset || 0;
      }
      
      const result = await this.model.findAndCountAll(query);
      
      return {
        data: result.rows,
        total: result.count,
        limit: query.limit,
        offset: query.offset,
      };
    } catch (error) {
      this.logger.error({
        message: 'Error in findAll',
        model: this.model.name,
        error: error.message,
      });
      
      throw error;
    }
  }
  
  // ... other methods
}

// User repository
class UserRepository extends BaseRepository {
  constructor({ db, logger }) {
    super({ db, model: db.userModel, logger });
  }
  
  async findActiveUsers() {
    return this.findAll({
      where: { status: 'active' },
    });
  }
  
  // ... other methods
}
```

**Implementation Steps:**
1. Create base repository with common methods
2. Create specific repositories for each model
3. Update services to use repositories consistently
4. Add comprehensive tests for repositories

### 2. Implement Consistent API Response Format

Response formats vary across endpoints.

**Current Implementation:**
```javascript
// Example 1
return reply.status(201).send({
  message: 'Successfully created',
  data: result,
});

// Example 2
return reply.status(200).send(result);

// Example 3
return reply.status(200).send({
  data: result,
  total: result.length,
  page: 1,
  limit: 10,
});
```

**Recommended Implementation:**
```javascript
// Response formatter utility
function formatResponse(data, message, meta = {}) {
  return {
    success: true,
    message,
    data,
    meta,
  };
}

// Error response formatter
function formatErrorResponse(error) {
  return {
    success: false,
    message: error.message,
    code: error.code || 'UNKNOWN_ERROR',
    details: error.details,
  };
}

// Usage in controller
return reply.status(201).send(
  formatResponse(
    result,
    'Successfully created',
    { timestamp: new Date() }
  )
);

// Error handling
app.setErrorHandler((error, request, reply) => {
  request.log.error(error);
  
  const statusCode = error.statusCode || 500;
  const response = formatErrorResponse(error);
  
  reply.status(statusCode).send(response);
});
```

**Implementation Steps:**
1. Create response formatter utilities
2. Update controllers to use response formatters
3. Update error handler to use error response formatter
4. Add comprehensive tests for response formatting

### 3. Add API Documentation

The API lacks comprehensive documentation.

**Recommended Implementation:**
```javascript
// OpenAPI specification
const openApiOptions = {
  routePrefix: '/documentation',
  swagger: {
    info: {
      title: 'PRS-Backend API',
      description: 'API for Purchase Requisition System',
      version: '1.0.0',
    },
    host: 'localhost',
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    securityDefinitions: {
      bearerAuth: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
      },
    },
  },
  exposeRoute: true,
};

// Route documentation
fastify.route({
  method: 'POST',
  url: '/',
  schema: {
    description: 'Create a new requisition',
    tags: ['requisitions'],
    summary: 'Create requisition',
    body: {
      type: 'object',
      properties: {
        isDraft: { type: 'boolean', description: 'Whether the requisition is a draft' },
        type: { type: 'string', enum: ['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], description: 'Type of requisition' },
        departmentId: { type: 'integer', description: 'Department ID' },
        projectId: { type: 'integer', description: 'Project ID' },
        companyId: { type: 'integer', description: 'Company ID' },
      },
      required: ['isDraft', 'type', 'departmentId'],
    },
    response: {
      201: {
        description: 'Successful response',
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          message: { type: 'string' },
          data: {
            type: 'object',
            properties: {
              id: { type: 'integer' },
              status: { type: 'string' },
              // ... other properties
            },
          },
          meta: {
            type: 'object',
            properties: {
              timestamp: { type: 'string', format: 'date-time' },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  handler: requisitionController.createRequisition.bind(requisitionController),
});
```

**Implementation Steps:**
1. Install and configure Swagger/OpenAPI plugin
2. Add documentation to all routes
3. Add examples and descriptions
4. Add authentication documentation
5. Add error response documentation

## Low-Priority Improvements

### 1. Clean Up Commented-Out Code and TODOs

The codebase contains numerous commented-out code blocks and TODO comments.

**Current Implementation:**
```javascript
// From src/app/services/rsPaymentRequestService.js
async createRsPaymentRequest(request) {
  // ... other code
  
  // comment for testing
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });
  
  // ... other code
}
```

**Recommended Implementation:**
```javascript
// Clean implementation
async createRsPaymentRequest(request) {
  // ... other code
  
  const { termsData } = await this.getPurchaseOrderDetails({
    id: payload.purchaseOrderId,
    employeeId: payload?.termsData?.employeeId,
    userFromToken,
  });
  
  // ... other code
}
```

**Implementation Steps:**
1. Identify commented-out code and TODOs
2. Address TODOs with proper implementation
3. Remove commented-out code
4. Add proper documentation where needed

### 2. Implement Consistent Naming Conventions

The codebase uses inconsistent naming conventions.

**Current Implementation:**
```javascript
// Example 1: camelCase
const userRepository = this.userRepository;

// Example 2: snake_case
const user_id = request.params.id;

// Example 3: Mixed
const RSNumber = await this.generateRSNumber();
```

**Recommended Implementation:**
```javascript
// Consistent naming conventions
const userRepository = this.userRepository;
const userId = request.params.id;
const rsNumber = await this.generateRsNumber();
```

**Implementation Steps:**
1. Define naming conventions for different types of identifiers
2. Update codebase to follow conventions
3. Add linting rules to enforce conventions
4. Document conventions for future development

### 3. Add Performance Monitoring

The application lacks performance monitoring.

**Recommended Implementation:**
```javascript
// Performance monitoring middleware
app.addHook('onRequest', async (request, reply) => {
  request.startTime = process.hrtime();
});

app.addHook('onResponse', async (request, reply) => {
  const hrtime = process.hrtime(request.startTime);
  const responseTime = hrtime[0] * 1000 + hrtime[1] / 1000000;
  
  request.log.info({
    responseTime,
    statusCode: reply.statusCode,
    method: request.method,
    url: request.url,
  });
});

// Database query monitoring
const sequelize = new Sequelize(config.database, {
  // ... other options
  benchmark: true,
  logging: (sql, timing) => {
    logger.info({
      sql,
      timing,
    });
  },
});
```

**Implementation Steps:**
1. Add performance monitoring middleware
2. Configure database query logging
3. Implement application metrics collection
4. Set up monitoring dashboard

## Implementation Roadmap

### Phase 1: Critical Fixes (1-2 weeks)
1. Fix the authorization system by re-enabling permission checking
2. Implement proper transaction management for all critical operations
3. Add missing validations for status transitions
4. Clean up commented-out code and fix obvious bugs

### Phase 2: Business Logic Refactoring (2-4 weeks)
1. Implement state machine pattern for status management
2. Extract business rules to configuration
3. Standardize error handling
4. Eliminate duplicate business logic

### Phase 3: Code Structure Improvements (3-6 weeks)
1. Reduce service dependencies
2. Implement domain-driven design patterns
3. Improve test coverage
4. Refactor complex methods for clarity

### Phase 4: Flow-Specific Improvements (4-8 weeks)
1. Refactor requisition creation and submission
2. Improve approval process
3. Enhance canvassing process
4. Optimize purchase order generation
5. Streamline delivery receipt management
6. Refine payment request processing

## Conclusion

The PRS-Backend system has a solid foundation but suffers from several issues in its business logic implementation. By addressing the identified issues and implementing the recommended improvements, the system can become more reliable, maintainable, and performant.

The most critical issues to address are:
1. The disabled permission checking in the authorization middleware
2. Inconsistent status management across the workflow
3. Incomplete transaction management
4. Excessive service dependencies
5. Duplicate business logic

By following the proposed implementation roadmap, these issues can be systematically addressed while minimizing disruption to the existing system.
