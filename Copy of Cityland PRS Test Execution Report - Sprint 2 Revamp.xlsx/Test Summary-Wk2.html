<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-right:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s37{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s47{border-bottom:1px SOLID #000000;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#3c78d8;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #000000;background-color:#3c78d8;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s46{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-right:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-right:1px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s38{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s49{border-right:1px SOLID #000000;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s42{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s44{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s41{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #000000;background-color:#3c78d8;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s40{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s36{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:center;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:3px SOLID #000000;background-color:#000000;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #ffffff;background-color:#ffffff;}.ritz .waffle .s43{border-bottom:1px SOLID #000000;background-color:#ffffff;}.ritz .waffle .s45{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s48{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s50{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s53{background-color:#0b5394;text-align:center;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:9pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:3px SOLID #ffffff;background-color:#000000;text-align:center;font-weight:bold;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:12pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;background-color:#0000ff;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s39{border-bottom:1px SOLID #000000;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s52{background-color:#000000;text-align:center;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ffffff;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{background-color:#000000;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:center;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #ffffff;border-right:1px SOLID #ffffff;background-color:#000000;text-align:left;color:#ffffff;font-family:Arial;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s51{background-color:#ffffff;text-align:center;color:#ffffff;font-family:Arial;font-size:11pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s35{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;color:#ffffff;font-family:"docs-Proxima Nova",Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="711325982C0" style="width:687px;" class="column-headers-background">A</th><th id="711325982C1" style="width:100px;" class="column-headers-background">B</th><th id="711325982C2" style="width:100px;" class="column-headers-background">C</th><th id="711325982C3" style="width:100px;" class="column-headers-background">D</th><th id="711325982C4" style="width:100px;" class="column-headers-background">E</th><th id="711325982C5" style="width:100px;" class="column-headers-background">F</th><th id="711325982C6" style="width:100px;" class="column-headers-background">G</th><th id="711325982C7" style="width:113px;" class="column-headers-background">H</th><th id="711325982C8" style="width:89px;" class="column-headers-background">I</th><th id="711325982C9" style="width:177px;" class="column-headers-background">J</th><th id="711325982C10" style="width:177px;" class="column-headers-background">K</th><th id="711325982C11" style="width:129px;" class="column-headers-background">L</th><th id="711325982C12" style="width:100px;" class="column-headers-background">M</th><th id="711325982C13" style="width:132px;" class="column-headers-background">N</th><th id="711325982C14" style="width:146px;" class="column-headers-background">O</th><th id="711325982C15" style="width:100px;" class="column-headers-background">P</th><th id="711325982C16" style="width:22px;" class="column-headers-background">Q</th><th id="711325982C17" style="width:119px;" class="column-headers-background">R</th><th id="711325982C18" style="width:100px;" class="column-headers-background">S</th></tr></thead><tbody><tr style="height: 31px"><th id="711325982R0" style="height: 31px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 31px">1</div></th><td class="s0" colspan="12"> </td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 42px"><th id="711325982R1" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">2</div></th><td class="s2">Stories to be Tested</td><td class="s3">Total Tests</td><td class="s3">Not Started</td><td class="s3">Passed</td><td class="s3">Failed</td><td class="s3">Blocked</td><td class="s3">Out of Scope</td><td class="s3">Deprecated</td><td class="s3">Not Run</td><td class="s3">In Progress</td><td class="s3">Percentage Complete %</td><td class="s4"># of Defects Raised</td><td class="s5">Assignee</td><td class="s1"></td><td class="s6"></td><td class="s6"></td><td></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="711325982R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s7">PRS-017 - [Canvassing] - Viewing of Canvass Sheet by Approver, Approving of Assigned Purchasing Staff Supervisor , Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s8">22</td><td class="s9">0</td><td class="s9">18</td><td class="s9">0</td><td class="s9">4</td><td class="s9">0</td><td class="s9">0</td><td class="s9">0</td><td class="s9">0</td><td class="s10">100.00%</td><td class="s11">42</td><td class="s12">Ann</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s16"></td><td class="s17"></td><td class="s18"></td></tr><tr style="height: 19px"><th id="711325982R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s7">PRS-017 - [Canvassing] - Approving of Management, Creating a Canvass - Attachments are clickable, Allow Manual overriding of Canvass Quantity, Assigning of Canvass Sheet Approvers with an existing Canvass Sheet, Adding of Items for Canvass Sheet, Supplier for Transfer of Materials</td><td class="s8">30</td><td class="s9">0</td><td class="s9">20</td><td class="s9">9</td><td class="s9">0</td><td class="s9">1</td><td class="s9">0</td><td class="s9">0</td><td class="s9">0</td><td class="s10">100.00%</td><td class="s11">16</td><td class="s12">Cherry</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s16"></td><td class="s17"></td><td class="s18"></td></tr><tr style="height: 19px"><th id="711325982R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s19">PRS-020 - [Syncing] - OFM Item Sync Scenario, Assigning Alt Approver, Viewing of Leave</td><td class="s8">43</td><td class="s9">0</td><td class="s9">29</td><td class="s9">9</td><td class="s9">5</td><td class="s9">0</td><td class="s9">0</td><td class="s9">0</td><td class="s9">0</td><td class="s10">100.00%</td><td class="s11">4</td><td class="s12">Kams</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s16"></td><td class="s17"></td><td class="s18"></td></tr><tr style="height: 24px"><th id="711325982R5" style="height: 24px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 24px">6</div></th><td class="s20">TOTAL</td><td class="s21">95</td><td class="s21">0</td><td class="s21">67</td><td class="s21">18</td><td class="s21">9</td><td class="s21">1</td><td class="s21">0</td><td class="s21">0</td><td class="s21">0</td><td class="s22">100.00%</td><td class="s23">62</td><td></td><td class="s24"></td><td class="s25">Defects Raised</td><td class="s15">1</td><td class="s16"></td><td class="s26">Defects Raised</td><td class="s18">0</td></tr><tr style="height: 19px"><th id="711325982R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td></td><td></td><td></td><td></td><td class="s27"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s24"></td><td class="s25"># of User Stories</td><td class="s28">6</td><td class="s16"></td><td class="s26"># of User Stories</td><td class="s29">6</td></tr><tr style="height: 19px"><th id="711325982R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s24"></td><td class="s25">Sprint Velocity</td><td class="s28">30</td><td class="s16"></td><td class="s30">Sprint Velocity</td><td class="s31">53</td></tr><tr style="height: 31px"><th id="711325982R8" style="height: 31px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 31px">9</div></th><td class="s0" colspan="12">API TEST SUMMARY</td><td></td><td class="s32"></td><td class="s33">Executed</td><td class="s15">95</td><td></td><td></td><td></td></tr><tr style="height: 42px"><th id="711325982R9" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">10</div></th><td class="s3">Stories to be Tested</td><td class="s3">Total Tests</td><td class="s3">Not Started</td><td class="s3">Not Started</td><td class="s3">Failed</td><td class="s3">Blocked</td><td class="s3">Out of Scope</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3">Percentage Complete %</td><td class="s4"># of Defects Raised</td><td></td><td class="s32"></td><td class="s33">Ageing</td><td class="s15">#REF!</td><td></td><td></td><td></td></tr><tr style="height: 19px"><th id="711325982R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s34"></td><td class="s35">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s37">#REF!</td><td class="s38">0</td><td></td><td class="s32"></td><td class="s39">Defect Ageing</td><td class="s40">#REF!</td><td></td><td></td><td></td></tr><tr style="height: 21px"><th id="711325982R11" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">12</div></th><td class="s34"></td><td class="s35">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s36">#REF!</td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s37">#REF!</td><td class="s38">0</td><td></td><td class="s41"></td><td class="s42"></td><td class="s43"></td><td></td><td></td><td></td></tr><tr style="height: 21px"><th id="711325982R12" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">13</div></th><td class="s34"></td><td class="s35"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s38"></td><td></td><td class="s44"></td><td class="s45">Defect count by Story Count</td><td class="s15">1</td><td></td><td></td><td></td></tr><tr style="height: 21px"><th id="711325982R13" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">14</div></th><td class="s34"></td><td class="s35"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s38"></td><td></td><td class="s46"></td><td class="s47">Defect Density </td><td class="s40">3.33%</td><td></td><td></td><td></td></tr><tr style="height: 21px"><th id="711325982R14" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">15</div></th><td class="s34"></td><td class="s35"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s38"></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 21px"><th id="711325982R15" style="height: 21px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 21px">16</div></th><td class="s48"></td><td class="s35"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s36"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s37"></td><td class="s38"></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 24px"><th id="711325982R16" style="height: 24px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 24px">17</div></th><td class="s20">TOTAL</td><td class="s21">#REF!</td><td class="s21">#REF!</td><td class="s21">#REF!</td><td class="s21">#REF!</td><td class="s21">#REF!</td><td class="s21">#REF!</td><td class="s21"></td><td class="s21"></td><td class="s21"></td><td class="s22">#REF!</td><td class="s23">0</td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R17" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">18</div></th><td></td><td class="s43"></td><td class="s43"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 37px"><th id="711325982R18" style="height: 37px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 37px">19</div></th><td class="s49">Overall Summary</td><td class="s50">71.58%</td><td class="s50">#REF!</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R19" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">20</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R20" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">21</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s51"></td><td class="s52">Sprint Total Defects</td><td class="s53">62</td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R21" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">22</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R22" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">23</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R23" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">24</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R24" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">25</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R25" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">26</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R26" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">27</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R27" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">28</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R28" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">29</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R29" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">30</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R30" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">31</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R31" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">32</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R32" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">33</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R33" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">34</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R34" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">35</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R35" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">36</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R36" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">37</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R37" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">38</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R38" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">39</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr><tr style="height: 20px"><th id="711325982R39" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">40</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s1"></td><td></td><td></td><td></td><td></td><td></td></tr></tbody></table></div><div id='embed_878046177' class='waffle-embedded-object-overlay' style='width: 490px; height: 371px; display: block;'></div><div id='embed_964845725' class='waffle-embedded-object-overlay' style='width: 512px; height: 371px; display: block;'></div><script>
  function posObj(sheet, id, row, col, x, y) {
      var rtl = false;
      var sheetElement = document.getElementById(sheet);
      if (!sheetElement) {
        sheetElement = document.getElementById(sheet + '-grid-container');
      }
      if (sheetElement) {
        rtl = sheetElement.getAttribute('dir') == 'rtl';
      }
      var r = document.getElementById(sheet+'R'+row);
      var c = document.getElementById(sheet+'C'+col);
      if (r && c) {
        var objElement = document.getElementById(id);
        var s = objElement.style;
        var t = y;
        while (r && r != sheetElement) {
          t += r.offsetTop;
          r = r.offsetParent;
      }
      var offsetX = x;
      while (c && c != sheetElement) {
        offsetX += c.offsetLeft;
        c = c.offsetParent;
      }
      if (rtl) {
        offsetX -= objElement.offsetWidth;
      }
      s.left = offsetX + 'px';
      s.top = t + 'px';
      s.display = 'block';
      s.border = '1px solid #000000';
    }
  }

  function posObjs() {
  posObj('711325982', 'embed_878046177', 20, 0, 33, 17);posObj('711325982', 'embed_964845725', 20, 2, 97, 17);}posObjs();</script><script src="resources/340999021-ChartsCombinedJ2clBootstrap_bootstrap_core.js"></script><script>var ritzspreadsheetconstants = {"localeName":"en_US","timeZoneConstants":{"GMT":{"names_ext":{"STD_GENERIC_LOCATION":"GMT","STD_LONG_NAME_GMT":"GMT"},"std_offset":0,"names":["GMT","Greenwich Mean Time"],"id":"GMT","transitions":[]},"America/Los_Angeles":{"names_ext":{"DST_GENERIC_LOCATION":"Los Angeles Time","DST_LONG_NAME_GMT":"GMT-07:00","STD_GENERIC_LOCATION":"Los Angeles Time","STD_LONG_NAME_GMT":"GMT-08:00"},"std_offset":-480,"names":["PST","Pacific Standard Time","PDT","Pacific Daylight Time"],"id":"America/Los_Angeles","transitions":[2770,60,7137,0,11506,60,16041,0,20410,60,24777,0,29146,60,33513,0,35194,60,42249,0,45106,60,50985,0,55354,60,59889,0,64090,60,68625,0,72994,60,77361,0,81730,60,86097,0,90466,60,94833,0,99202,60,103569,0,107938,60,112473,0,116674,60,121209,0,125578,60,129945,0,134314,60,138681,0,143050,60,147417,0,151282,60,156153,0,160018,60,165057,0,168754,60,173793,0,177490,60,182529,0,186394,60,191265,0,195130,60,200001,0,203866,60,208905,0,212602,60,217641,0,221338,60,226377,0,230242,60,235113,0,238978,60,243849,0,247714,60,252585,0,256450,60,261489,0,265186,60,270225,0,273922,60,278961,0,282826,60,287697,0,291562,60,296433,0,300298,60,305337,0,309034,60,314073,0,317770,60,322809,0,326002,60,331713,0,334738,60,340449,0,343474,60,349185,0,352378,60,358089,0,361114,60,366825,0,369850,60,375561,0,378586,60,384297,0,387322,60,393033,0,396058,60,401769,0,404962,60,410673,0,413698,60,419409,0,422434,60,428145,0,431170,60,436881,0,439906,60,445617,0,448810,60,454521,0,457546,60,463257,0,466282,60,471993,0,475018,60,480729,0,483754,60,489465,0,492490,60,498201,0,501394,60,507105,0,510130,60,515841,0,518866,60,524577,0,527602,60,533313,0,536338,60,542049,0,545242,60,550953,0,553978,60,559689,0,562714,60,568425,0,571450,60,577161,0,580186,60,585897,0,588922,60,594633,0]}},"numberFormatSymbols":{"DECIMAL_SEP":".","PERMILL":"‰","MINUS_SIGN":"-","PERCENT_PATTERN":"#,##0%","INFINITY":"∞","DEF_CURRENCY_CODE":"USD","PLUS_SIGN":"+","CURRENCY_PATTERN":"¤#,##0.00","DECIMAL_PATTERN":"#,##0.###","SCIENTIFIC_PATTERN":"#E0","PERCENT":"%","EXP_SYMBOL":"E","GROUP_SEP":",","NAN":"NaN","ZERO_DIGIT":"0"},"allowTerminalDateSeparator":true,"amPmEnglishAccepted":false,"currencyPrefix":true,"currencyTag":"\"$\"","datePostsAreSuffix":true,"dateTimeWithoutYearPattern":"M/d H:mm","dateWithoutYearPattern":"M/d","dayPost":"","decimalSeparator":".","defaultDatePattern":"M/d/yyyy","defaultDateTimePattern":"M/d/yyyy H:mm:ss","defaultTimePattern":"h:mm:ss am/pm","defaultUiLanguage":"en","exponentSeparator":"E","extraDateSeparator":"","firstDayOfWeek":0,"additionalFonts":[],"additionalFormats":[{"1":5,"2":"yyyy-MM-dd","3":1},{"1":5,"2":"MM-dd-yyyy","3":1},{"1":5,"2":"M/d/yy","3":1},{"1":5,"2":"MM-dd-yy","3":1},{"1":5,"2":"M/d","3":1},{"1":5,"2":"MM-dd","3":1},{"1":5,"2":"d-MMM","3":1},{"1":5,"2":"d-MMM-yyyy","3":1},{"1":5,"2":"MMMM d, yyyy","3":1},{"1":5,"2":"MMMM d","3":1},{"1":5,"2":"MMM-d","3":1},{"1":6,"2":"h:mm:ss am/pm","3":1},{"1":6,"2":"h:mm am/pm","3":1},{"1":6,"2":"H:mm:ss","3":1},{"1":6,"2":"H:mm","3":1},{"1":7,"2":"M/d H:mm","3":1}],"amPmStrings":["AM","PM"],"amString":"AM","monthsFull":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthsShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"pmString":"PM","timePrefix":"","timeSeparator":":","weekdaysFull":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"weekdaysShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"groupingSeparator":",","hourPost":"","minimalDaysInFirstWeek":1,"minusSign":"-","minutePost":"","monthPost":"","negativeParens":true,"percent":"%","periodIsDateSeparator":false,"plusSign":"+","secondPost":"","shortDateFormatSuffix":"","yearPost":"","textInputCurrencySymbol":"$"};</script><script>var chartData = { };chartData['878046177'] = null;chartData['964845725'] = null;function initCharts() {chartData['878046177'] = {'chartId': '878046177', 'elementId': 'embed_878046177', 'chartJson': '\x7b\x22view\x22:\x7b\x22columns\x22:\x5b0,1\x5d\x7d,\x22dataTable\x22:\x7b\x22parsedNumHeaders\x22:0,\x22rows\x22:\x5b\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Defects Raised\x22\x7d,\x7b\x22v\x22:1,\x22f\x22:\x221\x22\x7d\x5d\x7d\x5d,\x22cols\x22:\x5b\x7b\x22id\x22:\x22Col0\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22string\x22\x7d,\x7b\x22pattern\x22:\x22General\x22,\x22id\x22:\x22Col1\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22number\x22\x7d\x5d\x7d,\x22chartType\x22:\x22PieChart\x22,\x22options\x22:\x7b\x22pieSliceText\x22:\x22value\x22,\x22slices\x22:\x7b\x220\x22:\x7b\x22color\x22:\x22#999999\x22\x7d\x7d,\x22legendTextStyle\x22:\x7b\x22color\x22:\x22#1a1a1a\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22legend\x22:\x22right\x22,\x22useFirstColumnAsDomain\x22:true,\x22width\x22:490,\x22is3D\x22:true,\x22title\x22:\x22Sprint 4 Testing -  Test Summary\x22,\x22titleTextStyle\x22:\x7b\x22color\x22:\x22#000000\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22height\x22:371\x7d\x7d', 'serializedChartProperties': '\x5bnull,\x5b\x221589269149\x22,\x22724980248\x22\x5d,0,0,0,null,\x5b\x22\x7b\\\x22cols\\\x22:\x5b\x7b\\\x22id\\\x22:\\\x220\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22string\\\x22\x7d,\x7b\\\x22id\\\x22:\\\x221\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22number\\\x22\x7d\x5d,\\\x22rows\\\x22:\x5b\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Defects Raised\\\x22\x7d,\x7b\\\x22v\\\x22:1.0,\\\x22f\\\x22:\\\x221\\\x22\x7d\x5d\x7d\x5d,\\\x22parsedNumHeaders\\\x22:0\x7d\x22,\x220.6\x22\x5d,0,null,\x5bnull,null,13,null,\x5b\x5b\x22Sprint 4 Testing -  Test Summary\x22,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-16777216\x5d,null,null,0\x5d\x5d\x5d,\x5b1\x5d,null,\x5b4,null,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-15066598\x5d,null,null,0\x5d\x5d,\x5bnull,2,null,null,\x5b\x5b0,\x5bnull,null,\x5b-6710887\x5d\x5d\x5d\x5d,\x5b45.57\x5d\x5d\x5d\x5d', 'fallbackUri': '\/spreadsheets\/d\/1YEgUST0mDphlidLKpxbPuBJ6P4xP5wPQ\/embed\/oimg?newCharts\x3dfalse\x26oid\x3d878046177', 'chart': null, 'interactionState': ritz_tviz_charts.ChartsExportApi.newInteractionState()}; drawRitzChart( chartData['878046177'], 'en_US','\x7b\x22enableHighPrecisionTrendLines\x22:true,\x22enableOverflowLegendHover\x22:false,\x22enableChartWebFonts\x22:false\x7d', 490.0 , 371.0 ,'\x5b6,\x22Calibri\x22,\x5b\x5b1,\x5b2,0\x5d\x5d,\x5b2,\x5b2,16777215\x5d\x5d,\x5b3,\x5b2,5210557\x5d\x5d,\x5b4,\x5b2,12603469\x5d\x5d,\x5b5,\x5b2,10206041\x5d\x5d,\x5b6,\x5b2,8414370\x5d\x5d,\x5b7,\x5b2,4959430\x5d\x5d,\x5b8,\x5b2,16225862\x5d\x5d,\x5b9,\x5b2,255\x5d\x5d\x5d\x5d', true );chartData['964845725'] = {'chartId': '964845725', 'elementId': 'embed_964845725', 'chartJson': '\x7b\x22view\x22:\x7b\x22columns\x22:\x5b0,1\x5d\x7d,\x22dataTable\x22:\x7b\x22parsedNumHeaders\x22:0,\x22rows\x22:\x5b\x7b\x22c\x22:\x5b\x7b\x22v\x22:\x22Defects Raised\x22\x7d,\x7b\x22v\x22:0,\x22f\x22:\x220\x22\x7d\x5d\x7d\x5d,\x22cols\x22:\x5b\x7b\x22id\x22:\x22Col0\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22string\x22\x7d,\x7b\x22pattern\x22:\x22General\x22,\x22id\x22:\x22Col1\x22,\x22label\x22:\x22\x22,\x22type\x22:\x22number\x22\x7d\x5d\x7d,\x22chartType\x22:\x22PieChart\x22,\x22options\x22:\x7b\x22pieSliceText\x22:\x22value\x22,\x22slices\x22:\x7b\x220\x22:\x7b\x22color\x22:\x22#999999\x22\x7d\x7d,\x22legendTextStyle\x22:\x7b\x22color\x22:\x22#1a1a1a\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22legend\x22:\x22right\x22,\x22useFirstColumnAsDomain\x22:true,\x22width\x22:512,\x22is3D\x22:true,\x22title\x22:\x22Sprint 4 API Testing - Test Summary\x22,\x22titleTextStyle\x22:\x7b\x22color\x22:\x22#000000\x22,\x22bold\x22:false,\x22alignment\x22:\x22center\x22,\x22italic\x22:false,\x22schemeFontIndex\x22:0\x7d,\x22height\x22:371\x7d\x7d', 'serializedChartProperties': '\x5bnull,\x5b\x2234970473\x22,\x221761114894\x22\x5d,0,0,0,null,\x5b\x22\x7b\\\x22cols\\\x22:\x5b\x7b\\\x22id\\\x22:\\\x220\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22string\\\x22\x7d,\x7b\\\x22id\\\x22:\\\x221\\\x22,\\\x22label\\\x22:\\\x22\\\x22,\\\x22type\\\x22:\\\x22number\\\x22\x7d\x5d,\\\x22rows\\\x22:\x5b\x7b\\\x22c\\\x22:\x5b\x7b\\\x22v\\\x22:\\\x22Defects Raised\\\x22\x7d,\x7b\\\x22v\\\x22:0.0,\\\x22f\\\x22:\\\x220\\\x22\x7d\x5d\x7d\x5d,\\\x22parsedNumHeaders\\\x22:0\x7d\x22,\x220.6\x22\x5d,0,null,\x5bnull,null,13,null,\x5b\x5b\x22Sprint 4 API Testing - Test Summary\x22,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-16777216\x5d,null,null,0\x5d\x5d\x5d,\x5b1\x5d,null,\x5b4,null,\x5bnull,null,null,null,null,0,0,null,null,null,1,\x5b-15066598\x5d,null,null,0\x5d\x5d,\x5bnull,2,null,null,\x5b\x5b0,\x5bnull,null,\x5b-6710887\x5d\x5d\x5d\x5d,\x5b45.57\x5d\x5d\x5d\x5d', 'fallbackUri': '\/spreadsheets\/d\/1YEgUST0mDphlidLKpxbPuBJ6P4xP5wPQ\/embed\/oimg?newCharts\x3dfalse\x26oid\x3d964845725', 'chart': null, 'interactionState': ritz_tviz_charts.ChartsExportApi.newInteractionState()}; drawRitzChart( chartData['964845725'], 'en_US','\x7b\x22enableHighPrecisionTrendLines\x22:true,\x22enableOverflowLegendHover\x22:false,\x22enableChartWebFonts\x22:false\x7d', 512.0 , 371.0 ,'\x5b6,\x22Calibri\x22,\x5b\x5b1,\x5b2,0\x5d\x5d,\x5b2,\x5b2,16777215\x5d\x5d,\x5b3,\x5b2,5210557\x5d\x5d,\x5b4,\x5b2,12603469\x5d\x5d,\x5b5,\x5b2,10206041\x5d\x5d,\x5b6,\x5b2,8414370\x5d\x5d,\x5b7,\x5b2,4959430\x5d\x5d,\x5b8,\x5b2,16225862\x5d\x5d,\x5b9,\x5b2,255\x5d\x5d\x5d\x5d', true );}function drawRitzChart( chartState, spreadsheetLocale, serializedChartFlags, width, height, serializedWorkbookTheme, enableStandaloneCharts) {if (enableStandaloneCharts) {drawChartComponent(chartState, spreadsheetLocale, serializedChartFlags, width, height, serializedWorkbookTheme); return;}var canvas = document.createElement('canvas'); var chartElement = document.getElementById(chartState.elementId); width = width == 0 ? window.innerWidth : width; height = height == 0 ? window.innerHeight : height; canvas.width = width; canvas.height = height; canvas.id = 'chart_' + chartState.chartId; chartState.chart = ritz_tviz_charts.ChartsExportApi.buildAndLayoutChartFromGvizWrapper( canvas.getContext('2d'), chartState.chartJson, width, height, spreadsheetLocale, serializedChartFlags); if (chartState.chart && ritz_tviz_charts.ChartsExportApi.renderChart( canvas.getContext('2d'), chartState.chart, chartState.interactionState)) {canvas.addEventListener('click', function(e) {handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale);}); canvas.addEventListener('mousemove', function(e) {handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale);}); canvas.addEventListener('mouseleave', function(e) {handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale);}); chartElement.appendChild(canvas);} else {var img = document.createElement('img'); img.setAttribute('src', chartState.fallbackUri); img.setAttribute('width', width); img.setAttribute('height', height); img.id = 'chart_' + chartState.chartId; chartElement.appendChild(img);}chartState.tooltipRenderer = new docs.charts.Tooltip(new docs.zoom.Zoom(), chartElement);}function drawChartComponent(chartState, spreadsheetLocale, serializedChartFlags, width, height, serializedWorkbookTheme) {var chartElement = document.getElementById(chartState.elementId); width = width == 0 ? window.innerWidth : width; height = height == 0 ? window.innerHeight : height; chartState.chartComponent = new waffle.charts.export.RitzExportChartComponent( spreadsheetLocale, serializedChartFlags, new docs.zoom.Zoom()); chartState.chartComponent.render(chartElement); chartState.chartComponent.setSizeFromPrimitives(width, height); chartState.chartComponent.updateFromModel( ritz_tviz_charts.ChartsExportApi.createChartFromChartProperties( chartState.serializedChartProperties, serializedWorkbookTheme));}function layoutChart(chartState, canvas) {ritz_tviz_charts.ChartsExportApi.layoutChart( canvas.getContext('2d'), chartState.chart, chartState.interactionState, canvas.width, canvas.height);}function renderChart(chartState, canvas) {ritz_tviz_charts.ChartsExportApi.renderChart( canvas.getContext('2d'), chartState.chart, chartState.interactionState);}function handleMouseEvent(e, canvas, chartState, serializedChartFlags, spreadsheetLocale) {var x = e.clientX - canvas.getBoundingClientRect().left; var y = e.clientY - canvas.getBoundingClientRect().top; var oldState = chartState.interactionState; chartState.interactionState = (e.type == 'click') ? ritz_tviz_charts.ChartsExportApi.onClick(chartState.chart, oldState, x, y) : ritz_tviz_charts.ChartsExportApi.onMouseMove(chartState.chart, oldState, x, y); if(ritz_tviz_charts.ChartsExportApi.isLayoutNeeded( chartState.chart, oldState, chartState.interactionState)) {layoutChart(chartState, canvas);}renderChart(chartState, canvas); if (chartState.tooltipRenderer) {if (e.type == 'mouseleave') {chartState.tooltipRenderer.hide();} else {ritz_tviz_charts.ChartsExportApi.refreshTooltip( chartState.chart, chartState.tooltipRenderer, chartState.interactionState, serializedChartFlags, spreadsheetLocale);}}}function onChartsExportApiLoad() {initCharts();}window['__serializedchartmessages'] = {"p":{"CALL_CHART_REQUIRES_MINIMUM_COLUMNS":2,"CALL_CHART_PIE_SLICE":2,"CALL_CHART_ORG_CHART_EDGE":2,"MSG_CHART_LEGEND_MORE":0,"CALL_CHART_CHART_SUBTITLE":1,"CALL_CHART_ERROR_BARS":1,"MSG_CHART_HISTOGRAM_COUNT":0,"MSG_CHART_NO_DATA_DISPLAY":0,"CALL_CHART_PIE_SLICE_LABEL":1,"CALL_CHART_ERROR_BAR_ITEM":2,"CALL_CHART_STEPPED_LINE_SERIES":1,"CALL_CHART_TREE_MAP_MISSING_PARENT":1,"CALL_CHART_POINT_SERIES":1,"MSG_CHART_HISTOGRAM_INVALID_BUCKET_SIZE":0,"MSG_CHART_TABLE_CHART_CONTENT":0,"MSG_CHART_WATERFALL_CHART_TOTAL_UNLABELED":0,"CALL_CHART_WATERFALL_CHART_POSITIVE_LABELED":1,"CALL_CHART_LINE_SERIES":1,"CALL_CHART_CURVED_TREND_LINE":1,"CALL_CHART_CANDLESTICK_SERIES_ITEM":2,"MSG_CHART_TREE_MAP_MULTIPLE_ROOTS":0,"CALL_CHART_BAR_SERIES_ITEM":2,"MSG_CHART_PIE_CHART":0,"CALL_CHART_RIGHT_VERTICAL_AXIS_TITLE":1,"MSG_CHART_ORG_CHART_CONTENT":0,"MSG_CHART_GAUGE_CHART_CONTENT":0,"MSG_CHART_GRID_CHART_CONTENT":0,"MSG_CHART_LEGEND":0,"CALL_CHART_EMPHASIZED_PIE_SLICE":2,"MSG_CHART_PIE_CHART_CONTENT":0,"CALL_CHART_ANNOTATION_SERIES_ITEM":2,"CALL_CHART_GAUGE_SERIES_ITEM":1,"CALL_CHART_POINT_SERIES_ITEM":2,"CALL_CHART_AREA_SERIES":1,"CALL_CHART_COLUMN_MUST_BE_TEXT":1,"CALL_CHART_ORG_CHART_NODE":1,"CALL_CHART_TOTAL_DATA_LABEL_ITEM":1,"MSG_CHART_RIGHT_VERTICAL_AXIS_TICK_MARKS":0,"CALL_CHART_COLUMN_SERIES_ITEM":2,"MSG_CHART_HORIZONTAL_TICK_LABELS":0,"CALL_CHART_COLUMN_MUST_BE_NUMERIC":1,"CALL_CHART_LEGEND_ITEM":2,"CALL_CHART_HISTOGRAM_COUNT_LABEL":1,"MSG_CHART_WATERFALL_CHART_POSITIVE_UNLABELED":0,"MSG_CHART_LOADING_DISPLAY":0,"CALL_CHART_LINEAR_TREND_LINE":1,"MSG_CHART_HISTOGRAM_INVALID_BUCKET_PERCENTILE":0,"CALL_CHART_WATERFALL_CHART_TOTAL_LABELED":1,"CALL_CHART_COLUMN_SERIES":1,"MSG_CHART_LEFT_VERTICAL_AXIS_TICK_MARKS":0,"CALL_CHART_CANDLESTICK_SERIES":1,"MSG_CHART_GEO_CHART_CONTENT":0,"CALL_CHART_WATERFALL_CHART_NEGATIVE_LABELED":1,"MSG_CHART_INTERNAL_ERROR":0,"CALL_CHART_ANNOTATION_SERIES":1,"MSG_CHART_BOTTOM_HORIZONTAL_AXIS_TICK_MARKS":0,"MSG_CHART_WATERFALL_CHART_NEGATIVE_UNLABELED":0,"CALL_CHART_STEPPED_AREA_SERIES":1,"CALL_CHART_BAR_SERIES":1,"MSG_CHART_TREE_MAP_CHART_CONTENT":0,"MSG_CHART_VERTICAL_TICK_LABELS":0,"CALL_CHART_CHART_TITLE":1,"MSG_CHART_HORIZONTAL_MAJOR_GRIDLINES":0,"CALL_CHART_LEGEND_MORE_ENTRIES":1,"CALL_CHART_LEFT_VERTICAL_AXIS_TITLE":1,"CALL_CHART_TREE_MAP_DUPLICATE_NODE":1,"MSG_CHART_SCORECARD_CHART_CONTENT":0,"MSG_CHART_CHART_AREA":0,"MSG_CHART_VERTICAL_MAJOR_GRIDLINES":0,"CALL_CHART_HORIZONTAL_AXIS_TITLE":1},"m":{"CALL_CHART_REQUIRES_MINIMUM_COLUMNS":"Requires at least {0} column(s) but only {1} provided.","CALL_CHART_PIE_SLICE":"Pie slice {0}, {1} percent.","CALL_CHART_ORG_CHART_EDGE":"Edge from parent {0} to child {1}.","MSG_CHART_LEGEND_MORE":"Legend more","CALL_CHART_CHART_SUBTITLE":"Subtitle: {0}","CALL_CHART_ERROR_BARS":"Error bars for series {0}","MSG_CHART_HISTOGRAM_COUNT":"(count)","MSG_CHART_NO_DATA_DISPLAY":"Add a series to start visualizing your data","CALL_CHART_PIE_SLICE_LABEL":"Label for pie slice {0}","CALL_CHART_ERROR_BAR_ITEM":"Error bar for series {0} item {1}","CALL_CHART_STEPPED_LINE_SERIES":"Stepped line series {0}","CALL_CHART_TREE_MAP_MISSING_PARENT":"Couldn\u0027t find parent row with label: {0}","CALL_CHART_POINT_SERIES":"Point series {0}","MSG_CHART_HISTOGRAM_INVALID_BUCKET_SIZE":"Bucket size is invalid. It must be greater than zero.","MSG_CHART_TABLE_CHART_CONTENT":"Table chart content","MSG_CHART_WATERFALL_CHART_TOTAL_UNLABELED":"Subtotal","CALL_CHART_WATERFALL_CHART_POSITIVE_LABELED":"Positive ({0})","CALL_CHART_LINE_SERIES":"Line series {0}","CALL_CHART_CURVED_TREND_LINE":"Curved trendline for series {0}","CALL_CHART_CANDLESTICK_SERIES_ITEM":"Candlestick series {0} item {1}","MSG_CHART_TREE_MAP_MULTIPLE_ROOTS":"Found two root nodes. Only one root node is allowed.","CALL_CHART_BAR_SERIES_ITEM":"Bar series {0} item {1}","MSG_CHART_PIE_CHART":"Pie chart","CALL_CHART_RIGHT_VERTICAL_AXIS_TITLE":"Right vertical axis title: {0}","MSG_CHART_ORG_CHART_CONTENT":"Org chart content","MSG_CHART_GAUGE_CHART_CONTENT":"Gauge chart content","MSG_CHART_GRID_CHART_CONTENT":"Grid chart content","MSG_CHART_LEGEND":"Legend","CALL_CHART_EMPHASIZED_PIE_SLICE":"Emphasized pie slice {0}, {1} percent.","MSG_CHART_PIE_CHART_CONTENT":"Pie chart content","CALL_CHART_ANNOTATION_SERIES_ITEM":"Annotation for series {0} item {1}","CALL_CHART_GAUGE_SERIES_ITEM":"Gauge series item {0}","CALL_CHART_POINT_SERIES_ITEM":"Points series {0} item {1}","CALL_CHART_AREA_SERIES":"Area series {0}","CALL_CHART_COLUMN_MUST_BE_TEXT":"Column {0} must be text.","CALL_CHART_ORG_CHART_NODE":"Node {0}","CALL_CHART_TOTAL_DATA_LABEL_ITEM":"Total data label item {0}.","MSG_CHART_RIGHT_VERTICAL_AXIS_TICK_MARKS":"Right vertical axis tick marks","CALL_CHART_COLUMN_SERIES_ITEM":"Column series {0} item {1}","MSG_CHART_HORIZONTAL_TICK_LABELS":"Horizontal tick labels","CALL_CHART_COLUMN_MUST_BE_NUMERIC":"Column {0} must be numeric.","CALL_CHART_LEGEND_ITEM":"Legend entry {0}: {1}","CALL_CHART_HISTOGRAM_COUNT_LABEL":"{0} (count)","MSG_CHART_WATERFALL_CHART_POSITIVE_UNLABELED":"Positive","MSG_CHART_LOADING_DISPLAY":"Loading…","CALL_CHART_LINEAR_TREND_LINE":"Linear trendline for series {0}","MSG_CHART_HISTOGRAM_INVALID_BUCKET_PERCENTILE":"Bucket percentile is invalid. It must be between zero and one hundred percent.","CALL_CHART_WATERFALL_CHART_TOTAL_LABELED":"Subtotal ({0})","CALL_CHART_COLUMN_SERIES":"Column series {0}","MSG_CHART_LEFT_VERTICAL_AXIS_TICK_MARKS":"Left vertical axis tick marks","CALL_CHART_CANDLESTICK_SERIES":"Candlestick series {0}","MSG_CHART_GEO_CHART_CONTENT":"Geo chart content","CALL_CHART_WATERFALL_CHART_NEGATIVE_LABELED":"Negative ({0})","MSG_CHART_INTERNAL_ERROR":"An internal error has occurred while rendering the chart.","CALL_CHART_ANNOTATION_SERIES":"Annotations for series {0}","MSG_CHART_BOTTOM_HORIZONTAL_AXIS_TICK_MARKS":"Bottom horizontal axis tick marks","MSG_CHART_WATERFALL_CHART_NEGATIVE_UNLABELED":"Negative","CALL_CHART_STEPPED_AREA_SERIES":"Stepped area series {0}","CALL_CHART_BAR_SERIES":"Bar series {0}","MSG_CHART_TREE_MAP_CHART_CONTENT":"Treemap chart content","MSG_CHART_VERTICAL_TICK_LABELS":"Vertical tick labels","CALL_CHART_CHART_TITLE":"Title: {0}","MSG_CHART_HORIZONTAL_MAJOR_GRIDLINES":"Horizontal major gridlines","CALL_CHART_LEGEND_MORE_ENTRIES":"{0} more","CALL_CHART_LEFT_VERTICAL_AXIS_TITLE":"Left vertical axis title: {0}","CALL_CHART_TREE_MAP_DUPLICATE_NODE":"Found two entries with the same label: {0}","MSG_CHART_SCORECARD_CHART_CONTENT":"Scorecard chart content","MSG_CHART_CHART_AREA":"Chart area","MSG_CHART_VERTICAL_MAJOR_GRIDLINES":"Vertical major gridlines","CALL_CHART_HORIZONTAL_AXIS_TITLE":"Horizontal axis title: {0}"}};</script><script>var CHARTS_EXPORT_URI = [];CHARTS_EXPORT_URI.push('resources\/404429202-ChartsExportJ2cl_j2cl_core.js');
    if (window.addEventListener) {
      window.addEventListener('load',
        function() {
          window.tvizScriptLoader.load();
        });
    }
    </script>