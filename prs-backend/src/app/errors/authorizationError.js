const AppError = require('./appError');

/**
 * Error class for authorization errors
 */
class AuthorizationError extends AppError {
  /**
   * Creates a new AuthorizationError
   * 
   * @param {string} message - Error message
   * @param {string} permission - Permission that was required
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(
    message = 'You do not have permission to perform this action',
    permission = null,
    originalError = null
  ) {
    super(message, 'AUTHORIZATION_ERROR', { permission }, originalError);
    this.permission = permission;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 403; // Forbidden
  }
}

module.exports = AuthorizationError;
