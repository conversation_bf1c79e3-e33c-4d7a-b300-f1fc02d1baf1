/**
 * Constants for the Example module
 */

const EXAMPLE_STATUS = Object.freeze({
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
});

const EXAMPLE_TYPES = Object.freeze({
  TYPE1: 'type1',
  TYPE2: 'type2',
  TYPE3: 'type3',
});

const EXAMPLE_PRIORITIES = Object.freeze({
  LOWEST: 1,
  LOW: 2,
  MEDIUM: 3,
  HIGH: 4,
  HIGHEST: 5,
});

const EXAMPLE_SORT_COLUMNS = Object.freeze([
  'id',
  'name',
  'description',
  'status',
  'type',
  'priority',
  'createdAt',
  'updatedAt',
]);

const EXAMPLE_FILTER_COLUMNS = Object.freeze({
  name: {
    operators: ['eq', 'like'],
    type: 'string',
  },
  status: {
    operators: ['eq'],
    type: 'enum',
    values: Object.values(EXAMPLE_STATUS),
  },
  type: {
    operators: ['eq'],
    type: 'enum',
    values: Object.values(EXAMPLE_TYPES),
  },
  priority: {
    operators: ['eq', 'gt', 'lt', 'gte', 'lte'],
    type: 'number',
  },
  createdAt: {
    operators: ['eq', 'gt', 'lt', 'gte', 'lte'],
    type: 'date',
  },
});

module.exports = {
  EXAMPLE_STATUS,
  EXAMPLE_TYPES,
  EXAMPLE_PRIORITIES,
  EXAMPLE_SORT_COLUMNS,
  EXAMPLE_FILTER_COLUMNS,
};
