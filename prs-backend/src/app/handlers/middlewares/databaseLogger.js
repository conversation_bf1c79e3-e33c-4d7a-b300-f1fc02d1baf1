/**
 * Database Logger Middleware
 * 
 * This middleware logs database queries using Sequelize hooks.
 */
const { formatDatabaseLog } = require('../../../infra/logs/logFormatter');
const { LOG_CATEGORIES } = require('../../../infra/logs/loggerService');
const { requestContext } = require('@fastify/request-context');

/**
 * Sets up database logging hooks
 * 
 * @param {Object} sequelize - Sequelize instance
 * @param {Object} logger - Logger service
 */
function setupDatabaseLogger(sequelize, logger) {
  // Log all queries
  sequelize.addHook('beforeQuery', (options) => {
    const { type, model, sql, bind } = options;
    
    // Skip logging for internal queries
    if (sql.includes('SELECT 1+1 AS result')) {
      return;
    }
    
    const modelName = model ? model.name : 'Unknown';
    const operation = determineOperation(sql);
    
    // Store start time for duration calculation
    options._startTime = Date.now();
    
    logger.debug(`DB ${operation} on ${modelName}`, {
      category: LOG_CATEGORIES.DATABASE,
      ...formatDatabaseLog({
        operation,
        model: modelName,
        query: sql,
        parameters: bind,
      }),
    });
  });
  
  // Log query completion and duration
  sequelize.addHook('afterQuery', (options) => {
    if (!options._startTime) {
      return;
    }
    
    const duration = Date.now() - options._startTime;
    const { type, model, sql } = options;
    
    // Skip logging for internal queries
    if (sql.includes('SELECT 1+1 AS result')) {
      return;
    }
    
    const modelName = model ? model.name : 'Unknown';
    const operation = determineOperation(sql);
    
    // Only log slow queries (> 100ms) at info level
    if (duration > 100) {
      logger.info(`Slow DB ${operation} on ${modelName} (${duration}ms)`, {
        category: LOG_CATEGORIES.DATABASE,
        ...formatDatabaseLog({
          operation,
          model: modelName,
          duration,
        }),
        performance: {
          slow: true,
          duration,
        },
      });
    }
  });
  
  // Log errors
  sequelize.addHook('error', (error, options) => {
    const { type, model, sql, bind } = options;
    const modelName = model ? model.name : 'Unknown';
    const operation = determineOperation(sql);
    
    logger.error(`DB ${operation} error on ${modelName}`, {
      category: LOG_CATEGORIES.DATABASE,
      ...formatDatabaseLog({
        operation,
        model: modelName,
        query: sql,
        parameters: bind,
      }),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    });
  });
}

/**
 * Determines the database operation from SQL
 * 
 * @param {string} sql - SQL query
 * @returns {string} - Operation name
 */
function determineOperation(sql) {
  const normalizedSql = sql.trim().toUpperCase();
  
  if (normalizedSql.startsWith('SELECT')) {
    return 'SELECT';
  } else if (normalizedSql.startsWith('INSERT')) {
    return 'INSERT';
  } else if (normalizedSql.startsWith('UPDATE')) {
    return 'UPDATE';
  } else if (normalizedSql.startsWith('DELETE')) {
    return 'DELETE';
  } else if (normalizedSql.includes('CREATE TABLE')) {
    return 'CREATE';
  } else if (normalizedSql.includes('ALTER TABLE')) {
    return 'ALTER';
  } else if (normalizedSql.includes('DROP TABLE')) {
    return 'DROP';
  }
  
  return 'QUERY';
}

module.exports = setupDatabaseLogger;
