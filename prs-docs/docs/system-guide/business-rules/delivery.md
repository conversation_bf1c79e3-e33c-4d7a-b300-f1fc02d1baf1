# Delivery Report Business Rules

This document outlines the business rules for the delivery report receiving process in the PRS system.

## Delivery Report Entity

A delivery report (DR) is a document that confirms the delivery of purchased items

### Delivery Report Status Flow

```
DRAFT → DELIVERED

```

## Business Rules

### Delivery Report Creation Rules

- Requestor and Assigned Purchasing Staff can create one or more DR
- A DR can be created as a draft or submitted immediately
- Deliveries can only be created for Purchase Orders with status FOR_DELIVERY
- DR Number is auto-generated
- DR Letter is auto-generated
- One Purchase Order can have multiple Delivery Reports
- The sum of all delivery quantities of different DRs for an item cannot exceed the ordered quantity
- Each delivery must have a delivered date
- Delivery items must reference the Purchase Order items they correspond to
- Delivery Receipt from supplier can be uploaded
- Delivery Receipt No. from supplier must be inputted
- Returns will be inputted in the notes section


### Delivery Report Rules

- The actual quantity received must be recorded
- Returned quantity will be inputted in the notes section
- Upon submission, status will be DELIVERED
- The delivery date must be recorded when a delivery is received 