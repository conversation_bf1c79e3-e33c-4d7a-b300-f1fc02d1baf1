import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/config";
import { prisma } from "@/lib/db";

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return new Response("Unauthorized", { status: 401 });
  }

  const userId = parseInt(session.user.id);

  // Set up SSE response headers
  const response = new Response(
    new ReadableStream({
      start(controller) {
        // Function to send notifications
        const sendNotifications = async () => {
          try {
            // Get new notifications for user
            const notifications = await prisma.notification.findMany({
              where: {
                userId,
                read: false
              },
              orderBy: { createdAt: "desc" },
              take: 10
            });

            // Send as SSE event
            controller.enqueue(`data: ${JSON.stringify(notifications)}\n\n`);

            // Schedule next check
            setTimeout(sendNotifications, 5000);
          } catch (error) {
            console.error("SSE error:", error);
            controller.error(error);
          }
        };

        // Start sending notifications
        sendNotifications();
      }
    }),
    {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive"
      }
    }
  );

  return response;
}
