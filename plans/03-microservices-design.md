# Cityland PRS Rebuild: Microservices Design

## Domain-Driven Service Boundaries

Based on analysis of the existing system and test cases, we've identified the following domain-driven microservices:

### Core Services

1. **Identity Service**
   - User management
   - Authentication and authorization
   - Role and permission management
   - User preferences

2. **Organization Service**
   - Company management
   - Department management
   - Project management
   - Cost center management

3. **Requisition Service**
   - Requisition slip (RS) creation and management
   - Non-requisition slip (Non-RS) management
   - Requisition item management
   - Draft management

4. **Approval Service**
   - Approval workflow configuration
   - Approval routing
   - Approval actions (approve, reject, comment)
   - Delegation management

5. **Canvass Service**
   - Canvass sheet creation
   - Supplier quotation management
   - Canvass approval workflow
   - Canvass item management

6. **Purchase Order Service**
   - PO creation and management
   - PO approval workflow
   - PO item management
   - PO status tracking

7. **Delivery Service**
   - Delivery receipt management
   - Partial delivery handling
   - Return management
   - Delivery status tracking

8. **Payment Service**
   - Payment request creation
   - Payment approval workflow
   - Invoice management
   - Payment status tracking

9. **Supplier Service**
   - Supplier management
   - Supplier synchronization
   - Supplier evaluation
   - Supplier categorization

10. **Notification Service**
    - Email notifications
    - In-app notifications
    - Notification preferences
    - Notification history

11. **Document Service**
    - File attachment management
    - Document versioning
    - Document access control
    - Document search

12. **Reporting Service**
    - Standard reports generation
    - Custom report builder
    - Export functionality
    - Report scheduling

## Service Interaction Patterns

### Synchronous Communication (REST APIs)

Used for:
- Direct user actions requiring immediate response
- Simple CRUD operations
- Data lookups and validations

Example: User creating a requisition slip

### Asynchronous Communication (Events)

Used for:
- Cross-service business processes
- Notifications and alerts
- Status updates
- Audit logging

Example: Requisition approval triggering notification

## Data Management Strategy

### Data Ownership

Each microservice owns its domain data:

- **Identity Service**: Users, roles, permissions
- **Organization Service**: Companies, departments, projects
- **Requisition Service**: Requisitions, requisition items
- etc.

### Data Duplication vs. References

- **Reference by ID**: For frequently changing data
- **Controlled Duplication**: For relatively static data needed for performance
- **Event-Based Synchronization**: For keeping duplicated data in sync

### Database Per Service

Each service has its own database to ensure:
- Independent scaling
- Data isolation
- Technology flexibility
- Failure isolation

## API Design Principles

1. **RESTful Resources**
   - Resource-oriented API design
   - Consistent URL patterns
   - Proper HTTP method usage

2. **API Versioning**
   - URL-based versioning (e.g., /v1/resources)
   - Backward compatibility considerations

3. **Standardized Responses**
   - Consistent error formats
   - Pagination standards
   - Filtering and sorting conventions

4. **Documentation**
   - OpenAPI/Swagger documentation
   - Example requests and responses
   - Authentication requirements

## Service Implementation Template

Each microservice will follow a consistent implementation pattern:

```
service-name/
├── src/
│   ├── api/              # API controllers and routes
│   ├── domain/           # Domain models and business logic
│   ├── services/         # Service implementations
│   ├── repositories/     # Data access layer
│   ├── events/           # Event publishers and subscribers
│   ├── utils/            # Utility functions
│   ├── config/           # Service configuration
│   └── server.js         # Service entry point
├── tests/                # Automated tests
├── Dockerfile            # Container definition
├── package.json          # Dependencies and scripts
└── README.md             # Service documentation
```

## Service Discovery and Communication

1. **Service Registry**
   - Dynamic registration of service instances
   - Health check integration
   - Metadata storage

2. **Client-Side Discovery**
   - Services query registry for dependencies
   - Load balancing across instances
   - Circuit breaking for fault tolerance

3. **API Gateway Integration**
   - Route registration
   - Documentation aggregation
   - Cross-cutting concerns (auth, logging)

## Event-Driven Architecture

1. **Event Types**
   - Domain Events: Represent business occurrences
   - Integration Events: Cross-service communication
   - Command Events: Trigger actions in other services

2. **Event Schema Registry**
   - Centralized event schema definition
   - Versioning of event schemas
   - Compatibility validation

3. **Event Sourcing Considerations**
   - Event store for critical business processes
   - Replay capability for auditing
   - Snapshots for performance

## Deployment Strategy

Each microservice will be:
- Packaged as a Docker container
- Deployed to Kubernetes
- Independently scalable
- Configured via environment variables and config maps
- Monitored for health and performance