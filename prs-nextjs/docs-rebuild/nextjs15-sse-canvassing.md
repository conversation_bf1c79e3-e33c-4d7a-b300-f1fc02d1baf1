# Canvassing Workflow in Next.js 15 with SSE

This document outlines the implementation of the Canvassing workflow in the Next.js 15 PRS application with Server-Sent Events (SSE) for real-time updates.

## Table of Contents

1. [Canvassing Workflow Overview](#canvassing-workflow-overview)
2. [Canvassing Implementation](#canvassing-implementation)
3. [Canvassing Approval Process](#canvassing-approval-process)
4. [Real-Time Updates with SSE](#real-time-updates-with-sse)

## Canvassing Workflow Overview

The Canvassing workflow is a critical step in the procurement process that follows the Requisition Approval:

```
Approval Process → Canvassing → Canvassing Approval → Purchase Order
```

Canvassing involves selecting suppliers and comparing their prices for the items in a requisition. This process ensures that the organization gets the best value for its purchases.

## Canvassing Implementation

### 1. Canvassing Creation

```typescript
// app/canvassing/actions.ts
'use server'

import { revalidatePath } from 'next/cache'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth/config'
import { notifyUsers } from '@/lib/notifications'
import { CanvassSchema } from '@/models/canvass'

export async function createCanvass(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session) throw new Error('Unauthorized')

  const userId = parseInt(session.user.id)

  // Parse and validate form data
  const rawData = Object.fromEntries(formData.entries())
  const validatedData = CanvassSchema.parse(rawData)

  // Get the requisition
  const requisition = await prisma.requisition.findUnique({
    where: { id: validatedData.requisitionId },
    include: { items: true }
  })

  if (!requisition) throw new Error('Requisition not found')

  // Create canvass
  const canvass = await prisma.canvass.create({
    data: {
      requisitionId: requisition.id,
      status: validatedData.isDraft ? 'draft' : 'pending_approval',
      isDraft: validatedData.isDraft,
      canvassNumber: validatedData.isDraft ? null : `CNV-${Date.now()}`,
      draftCanvassNumber: validatedData.isDraft ? `DRAFT-CNV-${Date.now()}` : null,
      createdById: userId,
      note: validatedData.note
    }
  })

  // Create canvass items and suppliers
  if (validatedData.items && validatedData.items.length > 0) {
    for (const item of validatedData.items) {
      // Create canvass item
      const canvassItem = await prisma.canvassItem.create({
        data: {
          canvassId: canvass.id,
          requisitionItemId: item.requisitionItemId,
          quantity: item.quantity,
          selectedSupplierId: item.selectedSupplierId || null
        }
      })

      // Create canvass item suppliers
      if (item.suppliers && item.suppliers.length > 0) {
        await prisma.canvassItemSupplier.createMany({
          data: item.suppliers.map(supplier => ({
            canvassItemId: canvassItem.id,
            supplierId: supplier.supplierId,
            unitPrice: supplier.unitPrice,
            amount: supplier.unitPrice * item.quantity,
            remarks: supplier.remarks
          }))
        })
      }
    }
  }

  // If not draft, update requisition status and create approvals
  if (!validatedData.isDraft) {
    await prisma.requisition.update({
      where: { id: requisition.id },
      data: { status: 'canvass_for_approval' }
    })

    // Get approvers for canvass based on category and department
    const approvers = await getCanvassApprovers(requisition.departmentId, requisition.category)

    // Create approval records
    await prisma.approval.createMany({
      data: approvers.map((approver, index) => ({
        canvassId: canvass.id,
        userId: approver.userId,
        level: index + 1,
        status: 'pending'
      }))
    })

    // Notify approvers
    await notifyUsers(
      approvers.map(a => a.userId),
      {
        type: 'CANVASS_APPROVAL_REQUESTED',
        title: 'Canvass Approval Requested',
        message: `A canvass (${canvass.canvassNumber}) requires your approval`,
        referenceId: canvass.id,
        referenceType: 'canvass'
      }
    )
  }

  // Revalidate paths
  revalidatePath('/canvassing')
  revalidatePath(`/canvassing/${canvass.id}`)
  revalidatePath(`/requisitions/${requisition.id}`)

  return { success: true, id: canvass.id }
}

// Helper function to get canvass approvers
async function getCanvassApprovers(departmentId: number, category: string) {
  // Get purchasing head
  const purchasingHead = await prisma.role.findFirst({
    where: { name: 'Purchasing Head' },
    include: { users: true }
  })

  // Get department head
  const departmentHead = await prisma.departmentHead.findUnique({
    where: { departmentId }
  })

  // Get additional approvers based on category if needed
  let categoryApprovers = []
  if (category === 'project') {
    // For project category, we might need project manager approval
    const projectApprovers = await prisma.projectApproval.findMany({
      where: { projectId: requisition.projectId },
      orderBy: { level: 'asc' }
    })
    categoryApprovers = projectApprovers.map(approver => ({
      userId: approver.userId,
      level: approver.level
    }))
  }

  // Combine approvers
  const allApprovers = [
    { userId: departmentHead.userId, level: 1 },
    ...categoryApprovers,
    ...purchasingHead.users.map(user => ({
      userId: user.id,
      level: categoryApprovers.length > 0 ? 3 : 2
    }))
  ]

  return allApprovers
}
```

### 2. Canvassing Form Component

```tsx
// components/canvassing/CanvassForm.tsx
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createCanvass } from '@/app/canvassing/actions'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { CanvassItemForm } from './CanvassItemForm'
import { useGetSuppliers } from '@/app/suppliers/api'

export function CanvassForm({ requisition }) {
  const router = useRouter()
  const [items, setItems] = useState([])
  const [isDraft, setIsDraft] = useState(true)
  const [note, setNote] = useState('')

  // Fetch suppliers
  const { data: suppliers } = useGetSuppliers()

  // Initialize items from requisition
  useEffect(() => {
    if (requisition && requisition.items) {
      setItems(
        requisition.items.map(item => ({
          requisitionItemId: item.id,
          quantity: item.quantity,
          selectedSupplierId: null,
          suppliers: []
        }))
      )
    }
  }, [requisition])

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Create form data
    const formData = new FormData()
    formData.append('requisitionId', requisition.id.toString())
    formData.append('isDraft', isDraft.toString())
    formData.append('note', note)
    formData.append('items', JSON.stringify(items))

    // Submit form
    const result = await createCanvass(formData)

    if (result.success) {
      router.push(`/canvassing/${result.id}`)
    }
  }

  const updateItem = (index, updatedItem) => {
    setItems(prev => {
      const newItems = [...prev]
      newItems[index] = updatedItem
      return newItems
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div>
        <h2 className="text-xl font-semibold mb-4">Canvass Items</h2>

        {items.map((item, index) => (
          <CanvassItemForm
            key={item.requisitionItemId}
            item={item}
            requisitionItem={requisition.items.find(
              ri => ri.id === item.requisitionItemId
            )}
            suppliers={suppliers || []}
            onChange={updatedItem => updateItem(index, updatedItem)}
          />
        ))}
      </div>

      <div>
        <label htmlFor="note" className="block font-medium mb-1">
          Notes
        </label>
        <Textarea
          id="note"
          value={note}
          onChange={e => setNote(e.target.value)}
          rows={3}
        />
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          variant="outline"
          onClick={() => setIsDraft(true)}
        >
          Save as Draft
        </Button>
        <Button
          type="submit"
          onClick={() => setIsDraft(false)}
        >
          Submit for Approval
        </Button>
      </div>
    </form>
  )
}
```
