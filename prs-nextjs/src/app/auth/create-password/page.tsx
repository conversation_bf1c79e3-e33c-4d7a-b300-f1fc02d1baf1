"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { Form } from "@/components/ui/Form/Form";
import { Input } from "@/components/ui/Form/Input";
import { Button } from "@/components/ui/Button/Button";
import { AuthLayout } from "@/components/layouts/AuthLayout";
import { AuthService } from "@/lib/auth/auth-service";

// Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character
const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

const registerSchema = z.object({
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      passwordRegex,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
    ),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export default function CreatePasswordPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);

  const handlePasswordUpdate = async (values: z.infer<typeof registerSchema>) => {
    setLoading(true);
    setErrorMessage("");

    try {
      const response = await AuthService.updateTemporaryPassword(values.password);

      if (!response.success) {
        setErrorMessage(response.error || "Failed to update password");
        setLoading(false);
        return;
      }

      setIsSuccess(true);

      // In a real implementation, we would handle the next step in the auth flow
      // For now, we'll redirect to the login page after a delay
      setTimeout(() => {
        router.push("/auth/login");
      }, 3000);
    } catch (error) {
      console.error("Password update error:", error);
      setErrorMessage("An unexpected error occurred");
      setLoading(false);
    }
  };

  const SuccessContent = (
    <>
      <div className="flex flex-col gap-1">
        <h2 className="text-2xl font-bold text-red-950">Password Updated</h2>
        <p className="text-sm text-gray-800">
          Your password has been successfully updated. You will be redirected to the login page.
        </p>
      </div>
    </>
  );

  const PasswordUpdateForm = (
    <>
      <div className="flex flex-col gap-1">
        <h2 className="text-2xl font-bold text-red-950">Create New Password</h2>
        <p className="text-sm text-gray-800">
          Please create a new password for your account
        </p>
      </div>

      {errorMessage && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 my-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      <Form
        schema={registerSchema}
        onSubmit={handlePasswordUpdate}
        className="mt-4"
      >
        {({ register, formState, disabled }) => (
          <div className="flex flex-col gap-4">
            <Input
              type="password"
              label="New Password"
              error={formState.errors['password']?.message}
              registration={register('password')}
              placeholder="Samplepassword!1234!"
              disabled={loading}
            />
            <Input
              type="password"
              label="Re-enter Password"
              error={formState.errors['confirmPassword']?.message}
              registration={register('confirmPassword')}
              placeholder="Samplepassword!1234!"
              disabled={loading}
            />
            <Button
              disabled={disabled || loading}
              isLoading={loading}
              className="my-2"
              type="submit"
            >
              Update Password
            </Button>
          </div>
        )}
      </Form>
    </>
  );

  return (
    <AuthLayout navigateVisible={true}>
      {isSuccess ? SuccessContent : PasswordUpdateForm}
    </AuthLayout>
  );
}
