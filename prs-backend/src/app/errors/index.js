// Base error classes
const AppError = require('./appError');
const ValidationError = require('./validationError');
const NotFoundError = require('./notFoundError');
const AuthenticationError = require('./authenticationError');
const AuthorizationError = require('./authorizationError');
const BadRequestError = require('./badRequestError');
const ConflictError = require('./conflictError');
const DatabaseError = require('./databaseError');

// Error utilities
const ErrorTranslator = require('./errorTranslator');
const ErrorFactory = require('./errorFactory');
const errorHandler = require('./errorHandler');

// Legacy error modules (for backward compatibility)
const clientErrors = require('./clientErrors');
const serverErrors = require('./serverErrors');
const userErrors = require('./userErrors');
const infraError = require('./infraError');

module.exports = {
  // New error system
  AppError,
  ValidationError,
  NotFoundError,
  AuthenticationError,
  AuthorizationError,
  BadRequestError,
  ConflictError,
  DatabaseError,
  ErrorTranslator,
  ErrorFactory,
  errorHandler,

  // Legacy error system (for backward compatibility)
  clientErrors,
  serverErrors,
  userErrors,
  infraError
};
