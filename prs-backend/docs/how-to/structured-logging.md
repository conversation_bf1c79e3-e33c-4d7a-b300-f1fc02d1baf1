# Backend Structured Logging System for PRS

This document provides a comprehensive overview of the centralized structured logging system implemented for the PRS Backend project, including integration with the PLG (Prometheus, Loki, Grafana) observability stack.

## Overview

The logging system consists of:

1. **Logger Service**: A centralized service for structured logging
2. **Log Formatters**: Utilities for consistent log formatting
3. **Log Middleware**: Middleware for request and database logging
4. **Log Transport**: Configuration for log destinations and rotation
5. **PLG Stack Integration**: Prometheus metrics, Loki log aggregation, and Grafana dashboards
6. **Correlation IDs**: Cross-service request tracking with frontend

## Logger Service

The `LoggerService` provides a consistent interface for logging throughout the application:

```javascript
const { logger } = require('../infra/logs');

// Basic logging
logger.info('User logged in', { userId: 123 });
logger.error('Failed to process payment', { orderId: 456 });

// Error logging
try {
  // Some operation
} catch (error) {
  logger.error(error, { context: 'payment-processing' });
}

// Business event logging
logger.logBusinessEvent('Order Placed', {
  orderId: 123,
  amount: 99.99,
  items: 3
});

// Database operation logging
logger.logDatabase('INSERT', 'User', {
  userId: 123,
  fields: ['name', 'email']
});
```

### Log Levels

The logger supports the following log levels (in order of increasing severity):

- **TRACE**: Detailed information for debugging
- **DEBUG**: Debugging information
- **INFO**: General information about application operation
- **WARN**: Warning conditions
- **ERROR**: Error conditions
- **FATAL**: Severe error conditions that may cause the application to terminate

### Log Categories

Logs are categorized to make filtering and analysis easier:

- **API**: API requests and responses
- **DATABASE**: Database operations
- **SECURITY**: Authentication and authorization
- **PERFORMANCE**: Performance metrics
- **BUSINESS**: Business events and operations
- **INTEGRATION**: External service interactions
- **SYSTEM**: System-level events

## Log Structure

All logs follow a consistent structure:

```json
{
  "timestamp": "2023-06-01T12:34:56.789Z",
  "level": "info",
  "message": "User logged in",
  "service": "prs-backend",
  "environment": "development",
  "requestId": "01234567-89ab-cdef-0123-456789abcdef",
  "userId": 123,
  "username": "john.doe",
  "category": "security",
  "additionalData": {
    "ip": "***********",
    "userAgent": "Mozilla/5.0..."
  }
}
```

### Common Fields

- **timestamp**: ISO 8601 timestamp
- **level**: Log level
- **message**: Log message
- **service**: Service name
- **environment**: Environment (development, production, etc.)
- **requestId**: Request ID for correlation
- **userId**: User ID (if available)
- **username**: Username (if available)
- **category**: Log category

## Request Logging

All HTTP requests and responses are automatically logged with the following information:

### Request Logs

```json
{
  "message": "Incoming request",
  "category": "api",
  "request": {
    "id": "01234567-89ab-cdef-0123-456789abcdef",
    "method": "POST",
    "url": "/api/v1/users",
    "ip": "***********",
    "userAgent": "Mozilla/5.0..."
  },
  "user": {
    "id": 123,
    "username": "john.doe",
    "role": "admin"
  }
}
```

### Response Logs

```json
{
  "message": "Outgoing response",
  "category": "api",
  "request": {
    "id": "01234567-89ab-cdef-0123-456789abcdef",
    "method": "POST",
    "url": "/api/v1/users"
  },
  "response": {
    "statusCode": 201,
    "responseTime": 123.45
  }
}
```

## Database Logging

Database operations are automatically logged with the following information:

```json
{
  "message": "DB INSERT on User",
  "category": "database",
  "database": {
    "operation": "INSERT",
    "model": "User",
    "query": "INSERT INTO users (name, email) VALUES ($1, $2)",
    "parameters": ["John Doe", "<EMAIL>"]
  }
}
```

Slow queries (> 100ms) are logged at the INFO level with additional performance information:

```json
{
  "message": "Slow DB SELECT on User (150ms)",
  "category": "database",
  "database": {
    "operation": "SELECT",
    "model": "User",
    "duration": 150
  },
  "performance": {
    "slow": true,
    "duration": 150
  }
}
```

## Error Logging

Errors are logged with detailed information:

```json
{
  "message": "Failed to process payment",
  "category": "business",
  "error": {
    "name": "PaymentError",
    "message": "Payment gateway rejected the transaction",
    "stack": "PaymentError: Payment gateway rejected...",
    "code": "PAYMENT_REJECTED"
  },
  "order": {
    "id": 123,
    "amount": 99.99
  }
}
```

## Log Configuration

The logging system is configured based on the environment:

### Production

- Logs are written to rotating files
- Log level is set to INFO
- Files are rotated based on size (50MB) and time (daily)
- Old logs are compressed and retained for 14 days

### Development

- Logs are written to both console and files
- Console logs are pretty-printed with colors
- File logs include all levels (DEBUG and above)
- Files are rotated based on size (10MB) and time (daily)

### Local

- Logs are written to console only
- Console logs are pretty-printed with colors
- Log level is set to DEBUG

## PRS-Specific Logging Examples

### Requisition Workflow Logging

```javascript
// In requisition service
class RequisitionService {
  async createRequisition(requisitionData, userId) {
    const correlationId = this.generateCorrelationId();

    logger.info('Creating requisition', {
      category: LOG_CATEGORIES.BUSINESS,
      correlationId,
      user: { id: userId },
      requisition: {
        department: requisitionData.departmentId,
        project: requisitionData.projectId,
        itemCount: requisitionData.items?.length,
        totalAmount: requisitionData.totalAmount,
      },
    });

    try {
      const requisition = await this.requisitionRepository.create(requisitionData);

      // Log business event
      logger.logBusinessEvent('requisition_created', {
        requisitionId: requisition.id,
        userId,
        department: requisitionData.departmentId,
        amount: requisitionData.totalAmount,
      });

      // Log for audit trail
      logger.info('Requisition created successfully', {
        category: LOG_CATEGORIES.BUSINESS,
        correlationId,
        requisition: {
          id: requisition.id,
          status: requisition.status,
          createdBy: userId,
        },
      });

      return requisition;
    } catch (error) {
      logger.error('Failed to create requisition', error, {
        category: LOG_CATEGORIES.BUSINESS,
        correlationId,
        component: 'RequisitionService',
        user: { id: userId },
        requisitionData: this.sanitizeRequisitionData(requisitionData),
      });
      throw error;
    }
  }

  async approveRequisition(requisitionId, approverId, comments) {
    const correlationId = this.generateCorrelationId();

    logger.info('Approving requisition', {
      category: LOG_CATEGORIES.BUSINESS,
      correlationId,
      requisition: { id: requisitionId },
      approver: { id: approverId },
      comments: comments ? '[PROVIDED]' : '[NONE]',
    });

    try {
      const requisition = await this.requisitionRepository.findById(requisitionId);

      if (!requisition) {
        logger.warn('Attempted to approve non-existent requisition', {
          category: LOG_CATEGORIES.BUSINESS,
          correlationId,
          requisition: { id: requisitionId },
          approver: { id: approverId },
        });
        throw new NotFoundError('Requisition not found');
      }

      // Check approval permissions
      const canApprove = await this.checkApprovalPermissions(requisition, approverId);
      if (!canApprove) {
        logger.warn('Unauthorized approval attempt', {
          category: LOG_CATEGORIES.SECURITY,
          correlationId,
          requisition: { id: requisitionId, status: requisition.status },
          approver: { id: approverId },
        });
        throw new ForbiddenError('Insufficient permissions to approve');
      }

      const updatedRequisition = await this.requisitionRepository.approve(
        requisitionId,
        approverId,
        comments
      );

      // Log business event
      logger.logBusinessEvent('requisition_approved', {
        requisitionId,
        approverId,
        previousStatus: requisition.status,
        newStatus: updatedRequisition.status,
        amount: requisition.totalAmount,
      });

      return updatedRequisition;
    } catch (error) {
      logger.error('Failed to approve requisition', error, {
        category: LOG_CATEGORIES.BUSINESS,
        correlationId,
        component: 'RequisitionService',
        requisition: { id: requisitionId },
        approver: { id: approverId },
      });
      throw error;
    }
  }
}
```

### Authentication and Security Logging

```javascript
// In authentication service
class AuthService {
  async login(email, password, ipAddress, userAgent) {
    const correlationId = this.generateCorrelationId();

    logger.info('Login attempt', {
      category: LOG_CATEGORIES.SECURITY,
      correlationId,
      user: { email },
      request: {
        ip: ipAddress,
        userAgent: this.sanitizeUserAgent(userAgent),
      },
    });

    try {
      const user = await this.userRepository.findByEmail(email);

      if (!user) {
        logger.warn('Login attempt with non-existent email', {
          category: LOG_CATEGORIES.SECURITY,
          correlationId,
          user: { email },
          request: { ip: ipAddress },
        });
        throw new AuthenticationError('Invalid credentials');
      }

      const isValidPassword = await this.validatePassword(password, user.passwordHash);

      if (!isValidPassword) {
        logger.warn('Login attempt with invalid password', {
          category: LOG_CATEGORIES.SECURITY,
          correlationId,
          user: { id: user.id, email },
          request: { ip: ipAddress },
        });

        // Track failed attempts
        await this.trackFailedLogin(user.id, ipAddress);
        throw new AuthenticationError('Invalid credentials');
      }

      // Check if account is locked
      if (user.isLocked) {
        logger.warn('Login attempt on locked account', {
          category: LOG_CATEGORIES.SECURITY,
          correlationId,
          user: { id: user.id, email },
          request: { ip: ipAddress },
        });
        throw new AuthenticationError('Account is locked');
      }

      const token = await this.generateToken(user);

      // Log successful login
      logger.info('User logged in successfully', {
        category: LOG_CATEGORIES.SECURITY,
        correlationId,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        request: { ip: ipAddress },
      });

      // Log business event
      logger.logBusinessEvent('user_login', {
        userId: user.id,
        email: user.email,
        role: user.role,
        ipAddress,
      });

      return { token, user: this.sanitizeUser(user) };
    } catch (error) {
      logger.error('Login failed', error, {
        category: LOG_CATEGORIES.SECURITY,
        correlationId,
        component: 'AuthService',
        user: { email },
        request: { ip: ipAddress },
      });
      throw error;
    }
  }
}
```

### Database Operation Logging

```javascript
// Enhanced database middleware
const databaseLogger = (sequelize) => {
  sequelize.addHook('beforeQuery', (options) => {
    options.startTime = Date.now();
    options.correlationId = options.correlationId || this.generateCorrelationId();
  });

  sequelize.addHook('afterQuery', (options, query) => {
    const duration = Date.now() - options.startTime;
    const operation = this.determineOperation(query.sql);
    const model = options.model?.name || 'Unknown';

    logger.logDatabaseOperation(operation, model, duration, query.sql, query.bind);
  });

  sequelize.addHook('error', (error, options) => {
    const duration = Date.now() - options.startTime;
    const operation = this.determineOperation(options.sql);
    const model = options.model?.name || 'Unknown';

    logger.error(`Database ${operation} error on ${model}`, error, {
      category: LOG_CATEGORIES.DATABASE,
      correlationId: options.correlationId,
      database: {
        operation,
        model,
        duration,
        query: logger.sanitizeQuery(options.sql),
        parameters: logger.sanitizeParameters(options.bind),
      },
    });
  });
};
```

### API Integration Logging

```javascript
// External API service logging
class CitylandApiService {
  async fetchSupplierData(supplierId) {
    const correlationId = this.generateCorrelationId();
    const startTime = Date.now();

    logger.info('Fetching supplier data from Cityland API', {
      category: LOG_CATEGORIES.INTEGRATION,
      correlationId,
      supplier: { id: supplierId },
      api: {
        service: 'cityland',
        endpoint: '/suppliers',
      },
    });

    try {
      const response = await this.httpClient.get(`/suppliers/${supplierId}`, {
        timeout: 10000,
        headers: {
          'X-Correlation-ID': correlationId,
        },
      });

      const duration = Date.now() - startTime;

      logger.info('Cityland API request successful', {
        category: LOG_CATEGORIES.INTEGRATION,
        correlationId,
        api: {
          service: 'cityland',
          endpoint: '/suppliers',
          duration,
          statusCode: response.status,
        },
        supplier: { id: supplierId },
      });

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('Cityland API request failed', error, {
        category: LOG_CATEGORIES.INTEGRATION,
        correlationId,
        component: 'CitylandApiService',
        api: {
          service: 'cityland',
          endpoint: '/suppliers',
          duration,
          statusCode: error.response?.status,
        },
        supplier: { id: supplierId },
      });

      throw error;
    }
  }
}
```

## Best Practices for PRS Backend

1. **Use Appropriate Log Levels**: Use the appropriate log level for each message
2. **Include Context**: Always include relevant context in logs
3. **Structured Data**: Use structured data instead of string concatenation
4. **Sensitive Information**: Never log sensitive information (passwords, tokens, etc.)
5. **Correlation IDs**: Use request IDs for correlating logs across services
6. **Business Events**: Log important business events for auditing
7. **Performance Metrics**: Log performance metrics for monitoring
8. **Error Context**: Include sufficient context for debugging errors
9. **User Actions**: Log all significant user actions for audit trails
10. **API Integrations**: Log all external API calls with timing and status
11. **Database Performance**: Monitor and log slow database queries
12. **Security Events**: Log all authentication and authorization events

### PRS-Specific Guidelines

1. **Requisition Lifecycle**: Log all state changes in requisition workflow
2. **Approval Chains**: Track approval workflows with user context
3. **Financial Data**: Log financial operations with amounts (sanitized)
4. **Supplier Interactions**: Log all supplier-related operations
5. **Document Management**: Log file uploads, downloads, and modifications
6. **Integration Points**: Monitor Cityland API and accounting system integrations
7. **User Management**: Log user creation, role changes, and access modifications
8. **Audit Requirements**: Ensure compliance with financial audit requirements

## Integration with Error Handling

The logging system integrates with the error handling system:

```javascript
try {
  // Some operation
} catch (error) {
  // The error will be automatically translated to a standardized format
  logger.error(error, { context: 'payment-processing' });

  // Rethrow for the global error handler
  throw error;
}
```

## PLG Stack Integration

The PRS Backend integrates with the PLG (Prometheus, Loki, Grafana) stack for comprehensive observability:

### Prometheus Metrics Integration

The backend exposes metrics for Prometheus scraping at `/metrics` endpoint:

```javascript
// src/infra/metrics/prometheusMetrics.js
const promClient = require('prom-client');

// Create a Registry to register the metrics
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({
  app: 'prs-backend',
  prefix: 'prs_backend_',
  timeout: 10000,
  gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5],
  register,
});

// Custom metrics for PRS Backend
const httpRequestDuration = new promClient.Histogram({
  name: 'prs_backend_http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
});

const httpRequestTotal = new promClient.Counter({
  name: 'prs_backend_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

const databaseQueryDuration = new promClient.Histogram({
  name: 'prs_backend_database_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'model'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
});

const businessEventCounter = new promClient.Counter({
  name: 'prs_backend_business_events_total',
  help: 'Total number of business events',
  labelNames: ['event_type', 'status'],
});

const activeUsersGauge = new promClient.Gauge({
  name: 'prs_backend_active_users',
  help: 'Number of currently active users',
});

const errorCounter = new promClient.Counter({
  name: 'prs_backend_errors_total',
  help: 'Total number of errors',
  labelNames: ['error_type', 'component'],
});

// Register metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(databaseQueryDuration);
register.registerMetric(businessEventCounter);
register.registerMetric(activeUsersGauge);
register.registerMetric(errorCounter);

module.exports = {
  register,
  httpRequestDuration,
  httpRequestTotal,
  databaseQueryDuration,
  businessEventCounter,
  activeUsersGauge,
  errorCounter,
};
```

### Enhanced Logger Service with Metrics

Update the logger service to include Prometheus metrics:

```javascript
// src/infra/logs/loggerService.js (enhanced)
const {
  httpRequestDuration,
  httpRequestTotal,
  databaseQueryDuration,
  businessEventCounter,
  errorCounter
} = require('../metrics/prometheusMetrics');

class LoggerService {
  // ... existing methods ...

  /**
   * Log HTTP request with metrics
   */
  logHttpRequest(req, res, responseTime) {
    const method = req.method;
    const route = this.sanitizeRoute(req.url);
    const statusCode = res.statusCode;

    // Update Prometheus metrics
    httpRequestTotal.inc({ method, route, status_code: statusCode });
    httpRequestDuration.observe({ method, route, status_code: statusCode }, responseTime / 1000);

    // Log the request
    this.info('HTTP Request', {
      category: LOG_CATEGORIES.API,
      request: {
        id: req.id,
        method,
        url: req.url,
        route,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        correlationId: req.headers['x-correlation-id'],
      },
      response: {
        statusCode,
        responseTime,
      },
      user: req.user ? {
        id: req.user.id,
        username: req.user.username,
        role: req.user.role,
      } : null,
    });
  }

  /**
   * Log database operation with metrics
   */
  logDatabaseOperation(operation, model, duration, query, parameters) {
    // Update Prometheus metrics
    databaseQueryDuration.observe({ operation, model }, duration / 1000);

    const logLevel = duration > 100 ? 'warn' : 'debug';

    this[logLevel](`DB ${operation} on ${model}${duration > 100 ? ` (${duration}ms)` : ''}`, {
      category: LOG_CATEGORIES.DATABASE,
      database: {
        operation,
        model,
        duration,
        query: this.sanitizeQuery(query),
        parameters: this.sanitizeParameters(parameters),
      },
      performance: duration > 100 ? {
        slow: true,
        duration,
      } : undefined,
    });
  }

  /**
   * Log business event with metrics
   */
  logBusinessEvent(eventType, data, status = 'success') {
    // Update Prometheus metrics
    businessEventCounter.inc({ event_type: eventType, status });

    this.info(`Business Event: ${eventType}`, {
      category: LOG_CATEGORIES.BUSINESS,
      event: {
        type: eventType,
        status,
        data: this.sanitizeData(data),
      },
    });
  }

  /**
   * Enhanced error logging with metrics
   */
  error(message, error, data = {}) {
    const errorType = error?.name || 'UnknownError';
    const component = data.component || 'unknown';

    // Update Prometheus metrics
    errorCounter.inc({ error_type: errorType, component });

    super.error(message, error, data);
  }

  sanitizeRoute(url) {
    // Remove IDs and query parameters for metrics
    return url.replace(/\/\d+/g, '/:id').replace(/\?.*/, '');
  }

  sanitizeQuery(query) {
    // Remove sensitive data from queries
    if (typeof query !== 'string') return query;
    return query.replace(/password\s*=\s*'[^']*'/gi, "password = '[REDACTED]'");
  }

  sanitizeParameters(parameters) {
    if (!Array.isArray(parameters)) return parameters;
    return parameters.map(param => {
      if (typeof param === 'string' && param.length > 50) {
        return '[LARGE_DATA]';
      }
      return param;
    });
  }

  sanitizeData(data) {
    if (!data || typeof data !== 'object') return data;

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...data };

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
```

### Loki Integration

Configure Loki for log aggregation with proper labels:

```yaml
# deployment/loki/loki-config.yaml
auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://localhost:9093

# Frontend configuration for log ingestion
frontend:
  max_outstanding_per_tenant: 2048

# Limits configuration
limits_config:
  reject_old_samples: true
  reject_old_samples_max_age: 168h
  ingestion_rate_mb: 16
  ingestion_burst_size_mb: 32
  per_stream_rate_limit: 512M
  per_stream_rate_limit_burst: 1024M
```

### Enhanced Request Middleware with Correlation IDs

```javascript
// src/app/handlers/middlewares/requestLogger.js
const { v4: uuidv4 } = require('uuid');
const { logger } = require('../../../infra/logs');

const requestLogger = async (request, reply) => {
  const startTime = Date.now();

  // Generate or use existing correlation ID
  const correlationId = request.headers['x-correlation-id'] || uuidv4();
  request.correlationId = correlationId;

  // Add correlation ID to response headers
  reply.header('x-correlation-id', correlationId);

  // Log incoming request
  logger.info('Incoming Request', {
    category: 'api',
    correlationId,
    request: {
      id: request.id,
      method: request.method,
      url: request.url,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      source: request.headers['x-request-source'] || 'unknown',
    },
    user: request.user ? {
      id: request.user.id,
      username: request.user.username,
    } : null,
  });

  // Hook into response to log completion
  reply.addHook('onSend', async (request, reply, payload) => {
    const responseTime = Date.now() - startTime;

    logger.logHttpRequest(request, reply, responseTime);

    return payload;
  });
};

module.exports = requestLogger;
```

### Grafana Dashboard Configuration

Create comprehensive dashboards for backend monitoring:

```json
{
  "dashboard": {
    "title": "PRS Backend Monitoring",
    "tags": ["prs", "backend", "api"],
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(prs_backend_http_requests_total[5m])",
            "legendFormat": "{{method}} {{route}}"
          }
        ]
      },
      {
        "title": "Response Times",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(prs_backend_http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(prs_backend_http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(prs_backend_errors_total[5m])",
            "legendFormat": "Errors/sec"
          }
        ]
      },
      {
        "title": "Database Query Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(prs_backend_database_query_duration_seconds_bucket[5m]))",
            "legendFormat": "{{operation}} {{model}}"
          }
        ]
      },
      {
        "title": "Business Events",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(prs_backend_business_events_total[5m])",
            "legendFormat": "{{event_type}}"
          }
        ]
      },
      {
        "title": "Recent Error Logs",
        "type": "logs",
        "targets": [
          {
            "expr": "{service=\"prs-backend\",level=\"error\"} |= \"\"",
            "refId": "A"
          }
        ]
      }
    ]
  }
}
```

### LogQL Queries for Backend Monitoring

Essential LogQL queries for monitoring the backend:

```logql
# Error logs with correlation IDs
{service="prs-backend",level="error"} | json | line_format "{{.timestamp}} [{{.correlationId}}] {{.message}}"

# API requests by endpoint
{service="prs-backend",category="api"} | json | line_format "{{.request.method}} {{.request.route}} - {{.response.statusCode}} ({{.response.responseTime}}ms)"

# Slow database queries
{service="prs-backend",category="database"} | json | database_duration > 100

# Business events tracking
{service="prs-backend",category="business"} | json | line_format "{{.event.type}}: {{.event.status}}"

# Authentication events
{service="prs-backend",category="security"} | json | line_format "Auth: {{.message}} - User: {{.user.username}}"

# Cross-service correlation (with frontend)
{service=~"prs-backend|prs-frontend"} | json | correlationId != "" | line_format "{{.service}}: {{.message}} [{{.correlationId}}]"

# Error rate by component
sum by (component) (count_over_time({service="prs-backend",level="error"}[1h]))
```

### Prometheus Alerting Rules

Configure alerts for critical backend issues:

```yaml
# deployment/prometheus/alerts/backend-alerts.yml
groups:
  - name: prs-backend-alerts
    rules:
      - alert: HighBackendErrorRate
        expr: rate(prs_backend_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: prs-backend
        annotations:
          summary: "High error rate in PRS backend"
          description: "Backend error rate is {{ $value }} errors per second"

      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(prs_backend_database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: prs-backend
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile database query time is {{ $value }} seconds"

      - alert: BackendDown
        expr: up{job="backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: prs-backend
        annotations:
          summary: "PRS Backend is down"
          description: "Backend service is not responding"

      - alert: HighMemoryUsage
        expr: (prs_backend_process_resident_memory_bytes / prs_backend_process_virtual_memory_max_bytes) > 0.8
        for: 5m
        labels:
          severity: warning
          service: prs-backend
        annotations:
          summary: "High memory usage in PRS backend"
          description: "Memory usage is {{ $value | humanizePercentage }}"

      - alert: LowBusinessEventRate
        expr: rate(prs_backend_business_events_total[10m]) < 0.01
        for: 10m
        labels:
          severity: warning
          service: prs-backend
        annotations:
          summary: "Low business event rate"
          description: "Business event rate is unusually low: {{ $value }} events per second"
```

### Docker Compose Integration

Update the deployment configuration to include the PLG stack:

```yaml
# deployment/compose.yml (PLG stack section)
services:
  # ... existing services ...

  # Loki for log aggregation
  loki:
    image: grafana/loki:2.9.0
    restart: always
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./loki/loki-config.yaml:/etc/loki/local-config.yaml
      - loki_data:/loki
    networks:
      - monitoring_network

  # Prometheus for metrics (enhanced)
  prometheus:
    image: prom/prometheus:latest
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/alerts:/etc/prometheus/alerts
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monitoring_network

  # Grafana for visualization (enhanced)
  grafana:
    image: grafana/grafana:latest
    restart: always
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
      - loki
    networks:
      - monitoring_network

  # Alertmanager for alert handling
  alertmanager:
    image: prom/alertmanager:latest
    restart: always
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    networks:
      - monitoring_network

volumes:
  loki_data:
  alertmanager_data:
  # ... existing volumes ...

networks:
  monitoring_network:
    driver: bridge
```

## Extending the Logging System

To add new log categories or formatters:

1. Add new categories to `LOG_CATEGORIES` in `loggerService.js`
2. Add new formatters to `logFormatter.js`
3. Add new methods to `LoggerService` class as needed
4. Update Prometheus metrics if new metric types are required
5. Add corresponding Grafana dashboard panels
6. Configure alerts for critical new metrics
