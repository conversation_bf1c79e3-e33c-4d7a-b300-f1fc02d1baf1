/**
 * Utility functions for route documentation
 */
const { zodToOpenApi } = require('./zodToOpenApi');

/**
 * Generates documentation for a route
 *
 * @param {Object} options - Documentation options
 * @param {string} options.tag - Route tag (e.g., 'Users', 'Auth')
 * @param {string} options.summary - Short summary of the route
 * @param {string} options.description - Detailed description of the route
 * @param {Object} options.body - Zod schema for request body
 * @param {Object} options.params - Zod schema for path parameters
 * @param {Object} options.querystring - Zod schema for query parameters
 * @param {Object} options.response - Object mapping status codes to Zod schemas
 * @param {Object} options.security - Security requirements
 * @returns {Object} - Route documentation
 */
function documentRoute(options = {}) {
  const {
    tag,
    summary,
    description,
    body,
    params,
    querystring,
    response,
    security = [{ bearerAuth: [] }],
  } = options;

  // Generate schema from Zod schemas
  const schema = {};

  if (body) {
    schema.body = zodToOpenApi(body);
  }

  if (params) {
    schema.params = zodToOpenApi(params);
  }

  if (querystring) {
    schema.querystring = zodToOpenApi(querystring);
  }

  if (response) {
    schema.response = {};

    Object.entries(response).forEach(([statusCode, responseSchema]) => {
      schema.response[statusCode] = zodToOpenApi(responseSchema);
    });
  }

  // Add documentation metadata
  return {
    schema: {
      ...schema,
      tags: tag ? [tag] : undefined,
      summary,
      description,
      security,
    },
  };
}

/**
 * Generates documentation for a protected route (requires authentication)
 *
 * @param {Object} options - Documentation options
 * @returns {Object} - Route documentation
 */
function documentProtectedRoute(options = {}) {
  return documentRoute({
    ...options,
    security: [{ bearerAuth: [] }],
  });
}

/**
 * Generates documentation for a public route (no authentication required)
 *
 * @param {Object} options - Documentation options
 * @returns {Object} - Route documentation
 */
function documentPublicRoute(options = {}) {
  return documentRoute({
    ...options,
    security: [],
  });
}

/**
 * Generates standard response schemas for common operations
 *
 * @param {Object} entitySchema - Zod schema for the entity
 * @param {Object} options - Options
 * @returns {Object} - Standard response schemas
 */
function standardResponses(entitySchema, options = {}) {
  const { z } = require('zod');

  // Create standard response schemas
  const successResponse = z.object({
    success: z.boolean().default(true),
    message: z.string(),
    data: entitySchema,
  });

  const listResponse = z.object({
    success: z.boolean().default(true),
    message: z.string(),
    data: z.object({
      data: z.array(entitySchema),
      total: z.number(),
    }),
  });

  const errorResponse = z.object({
    success: z.boolean().default(false),
    message: z.string(),
    errorCode: z.string().optional(),
    timestamp: z.string().optional(),
    metadata: z.record(z.any()).optional(),
  });

  // Return standard responses
  return {
    // GET responses
    get: {
      200: successResponse,
      404: errorResponse,
      500: errorResponse,
    },

    // GET list responses
    list: {
      200: listResponse,
      500: errorResponse,
    },

    // POST responses
    create: {
      201: successResponse,
      400: errorResponse,
      422: errorResponse,
      500: errorResponse,
    },

    // PUT responses
    update: {
      200: successResponse,
      400: errorResponse,
      404: errorResponse,
      422: errorResponse,
      500: errorResponse,
    },

    // DELETE responses
    delete: {
      204: z.null(),
      404: errorResponse,
      500: errorResponse,
    },

    // Custom responses
    custom: {
      errorResponse,
      successResponse,
      listResponse,
    },
  };
}

module.exports = {
  documentRoute,
  documentProtectedRoute,
  documentPublicRoute,
  standardResponses,
};
