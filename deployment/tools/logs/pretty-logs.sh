#!/bin/bash

# This script formats and displays logs in a beautiful and easy-to-read format

# Define colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
BOLD='\033[1m'
RESET='\033[0m'

# Function to format timestamp
format_timestamp() {
  local timestamp="$1"
  echo -e "${GRAY}${timestamp}${RESET}"
}

# Function to format log level
format_level() {
  local level="$1"
  case "$level" in
    "ERROR")
      echo -e "${RED}${BOLD}ERROR${RESET}"
      ;;
    "WARN")
      echo -e "${YELLOW}${BOLD}WARN${RESET}"
      ;;
    "INFO")
      echo -e "${GREEN}${BOLD}INFO${RESET}"
      ;;
    "DEBUG")
      echo -e "${BLUE}${BOLD}DEBUG${RESET}"
      ;;
    *)
      echo -e "${GRAY}${level}${RESET}"
      ;;
  esac
}

# Function to format error type
format_error_type() {
  local error_type="$1"
  echo -e "${PURPLE}${error_type}${RESET}"
}

# Function to format message
format_message() {
  local message="$1"
  echo -e "${CYAN}${message}${RESET}"
}

# Function to format request ID
format_request_id() {
  local request_id="$1"
  echo -e "${GRAY}${request_id}${RESET}"
}

# Function to format stack trace
format_stack() {
  local stack="$1"
  # Extract the first few lines of the stack trace
  local short_stack=$(echo "$stack" | head -n 5)
  echo -e "${GRAY}${short_stack}${RESET}"
}

# Function to format error code
format_error_code() {
  local error_code="$1"
  echo -e "${RED}${error_code}${RESET}"
}

# Function to format original error
format_original_error() {
  local original_error="$1"
  echo -e "${YELLOW}${original_error}${RESET}"
}

# Function to parse and format a log line
parse_log_line() {
  local line="$1"
  
  # Extract timestamp
  local timestamp=$(echo "$line" | grep -o '\[[^]]*\]' | head -n 1)
  
  # Extract log level
  local level=$(echo "$line" | grep -o 'INFO\|ERROR\|WARN\|DEBUG' | head -n 1)
  
  # Check if this is a JSON log
  if [[ "$line" == *"errorType"* ]]; then
    # Extract error type
    local error_type=$(echo "$line" | grep -o '"errorType":"[^"]*"' | cut -d'"' -f4)
    
    # Extract message
    local message=$(echo "$line" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    
    # Extract request ID
    local request_id=$(echo "$line" | grep -o '"x-request-id":"[^"]*"' | cut -d'"' -f4)
    
    # Extract error code
    local error_code=$(echo "$line" | grep -o '"errorCode":"[^"]*"' | cut -d'"' -f4)
    
    # Extract original error
    local original_error=$(echo "$line" | grep -o '"originalError":"[^"]*"' | cut -d'"' -f4)
    
    # Extract stack trace (first few lines)
    local stack=$(echo "$line" | grep -o '"stack":"[^"]*"' | cut -d'"' -f4 | sed 's/\\n/\n/g' | head -n 5)
    
    # Format and print the log entry
    echo -e "\n${BOLD}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${RESET}"
    echo -e "$(format_timestamp "$timestamp") $(format_level "$level")"
    echo -e "${BOLD}Error Type:${RESET} $(format_error_type "$error_type")"
    echo -e "${BOLD}Error Code:${RESET} $(format_error_code "$error_code")"
    echo -e "${BOLD}Message:${RESET} $(format_message "$message")"
    echo -e "${BOLD}Request ID:${RESET} $(format_request_id "$request_id")"
    
    if [ ! -z "$original_error" ]; then
      echo -e "${BOLD}Original Error:${RESET} $(format_original_error "$original_error")"
    fi
    
    if [ ! -z "$stack" ]; then
      echo -e "${BOLD}Stack Trace:${RESET}\n$(format_stack "$stack")"
    fi
  else
    # Regular log line
    echo -e "$(format_timestamp "$timestamp") $(format_level "$level") ${line#*$level: }"
  fi
}

# Main function to process logs
process_logs() {
  local container_name="$1"
  local lines="$2"
  
  # Get logs from the container
  if [ -z "$lines" ]; then
    docker logs "$container_name" | while read -r line; do
      parse_log_line "$line"
    done
  else
    docker logs "$container_name" --tail "$lines" | while read -r line; do
      parse_log_line "$line"
    done
  fi
}

# Check if container name is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <container_name> [number_of_lines]"
  echo "Example: $0 deployment-backend-1 50"
  exit 1
fi

# Get container name and optional number of lines
container_name="$1"
lines="$2"

# Process logs
process_logs "$container_name" "$lines"

# Add a follow mode option
if [ "$3" == "-f" ] || [ "$3" == "--follow" ]; then
  echo -e "\n${YELLOW}Following logs... Press Ctrl+C to exit${RESET}\n"
  docker logs -f "$container_name" | while read -r line; do
    parse_log_line "$line"
  done
fi
