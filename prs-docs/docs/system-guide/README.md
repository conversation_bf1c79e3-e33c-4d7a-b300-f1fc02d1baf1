# System Guide

The System Guide provides a comprehensive overview of the PRS (Purchase Requisition System), including its architecture, business rules, development guidelines, workflows, and code templates.

## Sections

### Architecture

The Architecture section describes the overall system architecture, including:

- [Backend Architecture](architecture/overview.md): Backend system components and their interactions
- [Frontend Architecture](architecture/frontend-overview.md): Frontend system components and their interactions
- Database schema
- API design
- Integration points

### [Business Rules](business-rules/overview.md)

The Business Rules section details the business rules for each domain area:

- [Requisition](business-rules/requisition.md)
- [Purchase Order](business-rules/purchase-order.md)
- [Canvass](business-rules/canvass.md)
- [Delivery](business-rules/delivery.md)
- [Payment](business-rules/payment.md)

### [Development Guide](development-guide/getting-started.md)

The Development Guide provides guidelines for development, including:

- Setting up the development environment
- Coding standards
- Best practices
- Testing guidelines

### [Workflows](workflows/index.md)

The Workflows section details the key business processes:

- [Requisition Workflow](workflows/requisition-workflow.md)
- [Purchase Order Workflow](workflows/purchase-order-workflow.md)
- [Canvass Workflow](workflows/canvass-workflow.md)
- [Approval Workflow](workflows/approval-workflow.md)
- And more...

### [Templates](templates/controller.md)

The Templates section provides code templates for common components:

- [Controller](templates/controller.md)
- [Entity](templates/entity.md)
- [Repository](templates/repository.md)
- [Service](templates/service.md)
- [Test](templates/test.md)
