// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and Authentication Models
model User {
  id                Int                @id @default(autoincrement())
  username          String             @unique
  email             String?            @unique
  password          String
  firstName         String?
  lastName          String?
  otpSecret         String?
  status            UserStatus         @default(ACTIVE)
  isPasswordTemporary Boolean          @default(true)
  roleId            Int
  departmentId      Int?
  supervisorId      Int?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  deletedAt         DateTime?

  // Relationships
  role              Role               @relation(fields: [roleId], references: [id])
  department        Department?        @relation(fields: [departmentId], references: [id])
  supervisor        User?              @relation("UserSupervisor", fields: [supervisorId], references: [id])
  subordinates      User[]             @relation("UserSupervisor")

  // Auth relationships
  accounts          Account[]
  sessions          Session[]
}

model Account {
  id                String  @id @default(cuid())
  userId            Int
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       Int
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum UserStatus {
  ACTIVE
  INACTIVE
  LOCKED
}

model Role {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users       User[]
  permissions RolePermission[]
}

model Permission {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]
}

model RolePermission {
  id           Int      @id @default(autoincrement())
  roleId       Int
  permissionId Int
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  role         Role       @relation(fields: [roleId], references: [id])
  permission   Permission @relation(fields: [permissionId], references: [id])

  @@unique([roleId, permissionId])
}

// Organization Models
model Company {
  id          Int      @id @default(autoincrement())
  name        String
  code        String   @unique
  address     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  departments Department[]
}

model Department {
  id          Int      @id @default(autoincrement())
  name        String
  code        String   @unique
  companyId   Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  company     Company  @relation(fields: [companyId], references: [id])
  users       User[]
}
