<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle no-grid" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="507712789C0" style="width:103px;" class="column-headers-background">A</th><th id="507712789C1" style="width:167px;" class="column-headers-background">B</th><th id="507712789C2" style="width:252px;" class="column-headers-background">C</th><th id="507712789C3" style="width:84px;" class="column-headers-background">D</th><th id="507712789C4" style="width:233px;" class="column-headers-background">E</th><th id="507712789C5" style="width:330px;" class="column-headers-background">F</th><th id="507712789C6" style="width:67px;" class="column-headers-background">G</th><th id="507712789C7" style="width:511px;" class="column-headers-background">H</th><th id="507712789C8" style="width:108px;" class="column-headers-background">I</th><th id="507712789C9" style="width:114px;" class="column-headers-background">J</th><th id="507712789C10" style="width:124px;" class="column-headers-background">K</th><th id="507712789C11" style="width:124px;" class="column-headers-background">L</th><th id="507712789C12" style="width:124px;" class="column-headers-background">M</th><th id="507712789C13" style="width:124px;" class="column-headers-background">N</th><th id="507712789C14" style="width:78px;" class="column-headers-background">O</th><th id="507712789C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="507712789R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(E2E_Run1)</td><td class="s1">Actual Results<br>(E2E_Run1)</td><td class="s1">Status<br>(E2E_Run2)</td><td class="s1">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="507712789R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="16">PRS-033 - [Enhancements_3} -  User Types: Add Management User Type, Manage Department: Enhance Error Messaging for blank Approvers during Submission, Manage Projects: Approver Assignment - Enhance Error Messaging for blank Approvers during Submission                                        </td></tr><tr style="height: 19px"><th id="507712789R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s3"></td><td class="s4">User Types: Add Management User Type</td><td class="s4">Validate Added User Type Field for User Management</td><td class="s5">High</td><td class="s4">1. Users must be logged in to their Accounts </td><td class="s4">1. In Dashboard , Go to Admin<br>2. Click User Management<br>3. Click Create New User<br>4. Validate Added User Type</td><td class="s4"></td><td class="s6"><a target="_blank" href="https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?gid=**********#gid=**********&amp;range=M2">1. Should add a User Type of Management<br>2. Should follow this Table for the User Type Access<br><br>USER TYPE ACCESS<br>Cityland PRS - Product Backlog</a></td><td class="s3"></td><td class="s3"></td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s4">3/10: Unable to create new management user only max of 2 users</td><td class="s3"></td></tr><tr style="height: 19px"><th id="507712789R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s3"></td><td class="s4">Manage Department: Enhance Error Messaging for blank Approvers during Submission</td><td class="s4">Validate Error Message if the Added Approver is blank in field</td><td class="s5">Critical</td><td class="s4">1. Users must be logged in to their Accounts </td><td class="s4">1. In Dashboard, Go to Manage<br>2. Click Department tab<br>3. Click Department Name or Edit Pencil Icon<br>4. Click Edit Button<br>5. Click Add Level Button<br>6. Click Add Approver<br>7. Validate if Add Approver Field is blank</td><td class="s4"></td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should edit one Department<br>2. Should add an Approver then Submit without assigning a User<br>3. Should display an Error Message of, &quot;</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Approver is Required</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">&quot;</span></td><td class="s3"></td><td class="s3"></td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="507712789R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s3"></td><td class="s4">Manage Projects: Approver Assignment - Enhance Error Messaging for blank Approvers during Submission</td><td class="s4">Validate Error Message if the Added Approver is blank in field</td><td class="s5">Critical</td><td class="s4">1. Users must be logged in to their Accounts </td><td class="s4">1. In Dashboard, Go to Manage<br>2. Click Project tab<br>3. Click Project Name <br>4. On Project Approver &gt;&gt; Click Edit Button<br>5. Click Add Level Button<br>6. Click Add Approver<br>7. Validate if Add Approver Field is blank&quot;</td><td class="s4"></td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should edit one Project<br>2. Should add an Approver then Submit without assigning a User<br>3. Should display an Error Message of, &quot;</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Approver is Required</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">&quot;</span></td><td class="s3"></td><td class="s3"></td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s4"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="507712789R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s3"></td><td class="s4">Manage Projects: Project Sync - Project Address needs a Sample Data</td><td class="s4">Validate if Project Address  is able to Retrieve during Syncing of Data for Project</td><td class="s5">High</td><td class="s4">1. Users must be logged in to their Accounts </td><td class="s4">1. In Dashboard, Go to Manage<br>2. Click Project tab<br>3. Click Sync Button <br>4. Validate Data for project</td><td class="s3"></td><td class="s4">1. Should be able to retrieve the Project Address during Syncing of Data for Project</td><td class="s3"></td><td class="s3"></td><td class="s7">Passed</td><td class="s4"></td><td class="s8">Not Started</td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr></tbody></table></div>