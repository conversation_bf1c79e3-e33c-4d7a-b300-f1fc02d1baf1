const AppError = require('./appError');

/**
 * Error class for conflict errors (e.g., duplicate resources)
 */
class ConflictError extends AppError {
  /**
   * Creates a new ConflictError
   * 
   * @param {string} message - Error message
   * @param {string} resource - Resource type that has a conflict
   * @param {string} field - Field that has a conflict
   * @param {string} value - Value that caused the conflict
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(
    message = 'Resource already exists',
    resource = null,
    field = null,
    value = null,
    originalError = null
  ) {
    super(message, 'CONFLICT', { resource, field, value }, originalError);
    this.resource = resource;
    this.field = field;
    this.value = value;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 409; // Conflict
  }

  /**
   * Creates a ConflictError from a Sequelize unique constraint error
   * 
   * @param {Error} error - Sequelize error
   * @param {string} resource - Resource type
   * @param {string} message - Optional message override
   * @returns {ConflictError} - New ConflictError instance
   */
  static fromSequelizeUniqueConstraintError(error, resource, message = null) {
    const field = error.errors?.[0]?.path || 'unknown';
    const value = error.errors?.[0]?.value || 'unknown';
    
    return new ConflictError(
      message || `${resource} with this ${field} already exists`,
      resource,
      field,
      value,
      error
    );
  }
}

module.exports = ConflictError;
