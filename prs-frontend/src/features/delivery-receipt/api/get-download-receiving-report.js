import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/apiClient';

export const downloadReceivingReport = id => {
  return api
    .get(`/v1/generate-template/delivery-receipt/${id}`)
    /*
    .then(blob => {
      const fileURL = window.URL.createObjectURL(blob);
      /*
      let alink = document.createElement('a');
      alink.href = fileURL;
      alink.download = 'SamplePDF.pdf';
      alink.click();
      
    })
    .catch(error => {
      console.log(error);
    });
    */
};

export const useGetDownloadReceivingReport = (id, config = {}) => {
  return useQuery({
    queryKey: ['deliveryReceiptDownload', id],
    queryFn: () => downloadReceivingReport(id),
    onSuccess: res => {
      console.log('success');
      console.log(res);
    },
    refetchOnMount: 'always',
    ...config,
  });
};
