<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#e06666;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1697858629C0" style="width:103px;" class="column-headers-background">A</th><th id="1697858629C1" style="width:167px;" class="column-headers-background">B</th><th id="1697858629C2" style="width:252px;" class="column-headers-background">C</th><th id="1697858629C3" style="width:92px;" class="column-headers-background">D</th><th id="1697858629C4" style="width:233px;" class="column-headers-background">E</th><th id="1697858629C5" style="width:330px;" class="column-headers-background">F</th><th id="1697858629C6" style="width:77px;" class="column-headers-background">G</th><th id="1697858629C7" style="width:377px;" class="column-headers-background">H</th><th id="1697858629C8" style="width:100px;" class="column-headers-background">I</th><th id="1697858629C9" style="width:114px;" class="column-headers-background">J</th><th id="1697858629C10" style="width:146px;" class="column-headers-background">K</th><th id="1697858629C11" style="width:146px;" class="column-headers-background">L</th><th id="1697858629C12" style="width:146px;" class="column-headers-background">M</th><th id="1697858629C13" style="width:146px;" class="column-headers-background">N</th><th id="1697858629C14" style="width:109px;" class="column-headers-background">O</th><th id="1697858629C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1697858629R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Test Case/Scenario</td><td class="s2">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s2">STG_wk3</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1697858629R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-023 - [History] Non-OFM History, RS Related Documents, Request History</td></tr><tr style="height: 19px"><th id="1697858629R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-023-001</td><td class="s5">Non-OFM History</td><td class="s5">Verify  Viewing of Non-OFM History</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>4. An OFM Item List has used the Item<br>5. A Requisition Slip has used the Item for their Request </a></td><td class="s5">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non- OFM List&quot; sub menu<br>3. Ciick &quot;Item Name&quot; Text Link <br>4. Check Display of  OFM List View page<br>5. Check History section<br>6. Check the display of the ff: columns when data is not yet avaiable.<br>     a. Date Delivered<br>     b. Quantity Delivered<br>7. Check if RS number displayed are sync to Non-OFM item<br>8. Check if Company indicated is sync in the submitted Requisition Slip<br>9. Check if Project indicated is sync in the submitted Requisition Slip<br>10. Check if Department indicated is sync in the submitted Requisition Slip<br>11. Check if Date Requested is sync on Submitted RS<br>12. Check if Quantity Requested is sync to quantity indicated of RS item<br>13. Check if Date Delivered is sync to RS delivery date<br>14. Check if Quantity Delivered  is sync to RS Quantity delivery receipt</td><td class="s7"></td><td class="s5">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM List page <br>3. Should displayed the Non-OFM Item View page<br>4. Should displayed the Non-OFM List details<br>5. Should display Table of History<br>    a. Should display the List of Requests that has used the Item<br>    b. Should have the following Columns<br>        i. RS Number<br>        ii. Company<br>        iii. Project<br>        iv. Department<br>        v. Date Requested<br>        vi. Quantity Requested<br>        vii. Date Delivered<br>        viii. Quantity Delivered<br>    c. Should have default sorting by RS Number, 0-9<br>    d. Should display 10 Rows per Page<br>6. Should displayed as &quot;---&quot; for the Columns that Data is not yet available<br>7. RS Number should sync to submitted RS<br>8. Company should sync in indicated company of submitted RS<br>9. Project should sync in indicated Project of submitted RS<br>10. Department should sync in indicated departmemt of submitted RS<br>11. Date requested should sync on submitted date of the RS<br>12. Quantity should sync in the indicated item of RS<br>13. Date Delivery should sync on delivery date of an Item of RS<br>     a. If no Date yet should display &quot;---&quot;<br>14. Quantity Delivered should sync on entered quantity of Delivery receipt<br>      a. If no Date yet should display &quot;---&quot;</td><td class="s6" rowspan="36"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1oX7uYf47L_cJ2xzciMk5uAJHm4XPielpi3si6c5rnVc/edit?gid=1726485877#gid=1726485877">https://docs.google.com/spreadsheets/d/1oX7uYf47L_cJ2xzciMk5uAJHm4XPielpi3si6c5rnVc/edit?gid=1726485877#gid=1726485877</a></td><td class="s8">Failed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s6"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-892">https://youtrack.stratpoint.com/issue/CITYLANDPRS-892</a></td></tr><tr style="height: 19px"><th id="1697858629R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s5">PRS-023-002</td><td class="s5">Non-OFM History</td><td class="s5">Verify  Searching of Non-OFM History is working as expected</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>4. An OFM Item List has used the Item<br>5. A Requisition Slip has used the Item for their Request </a></td><td class="s5">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non- OFM List&quot; sub menu<br>3. Ciick &quot;Item Name&quot; Text Link <br>4. Check Display of  OFM List View page<br>5.Search an Specific RS Number<br>6. Search a Keyword of RS Number<br>7. Search an Item that is not existing on ther table<br>8. Click &quot;Clear&quot; button on the History section</td><td class="s7"></td><td class="s5">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM List page <br>3. Should displayed the Non-OFM Item View page<br>4. Should displayed the Non-OFM List details<br>5. Should be able to search the RS Number<br>6. Should be able to search  an RS Number using a keyword<br>7. Should display No Data if there is no available Data matching the Searched Item<br>8. Should clear the Search Item and reset the Table </td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1697858629R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s5">PRS-023-003</td><td class="s5">Non-OFM History</td><td class="s5">Verify  Sorting of every columns in Non-OFM History are working as expected</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>4. An OFM Item List has used the Item<br>5. A Requisition Slip has used the Item for their Request </a></td><td class="s5">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non- OFM List&quot; sub menu<br>3. Ciick &quot;Item Name&quot; Text Link <br>4. Check Display of  OFM List View page<br>5. Check Sorting of each of the following Columns         <br>        a. RS Number<br>        b. Company<br>        c. Project<br>        d. Department<br>        e. Date Requested<br>        f. Quantity Requested<br>        g. Date Delivered<br>        h. Quantity Delivered</td><td class="s7"></td><td class="s5">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM List page <br>3. Should displayed the Non-OFM Item View page<br>4. Should displayed the Non-OFM List details<br>5. Should sorted each columns correctly:<br>     a. RS Number - 0-9, 9-0<br>     b. Company - 0-9, A-Z || 9-0, Z-A<br>     c. Project - 0-9, A-Z || 9-0, Z-A<br>     d. Department - 0-9, A-Z || 9-0, Z-A<br>     e. Date Requested - Oldest Date-Latest Date, Latest Date-Oldest Date<br>     f. Quantity Requested - 0-9, 9-0<br>     g. Date Delivered - Oldest Date-Latest Date, Latest Date-Oldest Date<br>     h. Quantity Delivered  - 0-9, 9-0</td><td class="s8">Failed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s6"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-881">https://youtrack.stratpoint.com/issue/CITYLANDPRS-881</a></td></tr><tr style="height: 19px"><th id="1697858629R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s5">PRS-023-004</td><td class="s5">Non-OFM History</td><td class="s5">Verify Pagination in Non-OFM History are working as expected</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as<br>    a. Purchasing Staff<br>    b. Accounting Staff<br>    c. IT Admin<br>4. An OFM Item List has used the Item<br>5. A Requisition Slip has used the Item for their Request </a></td><td class="s5">1. Click &quot;Items&quot; dropdown<br>2. Click &quot;Non- OFM List&quot; sub menu<br>3. Ciick &quot;Item Name&quot; Text Link <br>4. Check Display of  OFM List View page<br>5. Check Display of Items per page of the table<br>6. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>7. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. A dropdown sub menus should be displayed:<br>    -OFM<br>    -OFM List<br>    -Non-OFM<br>2. Should displayed the Non-OFM List page <br>3. Should displayed the Non-OFM Item View page<br>4. Should displayed the Non-OFM List details<br>5. Should display 10 Rows per Page<br>6. Should display the previous or next page of the table<br>7. Should display the specific page selected with correct number of item entries in the table</td><td class="s8">Failed</td><td class="s9">Passed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s6"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-884">https://youtrack.stratpoint.com/issue/CITYLANDPRS-884</a></td></tr><tr style="height: 19px"><th id="1697858629R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s11">PRS-023-005</td><td class="s11">RS Related Documents</td><td class="s11">Verify Viewing of RS Related Documents</td><td class="s11">Critical</td><td class="s12"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s11">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Check Display of Related documents tab<br>4. Display of each tabs if no available data<br>5. Check Display of Canvassess tab<br>6. Click Payments tab and check display<br></td><td class="s13"></td><td class="s11">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display the Requisition Slip Number, the &quot;Request History,&quot; button, &quot;Select Actions&quot; button and display Tabs for:the ff:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>4. Should display a No Data if there is no available Data for each Tabs<br>5. Should display the following Columns of  Canvasses Tab<br>        i. Canvass Number<br>           i) Canvass Number of the Requisition Slip<br>        ii. Last Update<br>           i) Date of the last update made to the Canvass<br>              a) Including any Updates or Approval<br>        iii. Last Approver<br>           i) Last Approver of the Canvass<br>        iv. Status<br>           i) Canvass Status<br>              a) CS Draft - BG: F7F8FA, Text: 999999<br>              b) Partially Canvassed - BG: F6DFC1, Text: F0963D<br>              c) Canvass Approval - BG: CEE2C3, Text: 28A745<br>6.  Should display the following columns of  Payments tab<br>        i. PR Number<br>           i) Payment Request Number of the Items in the Requisition Slip<br>        ii. Last Update<br>           i) Date of the last update made to the Payment Request<br>              a) Including any Updates or Approval<br>        iii. Last Approver<br>           i) Last Approver of the Payment Request<br>        iv. Status<br>           i) Payment Request Status<br>              a) Payment Request<br>              b) Partial Payment Request<br>              c) PR Approval<br>              d) Closed<br></td><td class="s8">Failed</td><td class="s8">Failed</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s11"></td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-893">List of Defects:<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-893<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-894<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-895<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-896</a></td></tr><tr style="height: 19px"><th id="1697858629R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s11">PRS-023-006</td><td class="s11">RS Related Documents</td><td class="s11">Verify Searching of RS Related Documents is working as expected iin Canvassing Tab</td><td class="s11">High</td><td class="s12"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s11">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. On Canvasses tab, Search an Specific Canvass Number<br>4. Search a Keyword of Canvass Number<br>5. Search a Canvass  number that is not existing on the table<br>6. Click &quot;Clear&quot; button on the Canvass section</td><td class="s13"></td><td class="s11">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3.Should be able to search the Canvass Number<br>4. Should be able to search  an Canvass Number using a keyword<br>5. Should display No Data if there is no available Data matching the Searched Item<br>6. Should clear the Search Item and reset the Table </td><td class="s14">Out of Scope</td><td class="s14">Out of Scope</td><td class="s5"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s11">2/12/25 <br>As per Miss Irene:<br><br>Isahan lang ang Canvassing, walang way to create multiple Canvassing as of now. Other TC na requires Multiple Canvassing cannot be test at the moment</td><td class="s11"></td></tr><tr style="height: 19px"><th id="1697858629R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s11">PRS-023-007</td><td class="s11">RS Related Documents</td><td class="s11">Verify Searching of RS Related Documents is working as expected iin Orders Tab</td><td class="s11">High</td><td class="s12"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s11">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Orders tab and Search an Specific Order Number<br>4. Search a Keyword of Order Number<br>5. Search a Order number  that is not existing on the table<br>6. Click &quot;Clear&quot; button on the Order section</td><td class="s13"></td><td class="s11">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3.Should displayed Orders tab and  able to search the Order Number<br>4. Should be able to search  an Order Number using a keyword<br>5. Should display No Data if there is no available Data matching the Searched Item<br>6. Should clear the Search Item and reset the Table </td><td class="s15">Not Run</td><td class="s9">Passed</td><td class="s16" rowspan="15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1FjrNBJHhKH_mv1aYaKE_d9BDKKqRbYdEIrRdor-RPzw/edit?gid=1320297107#gid=1320297107">https://docs.google.com/spreadsheets/d/1FjrNBJHhKH_mv1aYaKE_d9BDKKqRbYdEIrRdor-RPzw/edit?gid=1320297107#gid=1320297107</a></td><td class="s10">Not Started</td><td class="s5"></td><td class="s11">Recently completed feature therefore not yet tested and no data</td><td class="s11"></td></tr><tr style="height: 19px"><th id="1697858629R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s11">PRS-023-008</td><td class="s11">RS Related Documents</td><td class="s11">Verify Searching of RS Related Documents is working as expected iin Deliveries Tab</td><td class="s11">High</td><td class="s12"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s11">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Deliveries tab and Search an Specific Delivery Number<br>4. Search a Keyword of Delivery Number<br>5. Search a Delivery number  that is not existing on the table<br>6. Click &quot;Clear&quot; button on the Deliveries section</td><td class="s13"></td><td class="s11">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3.Should displayed Deliveries tab and  able to search the Delivery Number<br>4. Should be able to search  an Delivery Number using a keyword<br>5. Should display No Data if there is no available Data matching the Searched Item<br>6. Should clear the Search Item and reset the Table </td><td class="s15">Not Run</td><td class="s8">Failed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s11">Recently completed feature therefore not yet tested and no data</td><td class="s13"></td></tr><tr style="height: 19px"><th id="1697858629R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s11">PRS-023-009</td><td class="s11">RS Related Documents</td><td class="s11">Verify Searching of RS Related Documents is working as expected iin Payments Tab</td><td class="s11">High</td><td class="s12"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s11">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Payments tab and Search an Specific Payment Number<br>4. Search a Keyword of Payment Number<br>5. Search a Payment number  that is not existing on the table<br>6. Click &quot;Clear&quot; button on the Paymnents section</td><td class="s13"></td><td class="s11">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3.Should displayed Payments tab and  able to search the Payment Number<br>4. Should be able to search  an Payment Number using a keyword<br>5. Should display No Data if there is no available Data matching the Searched Item<br>6. Should clear the Search Item and reset the Table </td><td class="s15">Not Run</td><td class="s8">Failed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s11">Recently completed feature therefore not yet tested and no data</td><td class="s13"></td></tr><tr style="height: 19px"><th id="1697858629R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s5">PRS-023-010</td><td class="s5">RS Related Documents</td><td class="s5">Verify Sorting of RS Related Documents is working as expected in Canvasses Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. On Canvasses tab, Click Sorting on &quot;Canvass Number&quot; column<br>4. Click Sorting  of &quot;Last Update&quot;  column<br>5. Click sorting of &quot;Last Approver&quot; column<br>6. Click Sorting of &quot;Status&quot; column</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Canvass Number Column should be sorted correctly to - 0-9, 9-0<br>4. Last Update Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>5. Last Approver Column should be sorted correctly to - A-Z, Z-A<br>6. Status Column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s14">Out of Scope</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">As per Miss Irene, feature right now can only have one Canvass so this feature cannot be tested</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s5">PRS-023-011</td><td class="s5">RS Related Documents</td><td class="s5">Verify Sorting of RS Related Documents is working as expected in Orders Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Orders tab, Click Sorting on &quot;PO Number&quot; column<br>4. Click sorting of &quot;Supplier&quot; column<br>5. Click Sorting  of &quot;Last Update&quot; column<br> 6. Click sorting of &quot;Last Approver&quot; column<br> 7. Click Sorting of &quot;Status&quot; column</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should displayed Orders tab and PO Number Column should be sorted correctly to - 0-9, 9-0<br>4. Supplier Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>5. Last Update Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>6. Last Approver Column should be sorted correctly to - A-Z, Z-A<br>7. Status  Column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s5">PRS-023-012</td><td class="s5">RS Related Documents</td><td class="s5">Verify Sorting of RS Related Documents is working as expected in Deliveries Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Deliveries tab, Click Sorting on &quot;DR Number&quot; column<br>4. Click sorting of &quot;Supplier &quot; column<br>5. Click Sorting  of &quot;Last Delivery Date&quot;  column<br>6. Click Sorting of &quot;Status&quot; column</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should displayed Deliveries tab and DR Number Column should be sorted correctly to  - 0-9, 9-0<br>4. Supplier Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>5. Last Delivery Date Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>6. Status Column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s8">Failed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s5">PRS-023-013</td><td class="s5">RS Related Documents</td><td class="s5">Verify Sorting of RS Related Documents is working as expected in Payments Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Payments tab, Click Sorting on &quot;PR Number&quot; column<br>4. Click Sorting  of &quot;Last Update&quot;  column<br>5. Click sorting of &quot;Last Approver&quot; column<br>6. Click Sorting of &quot;Status&quot; column</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should displayed Payments tab and PR Number Column should be sorted correctly to - 0-9, 9-0<br>4. Last Update Column should be sorted correctly to  - Oldest Date-Latest Date, Latest Date-Oldest Date<br>5. Last Approver Column should be sorted correctly to - A-Z, Z-A<br>6. Status Column should be sorted correctly to  - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s5">PRS-023-014</td><td class="s5">RS Related Documents</td><td class="s5">Verify Pagination of RS Related Documents is working as expected  in Canvassess Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. On Canvassesa tab,  Check Display of table list per page of the table<br>4. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>5. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Should display 10 Rows per Page<br>4. Should display the previous or next page of the table<br>5. Should display the specific page selected with correct number of item entries in the table</td><td class="s14">Out of Scope</td><td class="s14">Out of Scope</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">2/12/25 <br>As per Miss Irene:<br><br>Isahan lang ang Canvassing, walang way to create multiple Canvassing as of now. Other TC na requires Multiple Canvassing cannot be test at the moment</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s5">PRS-023-015</td><td class="s5">RS Related Documents</td><td class="s5">Verify Pagination of RS Related Documents is working as expected  in Orders Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Orders tab and Check Display of table list per page of the table<br>4. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>5. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Orders tab should be displayed amd should display 10 Rows per Page<br>4. Should display the previous or next page of the table<br>5. Should display the specific page selected with correct number of item entries in the table</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s5">PRS-023-016</td><td class="s5">RS Related Documents</td><td class="s5">Verify Pagination of RS Related Documents is working as expected  in Deliveries Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Deliveries tab and Check Display of table list per page of the table<br>4. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>5. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Deliveries tab should be displayed amd should display 10 Rows per Page<br>4. Should display the previous or next page of the table<br>5. Should display the specific page selected with correct number of item entries in the table</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s5">PRS-023-017</td><td class="s5">RS Related Documents</td><td class="s5">Verify Pagination of RS Related Documents is working as expected  in Payments Tab</td><td class="s5">High</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Cick Related Documents Tab<br>3. Click Payments tab and Check Display of table list per page of the table<br>4. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>5. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Canvasses Tab Table<br>3. Payments tab should be displayed amd should display 10 Rows per Page<br>4. Should display the previous or next page of the table<br>5. Should display the specific page selected with correct number of item entries in the table</td><td class="s17">Not Run</td><td class="s8">Failed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s5">PRS-023-018</td><td class="s5">Request History</td><td class="s5">Verify Request History of Canvassess tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Check Display of Canvassess tab<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display the ff columns in the Canvass Tab Table<br>        i. Canvass Number<br>           i) Canvass Number of the Requisition Slip<br>        ii. Item<br>           i) Canvassed Items<br>        iii. Supplier<br>           i) Supplier or Suppliers of the Item<br>        iv. Price<br>           i) Price of the Canvassed Item<br>        v. Discount<br>           i) Discount of the Canvassed Item if indicated<br>              a) Should display &quot;---&quot; if not indicated<br>        vi. Canvass Date<br>           i) Date of the Canvass was Submitted<br>        vii. Status<br>           i) Canvass Status</td><td class="s14">Out of Scope</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s5">PRS-023-019</td><td class="s5">Request History</td><td class="s5">Verify Request History of Orders tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Orders tab and Check Display of Order tab<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display the Order Tab and display the ff columns:<br>        i. PO Number<br>           i) Purchase Order Number of the Requisition Slip<br>        ii. Supplier<br>           i) Supplier of the Purchase Order<br>        iii. Purchase Order Price<br>           i) Total Price of the Purchased Item/s<br>        iv. Date Ordered<br>           i) Date of the Item Ordered<br>        v. Status<br>           i) Purhcase Order Status</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5">PRS-023-020</td><td class="s5">Request History</td><td class="s5">Verify Request History of Deliveries tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Deliveries tab and Check Display of Deliveries tab<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display the Deliveries Tab and display the ff columns:<br>        i. Delivery Receipt Number<br>           i) Delivery Receipt Number per Purchase Order<br>        ii. Supplier<br>           i) Supplier of the Item<br>        iii. Date Ordered<br>           i) Date of Ordering the Item<br>        iv. Quantity Ordered<br>           i) Quantity of the Ordered Item<br>        v. Date Delivered<br>           i) Date of the Order being Delivered<br>        vi. Quantity Delivered<br>           i) Quantity of the Ordered Item that was Delivered<br>        vii. Status<br>           i) Delivery Status</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5">PRS-023-021</td><td class="s5">Request History</td><td class="s5">Verify Request History of Payments tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Payments tab and Check Display of Payments tab<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display the Payments Tab and display the ff columns:<br>        i. Payment Req Number<br>           i) Payment Request Number of the Requisition Slip<br>        ii. Supplier<br>           i) Supplier of the Item<br>        iii. Amount<br>           i) Amount of the Delivered Item<br>        iv. Status<br>           i) Payment Request Status</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5">PRS-023-022</td><td class="s5">Request History</td><td class="s5">Verify Request History of Returns tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Returns tab and Check Display of Returns tab<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display the Returns Tab and display the ff columns:<br>        i. Delivery Receipt Number<br>           i) Delevery Receipt Number of the Item<br>        ii. Item<br>           i) Requested Item<br>        iii. Supplier<br>           i) Supplier or Suppliers of the Item<br>        iv. Quantity Ordered<br>           i) Quantity of the Ordered Item<br>        v. Quantity Returned<br>           i) Quantity of the Returned Item<br>        vi. Return Date<br>           i) Date of the Item being Returned or the Delivery Date<br>        vii. Status<br>           i) Return Status</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s18" rowspan="4"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=890249601#gid=890249601">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=890249601#gid=890249601</a></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s5">PRS-023-023</td><td class="s5">Request History</td><td class="s5">Verify Request History of Items tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Items tab and Check Display of Items tab<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display the Items Tab and display ff the columns:<br>        i. Item Name<br>           i) Requested Item<br>        ii. Quantity Requested<br>           i) Requested Quantity per Item in the Requisition Slip<br>        iii. Quantity Ordered<br>           i) Quantity of the Ordered Item<br>              a) Should display as &quot;---&quot; If not applicable<br>        iv. Quantity Delivered<br>           i) Quantity of the Delivered Item<br>              a) Should display as &quot;---&quot; If not applicable<br>        v. Last Updated By<br>           i) The User who has updated or approved the Item Quantity<br>              a) Should display as &quot;---&quot; If not applicable</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s5">PRS-023-024</td><td class="s5">Request History</td><td class="s5">Verify Request History Pagination is working as expected in Canvassess tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. On Canvassess Tab, Check Display of table list per page of the table<br>5. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>6. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Should display 10 Rows per Page<br>5. Should display the previous or next page of the table<br>6. Should display the specific page selected with correct number of item entries in the table</td><td class="s14">Out of Scope</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">2/12/25 <br>As per Miss Irene:<br><br>Isahan lang ang Canvassing, walang way to create multiple Canvassing as of now. Other TC na requires Multiple Canvassing cannot be test at the moment<br><br>3/12: pagination is working as expected</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s5">PRS-023-025</td><td class="s5">Request History</td><td class="s5">Verify Request History Pagination is working as expected in Orders tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Orders tab and Check Display of table list per page of the table<br>5. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>6. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should display 10 Rows per Page<br>5. Should display the previous or next page of the table<br>6. Should display the specific page selected with correct number of item entries in the table<br></td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">PRS-023-026</td><td class="s5">Request History</td><td class="s5">Verify Request History Pagination is working as expected in Deliveries tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Deliveries tab and Check Display of table list per page of the table<br>5. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>6. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should display 10 Rows per Page<br>5. Should display the previous or next page of the table<br>6. Should display the specific page selected with correct number of item entries in the table<br></td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s5">PRS-023-027</td><td class="s5">Request History</td><td class="s5">Verify Request History Pagination is working as expected in Payments tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Payments tab and Check Display of table list per page of the table<br>5. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>6. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should display 10 Rows per Page<br>5. Should display the previous or next page of the table<br>6. Should display the specific page selected with correct number of item entries in the table<br></td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s5">PRS-023-028</td><td class="s5">Request History</td><td class="s5">Verify Request History Pagination is working as expected in Returns tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Returns tab and Check Display of table list per page of the table<br>5. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>6. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should display 10 Rows per Page<br>5. Should display the previous or next page of the table<br>6. Should display the specific page selected with correct number of item entries in the table<br></td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5">PRS-023-029</td><td class="s5">Request History</td><td class="s5">Verify Request History Pagination is working as expected in Items tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Items tab and Check Display of table list per page of the table<br>5. Click &quot;Next&quot; button (example: &lt;&gt;) on pagination<br>6. Click &quot;Number&quot; button (example: 2, 3,4...) on pagination</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should display 10 Rows per Page<br>5. Should display the previous or next page of the table<br>6. Should display the specific page selected with correct number of item entries in the table<br></td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5">PRS-023-030</td><td class="s5">Request History</td><td class="s5">Verify Request History Sorting is working as expected in Canvassess tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. On Canvassess Tab,  Click Sorting on &quot;Canvass Number&quot; column<br>5. Click sorting of  &quot;Item&quot; column<br>6. Click Sorting  of  &quot;Supplier&quot;  column<br>7. Click sorting of &quot;Price&quot; column<br>8. Click Sorting of &quot;Discount&quot; column<br>9. Click Sorting of &quot;Canvass Date&quot; column<br>10. Click Sorting of &quot;Status&quot; column</td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4. Canvass Number Column should be sorted correctly to -  0-9, 9-0<br>5. Item Column should be sorted correctly to  - 0-9, A-Z || 9-0, Z-A<br>6. Supplier Column should be sorted correctly to  - 0-9, A-Z || 9-0, Z-A<br>7. Price Column should be sorted correctly to - 0-9, 9-0<br>8. Discount Column should be sorted correctly to - 0-9, 9-0<br>9. Canvass Date Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>10 Status Column should be sorted correctly to  - A-Z, Z-A</td><td class="s14">Out of Scope</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s5">PRS-023-031</td><td class="s5">Request History</td><td class="s5">Verify Request History Sorting is working as expected in Orders tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Orders tab and Click Sorting on &quot;PO Number&quot; column<br>5. Click Sorting  of  &quot;Supplier&quot;  column<br>6. Click sorting of &quot;Purchase Order Price&quot; column<br>7. Click Sorting of &quot;Date Ordered&quot; column<br>8. Click Sorting of &quot;Status&quot; column<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should displayed Orders tab and PO Number Column should be sorted correctly to - 0-9, 9-0<br>5. Supplier Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>6. Purchase Order Price Column should be sorted correctly to - 0-9, 9-0<br>7. Date Ordered Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>8. Status Column should be sorted correctly to - A-Z, Z-A<br></td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s5">PRS-023-032</td><td class="s5">Request History</td><td class="s5">Verify Request History Sorting is working as expected in Deliveries tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Deliveries tab and Click Sorting on &quot;Delivery Receipt No&quot;. column<br>5. Click Sorting  of  &quot;Supplier&quot;  column<br>6. Click Sorting of &quot;Date Ordered&quot; column<br>7. Click Sorting of &quot;Qty Ordered&quot; column<br>8 Click Sorting of &quot;Date Delivered&quot; column<br>9 Click Sorting of &quot;Qty Delivered&quot; column<br>10. Click Sorting of &quot;Status&quot; column<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should displayed Deliveries tab and Delivery Receipt No. Column should be sorted correctly to - 0-9, 9-0<br>5. Supplier Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>6. Date Ordered Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>7. Quantity Ordered Column should be sorted correctly to - 0-9, 9-0<br>8. Date Delivered Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>9. Quantity Delivered Column should be sorted correctly to - 0-9, 9-0<br>10. Status Column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s5">PRS-023-033</td><td class="s5">Request History</td><td class="s5">Verify Request History Sorting is working as expected in Payments tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Payments tab and Click Sorting on  &quot;P.R. No.&quot; column<br>5. Click Sorting  of  &quot;Supplier&quot;  column<br>6. Click sorting of &quot;P.R. Amount&quot;  column<br>7. Click Sorting of &quot;Status&quot; column<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should displayed Payments tab and P.R. No. Column should be sorted correctly to - 0-9, 9-0<br>5. Supplier Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>6. Amount Column should be sorted correctly to - 0-9, 9-0<br>7. Status Column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s5">PRS-023-034</td><td class="s5">Request History</td><td class="s5">Verify Request History Sorting is working as expected in Returns tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Returns tab and Click Sorting on &quot;Delivery Receipt No&quot;. column<br>5. Click sorting of  &quot;Items&quot; column<br>6. Click Sorting  of  &quot;Supplier&quot;  column<br>7. Click Sorting of &quot;Qty Ordered&quot; column<br>8. Click Sorting of &quot;Qty Returned&quot; column<br>9. Click Sorting of &quot;Return Date&quot;  column<br>10. Click Sorting of &quot;Status&quot; column<br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should displayed Returns tab and Delivery Receipt No. Column should be sorted correctly to - 0-9, 9-0<br>5. Item Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>6. Supplier Column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>7. Quantity Ordered Column should be sorted correctly to - 0-9, 9-0<br>8. Quantity Returned Column should be sorted correctly to - 0-9, 9-0<br>9. Return Date Column should be sorted correctly to - Oldest Date-Latest Date, Latest Date-Oldest Date<br>10. Status Column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s5">PRS-023-035</td><td class="s5">Request History</td><td class="s5">Verify Request History Sorting is working as expected in Items tab</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as IT Admin<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1, On RS dasboard, click the History Icon of a Requisition Slip in the table list<br>2. Check Display of Request History page<br>3. Check Display of each tabs if no available data<br>4. Click Items tab and Click Sorting on &quot;Item Name&quot; column<br>5. Click sorting of  &quot;Qty. Requested&quot; column<br>6. Click sorting of  &quot;Qty. Ordered&quot; column<br>7. Click sorting of  &quot;Qty. Delivered&quot; column<br>8. Click Sorting  of  &quot;Last Updated By&quot;  column<br><br></td><td class="s7"></td><td class="s5">1. Should display the Request History Page<br>2. Should display the Requisition Slip Number and Should display the following Tabs and tables:<br>    a. Canvasses<br>    b. Orders<br>    c. Deliveries<br>    d. Payments<br>    e. Returns<br>    f. Items<br>3. Should display a No Data if there is no available Data for each Tabs<br>4.  Should displayed Items tab and Item Name column should be sorted correctly to - 0-9, A-Z || 9-0, Z-A<br>5. Quantity Requested  column should be sorted correctly to - 0-9, 9-0<br>6. Quantity Ordered  column should be sorted correctly to - 0-9, 9-0<br>7. Quantity Delivered  column should be sorted correctly to  - 0-9, 9-0<br>8. Last Updated By  column should be sorted correctly to - A-Z, Z-A</td><td class="s17">Not Run</td><td class="s9">Passed</td><td class="s19"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5">Recently completed feature therefore not yet tested and no data</td><td class="s7"></td></tr><tr style="height: 19px"><th id="1697858629R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s5">PRS-023-036</td><td class="s5">Request History</td><td class="s5">Verify Request History Icon is not visible with any User Type Access</td><td class="s5">Critical</td><td class="s6"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Account as the following User types:<br>     a. Engineers<br>     b. Supervisor<br>     c. Assistant Manager<br>     d. Department Head<br>     e. Department Secretary<br>     f. Division Head<br>     g. Area Staff<br>     h. Purchasing Staff<br>     i. Purchasing Head<br>     j. Management<br>4. Should have a Requisition Slip created and Submitted </a></td><td class="s5">1. On RS dasboard, Check the &quot;History Icon&quot; of a Requisition Slip in the table list</td><td class="s7"></td><td class="s5">1. Should not displayed the &quot;History Icon&quot; in the table list and users are not able to access it.</td><td class="s9">Passed</td><td class="s9">Passed</td><td class="s20"></td><td class="s10">Not Started</td><td class="s5"></td><td class="s5"></td><td class="s7"></td></tr></tbody></table></div>