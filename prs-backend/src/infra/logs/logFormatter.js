/**
 * Log Formatter
 *
 * This module provides formatters for different types of logs to ensure
 * consistent log structure.
 */

/**
 * Formats a log object for consistent structure
 *
 * @param {Object} log - Log object
 * @returns {Object} - Formatted log object
 */
function formatLog(log) {
  const { time, level, msg, ...rest } = log;

  return {
    timestamp: time || new Date().toISOString(),
    level,
    message: msg,
    ...rest,
  };
}

/**
 * Formats a request log with enhanced metadata
 *
 * @param {Object} req - Request object
 * @returns {Object} - Formatted request log
 */
function formatRequestLog(req) {
  const { method, url, headers, id, ip, query, params, body } = req;

  // Filter out sensitive information from headers
  const safeHeaders = { ...headers };

  if (safeHeaders.authorization) {
    safeHeaders.authorization = safeHeaders.authorization.replace(
      /^(Bearer\s+)(.+)$/,
      '$1[REDACTED]'
    );
  }

  if (safeHeaders.cookie) {
    safeHeaders.cookie = '[REDACTED]';
  }

  // Filter out sensitive information from body
  const safeBody = body ? sanitizeBody(body) : undefined;

  // Extract path without query string for easier filtering
  const path = url.split('?')[0];

  // Get client IP address (handle proxies)
  const clientIp = headers['x-forwarded-for'] || ip;

  return {
    request: {
      id,
      method,
      url,
      path,
      ip: clientIp,
      originalIp: ip !== clientIp ? ip : undefined,
      userAgent: headers['user-agent'],
      referer: headers['referer'],
      origin: headers['origin'],
      host: headers['host'],
      query,
      params,
      ...(safeBody && { body: safeBody }),
      timestamp: new Date().toISOString(),
    },
    headers: safeHeaders,
  };
}

/**
 * Sanitizes request body to remove sensitive information
 *
 * @param {Object} body - Request body
 * @returns {Object} - Sanitized body
 */
function sanitizeBody(body) {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'passwordConfirmation',
    'currentPassword',
    'newPassword',
    'token',
    'accessToken',
    'refreshToken',
    'secret',
    'apiKey',
    'apiSecret',
    'otp',
    'otpSecret',
    'credit_card',
    'creditCard',
  ];

  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

/**
 * Formats a response log
 *
 * @param {Object} reply - Response object
 * @param {Object} payload - Response payload
 * @returns {Object} - Formatted response log
 */
function formatResponseLog(reply, payload) {
  const { statusCode } = reply;
  const responseTime = reply.getResponseTime();

  // Don't log large payloads
  let safePayload;
  try {
    if (payload && typeof payload === 'string' && payload.length < 10000) {
      safePayload = JSON.parse(payload);

      // Redact sensitive fields in the response
      if (safePayload && typeof safePayload === 'object') {
        if (safePayload.token) safePayload.token = '[REDACTED]';
        if (safePayload.accessToken) safePayload.accessToken = '[REDACTED]';
        if (safePayload.refreshToken) safePayload.refreshToken = '[REDACTED]';
      }
    }
  } catch (e) {
    // If payload is not JSON, don't include it
    safePayload = payload ? `<${typeof payload}: length=${payload.length}>` : undefined;
  }

  return {
    response: {
      statusCode,
      responseTime,
      ...(safePayload && { body: safePayload }),
    },
  };
}

/**
 * Formats an error log
 *
 * @param {Error} error - Error object
 * @returns {Object} - Formatted error log
 */
function formatErrorLog(error) {
  if (!(error instanceof Error)) {
    return { error };
  }

  return {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...(error.code && { code: error.code }),
      ...(error.statusCode && { statusCode: error.statusCode }),
      ...(error.errorCode && { errorCode: error.errorCode }),
      ...(error.metadata && { metadata: error.metadata }),
    },
  };
}

/**
 * Formats a database log
 *
 * @param {Object} options - Database log options
 * @param {string} options.operation - Database operation
 * @param {string} options.model - Database model
 * @param {Object} options.query - Database query
 * @param {Array} options.parameters - Query parameters
 * @param {number} options.duration - Query duration in milliseconds
 * @returns {Object} - Formatted database log
 */
function formatDatabaseLog({ operation, model, query, parameters, duration }) {
  return {
    database: {
      operation,
      model,
      ...(query && { query }),
      ...(parameters && { parameters }),
      ...(duration && { duration }),
    },
  };
}

module.exports = {
  formatLog,
  formatRequestLog,
  formatResponseLog,
  formatErrorLog,
  formatDatabaseLog,
  sanitizeBody,
};
