#!/bin/bash

# Purge Cloudflare Cache Script
# This script purges the cache for specific URLs to fix the JavaScript module loading issue

echo "🔄 Purging Cloudflare cache for PRS frontend..."

# You need to set these environment variables:
# CLOUDFLARE_ZONE_ID - Your zone ID from Cloudflare dashboard
# CLOUDFLARE_API_TOKEN - Your API token with Cache Purge permissions

if [ -z "$CLOUDFLARE_ZONE_ID" ] || [ -z "$CLOUDFLARE_API_TOKEN" ]; then
    echo "❌ Error: Please set CLOUDFLARE_ZONE_ID and CLOUDFLARE_API_TOKEN environment variables"
    echo ""
    echo "To get these values:"
    echo "1. Go to Cloudflare dashboard"
    echo "2. Select your domain"
    echo "3. Zone ID is on the right sidebar"
    echo "4. API Token: Go to My Profile > API Tokens > Create Token"
    echo ""
    echo "Example usage:"
    echo "export CLOUDFLARE_ZONE_ID='your-zone-id'"
    echo "export CLOUDFLARE_API_TOKEN='your-api-token'"
    echo "./purge-cloudflare-cache.sh"
    exit 1
fi

# Purge specific URLs
curl -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/purge_cache" \
     -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data '{
       "files": [
         "https://prs.stratpoint.io/",
         "https://prs.stratpoint.io/index.html"
       ]
     }' | jq '.'

echo ""
echo "✅ Cache purge request sent!"
echo "⏳ It may take a few minutes for the cache to be fully purged."
echo "🔄 Try accessing https://prs.stratpoint.io/ again in a few minutes."
