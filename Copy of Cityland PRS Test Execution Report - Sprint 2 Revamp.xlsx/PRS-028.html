<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1237791441C0" style="width:103px;" class="column-headers-background">A</th><th id="1237791441C1" style="width:167px;" class="column-headers-background">B</th><th id="1237791441C2" style="width:262px;" class="column-headers-background">C</th><th id="1237791441C3" style="width:100px;" class="column-headers-background">D</th><th id="1237791441C4" style="width:233px;" class="column-headers-background">E</th><th id="1237791441C5" style="width:330px;" class="column-headers-background">F</th><th id="1237791441C6" style="width:76px;" class="column-headers-background">G</th><th id="1237791441C7" style="width:248px;" class="column-headers-background">H</th><th id="1237791441C8" style="width:128px;" class="column-headers-background">I</th><th id="1237791441C9" style="width:114px;" class="column-headers-background">J</th><th id="1237791441C10" style="width:120px;" class="column-headers-background">K</th><th id="1237791441C11" style="width:120px;" class="column-headers-background">L</th><th id="1237791441C12" style="width:120px;" class="column-headers-background">M</th><th id="1237791441C13" style="width:120px;" class="column-headers-background">N</th><th id="1237791441C14" style="width:78px;" class="column-headers-background">O</th><th id="1237791441C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1237791441R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(STG_wk4&amp;5)</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1237791441R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="16">PRS-028 - [Enhancements_1]  Root User - Search Function for User Management Landing Page<br>IT Admin - Search Function for User Management Landing Page<br>User Management: IT Admin - Add User Types Management - only 2 Users will be assigned<br>Sorting for Department Columns<br>Sorting for User Type and Department Columns<br>OFM Items: Landing Page - Sorting of Table<br>Non-OFM: View Non-OFM - Enable RS Search in Item Table<br>Non-OFM: Enable Filter Function in Landing Page<br>RS Dashboard: : Update RS Number to Reference Number<br>RS Creation: Update Reference Number for Draft</td></tr><tr style="height: 19px"><th id="1237791441R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-028-001</td><td class="s4">Root User - Search Function for User Management Landing Page</td><td class="s4">Validate Search for Username</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as Root User</a></td><td class="s4">1. On IT Management page, populate username that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Username that is existing on the table in search field<br>5. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Users Table <br>4. Field should be populated<br>5. Should displayed all matched Username/s</td><td class="s5" rowspan="20"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1TKawgBQNmv-8A_N9zvQ2ZC_S23xXb6utJXcos-qlQyk/edit?gid=0#gid=0">https://docs.google.com/spreadsheets/d/1TKawgBQNmv-8A_N9zvQ2ZC_S23xXb6utJXcos-qlQyk/edit?gid=0#gid=0</a></td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s5" rowspan="20"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=669150672#gid=669150672">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=669150672#gid=669150672</a></td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1237791441R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-028-002</td><td class="s4">Root User - Search Function for User Management Landing Page</td><td class="s4">Validate Search for First Name</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as Root User</a></td><td class="s4">1. On IT Management page, populate First Name that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate First Name that is existing on the table in search field<br>5. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Users Table <br>4. Field should be populated<br>5. Should displayed  all matched First Name/s</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1237791441R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-028-003</td><td class="s4">Root User - Search Function for User Management Landing Page</td><td class="s4">Validate Search for Last Name</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as Root User</a></td><td class="s4">1. On IT Management page, populate Last Name that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Last Name that is existing on the table in search field<br>5. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Users Table <br>4. Field should be populated<br>5. Should displayed all matched Last Name/s</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="1237791441R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">PRS-028-004</td><td class="s4">Root User - Search Function for User Management Landing Page</td><td class="s4">Validate Search by keyword</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as Root User</a></td><td class="s4">1. On IT Management page, populate a keyword that is not existing on the table in search field<br>2. Click &quot;Search&quot; button<br>3. Click Clear button<br>4. Populate Any keyword that is existing on the table in search field<br>5. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Field should be populated<br>2. Should displayed No Data in the table<br>3..Should reset the Search Field and Users Table <br>4. Field should be populated<br>5. Should displayed all matched List related to entered keyword</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1237791441R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-028-005</td><td class="s4">IT Admin - Search Function for User Management Landing Page</td><td class="s4">Validate Search for Username</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Populate Username that is not existing on the table in search field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate Username that is existing on the table in search field<br>7. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Search Field and Users Table <br>6. Field should be populated<br>7. Should displayed all matched Username/s</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1237791441R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-028-006</td><td class="s4">IT Admin - Search Function for User Management Landing Page</td><td class="s4">Validate Search for First Name</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Populate First Name that is not existing on the table in search field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate First Name that is existing on the table in search field<br>7. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Search Field and Users Table <br>6. Field should be populated<br>7. Should displayed all matched First Name/s</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1237791441R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4">PRS-028-007</td><td class="s4">IT Admin - Search Function for User Management Landing Page</td><td class="s4">Validate Search for Last Name</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Populate Last Name that is not existing on the table in search field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate Last Name that is existing on the table in search field<br>7. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Search Field and Users Table <br>6. Field should be populated<br>7. Should displayed all matched Last Name/s</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1237791441R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4">PRS-028-008</td><td class="s4">IT Admin - Search Function for User Management Landing Page</td><td class="s4">Validate Search by keyword</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Populate a Keyword that is not existing on the table in search field<br>4. Click &quot;Search&quot; button<br>5. Click Clear button<br>6. Populate Keyword that is existing on the table in search field<br>7. Click &quot;Search&quot; button</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Field should be populated<br>4. Should displayed No Data in the table<br>5..Should reset the Search Field and Users Table <br>6. Field should be populated<br>7. Should displayed all matched List related to entered keyword</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1237791441R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-028-009</td><td class="s4">User Management: IT Admin - Add User Types Management - only 2 Users will be assigned</td><td class="s4">Validate access of Management</td><td class="s4">Critical</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User accounts are created</a></td><td class="s4">1. Open the Cityland Purchase Request System application website<br>2. Populate valid Login Credentials (Username and Password)<br>3. Click Login button<br>4. On OTP form, enter 6 digit numbers OTP code generated from Google Authenticator App<br>5. Click &quot;Validate&quot; button<br>6. Check user access on the app</td><td class="s6"></td><td class="s4">1. User should be able to login<br>2. User should have an access to the ff:<br>   a. Manage Requisition Slip<br>     -View RS Dashboard<br>     -Create RS<br>     -Save RS as Draft<br>     -Submit RS<br>     -View RS Details - Main<br>     -View RS Details - Related Documents<br>     -Edit RS<br>   b. Manage Canvass<br>     -View Canvass<br>     -Canvass Approval<br>   c. Manage Purchase Orders<br>     -View Purchase Order<br>   d. Manage Delivery Record<br>     -Create Delivey Record<br>     -View Delivery Record<br>     -Save Delivery Record as Draft<br>     -Delivery Record Submission<br>   e. Manage Payment Request<br>     -View Payment  Request<br>   f. Non-RS Payment<br>     -Create Non-Payment  Request<br>     -View Non-Payment  Request<br>     -Save Non-Payment  Request as Draft<br>     -Non-Payment  Request Submission<br>   g. Others<br>      -User Profile<br>     -Notification Bell</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4">6/7: OTP in staging is disabled</td><td class="s6"></td></tr><tr style="height: 113px"><th id="1237791441R11" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">12</div></th><td class="s4">PRS-028-010</td><td class="s4">Sorting for Department Columns</td><td class="s4">Validate Department column name sorting is working as expected</td><td class="s4">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as Root User</a></td><td class="s4">1. On IT Management page, click sort button in Department  column Name<br>2. Click Again the sort button in Department  column Name</td><td class="s6"></td><td class="s4">1. Should Sort the Department by Ascending (Blank-A-Z)<br>2. Should Sort the Department by Descending (Z-A- Blank)</td><td class="s11">Failed</td><td class="s12">Failed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s4">Bug has been been raised<br><br>3/7: Incorrect sorting still occurred</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-910">https://youtrack.stratpoint.com/issue/CITYLANDPRS-910</a></td></tr><tr style="height: 19px"><th id="1237791441R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">PRS-028-011</td><td class="s4">Sorting for Department Columns</td><td class="s4">Validate Department column name sorting  when reset</td><td class="s4">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as Root User</a></td><td class="s4">1. On IT Management page, click sort button in Department  column Name twice<br>2. Click Again the sort button in Department  column Name</td><td class="s6"></td><td class="s4">1. Should Sort the Department by blank-A-Z, Z-A-blank<br>2. Should reset the the Table to original/default Sorting</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1237791441R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">PRS-028-012</td><td class="s4">Sorting for User Type and Department Columns</td><td class="s4">Validate User Type column name sorting is working as expected</td><td class="s4">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Click sort button in User type  column Name<br>2. Click Again the sort button in User Type  column Name</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Should Sort the User Type by Ascending (A-Z)<br>4. Should Sort the User Type by Descending (Z-A)</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1237791441R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">PRS-028-013</td><td class="s4">Sorting for User Type and Department Columns</td><td class="s4">Validate User Type  column name sorting  when reset</td><td class="s4">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3.,Click sort button in User Type column Name twice<br>2. Click Again the sort button in User Type  column Name</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Should Sort the User type by A-Z, Z-A<br>4. Should reset the the Table to original/default Sorting</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1237791441R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">PRS-028-014</td><td class="s4">Sorting for User Type and Department Columns</td><td class="s4">Validate Department column name sorting is working as expected</td><td class="s4">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3. Click sort button in  Department column Name<br>2. Click Again the sort button in Department column Name</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Should Sort the Department by Ascending (A-Z)<br>4. Should Sort the Department by Descending (Z-A)</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1237791441R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-028-015</td><td class="s4">Sorting for User Type and Department Columns</td><td class="s4">Validate Department column name sorting  when reset</td><td class="s4">Minor</td><td class="s5"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin</a></td><td class="s4">1. Click &quot;Admin&quot; dropdown<br>2. Click &quot;User Management&quot; sub menu<br>3.,Click sort button in Department column Name twice<br>2. Click Again the sort button in Department column Name</td><td class="s6"></td><td class="s4">1. Should display a Sub-Menus for &quot;Approval Management&quot; and &quot;User Management&quot;<br>2. Should navigated to &quot;User management&quot; page<br>3. Should Sort the Department by A-Z, Z-A<br>4. Should reset the the Table to original/default Sorting</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s6"></td></tr><tr style="height: 19px"><th id="1237791441R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s13">PRS-028-016</td><td class="s13">RS Dashboard: : Update RS Number to Reference Number</td><td class="s13">Validate RS Number Column Name change into &quot;Ref Number&quot; in RS Dashboard All tab</td><td class="s13">Minor</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user</a></td><td class="s13">1. On RS Dashboard, check column name for &quot;Ref. Number&quot;<br>2. Check if all Requisition Slips and other Related Documents is displayed on the list such as:<br>    a. Canvass<br>    b. Purchase Order<br>    c. Delivery Receipt<br>    d. Payment Request</td><td class="s15"></td><td class="s13">1. Should display the &quot;Ref. Number&quot; Column Name in the first column of the table <br>2. Should displayed all Requisition Slips and other Related Documents of all Users</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="1237791441R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s13">PRS-028-017</td><td class="s13">RS Dashboard: : Update RS Number to Reference Number</td><td class="s13">Validate RS Number Column Name change into &quot;Ref Number&quot; in RS Dashboard For My Approval/Assigned To Me Tab</td><td class="s13">Minor</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user</a></td><td class="s13">1. On RS Dashboard, Click For My Approval/Assigned To Me Tab<br>2. Check column name for &quot;Ref. Number&quot;<br>3. Check if all Requisition Slips and other Related Documents is displayed on the list such as:<br>    a. Canvass<br>    b. Purchase Order<br>    c. Delivery Receipt<br>    d. Payment Request</td><td class="s15"></td><td class="s13">1. Should displayed For My Approval/Assigned To Me Tab dashboard<br>2. Should display the &quot;Ref. Number&quot; Column Name in the first column of the table <br>3. Should displayed all Requisition Slips and other Related Documents that is subject for a User&#39;s approval or assigned</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s13">3/7: My Approval/Assigned tab is currently disbaled due to envi issue<br><br>3/10: My Approval/Assigned tab is enabled</td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1063">3/7: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1063</a></td></tr><tr style="height: 19px"><th id="1237791441R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s13">PRS-028-018</td><td class="s13">RS Dashboard: : Update RS Number to Reference Number</td><td class="s13">Validate RS Number Column Name change into &quot;Ref Number&quot; in RS Dashboard For My Requests Tab</td><td class="s13">Minor</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user</a></td><td class="s13">1. On RS Dashboard, Click My Requests Tab<br>2. Check column name for &quot;Ref. Number&quot;<br>3. Check if all Requisition Slips and other Related Documents is displayed on the list such as:<br>    a. Canvass<br>    b. Purchase Order<br>    c. Delivery Receipt<br>    d. Payment Request</td><td class="s15"></td><td class="s13">1. Should displayed My Requests Tab dashboard<br>2. Should display the &quot;Ref. Number&quot; Column Name in the first column of the table <br>3. Should displayed all Requisition Slips and other Related Documents that the User&#39;s Created or Requests</td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="1237791441R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s13">PRS-028-019</td><td class="s13">RS Creation: Update Reference Number for Draft</td><td class="s13">Validate Reference Number when save as Draft</td><td class="s13">Minor</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user </a></td><td class="s13">1. On RS dashboard, click &quot;New Request&quot; button<br>2. Populate all required fields and add items<br>3. Click &quot;Save Draft&quot; button</td><td class="s15"></td><td class="s13"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display the RS Create form<br>2. All fields should be populated  and added items<br>3. Should save the Requisition Slip as a Draft and nominate a Temporary RS Number<br>    a. </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Format RS-TMP-[Company Code + Alphabet + 8-incremental digit]</span></td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="1237791441R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s13">PRS-028-020</td><td class="s13">RS Creation: Update Reference Number for Draft</td><td class="s13">Validate Reference Number when edit save Draft</td><td class="s13">Minor</td><td class="s14"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User already logged in successfully to their Account as IT Admin or Any user (Requestor)</a></td><td class="s13">1. On RS dashboard, click &quot;Ref. Number&quot; link of RS with draft status<br>2. Update any of the fields or items<br>3. Click &quot;Save Draft&quot; button</td><td class="s15"></td><td class="s13"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">1. Should display the Editable RS Draft form<br>2. Fields or items should be updated<br>3. Should successfully save the RS Draft with Temporary RS Number<br>    a.</span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;"> Format RS-TMP-[Company Code + Alphabet + 8-incremental digit]</span></td><td class="s7">Passed</td><td class="s8">Passed</td><td class="s9">Not Started</td><td class="s10"></td><td class="s15"></td><td class="s15"></td></tr></tbody></table></div>