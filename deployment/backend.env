# Application config/secrets
PORT=4000
HOST=0.0.0.0
JWT_SECRET=secret
NODE_ENV=production
OTP_KEY=U29tZVNlY3JldEtleVdpdGg2NEJ5dGVz
PASS_SECRET=12345
BYPASS_OTP=true
LOG_LEVEL=info
DISABLE_REQUEST_LOGS=true

# Database configs
POSTGRES_SERVICE=postgres
POSTGRES_HOST=postgres
POSTGRES_DB=prs_db
POSTGRES_PORT=5432
POSTGRES_USER=admin
POSTGRES_PASSWORD=Z50egW9jUyLMpCCdb7v6KTdpn9tpQUDg5t93FnWAjQ0=
DIALECT=postgres
POOL_MIN=0
POOL_MAX=10
POOL_ACQUIRE=30000
POOL_IDLE=10000
POOL_EVICTION=20000
DISABLE_SSL=true

# Redis config
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# Root User
ROOT_USER_NAME=rootuser
ROOT_USER_EMAIL=<EMAIL>
ROOT_USER_PASSWORD=rootuser

# API Integration
CITYLAND_API_URL=https://cmd-test.free.beeceptor.com
CITYLAND_ACCOUNTING_URL=https://cityland-accounting.free.beeceptor.com

# Department Association
ASSOCIATION_DEPARTMENT_CODE=10

# MinIO Configuration
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=${MINIO_ROOT_USER}
MINIO_SECRET_KEY=${MINIO_ROOT_PASSWORD}
MINIO_BUCKET=prs-uploads
