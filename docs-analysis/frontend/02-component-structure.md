# Component Structure

## Overview

The PRS-Frontend uses a component-based architecture with a mix of shared UI components and feature-specific components. This document provides a detailed analysis of the component structure, including component organization, composition patterns, and reusability.

## Component Organization

The components in the PRS-Frontend are organized into several categories:

### 1. Shared UI Components

Located in `src/components/ui`, these are generic, reusable components that form the building blocks of the UI:

```
components/ui/
├── Button/           # Button components
├── Form/             # Form components
├── Modal/            # Modal components
├── Table/            # Table components
├── Tabs/             # Tab components
├── Spinner/          # Loading spinner
├── InfoCard/         # Information card
├── Pill/             # Status pill
└── ...               # Other UI components
```

### 2. Layout Components

Located in `src/components/layouts`, these components define the overall structure of the application:

```
components/layouts/
├── AppLayout/        # Main application layout
├── AuthFlowWrapper/  # Authentication flow layout
├── Sidebar/          # Sidebar navigation
├── Header/           # Application header
└── ...               # Other layout components
```

### 3. Error Components

Located in `src/components/errors`, these components handle error states:

```
components/errors/
├── Main.jsx          # Main error fallback
├── NotFound.jsx      # 404 page
└── ...               # Other error components
```

### 4. Feature-Specific Components

Located within each feature directory, these components are specific to a particular feature:

```
features/dashboard/components/
├── RSMainTab.jsx     # Requisition slip main tab
├── ItemsTable.jsx    # Items table
├── StatusSection.jsx # Status section
└── ...               # Other dashboard components

features/auth/components/
├── LoginForm.jsx     # Login form
├── OTPForm.jsx       # OTP verification form
└── ...               # Other auth components
```

## Component Composition Patterns

The application uses several component composition patterns:

### 1. Container/Presentation Pattern

Components are often split into container components (which handle data fetching and state) and presentation components (which render the UI):

```jsx
// Container component
const CompanyListContainer = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const { data, isLoading } = useGetCompanies({ page, limit });

  return (
    <CompanyList
      companies={data?.data}
      isLoading={isLoading}
      onPageChange={setPage}
      onLimitChange={setLimit}
    />
  );
};

// Presentation component
const CompanyList = ({ companies, isLoading, onPageChange, onLimitChange }) => {
  if (isLoading) return <Spinner />;
  
  return (
    <Table
      data={companies}
      onPageChange={onPageChange}
      onLimitChange={onLimitChange}
    />
  );
};
```

### 2. Compound Components

Some components use the compound component pattern to provide a more flexible API:

```jsx
// Example of compound components
<Form onSubmit={handleSubmit}>
  <Form.Input name="username" label="Username" />
  <Form.Input name="password" type="password" label="Password" />
  <Button type="submit">Login</Button>
</Form>
```

### 3. Higher-Order Components (HOCs)

The application uses HOCs for cross-cutting concerns like authentication:

```jsx
// Example of HOC
export const ProtectedRoute = ({ children }) => {
  const { token, type } = useTokenStore();

  if (!token && type !== 'auth') {
    return <Navigate to="/login" replace />;
  }

  return children;
};
```

### 4. Render Props

Some components use render props for flexible rendering:

```jsx
// Example of render props
<AuthLoader
  renderLoading={() => (
    <div className="flex h-screen w-screen items-center justify-center">
      <Spinner size="xl" />
    </div>
  )}
>
  {children}
</AuthLoader>
```

## Key Components Analysis

### 1. Button Component

The Button component is a versatile UI element with multiple variants and states:

```jsx
// From src/components/ui/Button/Button.jsx
const Button = forwardRef(
  (
    {
      className,
      variant,
      hover,
      size,
      as: Component = 'button',
      children,
      isLoading,
      icon: Icon,
      iconPosition = 'R',
      iconSize,
      label = '',
      ...props
    },
    ref,
  ) => {
    const buttonContent = (
      <Component
        className={cn(buttonVariants({ variant, hover }), className)}
        ref={ref}
        {...props}
      >
        {isLoading && <Spinner size="sm" className="text-current mr-2" />}
        {Icon && iconPosition === 'L' && (
          <Icon className={cn('mr-2 h-4 w-4', iconSize)} />
        )}
        {children && (
          <span className="truncate overflow-hidden">{children}</span>
        )}
        {Icon && iconPosition !== 'L' && (
          <Icon className={cn('ml-2 h-4 w-4', iconSize)} />
        )}
      </Component>
    );

    return !!label ? (
      <FieldWrapper label={label}>{buttonContent}</FieldWrapper>
    ) : (
      buttonContent
    );
  },
);
```

**Strengths:**
- Supports multiple variants and sizes
- Handles loading states
- Supports icons with customizable positioning
- Can be rendered as different HTML elements
- Supports form labels

**Weaknesses:**
- Complex conditional rendering
- Mixing presentation and layout concerns

### 2. Form Components

The Form components provide a comprehensive solution for form handling:

```jsx
// From src/components/ui/Form/Form.jsx
export const Form = ({ children, onSubmit, className, ...props }) => {
  return (
    <form
      onSubmit={onSubmit}
      className={cn('space-y-6', className)}
      {...props}
    >
      {children}
    </form>
  );
};

// From src/components/ui/Form/Input.jsx
const Input = forwardRef(
  (
    {
      className,
      type,
      label,
      error,
      registration,
      placeholder,
      hasIcon = false,
      renderIcon: RenderIcon = null,
      iconClassName = '',
      isSearch = false,
      beforeIcon: BeforeIcon = null,
      iconHandler = () => {},
      columnSpan = 1,
      ...props
    },
    ref,
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const isPasswordType = type === 'password';
    const inputType = isPasswordType
      ? showPassword
        ? 'text'
        : 'password'
      : type;

    const inputContent = (
      <div className={cn('relative', `col-span-${columnSpan}`)}>
        {isSearch && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <SearchIcon className="w-4 h-4 text-gray-500" />
          </div>
        )}
        {beforeIcon && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <BeforeIcon className="w-4 h-4 text-gray-500" />
          </div>
        )}
        <input
          type={inputType}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            isSearch && 'pl-10',
            beforeIcon && 'pl-10',
            className,
          )}
          ref={ref}
          placeholder={placeholder}
          {...registration}
          {...props}
        />
        {hasIcon && RenderIcon && (
          <div
            className={cn(
              'absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer',
              iconClassName,
            )}
            onClick={iconHandler}
          >
            <RenderIcon className="w-4 h-4" />
          </div>
        )}
        {isPasswordType && (
          <div
            className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <VisibleEyeIcon className="w-4 h-4" />
            ) : (
              <NonVisibleEyeIcon className="w-4 h-4" />
            )}
          </div>
        )}
      </div>
    );

    return label || error ? (
      <FieldWrapper label={label} error={error}>
        {inputContent}
      </FieldWrapper>
    ) : (
      inputContent
    );
  },
);
```

**Strengths:**
- Comprehensive form component ecosystem
- Support for validation and error handling
- Flexible input types with icons
- Consistent styling and behavior

**Weaknesses:**
- Complex conditional rendering
- Mixing presentation and behavior concerns
- Limited abstraction of form state management

### 3. RSMainTab Component

The RSMainTab component is a complex feature-specific component that handles the main tab of a requisition slip:

```jsx
// From src/features/dashboard/components/RSMainTab.jsx
export const RSMainTab = ({
  setPage,
  setLimit,
  setSort,
  currentSort,
  isForApprover,
  isRequestor,
}) => {
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  // ... many state variables and hooks

  const {
    requisitionItems,
    steelbarItems,
    updateRequisitionNote,
    updateRequisitionQuantity,
    removeSteelbarItem,
    removeRequisitionItem,
    clearRequisitionItemsStore,
    removeSubmittedRequisitionItem,
    addRequisitionItem,
    addSubmittedRequisitionItem,
    addSteelbarItem,
    updateSteelbarQuantity,
    updateSteelbarNote,
    setAddItemMode,
    addItemMode,
  } = useRequisitionItemsStore();

  // ... many API calls and handlers

  return (
    <div className="flex flex-col gap-4">
      {/* ... complex UI rendering */}
    </div>
  );
};
```

**Strengths:**
- Encapsulates a complex feature
- Handles multiple states and interactions
- Integrates with the API and state management

**Weaknesses:**
- Very large component with too many responsibilities
- Mixes data fetching, state management, and UI rendering
- Difficult to test and maintain
- Limited reusability

## Component Reusability

The application has a mix of highly reusable components and feature-specific components:

### Highly Reusable Components

1. **UI Components**: Button, Input, Select, Table, Modal, etc.
2. **Layout Components**: AppLayout, AuthFlowWrapper, etc.
3. **Error Components**: MainErrorFallback, NotFound, etc.

These components are designed to be reusable across the application and have clear, focused responsibilities.

### Feature-Specific Components

1. **Dashboard Components**: RSMainTab, ItemsTable, StatusSection, etc.
2. **Auth Components**: LoginForm, OTPForm, etc.
3. **Company Components**: CompanyList, CompanyForm, etc.

These components are specific to a particular feature and often contain business logic that limits their reusability.

## Component Issues

### 1. Large Components

Some components, like RSMainTab, are very large and have too many responsibilities. This makes them difficult to understand, test, and maintain.

### 2. Prop Drilling

Some components pass props through multiple levels of the component tree, which can make the code harder to understand and maintain.

### 3. Mixed Concerns

Some components mix multiple concerns, such as data fetching, state management, and UI rendering. This violates the single responsibility principle.

### 4. Inconsistent Patterns

The application uses a mix of component patterns, which can make it harder for developers to understand and follow the codebase.

## Recommendations

### 1. Break Down Large Components

Split large components into smaller, more focused components with clear responsibilities:

```jsx
// Before: Large component with multiple responsibilities
export const RSMainTab = ({ /* props */ }) => {
  // ... many state variables and hooks
  // ... many API calls and handlers
  return (
    <div>
      {/* ... complex UI rendering */}
    </div>
  );
};

// After: Smaller, more focused components
export const RSMainTab = ({ /* props */ }) => {
  return (
    <div>
      <RSHeader />
      <RSDetails />
      <RSItems />
      <RSActions />
    </div>
  );
};
```

### 2. Extract Business Logic

Move business logic from components to custom hooks or services:

```jsx
// Before: Component with business logic
export const RSMainTab = ({ /* props */ }) => {
  // ... business logic
  return (
    <div>
      {/* ... UI rendering */}
    </div>
  );
};

// After: Component with extracted business logic
export const useRSMainTab = ({ /* props */ }) => {
  // ... business logic
  return {
    // ... data and handlers
  };
};

export const RSMainTab = ({ /* props */ }) => {
  const { data, handlers } = useRSMainTab({ /* props */ });
  return (
    <div>
      {/* ... UI rendering */}
    </div>
  );
};
```

### 3. Use Context for Deeply Nested Components

Use React Context to avoid prop drilling in deeply nested component trees:

```jsx
// Before: Prop drilling
export const RSMainTab = ({ /* props */ }) => {
  return (
    <div>
      <RSHeader {...props} />
      <RSDetails {...props} />
      <RSItems {...props} />
      <RSActions {...props} />
    </div>
  );
};

// After: Context provider
export const RSContext = createContext();

export const RSMainTab = ({ /* props */ }) => {
  return (
    <RSContext.Provider value={/* props */}>
      <div>
        <RSHeader />
        <RSDetails />
        <RSItems />
        <RSActions />
      </div>
    </RSContext.Provider>
  );
};
```

### 4. Standardize Component Patterns

Adopt consistent component patterns across the application:

```jsx
// Standardized component pattern
export const SomeComponent = ({ /* props */ }) => {
  // 1. State and hooks
  const [state, setState] = useState();
  const { data } = useSomeQuery();

  // 2. Derived state
  const derivedState = useMemo(() => {
    // ... derive state from props and data
  }, [props, data]);

  // 3. Event handlers
  const handleSomeEvent = useCallback(() => {
    // ... handle event
  }, [/* dependencies */]);

  // 4. Render
  return (
    <div>
      {/* ... UI rendering */}
    </div>
  );
};
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
