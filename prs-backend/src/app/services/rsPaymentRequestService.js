const { convertDateToDDMMMYYYY } = require('../utils');

class RsPaymentRequestService {
  constructor(container) {
    const {
      userRepository,
      roleRepository,
      requisitionRepository,
      rsPaymentRequestApproverRepository,
      purchaseOrderItemRepository,
      db,
      utils,
      entities,
      clientErrors,
      fastify,
      rsPaymentRequestRepository,
      commentRepository,
      constants,
      attachmentService,
      commentService,
      purchaseOrderService,
      deliveryReceiptService,
      purchaseOrderRepository,
      deliveryReceiptRepository,
      notificationService,
      deliveryReceiptInvoiceRepository,
    } = container;

    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.requisitionRepository = requisitionRepository;
    this.rsPaymentRequestApproverRepository =
      rsPaymentRequestApproverRepository;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.db = db;
    this.utils = utils;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.fastify = fastify;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.constants = constants;
    this.commentRepository = commentRepository;
    this.attachmentService = attachmentService;
    this.commentService = commentService;
    this.purchaseOrderService = purchaseOrderService;
    this.deliveryReceiptService = deliveryReceiptService;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.deliveryReceiptInvoiceRepository = deliveryReceiptInvoiceRepository;
    this.notificationService = notificationService;
  }

  async generatePRNumberCode(isDraft = false) {
    let whereClause;
    const numberField = isDraft === true ? 'prDraftNumber' : 'prNumber';
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    if (isDraft === true) {
      whereClause = {
        status: RS_PAYMENT_REQUEST_STATUS.DRAFT,
      };
    } else {
      whereClause = {
        status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
      };
    }

    const lastPr = await this.rsPaymentRequestRepository.findOne({
      where: whereClause,
      order: [[numberField, 'DESC']],
    });

    const lastNumber = lastPr?.[numberField] ?? '0';
    const lastLetter = lastPr?.prLetter ?? 'AA';
    const nextNumber =
      lastNumber === '99999999'
        ? '0'.padStart(8, '0')
        : (parseInt(lastNumber) + 1).toString().padStart(8, '0');
    const nextLetter =
      lastNumber === '99999999'
        ? this.utils.incrementLetters(lastLetter)
        : lastLetter;

    return {
      prNumber: nextNumber,
      prLetter: nextLetter,
    };
  }

  async createRsPaymentRequest(request) {
    const { transaction, userFromToken, ...payload } = request;
    const numberField = payload.isDraft === true ? 'prDraftNumber' : 'prNumber';
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    const { prLetter, prNumber } = await this.generatePRNumberCode(
      payload.isDraft,
    );

    // comment for testing
    const { termsData } = await this.getPurchaseOrderDetails({
      id: payload.purchaseOrderId,
      employeeId: payload?.termsData?.employeeId,
      userFromToken,
    });

    const result = await this.rsPaymentRequestRepository.create(
      {
        ...payload,
        prLetter,
        //comment for testing
        termsData,
        [numberField]: prNumber,
        status:
          payload.isDraft === true
            ? RS_PAYMENT_REQUEST_STATUS.DRAFT
            : RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
      },
      transaction,
    );

    if (payload.isDraft === false) {
      await this.requisitionRepository.update(
        { id: payload.requisitionId },
        { status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL },
        { transaction },
      );
    }

    return result;
  }

  async createComment(request) {
    const { comment, id, userFromToken, transaction } = request;
    const { MODELS } = this.constants.attachment;

    this.fastify.log.info(`Adding comment to RS Payment Request`);
    this.fastify.log.info(`Checking if Payment Request is valid`);
    const paymentRequest = await this.rsPaymentRequestRepository.getById(id);

    if (!paymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `RS Payment Request with ID: ${id} not found`,
      });
    }

    const result = await this.commentService.createComment({
      transaction,
      model: MODELS.RS_PAYMENT_REQUEST,
      userId: userFromToken.id,
      comment: comment,
      modelId: id,
    });

    this.fastify.log.info(
      `Successfully added Comment RS Payment Request with ID: ${id}`,
    );

    return result;
  }

  async createAttachment(request) {
    const { transaction, attachments, id, userFromToken } = request;
    const { MODELS } = this.constants.attachment;

    this.fastify.log.info(`Adding Attachments to RS Payment Request`);
    this.fastify.log.info(`Checking if Payment Request is valid`);
    const paymentRequest = await this.rsPaymentRequestRepository.getById(id);

    if (!paymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `RS Payment Request with ID: ${id} not found`,
      });
    }

    const result = await this.attachmentService.createAttachments({
      attachments,
      transaction,
      model: MODELS.RS_PAYMENT_REQUEST,
      parentPath: MODELS.RS_PAYMENT_REQUEST,
      userId: userFromToken.id,
      modelId: id,
    });

    this.fastify.log.info(
      `Successfully added Attachment/s RS Payment Request with ID: ${id}`,
    );

    return result;
  }

  async getPurchaseOrderDetails(request) {
    const { id, userFromToken, employeeId } = request;
    const purchaseOrderDetails =
      await this.purchaseOrderService.getPODetailsSupplierWarranty(id);

    if (!purchaseOrderDetails) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID: ${id} not found`,
      });
    }

    const deliveryReceipt =
      await this.deliveryReceiptService.getDeliveryReceiptByPOId(id);

    let deliveryInvoice = null;
    let drDetails = null;

    if (deliveryReceipt) {
      deliveryInvoice = await this.deliveryReceiptInvoiceRepository.findOne(
        { where: { deliveryReceiptId: deliveryReceipt.id } },
      );

      if (deliveryInvoice) {
        drDetails = await this.deliveryReceiptRepository.getById(
          deliveryInvoice.deliveryReceiptId,
        );
      }
    } else {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order is not Associated with Delivery Receipt`,
      });
    }

    const terms = await this.utils.Terms({
      terms: purchaseOrderDetails.terms,
      employeeId,
      totalAmount: deliveryReceipt.invoice?.totalSales,
      deposit: purchaseOrderDetails?.depositPercent,
      issuedInvoiceDate: deliveryReceipt.issuedInvoiceDate,
      userFromToken,
    });
    return {
      ...purchaseOrderDetails,
      termsData: terms,
      deliveryInvoice: deliveryInvoice || null,
      invoiceAttachment: drDetails?.invoice?.invoiceAttachment || null,
    };
  }

  async getPaymentRequestById(id) {
    const paymentRequest =
      await this.rsPaymentRequestRepository.getPaymentRequestById(id);

    if (!paymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Payment request not found',
      });
    }

    const employeeId = parseInt(paymentRequest?.termsData?.employeeId);
    if (employeeId) {
      const employee = await this.userRepository.getById(employeeId, {
        paranoid: false,
        attributes: ['id', 'firstName', 'lastName'],
      });

      paymentRequest.employee = employee;
    }

    return paymentRequest;
  }

  async getPRItems(purchaseOrderId, search, sort = {}, queries = {}, type) {
    const prItems =
      await this.purchaseOrderItemRepository.getItemsForPaymentRequest({
        ...queries,
        search,
        paginate: false,
        sort: sort || {},
        purchaseOrderId,
        type,
      });

    return prItems;
  }

  async getExistingPR(id) {
    const existingPR = await this.rsPaymentRequestRepository.getById(id);

    if (!existingPR) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Payment request not found',
      });
    }

    return existingPR;
  }

  async submit(request) {
    this.fastify.log.info(`Submitting Payment Request...`);
    const { userFromToken, body } = request;
    const { attachments, ...details } = { ...body };
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;
    let newBody = { ...details };
    let paymentRequestId;
    let validatePr;

    this.fastify.log.info(`Checking if Payment Request new or Draft`);

    if (details.id) {
      this.fastify.log.info(`Checking if Payment Request is a Draft`);
      const { id, ...newDetails } = { ...details };
      newBody = newDetails;
      paymentRequestId = id;
    }

    const parsedDetails = this.utils.parseDomain(
      this.entities.rsPaymentRequest.submitRsPaymentRequestSchema,
      newBody,
    );

    if (paymentRequestId) {
      validatePr = await this.rsPaymentRequestRepository.findOne({
        where: {
          id: paymentRequestId,
        },
      });
    }

    const transaction = await this.db.sequelize.transaction();

    // no payment request found create a submitted payment request
    if (!validatePr) {
      this.fastify.log.info(`New RS Payment Request`);
      this.fastify.log.info(`Creating RS Payment Request...`);

      let attachment;
      let comment;

      try {
        const paymentRequest = await this.createRsPaymentRequest({
          ...parsedDetails,
          transaction,
        });

        if (parsedDetails.comments) {
          comment = await this.createComment({
            id: paymentRequest.id,
            comment: parsedDetails.comments,
            userFromToken,
            transaction,
          });
        }

        if (attachments?.length) {
          attachment = await this.createAttachment({
            transaction,
            userFromToken,
            attachments,
            id: paymentRequest.id,
          });
        }

        this.fastify.log.info(`Generating PR Approvers - Create PR flow`);
        await this.#generatePRApprovers({
          transaction,
          paymentRequest,
        });

        await transaction.commit();

        this.fastify.log.info(`Created RS Payment Request Successfully`);
        return { paymentRequest, attachment, comment };
      } catch (error) {
        this.fastify.log.info(`Creation of RS Payment Request Failed`);
        this.fastify.log.info(`Initiating Rollback...`);
        await transaction.rollback();
        this.fastify.log.info(`Rollback Successful`);
        throw error;
      }
    }

    // if payment request found and status of draft or rejected
    if (
      validatePr &&
      validatePr.status === RS_PAYMENT_REQUEST_STATUS.SUBMITTED
    ) {
      throw this.clientErrors.NOT_FOUND({
        message: `Payment request with ID of ${paymentRequestId} is already submitted.`,
      });
    }

    this.fastify.log.info(`Payment Request is a draft. Submitting...`);
    const { prLetter, prNumber } = await this.generatePRNumberCode(
      parsedDetails.isDraft,
    );

    let result;
    try {
      const numberField =
        parsedDetails.isDraft === true ? 'prDraftNumber' : 'prNumber';
      this.fastify.log.info(`Updating submitted payment request`);
      result = await this.rsPaymentRequestRepository.update(
        { id: paymentRequestId },
        {
          ...parsedDetails,
          status:
            parsedDetails.isDraft === true
              ? RS_PAYMENT_REQUEST_STATUS.DRAFT
              : RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
          prLetter,
          [numberField]: prNumber,
        },
        { transaction },
      );

      if (parsedDetails.isDraft === false) {
        await this.requisitionRepository.update(
          { id: parsedDetails.requisitionId },
          { status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL },
          { transaction },
        );

        this.fastify.log.info(`Generating payment request approvers`);
        await this.#generatePRApprovers({
          transaction,
          paymentRequest: validatePr,
        });
      }
      await transaction.commit();
      this.fastify.log.info(
        `Payment Request ${parsedDetails.isDraft === true ? 'Submitted' : 'Updated'}`,
      );
    } catch (error) {
      this.fastify.log.error(`[Error] Update Submit / Generate PR Approvers`);
      this.fastify.log.error(error);
      await transaction.rollback();
      throw error;
    }

    return { paymentRequest: result[1][0].dataValues };
  }

  async approvePurchaseRequest(payload = {}) {
    const { existingPaymentRequest, userFromToken, transaction } = payload;
    const { PR_APPROVER_STATUS, RS_PAYMENT_REQUEST_STATUS } =
      this.constants.rsPaymentRequest;

    if (existingPaymentRequest.status === RS_PAYMENT_REQUEST_STATUS.DRAFT) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Payment Request is in draft status`,
      });
    }

    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      where: { paymentRequestId: existingPaymentRequest.id },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const userApproverRecords = approvers.data.filter((approverRecord) => {
      const isApprover =
        approverRecord.userId === userFromToken.id ||
        approverRecord.altApproverId === userFromToken.id;
      return isApprover;
    });

    if (userApproverRecords.length === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this payment request`,
      });
    }

    const pendingApprovals = userApproverRecords
      .filter((record) => record.status === PR_APPROVER_STATUS.PENDING)
      .sort((a, b) => a.level - b.level);

    const currentApprover =
      pendingApprovals.length > 0 ? pendingApprovals[0] : null;

    console.log('------------------------------------- ', currentApprover);

    if (!currentApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You have no pending approvals for this payment request`,
      });
    }

    if (currentApprover.isAdhoc) {
      const primaryApprover = approvers.data.find(
        (approver) =>
          approver.level === currentApprover.level && !approver.isAdhoc,
      );

      if (primaryApprover?.status !== PR_APPROVER_STATUS.APPROVED) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${currentApprover.level} primary approver must approve first`,
        });
      }
    }

    const previousLevelApprovers = approvers.data.filter(
      (approver) => approver.level < currentApprover.level,
    );

    for (const prevApprover of previousLevelApprovers) {
      if (prevApprover.status !== PR_APPROVER_STATUS.APPROVED) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${prevApprover.level} ${
            prevApprover.isAdhoc ? 'adhoc ' : ''
          }approver must approve first`,
        });
      }
    }

    await this.rsPaymentRequestApproverRepository.update(
      { id: currentApprover.id },
      { status: PR_APPROVER_STATUS.APPROVED },
      { transaction },
    );

    const updatedApprovers = approvers.data.map((approver) => ({
      ...approver,
      status:
        approver.userId === currentApprover.userId
          ? PR_APPROVER_STATUS.APPROVED
          : approver.status,
    }));

    const allApproved = updatedApprovers.every(
      (approver) => approver.status === PR_APPROVER_STATUS.APPROVED,
    );

    if (allApproved) {
      // TODO: To Update - final status of PR
      await this.rsPaymentRequestRepository.update(
        { id: existingPaymentRequest.id },
        { status: RS_PAYMENT_REQUEST_STATUS.APPROVED },
        { transaction },
      );
    }

    return allApproved;
  }

  async #generatePRApprovers(payload) {
    const { USER_TYPES } = this.constants.user;
    const { paymentRequest, transaction } = payload;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;

    /* Check existing approvers and handle resubmission case */
    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      paginate: false,
      where: { paymentRequestId: paymentRequest.id },
    });

    if (approvers.total > 0) {
      const hasRejectedApprovers = approvers.data.some(
        (approver) => approver.status === PR_APPROVER_STATUS.REJECTED,
      );

      if (hasRejectedApprovers) {
        await this.rsPaymentRequestApproverRepository.update(
          {
            paymentRequestId: paymentRequest.id,
            status: PR_APPROVER_STATUS.REJECTED,
          },
          { status: PR_APPROVER_STATUS.PENDING },
          { transaction },
        );
      }
      return;
    }

    const [requisition, supervisorRole, purchasingHeadRole] = await Promise.all(
      [
        this.requisitionRepository.findOne({
          where: { id: paymentRequest.requisitionId },
        }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.SUPERVISOR },
        }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.PURCHASING_HEAD },
        }),
      ],
    );

    if (!(supervisorRole && purchasingHeadRole)) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Required approver roles for the payment request management are not set',
      });
    }

    const [assignedUser, userPurchaseHead] = await Promise.all([
      this.userRepository.getUserById(requisition.assignedTo, {
        paranoid: false,
      }),
      this.userRepository.findOne({
        where: { roleId: purchasingHeadRole.id },
        paranoid: false,
      }),
    ]);

    let prApprovers;
    const isOFM = ['ofm', 'ofm-tom'].includes(requisition?.type);
    if (isOFM) {
      const requester = await this.userRepository.getUserById(
        requisition.createdBy,
        { paranoid: false },
      );

      prApprovers = [
        {
          level: 1,
          roleId: supervisorRole.id,
          ...(assignedUser?.supervisor?.id && {
            userId: assignedUser.supervisor.id,
          }),
        },
        {
          level: 2,
          roleId: supervisorRole.id,
          ...(requester?.supervisor?.id && {
            userId: requester.supervisor.id,
          }),
        },
        {
          level: 3,
          roleId: purchasingHeadRole.id,
          ...(userPurchaseHead?.id && {
            userId: userPurchaseHead.id,
          }),
        },
      ];
    } else {
      prApprovers = [
        {
          level: 1,
          roleId: supervisorRole.id,
          ...(assignedUser?.supervisor?.id && {
            userId: assignedUser.supervisor.id,
          }),
        },
        {
          level: 2,
          roleId: purchasingHeadRole.id,
          ...(userPurchaseHead?.id && {
            userId: userPurchaseHead.id,
          }),
        },
      ];
    }

    if (prApprovers.length > 0) {
      await this.rsPaymentRequestApproverRepository.bulkCreate(
        prApprovers.map((approver) => ({
          ...approver,
          paymentRequestId: paymentRequest.id,
        })),
        { transaction },
      );
    }
  }

  async getPOlists(requisitionId) {
    this.fastify.log.info(`Retrieving PO lists...`);
    const { DELIVERY_ITEM_STATUSES } = this.constants.deliveryReceiptItem;
    const requisition = await this.requisitionRepository.findOne({
      attributes: ['companyCode'],
      where: {
        id: Number(requisitionId),
      },
    });

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    const result = await this.purchaseOrderRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'PO-',
            requisition.companyCode,
            this.db.Sequelize.col('po_letter'),
            this.db.Sequelize.col('po_number'),
          ),
          'poNumber',
        ],
      ],
      include: [
        {
          model: this.db.deliveryReceiptModel,
          attributes: [],
          as: 'deliveryReceipt',
          where: {
            latestDeliveryStatus: {
              [this.Sequelize.Op.in]: [
                DELIVERY_ITEM_STATUSES.FULLY_DELIVERED,
                DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
              ],
            },
          },
        },
        {
          model: this.db.rsPaymentRequestModel,
          attributes: [],
          as: 'rsPaymentRequests',
          required: false,
        },
      ],
      where: {
        requisitionId: Number(requisitionId),
        '$rsPaymentRequests.id$': null,
      },
      order: [['id', 'ASC']],
      paginate: false,
    });

    this.fastify.log.info(`Retrieved PO lists`);
    return result;
  }

  async addAdhocApprover(payload = {}) {
    const {
      paymentRequestId,
      approver,
      creatorId,
      transaction,
      requisitionId,
      fullName,
    } = payload;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      paginate: false,
      where: {
        userId: creatorId,
        paymentRequestId,
        status: PR_APPROVER_STATUS.PENDING,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    if (approvers.total === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    const creatorApprover = approvers.data[0];

    if (creatorApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    const isExistingApprover =
      await this.rsPaymentRequestApproverRepository.findOne({
        where: {
          paymentRequestId,
          userId: approver.id,
        },
      });

    if (isExistingApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Approver already exists',
      });
    }

    const existingAdhoc = await this.rsPaymentRequestApproverRepository.findOne(
      {
        where: {
          paymentRequestId,
          level: creatorApprover.level,
          isAdhoc: true,
        },
      },
    );

    if (existingAdhoc) {
      await this.rsPaymentRequestApproverRepository.update(
        { id: existingAdhoc.id },
        { userId: approver.id },
        { transaction },
      );

      return;
    }

    await this.rsPaymentRequestApproverRepository.create(
      {
        paymentRequestId,
        userId: approver.id,
        level: creatorApprover.level,
        isAdhoc: true,
        roleId: approver.role.id,
      },
      { transaction },
    );

    await this.notificationService.sendNotification({
      transaction,
      senderId: creatorId,
      type: NOTIFICATION_TYPES.PAYMENT_REQUEST,
      title: NOTIFICATION_DETAILS.ADDITIONAL_APPROVER_PR.title,
      message: NOTIFICATION_DETAILS.ADDITIONAL_APPROVER_PR.message(fullName),
      recipientUserIds: [approver.id],
      metaData: {
        addedBy: creatorId,
        adhocApprover: approver.id,
        paymentRequestId,
        requisitionId,
      },
    });
  }

  async rejectPaymentRequest(payload = {}) {
    const { existingPaymentRequest, approverId, transaction } = payload;
    const { PR_APPROVER_STATUS, RS_PAYMENT_REQUEST_STATUS } =
      this.constants.rsPaymentRequest;

    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      paginate: false,
      where: {
        paymentRequestId: existingPaymentRequest.id,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const userApproverRecords = approvers.data.filter((approverRecord) => {
      const isApprover =
        approverRecord.userId === approverId ||
        approverRecord.altApproverId === approverId;

      return isApprover;
    });

    if (userApproverRecords.length === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this payment request`,
      });
    }

    const pendingApprovals = userApproverRecords
      .filter((record) => record.status === PR_APPROVER_STATUS.PENDING)
      .sort((a, b) => a.level - b.level);

    const currentApprover =
      pendingApprovals.length > 0 ? pendingApprovals[0] : null;

    if (!currentApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You have no pending approvals for this payment request`,
      });
    }

    if (currentApprover.isAdhoc) {
      const primaryApprover = approvers.data.find(
        (approver) =>
          approver.level === currentApprover.level && !approver.isAdhoc,
      );

      if (primaryApprover?.status !== PR_APPROVER_STATUS.APPROVED) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${currentApprover.level} primary approver must approve first`,
        });
      }
    }

    const previousLevelApprovers = approvers.data.filter(
      (approver) => approver.level < currentApprover.level,
    );

    for (const prevApprover of previousLevelApprovers) {
      if (prevApprover.status !== PR_APPROVER_STATUS.APPROVED) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${prevApprover.level} ${
            prevApprover.isAdhoc ? 'adhoc ' : ''
          }approver must approve first`,
        });
      }
    }

    await this.rsPaymentRequestApproverRepository.update(
      { id: currentApprover.id },
      { status: PR_APPROVER_STATUS.REJECTED },
      { transaction },
    );

    await this.rsPaymentRequestRepository.update(
      { id: existingPaymentRequest.id },
      { status: RS_PAYMENT_REQUEST_STATUS.REJECTED },
      { transaction },
    );
  }

  async cascadeRoleApprover({ userId, roleId, transaction }) {
    const { USER_TYPES } = this.constants.user;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;
    const options = transaction ? { transaction } : {};

    const role = await this.roleRepository.findOne({ where: { id: roleId } });
    const isPurchasingHead = role?.name === USER_TYPES.PURCHASING_HEAD;

    if (!isPurchasingHead) {
      return;
    }

    const pendingApprovers =
      await this.rsPaymentRequestApproverRepository.findAll({
        paginate: false,
        where: {
          roleId,
          isAdhoc: false,
          status: PR_APPROVER_STATUS.PENDING,
          userId: null,
          level: {
            [this.db.Sequelize.Op.in]: [2, 3],
          },
        },
      });

    if (pendingApprovers.total === 0) {
      this.fastify.log.info(
        `[INFO] No pending ${role.name} approvers found to cascade`,
      );
      return;
    }

    try {
      const updatePromises = pendingApprovers.data.map((approver) => {
        return this.rsPaymentRequestApproverRepository.update(
          { id: approver.id },
          { userId },
          options,
        );
      });

      await Promise.all(updatePromises);
      this.fastify.log.info(
        `[INFO] Successfully cascaded ${pendingApprovers.total} ${role.name} approvers. 
        Payment Request Approver IDs: ${pendingApprovers.data.map((a) => a.id).join(', ')}`,
      );
    } catch (error) {
      this.fastify.log.error(
        `[ERROR] Failed to cascade ${role.name} approver in payment request`,
      );
      this.fastify.log.error(error);
      throw error;
    }
  }

  async cascadeSupervisorId({ transaction, userId, supervisorId }) {
    const approvers =
      await this.rsPaymentRequestApproverRepository.findPendingApproversByUserId(
        userId,
      );

    this.fastify.log.info(
      `[PR-Cascade] Pending approvers for user ID ${userId}: ${JSON.stringify(approvers)}`,
    );

    if (approvers.total === 0) {
      this.fastify.log.info(
        `[PR-Cascade] No pending PR Request associated with user ID ${userId}`,
      );
      return;
    }

    const updatePromises = approvers.data.map((approver) =>
      this.rsPaymentRequestApproverRepository.update(
        { id: approver.id },
        { userId: supervisorId },
        { transaction },
      ),
    );

    await Promise.all(updatePromises);

    this.fastify.log.info(
      `[PR-Cascade] Successfully cascaded supervisor ID ${supervisorId} for user ID ${userId}`,
    );
  }

  async getPaymentRequestsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const paymentRequests =
      await this.rsPaymentRequestRepository.getPaymentRequestsFromRequisitionId(
        requisitionId,
        { search, page, limit, sortBy },
      );

    return this._formatPaymentRequestForListing(paymentRequests);
  }

  _formatPaymentRequestForListing(paymentRequests) {
    return {
      data: paymentRequests.data?.map((paymentRequest) => ({
        id: paymentRequest.id,
        prNumber: paymentRequest.prNumber,
        lastApprover: paymentRequest.lastApprover
          ? `${paymentRequest.lastApprover.firstName} ${paymentRequest.lastApprover.lastName}`
          : null,
        lastUpdate: convertDateToDDMMMYYYY(paymentRequest.updatedAt),
        status: paymentRequest.status,
      })),
      total: paymentRequests.total,
    };
  }

  async getRsPaymentRequestByPk(id) {
    const rsPaymentRequest = await this.rsPaymentRequestRepository.findOne({
      where: { id },
      include: [
        {
          model: this.db.purchaseOrderModel,
          include: [
            {
              model: this.db.supplierModel,
              attributes: ['name'],
              as: 'supplier',
            },
          ],
          as: 'purchaseOrder',
        },
        {
          model: this.db.requisitionModel,
          include: [
            {
              model: this.db.companyModel,
              attributes: ['id', 'name'],
              as: 'company',
            },
          ],
          as: 'requisition',
        },
      ],
    });

    if (!rsPaymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `Payment request not found with ID ${id}`,
      });
    }

    const items = await this.purchaseOrderItemRepository.getPOItemsById({
      purchaseOrderId: rsPaymentRequest.purchaseOrderId,
      paginate: false,
    });

    if (!items) {
      throw this.clientErrors.NOT_FOUND({
        message: `No Purchase Order Items found in Payment Request: ${id}`,
      });
    }

    return { rsPaymentRequest, items };
  }

  async removeAdhocApprover(payload = {}) {
    const { id, primaryApproverId } = payload;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;

    const approvers =
      await this.rsPaymentRequestApproverRepository.getPRApprovers(id);

    const primaryApprover = approvers.data.find(
      (approver) => approver.userId === primaryApproverId && !approver.isAdhoc,
    );

    if (!primaryApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to remove adhoc approvers',
      });
    }

    if (primaryApprover.status !== PR_APPROVER_STATUS.PENDING) {
      throw this.clientErrors.FORBIDDEN({
        message:
          'You cannot remove an adhoc approver if your status of approval is not in pending state',
      });
    }

    const adhocApprover = approvers.data.find(
      (approver) =>
        approver.isAdhoc && approver.level === primaryApprover.level,
    );

    if (!adhocApprover) {
      throw this.clientErrors.NOT_FOUND({
        message: `No adhoc approver found for level ${primaryApprover.level}`,
      });
    }

    await this.rsPaymentRequestApproverRepository.destroy({
      id: adhocApprover.id,
    });
  }
}

module.exports = RsPaymentRequestService;
