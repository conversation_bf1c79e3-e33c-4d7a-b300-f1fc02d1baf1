const AppError = require('./appError');

/**
 * Error class for validation errors
 */
class ValidationError extends AppError {
  /**
   * Creates a new ValidationError
   * 
   * @param {string} message - Error message
   * @param {Object} validationErrors - Validation errors by field
   * @param {Error} originalError - Original error that caused this error
   */
  constructor(message = 'Validation failed', validationErrors = {}, originalError = null) {
    super(message, 'VALIDATION_ERROR', { validationErrors }, originalError);
    this.validationErrors = validationErrors;
  }

  /**
   * Gets HTTP status code for this error
   * 
   * @returns {number} - HTTP status code
   */
  getHttpStatus() {
    return 422; // Unprocessable Entity
  }

  /**
   * Creates a ValidationError from a Zod error
   * 
   * @param {import('zod').ZodError} zodError - Zod validation error
   * @param {string} message - Optional message override
   * @returns {ValidationError} - New ValidationError instance
   */
  static fromZodError(zodError, message = 'Validation failed') {
    const validationErrors = zodError.errors.reduce((acc, error) => {
      const path = error.path.join('.') || 'value';
      acc[path] = error.message;
      return acc;
    }, {});

    return new ValidationError(
      message || zodError.errors[0]?.message || 'Validation failed',
      validationErrors,
      zodError
    );
  }
}

module.exports = ValidationError;
