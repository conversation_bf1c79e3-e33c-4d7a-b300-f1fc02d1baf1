<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffe599;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-right:none;border-bottom:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s38{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s36{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s35{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s37{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ead1dc;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fff2cc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="481468692C0" style="width:103px;" class="column-headers-background">A</th><th id="481468692C1" style="width:163px;" class="column-headers-background">B</th><th id="481468692C2" style="width:252px;" class="column-headers-background">C</th><th id="481468692C3" style="width:252px;" class="column-headers-background">D</th><th id="481468692C4" style="width:233px;" class="column-headers-background">E</th><th id="481468692C5" style="width:330px;" class="column-headers-background">F</th><th id="481468692C6" style="width:184px;" class="column-headers-background">G</th><th id="481468692C7" style="width:385px;" class="column-headers-background">H</th><th id="481468692C8" style="width:100px;" class="column-headers-background">I</th><th id="481468692C10" style="width:105px;" class="column-headers-background">K</th><th id="481468692C11" style="width:212px;" class="column-headers-background">L</th><th id="481468692C12" style="width:212px;" class="column-headers-background">M</th><th id="481468692C13" style="width:212px;" class="column-headers-background">N</th><th id="481468692C14" style="width:212px;" class="column-headers-background">O</th><th id="481468692C15" style="width:212px;" class="column-headers-background">P</th><th id="481468692C16" style="width:221px;" class="column-headers-background">Q</th></tr></thead><tbody><tr style="height: 42px"><th id="481468692R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s2">Priority</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG_wk2</td><td class="s3">Status<br>(E2E_Run1)</td><td class="s3">Actual Results<br>(E2E_Run1)</td><td class="s3">Status<br>(E2E_Run2)</td><td class="s3">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="481468692R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="16">PRS-017 - [Canvassing] - Viewing of Canvass Sheet by Approver, Approving of Assigned Purchasing Staff Supervisor , Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td></tr><tr style="height: 19px"><th id="481468692R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s5">PRS-017-001</td><td class="s6">Viewing of Canvass Sheet by Approver</td><td class="s5"></td><td class="s5">Verify View on Canvass Sheet - empty state</td><td class="s5">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver</td><td class="s5">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Related Documents Tab<br>3. Click a Canvass Number <br></td><td class="s5"></td><td class="s5">1. Able to view Canvass Sheet by Approver<br>2. Able to Click Related Documents Tab<br>3. Able to View Requisition Slip<br>4. Able to click a canvass number<br>5. Able to See Confirmation<br>6. Able to see Canvass Number and RS Number<br>7. Able to see options Going back to Main RS<br>8. Able to see Items in Canvassing Section<br>     a. no items<br>10. Able to see Attachkments and Notes Section<br> i. Attachments<br>          i) When Attachment Field is clicked should implement a File Validation<br>             a) File Formats should be PDF, Doc File, Excel or CSV, JPG, PNG, JPEG<br>             b) Allow multiple file for uploading, Maximum File size is 25MB per File<br>       ii. Notes<br>          i) Should allow 100 Characters for the Notes that will have <br>11. Check for Different Request Types:<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials</td><td class="s5"></td><td class="s7">Blocked</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">Canvass sheet number is not clickable <br><br>same issue in case number PRS-017-029<br><br>Cannot Create RS</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-837">https://youtrack.stratpoint.com/issue/CITYLANDPRS-837<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-985</a></td></tr><tr style="height: 211px"><th id="481468692R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s12">PRS-017-002</td><td class="s12">Viewing of Canvass Sheet by Approver</td><td class="s12"></td><td class="s12">Verify View on Canvass Sheet</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Related Documents Tab<br>3. Click a Canvass Number <br></td><td class="s12"></td><td class="s12">1. Able to view Canvass Sheet by Approver<br>2. Able to Click Related Documents Tab<br>3. Able to View Requisition Slip<br>4. Able to click a canvass number<br>5. Able to See Confirmation<br>6. Able to see Canvass Number and RS Number<br>7. Able to see options Going back to Main RS<br>8. Able to see Items in Canvassing Section<br>        i. Item<br>        ii. Account Code<br>        iii. Unit<br>        iv. Quantity<br>        v. Supplier<br>        vi. Canvass Status<br>        viii.  Actions<br>               i) Edit Icon, Down Icon, View Icon<br>9. Able to see Status and Approvers Section<br>        i. Status is the current Status of the Canvass Sheet<br>        ii. Should display Assigned Purchasing Staff<br>        iii. Approver can set another Approver after them<br>10. Able to see Attachkments and Notes Section<br> i. Attachments<br>          i) When Attachment Field is clicked should implement a File Validation<br>             a) File Formats should be PDF, Doc File, Excel or CSV, JPG, PNG, JPEG<br>             b) Allow multiple file for uploading, Maximum File size is 25MB per File<br>       ii. Notes<br>          i) Should allow 100 Characters for the Notes that will have <br>11. Check for Different Request Types:<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials</td><td class="s5" rowspan="8"></td><td class="s7">Blocked</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">Cannot Create RS<br><br>[Viewing of Canvass Sheet by Approver] Item Table sorting is not working<br><br>[Viewing of Canvass Sheet by Approver] Search and Clear button is not working<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Viewing of Canvass Sheet by Approver] Date picker issues in notes item view button<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-985">https://youtrack.stratpoint.com/issue/CITYLANDPRS-985<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1010<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1011<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 211px"><th id="481468692R4" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">5</div></th><td class="s5">PRS-017-003</td><td class="s6">Viewing of Canvass Sheet by Approver</td><td class="s5"></td><td class="s5">Verify Confirmation for Approving or Rejecting</td><td class="s5">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver</td><td class="s5">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Related Documents Tab<br>3. Click a Canvass Number <br>4. Sticky confirmation for approving or rejecting</td><td class="s5"></td><td class="s5">1. Able to view the confirmation modal for approving or rejecting<br>2. Confirmation is sticky or follows user when scrolling</td><td class="s7">Blocked</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">Cannot Create RS<br><br>[Viewing of Canvass Sheet by Approver] Canvass Sheet Approval is not sticky</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-985">https://youtrack.stratpoint.com/issue/CITYLANDPRS-985<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1003</a></td></tr><tr style="height: 211px"><th id="481468692R5" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">6</div></th><td class="s12">PRS-017-004</td><td class="s12">Viewing of Canvass Sheet by Approver</td><td class="s12"></td><td class="s12">Verify Actions for Canvass Sheet </td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Related Documents Tab<br>3. Click a Canvass Number <br>4a. Click view on Supplier Details<br>4b. Click Edit on Supplier Details</td><td class="s12"></td><td class="s12">1. Able to View Supplier details through Accordian or Modal<br>2. Able to Edit Supplier details through Accordian or Modal</td><td class="s7">Blocked</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">need new canvas. not yet submitted<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Check Notes] Date picker issues in check notes<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-985">https://youtrack.stratpoint.com/issue/CITYLANDPRS-985<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 211px"><th id="481468692R6" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">7</div></th><td class="s12">PRS-017-005</td><td class="s12">Approving of Assigned Purchasing Staff Supervisor </td><td class="s12"></td><td class="s12">Verify Approving of Assigned Purchasing Staff</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and was submitted<br>5. Supervisor of the Assigned Purchasing Staff<br>6. User must be Supervisor of the Assigned Purchasing Staff</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>3. Click a Canvass Number <br>4. Click Approve</td><td class="s12"></td><td class="s12">1. Able to Click RS Number<br>2. Able to Click Related Documents Tab<br>3. Able to Click Canvass Sheet number in the canvasses tab<br>4. Able to view Canvass Sheet View for approvers<br>5. Able to view Confirmation for Approving or Rejecting (Message should be sticky or follow user on every scroll of the page)<br>6. Able to click approve button<br>7. Able to view the approver status to Approved and shall proceed to the next Approver<br>8. Item status should still be the same<br>9. Check for Different Request Types:<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">need to check diffent RS types<br><br>ok in ofm<br><br>[Approving of Assigned Purchasing Staff Supervisor] Canvass for approval is missing the Status, Assigned to and Approvers</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-985">https://youtrack.stratpoint.com/issue/CITYLANDPRS-985<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1042</a></td></tr><tr style="height: 211px"><th id="481468692R7" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">8</div></th><td class="s12">PRS-017-006</td><td class="s12">Approving of Assigned Purchasing Staff Supervisor </td><td class="s12"></td><td class="s12">Verify Action Functions - Edit</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and was submitted<br>5. Supervisor of the Assigned Purchasing Staff<br>6. User must be Supervisor of the Assigned Purchasing Staff</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>3. Click a Canvass Number <br>4. Click Edit button on an Item for Canvassing<br>5. Check Edit Form Modal pop up<br>6. Check the display for New Supplier to the current and succeeding Approvers<br>7. Check notification for Requester and Assigned Purchasing Staff via Notification Bell for changes</td><td class="s12"></td><td class="s12">1. Able to Click Edit Button<br>2. Able to view Edit Form Modal<br>3. Able to view in the modal the updating Supplier and/or Supplier Details<br>4. Able to view New supplier to the current and succeeding approvers<br>5. Able to receive notification as the requester and as the assigned purchasing staff via Notification Bell</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Approving of Assigned Purchasing Staff Supervisor] Notification for the edited items for requestor and purchasing staff is giving an error on click</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-985"><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1052</a></td></tr><tr style="height: 210px"><th id="481468692R8" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">9</div></th><td class="s12">PRS-017-007</td><td class="s12">Approving of Assigned Purchasing Staff Supervisor </td><td class="s12"></td><td class="s12">Verify Action Functions - View</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and was submitted<br>5. Supervisor of the Assigned Purchasing Staff<br>6. User must be Supervisor of the Assigned Purchasing Staff</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>3. Click a Canvass Number <br>4. Click View button on an Item for Canvassing<br>5a. Click Down Icon for view<br>5b. Click Eye Icon for view</td><td class="s12"></td><td class="s12">1. Able to Click Down Icon or Eye for View<br>2. Able to view accordion with supplier details when clicking Down icon<br>3. Able to view Modal with supplier full details </td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Viewing of Canvass Sheet by Approver] Date picker issues in notes item view button<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 210px"><th id="481468692R9" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">10</div></th><td class="s5">PRS-017-008</td><td class="s16">Approving of Assigned Purchasing Staff Supervisor </td><td class="s5"></td><td class="s5">Verify Rejecting of Assigned Purchasing Staff</td><td class="s5">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and was submitted<br>5. Supervisor of the Assigned Purchasing Staff<br>6. User must be Supervisor of the Assigned Purchasing Staff</td><td class="s5">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>3. Click a Canvass Number <br>4. Click Reject<br>5. Input Reason<br>6. Click Continue<br>7. Check Approver Status<br>8. Notification received by Requester and Assigned Purchasing Staff via Notification bell</td><td class="s5"></td><td class="s5">1. Able to click reject button<br>2. Able to input reason<br>3. Able to click continue<br>4. Able to check approver status change to Rejected<br>5. Able to receive notification by requester and assigned pruchasing staff via notification bell<br>6. Assigned Purchasing Staff should be able to update all the items supplier details and resubmit for re-approval</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">RS-07AA00000126<br>CS-AA00000018<br><br>[Approving of Assigned Purchasing Staff Supervisor] Rejecting reason notes is accepting emoji</td><td class="s17 softmerge"><div class="softmerge-inner" style="width:419px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-751">CITYLANDPRS-751<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1054</a></div></td></tr><tr style="height: 210px"><th id="481468692R10" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">11</div></th><td class="s12">PRS-017-009</td><td class="s12">Approving of Assigned Purchasing Staff Supervisor </td><td class="s12"></td><td class="s12">Verify Adding of Approver</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and was submitted<br>5. Supervisor of the Assigned Purchasing Staff<br>6. User must be Supervisor of the Assigned Purchasing Staff</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>3. Click a Canvass Number <br>4. Click Add on the Approvers part<br>5. Click the search field<br>6. Check different User - User Types<br>7. Input/Select a User<br>8. Click Add Approver<br>9. Click Approve <br>10. Notification received by Additional Approver via Notification Bell</td><td class="s12"></td><td class="s12">1. Able to Click Add button for Approver<br>2. Able to Click Search field<br>3. Able to view different user<br>4. Additional Approver Selected able to receive and view Notification<br>5. Able to view different user types:<br>           i) Supervisor<br>           ii) Assistant Manager<br>           iii) Department Head<br>           iv) Division Head<br>           v) Area Staff<br>6. Added Approver is added to the current or open request</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s14"></td></tr><tr style="height: 210px"><th id="481468692R11" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">12</div></th><td class="s12">PRS-017-010</td><td class="s12">Approving of Assigned Purchasing Staff Supervisor </td><td class="s12"></td><td class="s12">Verify Adding of Approver during Approving</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and was submitted<br>5. Supervisor of the Assigned Purchasing Staff<br>6. User must be Supervisor of the Assigned Purchasing Staff</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to an approver<br>2. Click Select Action<br>3. Click Enter Canvass <br>3. Click a Canvass Number <br>4. Click Approve<br>5. Click Add Approver <br>6. Click the search field<br>7. Check different User - User Types<br>8. Input/Select an approver<br>9. Click Add Approver <br>10. Click Submit</td><td class="s12"></td><td class="s12">1. Able to Click Add button for Approver<br>2. Able to Click Search field<br>3. Able to view different user<br>4. Additional Approver Selected able to receive and view Notification<br>5. Added Approver is added to the current or open request<br>6. Added Additional Approver should be able to Approve or Decline Canvass Sheet</td><td class="s5" rowspan="3"></td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R12" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">13</div></th><td class="s5">PRS-017-011</td><td class="s18">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s5"></td><td class="s5">Supplier selection of Purchasing Head for OFM Request Type when Edit</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Edit  Icon for the item in actions column<br>6. Click the &quot;Add Supplier&quot; button in the form<br>7. Populate all fields<br>8. Click &quot;Confirm&quot; button<br>9. Click &quot;Continue&quot; on the modal</td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should display a Edit Form Modal that will allow updating of Supplier and/or Supplier Details<br>6.  A new suppllier shoud be added<br>7.. Fieds should be populated<br>8. A &quot;Save Changes&quot; modal should be displayed with contains of the ff:<br>     a. A description  You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>9. Should display the New Supplier to the current and succeeding Approvers<br>10. Should notify the Requester and Assigned Purchasing Staff through the Notification Bell<br>NOTIFICATION TEMPLATE:<br>Title: <br>Supplier Canvass Updated<br><br>Content:<br>[PURCHASING HEAD NAME] has updated the List of Suppliers for the Canvass Sheet of your Request. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">RS-07AA00000126<br><br>[Approving of Assigned Purchasing Staff Supervisor] Notification for the edited items for requestor and purchasing staff is giving an error on click</td><td class="s17 softmerge"><div class="softmerge-inner" style="width:419px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-754">CITYLANDPRS-754<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1052</a></div></td></tr><tr style="height: 210px"><th id="481468692R13" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">14</div></th><td class="s12">PRS-017-012</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s12">Supplier selection of Purchasing Head for OFM Request Type when Viewing</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Click Eye Icon for the item in actions column</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Should display a Modal with the Supplier full Details<br></td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Viewing of Canvass Sheet by Approver] Date picker issues in notes item view button<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 210px"><th id="481468692R14" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">15</div></th><td class="s12">PRS-017-013</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s12">Supplier selection of Purchasing Head for OFM Request Type when Approving</td><td class="s12">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Select Suppiers per item or multiple items<br>7. Check &quot;Approve&quot; button in the Confirmation Message<br>8. Click Approve button<br>9. Click Confirm on the Modal</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Item per supplier should be selected<br>7. Should enable the Approve Button in the Confirmation Message if one or more of the Items has a selected Supplier/s<br>8. Should display a Confirmation message<br>9. Once Cofirmed<br>         i. Should display the selected Supplier in the Suppliers Column<br>         ii. Should only update the Status of the Purchasing Head to Approved if all of the Items in the Canvass Sheet has a selected Supplier/s<br>             i) Item Status should still be For Approval<br>    b. Should not allow the Purchasing Head to edit or re-approve the Canvass<br>    c. Should allow the Next Approver to review the Canvass Sheet after all of the Items has a selected Supplier/s</td><td class="s5" rowspan="3"></td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Canvass Sheet Approval is not sticky</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1003">CITYLANDPRS-1003</a></td></tr><tr style="height: 210px"><th id="481468692R15" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">16</div></th><td class="s12">PRS-017-014</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s20">Supplier selection of other user type for OFM Request Type when Approving</td><td class="s12">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as the ff:<br>      a. Purchasing Staff<br>      b. Management<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Select Suppiers per item or multiple items<br>7. Check &quot;Approve&quot; button in the Confirmation Message<br>8. Click Approve button<br>9. Click Confirm on the Modal</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Should not able to select a supplier per item or multiple items<br>7. Should enable the Approve Button in the Confirmation Message if one or more of the Items has a selected Supplier/s<br>8. Should display a Confirmation message<br>9. Once Cofirmed<br>         i. Should display the selected Supplier in the Suppliers Column<br>         ii. Should only update the Status of the Purchasing Head to Approved if all of the Items in the Canvass Sheet has a selected Supplier/s<br>             i) Item Status should still be For Approval<br>    b. Should not allow the Purchasing Head to edit or re-approve the Canvass<br>    c. Should allow the Next Approver to review the Canvass Sheet after all of the Items has a selected Supplier/s</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R16" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">17</div></th><td class="s5">PRS-017-015</td><td class="s18">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s21"></td><td class="s21">Supplier selection of Purchasing Head for OFM Request Type when Rejecting</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Reject Button<br>6. Populate a reason for rejecting<br>7. Click continue on the modal<br>8. Check Notification bell of the Requester and Assigned Purchasing Staff </td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should dispaly a modal and Should require the Approver to enter Reason for Rejecting<br>6. Reason for rejecting should be populated<br>7. Should update the Approver Status to Rejected<br>8. Should notify the Requester and Assigned Purchasing Staff through the Notification Bell<br>Sample Notification:<br></span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Title: <br>Canvass Sheet Rejected<br><br>Content:<br>Canvass Sheet has been Rejected by one of the Approvers. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</span></td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Approving of Assigned Purchasing Staff Supervisor] Rejecting reason notes is accepting emoji</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1054">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1054</a></td></tr><tr style="height: 210px"><th id="481468692R17" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">18</div></th><td class="s5">PRS-017-016</td><td class="s18">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s5"></td><td class="s5">Supplier selection of Purchasing Head for OFM Request Type when adding of approvers</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click &quot;Add&quot; Button in the Approvers Section<br>6. Select Approver<br>7. Click &quot;Add User&quot;<br>8. Click &quot;Approve&quot; button<br>9. Select Approver<br>10. Click &quot;Add User&quot;<br>11. Check Notification Bell of added additional approver</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5. A confirmation modal to elect an approver should be displayed <br>6.Approver should be selected<br>7. Should allow the current Approver to add an Approver<br>8. A confirmation modal should be displayed with Add approver option modal<br>9.Approver should be selected<br>10. Should allow the current Approver, During approving to add an Approver<br>11. Should notify the Additional Approver through Notification Bell for their Approval<br>    a. Should require the Additional Approver to Approve or Decline the Canvass Sheet<br>    b. Added Approver is only added to the current or Open Request<br></span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">NOTIFICATION TEMPLATE:<br></span><span style="font-family:Poppins,Arial;color:#000000;">Title: <br>Assigned as an Additional Approver<br><br>Content:<br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</span></td><td class="s5" rowspan="3"></td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">error in approving purchase order<br><br>RS-32AA00000057<br><br><br></td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R18" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">19</div></th><td class="s12">PRS-017-017</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s12">Supplier selection of Purchasing Head for Non-OFM Request Type when edit</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i.Non-OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Edit  Icon for the item in actions column<br>6. Click the &quot;Add Supplier&quot; button in the form<br>7. Populate all fields<br>8. Click &quot;Confirm&quot; button<br>9. Click &quot;Continue&quot; on the modal</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should display a Edit Form Modal that will allow updating of Supplier and/or Supplier Details<br>6.  A new suppllier shoud be added<br>7.. Fieds should be populated<br>8. A &quot;Save Changes&quot; modal should be displayed with contains of the ff:<br>     a. A description  You are about to make changes. Make sure all items are correct. Press continue if you want to proceed with this action.<br>     b. A &quot;Cancel&quot; and &quot;Continue&quot; buttons<br>9. Should display the New Supplier to the current and succeeding Approvers<br>10. Should notify the Requester and Assigned Purchasing Staff through the Notification Bell<br>NOTIFICATION TEMPLATE:<br>Title: <br>Supplier Canvass Updated<br><br>Content:<br>[PURCHASING HEAD NAME] has updated the List of Suppliers for the Canvass Sheet of your Request. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Approving of Assigned Purchasing Staff Supervisor] Notification for the edited items for requestor and purchasing staff is giving an error on click</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1052">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1052</a></td></tr><tr style="height: 210px"><th id="481468692R19" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">20</div></th><td class="s12">PRS-017-018</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s12">Supplier selection of Purchasing Head for Non-OFM Request Type when Viewing</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i.Non-OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Click Eye Icon for the item in actions column</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Should display a Modal with the Supplier full Details<br></td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Viewing of Canvass Sheet by Approver] Date picker issues in notes item view button<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 210px"><th id="481468692R20" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">21</div></th><td class="s12">PRS-017-019</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s12">Supplier selection of Purchasing Head for Non-OFM  Request Type when Approving</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i.Non-OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Select Suppiers per item or multiple items<br>7. Check &quot;Approve&quot; button in the Confirmation Message<br>8. Click Approve button<br>9. Click Confirm on the Modal</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Item per supplier should be selected<br>7. Should enable the Approve Button in the Confirmation Message if one or more of the Items has a selected Supplier/s<br>8. Should display a Confirmation message<br>9. Once Cofirmed<br>         i. Should display the selected Supplier in the Suppliers Column<br>         ii. Should only update the Status of the Purchasing Head to Approved if all of the Items in the Canvass Sheet has a selected Supplier/s<br>             i) Item Status should still be For Approval<br>    b. Should not allow the Purchasing Head to edit or re-approve the Canvass<br>    c. Should allow the Next Approver to review the Canvass Sheet after all of the Items has a selected Supplier/s</td><td class="s5" rowspan="3"></td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R21" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">22</div></th><td class="s12">PRS-017-020</td><td class="s12">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s12"></td><td class="s12">Supplier selection of other user type for Non-OFM Request Type when Approving</td><td class="s12">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as the ff:<br>      a. Supervisor<br>      b. Management<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. Non-OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Select Suppiers per item or multiple items<br>7. Check &quot;Approve&quot; button in the Confirmation Message<br>8. Click Approve button<br>9. Click Confirm on the Modal</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Should not able to select a supplier per item or multiple items<br>7. Should enable the Approve Button in the Confirmation Message if one or more of the Items has a selected Supplier/s<br>8. Should display a Confirmation message<br>9. Once Cofirmed<br>         i. Should display the selected Supplier in the Suppliers Column<br>         ii. Should only update the Status of the Purchasing Head to Approved if all of the Items in the Canvass Sheet has a selected Supplier/s<br>             i) Item Status should still be For Approval<br>    b. Should not allow the Purchasing Head to edit or re-approve the Canvass<br>    c. Should allow the Next Approver to review the Canvass Sheet after all of the Items has a selected Supplier/s</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R22" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">23</div></th><td class="s5">PRS-017-021</td><td class="s18">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s5"></td><td class="s22">Supplier selection of Purchasing Head for Non-OFM  Request Type when Rejecting</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i.Non-OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Reject Button<br>6. Populate a reason for rejecting<br>7. Click continue on the modal<br>8. Check Notification bell of the Requester and Assigned Purchasing Staff </td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should dispaly a modal and Should require the Approver to enter Reason for Rejecting<br>6. Reason for rejecting should be populated<br>7. Should update the Approver Status to Rejected<br>8. Should notify the R</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">equester and Assigned Purchasing Staff through the Notification Bell</span><span style="font-family:Poppins,Arial;color:#000000;"><br>Sample Notification:<br>Title: <br>Canvass Sheet Rejected<br><br>Content:<br>Canvass Sheet has been Rejected by one of the Approvers. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</span></td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Approving of Assigned Purchasing Staff Supervisor] Rejecting reason notes is accepting emoji</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1054">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1054</a></td></tr><tr style="height: 210px"><th id="481468692R23" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">24</div></th><td class="s5">PRS-017-022</td><td class="s18">Supplier selection of Purchasing Head for OFM and Non-OFM Request Type</td><td class="s5"></td><td class="s5">Supplier selection of Purchasing Head for Non-OFM Request Type when adding of approvers</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i.Non-OFM<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor has Approved the Canvass Sheet<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click &quot;Add&quot; Button in the Approvers Section<br>6. Select Approver<br>7. Click &quot;Add User&quot;<br>8. Click &quot;Approve&quot; button<br>9. Select Approver<br>10. Click &quot;Add User&quot;<br>11. Check Notification Bell of added additional approver</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5. A confirmation modal to elect an approver should be displayed <br>6.Approver should be selected<br>7. Should allow the current Approver to add an Approver<br>8. A confirmation modal should be displayed with Add approver option modal<br>9.Approver should be selected<br>10. Should allow the current Approver, During approving to add an Approver<br>11. Should notify the Additional Approver through Notification Bell for their Approval<br>    a. Should require the Additional Approver to Approve or Decline the Canvass Sheet<br>    b. Added Approver is only added to the current or Open Request<br></span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">NOTIFICATION TEMPLATE:<br></span><span style="font-family:Poppins,Arial;color:#000000;">Title: <br>Assigned as an Additional Approver<br><br>Content:<br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</span></td><td class="s5"></td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="481468692R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s12">PRS-017-023</td><td class="s12">Approving of Management</td><td class="s12"></td><td class="s12">Verify  Approving of Management when Viewing</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor and Purchasing Head has Approved All of the Canvass per Item in the Canvass Sheet<br>10. A User is created with a User Type of Management<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Drop down icon for the item in actions column<br>6. Click Eye Icon for the item in actions column<br>7. Check if purchasing head is able to view the selected supplier</td><td class="s12"></td><td class="s12">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should open an Accordion with Supplier Details<br>6. Should display a Modal with the Supplier full Details<br>7. Should be able to view the selected Supplier by the Purchasing Head</td><td class="s11" rowspan="31"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1FaOqKw0_N6ux4F09mTyIhbeizYxv6Njcmeb1sg-4sjM/edit?gid=0#gid=0">https://docs.google.com/spreadsheets/d/1FaOqKw0_N6ux4F09mTyIhbeizYxv6Njcmeb1sg-4sjM/edit?gid=0#gid=0</a></td><td class="s23">Failed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"><span style="font-size:10pt;font-family:Poppins,Arial;text-decoration:line-through;color:#000000;">2/3 - Canvass sheet number is not clickable </span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"><br><br>[For My Approval/Assigned Tab]  Missing RS numbers of any related documents of approvers in For My Approval/Assigned Tab<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Viewing of Canvass Sheet by Approver] Date picker issues in notes item view button<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</span></td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-837">https://youtrack.stratpoint.com/issue/CITYLANDPRS-837<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1051<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 210px"><th id="481468692R25" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">26</div></th><td class="s12">PRS-017-024</td><td class="s12">Approving of Management</td><td class="s12"></td><td class="s12">Verify  Approving of Management during Approving</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor and Purchasing Head has Approved All of the Canvass per Item in the Canvass Sheet<br>10. A User is created with a User Type of Management<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Approve button<br>6. Click Confirm on the Modal</td><td class="s12"></td><td class="s12"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5. Should display a Confirmation message<br>6.  Once Confirmed and Approved should update the Approver Status to Approved and shall update the</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;"> RS Status to Canvass Approved</span></td><td class="s23">Failed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">RS-26AA00000060 <br>CITYLANDPRS-780<br><br>2/3: Bug has been raised for step 3<br>2/4: <br>-Bug has been raised for step 4<br>-Bug has been raised for step 6<br><br>Waiting for Ms Irene Input if Status is Canvass Approved or Approved</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-837">https://youtrack.stratpoint.com/issue/CITYLANDPRS-837<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-838<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-839<br><br></a></td></tr><tr style="height: 210px"><th id="481468692R26" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">27</div></th><td class="s12">PRS-017-024</td><td class="s12">Approving of Management</td><td class="s12"></td><td class="s12"><span style="font-family:Poppins,Arial;color:#000000;">Verify  Approving of Management during Approving - </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Non - OFM Transfer of Materials</span></td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor and Purchasing Head has Approved All of the Canvass per Item in the Canvass Sheet<br>10. A User is created with a User Type of Management<br></a></td><td class="s12">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Approve button<br>6. Click Confirm on the Modal</td><td class="s12"></td><td class="s12"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5. Should display a Confirmation message<br>6.  Once Confirmed and Approved should update the Approver Status to Approved and shall update the</span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;"> RS Status to Canvass Approved</span></td><td class="s23">Failed</td><td class="s24">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">RS-26AA00000060 <br>CITYLANDPRS-780<br><br>2/3: Bug has been raised for step 3<br>2/4: <br>-Bug has been raised for step 4<br>-Bug has been raised for step 6<br><br>[Approving of Management] Error 502 on Non-OFM Transfer of Materials approval by last management approver<br><br>Note: Temporary solution to add Non-OFM Transfer of Materials to Isolate blockers. Will add test cases separating Request Types</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-837">https://youtrack.stratpoint.com/issue/CITYLANDPRS-837<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-838<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-839<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1058</a></td></tr><tr style="height: 19px"><th id="481468692R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s5">PRS-017-025</td><td class="s18">Approving of Management</td><td class="s5"></td><td class="s22">Verify  Approving of Management when Rejecting</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor and Purchasing Head has Approved All of the Canvass per Item in the Canvass Sheet<br>10. A User is created with a User Type of Management<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click Reject Button<br>6. Populate a reason for rejecting<br>7. Click continue on the modal<br>8. Check Notification bell of the Requester and Assigned Purchasing Staff </td><td class="s5"></td><td class="s5">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5.  Should dispaly a modal and Should require the Approver to enter Reason for Rejecting<br>6. Reason for rejecting should be populated<br>7. Should update the Approver Status to Rejected<br>8. Should notify the Requester and Assigned Purchasing Staff through the Notification Bell<br>Sample Notification:<br>Title: <br>Canvass Sheet Rejected<br><br>Content:<br>Canvass Sheet has been Rejected by one of the Approvers. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/4: <br>- Bug has been raised for step 3<br>-Bug has been raised for step 4</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-838">https://youtrack.stratpoint.com/issue/CITYLANDPRS-837<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-838<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-839<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s5">PRS-017-026</td><td class="s18">Approving of Management</td><td class="s5"></td><td class="s5">Verify  Approving of Management when Adding of Approvers</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor and Purchasing Head has Approved All of the Canvass per Item in the Canvass Sheet<br>10. A User is created with a User Type of Management<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click &quot;Add&quot; Button in the Approvers Section<br>6. Select Approver<br>7. Click &quot;Add User&quot;<br>8. Click &quot;Approve&quot; button<br>9. Select Approver<br>10. Click &quot;Add User&quot;<br>11. Check Notification Bell of added additional approver</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5. A confirmation modal to elect an approver should be displayed <br>6.Approver should be selected<br>7. Should allow the current Approver to add an Approver<br>8. A confirmation modal should be displayed with Add approver option modal<br>9.Approver should be selected<br>10. Should allow the current Approver, During approving to add an Approver<br>11. Should notify the Additional Approver through Notification Bell for their Approval<br>    a. Should require the Additional Approver to Approve or Decline the Canvass Sheet<br>    b. Added Approver is only added to the current or Open Request<br></span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">NOTIFICATION TEMPLATE:<br></span><span style="font-family:Poppins,Arial;color:#000000;">Title: <br>Assigned as an Additional Approver<br><br>Content:<br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</span></td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/4: <br>- Bug has been raised for step 3<br>-Bug has been raised for step 4</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-839">https://youtrack.stratpoint.com/issue/CITYLANDPRS-838<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-839<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s5">PRS-017-027</td><td class="s18">Approving of Management</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">Verify  Approving of Management when Adding of Approvers - </span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">Non-OFM Transfer of Materials</span></td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>5. Suppliers has been synced<br>6. Should be the Purchasing Head<br>7. Items has been added to the Canvass Sheet<br>8. A Supplier has been added to the Item and was submitted<br>9. Supervisor and Purchasing Head has Approved All of the Canvass per Item in the Canvass Sheet<br>10. A User is created with a User Type of Management<br></a></td><td class="s5">1, On RS dasboard, clck the RS Number <br>2. Click &quot;Related Documents&quot; tab button<br>3. Click the &quot; Canvass Sheet Number&quot; in the Canvasses Tab<br>4. Check Display of Canvass Sheet View for Approvers<br>5. Click &quot;Add&quot; Button in the Approvers Section<br>6. Select Approver<br>7. Click &quot;Add User&quot;<br>8. Click &quot;Approve&quot; button<br>9. Select Approver<br>10. Click &quot;Add User&quot;<br>11. Check Notification Bell of added additional approver</td><td class="s5"></td><td class="s5"><span style="font-family:Poppins,Arial;color:#000000;">1. RS number should be opened and displayed the View Requisition Slip Details<br>2. Should display the Related Documents tab<br>3. Should display the Canvass Sheet View for Approvers<br>4. Should display the following<br>    a. Confirmation for Approving or Rejecting<br>        i. This should be sticky or should follow every scroll of the Page<br>5. A confirmation modal to elect an approver should be displayed <br>6.Approver should be selected<br>7. Should allow the current Approver to add an Approver<br>8. A confirmation modal should be displayed with Add approver option modal<br>9.Approver should be selected<br>10. Should allow the current Approver, During approving to add an Approver<br>11. Should notify the Additional Approver through Notification Bell for their Approval<br>    a. Should require the Additional Approver to Approve or Decline the Canvass Sheet<br>    b. Added Approver is only added to the current or Open Request<br></span><span style="font-family:Poppins,Arial;font-weight:bold;color:#000000;">NOTIFICATION TEMPLATE:<br></span><span style="font-family:Poppins,Arial;color:#000000;">Title: <br>Assigned as an Additional Approver<br><br>Content:<br>[DEFAULT APPROVER&#39;S NAME] has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</span></td><td class="s25"></td><td class="s24">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">RS-26AA00000060 <br>CITYLANDPRS-780<br><br>2/3: Bug has been raised for step 3<br>2/4: <br>-Bug has been raised for step 4<br>-Bug has been raised for step 6<br><br>[Approving of Management] Error 502 on Non-OFM Transfer of Materials approval by last management approver<br><br>Note: Temporary solution to add Non-OFM Transfer of Materials to Isolate blockers. Will add test cases separating Request Types</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-837">https://youtrack.stratpoint.com/issue/CITYLANDPRS-837<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-838<br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-839<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1058</a></td></tr><tr style="height: 19px"><th id="481468692R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5">PRS-017-028</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments are clickable in Canvass Sheet</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. On Canvass Sheet, click Attachment to upload an Attachment with greater than 25MB of Max size<br>2. Attach an Attachment with any of file formats of (docx, pptx, GIF, text)<br>3. Attach an attachment with max file size of 25MB and file formats of PNG, JPG, JPEG, PDF, Excel, CSV. <br>4. Click &quot;Submit&quot; button on the Canvass Sheet<br>5. Click the Attachment added in the canvass sheet</td><td class="s5"></td><td class="s5">1. Attachment should not be attached and error message should be displayed<br>2. Attachment should not be attached and error message should be displayed<br>3.Should upload and display the Attachment in the Canvass Sheet <br>4. Uploaded Attachment should be saved and displayed<br>5. Should open the Attachment into a New Tab for Preview</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 19px"><th id="481468692R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5">PRS-017-029</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments are clickable in Adding a Supplier</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. Click &quot;Enter Canvass&quot; button on Item in Adding a Supplier<br>2. Check the display of  Canvass Modal for adding a supplier<br>3. Populate all fields<br>4. Attach an Attachment with greater than 25MB of Max size<br>5.Attach an Attachment with any of file formats of (docx, pptx, GIF,  text) <br>6. Attach an Attachment with Maximum File size is 25MB and file formats of PNG, JPG, JPEG, PDF, Excel, CSV.<br>7. Populate Notes<br>8. Click &quot;Confirm&quot; button on the canvass modal<br>9. Check the &quot;Enter Canvass&quot; button of the same item<br>10. Clck Eye Icon in Actions Column in the Canvass sheet <br>11. Click the Attachment added in the Item Supplier</td><td class="s5"></td><td class="s5">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. A Note &quot; Note: Maximum of 1 supplier allowed (Transfer of Materials)&quot;<br>    j. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated<br>4. Attachment should not be attached and error message should be displayed <br>5. Attachment should not be attached and error message should be displayed<br>6. Attachments should be attached<br>7. Notes field should be populated<br>8. Should add the suplier to the item<br>9. Should disable the Enter Canvass Button and Should change the Button Text to Canvass Entered.<br>         a. Should Enable the Actions Buttons<br>          b. Should update the Status to For Approval<br>10. Should be able to view Canvass Item details<br>11.Should open the Attachment into a New Tab for Preview</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 210px"><th id="481468692R32" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">33</div></th><td class="s5">PRS-017-030</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments delete in  Canvass Sheet is working as expected</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. On Canvass Sheet, Click the Attachment in the canvass sheet<br>2. Clck &#39;x&#39; icon on the attached file in View attachment</td><td class="s5"></td><td class="s5">1. Should open the Attachment into a New Modal for Preview<br>2. Should removed/delete the attachment</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 210px"><th id="481468692R33" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">34</div></th><td class="s5">PRS-017-031</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments delete in Adding a Supplier is working as expected</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. Clck Eye Icon in Actions Column in the Canvass sheet <br>2. Click the Attachment added in the Item Supplier<br>3. Clck &#39;x&#39; icon on the attached file in View attachment</td><td class="s5"></td><td class="s5">1. Should be able to view Canvass Item details<br>2. Should open the Attachment into a New Modal for Preview<br>3.  Should removed/delete the attachment</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 210px"><th id="481468692R34" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">35</div></th><td class="s5">PRS-017-032</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments Filter in  Canvass Sheet is working as expected</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. On Canvass Sheet, Click the Attachment in the canvass sheet<br>2. Select/populate future date in &quot;From&quot; date<br>3. Select/populate future date in &quot;To&quot; date<br>4. Select/Populate past date up to current date in &quot;From&quot; date<br>5.  Select/Populate past date up to current date in &quot;To&quot; date<br>6. Click Filter button</td><td class="s5"></td><td class="s5">1. Should open the Attachment into a New Modal for Preview<br>2. Should not be able to select/populate future date in &quot;From&quot; date<br>3. Should not be able to select/populate future date in &quot;To&quot; date<br>4. Should be able to select past date up to current date in &quot;From&quot; date<br>5. Should be able to select past date up to current date on &quot;To&quot; date<br>6. Should Display all attachments that has been uploaded based on selected filter date</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">blocked due to attachedment and notes issue<br><br>2/4: Out of Scope/ Not yet implemented for steps 2-3<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 210px"><th id="481468692R35" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">36</div></th><td class="s5">PRS-017-033</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments Filter in  Canvass Sheet is working as expected when no data available</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. On Canvass Sheet, Click the Attachment in the canvass sheet<br>2. Select/Populate past date up to current date in &quot;From&quot; date that no available file uploaded<br>3.  Select/Populate past date up to current date in &quot;To&quot; date that no available file uploaded<br>4. Click Filter button</td><td class="s5"></td><td class="s5">1. Should open the Attachment into a New Modal for Preview<br>2. Should be able to select past date up to current date in &quot;From&quot; date<br>3. Should be able to select past date up to current date on &quot;To&quot; date<br>4. Should Display &quot;No data available&quot;</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">blocked due to attachedment and notes issue<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 210px"><th id="481468692R36" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">37</div></th><td class="s5">PRS-017-034</td><td class="s26">Creating a Canvass - Attachments are clickable</td><td class="s5"></td><td class="s5">Verify  Canvass - Attachments Filter in Adding a Supplier is working as expected</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s5">1. Clck Eye Icon in Actions Column in the Canvass sheet <br>2. Click the Attachment added in the Item Supplier<br>3. Select/populate future date in &quot;From&quot; date<br>4. Select/populate future date in &quot;To&quot; date<br>5. Select/Populate past date up to current date in &quot;From&quot; date<br>6.  Select/Populate past date up to current date in &quot;To&quot; date<br>7. Click Filter button</td><td class="s5"></td><td class="s5">1. Should be able to view Canvass Item details<br>2. Should open the Attachment into a New Modal for Preview<br>3.  Should not be able to select/populate future date in &quot;From&quot; date<br>4. Should not be able to select/populate future date in &quot;To&quot; date<br>5. Should be able to select past date up to current date in &quot;From&quot; date<br>6. Should be able to select past date up to current date on &quot;To&quot; date<br>7. Should Display all attachments that has been uploaded based on selected filter date</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">blocked due to attachedment and notes issue<br><br>2/4: Out of Scope/ Not yet implemented for steps 3-4<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012</a></td></tr><tr style="height: 19px"><th id="481468692R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s12">PRS-017-035</td><td class="s12">Allow Manual overriding of Canvass Quantity</td><td class="s12"></td><td class="s12">Verify Manual overriding of canvass quantity when saving draft first before submit</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s12">1. Click &quot;Enter Canvass&quot; button on Item in Adding a Supplier<br>2. Check the display of  Canvass Modal for adding a supplier<br>3. Populate all fields except Quantity field<br>4. Populate Quantity that has less than the Requested Quantity in the Requisition Slip<br>5. Clicked &quot;confirm&quot; on the Enter canvass modal<br>6. Click &quot;Saved Draft&quot; button on canvass sheet<br>7. Click &quot;Submit&quot; button on Canvass sheet<br>8. Click &quot;Submit&quot; button on the Modal<br></td><td class="s12"></td><td class="s12">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. A Note &quot; Note: Maximum of 1 supplier allowed (Transfer of Materials)&quot;<br>    j. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated<br>4. Quantity field should be populated<br>5. Should successfully saved entered cavass and quantity<br>6. Should successfully saved as draft with success toast message and temporary canvass sheet #<br>7. Should displayed a &quot;Submit Canvas Form - Inc&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;It seems that you have incomplete item quantity. Make sure all items are correct. Press submit if you still want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Submit&quot; buttons<br>8. Should allow the creation of Canvass for the Item</td><td class="s23">Failed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">CITYLANDPRS-766<br><br>2/4: issue still occurred<br><br>[Allow Manual overriding of Canvass Quantity] Error on clicking Save Draft button when populated quantity is less than the Requested Quantity</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1075">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1075</a></td></tr><tr style="height: 19px"><th id="481468692R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s12">PRS-017-036</td><td class="s12">Allow Manual overriding of Canvass Quantity</td><td class="s12"></td><td class="s12">Verify Manual overriding of canvass quantity when directly clicked submit</td><td class="s19"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts as Purchasing Staff<br>4. Requisition Slip has been created an Approved</a></td><td class="s12">1. Click &quot;Enter Canvass&quot; button on Item in Adding a Supplier<br>2. Check the display of  Canvass Modal for adding a supplier<br>3. Populate all fields except Quantity field<br>4. Populate Quantity that has less than the Requested Quantity in the Requisition Slip<br>5. Clicked &quot;confirm&quot; on the Enter canvass modal<br>6. Click &quot;Submit&quot; button on Canvass sheet<br>7. Click &quot;Submit&quot; button on the Modal<br></td><td class="s12"></td><td class="s12">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. A Note &quot; Note: Maximum of 1 supplier allowed (Transfer of Materials)&quot;<br>    j. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated<br>4. Quantity field should be populated<br>5. Should successfully saved entered cavass and quantity<br>6. Should displayed a &quot;Submit Canvas Form - Inc&quot; confirmation modal with contains of the ff:<br>     a. A description &quot;It seems that you have incomplete item quantity. Make sure all items are correct. Press submit if you still want to proceed with this action.&quot;<br>     b. A &quot;Cancel&quot; and &quot;Submit&quot; buttons<br>7. Should allow the creation of Canvass for the Item</td><td class="s23">Failed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s27">CITYLANDPRS-766<br>2/4: issue still occurred</td><td class="s14"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-766">https://youtrack.stratpoint.com/issue/CITYLANDPRS-766</a></td></tr><tr style="height: 210px"><th id="481468692R39" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">40</div></th><td class="s12">PRS-017-037</td><td class="s12">Assigning of Canvass Sheet Approvers with an existing Canvass Sheet</td><td class="s12"></td><td class="s12">Verify No Assigned Approvers - No assigned approver in any level in canvassing</td><td class="s12">1. A Canvass Sheet has been created without any assigned Approvers</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number<br>2. Click on Actions Button<br>3. Click Edit Canvass<br>4. Check for Approver<br>5. Check if user can submit<br>6. Add approver<br>7. Check if user can submit</td><td class="s12"></td><td class="s12">1. User won&#39;t be able to continue to PO Review without approver<br>2. After Adding Approvers, User should be able to submit</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R40" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">41</div></th><td class="s12">PRS-017-038</td><td class="s12">Assigning of Canvass Sheet Approvers with an existing Canvass Sheet</td><td class="s12"></td><td class="s12">Verify No Assigned Approvers - 1 or more approver is missing in canvassing</td><td class="s12">1. A Canvass Sheet has been created with missing approver</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number<br>2. Click on Actions Button<br>3. Click Edit Canvass<br>4. Check for Approver<br>5. Check if user can submit<br>6. Add approver<br>7. Check if user can submit</td><td class="s12"></td><td class="s12">1. User won&#39;t be able to continue to PO Review with missing approver<br>2. Approver should be sequential (ex. if level 2 is missing, level 3 and 4 approver cannot proceed)<br>2. After Adding Approvers, User should be able to submit</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">need to check l3 and l4 tom if they can approve or not<br>CITYLANDPRS-772<br><br>Need to find a way to remove Management</td><td class="s5"></td></tr><tr style="height: 210px"><th id="481468692R41" style="height: 210px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 210px">42</div></th><td class="s12">PRS-017-039</td><td class="s12">Assigning of Canvass Sheet Approvers with an existing Canvass Sheet</td><td class="s12"></td><td class="s12">Verify Canvass Sheet upon creation should have Assigned Approvers</td><td class="s12">1. A Canvass Sheet has been created</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number<br>2. Click on Actions Button<br>3. Click Edit Canvass<br>4. Check for Approver</td><td class="s12"></td><td class="s12">1. Approver should be assigned automatically upon creating canvass<br>2. Open, Not Approved and New Canvass Sheets should have Approvers</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="481468692R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s5">PRS-017-040</td><td class="s28">Adding of Items for Canvass Sheet</td><td class="s5"></td><td class="s5">Verify Accessing Adding of Items for Canvass Sheet by Purchasing Staff </td><td class="s5">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Requisition Slip has been Approved<br>3. Requisition Slip has been assigned to a Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, Click an approved Requisition Slip number that is assigned to a purchasing staff<br>2. Click Select Action button<br>3. Click Enter Canvass</td><td class="s29"></td><td class="s5">1. Able to click RS number<br>2. Able to display RS details<br>3. Able to click select actions button<br>4. Able to see other buttons inside select actions button<br>5. Able to transition to Canvass Sheet page<br>6. Able to display blank canvass sheet number<br>7. Able to display RS Number<br>8. Able to access even if request type is any of the following: OFM, Non-OFM, OFM Transfer of Materials, Non-OFM Transfer of Materials<br>9. Check for Different Request Types:<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="481468692R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s12">PRS-017-041</td><td class="s12">Adding of Items for Canvass Sheet</td><td class="s12"></td><td class="s12">Verify Print Function in Adding of Canvass Sheet Page</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Requisition Slip has been Approved<br>3. Requisition Slip has been assigned to a Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to a purchasing staff<br>2. Click Select Action button<br>3. Click Enter Canvass<br>4. Click Print Button</td><td class="s30"></td><td class="s12">1. Able to Print whole canvass sheet page</td><td class="s31">Out of Scope</td><td class="s32">Out of Scope</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">As per BA, print button is out of scope</td><td class="s5"></td></tr><tr style="height: 19px"><th id="481468692R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s5">PRS-017-042</td><td class="s28">Adding of Items for Canvass Sheet</td><td class="s5"></td><td class="s5">Verify Main RS Button in Adding of Canvass Sheet Page</td><td class="s5">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Requisition Slip has been Approved<br>3. Requisition Slip has been assigned to a Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, Click an approved Requisition Slip number that is assigned to a purchasing staff<br>2. Click Select Action button<br>3. Click Enter Canvass<br>4. Click Visit Main R.S button</td><td class="s29"></td><td class="s5">1. Able to display the Requisition details</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="481468692R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s12">PRS-017-043</td><td class="s12">Adding of Items for Canvass Sheet</td><td class="s12"></td><td class="s12">Verify Canvass Table Contents</td><td class="s12">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Requisition Slip has been Approved<br>3. Requisition Slip has been assigned to a Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s12">1. In the dashboard, Click an approved Requisition Slip number that is assigned to a purchasing staff<br>2. Click Select Action button<br>3. Click Enter Canvass<br>4. Click Add Items button<br>5. Click on any on the Items List<br>6. Click Add Item/s button<br></td><td class="s30"></td><td class="s12">1. If Canvass Sheet displays blank if no data has been set yet.<br>2. Able to click add items button<br>3. Add items modal displays what should be in the requisition slip<br>4. Able to click one or multiple items in add items modal<br>5. Able to see selected items in the right part of the modal<br>6. Able to delete selected item by clicking the X button<br>7. Able to display Added Items in Table of Canvass Sheet<br>8. Able to display Columns: Item, Account Code, Unit, Quantity, Canvass Status, Canvass, Actions<br>9. Canvass status displays New Status if no Canvass entered<br>10. Canvass displays Enter Canvass Button<br>11. Actions has disabled buttons for Edit, Down, View</td><td class="s23">Failed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5: Actions for edit and eye icons are enabled<br><br>[Adding of Items for Canvass Sheet] Search and Clear button is not working in Add Item/s Modal<br>Other function seems to be working fine<br><br>[Adding of Items for Canvass Sheet] Total items selected in Add Item/s Modal is not displaying the correct value<br><br>[Adding of Items for Canvass Sheet] Upper right save draft is not being fully shown<br><br>[Adding of Items for Canvass Sheet] Items Table sorting is not working<br><br>[QA BUGS] [Supplier for Transfer of Materials] Canvass Status still displayed as &quot;New&quot; even there are already selected suppliers<br><br>[Adding of Items for Canvass Sheet] Wrong canvass calculations</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-840">https://youtrack.stratpoint.com/issue/CITYLANDPRS-840<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-994<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-995<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-997<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-999<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-850<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1061</a></td></tr><tr style="height: 19px"><th id="481468692R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s5">PRS-017-044</td><td class="s28">Adding of Items for Canvass Sheet</td><td class="s5"></td><td class="s5">Verify Canvass Sheet Attachment and Notes</td><td class="s5">1. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM<br>       ii. Non-OFM<br>       iii. OFM Transfer of Materials<br>       iv. Non-OFM Transfer of Materials<br>2. Requisition Slip has been Approved<br>3. Requisition Slip has been assigned to a Purchasing Staff<br>4. User must be signed in to the right account</td><td class="s5">1. In the dashboard, Click an approved Requisition Slip number that is assigned to a purchasing staff<br>2. Click Select Action button<br>3. Click Enter Canvass<br>4. Click Add Items button<br>5. Click on any on the Items List<br>6. Click Add Item/s button<br>7. Click Select Attachment/s button<br>8. Select an attachment file<br>9. Input in Notes<br><br><br>Negative Scenario:<br>1. Select a different file from the listed<br>2. Select a file that is over 25mb<br>3. Input emojis in Notes<br>4. Input more than 100 characters in notes.</td><td class="s29"></td><td class="s5">1. Able to select file to attach<br>2. Only the ff file attachment are allowed: PDF, Doc File, Excel, CSV, JPG, PNG, JPEG<br>3. 25mb max file size for attachment<br>4. 100 Characters only for notes<br>5. Alphanumeric and special characters except emojis are allowed for input.</td><td class="s23">Failed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5: Undefined notes displayed when check notes in Canvass sheet<br><br>[Viewing of Canvass Sheet by Approver] Check Attachment button is showing an error upon clicking<br>(Cannot check Check Attachments Modal)<br><br>[Viewing of Canvass Sheet by Approver] Check Notes, Note is being cut off when characters are all together<br><br>[Viewing of Canvass Sheet by Approver] Date picker issues in notes item view button<br><br>[Viewing of Canvass Sheet by Approver] Notes modal inconsistency</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-847">https://youtrack.stratpoint.com/issue/CITYLANDPRS-847<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1012<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1015<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1027<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1025</a></td></tr><tr style="height: 19px"><th id="481468692R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s5">PRS-017-045</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials canvass display</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff</a></td><td class="s34">1. Click &quot;Enter Canvass&quot; button on Transfer of Materials Item<br>2. Check the display of  Canvass Modal for Transfer  of Materials</td><td class="s36"></td><td class="s34">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons</td><td class="s23">Failed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5: Able to add max of 4 suppliers</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s5">PRS-017-046</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when validating all fields in Enter Canvass modal</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff</a></td><td class="s34">1. Click &quot;Enter Canvass&quot; button on Transfer of Materials Item<br>2. Validate Supplier Field<br>3. Validate Terms field<br>4. Validate Quantity field<br>5. Validate Unit price fiekd<br>6. Validate Dscount field<br>7. Validate Unit Price (discounted) field<br>8. Validate Attachments field<br>9. Validate Notes field</td><td class="s36"></td><td class="s34">1. Should display the Modal for the Supplier<br>2. Supplier field should met the ff:<br>        i. Should be Drop-down Values of Company and Projects<br>        ii. Should only allow entering of one Supplier<br>           i) Cannot add another Supplier<br>3. Terms Shoud met the ff:<br>        i. Should be Drop-down Values of<br>           i) NET 15 <br>           ii) NET 30<br>           iii) Cash in Advance (CIA)<br>           iv) Cash on Delivery (COD)<br>           v) 10% DP, Balance upon delivery<br>           vi) 20% DP, Balance upon delivery<br>           vii) 30% DP, Balance upon delivery<br>           viii) 50% DP, Balance upon delivery<br>           ix) 80% DP, Balance upon delivery<br>           x) 10% DP, PB, 10% RETENTION<br>           xi) 20% DP, PB, 10% RETENTION<br>           xii) 30% DP, PB, 10% RETENTION<br>4. Quantity Should met the ff:<br>        i. Quantity that was communicated by the Supplier to the Purchasing Staff<br>5.  Unit Price should met the ff<br>        i. Unit Price that was communicated by the Supplier to the Purchasing Staff<br>6.  Discount should met the ff:<br>        i. Fixed Amount - the fixed Amount of the Item that was already discounted<br>           i) Should display to the Unit Price (discounted)<br>        ii. Percentage - the Discount Percentage communicated by the Supplier to the Purchasing Staff<br>           i) Should get the lessen percentage of the Unit Price that should be deducted to the Unit Price<br>7. Unit Price (discounted) should met the ff:<br>        i. Computed if a Discount has been entered<br>        ii. Should be disabled<br>8. Attachmentsshould met the ff:<br>       i. When Attachment Field is clicked should implement a File Validation<br>          i) File Formats should be PDF, Doc File, Excel or CSV, JPG, PNG, JPEG<br>          ii) Allow multiple file for uploading, Maximum File size is 25MB per File<br>9. Notes should met the ff;<br>        i. Should allow 100 Characters for the Notes that will have Alphanumeric and Sepcial Characters except Emojis</td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5: Able to add max of 4 suppliers for step 2<br>-tagged as partially passed<br><br>[Adding of Items for Canvass Sheet] Search and Clear button is not working in Add Item/s Modal<br>Other function seems to be working fine<br><br>[Adding of Items for Canvass Sheet] Total items selected in Add Item/s Modal is not displaying the correct value<br><br>[Adding of Items for Canvass Sheet] Upper right save draft is not being fully shown<br><br>[Adding of Items for Canvass Sheet] Items Table sorting is not working<br><br>[QA BUGS] [Supplier for Transfer of Materials] Canvass Status still displayed as &quot;New&quot; even there are already selected suppliers<br><br>[Adding of Items for Canvass Sheet] Wrong canvass calculations</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-994<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-995<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-997<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-999<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-850<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1061</a></td></tr><tr style="height: 19px"><th id="481468692R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s5">PRS-017-047</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when Entering Canvass and clicked submit</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff</a></td><td class="s34">1. Click &quot;Enter Canvass&quot; button on Transfer of Materials Item<br>2. Check the display of  Canvass Modal for Transfer  of Materials<br>3. Populate all fields<br>4. Attach an Attachment with Maximum File size is 25MB <br>5. Attach an Attachment with greater than 25MB of Max size<br>6. Populate Notes<br>7. Click &quot;Confirm&quot; button on the canvass modal<br>8. Check the &quot;Enter Canvass&quot; button of the same item<br>9. Clck Submit button<br></td><td class="s36"></td><td class="s34">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated<br>4. Attachments should be attached<br>5. Attachment should not be attached and error message should be displayed<br>6. Notes field should be populated<br>7. Should add the suplier to the item<br>8. Should disable the Enter Canvass Button and Should change the Button Text to Canvass Entered.<br>         a. Should Enable the Actions Buttons<br>          b. Should update the Status to For Approval<br>9. Should allow submission of all of the Items were Canvassed<br></td><td class="s23">Failed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5:<br>- Able to add max of 4 suppliers for step 2<br>- Status incorrect on Step 8<br><br>[QA BUGS] [Supplier for Transfer of Materials] Canvass Status still displayed as &quot;New&quot; even there are already selected suppliers</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-850<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s5">PRS-017-048</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when Entering Canvass and Save as Draft</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff</a></td><td class="s34">1. Click &quot;Enter Canvass&quot; button on Transfer of Materials Item<br>2. Check the display of  Canvass Modal for Transfer  of Materials<br>3. Populate all fields<br>4. Attach an Attachment with Maximum File size is 25MB <br>5. Attach an Attachment with greater than 25MB of Max size<br>6. Populate Notes<br>7. Click &quot;Confirm&quot; button on the canvass modal<br>8. Check the &quot;Enter Canvass&quot; button of the same item<br>9. Clck Save Draft  button<br></td><td class="s36"></td><td class="s34">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated<br>4. Attachments should be attached<br>5. Attachment should not be attached and error message should be displayed<br>6. Notes field should be populated<br>7. Should add the suplier to the item<br>8. Should disable the Enter Canvass Button and Should change the Button Text to Canvass Entered.<br>         a. Should Enable the Actions Buttons<br>          b. Should update the Status to For Approval<br>9. Should allow Saving the Canvass Sheet as Draft if the other Items does not have a Supplier entered to them<br>         a. Should nominate a Temporary Canvass Sheet Number<br>            i.FORMAT: CS-TMP-[CANVASS NUMBER FORMAT]<br></td><td class="s15">Passed</td><td class="s13">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5:<br>- Able to add max of 4 suppliers for step 2<br>- Status incorrect on Step 8<br><br>[Adding of Items for Canvass Sheet] Upper right save draft is not being fully shown</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-850<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-997</a></td></tr><tr style="height: 19px"><th id="481468692R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s5">PRS-017-049</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when Entering Canvass and Clicked cancel</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff</a></td><td class="s34">1. Click &quot;Enter Canvass&quot; button on Transfer of Materials Item<br>2. Check the display of  Canvass Modal for Transfer  of Materials<br>3. Populate all fields<br>4. Attach an Attachment with Maximum File size is 25MB <br>5. Attach an Attachment with greater than 25MB of Max size<br>6. Populate Notes<br>7. Click &quot;Cancel&quot; button on the canvass modal<br>8. Click &quot;Continue&quot; on Cancel Canvass&quot; Modal<br></td><td class="s36"></td><td class="s34">1. Should display the Modal for the Supplier<br>2. Should display  the following:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated<br>4. Attachments should be attached<br>5. Attachment should not be attached and error message should be displayed<br>6. Notes field should be populated<br>7. Should display a &quot;Cancel Canvass&quot; confirmation modal withe contains of the ff:<br>    a. A Description &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>    b. &quot;Cancel&quot; and &quot;Continue&quot; Buttons<br>8. Should cancel adding of the Supplier to the Item</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/5:<br>- Able to add max of 4 suppliers for step 2<br>- Status incorrect on Step 8</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-850<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s5">PRS-017-050</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when Edit Added supplier in the Item thru Actions column</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff<br>7. A Canvass Supplier for an Item already added</a></td><td class="s34">1. On Canvass Sheet, Click edit icon in the item of Actions column<br>2. Check Edit Canvass Display<br>3. Update all fields<br>4. Update attachments with max file size of 25 MB<br>5 Upload an attachemnts with greater than 25MB file size<br>6. Update Notes field<br>7. Click &quot;Confirm&quot;</td><td class="s36"></td><td class="s34">1. Should display the &quot;Edit Canvass&quot; Modal <br>2. Should display  and enabled the  following fields except for Unit Price (discounted) field:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. All Fields are populated and updated<br>4. Attachments should be updated<br>5. Attachment should not be attached and error message should be displayed<br>6. Notes field should be populated  and updated<br>7. Should successfully saved the updated details</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/6:<br>- Able to add max of 4 suppliers for step 2</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s5">PRS-017-051</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when Edit Added supplier in the Item thru Edit button in View Canvass </td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff<br>7. A Canvass Supplier for an Item already added</a></td><td class="s34">1. On Canvass Sheet, Click Eye icon in the item of Actions column<br>2. Check View Canvass Display<br>3. Click Edit button<br>4. Update all fields<br>5. Update attachments with max file size of 25 MB<br>6 Upload an attachemnts with greater than 25MB file size<br>7. Update Notes field<br>8. Click &quot;Confirm&quot;</td><td class="s36"></td><td class="s34">1. Should display the &quot;Edit Canvass&quot; Modal <br>2. Should display  the following fields that all are should be disabled:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>3. Should display  and enabled the  following fields except for Unit Price (discounted) field:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. A Note &quot; Note: Maximum of 1 supplier allowed (Transfer of Materials)&quot;<br>    j. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br>4. All Fields are populated and updated<br>5. Attachments should be updated<br>6. Attachment should not be attached and error message should be displayed<br>7. Notes field should be populated  and updated<br>8. Should successfully saved the updated details</td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5">2/6:<br>- Able to add max of 4 suppliers for step 2</td><td class="s11"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-848">https://youtrack.stratpoint.com/issue/CITYLANDPRS-848<br><br></a></td></tr><tr style="height: 19px"><th id="481468692R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s5">PRS-017-052</td><td class="s33">Supplier for Transfer of Materials</td><td class="s34"></td><td class="s34">Verify Supplier for Transfer of Materials when Viewing Added supplier in the Item</td><td class="s35"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. A Requisition Slip has been created<br>   a. Request Type of<br>       i. OFM Transfer of Materials<br>       ii. Non-OFM Transfer of Materials<br>5. Requisition Slip has been Approved<br>6. Requisition Slip has been assigned to a Purchasing Staff<br>7. A Canvass Supplier for an Item already added</a></td><td class="s34">1. On Canvass Sheet, Click Eye icon in the item of Actions column<br>2. Check View Canvass Display<br></td><td class="s36"></td><td class="s34">1. Should display the &quot;Edit Canvass&quot; Modal <br>2. Should display  the following fields that all are should be disabled:<br>    a. Supplier field<br>    b. Terms field<br>    c. Quantity field<br>    d. Unit Price dropdown field<br>    e. Discount field<br>    f. Unit Price (discounted) field<br>    g. Attachments field<br>    h. Notes field<br>    i. &quot;Cancel&quot; and &quot;Confirm&quot; Buttons<br></td><td class="s15">Passed</td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s5"></td><td class="s14"></td></tr><tr style="height: 209px"><th id="481468692R55" style="height: 209px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 209px">56</div></th><td class="s30 softmerge"><div class="softmerge-inner" style="width:100px;left:-1px">OFM Item Sync Scenario</div></td><td class="s12">Scenario 3:<br>Verify that RS with “Canvassing” status allows updates, cancellations, or item replacements.</td><td class="s30"></td><td class="s12"></td><td class="s12"></td><td class="s12">1. Create an RS with status “Canvassing”.<br>2. Update the OFM List.<br>3. Attempt to update the item quantity.<br>4. Replace an item.<br>5. Cancel the RS.</td><td class="s12">1. A Requisition Slip with Canvassing status</td><td class="s12">1. Updates and replacements restart the approval process. Cancellations return the requested quantity to the Remaining GFQ.<br><br>2. Should not proceed with succeeding Processes</td><td class="s9"></td><td class="s9"></td><td class="s13">Failed</td><td class="s9"></td><td class="s9"></td><td class="s9"></td><td class="s9">[OFM Item Sync Scenario] RS in Canvass Approval status has an Item that was changed in the backend but didn&#39;t update in RS</td><td class="s37"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1151">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1151</a></td></tr><tr style="height: 209px"><th id="481468692R56" style="height: 209px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 209px">57</div></th><td class="s30 softmerge"><div class="softmerge-inner" style="width:100px;left:-1px">OFM Item Sync Scenario</div></td><td class="s12">Scenario 3:<br>Verify update or replaced items in R.S status of canvassing or partially canvassed</td><td class="s30"></td><td class="s12"></td><td class="s12"></td><td class="s12">1. Click R.S<br>2. Update/replaced the qty of the items <br>3. click submit</td><td class="s12">1. already updated OFM</td><td class="s12">1. Should repeat the Approval Process of Canvassing</td><td class="s9"></td><td class="s9"></td><td class="s13">Failed</td><td class="s9"></td><td class="s9"></td><td class="s9"></td><td class="s9">[OFM Item Sync Scenario] RS in Canvass Approval status has an Item that was changed in the backend but didn&#39;t update in RS</td><td class="s37"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1151">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1151</a></td></tr><tr style="height: 209px"><th id="481468692R57" style="height: 209px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 209px">58</div></th><td class="s30 softmerge"><div class="softmerge-inner" style="width:100px;left:-1px">OFM Item Sync Scenario</div></td><td class="s12">Scenario 3:<br>Verify Cancelled R.S status of canvassing or partially canvassed</td><td class="s30"></td><td class="s12"></td><td class="s12"></td><td class="s12">1. Click R.S<br>2. Cancel the RS.<br>3. Check Remaining GFQ and RS status.</td><td class="s12">1. already updated OFM</td><td class="s12">1.   If RS is Cancelled, should add the Requested Item Quantity back to the Remaining GFQ<br></td><td class="s9"></td><td class="s9"></td><td class="s13">Failed</td><td class="s9"></td><td class="s9"></td><td class="s9"></td><td class="s9">[OFM Item Sync Scenario] GFQ doesn&#39;t update on the OFM List when an RS is cancelled</td><td class="s38"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1155">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1155</a></td></tr></tbody></table></div>