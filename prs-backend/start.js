/**
 * Production-safe startup script
 *
 * This script checks the environment and decides whether to run migrations
 * before starting the application.
 */

require('dotenv').config();
const { execSync } = require('child_process');

// Get environment
const env = process.env.NODE_ENV || 'development';

// In production, allow structured logs to pass through
// We're using Pino for structured logging, so we don't need to filter console logs
if (env === 'production') {
  // Save original console methods
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;

  // Allow JSON logs to pass through
  console.log = (...args) => {
    // Allow JSON logs and startup messages
    if (
      // Allow structured JSON logs
      (args[0] && typeof args[0] === 'string' && (args[0].startsWith('{') || args[0].includes('"level":'))) ||
      // Allow startup messages
      (args[0] && typeof args[0] === 'string' && (
        args[0].includes('==================================================') ||
        args[0].includes('PRS BACKEND SERVER') ||
        args[0].includes('Starting application') ||
        args[0].includes('Skipping automatic migrations') ||
        args[0].includes('Server listening at')
      ))
    ) {
      originalConsoleLog(...args);
    }
  };

  // Allow all errors
  console.error = originalConsoleError;

  // Keep warn and info for important messages
  // These are handled by our structured logging system
}

// Clear startup banner for clean logs
console.log('\n==================================================');
console.log(`PRS BACKEND SERVER - ${env.toUpperCase()} MODE`);
console.log('==================================================\n');

// Log startup information
console.log(`Starting application in ${env} mode`);

try {
  // In production, we don't run migrations automatically for safety
  if (env === 'production') {
    console.log('Skipping automatic migrations in production mode');
    console.log('To run migrations manually, use: npm run migrate:prod');

    // Start the application without migrations
    require('./index');
  } else {
    // In development, run migrations and then start
    console.log('Running migrations and seeds...');
    execSync('npm run migrate-and-seed', { stdio: 'inherit' });

    // Start the application
    require('./index');
  }
} catch (error) {
  console.error('Error during startup:', error);
  process.exit(1);
}
