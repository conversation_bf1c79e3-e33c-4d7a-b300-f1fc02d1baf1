# Workflows

This section details the key business processes in the PRS system. Each workflow describes the steps, actors, and business rules involved in a specific process.

For information about how these workflows are implemented in the frontend, see [Frontend Implementation](frontend-implementation.md).

## Frontend-Backend Workflow Alignment

We've added new documentation to help align frontend and backend workflow implementations:

- [Frontend-Backend Alignment](frontend-backend-alignment.md): Overview of the architecture and synchronization points
- [Status Transition Matrix](status-transition-matrix.md): Detailed matrix of valid status transitions
- [API Contract](api-contract.md): API contract for workflow status transitions
- [Improving Alignment](improving-alignment.md): Recommendations for improving frontend-backend alignment
- [Frontend-Backend Workflow Alignment](frontend-backend-workflow-alignment.md): Detailed guide on aligning Zustand stores with backend database status fields

These documents include visual diagrams and detailed guidance to ensure consistent workflow implementation across the system.

## Available Workflows

- [Requisition Workflow](requisition-workflow.md): The process of creating and approving requisitions
- [Canvass Workflow](canvass-workflow.md): The process of canvassing suppliers for quotes
- [Purchase Order Workflow](purchase-order-workflow.md): The process of reviewing and approving purchase orders
- [Delivery Report Workflow](delivery-workflow.md): The process of confirming and recording delivery of purchased items
- [Invoice Report Workflow](invoice-workflow.md): The process of recording invoice from supplier
- [Payment Request Workflow](payment-workflow.md): The process of requesting for payments to suppliers
- [Non-Requisition Workflow](non-requisition-workflow.md): The process for non-requisition-based payment requests
- [Approval Workflow](approval-workflow.md): The general approval process for various documents
- [Notification Workflow](notification-workflow.md): The process of sending notifications to users
- [Attachment Workflow](attachment-workflow.md): The process of managing document attachments
- [Master Data Workflow](master-data-workflow.md): The process of managing master data
- [Item Management Workflow](item-management-workflow.md): The process of managing items in the system
- [Supplier Workflow](supplier-workflow.md): The process of managing suppliers
- [User Management Workflow](user-management-workflow.md): The process of managing users and permissions
