# Temporarily commented out until backend service is running
# upstream backend_servers {
#     server backend:4000;
#     # Add more backend servers if scaling horizontally
#     # server backend2:4000;
# }

# Server block for prs.stratpoint.io (default server)
server {
    listen 80 default_server;
    server_name prs.stratpoint.io _;

    # Redirect to HTTPS in production
    # return 301 https://$host$request_uri;

    # For development/testing, allow HTTP
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API
    location /api/ {
        proxy_pass http://backend:4000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # For WebSocket support if needed
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Restrict access to Cloudflare IPs and internal networks
    # Cloudflare IPs from https://www.cloudflare.com/ips/
    # IPv4
    allow ************/20;
    allow ************/22;
    allow ************/22;
    allow **********/22;
    allow ************/18;
    allow *************/18;
    allow ************/20;
    allow ************/20;
    allow *************/22;
    allow ************/17;
    allow ***********/15;
    allow **********/13;
    allow **********/14;
    allow **********/13;
    allow **********/22;

    # Also allow internal networks for local development
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
}

# Server block for adminer.stratpoint.io
server {
    listen 80;
    server_name adminer.stratpoint.io;

    # Redirect to HTTPS in production
    # return 301 https://$host$request_uri;

    # For development/testing, allow HTTP
    # Temporarily commented out until frontend service is running
    # location / {
    #     proxy_pass http://frontend:3000;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    # }

    # Direct proxy to Adminer
    location / {
        proxy_pass http://adminer:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Restrict access to Cloudflare IPs and internal networks
        # Cloudflare IPs from https://www.cloudflare.com/ips/
        # IPv4
        allow ************/20;
        allow ************/22;
        allow ************/22;
        allow **********/22;
        allow ************/18;
        allow *************/18;
        allow ************/20;
        allow ************/20;
        allow *************/22;
        allow ************/17;
        allow ***********/15;
        allow **********/13;
        allow **********/14;
        allow **********/13;
        allow **********/22;

        # Also allow internal networks for local development
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;

        # Deny all other IPs
        deny all;
    }

    # Backend API - Temporarily commented out until backend service is running
    # location /api/ {
    #     proxy_pass http://backend_servers/;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #
    #     # For WebSocket support if needed
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    # }

    # MinIO API - Restricted to internal access
    location /minio/ {
        proxy_pass http://minio:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Restrict access to internal networks only
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
    }

    # MinIO Console - Restricted to internal access
    location /minio-console/ {
        proxy_pass http://minio:9001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Restrict access to internal networks only
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
    }

    # Adminer - Database Management (removed since we're using root location)

    # Portainer - Container Management
    location /portainer/ {
        proxy_pass http://portainer:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Restrict access to internal networks only
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
    }

    # Nginx Proxy Manager removed to simplify architecture
}

# Server block for portainer.stratpoint.io
server {
    listen 80;
    server_name portainer.stratpoint.io;

    # Redirect to HTTPS in production
    # return 301 https://$host$request_uri;

    # For development/testing, allow HTTP
    location / {
        proxy_pass http://portainer:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # For WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Restrict access to Cloudflare IPs and internal networks
    # Cloudflare IPs from https://www.cloudflare.com/ips/
    # IPv4
    allow ************/20;
    allow ************/22;
    allow ************/22;
    allow **********/22;
    allow ************/18;
    allow *************/18;
    allow ************/20;
    allow ************/20;
    allow *************/22;
    allow ************/17;
    allow ***********/15;
    allow **********/13;
    allow **********/14;
    allow **********/13;
    allow **********/22;

    # Also allow internal networks for local development
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;

    # Deny all other IPs
    deny all;
}



# HTTPS server - uncomment and configure for production
# server {
#     listen 443 ssl;
#     server_name ***********;
#
#     # SSL Configuration
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#
#     # Warning message for direct IP access
#     location / {
#         return 403 '<!DOCTYPE html>
# <html>
# <head>
#     <title>Access Restricted</title>
#     <style>
#         body {
#             font-family: Arial, sans-serif;
#             background-color: #f8f8f8;
#             color: #333;
#             margin: 0;
#             padding: 30px;
#             text-align: center;
#         }
#         .container {
#             max-width: 800px;
#             margin: 0 auto;
#             background-color: #fff;
#             border-radius: 5px;
#             box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
#             padding: 30px;
#         }
#         h1 {
#             color: #e74c3c;
#             margin-bottom: 20px;
#         }
#         .warning-icon {
#             font-size: 60px;
#             color: #e74c3c;
#             margin-bottom: 20px;
#         }
#         p {
#             line-height: 1.6;
#             margin-bottom: 15px;
#         }
#         .footer {
#             margin-top: 30px;
#             font-size: 12px;
#             color: #777;
#         }
#     </style>
# </head>
# <body>
#     <div class="container">
#         <div class="warning-icon">⚠️</div>
#         <h1>Access Restricted</h1>
#         <p>Direct IP access to this server is not permitted.</p>
#         <p>Please use the appropriate domain name to access the services.</p>
#         <p>If you believe you have reached this page in error, please contact your system administrator.</p>
#         <div class="footer">
#             <p>This server is protected by Cloudflare and additional security measures.</p>
#         </div>
#     </div>
# </body>
# </html>';
#         add_header Content-Type text/html;
#     }
#
#     # Backend API - Temporarily commented out until backend service is running
#     # location /api/ {
#     #     proxy_pass http://backend_servers/;
#     #     proxy_set_header Host $host;
#     #     proxy_set_header X-Real-IP $remote_addr;
#     #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#     #     proxy_set_header X-Forwarded-Proto $scheme;
#     # }
#
#     # Management and monitoring endpoints (protected)
#     location /portainer/ {
#         proxy_pass http://portainer:9000/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Restrict access to internal networks only
#         allow 127.0.0.1;
#         allow 10.0.0.0/8;
#         allow **********/12;
#         allow ***********/16;
#         deny all;
#     }
#
#     location /adminer/ {
#         proxy_pass http://adminer:8080/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Restrict access to internal networks only
#         allow 127.0.0.1;
#         allow 10.0.0.0/8;
#         allow **********/12;
#         allow ***********/16;
#         deny all;
#     }
#
#     location /grafana/ {
#         proxy_pass http://grafana:3000/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Restrict access to internal networks only
#         allow 127.0.0.1;
#         allow 10.0.0.0/8;
#         allow **********/12;
#         allow ***********/16;
#         deny all;
#     }
#
#     location /prometheus/ {
#         proxy_pass http://prometheus:9090/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Restrict access to internal networks only
#         allow 127.0.0.1;
#         allow 10.0.0.0/8;
#         allow **********/12;
#         allow ***********/16;
#         deny all;
#     }
#
#     location /minio/ {
#         proxy_pass http://minio:9000/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Restrict access to internal networks only
#         allow 127.0.0.1;
#         allow 10.0.0.0/8;
#         allow **********/12;
#         allow ***********/16;
#         deny all;
#     }
#
#     location /minio-console/ {
#         proxy_pass http://minio:9001/;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # Restrict access to internal networks only
#         allow 127.0.0.1;
#         allow 10.0.0.0/8;
#         allow **********/12;
#         allow ***********/16;
#         deny all;
#     }
#
#     # Nginx Proxy Manager removed to simplify architecture
# }

# Server block for direct IP access (fallback)
server {
    listen 80;
    server_name ***********;

    # Warning message for direct IP access
    location / {
        return 403 '<!DOCTYPE html>
<html>
<head>
    <title>Access Restricted</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f8f8;
            color: #333;
            margin: 0;
            padding: 30px;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .warning-icon {
            font-size: 60px;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        p {
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning-icon">⚠️</div>
        <h1>Access Restricted</h1>
        <p>Direct IP access to this server is not permitted.</p>
        <p>Please use the appropriate domain name to access the services.</p>
        <p>If you believe you have reached this page in error, please contact your system administrator.</p>
        <div class="footer">
            <p>This server is protected by Cloudflare and additional security measures.</p>
        </div>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }
}
