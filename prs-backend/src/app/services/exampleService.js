const BaseService = require('./baseService');

/**
 * Example service that extends BaseService
 */
class ExampleService extends BaseService {
  /**
   * Creates a new ExampleService instance
   * 
   * @param {Object} container - Dependency injection container
   */
  constructor(container) {
    super(container);
    
    const { exampleRepository, utils } = container;
    
    this.exampleRepository = exampleRepository;
    this.utils = utils;
  }

  /**
   * Creates a new example
   * 
   * @param {Object} data - Example data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Created example
   */
  async createExample(data, options = {}) {
    return await this.create(this.exampleRepository, data, options);
  }

  /**
   * Updates an existing example
   * 
   * @param {string|number} id - Example ID
   * @param {Object} data - Example data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Updated example
   */
  async updateExample(id, data, options = {}) {
    return await this.update(this.exampleRepository, { id }, data, options);
  }

  /**
   * Deletes an example
   * 
   * @param {string|number} id - Example ID
   * @param {Object} options - Additional options
   * @returns {Promise<number>} - Number of deleted examples
   */
  async deleteExample(id, options = {}) {
    return await this.delete(this.exampleRepository, { id }, options);
  }

  /**
   * Gets an example by ID
   * 
   * @param {string|number} id - Example ID
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Found example
   */
  async getExampleById(id, options = {}) {
    return await this.getById(this.exampleRepository, id, options);
  }

  /**
   * Gets all examples with pagination
   * 
   * @param {Object} query - Query parameters
   * @returns {Promise<Object>} - Found examples with pagination
   */
  async getAllExamples(query = {}) {
    return await this.getAll(this.exampleRepository, query);
  }

  /**
   * Example of a complex operation using transaction
   * 
   * @param {Object} data - Example data
   * @returns {Promise<Object>} - Operation result
   */
  async complexOperation(data) {
    return await this.withTransaction(async (transaction) => {
      // Create main record
      const example = await this.createExample(data, { transaction });
      
      // Create related records
      if (data.relatedItems && Array.isArray(data.relatedItems)) {
        for (const item of data.relatedItems) {
          await this.exampleRepository.create({
            ...item,
            exampleId: example.id
          }, { transaction });
        }
      }
      
      return example;
    });
  }
}

module.exports = ExampleService;
