# Non-Requisition Payment Request Workflow

This document outlines the complete workflow for non-requisition payments in the PRS system, which allows for processing payments that don't follow the standard requisition process.

## Workflow Diagram

```mermaid
flowchart TD
  Start([Create or Edit
  Non-RS Payment
  Request]) -->|isDraft| S1[Draft]
  S1 -->|Edit| Start
  Start -->|Submitted| S2[For PR Approval]
  S2 -->|Fully Approved| S3[Closed PR]
  S2 -->|Rejected| S4[PR Rejected]
  S4 -->|Return| Start
```

## Status Definitions

| Status | Description |
|--------|-------------|
| DRAFT | Initial status when a non-requisition payment request is created but not submitted |
| FOR_APPROVAL | Non-requisition payment request has been submitted for approval |
| CLOSED | Non-requisition payment request has been approved and processed |
| REJECTED | Non-requisition has been rejected by an approver |

## Detailed Workflow Steps

### 1. Non-Requisition Creation

**Actor**: Requester

**Actions**:

- Create a new non-requisition payment request
- Fill in required fields (company, department, supplier, invoice details, etc.)
- Add items and pricing information
- Save as draft or submit for approval

**Status Transitions**:

- New → DRAFT (if saved as draft)
- New → FOR_APPROVAL (if submitted immediately)

**Business Rules**:

- If submitted, all required fields must be filled
- An non-requisition request can be created as a draft or submitted immediately
- Number is auto-generated
- Letter is auto-generated
- Invoice number and date are required
- Supplier must be selected
- Company and department must be specified
- Category must be selected (association, company, or project)

### 2. Non-Requisition Submission

**Actor**: Requester

**Actions**:

- Update a draft non-requisition payment request
- Submit the non-requisition payment request for approval

**Status Transitions**:

- DRAFT → FOR_APPROVAL

**Business Rules**:

- All required fields must be filled
- Approvers are assigned based on project and department
- Attachments (like invoice scans) may be required

### 3. Non-Requisition Approval

**Actor**: Approvers

**Actions**:

- Review the non-requisition payment request
- Approve or reject the non-requisition payment request
- Add note of approval (optional) or rejection (required)
- Add additional approvers (optional)

**Status Transitions**:

- FOR_APPROVAL → CLOSED (if approved)
- FOR_APPROVAL → REJECTED (if rejected)

**Business Rules**:

- Approvers must be assigned to the non-requisition
- Only FOR_APPROVAL or REJECTED non-requisition payment requests can be approved
- User must be an assigned approver for the payment request
- Status will remain as FOR APPROVAL until fully approved
- Approvers are processed in order by level
- Additional approvers can be added during the approval process
- Approvers can input a note during approval
- If all approvers approve, status changes to CLOSED
- Rejection requires a reason/comment

## Example Scenarios

### Scenario 1: Standard Non-Requisition Flow

1. Requester creates a non-requisition with invoice details
2. Requester submits the non-requisition for approval
3. Approvers review and approve the non-requisition
4. Non-requisition is closed and ready for payment processing

### Scenario 2: Non-Requisition with Rejection

1. Requester creates and submits a non-requisition
2. An approver rejects the non-requisition with a reason
3. Requester updates the non-requisition and resubmits it
4. Approvers approve the non-requisition
5. Non-requisition is closed

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/nonRequisitionController.js`
- **Service**: `src/app/services/nonRequisitionService.js`
- **Repository**: `src/infra/repositories/nonRequisitionRepository.js`
- **Entity**: `src/domain/entities/nonRequisitionEntity.js`
- **Constants**: `src/domain/constants/nonRSConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic based on actual code
async function updateNonRequisitionStatus(nonRequisitionId, newStatus, reason, transaction) {
  const nonRequisition = await nonRequisitionRepository.findOne({
    where: { id: nonRequisitionId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'draft': ['for_approval', 'cancelled'],
    'for_approval': ['closed', 'rejected', 'cancelled'],
    'rejected': ['for_approval'],
  };
  
  if (!validTransitions[nonRequisition.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${nonRequisition.status} to ${newStatus}`);
  }
  
  // Additional validation for rejection or cancellation
  if ((newStatus === 'rejected' || newStatus === 'cancelled') && !reason) {
    throw new Error(`Reason is required for ${newStatus} status`);
  }
  
  // Update status
  await nonRequisition.update({ 
    status: newStatus,
    statusReason: ['rejected', 'cancelled'].includes(newStatus) ? reason : null,
    statusChangedAt: new Date(),
  }, { transaction });
  
  return nonRequisition;
}
```

### Non-Requisition Approval

```javascript
// Example approval logic based on actual code
async function approveNonRequisition({
  nonRequisitionId,
  approverId,
  transaction,
}) {
  const nonRequisition = await nonRequisitionRepository.findOne({
    where: { id: nonRequisitionId },
    include: [{ association: 'nonRequisitionApprovers' }],
    transaction,
  });
  
  if (nonRequisition.status !== 'for_approval' && nonRequisition.status !== 'rejected') {
    throw new Error('Non-requisition cannot be approved');
  }
  
  const approver = nonRequisition.nonRequisitionApprovers.find(a => a.userId === approverId);
  
  if (!approver) {
    throw new Error('User is not an approver for this non-requisition');
  }
  
  if (approver.status === 'approved') {
    throw new Error('User has already approved this non-requisition');
  }
  
  await approver.update({ status: 'approved' }, { transaction });
  
  const allApprovers = nonRequisition.nonRequisitionApprovers;
  const pendingApprovers = allApprovers.filter(a => a.status !== 'approved');
  
  if (pendingApprovers.length === 0) {
    await nonRequisition.update({ status: 'closed' }, { transaction });
    return true; // All approved
  }
  
  return false; // Not all approved yet
}
```

## Common Issues and Solutions

### Issue 1: Cannot Submit Non-Requisition

**Cause**: Missing required fields or validation errors.

**Solution**:

- Check validation error messages
- Ensure all required fields are filled
- Verify invoice details are correct
- Make sure supplier is selected

### Issue 2: Approval Issues

**Cause**: Approvers not assigned correctly or missing permissions.

**Solution**:

- Check if approvers are assigned correctly
- Verify approver permissions
- Ensure approvers are active users
- Check if approvers have the correct roles