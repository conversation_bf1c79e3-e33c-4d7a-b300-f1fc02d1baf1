import React, { forwardRef, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import { FieldWrapper } from './FieldWrapper';
import { cn } from '@src/utils/cn';
import { getRationalPattern } from '@src/utils/inputSanitizer';
import { useNumberFormatter } from '@src/hooks/useNumberFormatter';
import { useViewMode } from '@src/hooks/useViewMode';
/**
 * NumericalInput
 *
 * A highly‑configurable numeric input field with:
 *  - precision control (decimal places & maximum length)
 *  - optional “super strict” text‑only mode
 *  - built‑in prevention of scientific notation (e/E), plus optional
 *    blocking of negatives or decimals
 *  - support for a leading icon (BeforeIcon) or a trailing button icon
 *  - optional formatting on blur
 *  - integrates with React Hook Form or any other registration/onChange pattern
 *
 * @component
 * @param {object}   props
 * @param {number}   [props.decimalPlaces=2]  Maximum digits after the decimal point.
 * @param {number}   [props.maxLimit=10]      Total maximum digits before + after the decimal.
 * @param {boolean}  [props.enforceFormat=true] If true, when out of focus, value will be formatted.
 * @param {boolean}  [props.onlyInteger=false]  If true, disallows “.” (decimal) key.
 * @param {boolean}  [props.noNegative=true]   If true, disallows “-” (negative) key.
 * @param {boolean}  [props.superStrict=false] If true, renders as text (useful if you want
 *                                              full regex control / copy/paste behavior).
 * @param {string | number}   props.value               Controlled input value.
 * @param {function} props.onChange            Change handler; only called if new value
 *                                              matches the defined pattern.
 * @param {object}   [props.registration={}]   Spread your React‑Hook‑Form `register(...)` or 
 *                                              any other object with `name`, `ref`, etc.
 * @param {object}   [props.inputProps={}]     Any extra `<input>` props (e.g. `min`, `max`,
 *                                              `step`, `disabled`).
 * @param {string}   [props.fallbackValue='']  Fallback value if `value` is null/undefined.
 * @param {string | React.FC}   [props.label='']          Field label text or component  (renders above the input).
 * @param {string}   [props.placeholder='']    Placeholder text.
 * @param {object}   [props.error]             Error object with `.message` (renders below).
 * @param {number}   [props.columnSpan=1]      Tailwind CSS grid‑col span for `<FieldWrapper>`.
 * @param {boolean}  [props.hasIcon=false]     If true, will render a trailing icon button.
 * @param {React.FC} [props.renderIcon=null]   Component to render inside the trailing button.
 * @param {React.FC} [props.beforeIcon=null]   Component to render inside the input (left).
 * @param {function} [props.iconHandler=() => {}] Click handler for the trailing icon button.
 * @param {string}   [props.iconClassName='']  Extra classes for the trailing icon button.
 * @param {string}   [props.className='']      Extra classes to append to the `<input>`.
 * @param {React.Ref} ref                     Forwarded ref for the `<input />` element.
 *
 * @example
 * // Controlled usage:
 * const [price, setPrice] = useState("0.00");
 * <NumericalInput
 *   decimalPlaces={2}
 *   maxLimit={6}
 *   onlyInteger={false}
 *   noNegative={false}
 *   label="Price"
 *   placeholder="Enter price"
 *   value={price}
 *   onChange={e => setPrice(e.target.value)}
 *   beforeIcon={DollarSignIcon}
 * />
 *
 * @example
 * // With React Hook Form:
 * <Controller
 *   name="quantity"
 *   control={form.control}
 *   render={({ field, fieldState }) => (
 *     <NumericalInput
 *       {...field}
 *       error={fieldState.error}
 *       onlyInteger
 *       label="Quantity"
 *     />
 *   )}
 * />
 */

const NumericalInput = forwardRef(({
  decimalPlaces = 2,
  maxLimit = 10,
  enforceFormat = true,
  onlyInteger = false,
  noNegative = true,
  superStrict = false,
  value,
  onChange,
  registration = {},
  inputProps = {},
  fallbackValue = '',
  label = '',
  placeholder = '',
  error,
  columnSpan = 1,
  hasIcon = false,
  renderIcon: RenderIcon = null,
  beforeIcon: BeforeIcon = null,
  iconHandler = () => {},
  iconClassName = '',
  className = '',
}, ref) => {
  const { disabled } = inputProps;
  const { isViewMode, onFocus, onBlur } = useViewMode(enforceFormat, disabled);
  const format = useNumberFormatter();

  const displayValue = useMemo(
    () => format(isViewMode, value, decimalPlaces, fallbackValue),
    [format, isViewMode, value, decimalPlaces, fallbackValue]
  );
  // pattern for validating onChange
  const pattern = useMemo(
    () => getRationalPattern(decimalPlaces, maxLimit),
    [decimalPlaces, maxLimit]
  );

  // derive step if not provided
  const step = useMemo(() => {
    if (onlyInteger) return '1';
    if (inputProps.step) return inputProps.step;
    // e.g. decimalPlaces=3 → "0.001"
    return `0.${'0'.repeat(decimalPlaces - 1)}1`;
  }, [onlyInteger, decimalPlaces, inputProps.step]);

  const handleFocus = useCallback(e => {
    onFocus();
    if (inputProps?.handleFocus) inputProps?.handleFocus(e);
  })

  const handleBlur = useCallback(e => {
    onBlur();
    if (inputProps?.handleBlur) inputProps?.handleBlur(e);
  })

  const handleChange = useCallback(e => {
    const next = e.target.value;
    if (next === '' || pattern.test(next)) onChange(e);
  }, [onChange, pattern]);

  // block unwanted keys
  const handleKeyDown = useCallback(e => {
    const forbidden = new Set(['e', 'E', '+']);
    if (noNegative && e.key === '-') forbidden.add('-');
    if (!noNegative && e.target.value.length == 0 && e.key === '-') e.target.value = "";
    if (!noNegative && e.target.value.length > 0 && e.key === '-') forbidden.add('-');
    if (onlyInteger && e.key === '.') forbidden.add('.');

    if (forbidden.has(e.key)) {
      e.preventDefault();
    }
  }, [noNegative, onlyInteger]);

  return (
    <FieldWrapper
      label={label}
      error={error}
      className={cn(`col-span-${columnSpan}`, className)}
    >
      <div className="relative">
        <input
          {...registration}
          {...inputProps}
          type={superStrict ? 'text' : 'number'}
          step={step}
          value={enforceFormat ? displayValue : (value ?? fallbackValue)}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          ref={ref}
          className={cn(
            `flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm
             shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1
             focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50
             placeholder:font-normal placeholder:text-sm`,
            BeforeIcon && 'pl-10',
            className
          )}
        />

        {BeforeIcon && (
          <BeforeIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 text-gray-700" />
        )}

        {hasIcon && RenderIcon && (
          <button
            type="button"
            onClick={iconHandler}
            className={cn('absolute inset-y-0 right-0 pr-3 flex items-center', iconClassName)}
          >
            <RenderIcon />
          </button>
        )}
      </div>
    </FieldWrapper>
  );
});

NumericalInput.displayName = 'NumericalInput';
NumericalInput.propTypes = {
  decimalPlaces: PropTypes.number,
  maxLimit: PropTypes.number,
  enforceFormat: PropTypes.bool,
  onlyInteger: PropTypes.bool,
  noNegative: PropTypes.bool,
  superStrict: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func.isRequired,
  registration: PropTypes.object,
  inputProps: PropTypes.object,
  fallbackValue: PropTypes.string,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  placeholder: PropTypes.string,
  error: PropTypes.object,
  columnSpan: PropTypes.number,
  hasIcon: PropTypes.bool,
  renderIcon: PropTypes.func,
  beforeIcon: PropTypes.func,
  iconHandler: PropTypes.func,
  iconClassName: PropTypes.string,
  className: PropTypes.string,
};

export { NumericalInput };