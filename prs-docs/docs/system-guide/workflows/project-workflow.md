# Project Management Workflow

This document outlines the complete workflow for managing projects in the PRS system, including synchronization from external sources, project details management, approver assignment for requisition workflows, and trade management.

## Workflow Diagram

```mermaid
flowchart TD
    Start([Project Management]) --> A[Projects Landing Page]
    
    A --> B[View Projects]
    A --> C{User Role}
    
    C -->|IT Admin/Purchasing Admin| D[Sync Projects]
    C -->|IT Admin/Purchasing Admin| F[Manage Trade Assignment]
    C -->|IT Admin/Purchasing Admin| G[Manage Approvers]
    
    D --> H[Pull from Project Master File]
    H --> I[Update Project List]
    
    B --> J[View Project Details]
    J --> K[Project Information View]
    
    K --> L[Project Details Section]
    K --> M[Trade Management Section]
    K --> N[Project Approvers Section]
    
    F --> Q[Major Trades Management]
    F --> R[Sub Trades Management]
    Q --> S[Assign Engineers to Major Trades]
    R --> T[Assign Engineers to Sub Trades]
    
    G --> U[Manage Regular Approvers]
    U --> V[Manage Approval Levels]
    V --> W[Assign Level Approvers]
    V --> X[Assign Optional Approvers]
    
    W --> Y[Add/Edit/Delete Level]
    X --> Z[Add/Edit Optional]
    
    Y --> AA[Save Project Changes]
    Z --> AA
    S --> AA
    T --> AA
```

## Entity Types

### Project Records
- **Source**: Synced from Cityland's Project Master File
- **Management**: Full approver and trade management
- **Structure**: Project details + trade assignments + approver hierarchy

### Trade Categories
- **Major Trades**: Civil & Architecture, Mechanical, Electrical, Plumbing & Sanitary, Fire & Protection
- **Sub Trades**: Bored Piles, Substructure Works
- **Assignment**: Engineers assigned to specific trades per project

## User Roles and Permissions

| Role | View Projects | Sync Projects | Assign Approvers | Update Approvers | Manage Trades |
|------|---------------|---------------|------------------|------------------|---------------|
| All Other Users | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ |
| IT Admin | ✓ | ✓ | ✓ | ✓ | ✓ |
| Purchasing Admin | ✓ | ✓ | ✓ | ✓ | ✓ |

## Detailed Workflow Steps

### 1. Projects Landing Page Access

**Actor**: IT Admin, Purchasing Admin

**Actions**:
- Access Projects menu from navigation
- View list of all projects

**Features**:
- Search functionality by project name
- Sortable columns
- Pagination (10 rows per page)
- Default sorting: Alphabetical by Project Name (A-Z)

### 2. Project Data Synchronization

**Actor**: IT Admin, Purchasing Admin

**Prerequisites**: 

- Cityland Legacy System integration setup

**Actions**:

- Click "Sync Project List" button
- System pulls data from Cityland's Project Master File
- Updates existing projects or adds new ones

**Synchronized Data (Evolution)**:

- Project Name
- Project Code
- Project Initials
- Project Address
- Company Code
- Company Name

**Business Rules**:
- Adds projects not yet in the system
- Updates existing project details from master file
- Updates filter and form options across the system
- Display last sync date and time
- Button is disabled during sync process
- Returns to page 1 after synchronization
- Synced data fields are read-only

### 3. View Project Details

**Actor**: All Users (initial), IT Admin (final)

**Actions**:

- Click on project name or view button
- Display full page view

**Information Displayed**:

- Project Details Section
- Trade Management Section
- Project Approvers Section (accordion-style)
- Edit button (for authorized users)

### 4. Project Details Management

**Actor**: Purchasing Admin and IT Admin

**Actions**:

- View all project details as read-only
- No editing capability for project details
- Focus on approver and trade management

### 5. Trade Management

#### 5.1 View Trade Assignments

**Actor**: IT Admin and Purchasing Admin

**Actions**:

- Click project name to view project details
- Navigate to Trade Management section

**Trade Categories**:

*Major Trades*:
- Civil and Architecture Works
- Mechanical Works
- Electrical Works
- Plumbing and Sanitary
- Fire and Protection Works

*Sub Trades*:
- Bored Piles Work
- Substructure Works

**Features**:
- Two separate tabs (Major and Sub)
- View users assigned per trade
- Search functionality for users per trade

#### 5.2 Assign Engineers to Trades

**Actor**: IT Admin and Purchasing Admin

**Actions**:

1. Click project name
2. Click Edit button for Trade section
3. Enable editing for Trade Section only
4. Navigate between Major and Sub trade tabs

**Add Engineers Process**:
1. Click "Add User" button for specific trade
2. Select trade from modal
3. Use search field to find engineers
4. Select from filtered user list:
   - Must have Engineers user type
   - Not yet added to selected trade
5. Select one or more users
6. Click "Add Users" to assign to trade

**Save Process**:
- Cancel Button: Display confirmation modal, cancel adding users
- Save Button: Display confirmation modal, add users to trade
- Allow adding users to multiple trades

#### 5.3 Update Trade Assignments

**Actor**: IT Admin

**Actions**:

1. Access Trade Management edit mode
2. View current assignments per trade
3. Remove users by clicking X icon per user
4. Add new users using "Add User" button

**Update Process**:
- Remove existing assignments
- Add new assignments
- Maintain same validation rules as initial assignment
- Users can be assigned to multiple trades

### 6. Project Approver Management

#### 6.1 Assign Regular Approvers

**Actor**: IT Admin and Purchasing Admin

**Actions**:

- Access via Edit button in project approvers section
- Manage multiple approval levels
- Add approvers to each level

**Level Management**:
- Click "Add Level" to create new approval level
- New levels always added as the last level
- Each level can have multiple approvers
- Levels can be deleted if empty

**Add Approver Process**:
1. Click "Add Approver" button for specific level
2. Select from modal with filtered user list
3. Available user type: Engineers only
4. Confirm selection to add to approver list

**Business Rules**:
- Same user cannot be approver on different levels within same workflow
- All levels must have at least one approver
- Empty levels can be deleted before submission
- Changes are not saved until confirmation

#### 6.2 Assign Optional Approvers

**Actor**: IT Admin and Purchasing Admin

**Actions**:

- Manage optional approvers (single level only)
- Add/edit optional approver in separate section

**Optional Approver Rules**:

- Only one level available for optional approvers
- User types allowed: All except Root User
- Cannot be same user as regular approvers
- Optional - not required for workflow completion

#### 6.3 Update Project Approvers

**Actor**: IT Admin and Purchasing Admin

**Prerequisites**:

- Approvers already assigned to project
- User has update permissions

**Actions**:
- Click Edit icon for specific level
- Select new approver from filtered list
- Confirm changes

**Update Process**:
- Edit existing approver assignments
- Add new levels if needed
- Remove approvers or entire levels
- Maintain workflow integrity

### 7. Save and Validation

**Actor**: IT Admin and Purchasing Admin

**Actions**:

- Review all approver assignments
- Submit changes via Save button
- Handle validation errors

**Save Process**:
1. Click Save button after making changes
2. System validation checks:
   - All levels have assigned approvers
   - No duplicate approvers across levels
   - Proper user types assigned
3. Display confirmation modal
4. Final save or cancel option

**Validation Rules**:
- Error message: "Approver is Required" for empty levels
- Cannot save with empty approval levels
- Cannot have same approver on multiple levels
- Engineers required for regular approvers
- Specified user types for optional approvers

**Success Actions**:
- Display success toast message
- Automatically close modal after 5 seconds
- Return to project view with updated assignments

## Business Rules Summary

### Project Synchronization Rules
- Projects synced from external master file
- Incremental data enhancement (basic → full details)
- Synced data is read-only except for specific editable fields
- Sync updates existing or adds new projects

### Project Details Editing Rules
- Initial version: Purchasing Staff can edit Company and dates
- Final version: All project details read-only for IT Admin
- Date validation: Start date > current date, End date > start date
- Company selection from synced Company Master List

### Trade Assignment Rules
- Engineers user type required for trade assignments
- Users can be assigned to multiple trades
- Separate management for Major and Sub trades
- Search and filter functionality for easy user selection

### Approver Assignment Rules
- Regular approvers: Engineers user type only
- Optional approvers: Multiple user types allowed
- No duplicate approvers across levels within same workflow
- All levels must have at least one approver before saving
- Empty levels can be deleted during editing

### User Access Evolution
- Initial: All users can view, Purchasing Staff can edit
- Final: IT Admin exclusive access with enhanced features
- Purchasing Admin has same access as IT Admin
- Edit functionality removed for project details in final version

## Example Scenarios

### Scenario 1: Sync Project Data

1. IT Admin accesses Projects menu
2. Clicks "Sync Project List" button
3. System connects to master file and updates projects
4. Sync completion message displays with timestamp
5. Updated project list refreshes to page 1
6. Button re-enabled after sync completion

### Scenario 2: Assign Engineers to Trades

1. IT Admin views project details
2. Clicks Edit button for Trade section
3. Navigates to Major trades tab
4. Clicks "Add User" for Civil and Architecture Works
5. Searches for and selects multiple Engineers
6. Clicks "Add Users" to assign to trade
7. Repeats for other trades as needed
8. Clicks Save and confirms changes
9. Returns to project view with updated trade assignments

### Scenario 3: Setup Project Approvers

1. IT Admin views project details
2. Navigates to Project Approvers section
3. Clicks Edit button for approver management
4. Clicks "Add Level" to create Level 1
5. Adds Engineers to Level 1
6. Clicks "Add Level" to create Level 2
7. Adds different Engineers to Level 2
8. Adds Supervisor as optional approver
9. Clicks Save and confirms changes
10. Success toast displays and returns to project view

### Scenario 4: Update Trade Assignments

1. IT Admin accesses project with existing trade assignments
2. Clicks Edit button for Trade section
3. Removes some engineers by clicking X icon
4. Adds new engineers using "Add User" button
5. Ensures no conflicts with business rules
6. Saves changes with confirmation
7. Updated assignments display in trade view

### Scenario 5: Handle Validation Errors

1. IT Admin edits project approvers
2. Creates new level but doesn't assign approver
3. Attempts to save configuration
4. System displays "Approver is Required" error
5. Either assigns approver to level or deletes empty level
6. Successfully saves valid configuration
7. Returns to project view with success message

## Data Flow Integration

### Incoming Data
- Project Master File → Project records (name, code, initials, address, company, dates)
- Company Master List → Company dropdown options
- User Management System → Engineer and approver selection lists
- Organization hierarchy → User type classifications

### Outgoing Data
- Project data → Requisition workflow assignment
- Trade assignments → Engineer work allocation
- Approver configurations → Requisition approval routing
- Project filters → Various system forms and reports

## Common Issues and Solutions

### Issue 1: Sync Button Not Responding

**Cause**: Network connectivity or master file access issues

**Solution**:
- Check network connection
- Verify master file system availability
- Wait for current sync to complete if in progress
- Contact system administrator if issue persists

### Issue 2: Cannot Save Project Configuration

**Cause**: Validation errors or missing approvers

**Solution**:
- Review error messages for specific issues
- Ensure all levels have assigned approvers
- Check for duplicate approvers across levels
- Verify correct user types for different approver categories

### Issue 3: Engineer Not Available for Trade Assignment

**Cause**: User doesn't have Engineers user type or already assigned

**Solution**:
- Verify user has Engineers user type
- Check if user is already assigned to same trade
- Confirm user account is active in User Management
- Contact IT Admin to update user permissions if needed

### Issue 4: Cannot Delete Approval Level

**Cause**: Level has assigned approvers or validation rules

**Solution**:
- Remove all approvers from level before deletion
- Ensure at least one level remains in configuration
- Cannot delete if it would violate minimum level requirements
- Use Edit function to reassign approvers instead

### Issue 5: Trade Assignment Conflicts

**Cause**: Attempting to assign non-Engineer users or duplicate assignments

**Solution**:
- Verify all assigned users have Engineers user type
- Remove existing assignment before reassigning to different trade
- Use search function to locate correct engineer profiles
- Check User Management for accurate user type classifications
