<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{border-right:none;border-bottom:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#ff0000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#000000;font-family:Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f9cb9c;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="2013526587C0" style="width:103px;" class="column-headers-background">A</th><th id="2013526587C1" style="width:167px;" class="column-headers-background">B</th><th id="2013526587C2" style="width:252px;" class="column-headers-background">C</th><th id="2013526587C3" style="width:84px;" class="column-headers-background">D</th><th id="2013526587C4" style="width:233px;" class="column-headers-background">E</th><th id="2013526587C5" style="width:330px;" class="column-headers-background">F</th><th id="2013526587C6" style="width:69px;" class="column-headers-background">G</th><th id="2013526587C7" style="width:511px;" class="column-headers-background">H</th><th id="2013526587C8" style="width:128px;" class="column-headers-background">I</th><th id="2013526587C9" style="width:114px;" class="column-headers-background">J</th><th id="2013526587C10" style="width:126px;" class="column-headers-background">K</th><th id="2013526587C11" style="width:126px;" class="column-headers-background">L</th><th id="2013526587C12" style="width:126px;" class="column-headers-background">M</th><th id="2013526587C13" style="width:126px;" class="column-headers-background">N</th><th id="2013526587C14" style="width:117px;" class="column-headers-background">O</th><th id="2013526587C15" style="width:152px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="2013526587R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results<br>(STG_wk4&amp;5)</td><td class="s1">Status<br>(STG_wk4&amp;5)</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="2013526587R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="16">PRS-031- [NON-RS PAYMENT REQUEST] - Non-RS Dashboard, Mapping of Approvers for Non-RS Payment, Non-RS Payment Creation, Non-RS Payment Approval, Non-RS Payment Viewing, Editing during Non-RS Approval,  Non-RS Payment Rejecting, Resubmitting of rejected Non-RS</td></tr><tr style="height: 19px"><th id="2013526587R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify Navigation to Non-RS Dashboard</td><td class="s4">Critical</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Login<br>2. Click Request Flow<br>3. Click Non-RS</td><td class="s5"></td><td class="s4">1. Able to view Non-RS in Request Flow<br>2. User is successfully navigated to the Non-RS Payment Dashboard.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="2013526587R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify Display of Non-RS Payment Dashboard</td><td class="s4">Critical</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Login<br>2. Click Request Flow<br>3. Click Non-RS</td><td class="s5"></td><td class="s4">1. Able to display Non-RS Dashboard<br>2. Able to Display All, For My Approval/Assigned and My Requests Tab<br>3. Able to display New Non-RS Request button<br>4. Able to display download button<br>5. Able to display Search bar with Search button and Clear button<br>6. Able to display Table for Non-RS Payment with Columns<br>    a. Non-RS Number      <br>    b. Charge To<br>    c. Requested by   <br>    d. Last Updated  <br>    e. Amount <br>    f. Status</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="2013526587R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that entering a keyword in the search field and clicking the Search button filters the results accordingly.</td><td class="s4">High</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Login<br>2. Click Request Flow<br>3. Click Non-RS<br>4. Click Search bar<br>5. Input a keyword<br>6. Click Search button</td><td class="s5"></td><td class="s4">1. Only matching records are displayed in the table.<br>2. Able to Use Search bar and Search button</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="2013526587R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that clicking the Search button without any input does not perform any action.</td><td class="s4">High</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Login<br>2. Click Request Flow<br>3. Click Non-RS<br>4. Click Search bar<br>5. Click Search button</td><td class="s5"></td><td class="s4">1. No changes occur in the table display.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="2013526587R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that if no matching records are found, &quot;No Data&quot; is displayed.</td><td class="s4">High</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Login<br>2. Click Request Flow<br>3. Click Non-RS<br>4. Click Search bar<br>5. Input a keyword that is not from the list<br>6. Click Search button</td><td class="s5"></td><td class="s4">1. &quot;No Data&quot; message is shown when no results match the search keyword.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="2013526587R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that clicking the Clear button resets the search field and removes any applied filters.</td><td class="s4">High</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Search bar<br>4. Input a keyword<br>5. Click Search button<br>6. Click Clear button</td><td class="s5"></td><td class="s4">1. Search field is cleared, and table resets to default state.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="2013526587R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the &quot;All&quot; tab displays all Non-RS Payments.</td><td class="s4">Critical</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click All tab</td><td class="s5"></td><td class="s4">1. The &quot;All&quot; tab lists all Non-RS Payments.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="2013526587R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the &quot;For My Approval&quot; tab is visible only to approvers.</td><td class="s4">Critical</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click For My Approval/Assigned Tab</td><td class="s5"></td><td class="s4">1. The tab is not visible to non-approvers.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="2013526587R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the &quot;For My Approval&quot; tab displays only the payments awaiting the approver’s action.</td><td class="s4">Critical</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click For My Approval/Assigned Tab</td><td class="s5"></td><td class="s4">1. Only items requiring approval are shown.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the &quot;My Requests&quot; tab displays requests created by the currently logged-in user.</td><td class="s4">Critical</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click For My Requests</td><td class="s5"></td><td class="s4">1. The tab only shows requests created by the logged-in user.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 113px"><th id="2013526587R12" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">13</div></th><td class="s9"></td><td class="s9">Non-RS Dashboard</td><td class="s9">Verify that the &quot;Create Non-RS Request&quot; button is available and navigates the user to the request creation form.</td><td class="s12">Critical</td><td class="s9">1. Users must be logged in to their Accounts</td><td class="s9">1. Click Request Flow<br>2. Click Non-RS<br>3. Click New Non-RS Request button</td><td class="s13"></td><td class="s9">1. Clicking the button redirects the user to the request creation page.</td><td class="s14"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the &quot;Download&quot; button exports the Non-RS Payment Table in an Excel file format.</td><td class="s4">High</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Download button</td><td class="s5"></td><td class="s4">1. An Excel file containing the table data is downloaded.</td><td class="s6"></td><td class="s7"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4">missing non-rs</td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1038">CITYLANDPRS-1038</a></td></tr><tr style="height: 19px"><th id="2013526587R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that sorting by &quot;Non-RS Number&quot; works in ascending (0-9) and descending (9-0) order.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Non-RS Number column name for sorting</td><td class="s5"></td><td class="s4">1. Sorting functions correctly.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that sorting by &quot;Charge To&quot; works in ascending (A-Z, 0-9) and descending (Z-A, 9-0) order</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Charge To column name for sorting</td><td class="s5"></td><td class="s4">1. Sorting functions correctly.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that sorting by &quot;Requested By&quot; works in ascending (A-Z) order.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Requested By column name for sorting</td><td class="s5"></td><td class="s4">1. Sorting functions correctly.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that sorting by &quot;Last Updated&quot; works from oldest-to-latest and latest-to-oldest.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click last Updated column name for sorting</td><td class="s5"></td><td class="s4">1. Sorting functions correctly.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that sorting by &quot;Amount&quot; works in ascending (0-9) and descending (9-0) order.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Amount column name for sorting</td><td class="s5"></td><td class="s4">1. Sorting functions correctly.</td><td class="s6"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s4"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that sorting by &quot;Status&quot; works in ascending (A-Z) order.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click Status column name for sorting</td><td class="s5"></td><td class="s4">1. Sorting functions correctly.</td><td class="s4"></td><td class="s7"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s5"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the &quot;Status&quot; column only contains valid statuses: Draft, For Approval, Rejected, Closed.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. View Status column in table</td><td class="s5"></td><td class="s4">1. Only valid statuses are displayed.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s5"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the table displays 10 rows per page.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. View the table</td><td class="s5"></td><td class="s4">1. The table paginates correctly with 10 rows per page.</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1028">CITYLANDPRS-1028</a></td></tr><tr style="height: 19px"><th id="2013526587R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s5"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that pagination works correctly when navigating between pages.</td><td class="s4">High</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on the pages</td><td class="s5"></td><td class="s4">1. Users can navigate through pages without errors.</td><td class="s5"></td><td class="s5"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1028">CITYLANDPRS-1028</a></td></tr><tr style="height: 19px"><th id="2013526587R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s5"></td><td class="s4">Non-RS Dashboard</td><td class="s4">Verify that the table is initially sorted by the latest updated date by default.</td><td class="s4">Minor</td><td class="s4">1. Users must be logged in to their Accounts</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. View the table</td><td class="s5"></td><td class="s4">1. The table defaults to sorting by latest updated date.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 79px"><th id="2013526587R24" style="height: 79px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 79px">25</div></th><td class="s5"></td><td class="s4">Mapping of Approvers for Non-RS Payment</td><td class="s4">Verify the mapping of Approver</td><td class="s4">Critical</td><td class="s4">1. Users must be setup<br>2. Non-RS Payment is Created and for approval</td><td class="s4">1. Click Request Flow<br>2. Click Non-RS<br>3. Click one of the Non-RS Payment on the table</td><td class="s5"></td><td class="s4">1. Level 1 - Purchasing Head is the approver</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s13"></td><td class="s9">Mapping of Approvers for Non-RS Payment</td><td class="s9">Verify only required User Types can approve Non-RS Payments</td><td class="s12">Critical</td><td class="s9">1. Users must be setup<br>2. Non-RS Payment is Created and for approval</td><td class="s9">1. Click Request Flow<br>2. Click Non-RS<br>3. Click one of the Non-RS Payment on the table<br>4. Check for Confirmation message in Purchasing Head account</td><td class="s13"></td><td class="s9">1. Only Purchasing Head should have the confirmation message for approving </td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s13"></td><td class="s9">Mapping of Approvers for Non-RS Payment</td><td class="s18">Verify only required User Types can approve Non-RS Payments - Negative Scenario</td><td class="s12">High</td><td class="s9">1. Users must be setup<br>2. Non-RS Payment is Created and for approval</td><td class="s9">1. Click Request Flow<br>2. Click Non-RS<br>3. Click one of the Non-RS Payment on the table<br>4. Check for Confirmation message in different user types</td><td class="s13"></td><td class="s9">1. Check using other different user types</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s13"></td><td class="s9">Mapping of Approvers for Non-RS Payment</td><td class="s9">Verify Notification for the Approver - approved</td><td class="s12">High</td><td class="s9">1. Users must be setup<br>2. Non-RS Payment is Created and for approval</td><td class="s9">1. Click Request Flow<br>2. Click Non-RS<br>3. Click one of the Non-RS Payment on the table<br>4. Click on the Notification bell</td><td class="s13"></td><td class="s9">1. Check notification for the approval of Non RS Payment</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s19 softmerge"><div class="softmerge-inner" style="width:347px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1006">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1006</a></div></td></tr><tr style="height: 19px"><th id="2013526587R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s13"></td><td class="s9">Mapping of Approvers for Non-RS Payment</td><td class="s9">Verify Notification for the Approver - rejected</td><td class="s12">High</td><td class="s9">1. Users must be setup<br>2. Non-RS Payment is Created and for approval</td><td class="s9">1. Click Request Flow<br>2. Click Non-RS<br>3. Click one of the Non-RS Payment on the table<br>4. Click on the Notification bell</td><td class="s13"></td><td class="s9">1. Check notification for the rejection of Non RS Payment</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s13"></td><td class="s9">Non-RS Payment Creation</td><td class="s9">Verify Non-RS Request Create page</td><td class="s12">High</td><td class="s20"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts</a></td><td class="s9">1. Navigate to Non-RS Dashboard<br>2. Click &quot;New Non-RS Request&quot;<br>3. Validate Non-RS Request Number</td><td class="s13"></td><td class="s9">1. Should open Non-RS Dashoard<br>2. Should display Non-RS Request Create form<br>3. Should display a blank Non-RS Number</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s5"></td><td class="s4">Non-RS Payment Creation</td><td class="s4">Verify Non-RS Request Details section</td><td class="s4">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s4">1. Navigate to Non-RS Create form<br>2. Validate Non-RS Details section<br>3. Validate Charge To (Category) field<br>4. Validate Charge To (Client) field<br>5. Validate Invoice Number field<br>6. Validate Payable To field<br>7. Validate Date Needed field<br>8. Validate Delivery Fee field<br>9. Validate Attachments field<br>10. Validate Notes field</td><td class="s5"></td><td class="s4">1. Should display Non-RS Request Create form<br>2. Should display Non-RS Details section:<br>    a. Charge To (Category)<br>    b. Charge To (Client)<br>    d. Payable To<br>    e. Date Needed<br>    f. Delivery Fee<br>3. Should display a Drop-down with values of Company, Association, Project, and Supplier<br>4. Should display a Dropdown with values of Company, Association, Project, and Supplier depending on the selected Category<br>5. Should meet the following criteria:<br>        i. Text field<br>        ii. Invoice Number Issued to the User<br>        iii. Alphanumeric only<br>        iv. Maximum of 50 Characters<br>6. Should meet the following criteria:<br>        i. Text field<br>        ii. Alphanumeric and Special Characters except for Emojis<br>        iii. Maximum of 100 Characters<br>7. Should meet the following criteria:<br>        i. Date Picker<br>        ii. Allow selecting of Date greater than the Current Date<br>8. Should meet the following criteria:<br>        i. Amount needed by the User<br>        ii. Numbers only with a Decimal<br>9. Should meet the following criteria:<br>       i. Uploading of Files such as Images (JPG, JPEG, PNG), PDF, Doc, CSV, and Excel<br>       ii. Maximum of 25MB per File<br>       iii. Note should display &quot;The maximum size for each file is 25 MB. File formats - PNG, JPG, <br>10. Should meet the following criteria:<br>       i. Text area<br>       ii. Alphanumeric and Special Characters except for Emojis<br>       iii. Maximum of 100 Characters</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s5"></td><td class="s4">Non-RS Payment Creation</td><td class="s4">Verify Non-RS Request Item/s section</td><td class="s4">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s4">1. Navigate to Non-RS Create form<br>2. Validate Non-RS Item/s section<br>3. Validate Search field<br>4. Validate Item field<br>5. Validate Quantity field<br>6. Validate Amount field<br>7. Validate Discount field<br>8. Validate Add Item button</td><td class="s5"></td><td class="s4">1. Should display Non-RS Request Create form<br>2. Should display Non-RS Item/s section:<br>    a. Search field<br>    b. Item field<br>    c. Quantity field<br>    d. Amount field<br>    e. Discount field<br>    f. Add Item button<br>   g. Item Table List<br>3. Should meet the following criteria:<br>       i. Allow searching for added Item Name in the Items Table<br>       ii. Allow searching for a Keyword<br>       iii. Triggered by clicking Search Button<br>       iv. Should clear the Search Field and Filtering of Table by clicking Clear Button<br>4. Should meet the following criteria:<br>       i. Should be Alphanumeric and Special Character except Emojis<br>       ii. Maximum of 100 Characters<br>5. Should meet the following criteria:<br>       i. Numbers Only<br>       ii. Maximum of 5 Characters<br>6. Should meet the following criteria:<br>       i. Numbers only with a Decimal<br>       ii. Maximum of 10 Characters<br>7. Should meet the following criteria:<br>       i. Can only choose between fixed or percentage button<br>       ii. Text field beside button<br>8. Should have a button for Add Item</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s5"></td><td class="s4">Non-RS Payment Creation</td><td class="s4">Verify Non-RS Request Item Table List</td><td class="s4">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s4">1. Navigate to Non-RS Create form<br>2. Validate Non-RS Item Table List<br>3. Validate Item field<br>4. Validate Quantity field<br>5. Validate Amount field<br>6. Validate Discount field<br>7. Validate Discounted Price field</td><td class="s5"></td><td class="s4">1. Should display Non-RS Request Create form<br>2. Should display Non-RS Item/s section:<br>    a. Item<br>    b. Quantity<br>    c. Amount<br>    d. Discount<br>    e. Discounted Price<br>3. Should meet the following criteria:<br>       i. Should display Item name<br>       ii. Should sort the &quot;Items&quot; to - A-Z, Z-A when sorting is clicked<br>4. Should meet the following criteria:<br>       i. Should display the correct quantity of the item<br>       ii. Should sort in ascending or descending order based on Quantity<br>5. Should meet the following criteria:<br>       i. Should display the correct price of the item<br>       ii. Should sort in ascending or descending order based on Amount<br>6. Should meet the following criteria:<br>       i. Should display the correct discount of the item<br>       ii. Should sort in ascending or descending order based on Discount<br>7. Should meet the following criteria:<br>       i. Should display the correct discounted price of the item computed depending on the Amount and the entered Discount</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s5"></td><td class="s4">Non-RS Payment Creation</td><td class="s4">Verify Non-RS Request Item Table List Discount field</td><td class="s4">High</td><td class="s11"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s4">1. Navigate to Non-RS Create form<br>2. Click Edit button on Discount field in Item Table List<br>3. Validate Item Discount modal<br>4.Click &quot;Fixed Amount&quot; button<br>5. Enter discount amount<br>6. Click &quot;Apply&quot; button<br>7. Click &quot;Percentage&quot; button<br>8. Enter discount percentage<br>9. Click &quot;Apply&quot; button</td><td class="s5"></td><td class="s4">1. Should display Non-RS Request Create form<br>2. Item Discount modal should display &quot;Please enter the discount for the item selected. You can choose between fixed amount or percentage. Discounted amount will be updated.&quot;<br>3. The modal should have Fixed Amount and Percentage buttons<br>4. &quot;Fixed Amount&quot; button should be highlighted as active<br>5. Amount field should accept numeric values with decimals and display the ₱ peso sign before the number<br>6. The modal should close and the Discounted Price field in the item table should be updated based on the entered amount<br>7. &quot;Percentage&quot; button should be highlighted as active<br>8. Amount field should accept numeric values with a % sign<br>9. The modal should close and the Discounted Price field should update based on the entered discount</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s13"></td><td class="s9">Non-RS Payment Creation</td><td class="s9">Verify Non-RS Request Amount Summary section</td><td class="s12">High</td><td class="s20"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s9">1. Navigate to Non-RS Create form<br>2. Validate Amount Summary section<br>3. Validate Amount (+ Other Charges) field<br>4. Validate Discount field<br>5. Validate Total Amount</td><td class="s13"></td><td class="s9">1. Should display Non-RS Request Create form<br>2. Should display Non-RS Details section:<br>    a. Amount (+ Other Charges)<br>    b. Discount<br>    c. Total Amount<br>3. Amount should display the total amount before discounts with a peso sign (₱) and formatted with two decimal places<br>4. Discount should display the total discount applied with a peso sign (₱) and formatted with two decimal places<br>5. Total amount should display the final price after distracting discounts with a peso sign (₱) and formatted with two decimal places</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 19px"><th id="2013526587R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s13"></td><td class="s9">Non-RS Payment Creation</td><td class="s9">Verify Saving Non-RS Payment as Draft</td><td class="s12">High</td><td class="s20"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s9">1. Navigate to Non-RS Create form<br>2. Populate all fields<br>3. Click &quot;Save Draft&quot; button<br>4. Click &quot;Save&quot; in the modal<br>5. Click &quot;Cancel&quot; in the modal</td><td class="s13"></td><td class="s14">1. Form should display without errors<br>2. All fields should be populated<br>3. Confirmation modal should display &quot;You are about to save this request as a draft. Make sure all items are correct. Press save if you want to proceed with this action.&quot;<br>4. Once Confirmed, should save the Non-RS Payment as Draft<br>    a. Should have a Status of Draft<br>    b. Should have a Temporary Non-RS Number<br>         Format: NRS-TMP-[Company Code(2Digit)] + AA-ZZ + ********-********<br>         Sample: NRS-TMP-12AA00000002<br>5. Should close the modal and return to Non-RS Create form</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s13"></td><td class="s9">Non-RS Payment Creation</td><td class="s9">Verify Canceling Non-RS Payment Creation</td><td class="s12">High</td><td class="s20"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s9">1. Navigate to Non-RS Create form<br>2. Populate all fields<br>3. Click &quot;Cancel&quot; button<br>4. Click &quot;Continue&quot; in the modal<br>5. Click &quot;Cancel&quot; in the modal</td><td class="s13"></td><td class="s14">1. Form should display without errors<br>2. All fields should be populated<br>3. Confirmation modal should display &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;<br>4. Should not be able to create Non-RS Request<br>5. Should close the modal and return to Non-RS Create form</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s13"></td><td class="s9">Non-RS Payment Creation</td><td class="s9">Verify Submitting Non-RS Payment Request</td><td class="s12">High</td><td class="s20"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard</a></td><td class="s9">1. Navigate to Non-RS Create form<br>2. Populate all fields<br>3. Click &quot;Submit&quot; button<br>4. Click &quot;Submit&quot; in the modal<br>5. Click &quot;Cancel&quot; in the modal</td><td class="s13"></td><td class="s14">1. Form should display without errors<br>2. All fields should be populated<br>3. Confirmation modal should display &quot;You are about to submit this record. Make sure all items are correct. Press sumit if you want to proceed with this action.&quot;<br>4. Once Confirmed, should create the Non-RS Payment Request<br>    a. Should have a Status of For Approval[USER TYPE OF THE APPROVER]<br>    b. Should have a Non-RS Number<br>         Format: NRS-[Company Code(2Digit)] + AA-ZZ + ********-********<br>         Sample: NRS-12AA00000002<br>5. Should close the modal and return to Non-RS Create form</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s13"></td><td class="s9">Non-RS Payment Creation</td><td class="s9">Verify if Invoice Number field accepts multiple invoice numbers</td><td class="s9">Medium</td><td class="s20"><a target="_blank" href="http://*************/login">1. Application link already available.<br>- DEVELOP<br>- STAGING<br>2. User should have created User account<br>3. User already logged in successfully to their Accounts<br>4. User should be in Non-RS Dashboard &gt; Non-RS Create form</a></td><td class="s9">1. . Navigate to &quot;Invoice No.&quot; field<br>2. Enter Multiple Invoice Numbers seperated by comma<br>3. Click Save Draft button<br>4. Enter Multiple Invoice Numbers seperated by spaces<br>5. Enter Multiple Invoice Numbers with an Invalid Separator (e.g &quot;/&quot;)</td><td class="s13"></td><td class="s9">1. Placeholder text should display: &quot;One or more invoice no. Seperate by comma.&quot;<br>2. The system should accept the input and processes multiple invoice numbers correctly<br>3. The form should save successfully<br>4. An error message should appear instructing the user on the correct format<br>5. An error message should appear  instructing the user on the correct format</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s5"></td><td class="s22">Non-RS Payment Viewing</td><td class="s22">Verify Navigation to Non-RS Payments Dashboard</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created</td><td class="s22">1. Click Request Flow<br>2. Click Non-RS</td><td class="s24"></td><td class="s22">1. The Non-RS Payments Dashboard should be displayed.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 19px"><th id="2013526587R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s5"></td><td class="s22">Non-RS Payment Viewing</td><td class="s22">Verify Non-RS Payment Request Details</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created</td><td class="s22">1. Click Request Flow<br>2. Click Non-RS</td><td class="s24"></td><td class="s22">1. Should display a Non-RS Number.<br>2. Should have buttons for Request History, Non-RS Details, Status and Approvers, Attachments and Notes, Items, and Amount.</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5">CITYLANDPRS-1023</td></tr><tr style="height: 19px"><th id="2013526587R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Viewing a Non-RS Payment Request</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.</td><td class="s27"></td><td class="s25">1. The details of the selected Non-RS Payment Request should be displayed.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s5"></td><td class="s22">Non-RS Payment Viewing</td><td class="s22">Verify Request History Section</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created</td><td class="s22">1. Click on the Request History button.</td><td class="s24"></td><td class="s22">1. Should display a table containing:<br> - Non-RS Number<br> - Last Updated<br> - Last Approver<br> - Status</td><td class="s5"></td><td class="s5"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4">no history button</td><td class="s5">CITYLANDPRS-1023</td></tr><tr style="height: 19px"><th id="2013526587R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Non-RS Details Section</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.</td><td class="s27"></td><td class="s25">1. Should display fields for:<br> - Charge To<br> - Invoice Number<br> - Payable To<br> - Date Needed<br> - Delivery Fee</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Status and Approvers Section</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.</td><td class="s27"></td><td class="s25">1. Should display the current status of the Non-RS Payment.<br>2. Should list all approvers with pending status if no approval has been made.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Check Attachments Button</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Click on the Check Attachments button.<br>3. Click and Input in Search Bar<br>4. Click Search button<br>5. Click Clear button<br>6. Click Close Window</td><td class="s27"></td><td class="s25">1. Should open a modal displaying all uploaded files.<br>2. Should allow searching for a file name.<br>3. Should allow viewing the file by clicking the file name, which opens in a new tab.<br>4. Should allow removing an attachment with a confirmation modal.<br>5. Should be able to Input and Search in the Attachments modal<br>6. Should be able to clear search bar and searched attachments<br>7. Should be able to close modal</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13">CITYLANDPRS-1002</td></tr><tr style="height: 19px"><th id="2013526587R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Check Notes Button</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Click on the Notes button.<br>3. Click on Select Date<br>4. Select a date<br>5. Click on Select Month/Date<br>6. Select a date<br>7. Click Close Window</td><td class="s27"></td><td class="s25">1. Should display all notes and reasons added during request approval.<br>2. Should be able to Select a date<br>3. Should be able to Close modal</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13">CITYLANDPRS-1004</td></tr><tr style="height: 19px"><th id="2013526587R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Uploading Attachments</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Click Select Attachments</td><td class="s27"></td><td class="s25">1. Maximum file size should be 25MB.<br>2. Allowed file types: PDF, DOC, JPG, JPEG, PNG, CSV, Excel.<br>3. Click Submit to confirm file upload.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s28">Verify Uploading Attachments - Negative Scenario</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Click Select Attachments that is more than 25 MB<br>3. Click Select Attachments that is other than the allowed file types</td><td class="s27"></td><td class="s25">1. Should not be able to upload 25MB<br>2. Should not be able to upload different file type</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Adding Notes</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Add a note with alphanumeric and special characters (except emojis).<br>3. Click Submit.</td><td class="s27"></td><td class="s25">1. Should allow a maximum of 100 characters.<br>2. Note should be saved and displayed.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s28">Verify Adding Notes - Negative Scenario</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Add a note with emojis<br>3. Add a note that is more than 100 characters</td><td class="s27"></td><td class="s25">1. Should not be able to add emojis<br>2. Should not be able to input more than 100 characters</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Search for an Item</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Enter a keyword in the search field.<br>2. Click the Search button.</td><td class="s27"></td><td class="s25">1. Should filter items based on the keyword.</td><td class="s13"></td><td class="s13"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9">blocked due. to pagination issues</td><td class="s29"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1028">CITYLANDPRS-1028</a></td></tr><tr style="height: 19px"><th id="2013526587R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Clear Search</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click the Clear button.</td><td class="s27"></td><td class="s25">1. Should reset the search field and display all items.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Items Table Columns</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on a Non-RS Payment Number.<br>4. Check for the Item Table</td><td class="s27"></td><td class="s25">1. Should display columns: Item, Quantity, Amount, Discount, Discounted Price.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 19px"><th id="2013526587R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Amount Section</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on a Non-RS Payment Number.<br>4. Check on the bottom part for the Amount Section</td><td class="s27"></td><td class="s25">1. Should display Sub-total, Discounts, and Total Amount.</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9">incorrect discount</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 19px"><th id="2013526587R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Cancel Request Button</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Click the Cancel Request button.<br>2. Confirm the cancellation in the modal.</td><td class="s27"></td><td class="s25">1. Should be allowed only if no approver has approved the request.<br>2. Once confirmed, the request should be marked as &quot;Cancelled.&quot;</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9">no cancel button</td><td class="s29"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1046">CITYLANDPRS-1046</a></td></tr><tr style="height: 19px"><th id="2013526587R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s13"></td><td class="s25">Non-RS Payment Viewing</td><td class="s25">Verify Sticky Confirmation Message for Approvers</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created</td><td class="s25">1. Login using approver account<br>2. Click Request Flow<br>3. Click Non-RS<br>4. Click on a Non-RS Payment Number.<br>5. Check for the confirmation message</td><td class="s27"></td><td class="s25">1. Should display a sticky confirmation message if the viewer is an approver.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R57" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">58</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Verify Navigation to Non-RS Payments Request</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on a Non-RS Payment Number for approval</td><td class="s27"></td><td class="s25">1. The Non-RS Payments Dashboard should be displayed.<br>2. The Non-RS Payment Request should display</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R58" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">59</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Verify Approval and Rejection Confirmation</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on a Non-RS Payment Number for approval<br>4. Check for sticky confirmation messages for Approving or Rejecting the payment.</td><td class="s27"></td><td class="s25">1. Sticky confirmation should appear for both Approve and Reject actions.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R59" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">60</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Verify Adding an Approver</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Locate the Approvers section.<br>2. Click the Add button.<br>3. Verify that a modal appears allowing the addition of an Approver.<br>4. Ensure that all user types are displayed except IT Admin and Root User.<br>5. Verify that users already in the approval list are excluded.<br>6. Add a new Approver.<br>7. Verify that the newly added Approver appears below the current Approver in the Approvers section.<br>8. Edit or remove the newly added Approver using the ellipsis menu.</td><td class="s27"></td><td class="s25">1. The Approver should be added successfully and displayed in the list.<br>2. Edit and Delete options should function correctly.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R60" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">61</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s22">Verify Non-RS Payment Details Section</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Check if fields for Charge To, Invoice Number, Payable To, Date Needed, and Delivery Fee are present.</td><td class="s24"></td><td class="s22">1. All required fields should be available and populated correctly.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R61" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">62</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s22">Verify Attachments and Notes Section</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on the Attachments button.<br>2. Verify that a modal with the uploaded file list appears.<br>3. Search for a file using the filename.<br>4. Click on a file name to open it in a new tab.<br>5. Remove an attachment and confirm the action.<br>6. Click on Notes to view all approval-related comments.<br>7. Upload an attachment ensuring that it meets the file type and size requirements.<br>8. Add a note using alphanumeric and special characters except emojis.</td><td class="s24"></td><td class="s22">1. Attachments should be viewable, searchable, and removable.<br>2. Notes should display correctly and be limited to 100 characters.</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5">CITYLANDPRS-1024<br>CITYLANDPRS-1002<br>CITYLANDPRS-1004</td></tr><tr style="height: 67px"><th id="2013526587R62" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">63</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s30">Verify Attachments and Notes Section - Negative Scenario</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Upload an attachment that is more than 25mb<br>2. Upload an attachment that is not from these file types: PDF, Doc, JPG, JPEG, PNG, CSV, Excel<br>3. Input an emoji in the notes section<br>4. Input more than 100 characters in note section</td><td class="s24"></td><td class="s22">1. Should not be able to upload 25MB<br>2. Should not be able to upload different file type<br>3. Should not be able to add emojis<br>4. Should not be able to input more than 100 characters</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5">CITYLANDPRS-1024<br>CITYLANDPRS-1002<br>CITYLANDPRS-1004</td></tr><tr style="height: 67px"><th id="2013526587R63" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">64</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s22">Verify Items Section</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Search for an item by name using the search button.<br>2. Clear the search filter using the Clear button.<br>3. Check if columns for Item, Quantity, Amount, Discount, and Discounted Price are available.</td><td class="s24"></td><td class="s22">1. Items should be searchable.<br>2. The item table should display correct details.</td><td class="s5"></td><td class="s5"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4">blocked due. to pagination issues</td><td class="s16"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1028">CITYLANDPRS-1028</a></td></tr><tr style="height: 67px"><th id="2013526587R64" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">65</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s22">Verify Items Section Sorting</td><td class="s23">Minor</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on the column names to use the sorting function</td><td class="s24"></td><td class="s22">1. Sorting must correctly function asc - desc or desc - asc</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s31 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1031">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1031</a></div></td></tr><tr style="height: 67px"><th id="2013526587R65" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">66</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s22">Verify Search for an Item</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on search bar<br>2. Input in search bar<br>3. Click search</td><td class="s24"></td><td class="s22">1. Must be able to search for an item</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R66" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">67</div></th><td class="s5"></td><td class="s22">Non-RS Payment Approval</td><td class="s22">Verify Clear button in Item section</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on search bar<br>2. Input in search bar<br>3. Click search<br>4. Click clear</td><td class="s24"></td><td class="s22">1. Must be able to reset to default view of Items Table and search bar</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R67" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">68</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Verify Pagination of Items Section</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click on the other pages in items section<br>2. Check for max 10 for displayed items</td><td class="s27"></td><td class="s25">1. Able to switch pages without issue<br>2. Able to show only max 10 items per page</td><td class="s13"></td><td class="s13"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9">blocked due. to pagination issues</td><td class="s29"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1028">CITYLANDPRS-1028</a></td></tr><tr style="height: 67px"><th id="2013526587R68" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">69</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Verify Amount Section</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Ensure that Sub-total, Discounts, and Total Amount fields are present and calculated correctly.</td><td class="s27"></td><td class="s25">1. Amount calculations should be accurate and updated as per input.</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9">incorrect computations</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 171px"><th id="2013526587R69" style="height: 171px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 171px">70</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Approving the Payment Request</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click the Approve button.<br>2. If an additional Approver is added, verify that a confirmation modal appears with Submit and Cancel buttons.<br>3. Click Cancel and verify that the modal closes without approving.<br>4. Click Submit and ensure the Approver’s status updates to Approved.<br>5. If no additional Approver is added, verify that a modal appears with options: Cancel, Add Approver, and Confirm.<br>6. Click Add Approver and verify that a new Approver can be added.<br>7. Click Confirm and verify that the request is approved.<br>8. If the Approver is the last in the chain, ensure the request is sent to Accounting.</td><td class="s27"></td><td class="s25">1. The approval flow should work as expected.<br>2. Actions should be recorded in the request history.<br>3. If the last Approver, the request should be marked as Closed and sent to Accounting.</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s32"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1006">CITYLANDPRS-1006</a></td></tr><tr style="height: 67px"><th id="2013526587R70" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">71</div></th><td class="s13"></td><td class="s25">Non-RS Payment Approval</td><td class="s25">Verify Final Status Update</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Approve the request as the last Approver.<br>2. Verify that the Approver’s status updates to Approved.<br>3. Check that the Non-RS Status updates to Closed.</td><td class="s27"></td><td class="s25">1. The payment request should be marked as Closed and proceed to Accounting.</td><td class="s13"></td><td class="s13"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s32"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1006">CITYLANDPRS-1006</a></td></tr><tr style="height: 67px"><th id="2013526587R71" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">72</div></th><td class="s5"></td><td class="s22">Editing during Non-RS Approval</td><td class="s22">Verify Navigation to Non-RS Payments Request</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on a Non-RS Payment Number for approval</td><td class="s24"></td><td class="s22">1. The Non-RS Payments Dashboard should be displayed.<br>2. The Non-RS Payment Request should display</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R72" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">73</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">Verify Non-Editable Fields in Non-RS Details</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Navigate to the Non-RS Details section.<br>2. Verify that the following fields are non-editable:<br> - Charge To<br> - Invoice Number<br> - Payable To<br> - Date Needed<br> - Delivery Fee</td><td class="s27"></td><td class="s25">1. The mentioned fields should not be editable.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R73" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">74</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">View Attachments in Non-RS Payment Request</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click on the Attachments button.<br>2. Verify that a modal opens displaying a list of uploaded files.<br>3. Search for a file name.<br>4. Click on a file name to view the attachment in a new tab.<br>5. Click the X icon to remove an attachment.<br>6. Confirm the removal in the confirmation modal.</td><td class="s27"></td><td class="s25">1. The attachment list modal should display all uploaded files.<br>2. Searching should filter the list accordingly.<br>3. Clicking a file name should open the attachment in a new tab.<br>4. Removing an attachment should require confirmation and remove the file upon confirmation.</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s32"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1002">CITYLANDPRS-1002</a></td></tr><tr style="height: 67px"><th id="2013526587R74" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">75</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">Verify Uploading Attachments</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Click Select Attachments</td><td class="s27"></td><td class="s25">1. Maximum file size should be 25MB.<br>2. Allowed file types: PDF, DOC, JPG, JPEG, PNG, CSV, Excel.<br>3. Click Submit to confirm file upload.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R75" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">76</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s28">Verify Uploading Attachments in Non-RS Payment Request - Negative Scenario</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Upload an attachment that is more than 25mb<br>2. Upload an attachment that is not from this list of file types: PDF, Doc, JPG, JPEG, PNG, CSV, Excel</td><td class="s27"></td><td class="s25">1. Should not be able to upload attachment more than 25mb<br>2. Should not be able to upload attachment that is not from the list</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R76" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">77</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">Verify Adding Notes</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Add a note with alphanumeric and special characters (except emojis).<br>3. Click Submit.</td><td class="s27"></td><td class="s25">1. Should allow a maximum of 100 characters.<br>2. Note should be saved and displayed.</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s29"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1039">CITYLANDPRS-1039</a></td></tr><tr style="height: 67px"><th id="2013526587R77" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">78</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s28">Verify Adding Notes - Negative Scenario</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click on a Non-RS Payment Number.<br>2. Add a note with emojis<br>3. Add a note that is more than 100 characters</td><td class="s27"></td><td class="s25">1. Should not be able to add emojis<br>2. Should not be able to input more than 100 characters</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s29"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1039">CITYLANDPRS-1039</a></td></tr><tr style="height: 67px"><th id="2013526587R78" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">79</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">Verify Edit Item Quantity and Discount</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Locate an item in the Items Table.<br>2. Click the Search button and search for an item by keyword.<br>3. Click the Clear button to reset the search field.<br>4. Update the Quantity field.<br>5. Verify that only numeric values are accepted and a maximum of 3 characters is enforced.<br>6. Click the Edit button to update the Discount field.</td><td class="s27"></td><td class="s25">1. Searching should filter the table accordingly.<br>2. Clicking Clear should reset the filter.<br>3. The Quantity field should only accept numeric values and be limited to 3 characters.<br>4. The Discount field should be editable when clicking Edit.</td><td class="s13"></td><td class="s13"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s29"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1040">CITYLANDPRS-1040</a></td></tr><tr style="height: 67px"><th id="2013526587R79" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">80</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">Verify Amount Section</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Navigate to the Amount section.<br>2. Verify that the following values are displayed:<br> - Sub-total<br> - Discounts<br> - Total Amount</td><td class="s27"></td><td class="s25">1. The Amount section should correctly display the calculated values.</td><td class="s13"></td><td class="s13"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 67px"><th id="2013526587R80" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">81</div></th><td class="s13"></td><td class="s25">Editing during Non-RS Approval</td><td class="s25">Verify Save Changes and Approve Request</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Make changes to the editable fields.<br>2. Click the Approve button.<br>3. Verify that changes are saved and reflected in the Non-RS Payment Request.<br>4. Ensure previous approvers are not required to re-approve.<br>5. Verify that a notification is sent to the requester.</td><td class="s27"></td><td class="s25">1. Changes should be saved successfully.<br>2. Previous approvers should not need to re-approve.<br>3. The requester should receive a notification via the notification bell.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R81" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">82</div></th><td class="s13"></td><td class="s25">Non-RS Payment Rejecting</td><td class="s25">Verify Navigation to Non-RS Payments Request</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click Request Flow<br>2. Click Non-RS<br>3. Click on a Non-RS Payment Number for approval</td><td class="s27"></td><td class="s25">1. The Non-RS Payments Dashboard should be displayed.<br>2. The Non-RS Payment Request should display</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R82" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">83</div></th><td class="s13"></td><td class="s25">Non-RS Payment Rejecting</td><td class="s25">Verify Approval and Rejection Confirmation</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Check for sticky confirmation messages for Approving or Rejecting the payment.</td><td class="s27"></td><td class="s25">1. Sticky confirmation should appear for both Approve and Reject actions.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R83" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">84</div></th><td class="s13"></td><td class="s25">Non-RS Payment Rejecting</td><td class="s25">Verify Adding an Approver</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Locate the Approvers section.<br>2. Click the Add button.<br>3. Verify that a modal appears allowing the addition of an Approver.<br>4. Ensure that all user types are displayed except IT Admin and Root User.<br>5. Verify that users already in the approval list are excluded.<br>6. Add a new Approver.<br>7. Verify that the newly added Approver appears below the current Approver in the Approvers section.<br>8. Edit or remove the newly added Approver using the ellipsis menu.</td><td class="s27"></td><td class="s25">1. The Approver should be added successfully and displayed in the list.<br>2. Edit and Delete options should function correctly.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R84" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">85</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Non-RS Payment Details Section</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Check if fields for Charge To, Invoice Number, Payable To, Date Needed, and Delivery Fee are present.</td><td class="s24"></td><td class="s22">1. All required fields should be available and populated correctly.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R85" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">86</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Uploading Attachments</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on a Non-RS Payment Number.<br>2. Click Select Attachments</td><td class="s24"></td><td class="s22">1. Maximum file size should be 25MB.<br>2. Allowed file types: PDF, DOC, JPG, JPEG, PNG, CSV, Excel.<br>3. Click Submit to confirm file upload.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R86" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">87</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s30">Verify Uploading Attachments in Non-RS Payment Request - Negative Scenario</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Upload an attachment that is more than 25mb<br>2. Upload an attachment that is not from this list of file types: PDF, Doc, JPG, JPEG, PNG, CSV, Excel</td><td class="s24"></td><td class="s22">1. Should not be able to upload attachment more than 25mb<br>2. Should not be able to upload attachment that is not from the list</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R87" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">88</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Adding Notes</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on a Non-RS Payment Number.<br>2. Add a note with alphanumeric and special characters (except emojis).<br>3. Click Submit.</td><td class="s24"></td><td class="s22">1. Should allow a maximum of 100 characters.<br>2. Note should be saved and displayed.</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5">CITYLANDPRS-1024<br>CITYLANDPRS-1002<br>CITYLANDPRS-1004</td></tr><tr style="height: 67px"><th id="2013526587R88" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">89</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s30">Verify Adding Notes - Negative Scenario</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on a Non-RS Payment Number.<br>2. Add a note with emojis<br>3. Add a note that is more than 100 characters</td><td class="s24"></td><td class="s22">1. Should not be able to add emojis<br>2. Should not be able to input more than 100 characters</td><td class="s5"></td><td class="s5"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s33"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1039">CITYLANDPRS-1039</a></td></tr><tr style="height: 67px"><th id="2013526587R89" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">90</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Items Section</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Search for an item by name using the search button.<br>2. Clear the search filter using the Clear button.<br>3. Check if columns for Item, Quantity, Amount, Discount, and Discounted Price are available.</td><td class="s24"></td><td class="s22">1. Items should be searchable.<br>2. The item table should display correct details.</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R90" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">91</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Items Section Sorting</td><td class="s23">Minor</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on the column names to use the sorting function</td><td class="s24"></td><td class="s22">1. Sorting must correctly function asc - desc or desc - asc</td><td class="s5"></td><td class="s5"></td><td class="s15">Failed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s33"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1031">CITYLANDPRS-1031</a></td></tr><tr style="height: 67px"><th id="2013526587R91" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">92</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Search for an Item</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on search bar<br>2. Input in search bar<br>3. Click search</td><td class="s24"></td><td class="s22">1. Must be able to search for an item</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R92" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">93</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Clear button in Item section</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on search bar<br>2. Input in search bar<br>3. Click search<br>4. Click clear</td><td class="s24"></td><td class="s22">1. Must be able to reset to default view of Items Table and search bar</td><td class="s5"></td><td class="s5"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s5"></td></tr><tr style="height: 67px"><th id="2013526587R93" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">94</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Pagination of Items Section</td><td class="s23">High</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Click on the other pages in items section<br>2. Check for max 10 for displayed items</td><td class="s24"></td><td class="s22">1. Able to switch pages without issue<br>2. Able to show only max 10 items per page</td><td class="s5"></td><td class="s5"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s33"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1028">CITYLANDPRS-1028</a></td></tr><tr style="height: 67px"><th id="2013526587R94" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">95</div></th><td class="s5"></td><td class="s22">Non-RS Payment Rejecting</td><td class="s22">Verify Amount Section</td><td class="s23">Critical</td><td class="s22">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s22">1. Ensure that Sub-total, Discounts, and Total Amount fields are present and calculated correctly.</td><td class="s24"></td><td class="s22">1. Amount calculations should be accurate and updated as per input.</td><td class="s5"></td><td class="s5"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s4"></td><td class="s34"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 67px"><th id="2013526587R95" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">96</div></th><td class="s13"></td><td class="s25">Non-RS Payment Rejecting</td><td class="s25">Verify Rejecting the Payment Request</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should be an Approver</td><td class="s25">1. Click the Reject button.<br>2. Enter a reason for rejection.<br>3. Ensure that the reason field allows alphanumeric and special characters (except emojis) with a max of 100 characters.<br>4. Click Cancel to close the modal and return to the Non-RS Payment view.<br>5. Click Continue to proceed with rejection.<br>6. Verify that the Approver&#39;s status and Non-RS status update to Rejected.<br>7. Confirm that the requester is notified via the notification bell.<br>8. Verify that the rejection reason appears in the Notes section with a Disapproval tag.</td><td class="s27"></td><td class="s25">1. The rejection modal should function correctly.<br>2. The Non-RS payment request should update to Rejected status.<br>3. The requester should be notified of the rejection.</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R96" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">97</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify resubmission entry via Non-RS Dashboard</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Navigate to Non-RS Dashboard Page <br>2. Locate the rejected Non-RS Payment Request <br>3. Click on the rejected request</td><td class="s27"></td><td class="s25">1. User is redirected to the resubmission page</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R97" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">98</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify resubmission entry via Notification Bell</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Click on the Notification Bell <br>2. Locate the rejected Non-RS Payment Request notification <br>3. Click on the notification</td><td class="s27"></td><td class="s25">1. User is redirected to the resubmission page</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R98" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">99</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify redirection to the Non-RS PR</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Click on an entry point <br>2. Observe the redirection</td><td class="s27"></td><td class="s25">1. User is redirected to the resubmission page</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R99" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">100</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify editing of &quot;Charge To&quot; field</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Navigate to &quot;Charge To&quot; field <br>2. Edit and save changes</td><td class="s27"></td><td class="s25">1. Changes should be successfully saved</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R100" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">101</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify editing of &quot;Payable To&quot; field</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Navigate to &quot;Payable To&quot; field <br>2. Edit and save changes</td><td class="s27"></td><td class="s25">1. Changes should be successfully saved</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R101" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">102</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify editing of &quot;Date Needed&quot; field</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Navigate to &quot;Date Needed&quot; field <br>2. Edit and save changes</td><td class="s27"></td><td class="s25">1. Changes should be successfully saved</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R102" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">103</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify editing of &quot;Delivery Fee&quot; field</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Navigate to &quot;Delivery Fee&quot; field <br>2. Edit and save changes</td><td class="s27"></td><td class="s25">1. Changes should be successfully saved</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R103" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">104</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Quantity field only accepts numbers</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Enter non-numeric characters in Quantity field</td><td class="s27"></td><td class="s25">1. should not allow non-numeric input</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R104" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">105</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25"> Verify Quantity field allows up to 5 characters</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Enter more than 5 characters in Quantity field</td><td class="s27"></td><td class="s25">1. should restrict entry to 5 characters</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R105" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">106</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Amount field allows decimal numbers</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Enter a decimal value in the Amount field</td><td class="s27"></td><td class="s25">1. should allow decimal numbers</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R106" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">107</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Amount field allows up to 10 characters</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Enter more than 10 characters in Amount field</td><td class="s27"></td><td class="s25">1. should restrict entry to 10 characters</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R107" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">108</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Discount calculation for Fixed Amount</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Enter a fixed discount value <br>2. Verify calculation</td><td class="s27"></td><td class="s25">1. should correctly deduct the discount amount</td><td class="s13"></td><td class="s13"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 67px"><th id="2013526587R108" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">109</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Discount calculation for Percentage</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Enter a percentage discount <br>2. Verify calculation</td><td class="s27"></td><td class="s25">1. should correctly calculate and apply the discount</td><td class="s13"></td><td class="s13"></td><td class="s17">Blocked</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-988">CITYLANDPRS-988</a></td></tr><tr style="height: 67px"><th id="2013526587R109" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">110</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Cancel button functionality</td><td class="s26">High</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Click the Cancel button <br>2. Confirm the cancellation</td><td class="s27"></td><td class="s25">1. should close the modal and retain rejection status</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R110" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">111</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Submit button functionality</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Click the Submit button <br>2. Confirm submission</td><td class="s27"></td><td class="s25">1. should update request details and change status to Pending</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R111" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">112</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Approver Status after submission</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Submit the resubmission request <br>2. Check the Approver&#39;s Status</td><td class="s27"></td><td class="s25">1. Approver&#39;s Status should update to Pending</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R112" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">113</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Non-RS Status after submission</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Submit the resubmission request <br>2. Check the Non-RS Status</td><td class="s27"></td><td class="s25">1. Non-RS Status should update to &quot;For Approval&quot;</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr><tr style="height: 67px"><th id="2013526587R113" style="height: 67px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 67px">114</div></th><td class="s13"></td><td class="s25">Resubmitting of rejected Non-RS</td><td class="s25">Verify Approver can proceed with approval</td><td class="s26">Critical</td><td class="s25">1. Should have a Non-RS Payment Request created<br>2. Should have the Non-RS Payment Request Rejected<br>3. Account use is the Requester</td><td class="s25">1. Submit the resubmission request <br>2. Login as Approver <br>3. Check for pending approval</td><td class="s27"></td><td class="s25">1. Approver should be able to review and approve/reject</td><td class="s13"></td><td class="s13"></td><td class="s8">Passed</td><td class="s9"></td><td class="s10">Not Started</td><td class="s9"></td><td class="s9"></td><td class="s13"></td></tr></tbody></table></div>