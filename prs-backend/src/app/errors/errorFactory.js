const AppError = require('./appError');
const ValidationError = require('./validationError');
const NotFoundError = require('./notFoundError');
const AuthenticationError = require('./authenticationError');
const AuthorizationError = require('./authorizationError');
const BadRequestError = require('./badRequestError');
const ConflictError = require('./conflictError');
const DatabaseError = require('./databaseError');

/**
 * Factory for creating application errors
 */
class ErrorFactory {
  /**
   * Creates a validation error
   * 
   * @param {string} message - Error message
   * @param {Object} validationErrors - Validation errors by field
   * @param {Error} originalError - Original error that caused this error
   * @returns {ValidationError} - New ValidationError instance
   */
  static validation(message = 'Validation failed', validationErrors = {}, originalError = null) {
    return new ValidationError(message, validationErrors, originalError);
  }

  /**
   * Creates a not found error
   * 
   * @param {string} resource - Resource type that was not found
   * @param {string|number} identifier - Resource identifier
   * @param {string} message - Optional custom error message
   * @param {Error} originalError - Original error that caused this error
   * @returns {NotFoundError} - New NotFoundError instance
   */
  static notFound(resource = 'Resource', identifier = null, message = null, originalError = null) {
    return new NotFoundError(resource, identifier, message, originalError);
  }

  /**
   * Creates an authentication error
   * 
   * @param {string} message - Error message
   * @param {string} reason - Reason for authentication failure
   * @param {Error} originalError - Original error that caused this error
   * @returns {AuthenticationError} - New AuthenticationError instance
   */
  static authentication(message = 'Authentication failed', reason = 'INVALID_CREDENTIALS', originalError = null) {
    return new AuthenticationError(message, reason, originalError);
  }

  /**
   * Creates an authorization error
   * 
   * @param {string} message - Error message
   * @param {string} permission - Permission that was required
   * @param {Error} originalError - Original error that caused this error
   * @returns {AuthorizationError} - New AuthorizationError instance
   */
  static authorization(message = 'You do not have permission to perform this action', permission = null, originalError = null) {
    return new AuthorizationError(message, permission, originalError);
  }

  /**
   * Creates a bad request error
   * 
   * @param {string} message - Error message
   * @param {Object} details - Additional error details
   * @param {Error} originalError - Original error that caused this error
   * @returns {BadRequestError} - New BadRequestError instance
   */
  static badRequest(message = 'Bad request', details = {}, originalError = null) {
    return new BadRequestError(message, details, originalError);
  }

  /**
   * Creates a conflict error
   * 
   * @param {string} message - Error message
   * @param {string} resource - Resource type that has a conflict
   * @param {string} field - Field that has a conflict
   * @param {string} value - Value that caused the conflict
   * @param {Error} originalError - Original error that caused this error
   * @returns {ConflictError} - New ConflictError instance
   */
  static conflict(message = 'Resource already exists', resource = null, field = null, value = null, originalError = null) {
    return new ConflictError(message, resource, field, value, originalError);
  }

  /**
   * Creates a database error
   * 
   * @param {string} message - Error message
   * @param {string} operation - Database operation that failed
   * @param {Error} originalError - Original error that caused this error
   * @returns {DatabaseError} - New DatabaseError instance
   */
  static database(message = 'Database operation failed', operation = null, originalError = null) {
    return new DatabaseError(message, operation, originalError);
  }

  /**
   * Creates a generic application error
   * 
   * @param {string} message - Error message
   * @param {string} errorCode - Error code
   * @param {Object} metadata - Additional error metadata
   * @param {Error} originalError - Original error that caused this error
   * @returns {AppError} - New AppError instance
   */
  static generic(message = 'An error occurred', errorCode = 'APP_ERROR', metadata = {}, originalError = null) {
    return new AppError(message, errorCode, metadata, originalError);
  }
}

module.exports = ErrorFactory;
