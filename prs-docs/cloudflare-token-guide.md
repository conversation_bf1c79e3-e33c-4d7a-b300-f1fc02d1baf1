# Cloudflare API Token Guide

This guide will help you create a Cloudflare API token with the correct permissions for deploying to Cloudflare Pages.

## Creating a Cloudflare API Token

1. **Log in to your Cloudflare dashboard** at https://dash.cloudflare.com/

2. **Navigate to API Tokens**:
   - Click on your profile icon in the top right corner
   - Select "My Profile"
   - Go to the "API Tokens" tab

3. **Create a new token**:
   - Click "Create Token"
   - Select "Create Custom Token"

4. **Configure token permissions**:
   - **Token name**: `Cloudflare Pages Deployment`
   - **Permissions**:
     - Account > Cloudflare Pages > Edit
     - Account > Account Settings > Read
     - User > User Details > Read (IMPORTANT - this was missing in your previous token)
     - Zone > Zone Settings > Read
     - Zone > Zone > Read

5. **Set the Account Resources**:
   - Include > Specific account > Select your account (cf535d76fffdb44d43f6a3c00a3ebbe6)

6. **Set the Zone Resources** (if applicable):
   - Include > All zones

7. **Set the TTL** (Time to Live):
   - You can leave this as the default or set it to "No expiration" if you want the token to be permanent

8. **Create the token**:
   - Click "Continue to summary"
   - Review the permissions
   - Click "Create Token"

9. **Copy the token**:
   - After creating the token, you'll see it displayed once
   - Copy this token and store it securely
   - You'll need this token for deploying to Cloudflare Pages

## Using the New API Token

After creating the new token, you can use it to deploy to Cloudflare Pages:

```bash
# Set the token as an environment variable
export CLOUDFLARE_API_TOKEN=your_new_token_here

# Deploy to Cloudflare Pages
./docs.sh deploy-cf
```

## Troubleshooting

If you still encounter issues with the API token:

1. **Double-check the permissions**: Make sure the token has all the required permissions, especially the "User > User Details > Read" permission.
2. **Verify the account ID**: Make sure the token has access to the correct account (cf535d76fffdb44d43f6a3c00a3ebbe6).
3. **Try logging in with Wrangler**: Run `wrangler login` to authenticate with your Cloudflare account instead of using an API token.

## Security Note

Remember to keep your API token secure and never share it in plain text. If you accidentally expose your token, revoke it immediately and create a new one.
