# PRS Next.js Full Stack Rebuild Guide

This guide provides comprehensive instructions for rebuilding the Purchase Requisition System (PRS) using Next.js 15 full stack architecture with Server-Sent Events (SSE) for real-time functionality.

## Overview

The original PRS system consists of:
- **prs-backend**: A Node.js/Fastify API with PostgreSQL database
- **prs-frontend**: A React application with Vite, React Router, and Zustand

This guide will help you rebuild both components into a unified Next.js 15 full stack application, leveraging:
- Next.js App Router
- Server Components
- Server Actions
- API Routes
- Server-Sent Events (SSE) for real-time updates
- Prisma ORM for database access
- NextAuth.js for authentication

## Table of Contents

1. [Project Setup](./docs/01-project-setup.md)
2. [Architecture Overview](./docs/02-architecture-overview.md)
3. [Database Setup](./docs/03-database-setup.md)
4. [Authentication Implementation](./docs/04-authentication.md)
5. [API Routes Implementation](./docs/05-api-routes.md)
6. [Server-Sent Events Implementation](./docs/06-server-sent-events.md)
7. [Business Logic Implementation](./docs/07-business-logic.md)
8. [Frontend Components](./docs/08-frontend-components.md)
9. [Workflow Implementation](./docs/09-workflow-implementation.md)
10. [Testing Strategy](./docs/10-testing-strategy.md)
11. [Deployment Guide](./docs/11-deployment-guide.md)

## Getting Started

To get started with the rebuild, follow these steps:

1. Clone the repository
2. Set up the devcontainer environment
3. Install dependencies with `npm install`
4. Set up the database connection in the `.env` file
5. Run database migrations with `npx prisma migrate dev`
6. Seed the database with test data using `npx prisma db seed`
7. Start the development server with `npm run dev`

## Testing

For detailed instructions on how to test the application, see the [TESTING.md](./TESTING.md) file. This includes:

- Testing the authentication system
- Testing the requisition workflow
- Testing the approval process
- Testing the purchase order system
- Testing the payment system

## PRS Workflow

The PRS workflow follows this sequence:
1. Create RS (Requisition Slip)
2. Submit for Approval
3. Approval Process
4. Canvassing
5. Canvassing Approval
6. Purchase Order
7. Purchase Order Approval
8. Delivery/Invoice
9. Payment
10. Payment Approval

Each step in this workflow will be implemented in the Next.js application with appropriate data models, API routes, and UI components.

## Development Environment

This project uses a devcontainer with Node.js 22 Alpine for development. The devcontainer configuration is provided in the `.devcontainer` directory.

## Contributing

When contributing to this rebuild project, please follow the coding standards and guidelines outlined in the [Development Guide](./docs/12-development-guide.md).
