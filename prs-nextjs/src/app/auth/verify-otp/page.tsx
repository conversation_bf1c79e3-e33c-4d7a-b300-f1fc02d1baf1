"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { Form } from "@/components/ui/Form/Form";
import { Input } from "@/components/ui/Form/Input";
import { Button } from "@/components/ui/Button/Button";
import { AuthLayout } from "@/components/layouts/AuthLayout";
import { AuthService } from "@/lib/auth/auth-service";

const otpSchema = z.object({
  otp: z.string().min(1, "OTP code is required"),
});

export default function VerifyOTPPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const handleOTPVerification = async (values: z.infer<typeof otpSchema>) => {
    setLoading(true);
    setErrorMessage("");

    try {
      // In a real implementation, we would get the username and password from the session
      const response = await AuthService.verifyOTP(
        "user", // Example username
        "password", // Example password
        values.otp
      );

      if (!response.success) {
        setErrorMessage(response.error || "Failed to verify OTP");
        setLoading(false);
        return;
      }

      // Successful verification
      router.push("/app/dashboard");
    } catch (error) {
      console.error("OTP verification error:", error);
      setErrorMessage("An unexpected error occurred");
      setLoading(false);
    }
  };

  return (
    <AuthLayout navigateVisible={true}>
      <div className="flex flex-col gap-1">
        <h2 className="text-2xl font-bold text-red-950">Verify OTP Code</h2>
        <p className="text-sm text-gray-800">
          Enter the 6-digit code from your authenticator app
        </p>
      </div>

      {errorMessage && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 my-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      <Form
        schema={otpSchema}
        onSubmit={handleOTPVerification}
        className="mt-4"
      >
        {({ register, formState, disabled }) => (
          <div className="flex flex-col gap-4">
            <Input
              type="text"
              label="OTP Code"
              error={formState.errors['otp']?.message}
              registration={register('otp')}
              placeholder="Enter your OTP code"
              disabled={loading}
            />

            <Button
              disabled={disabled || loading}
              isLoading={loading}
              className="my-2"
              type="submit"
            >
              Verify OTP
            </Button>
          </div>
        )}
      </Form>
    </AuthLayout>
  );
}
