import PropTypes from 'prop-types';
import React from 'react';
import { Button } from '@components/ui/Button';
import BackIcon from '@assets/icons/back.svg?react';
import { cn } from '@src/utils/cn';
import ReactDOM from 'react-dom';
const ScreenModal =({
    isOpen = true,
    onClose,
    header = {},
    onConfirm,
    onCancel,
    children,
    portal,
    hasBackButton = true,
}) => {

    if (!isOpen) return null;

    return ReactDOM.createPortal(
    <div className='z-20 absolute inset-x-8 flex items-center justify-center'>
        <div className='w-screen max-w-full mx-auto bg-[#F7F8FA] p-6 rounded-lg shadow-sm '>
            <header className="mb-8">
                <div className="flex space-x-6">
                    {hasBackButton && (
                    <Button
                        onClick={onClose}
                        className="w-fit"
                        icon={BackIcon}
                        iconPosition="L"
                        variant="outline"
                        hover="outline"
                    >
                        Go Back
                    </Button>
                    )}
                    <h1 className="text-2xl font-bold leading-relaxed">
                    {header.title}
                    </h1>
                </div>
            </header>
            {children}

            <div className='flex flex-row justify-end space-x-4 mt-5'>
                <React.Fragment>
                    <Button 
                        onClick={onCancel}
                        className="min-w-fit w-24"
                        variant="outline"
                        hover="outline"
                        type="button"
                    >
                        Cancel
                    </Button>
                    <Button 
                        onClick={onConfirm}
                        className="min-w-fit w-24"
                        type="button"
                        variant="submit"
                        hover="submit"
                    >
                        Confirm
                    </Button>
                </React.Fragment>
            </div>
        </div>
    </div>,
    document.getElementById(portal)
    );
}

ScreenModal.propTypes = {
    isOpen: PropTypes.bool,
    onclose: PropTypes.func,
    portal: PropTypes.string.isRequired,
    header: PropTypes.shape({
        title: PropTypes.string,
        titleCSS: PropTypes.string,
    }),
    onConfirm: PropTypes.func,
    onCancel: PropTypes.func,
    children: PropTypes.node,
}

export { ScreenModal };