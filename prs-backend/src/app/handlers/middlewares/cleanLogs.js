/**
 * Clean Logs Middleware
 *
 * This middleware implements production-grade logging practices:
 * 1. Adds request context to all logs
 * 2. Filters out unnecessary logs in production
 * 3. Implements log sampling for high-volume endpoints
 * 4. Ensures consistent structured logging
 * 5. Redacts sensitive information
 * 6. Optimizes performance by reducing log volume
 */

const { LOG_CATEGORIES } = require('../../../infra/logs/loggerService');

// High-volume endpoints that should use log sampling
const HIGH_VOLUME_ENDPOINTS = [
  '/api/health',
  '/api/healthz',
  '/api/metrics',
];

// Sampling rates for different log levels (percentage of logs to keep)
const SAMPLING_RATES = {
  info: 0.1,  // Keep 10% of info logs for high-volume endpoints
  warn: 0.5,  // Keep 50% of warning logs for high-volume endpoints
  error: 1.0, // Keep all error logs
};

/**
 * Determines if a log should be sampled based on endpoint and log level
 * @param {string} url - Request URL
 * @param {string} level - Log level
 * @returns {boolean} - Whether to keep the log
 */
const shouldSampleLog = (url, level) => {
  // For high-volume endpoints, apply sampling
  if (HIGH_VOLUME_ENDPOINTS.some(endpoint => url.startsWith(endpoint))) {
    return Math.random() < (SAMPLING_RATES[level] || 1.0);
  }

  // For normal endpoints, keep all logs
  return true;
};

/**
 * Checks if a log object contains important business information
 * @param {object} obj - Log object
 * @returns {boolean} - Whether the log contains important information
 */
const isImportantLog = (obj) => {
  if (!obj || typeof obj !== 'object') return false;

  // Always log objects with these properties
  return !!(
    obj.event ||
    obj.action ||
    obj.userId ||
    obj.error ||
    obj.warning ||
    obj.message ||
    obj.businessMetric ||
    obj.security ||
    obj.performance ||
    (obj.category && [
      LOG_CATEGORIES.BUSINESS,
      LOG_CATEGORIES.SECURITY,
      LOG_CATEGORIES.PERFORMANCE
    ].includes(obj.category))
  );
};

/**
 * Redacts sensitive information from log objects
 * @param {object} obj - Log object
 * @returns {object} - Redacted log object
 */
const redactSensitiveInfo = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;

  const result = { ...obj };

  // Redact sensitive fields
  const sensitiveFields = [
    'password', 'token', 'secret', 'authorization', 'accessToken',
    'refreshToken', 'apiKey', 'key', 'credential'
  ];

  // Recursively check and redact fields
  const redact = (object, path = '') => {
    if (!object || typeof object !== 'object') return;

    Object.keys(object).forEach(key => {
      const lowerKey = key.toLowerCase();
      const fullPath = path ? `${path}.${key}` : key;

      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        object[key] = '[REDACTED]';
      } else if (typeof object[key] === 'object' && object[key] !== null) {
        redact(object[key], fullPath);
      }
    });
  };

  redact(result);
  return result;
};

const cleanLogsMiddleware = (req, _reply, done) => {
  // Add request context to all logs with enhanced metadata
  const requestContext = {
    requestId: req.id,
    url: req.url,
    method: req.method,
    // Capture both direct IP and forwarded IP (for proxy scenarios)
    ip: req.ip,
    forwardedIp: req.headers['x-forwarded-for'] || undefined,
    // Capture user agent and other useful headers
    userAgent: req.headers['user-agent'],
    referer: req.headers['referer'] || undefined,
    origin: req.headers['origin'] || undefined,
    // Add host information
    host: req.headers['host'] || undefined,
    // Add request path and query parameters separately for easier filtering
    path: req.routerPath || req.url.split('?')[0],
    // Add timestamp for request start
    requestTime: new Date().toISOString(),
  };

  // Add user context if available
  if (req.user) {
    requestContext.userId = req.user.id;
    requestContext.username = req.user.username;
    requestContext.role = req.user.role?.name;
    // Add department if available
    if (req.user.department) {
      requestContext.departmentId = req.user.department.id;
      requestContext.departmentName = req.user.department.name;
    }
  }

  // Create a child logger with request context
  req.log = req.log.child(requestContext);

  // In production, apply advanced logging strategies
  if (process.env.NODE_ENV === 'production') {
    // Store the original logger methods
    const originalInfo = req.log.info;
    const originalError = req.log.error;
    const originalWarn = req.log.warn;

    // Override info logs with sampling and filtering
    req.log.info = (obj, msg, ...args) => {
      // Apply sampling based on endpoint
      if (!shouldSampleLog(req.url, 'info')) return;

      // Handle string messages
      if (typeof obj === 'string') {
        // Only log strings that appear to be important
        if (
          obj.includes('Starting') ||
          obj.includes('Completed') ||
          obj.includes('Created') ||
          obj.includes('Updated') ||
          obj.includes('Deleted') ||
          obj.includes('Processing')
        ) {
          originalInfo.call(req.log, obj);
        }
        return;
      }

      // For objects, only log important ones
      if (isImportantLog(obj)) {
        // Redact sensitive information
        const safeObj = redactSensitiveInfo(obj);
        originalInfo.call(req.log, safeObj, msg, ...args);
      }
    };

    // Override error logs - always log errors but redact sensitive info
    req.log.error = (obj, msg, ...args) => {
      if (typeof obj === 'string') {
        originalError.call(req.log, obj);
        return;
      }

      // Redact sensitive information from error objects
      const safeObj = redactSensitiveInfo(obj);
      originalError.call(req.log, safeObj, msg, ...args);
    };

    // Override warning logs with sampling and filtering
    req.log.warn = (obj, msg, ...args) => {
      // Apply sampling based on endpoint
      if (!shouldSampleLog(req.url, 'warn')) return;

      if (typeof obj === 'string') {
        originalWarn.call(req.log, obj);
        return;
      }

      // For objects, only log important ones
      if (isImportantLog(obj)) {
        // Redact sensitive information
        const safeObj = redactSensitiveInfo(obj);
        originalWarn.call(req.log, safeObj, msg, ...args);
      }
    };

    // In production, disable debug and trace logs
    req.log.debug = () => {};
    req.log.trace = () => {};
  }

  done();
};

module.exports = cleanLogsMiddleware;
