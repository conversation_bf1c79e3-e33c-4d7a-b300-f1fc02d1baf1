# System Architecture

## Overview

The PRS-Backend is built using a clean architecture approach with clear separation of concerns. This document provides a detailed analysis of the system's architecture, including its layers, components, and interactions.

## Architectural Layers

The system follows a layered architecture with the following components:

```
prs-backend/
├── src/
│   ├── app/                  # Application layer
│   │   ├── errors/           # Error handling
│   │   ├── handlers/         # Controllers and middleware
│   │   ├── services/         # Business logic
│   │   └── utils/            # Utility functions
│   ├── domain/               # Domain layer
│   │   ├── constants/        # Business constants
│   │   └── entities/         # Domain entities and validation schemas
│   ├── infra/                # Infrastructure layer
│   │   ├── database/         # Database models, migrations, seeders
│   │   └── logs/             # Logging configuration
│   ├── interfaces/           # Interface layer
│   │   ├── cors/             # CORS settings
│   │   └── router/           # API routes
│   ├── container.js          # Dependency injection container
│   └── server.js             # Server initialization
├── index.js                  # Application entry point
```

### Domain Layer

The domain layer contains the core business logic and entities of the application. It is independent of other layers and frameworks.

#### Domain Entities

Domain entities represent the core business objects and include validation schemas using Zod:

```javascript
// Example from src/domain/entities/requisitionEntity.js
const createRequisitionSchema = z
  .object({
    isDraft: z
      .enum(['true'], {
        required_error: 'Draft status is required',
        invalid_type_error: 'Invalid draft status',
      })
      .transform((value) => value === 'true'),
    type: z.enum(['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], {
      message: REQUIRED_FIELD_ERROR,
    }),
    // ... other fields
  })
  .strict();
```

#### Domain Constants

Business constants define the valid values and states for domain entities:

```javascript
// Example from src/domain/constants/requisitionConstants.js
const REQUISITION_STATUS = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  APPROVED: 'approved',
  ASSIGNED: 'assigned',
  ASSIGNING: 'assigning',
  CANVASS_FOR_APPROVAL: 'canvass_for_approval',
  FOR_DELIVERY: 'for_delivery',
  FOR_PO_REVIEW: 'for_po_review',
  FOR_PR_APPROVAL: 'for_pr_approval',
  PARTIALLY_CANVASSED: 'partially_canvassed',
  REJECTED: 'rejected',
  CLOSED: 'closed',
};
```

### Application Layer

The application layer contains the application services that implement the business logic and orchestrate the domain entities.

#### Services

Services implement the business logic and use repositories to interact with the database:

```javascript
// Example from src/app/services/requisitionService.js
class RequisitionService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      userRepository,
      requisitionRepository,
      // ... many other dependencies
    } = container;
    
    // ... initialize service
  }
  
  async createRequisition(data) {
    // Implementation of business logic
  }
  
  // ... other methods
}
```

#### Error Handling

The application defines custom error classes for different types of errors:

```javascript
// Example from src/app/errors/clientErrors.js
const UNAUTHORIZED = (payload = {}) => {
  const { message = 'Unauthorized', description } = payload;

  return new HttpError({
    message,
    status: 401,
    description,
    errorCode: 'UNAUTHORIZED',
  });
};
```

### Infrastructure Layer

The infrastructure layer provides implementations for external interfaces such as databases, external APIs, and file systems.

#### Database Models

Database models are defined using Sequelize:

```javascript
// Example from src/infra/database/models/requisitionModel.js
const RequisitionModel = sequelize.define(
  'requisitions',
  {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    // ... other fields
  },
  {
    timestamps: true,
    underscored: true,
  }
);

RequisitionModel.associate = (models) => {
  // Define associations
};
```

#### Repositories

Repositories provide an abstraction layer for database operations:

```javascript
// Example repository pattern
class RequisitionRepository {
  constructor({ db }) {
    this.db = db;
    this.model = db.requisitionModel;
  }
  
  async findById(id) {
    return this.model.findByPk(id);
  }
  
  // ... other methods
}
```

### Interface Layer

The interface layer handles the communication with external systems and users.

#### Routes

Routes define the API endpoints and connect them to controllers:

```javascript
// Example from src/interfaces/router/private/requisitionRoute.js
async function requisitionRoutes(fastify) {
  const requisitionController = fastify.diScope.resolve('requisitionController');
  
  fastify.route({
    method: 'POST',
    url: '/',
    handler: requisitionController.createRequisition.bind(requisitionController),
  });
  
  // ... other routes
}
```

#### Controllers

Controllers handle HTTP requests and delegate to services:

```javascript
// Example from src/app/handlers/controllers/requisitionController.js
class RequisitionController {
  constructor({ requisitionService, clientErrors }) {
    this.requisitionService = requisitionService;
    this.clientErrors = clientErrors;
  }
  
  async createRequisition(request, reply) {
    // Handle request and delegate to service
    const result = await this.requisitionService.createRequisition(request.body);
    return reply.status(201).send(result);
  }
  
  // ... other methods
}
```

## Dependency Injection

The system uses Awilix for dependency injection, which helps with:
- Decoupling components
- Simplifying testing
- Managing service lifecycles

```javascript
// Example from src/container.js
diContainer.register({
  server: asClass(server, {
    lifetime: Lifetime.SINGLETON,
  }).singleton(),
  fastify: asValue(fastify, {
    lifetime: Lifetime.SINGLETON,
  }),
  // ... other registrations
});

diContainer.loadModules(
  [
    'src/app/handlers/controllers/**/*.js',
    'src/infra/repositories/**/*.js',
    'src/app/services/**/*.js',
  ],
  {
    formatName: 'camelCase',
    resolverOptions: {
      lifetime: Lifetime.SINGLETON,
      register: asClass,
    },
  }
);
```

## API Structure

The API is organized into public and private routes:

### Public Routes
- Authentication endpoints (login, token refresh, OTP verification)
- Health check endpoints

### Private Routes
- Protected endpoints requiring authentication
- Organized by resource (users, requisitions, suppliers, etc.)
- Versioned (e.g., `/v1/users`, `/v2/requisitions`)

## Architectural Strengths

1. **Clean Separation of Concerns**: The system clearly separates domain logic, application services, and infrastructure.
2. **Dependency Injection**: The use of Awilix provides good decoupling and testability.
3. **Domain-Driven Design**: The system follows DDD principles with clear domain entities and services.
4. **Validation**: Comprehensive validation using Zod ensures data integrity.
5. **Error Handling**: Custom error classes provide consistent error responses.

## Architectural Weaknesses

1. **Service Bloat**: Many services have too many dependencies and responsibilities.
2. **Inconsistent Repository Pattern**: The repository pattern is not consistently applied.
3. **Tight Coupling**: Despite DI, there's tight coupling between services.
4. **Missing Abstractions**: Some infrastructure concerns leak into the application layer.
5. **Incomplete Transaction Management**: Transaction handling is inconsistent across services.

## Recommendations

1. **Reduce Service Dependencies**: Refactor services to have fewer, more focused dependencies.
2. **Consistent Repository Pattern**: Ensure all data access goes through repositories.
3. **Introduce Service Facades**: Use facade pattern to simplify service interactions.
4. **Improve Transaction Management**: Implement a consistent transaction handling strategy.
5. **Extract Cross-Cutting Concerns**: Move logging, error handling, and validation to cross-cutting concerns.

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
