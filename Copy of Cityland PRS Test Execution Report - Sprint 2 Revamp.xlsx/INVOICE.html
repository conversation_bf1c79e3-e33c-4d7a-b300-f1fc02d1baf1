<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON>l;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:center;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="364980585C0" style="width:66px;" class="column-headers-background">A</th><th id="364980585C1" style="width:208px;" class="column-headers-background">B</th><th id="364980585C2" style="width:57px;" class="column-headers-background">C</th><th id="364980585C3" style="width:252px;" class="column-headers-background">D</th><th id="364980585C4" style="width:233px;" class="column-headers-background">E</th><th id="364980585C5" style="width:330px;" class="column-headers-background">F</th><th id="364980585C7" style="width:258px;" class="column-headers-background">H</th><th id="364980585C8" style="width:136px;" class="column-headers-background">I</th><th id="364980585C9" style="width:128px;" class="column-headers-background">J</th><th id="364980585C10" style="width:245px;" class="column-headers-background">K</th><th id="364980585C11" style="width:250px;" class="column-headers-background">L</th><th id="364980585C12" style="width:133px;" class="column-headers-background">M</th><th id="364980585C13" style="width:133px;" class="column-headers-background">N</th><th id="364980585C14" style="width:133px;" class="column-headers-background">O</th><th id="364980585C15" style="width:133px;" class="column-headers-background">P</th><th id="364980585C16" style="width:133px;" class="column-headers-background">Q</th><th id="364980585C17" style="width:133px;" class="column-headers-background">R</th><th id="364980585C18" style="width:133px;" class="column-headers-background">S</th><th id="364980585C19" style="width:133px;" class="column-headers-background">T</th><th id="364980585C20" style="width:133px;" class="column-headers-background">U</th><th id="364980585C21" style="width:133px;" class="column-headers-background">V</th><th id="364980585C22" style="width:133px;" class="column-headers-background">W</th><th id="364980585C23" style="width:133px;" class="column-headers-background">X</th><th id="364980585C24" style="width:133px;" class="column-headers-background">Y</th><th id="364980585C25" style="width:133px;" class="column-headers-background">Z</th></tr></thead><tbody><tr style="height: 42px"><th id="364980585R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0">Case Number</td><td class="s0">Feature Name</td><td class="s0">Priority</td><td class="s0">Test Case/Scenario</td><td class="s0">Pre-requisite</td><td class="s0">Test Steps</td><td class="s0">Expected Results</td><td class="s0">Actual Results</td><td class="s0">STATUS</td><td class="s0">Remarks</td><td class="s0">Defects</td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td><td class="s1"></td></tr><tr style="height: 19px"><th id="364980585R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s2" colspan="11">Invoice Entry Points and Invoice Creation</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="364980585R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">1304-011</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Critical</td><td class="s4">Verify behavior when  Purchase Order Number is selected in Purchase Order field</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Click &quot;Receive Invoice&quot; in Floating banner in DR or Select Actions modal<br>2. Select Purchase Order number in Purchase Order Field</td><td class="s4"><br>2. Section for the Delivery Reports Related to the Purchase Order should be displayed<br>                a) User may be able to select one or more Delivery Reports<br>                b) Once the Puchase Order Number is changed, should reset the selected Delivery Reports and Items Table<br>            </td><td class="s5" rowspan="2"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1n4b2OwZq4Kg0hay8qt8YEoOo9YHsJSSC9K4nLwv8_1c/edit?gid=0#gid=0">Verna_Sprint1_Test Result</a></td><td class="s6">Not Started</td><td class="s4">1. Cannot verify the ff due to 1 PO = 1 DR<br>a) User may be able to select one or more Delivery Reports</td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s4">1304-036</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Minor</td><td class="s4">Verify Default sorting of Delivery Reports table</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Check default sorting of tables</td><td class="s4">1. Should have a default sorting by Latest Delivered Date</td><td class="s6">Not Started</td><td class="s4">1 PO = 1 DR</td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s4">1304-039</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Minor</td><td class="s4">Verify Sorting of Delivery Report No.</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Click Sort of Delivery Report No <br>2. Click Sort for 2nd time<br>3. Click Sort for 3rd time</td><td class="s4">1. Should sorted Ascending -  0-9, A-Z<br>2. Should sorted Descending - 9-0, Z-A<br>3. Back to default</td><td class="s9" rowspan="5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1YkgPxBR4oTAmadw4zMFKWzQhSyNv6YQUttRu65TPerY/edit?gid=0#gid=0">Aira_Results</a></td><td class="s6">Not Started</td><td class="s4"></td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s4">1304-040</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Minor</td><td class="s4">Verify Sorting of Date delivered</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Click Sort of Date Delivered<br>2. Click Sort for 2nd time<br>3. Click Sort for 3rd time</td><td class="s4">1. Should sorted Ascending -Oldest - Latest<br>2. Should sorted Descending - Latest - Oldest<br>3. Back to default</td><td class="s6">Not Started</td><td class="s4"></td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s4">1304-041</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Minor</td><td class="s4">Verify Sorting of Status</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Click Sort of Status<br>2. Click Sort for 2nd time<br>3. Click Sort for 3rd time</td><td class="s4">1. Should sorted Ascending -  A-Z<br>2. Should sorted Descending - Z-A<br>3. Back to default</td><td class="s6">Not Started</td><td class="s4"></td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s4">1304-042</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Minor</td><td class="s4">Verify Rows displayed on Delivery Reports table</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Check count of rows displayed in Delivery Report Table</td><td class="s4">1.  Should display 10 Rows of Data per Page</td><td class="s6">Not Started</td><td class="s4"></td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s4">1304-043</td><td class="s4">Invoice Entry Points and Invoice Creation</td><td class="s4">Minor</td><td class="s4">Verify Paginations of Delivery Reports table</td><td class="s4">1. Logged in as:<br>-Requester<br>-Assigned Purchasing Staff<br>2. A Delivery Report has been created and Submitted</td><td class="s4">1. Click &quot;&lt;&quot;&quot;&gt;&quot; or next or previous page<br>2. Click page numbers (1,2,3,4..)</td><td class="s4">1. Should be able to display the next and previous page with correct total counts of entries<br>2. Should be able to display the selected page number with correct total counts of entries</td><td class="s6">Not Started</td><td class="s4"></td><td class="s7"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8"></td></tr><tr style="height: 19px"><th id="364980585R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s2" colspan="11">[INVOICE] Viewing of an Invoice</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="364980585R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s4">1305-001</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">Critical</td><td class="s4">Verify if user can access Invoice from Dashboard</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Navigate to Dashboard<br>2. Click Invoice Number <br>3. Validate that the invoice is accessible through dashboard</td><td class="s4">Invoice is accessible from dashboard</td><td class="s4">Inaccesible through dashboard</td><td class="s6">Not Started</td><td class="s4" rowspan="2">Not included in Sprint 1</td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="364980585R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s4">1305-002</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">Critical</td><td class="s4">Verify if user can access Invoice from Requisition Slip</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Navigate to Requisition Slip<br>2. Open “Related Documents” section<br>3. Validate that the invoice is accessible through Related Documents in the Requisition Slip<br>4. Click Invoice tab &gt; Click Invoice Number to View Invoice</td><td class="s4">Invoice is accessible through Related Documents in the Requisition Slip</td><td class="s4">No invoice tab in related documents</td><td class="s6">Not Started</td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 64px"><th id="364980585R57" style="height: 64px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 64px">58</div></th><td class="s4">1305-003</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">High</td><td class="s4">Verify if user can click the Invoice Number to open</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Click on the Invoice Number from either access point</td><td class="s4">Invoice page opens	</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 50px"><th id="364980585R58" style="height: 50px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 50px">59</div></th><td class="s4">1305-004</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">High</td><td class="s4">Verify Open Editable Invoice - Draft status</td><td class="s4">1. An Invoice has been save as draft</td><td class="s4">1. Open Invoice with status &quot;Draft&quot;</td><td class="s4">Editable Invoice Form is displayed	</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 64px"><th id="364980585R59" style="height: 64px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 64px">60</div></th><td class="s4">1305-005</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">Minor</td><td class="s4">Verify Field Validations on Draft        </td><td class="s4">1. An Invoice has been save as draft</td><td class="s4">1. Try submitting form with empty or invalid required fields</td><td class="s4">Proper validation messages are shown</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="364980585R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s4">1305-006</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">High</td><td class="s4">Verify Open View-Only Invoice</td><td class="s4">1. An Invoice has been created<br>2. Invoice is For IR Approval Status / submitted invoice</td><td class="s4">1. Open Invoice with status &quot;For IR Approval&quot;</td><td class="s4">Read-only Invoice view is displayed</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="364980585R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s4">1305-007</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">High</td><td class="s4">Verify Invoice Details section</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open any Invoice<br>2. Check presence of:<br>- PO Number<br>- Supplier Invoice No<br>- Issued Date<br>- Amount<br>- Attachments<br>- Notes</td><td class="s4">All fields in Invoice Details are visible</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 59px"><th id="364980585R62" style="height: 59px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 59px">63</div></th><td class="s4">1305-008</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">Minor</td><td class="s4">Verify Status section</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an Invoice<br>2. Observe Status Section</td><td class="s4">Status section is present and shows current status	</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 45px"><th id="364980585R63" style="height: 45px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 45px">64</div></th><td class="s4">1305-009</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s10">High</td><td class="s4">Verify Assigned To section</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an Invoice<br>2. Observe Assigned Status Section</td><td class="s4">Assigned To section is visible</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 126px"><th id="364980585R64" style="height: 126px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 126px">65</div></th><td class="s4">1305-010</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s13">Verify Items Table	</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Check table columns:<br>- DR No.<br>- Qty Ordered<br>- Qty Delivered<br>- Date Delivered</td><td class="s4">All specified columns are visible in Items Table	</td><td class="s13"></td><td class="s6">Not Started</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 72px"><th id="364980585R65" style="height: 72px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 72px">66</div></th><td class="s4">1305-011</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify Search Funtionality is working in Items Table</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Type an Item<br>3. Validate search result</td><td class="s4">The Items Table was filtered based on the search query</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 19px"><th id="364980585R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s4">1305-012</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify Sorting button is working in Items Table</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Click the sorting button (e.g. Delivery Report No., Item Name, Qty. Ordered, etc.,)<br>3. Observe sorting in Items Table</td><td class="s4">Items are sorted by ascending, descenting and by defult. </td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 74px"><th id="364980585R67" style="height: 74px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 74px">68</div></th><td class="s4">1305-013</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify if the items table creates another page when the content exceeds the limit</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Observe the items table exceeding the limit</td><td class="s4">Another page/s is created and the items table continue on another page</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 70px"><th id="364980585R68" style="height: 70px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 70px">69</div></th><td class="s4">1305-014</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify correct value for Supplier Invoice</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Observe Value for Supplier Invoice</td><td class="s4">Supplier Invoice should have accurate details</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 59px"><th id="364980585R69" style="height: 59px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 59px">70</div></th><td class="s4">1305-015</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify correct value of Amount in Invoice</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Observe Amount in Invoice</td><td class="s4">Amount in Invoice should have accurate details</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 59px"><th id="364980585R70" style="height: 59px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 59px">71</div></th><td class="s4">1305-016</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify correct value of Issues Date</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Observe Issues Date</td><td class="s4">Issues Date should have accurate details</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 59px"><th id="364980585R71" style="height: 59px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 59px">72</div></th><td class="s4">1305-017</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">High</td><td class="s4">Verify correct value for Purchase Order No</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an  Invoice<br>2. Observe Value for Purchase Order No.</td><td class="s4">Purchase Order No. should have accurate details</td><td class="s4"></td><td class="s6">Not Started</td><td class="s4"></td><td class="s11"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td><td class="s12"></td></tr><tr style="height: 59px"><th id="364980585R72" style="height: 59px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 59px">73</div></th><td class="s4">1305-018</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">Minor</td><td class="s13">Verify 60-Character Limit for Notes</td><td class="s4">1. An Invoice has been created</td><td class="s13">1. Try to enter 61-character.<br>2. Click Submit .</td><td class="s4">System prevents entry exceeding 60 characters.</td><td class="s13"></td><td class="s6">Not Started</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s4">1305-019</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">Minor</td><td class="s4">Verify notes are filtered correctly and display the correct user&#39;s name, date, and content</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Open an Invoice<br>2. Verify all notes are listed initially without filters applied<br>3. Apply a filter to show only notes added by the Requester.</td><td class="s4">Each note should display the following:<br>a) Display the full name of the user who added the note<br>b) Show the correct date and time in the expected format (e.g., DD MM YYYY, HH:MM AM/PM)<br>c) Contain the accurate note content as entered by the user.</td><td class="s13"></td><td class="s6">Not Started</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 86px"><th id="364980585R74" style="height: 86px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 86px">75</div></th><td class="s4">1305-020</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">Minor</td><td class="s4">Verify that entered Notes are displayed in the Check Notes section after Submit.</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Notes”.<br>3. Verify the badge is cleared.</td><td class="s4">1. “New Attachment” badge is displayed.<br>2. “New Attachment” badge is cleared when viewed.</td><td class="s13"></td><td class="s6">Not Started</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s4">1305-021</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">[INVOICE]</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;"> Viewing of an Invoice</span></td><td class="s13">Minor</td><td class="s4">Verify that attachment is displayed in the Check Notes section after Submit.</td><td class="s4">1. An Invoice has been created</td><td class="s4">1. Verify the presence of the “New Attachment” badge.<br>2. Click on “Check Attachment”.<br>3. Verify the badge is cleared.</td><td class="s4">1. “New Attachment” badge is displayed.<br>2. “New Attachment” badge is cleared when viewed.</td><td class="s13"></td><td class="s6">Not Started</td><td class="s13"></td><td class="s14"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s16" colspan="11">INVOICE - Adding of Invoice Tab in Related Documents</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td class="s4">PRS-001</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">Critical</td><td class="s4">Verify that the user can access RS Details via RS Number</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click any RS Number<br>2. Check redirection to the corresponding Requisition Slip Details Page</td><td class="s4">2. Should click a Requisition Slip Number to display the Requisition Slip Details</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s4">PRS-002</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">Critical</td><td class="s4">Verify that the user can access the Related Documents tab</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click the &quot;Related Documents&quot; tab<br>2. Related Documents Tab is clickable and accessible</td><td class="s4">2. Should click the Related Documents Tab</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s4">PRS-003</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">Medium</td><td class="s4">Verify that the Invoices Tab appears after Deliveries and before Payments</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Observe tab order in Related Documents<br>2. Validate if Invoices Tab is in between Deliveries Tab and Payments Tab</td><td class="s4">2. Should add the Invoices Tab after Deliveries Tab and before the Payments Tab</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s19"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s4">PRS-004</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify the presence of correct columns in Invoices Tab</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Open Invoices tab<br>2. Observe displayed columns</td><td class="s4">2. Should display the following Columns<br>    a. Invoice No - Document Number<br>    b. Supplier Invoice No - indicated in the created Invoice<br>    c. Supplier Invoice Issued Date - indicated in the created Invoice<br>    d. Supplier Invoice Amount - Total Amount of the Invoice<br>    e. Last Updated - Last Updated Date of the Invoice<br>    f. Status - Status of the Invoice</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s4">PRS-005</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify Format of Last Updated Date Column</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Open Invoices tab<br>2. Observe format of Last Updated Column</td><td class="s4"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">2. Last Updated - Last Updated Date of the Invoice<br>        FORMAT: </span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">DD MMM YYYY<br>        23 Jul 2024</span></td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s4">PRS-006</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify that Latest Updated Date appears first in default sorting</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Locate Last Updated Date in the Table<br>2. Observe the table</td><td class="s4">2. Should have a default sorting by the Latest Updated Date shown first</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s4">PRS-007</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">Medium</td><td class="s4">Verify that invoices are sorted by Latest Updated Date by default</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Open Invoices tab<br>2. Observe sorting order</td><td class="s4">2. Should be sorted by Latest Updated Date by default</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s4">PRS-008</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify sorting by Invoice No in ascending and descending order</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click on Invoice No column header&#39;s arrow buttons<br>2. Validate if table is sorted accordingly</td><td class="s4">2. Should be able to sort per Columns by<br>    a. Invoice No - 0-9, A-Z || 9-0, Z-A || Default</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s4">PRS-009</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify sorting by Supplier Invoice No</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click on Supplier Invoice No column header&#39;s arrow buttons<br>2. Validate if table is sorted accordingly</td><td class="s4">2. Should be able to sort per Columns by<br>    b. Supplier Invoice No - 0-9, A-Z || 9-0, Z-A || Default</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s4">PRS-010</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify sorting by Supplier Invoice Issued Date</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click on Supplier Invoice Issued Date column header&#39;s arrow buttons<br>2. Validate if table is sorted accordingly</td><td class="s4">2. Should be able to sort per Columns by<br>    c. Supplier Invoice Issued Date - Oldest Date-Latest Date || Latest Date-Oldest Date || Default</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R90" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">91</div></th><td class="s4">PRS-011</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify sorting by Supplier Invoice Amount</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click on Supplier Invoice Amount column header&#39;s arrow buttons<br>2. Validate if table is sorted accordingly</td><td class="s4">2. Should be able to sort per Columns by<br>    d. Supplier Invoice Amount - 0-9, 9-0 || Default</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s20"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R91" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">92</div></th><td class="s4">PRS-012</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify sorting by Last Updated Date</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click on Last Updated column header&#39;s arrow buttons<br>2. Validate if table is sorted accordingly</td><td class="s4">2. Should be able to sort per Columns by<br>    e. Last Updated - Oldest Date-Latest Date || Latest Date-Oldest Date || Default<br></td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R92" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">93</div></th><td class="s4">PRS-013</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify sorting by Invoice Status</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click on Status column header&#39;s arrow buttons<br>2. Validate if table is sorted accordingly</td><td class="s4">2. Should be able to sort per Columns by<br>    f. Status - A-Z || Z-A || Default</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R93" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">94</div></th><td class="s4">PRS-014</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify that Go back button is working</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click Go back Button<br>2. Validate if Go back button is working as intended</td><td class="s4">2. Go back button is working as intended</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R94" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">95</div></th><td class="s4">PRS-015</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">High</td><td class="s4">Verify that Pagination buttons are working</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click the Numbered Buttons and [Left &amp; Right] Arrow Buttons to navigate to different pages of the table<br>2. Validate if the Pagination buttons are working as intended</td><td class="s4">2. Pagination buttons are working as intended</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R95" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">96</div></th><td class="s4">PRS-016</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">Medium</td><td class="s4">Verify that table is responsive and adjusts with the number of invoices</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Observe the table<br>2. Check if the Table is responsive and adjust with the number of invoices in the table</td><td class="s4">2. Table is responsive</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s4">PRS-017</td><td class="s11">Adding of Invoice Tab in Related Documents</td><td class="s4">Medium</td><td class="s4">Verify that the pagination displays &quot;1 to X of X items&quot; properly below the table</td><td class="s4">1. Logged in as any user type except Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Observe pagination section at bottom of table<br>2. Check if it displays &quot;1 to X of X items&quot; properly below the table</td><td class="s4">2. &quot;1 to X of X items&quot; is displayed properly below the table</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s16" colspan="11">[INVOICE] Adding of Invoice Tab in Request History </td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s4">PRS-1501-001</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Critical</td><td class="s4">Verify Invoice Tab in Request History</td><td class="s4">1. User must not be logged in as Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click History icon in Requistion Slip</td><td class="s4">1. Should add the Invoices Tab after Deliveries Tab and before the Payments Tab</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s4">PRS-1501-002</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Critical</td><td class="s4">Verify Invoice Request History Columns</td><td class="s4">1. User must not be logged in as Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Validate Columns</td><td class="s4">2. Should display the following Columns<br>    a. Invoice No - Document Number<br>    b. Supplier Invoice No - indicated in the created Invoice<br>    c. Supplier Invoice Issued Date - indicated in the created Invoice<br>    d. Supplier Invoice Amount - Total Amount of the Invoice<br>    e. Last Updated - Last Updated Date of the Invoice<br>        FORMAT: DD MMM YYYY<br>        23 Jul 2024<br>    f. Status - Status of the Invoice</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s4">PRS-1501-003</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">High</td><td class="s4">Verify Last Updated Column&#39;s Format</td><td class="s4">1. User must not be logged in as Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Validate Last Updated Date Format</td><td class="s4">2 Last Updated - Last Updated Date of the Invoice<br>        FORMAT: DD MMM YYYY<br>        23 Jul 2024</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s19"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s4">PRS-1501-004</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">High</td><td class="s4">Verify Default Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Validate Default Sorting when Invoices Tab is clicked</td><td class="s4">2. Should have a default sorting by the Latest Updated Date shown first</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s4">PRS-1501-005</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify Invoice No Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Sort button for the Invoice No column<br>3. Click Sort button for a 2nd time<br>4. Click Sort button for the last time</td><td class="s4">2. Should be sorted in ascending order from 0-9, A-Z<br>3. Should be sorted in descending order from 9-0, Z-A<br>4. Should go back to default sorting</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s4">PRS-1501-006</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify Supplier Invoice No Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Sort button for the Supplier Invoice No column<br>3. Click Sort button for a 2nd time<br>4. Click Sort button for the last time</td><td class="s4">2. Should be sorted in ascending order from 0-9, A-Z<br>3. Should be sorted in descending order from 9-0, Z-A<br>4. Should go back to default sorting</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s4">PRS-1501-007</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify Supplier Invoice Issued Date Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Sort button for the Supplier Invoice Issued Date column<br>3. Click Sort button for a 2nd time<br>4. Click Sort button for the last time</td><td class="s4">2. Should be sorted in ascending order from Oldest Date-Latest Date<br>3. Should be sorted in descending order from Latest Date-Oldest Date<br>4. Should go back to default sorting</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s4">PRS-1501-008</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify Supplier Invoice Amount Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Sort button for the Supplier Invoice Amount column<br>3. Click Sort button for a 2nd time<br>4. Click Sort button for the last time</td><td class="s4">2. Should be sorted in ascending order from 0-9<br>3. Should be sorted in descending order from 9-0<br>4. Should go back to default sorting</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R106" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">107</div></th><td class="s4">PRS-1501-009</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify Last Updated Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Sort button for the Last Updated column<br>3. Click Sort button for a 2nd time<br>4. Click Sort button for the last time</td><td class="s4">2. Should be sorted in ascending order from Oldest Date-Latest Date<br>3. Should be sorted in descending order from Latest Date-Oldest Date<br>4. Should go back to default sorting</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R107" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">108</div></th><td class="s4">PRS-1501-010</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify Status Sorting</td><td class="s4">1. User must not be logged in as Root User <br>2. At least two Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Sort button for the Status column<br>3. Click Sort button for a 2nd time<br>4. Click Sort button for the last time</td><td class="s4">2. Should be sorted in ascending order from A-Z<br>3. Should be sorted in descending order from Z-A<br>4. Should go back to default sorting</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R108" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">109</div></th><td class="s4">PRS-1501-011</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">High</td><td class="s4">Verify Activities Displayed Per Invoice</td><td class="s4">1. User must not be logged in as Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Observe the activities listed under the Invoice</td><td class="s4">2. Should display all of the Activities done per Invoice<br>    a. Should display as a New Row of Data per Activity</td><td class="s17"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s20"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R109" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">110</div></th><td class="s4">PRS-1501-012</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">High</td><td class="s4">Verify Invoice No Redirection from Invoice Request History</td><td class="s4">1. User must not be logged in as Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Invoice No</td><td class="s4">2. Should redirect user to respective Invoice page</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R110" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">111</div></th><td class="s4">PRS-1501-013</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">Minor</td><td class="s4">Verify that Go back button is working</td><td class="s4">1. User must not be logged in as Root User <br>2. At least one Invoice has been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click Go back Button</td><td class="s4">2. Should redirect to RS page</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr><tr style="height: 19px"><th id="364980585R111" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">112</div></th><td class="s4">PRS-1501-014</td><td class="s11">Adding of Invoice Tab in Request History </td><td class="s4">High</td><td class="s4">Verify that Pagination buttons are working</td><td class="s4">1. User must not be logged in as Root User <br>2. At least eleven Invoices have been created or saved</td><td class="s4">1. Click Invoices Tab<br>2. Click &quot;&lt;&quot;&quot;&gt;&quot; or next or previous page<br>3. Click page numbers (1,2,3,4..)</td><td class="s4">2. Should go to the next or previous page<br>3. Should navigate to page clicked</td><td class="s18"></td><td class="s6">Not Started</td><td class="s18"></td><td class="s18"></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s15"></td><td class="s15"></td><td class="s15"></td><td class="s15"></td></tr></tbody></table></div>