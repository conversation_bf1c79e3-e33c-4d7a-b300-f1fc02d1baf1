#!/usr/bin/env node
/**
 * Enhanced Logging Test Script
 * 
 * This script tests the enhanced logging features by:
 * 1. Generating test requests with various headers and parameters
 * 2. Verifying that IP addresses and other metadata are properly captured in logs
 * 3. Checking log file contents and structure
 * 
 * Usage:
 *   node test-enhanced-logging.js
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const { execSync } = require('child_process');

// Configuration
const API_HOST = process.env.API_HOST || 'localhost';
const API_PORT = process.env.API_PORT || 4000;
const LOG_DIR = path.join(__dirname, '..', 'logs');
const TEST_ITERATIONS = 5;
const TEST_ENDPOINTS = [
  { path: '/v1/health', method: 'GET' },
  { path: '/v1/auth/login', method: 'POST', body: { username: '<EMAIL>', password: 'testpassword' } },
  { path: '/v1/requisitions', method: 'GET' },
  { path: '/v1/requisitions', method: 'POST', body: { title: 'Test Requisition', description: 'Test Description' } },
];

// Ensure log directory exists
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
  console.log(`Created log directory: ${LOG_DIR}`);
}

// Test headers with different IP scenarios
const TEST_HEADERS = [
  // Standard request
  {
    'User-Agent': 'LoggingTest/1.0',
    'Content-Type': 'application/json',
  },
  // Request with X-Forwarded-For
  {
    'User-Agent': 'LoggingTest/1.0',
    'Content-Type': 'application/json',
    'X-Forwarded-For': '*************',
  },
  // Request with multiple X-Forwarded-For values
  {
    'User-Agent': 'LoggingTest/1.0',
    'Content-Type': 'application/json',
    'X-Forwarded-For': '*************, *************, **********',
  },
  // Request with Cloudflare headers
  {
    'User-Agent': 'LoggingTest/1.0',
    'Content-Type': 'application/json',
    'X-Forwarded-For': '*************',
    'CF-IPCountry': 'US',
    'CF-Ray': '6a82e5a3db8e6a9e-SJC',
  },
  // Request with referer and origin
  {
    'User-Agent': 'LoggingTest/1.0',
    'Content-Type': 'application/json',
    'Referer': 'https://example.com/dashboard',
    'Origin': 'https://example.com',
  },
];

/**
 * Makes an HTTP request to the API
 * 
 * @param {Object} options - Request options
 * @returns {Promise<Object>} - Response data
 */
function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: API_HOST,
      port: API_PORT,
      path: options.path,
      method: options.method,
      headers: options.headers,
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const responseData = data.length > 0 ? JSON.parse(data) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData,
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message,
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

/**
 * Checks if a log file contains specific content
 * 
 * @param {string} logFile - Path to log file
 * @param {string} content - Content to search for
 * @returns {boolean} - Whether the content was found
 */
function logContains(logFile, content) {
  try {
    const logContent = fs.readFileSync(logFile, 'utf8');
    return logContent.includes(content);
  } catch (error) {
    console.error(`Error reading log file ${logFile}:`, error.message);
    return false;
  }
}

/**
 * Runs the logging tests
 */
async function runTests() {
  console.log('=== Enhanced Logging Test ===');
  console.log(`API: ${API_HOST}:${API_PORT}`);
  console.log(`Log Directory: ${LOG_DIR}`);
  console.log(`Test Iterations: ${TEST_ITERATIONS}`);
  console.log('');
  
  // Clear existing log files for clean test
  const appLogPath = path.join(LOG_DIR, 'app.log');
  const errorLogPath = path.join(LOG_DIR, 'error.log');
  
  if (fs.existsSync(appLogPath)) {
    fs.writeFileSync(appLogPath, '');
    console.log('Cleared app.log for testing');
  }
  
  if (fs.existsSync(errorLogPath)) {
    fs.writeFileSync(errorLogPath, '');
    console.log('Cleared error.log for testing');
  }
  
  // Run test requests
  console.log('Sending test requests...');
  
  for (let i = 0; i < TEST_ITERATIONS; i++) {
    for (const endpoint of TEST_ENDPOINTS) {
      const headerSet = TEST_HEADERS[i % TEST_HEADERS.length];
      
      try {
        console.log(`Request ${i+1}: ${endpoint.method} ${endpoint.path} with ${Object.keys(headerSet).join(', ')}`);
        
        const response = await makeRequest({
          path: endpoint.path,
          method: endpoint.method,
          headers: headerSet,
          body: endpoint.body,
        });
        
        console.log(`  Response: ${response.statusCode}`);
      } catch (error) {
        console.error(`  Error: ${error.message}`);
      }
    }
  }
  
  // Check log files
  console.log('\nChecking log files...');
  
  // Wait a moment for logs to be written
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Check app.log
  if (fs.existsSync(appLogPath)) {
    const appLogSize = fs.statSync(appLogPath).size;
    console.log(`app.log exists (${appLogSize} bytes)`);
    
    // Check for IP addresses in logs
    const containsIp = logContains(appLogPath, '"ip":');
    console.log(`  Contains IP addresses: ${containsIp ? 'Yes' : 'No'}`);
    
    // Check for forwarded IPs
    const containsForwardedIp = logContains(appLogPath, '"forwardedIp":') || 
                               logContains(appLogPath, '"x-forwarded-for":') ||
                               logContains(appLogPath, '"forwardedFor":');
    console.log(`  Contains forwarded IPs: ${containsForwardedIp ? 'Yes' : 'No'}`);
    
    // Check for user agent
    const containsUserAgent = logContains(appLogPath, '"userAgent":') || 
                             logContains(appLogPath, '"user-agent":');
    console.log(`  Contains User-Agent: ${containsUserAgent ? 'Yes' : 'No'}`);
    
    // Check for referer/origin
    const containsReferer = logContains(appLogPath, '"referer":') || 
                           logContains(appLogPath, '"origin":');
    console.log(`  Contains referer/origin: ${containsReferer ? 'Yes' : 'No'}`);
    
    // Check for request path
    const containsPath = logContains(appLogPath, '"path":');
    console.log(`  Contains request path: ${containsPath ? 'Yes' : 'No'}`);
    
    // Check for timestamp
    const containsTimestamp = logContains(appLogPath, '"timestamp":') || 
                             logContains(appLogPath, '"requestTime":');
    console.log(`  Contains timestamp: ${containsTimestamp ? 'Yes' : 'No'}`);
  } else {
    console.log('app.log does not exist');
  }
  
  // Test log rotation
  console.log('\nTesting log rotation...');
  try {
    const rotateScript = path.join(__dirname, 'rotate-logs.sh');
    execSync(`bash ${rotateScript}`, { stdio: 'inherit' });
    console.log('Log rotation script executed');
    
    // Check for rotated logs
    const rotatedLogs = fs.readdirSync(LOG_DIR).filter(file => file.endsWith('.gz'));
    console.log(`Found ${rotatedLogs.length} rotated log files`);
  } catch (error) {
    console.error('Error executing log rotation script:', error.message);
  }
  
  console.log('\nTest completed!');
}

// Run the tests
runTests().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
