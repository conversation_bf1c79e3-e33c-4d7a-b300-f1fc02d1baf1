<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s42{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#666666;text-align:left;color:#ffffff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s43{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#00b800;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s48{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#666666;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s38{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:center;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s46{background-color:#ffff00;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s37{background-color:#ffffff;text-align:left;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s47{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s49{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#4a86e8;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#00b800;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s36{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s39{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s50{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:bottom;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9d9d9;text-align:center;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s33{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s52{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s51{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ea9999;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s45{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:line-through;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s44{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;color:#000000;font-family:docs-Calibri,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s41{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00ff00;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00ff00;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s35{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:center;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#0b5394;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:12pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:center;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s40{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle no-grid" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1807361666C0" style="width:133px;" class="column-headers-background">A</th><th id="1807361666C1" style="width:198px;" class="column-headers-background">B</th><th id="1807361666C2" style="width:215px;" class="column-headers-background">C</th><th id="1807361666C3" style="width:146px;" class="column-headers-background">D</th><th id="1807361666C4" style="width:156px;" class="column-headers-background">E</th><th id="1807361666C5" style="width:95px;" class="column-headers-background">F</th><th id="1807361666C6" style="width:136px;" class="column-headers-background">G</th><th id="1807361666C7" style="width:166px;" class="column-headers-background">H</th><th id="1807361666C8" style="width:164px;" class="column-headers-background">I</th><th id="1807361666C9" style="width:100px;" class="column-headers-background">J</th><th id="1807361666C10" style="width:100px;" class="column-headers-background">K</th><th id="1807361666C11" style="width:132px;" class="column-headers-background">L</th><th id="1807361666C12" style="width:732px;" class="column-headers-background">M</th></tr></thead><tbody><tr style="height: 19px"><th id="1807361666R0" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">1</div></th><td class="s0">Bug</td><td class="s0">Module</td><td class="s0">Parent Backlog Ticket Number</td><td class="s0"># of TCs Blocked</td><td class="s0">QA Assigned</td><td class="s0">Status</td><td class="s0">QA Status on Dev<br>3/7</td><td class="s0">QA Status on Staging 2/26</td><td class="s0">QA Status - STG<br>2/28</td><td class="s0">Effort</td><td class="s0">FE/BE</td><td class="s0">Assigned</td><td class="s0">Remarks</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1807361666R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s1">CITYLANDPRS-837</td><td class="s2"></td><td class="s2"></td><td class="s3">8</td><td class="s2"></td><td class="s1">PO</td><td class="s4"></td><td class="s5">PASSED</td><td class="s2"></td><td class="s2">-</td><td class="s6"></td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1807361666R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s1">CITYLANDPRS-735</td><td class="s2"></td><td class="s2"></td><td class="s3">11</td><td class="s1">Kams</td><td class="s1">PO</td><td class="s4"></td><td class="s5">PASSED</td><td class="s8">FAILED</td><td class="s2">-</td><td class="s6"></td><td class="s6"></td><td class="s9">2/28 Kams: Ticket re-opened. Moved to critical as priority instead of blocker as it only fail if more than 4 digits company code</td></tr><tr style="height: 19px"><th id="1807361666R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-841">CITYLANDPRS-841</a></td><td class="s11">OFM Item</td><td class="s11">CITYLANDPRS-752(PB)</td><td class="s12">8</td><td class="s11">Kams</td><td class="s1">PO</td><td class="s5">PASSED</td><td class="s5">PASSED</td><td class="s5">PASSED</td><td class="s11">0.25</td><td class="s7">FE</td><td class="s7">Bryan Mattias</td><td class="s7">Feb 26  Hindi daw ma edit yung description. Hindi ma replicate ang issue. Matty to align with Kams. <br><br>need to add other fields in payload before passing to PUT /ofm/:id</td></tr><tr style="height: 19px"><th id="1807361666R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-860">CITYLANDPRS-860</a></td><td class="s11">Alt Approver</td><td class="s11">CITYLANDPRS-704(PB)</td><td class="s12">3</td><td class="s11">Kams</td><td class="s11">PO</td><td class="s5">PASSED</td><td class="s5">PASSED</td><td class="s11"></td><td class="s11">0</td><td class="s7">FE</td><td class="s7">Jan</td><td class="s9"><span style="font-size:11pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">Feb 26 5PM </span><span style="font-size:11pt;font-family:Poppins,Arial;color:#000000;">For QA testing na.<br><br>2/26 Kams: Retest passed on staging but failed on Dev probably due to recent build in Dev. Keeping ticket open as Staging will going to be a copy of Dev after build deployment<br><br>Feb 25 Na merge na, pag allow ng 1 day leave. Ready for QA na.<br><br>for verification</span></td></tr><tr style="height: 19px"><th id="1807361666R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-812">CITYLANDPRS-812</a></td><td class="s11">RS Approval</td><td class="s11">CITYLANDPRS-490(YT)</td><td class="s12">5</td><td class="s11">Cherry</td><td class="s14">For Code Review</td><td class="s8">FAILED</td><td class="s8">FAILED</td><td class="s11"></td><td class="s11">2</td><td class="s7">FE + BE</td><td class="s7">Francis/Von</td><td class="s15">March 7: [Cherry] still failed in Dev Environment. please see comment on ticket<br>Feb 28: [Cherry] Partially passed in staging and with some clarification with Ms. Irene. <br>Ms. Irene: should have Deliver To and Charge To disabled. Only Adding of Items Quantity and Notes are editable<br><br>Feb 27 11:50 Merged to dev na<br>Cherry: Partially passed in dev and some clarification with Ms. Irene. please see comments on ticket. Thank you<br><br>Feb 26 9PM<br><br>Feb  26 No blockers. TCD EOD Feb 26. Von will update ng 1PM if kaya by 2PM ang FE matapos.<br><br>Feb 25 TCD 2/25; no need for BE. Hindi natapos talaga more on missed AC. HIndi sya bug insted missed AC.</td></tr><tr style="height: 19px"><th id="1807361666R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-809">CITYLANDPRS-809</a></td><td class="s11">RS Approval</td><td class="s11">CITYLANDPRS-486(YT)</td><td class="s12">3</td><td class="s11">Cherry</td><td class="s11">PO</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11"></td><td class="s11">0.5</td><td class="s7">FE + BE</td><td class="s7">Francis/Sean</td><td class="s7">2/26: Cherry: Tested in both Dev and Staging</td></tr><tr style="height: 19px"><th id="1807361666R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-799">CITYLANDPRS-799</a></td><td class="s11">RS Dashboard</td><td class="s11">CITYLANDPRS-253(YT)</td><td class="s12">3</td><td class="s11">Gela</td><td class="s1">PO</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11">1</td><td class="s7">BE</td><td class="s7">Francis</td><td class="s17"><a target="_blank" href="https://gitlab.stratpoint.dev/cityland/prs/prs-backend/-/merge_requests/820">Feb 26 5PM For code review; inayos views table and nag create na new script just in case mag refresh ang views.<br><br>Feb 26 Current implem working pero historical hindi. For checking pa ng BE. Cleanup ng RS.<br>     &gt;&gt; Affected lahat ng modules once cleaned up, matataman ang testing<br>     &gt;&gt; Rerun script??? Francis checking sa migration; need refactor<br><br>MR Parked- cc: Carlo Ditan<br><br>MR: https://gitlab.stratpoint.dev/cityland/prs/prs-backend/-/merge_requests/807<br>have impact for checking search -&gt; PO/DR etc.<br><br>[Francis] Current implementation works, we just need to do a clean up - RS since we are using view and it does not apply to historicals etc.<br><br>2/26: [Francis] Done (need. to run a script after merge  npm run run:view-rs:migration)<br>https://gitlab.stratpoint.dev/cityland/prs/prs-backend/-/merge_requests/820<br><br>2/27: [Francis] FE Fix - MR Resubmitted - Merged in Dev</a></td></tr><tr style="height: 19px"><th id="1807361666R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-933">CITYLANDPRS-933</a></td><td class="s11">Resubmit Rejected PO</td><td class="s11">CITYLANDPRS-730(PB)</td><td class="s12" rowspan="2">20</td><td class="s11">Cherry</td><td class="s11">PO</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11"></td><td class="s11">0.5</td><td class="s7">FE + BE</td><td class="s7">Francis/Jan</td><td class="s18">Feb 26 Kapag na merge na pwede na re-test.<br>[Cherry] - Retested and Passed in Both Dev and Staging<br><br>Feb 25 Hindi nakasama for STG deployment; Gumawa na ng MR; Checking na lang for Carlo. Issue occured, operation timeout &gt;&gt; Francis is checking. <br><br>for chiriann confirmation - 2/25:  Flow for Resubmit Rejected PO is Passed but still displayed an error message Please see comments in the ticket. <br>Jan - Commit not yet included in the latest staging deployment. Currently for merging to staging</td></tr><tr style="height: 19px"><th id="1807361666R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s19"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-922">CITYLANDPRS-922</a></td><td class="s20">Reviewing of Purchase Order</td><td class="s20">CITYLANDPRS-718(PB)<br>CITYLANDPRS-705(PB)</td><td class="s8">Verna</td><td class="s21">To Do</td><td class="s8"></td><td class="s8"></td><td class="s8"></td><td class="s8">1</td><td class="s22">FE + BE</td><td class="s22"></td><td class="s22">Feb 26 Related sa cancellation ng PO. For further discussion.<br><br>after History - for figma checking</td></tr><tr style="height: 19px"><th id="1807361666R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-931">CITYLANDPRS-931</a></td><td class="s23">Adding of Canvass Items</td><td class="s11">CITYLANDPRS-511(PB)</td><td class="s12">Blocks flow going to PO, DR, PR</td><td class="s12">Aira</td><td class="s1">PO</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11">-</td><td class="s7">2/24</td><td class="s7"></td><td class="s7"></td></tr><tr style="height: 19px"><th id="1807361666R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s23">CITYLANDPRS-907</td><td class="s23">Payment Request</td><td class="s2"></td><td class="s3" rowspan="2">125</td><td class="s23">Cherry</td><td class="s1">PO</td><td class="s16">PASSED</td><td class="s5">PASSED</td><td class="s2"></td><td class="s2">-</td><td class="s6"></td><td class="s6"></td><td class="s7">Okay in Dev Env<br><br>Cherry 2/26: Tested in both dev and staging</td></tr><tr style="height: 19px"><th id="1807361666R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s1">CITYLANDPRS-908</td><td class="s2"></td><td class="s2"></td><td class="s2"></td><td class="s1">PO</td><td class="s16">PASSED</td><td class="s8">Pending Deployment</td><td class="s2"></td><td class="s2">-</td><td class="s6"></td><td class="s6"></td><td class="s24">2/27 Kams: To be retested on Staging once deployment is done from Dev to Staging<br>-Okay in Dev Env</td></tr><tr style="height: 19px"><th id="1807361666R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-947">CITYLANDPRS-947</a></td><td class="s11">Payment Request</td><td class="s11">CITYLANDPRS-733(PB)</td><td class="s12">11</td><td class="s12">Aira</td><td class="s1">PO</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11">0.5</td><td class="s7">BE</td><td class="s7">Francis</td><td class="s7">2/25 - Tested in Dev and Staging</td></tr><tr style="height: 19px"><th id="1807361666R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s25"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-948">CITYLANDPRS-948</a></td><td class="s12">Creation of Requisition Slip for Non-OFM Items</td><td class="s12"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">CITYLANDPRS-290</span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">(YT)</span></td><td class="s12"></td><td class="s12">Aira</td><td class="s11">PO</td><td class="s16">PASSED</td><td class="s8">FAILED</td><td class="s16">PASSED</td><td class="s11">0.5</td><td class="s7">FE</td><td class="s7">Ellis</td><td class="s24">2/28 Aira - Tested in Staging<br>2/27 Kams: To be retested on Staging once deployment is done from Dev to Staging<br>2/26 - Tested in Dev and Staging</td></tr><tr style="height: 19px"><th id="1807361666R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s25"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-949">CITYLANDPRS-949</a></td><td class="s12">Purchase Order</td><td class="s12">CITYLANDPRS-706</td><td class="s12"></td><td class="s12">Aira</td><td class="s11">PO</td><td class="s16">PASSED</td><td class="s8">Pending Deployment</td><td class="s16">PASSED</td><td class="s11">0.75</td><td class="s7">BE</td><td class="s7">Richard</td><td class="s24">3/03 Aira - Tested in Staging<br>2/28 Aira - Tested in Staging<br>2/27 Kams: To be retested on Staging once deployment is done from Dev to Staging<br>2/26 - Tested in Dev</td></tr><tr style="height: 19px"><th id="1807361666R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s25"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-949">CITYLANDPRS-920</a></td><td class="s12">Purchase Order</td><td class="s12">CITYLANDPRS-728</td><td class="s12">3</td><td class="s11">Cherry</td><td class="s1">PO</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11"></td><td class="s11"></td><td class="s7"></td><td class="s7"></td><td class="s7">2/25: Cherry: Tested in both Dev and Staging</td></tr><tr style="height: 19px"><th id="1807361666R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s1">CITYLANDPRS-954</td><td class="s1">Steelbars</td><td class="s7"></td><td class="s26">14</td><td class="s1">Kurt</td><td class="s27">For QA</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11"></td><td class="s11">0.5</td><td class="s7">BE</td><td class="s7">Keith</td><td class="s7">Feb 27 TCD Feb 27 - As per keith, okay in DEV ENV; for testing on STG ENV once deployed<br><br>Feb 26 5PM For dev estimate.<br><br>can&#39;t replicate the issue.</td></tr><tr style="height: 19px"><th id="1807361666R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-927">CITYLANDPRS-927</a></td><td class="s12">Manage Department and Project</td><td class="s11">CITYLANDPRS-304</td><td class="s28"></td><td class="s12">Cherry</td><td class="s27">For QA</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11"></td><td class="s11">0.5</td><td class="s7">FE</td><td class="s7">Kurt</td><td class="s7">Feb 27 9AM Okay in Dev as per checking. For re-rest.<br>Cherry: Tested passed in both Dev and Staging<br><br>Feb 26 5PM For dev estimate.</td></tr><tr style="height: 19px"><th id="1807361666R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-972">/CITYLANDPRS-972</a></td><td class="s11">RS Creation</td><td class="s11"></td><td class="s12"></td><td class="s11">Cherry</td><td class="s27">For QA</td><td class="s16">PASSED</td><td class="s16">PASSED</td><td class="s11"></td><td class="s11">0.5</td><td class="s7">BE</td><td class="s7">Francis</td><td class="s29"><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Feb 26 5PM For dev estimate.<br></span><span style="font-size:10pt;font-family:Poppins,Arial;font-weight:bold;color:#000000;">2/27: [Francis] Done - Merged to Dev<br></span><span style="font-size:10pt;font-family:Poppins,Arial;color:#000000;">Cherry: Retest passed in Dev<br><br>2/28: [CHerry] Retest Passed in Staging</span></td></tr><tr style="height: 19px"><th id="1807361666R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-890">CITYLANDPRS-890</a></td><td class="s11">RS -Adding of Items</td><td class="s11">CITYLANDPRS-695</td><td class="s11"></td><td class="s11">Cherry</td><td class="s11">To Do</td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td><td class="s11"></td></tr><tr style="height: 19px"><th id="1807361666R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s30"></td><td class="s31"></td><td class="s31"></td><td class="s32"></td><td class="s31"></td><td class="s30"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 19px"><th id="1807361666R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s30"></td><td class="s31"></td><td class="s31"></td><td class="s33"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td><td class="s31"></td></tr><tr style="height: 19px"><th id="1807361666R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s37"></td></tr><tr style="height: 19px"><th id="1807361666R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s34"></td><td class="s35"></td><td class="s35"></td><td class="s36"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s38"></td><td class="s39"></td><td class="s39"></td><td class="s40"></td><td class="s39"></td><td class="s39"></td><td class="s39"></td><td class="s39"></td><td class="s39"></td><td class="s39"></td><td class="s35"></td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s41"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-806">CITYLANDPRS-806</a></td><td class="s42"></td><td class="s43">PRS-009</td><td class="s44">Item Notes is not optional</td><td class="s45">Minor</td><td class="s45"></td><td class="s43">Task</td><td class="s43">Manage RS</td><td class="s45">Kurt</td><td class="s43"></td><td class="s46">3/19: For CR (Change type to Task)</td><td class="s35"></td><td class="s35"></td></tr><tr style="height: 19px"><th id="1807361666R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s47"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1168">CITYLANDPRS-1168</a></td><td class="s48"></td><td class="s49"></td><td class="s50">[QA BUGS][Creating a Canvass] No Enter Canvass in Select Action when RS is Partially Canvassed</td><td class="s51">High</td><td class="s49"></td><td class="s43">Task</td><td class="s43">Creating a Canvass</td><td class="s49"></td><td class="s49"></td><td class="s46">3/19: For CR (Change type to Task)</td><td class="s52"></td><td class="s35"></td></tr></tbody></table></div>