# Cityland PRS Deployment

This directory contains all the necessary configuration files and scripts to deploy the Cityland PRS (Purchase Requisition System) in a production environment.

## Architecture

The deployment architecture consists of the following components:

- **Backend API**: Node.js/Fastify application
- **Frontend**: React/Vite application
- **Database**: PostgreSQL
- **Cache**: Redis
- **Object Storage**: MinIO
- **Reverse Proxy**: Nginx
- **Secure Access**: Cloudflared (Cloudflare Tunnel)
- **Monitoring**: Prometheus and Grafana
- **Container Management**: Portainer
- **Database Management**: Adminer
- **Metrics Collection**: cAdvisor and Node Exporter

## Prerequisites

- Docker with Docker Compose plugin
- Git
- Bash shell
- Cloudflare account (for Cloudflared setup)

## Directory Structure

```
deployment/
├── .env                      # Environment variables for docker compose
├── backend.env               # Environment variables for the backend
├── frontend.env              # Environment variables for the frontend
├── compose.yml               # Docker Compose configuration
├── deploy.sh                 # Deployment script
├── cloudflared-guide.md      # Guide for setting up Cloudflare Tunnel
├── cloudflared/              # Cloudflared configuration
│   └── README.md             # Information about Cloudflared setup
├── nginx/                    # Nginx configuration
│   └── conf.d/               # Nginx site configurations
│       └── default.conf      # Default site configuration
├── prometheus/               # Prometheus configuration
│   └── prometheus.yml        # Prometheus configuration file
├── grafana/                  # Grafana configuration
│   └── datasources/          # Grafana datasource configurations
│       └── prometheus.yml    # Prometheus datasource configuration
└── tools/                    # Utility tools
    └── logs/                 # Log viewing utilities
        ├── README.md         # Log viewer documentation
        ├── logs              # Log viewer wrapper script
        └── pretty-logs.sh    # Log formatting script
```

## Configuration

Before deploying, you need to configure the environment variables:

1. Edit `.env` to set secure passwords for PostgreSQL, Redis, MinIO, and add the Cloudflare Tunnel token
2. Edit `backend.env` to configure the backend application
3. Edit `frontend.env` to configure the frontend application

## Deployment Steps

### 1. Initial Setup

```bash
# Clone the repository
git clone https://your-repository-url.git
cd your-repository

# Make the deployment script executable
chmod +x deployment/deploy.sh
chmod +x deployment/tools/logs/logs
chmod +x deployment/tools/logs/pretty-logs.sh
```

### 2. Set Up Cloudflare Tunnel

Follow the instructions in `cloudflared-guide.md` to set up your Cloudflare Tunnel.

### 3. Build and Start the Containers

```bash
cd deployment
./deploy.sh --build --up
```

### 4. Initialize the Database

```bash
./deploy.sh --init-db
```

### 5. Verify the Deployment

Access the application at your configured Cloudflare Tunnel domain (e.g., https://prs.yourdomain.com)

## Common Operations

### Starting the Application

```bash
./deploy.sh --up
```

### Stopping the Application

```bash
./deploy.sh --down
```

### Viewing Logs

```bash
# Using the deploy script
./deploy.sh --logs

# Using the enhanced log viewer
./tools/logs/logs [options] [container]
```

### Restarting the Application

```bash
./deploy.sh --restart
```

### Backing Up the Database

```bash
./deploy.sh --backup-db
```

### Restoring the Database

```bash
./deploy.sh --restore-db path/to/backup.sql
```

## Accessing Services

When using Cloudflared, services are accessed through your configured domains:

- **Main Application**: https://prs.yourdomain.com/
- **API**: https://prs.yourdomain.com/api/
- **MinIO Console**: https://prs.yourdomain.com/minio-console/
- **Portainer**: https://portainer.yourdomain.com/ (requires additional security)
- **Adminer**: https://adminer.yourdomain.com/ (requires additional security)
- **Grafana**: https://prs.yourdomain.com/grafana/
- **Prometheus**: https://prs.yourdomain.com/prometheus/

## Security Considerations

1. **Environment Variables**: Replace all default passwords in the `.env` files with strong, unique passwords
2. **Cloudflare Security**: Use Cloudflare Access policies to restrict access to management interfaces
3. **Portainer Security**: Never expose Portainer directly to the internet, even through Cloudflared, without proper security measures
4. **Access Control**: Restrict access to management interfaces (Portainer, Adminer, Grafana, Prometheus) using Cloudflare Access
5. **Regular Updates**: Keep all containers updated with the latest security patches
6. **WAF Protection**: Configure Cloudflare WAF (Web Application Firewall) rules for additional protection

## Monitoring

The deployment includes Prometheus and Grafana for monitoring:

1. Prometheus collects metrics from the application and infrastructure
2. Grafana provides dashboards to visualize these metrics
3. cAdvisor collects container metrics
4. Node Exporter collects host system metrics

## Log Management

The deployment includes enhanced log viewing tools:

1. Use the log viewer to see formatted, color-coded logs:
   ```bash
   ./tools/logs/logs -f backend
   ```

2. See the log viewer documentation for more options:
   ```bash
   ./tools/logs/logs --help
   ```

## Backup Strategy

1. **Database**: Use the `--backup-db` option to create regular backups
2. **Configuration**: Back up the entire `deployment` directory
3. **Uploaded Files**: MinIO data is persisted in a Docker volume, consider setting up MinIO replication for critical data

## Troubleshooting

### Container Issues

```bash
# Check container status
docker compose ps

# View container logs
docker compose logs [service_name]

# View formatted logs
./tools/logs/logs [container]
```

### Database Issues

```bash
# Connect to the database
docker compose exec postgres psql -U $POSTGRES_USER $POSTGRES_DB

# Check database status
docker compose exec postgres pg_isready
```

### Cloudflared Issues

```bash
# Check Cloudflared logs
docker compose logs cloudflared

# Verify Cloudflared connection
docker compose exec cloudflared cloudflared tunnel info
```

### Application Issues

```bash
# Check backend logs
docker compose logs backend
# Or use the enhanced log viewer
./tools/logs/logs backend

# Check frontend logs
docker compose logs frontend
# Or use the enhanced log viewer
./tools/logs/logs frontend
```
