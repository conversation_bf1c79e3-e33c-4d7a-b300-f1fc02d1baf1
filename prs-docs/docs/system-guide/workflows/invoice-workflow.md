# Invoice Report Workflow

This document outlines the complete workflow for the invoice report in the PRS system.

## Workflow Diagram

```mermaid
flowchart TD
  Start([Create Invoice Report]) -->|isDraft| S1[Draft]
  S1 -->|Edit IR| Start
  Start -->|Submitted| S2[Invoice Received]
```

## Status Definitions

| Status | Description |
|--------|-------------|
| DRAFT | Initial status when a invoice report is created but not submitted |
| INVOICE RECEIVED | Invoice report has been submitted |

## Detailed Workflow Steps

### 1. Invoice Report Creation

**Actor**: Purchasing Staff or Requester

**Actions**:

- Create a new invoice report for a purchase order
- Record invoice details from supploer
- Upload invoice from supplier
- Attach or relate delivery reports (optional)
- Save as draft or submit immediately

**Status Transitions**:

- New → DRAFT (if saved as draft)
- New → INVOICE_RECEIVED (if submitted immediately)

**Business Rules**:

- Invoice Reports can only be created for Purchase Orders with status FOR_DELIVERY
- Only the creator or assignee of the requisition can create a delivery report
- An Invoice Report can be created as a draft or submitted immediately
- IR Number is auto-generated
- IR Letter is auto-generated
- One Purchase Order can have multiple invoices
- Supplier Invoice No., Invoice Date, and Amount must be inputted
- Supplier Invoice supplier can be uploaded
- Can attach DR/s related to the PO (optional)

### 2. Invoice Report Submission

**Actor**: Purchasing Staff or Requester

**Actions**:

- Update a draft invoice report
- Add or update invoice details
- Submit the invoice report

**Status Transitions**:

- DRAFT → INVOICE_RECEIVED

**Business Rules**:

- Same as above
- All required fields must be filled

## Example Scenarios

### Scenario 1: Standard Invoicing Flow

1. Purchasing Staff creates a invoice report for a purchase order
2. Purchasing Staff records all invoice details from supplier
3. Purchasing Staff attaches related delivery reports
4. Purchasing Staff submits the invoice report

### Scenario 2: Partial Invoice

1. Purchasing Staff creates a invoice report for some items in a purchase order
2. Purchasing Staff submits the invoice report
3. Later, Purchasing Staff creates another invoice report for remaining items
4. Purchasing Staff submits the second invoice report
5. Continue with standard flow


## Common Issues and Solutions

### Issue 1: Cannot Create Invoice Report

**Cause**: User is not authorized or purchase order is not in FOR_DELIVERY status.

**Solution**:

- Check if the user is the creator or assignee of the requisition
- Ensure the purchase order is in FOR_DELIVERY status
