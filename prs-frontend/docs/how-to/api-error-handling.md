# API Error Handling Guide

This guide explains how to use the enhanced API error handling features in the PRS Frontend application.

## Overview

The API client has been refactored to provide more robust error handling capabilities, including:

- Standardized error types
- User-friendly error messages
- Automatic retry for network errors
- Detailed error logging
- Consistent error formatting

## Error Types

The following error types are defined in the API client:

```javascript
export const API_ERROR_TYPES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION: 'VALIDATION',
  SERVER_ERROR: 'SERVER_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNKNOWN: 'UNKNOWN',
};
```

These error types are mapped to HTTP status codes automatically:

```javascript
const ERROR_STATUS_MAP = {
  400: API_ERROR_TYPES.VALIDATION,
  401: API_ERROR_TYPES.UNAUTHORIZED,
  403: API_ERROR_TYPES.FORBIDDEN,
  404: API_ERROR_TYPES.NOT_FOUND,
  422: API_ERROR_TYPES.VALIDATION,
  500: API_ERROR_TYPES.SERVER_ERROR,
  502: API_ERROR_TYPES.SERVER_ERROR,
  503: API_ERROR_TYPES.SERVER_ERROR,
  504: API_ERROR_TYPES.SERVER_ERROR,
};
```

## Basic Usage

### Making API Requests

The API client usage remains the same:

```javascript
import { api } from '@lib/apiClient';

// GET request
const getData = async () => {
  try {
    const data = await api.get('/some-endpoint');
    return data;
  } catch (error) {
    // Handle error
  }
};

// POST request
const createData = async (payload) => {
  try {
    const response = await api.post('/some-endpoint', payload);
    return response;
  } catch (error) {
    // Handle error
  }
};
```

### Handling Errors

The API client now provides a `getErrorMessage` function to get user-friendly error messages:

```javascript
import { api, getErrorMessage } from '@lib/apiClient';

try {
  const data = await api.get('/some-endpoint');
  // Process data
} catch (error) {
  // Get user-friendly error message
  const message = getErrorMessage(error);

  // Display message to user (e.g., using toast notification)
  toast.error(message);

  // Log detailed error information
  console.error('Error details:', error.formattedError);
}
```

## Advanced Usage

### Accessing Error Details

Each error caught from the API client now includes a `formattedError` property with detailed information:

```javascript
try {
  const data = await api.get('/some-endpoint');
} catch (error) {
  const {
    type,          // Error type (e.g., 'VALIDATION')
    message,       // User-friendly message
    statusCode,    // HTTP status code (if available)
    data,          // Response data (if available)
    originalError  // Original error object
  } = error.formattedError;

  // Handle specific error types
  if (type === API_ERROR_TYPES.VALIDATION) {
    // Handle validation errors
    const validationErrors = data.errors;
    // ...
  }
}
```

### Handling Specific Error Types

You can handle specific error types based on the `type` property:

```javascript
try {
  const data = await api.get('/some-endpoint');
} catch (error) {
  const { type } = error.formattedError;

  switch (type) {
    case API_ERROR_TYPES.UNAUTHORIZED:
      // Handle unauthorized error (user is logged out)
      break;
    case API_ERROR_TYPES.FORBIDDEN:
      // Handle forbidden error (user doesn't have permission)
      break;
    case API_ERROR_TYPES.VALIDATION:
      // Handle validation errors
      break;
    case API_ERROR_TYPES.NETWORK_ERROR:
      // Handle network errors
      break;
    default:
      // Handle other errors
      break;
  }
}
```

## Automatic Retry

The API client automatically retries requests that fail due to network errors or timeouts:

- Maximum of 3 retries
- Exponential backoff delay between retries (1s, 2s, 4s)
- Detailed logging of retry attempts

This behavior is handled automatically and doesn't require any additional code.

## Best Practices

1. **Always use try/catch**: Always wrap API calls in try/catch blocks to handle errors properly.

2. **Use getErrorMessage**: Use the `getErrorMessage` function to get user-friendly error messages for display.

3. **Log detailed errors**: Log the `error.formattedError` object for debugging purposes.

4. **Handle specific error types**: Handle specific error types based on your application's needs.

5. **Centralize error handling**: Consider creating a custom hook for API calls that includes standardized error handling:

```javascript
// useApi.js
import { api, getErrorMessage, API_ERROR_TYPES } from '@lib/apiClient';
import { toast } from 'react-toastify';

export const useApi = () => {
  const handleError = (error) => {
    const message = getErrorMessage(error);
    toast.error(message);

    // Handle specific error types
    if (error.formattedError?.type === API_ERROR_TYPES.UNAUTHORIZED) {
      // Redirect to login page
    }

    return error.formattedError;
  };

  const get = async (url, config) => {
    try {
      return await api.get(url, config);
    } catch (error) {
      return handleError(error);
    }
  };

  const post = async (url, data, config) => {
    try {
      return await api.post(url, data, config);
    } catch (error) {
      return handleError(error);
    }
  };

  // Add other methods as needed

  return { get, post };
};
```

## Conclusion

The enhanced API error handling provides a more robust and user-friendly way to handle errors in the application. By using the provided utilities and following the best practices, you can ensure a consistent and reliable error handling experience throughout the application.
