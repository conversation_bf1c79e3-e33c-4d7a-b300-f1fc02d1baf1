<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s9{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#ff0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d0e0e3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6fa8dc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fff2cc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff9900;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9d2e9;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="135784448C0" style="width:103px;" class="column-headers-background">A</th><th id="135784448C1" style="width:167px;" class="column-headers-background">B</th><th id="135784448C2" style="width:282px;" class="column-headers-background">C</th><th id="135784448C3" style="width:233px;" class="column-headers-background">D</th><th id="135784448C4" style="width:233px;" class="column-headers-background">E</th><th id="135784448C5" style="width:330px;" class="column-headers-background">F</th><th id="135784448C7" style="width:144px;" class="column-headers-background">H</th><th id="135784448C12" style="width:126px;" class="column-headers-background">M</th><th id="135784448C13" style="width:126px;" class="column-headers-background">N</th><th id="135784448C16" style="width:78px;" class="column-headers-background">Q</th><th id="135784448C17" style="width:125px;" class="column-headers-background">R</th></tr></thead><tbody><tr style="height: 71px"><th id="135784448R0" style="height: 71px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 71px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s2">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Expected Results</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="135784448R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="11">PRS-026 - [Payment Request] - Scenario per Terms of the Supplier, Entering of Payment Request per Purchase Order, Viewing of Payment Request, Payment Request Approval - OFM Request, Payment Request Approval - Non-OFM Request, Payment Request Approval - Transfer of Materials, Payment Submission to Accounting</td></tr><tr style="height: 19px"><th id="135784448R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify NET 15 Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. NET 15 is selected as term</td><td class="s4">1. Check for Terms<br>2. Check note under terms<br>3. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be NET 15<br>2. Text under Terms is written as<br>&quot;Payment must be sent 15 days after the issued Invoice Date&quot;<br>3. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s7">Failed</td><td class="s8" rowspan="152"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1FjrNBJHhKH_mv1aYaKE_d9BDKKqRbYdEIrRdor-RPzw/edit?gid=0#gid=0">https://docs.google.com/spreadsheets/d/1FjrNBJHhKH_mv1aYaKE_d9BDKKqRbYdEIrRdor-RPzw/edit?gid=0#gid=0</a></td><td class="s9" rowspan="2">3/10: No text under Terms</td><td class="s10" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1115">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1115</a></td></tr><tr style="height: 19px"><th id="135784448R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify NET 30 Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. NET 30 is selected as term</td><td class="s4">1. Check for Terms<br>2. Check note under terms<br>3. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be NET 30<br>2. Text under Terms is written as<br>&quot;Payment must be sent 30 days after the issued Invoice Date&quot;<br>3. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR<br></td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify Cash in Advance (CIA) Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. Cash in Advance (CIA) is selected as term</td><td class="s4">1. Check for Terms<br>2. Click on employee dropdown<br>3. Select employee<br>4. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be CIA<br>2. Able to select employee name<br>3. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s7">Failed</td><td class="s9" rowspan="3">3/10: No employee dropdown</td><td class="s10" rowspan="3"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1116">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1116</a></td></tr><tr style="height: 19px"><th id="135784448R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s11">Verify Cash in Advance (CIA) Terms - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. Cash in Advance (CIA) is selected as term</td><td class="s4">1. Check for Terms<br>2. Click on employee dropdown<br>3. Select Root User or Admin user type <br></td><td class="s4">1. Must not be able to select Root User or Admin user types as a selection in the dropdown<br></td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify Cash on Delivery (COD) Terms </td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. Cash on Delivery (COD) is selected as term</td><td class="s4">1. Check for Terms<br>2. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be COD<br>2. Payment is done upon delivery of goods<br>3. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 10% DP, Balance upon delivery Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 10% DP, Balance upon delivery is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 10% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 10%<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 10% DP: 1,000 downpayment, 9,000 balance<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 20% DP, Balance upon delivery Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 20% DP, Balance upon delivery is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 20% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 20%<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 20% DP: 2,000 downpayment, 8,000 balance<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 30% DP, Balance upon delivery Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 30% DP, Balance upon delivery is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 30% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 30%<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 30% DP: 3,000 downpayment, 7,000 balance<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 50% DP, Balance upon delivery Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 50% DP, Balance upon delivery is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 50% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 50%<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 50% DP: 5,000 downpayment, 5,000 balance<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 113px"><th id="135784448R11" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">12</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 80% DP, Balance upon delivery Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 80% DP, Balance upon delivery is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 80% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 80%<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 80% DP: 8,000 downpayment, 2,000 balance<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 10% DP, PB, 10% RETENTION Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 10% DP, PB, 10% RETENTION is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check for Percent Retention<br>5. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 10% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 10%<br>3. Percent Retention(Progress Billing) amount should be 10% of the remaining invoice amount<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 10% DP: 1,000 downpayment, 9,000 balance. Progress billing should release 900 per 10% increment.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 20% DP, PB, 10% RETENTION Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 20% DP, PB, 10% RETENTION is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check for Percent Retention<br>5. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 20% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 10%<br>3. Percent Retention(Progress Billing) amount should be 10% of the remaining invoice amount<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 20% DP: 2,000 downpayment, 8,000 balance. Progress billing should release 800 per 10% increment.<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s12"></td><td class="s5">Scenario per Terms of the Supplier</td><td class="s4">Verify 30% DP, PB, 10% RETENTION Terms</td><td class="s6">High</td><td class="s4">1. Should have created an RS with Item and Approved<br>2. Should have selected the Terms in Canvassing of the Items and approved<br>3. Should have purchase order approved<br>4. Should have delivery receipt approved<br>5. Should be in the Payment Request screen<br>6. 30% DP, PB, 10% RETENTION is selected as term</td><td class="s4">1. Check for Terms<br>2. Check remaining balance<br>3. Check for Deposit<br>4. Check for Percent Retention<br>5. Check computation if correct on the lower right of the page</td><td class="s4">1. Terms must be 30% DP, Balance upon delivery<br>2. Deposit(downpayment) should be 10%<br>3. Percent Retention(Progress Billing) amount should be 10% of the remaining invoice amount<br>3. Remaining balance = invoice amount - downpayment amount<br><br>Sample: 30% DP: 3,000 downpayment, 7,000 balance. Progress billing should release 700 per 10% increment.<br><br>4. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Redirection to Create Payment Request Page</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click on an RS Number from the list.<br>2. Click on Actions Button<br>3. Click Create Payment Request</td><td class="s4">1. Able to click RS number<br>2. Able to See RS Details<br>3. Able to Click Actions Button<br>4. Able to Click Create Payment Request<br>5. Able to transition to Payment Request Page</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s16"></td><td class="s17">Entering of Payment Request per Purchase Order</td><td class="s17">Verify Purchase Order Details Section</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s17">1. Check Purchase Order Number dropdown.<br>2. Select a Purchase Order.</td><td class="s17">1. Dropdown should list POs with &#39;Fully Delivered&#39; or &#39;Fully Delivered w/ Return&#39; status.<br><br>2. Once selected, the fields (Supplier, Delivery Address, Terms, Deposit %, Warranty) should be auto-filled.<br><br>3. Attached invoice should be retrieved.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s16"></td><td class="s17">Entering of Payment Request per Purchase Order</td><td class="s17">Verify Invoice Viewing</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s17">1. Click &#39;View Invoice&#39; button.</td><td class="s17">1. A modal should open with invoice details and file should open in a new tab.<br><br>Invoice Details:<br> a) Invoice File that should open on a New Tab<br> b) Invoice No<br> c) Invoice Date<br> d) Total Sales<br> e) VAT Amount<br>  f) VAT Exempt Sales Amount<br> g) Zero Rated Sales Amount</td><td class="s7">Failed</td><td class="s9">3/3: Date is in incorrect format</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1013">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1013</a></td></tr><tr style="height: 19px"><th id="135784448R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s16"></td><td class="s17">Entering of Payment Request per Purchase Order</td><td class="s17">Verify Request Details Section</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s17">1. Check &#39;Payable To&#39; field.<br>2. Check &#39;Charge To&#39; field.<br>3. Select a &#39;Payable Date&#39;.<br>4. Apply a discount (Fixed Amount or Percentage).</td><td class="s17">1. Payable To field is autofilled once PO Number was retrieved<br>2. Charge to should be autofilled<br>3. Payable date should be a date picker and only dates from now onwards can be picked.<br>4. Discount should be calculated accordingly.<br> i) If Fixed Amount, this should be removed from the Current Total of the Purchase Order<br> ii) If Percentage, should get the Certain percentage of the Total, to be subtracted to it</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Additional Fees Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click &#39;Additional Fees&#39; section.<br>2. Enter values in the fields (Withholding Tax Reduction, Delivery Fee, Extra Charges, Tip).</td><td class="s4">1. Should display the following fields<br>     i) Withholding Tax Reduction<br>     ii) Delivery Fee<br>     iii) Extra Charges<br>     iv) Tip<br>2. Able to input in fields<br>3. Only accepts numbers in fields</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s11">Verify Additional Fees Section - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click &#39;Additional Fees&#39; section.<br>2. Enter letters in the fields (Withholding Tax Reduction, Delivery Fee, Extra Charges, Tip).</td><td class="s4">1. Should not allow to enter letters in field</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Attachments and Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click Select Attachments<br>2. Upload a file (PDF, DOC, JPG, JPEG, PNG, CSV, Excel).<br>3. Ensure file size is within 25MB limit.<br>4. Add a note within 100 characters.<br>5. Click Submit.</td><td class="s4">1. Able to click select attachments<br>2. Able to upload file with the file types allowed and 25mb limit file size<br>3. Able to input 100 characters<br>4. Able to input alphanumeric and special characters<br>5. Attachments should be uploaded successfully.<br>6. Notes should be saved</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s11">Verify Attachments and Notes - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click Select Attachments<br>2. Upload a file different from the list (PDF, DOC, JPG, JPEG, PNG, CSV, Excel).<br>3. Upload a file greater than 25MB.<br>4. Add a note within 101 characters.<br>5. Add an emoji in the notes<br>6. Click Submit.</td><td class="s4">1. Should not allow other files to be uploaded other than the said file types<br>2. Should not allow to upload file greater than 25mb<br>3. Notes limit should be 100 characters only<br>4. Should not allow emoji in the notes</td><td class="s7">Failed</td><td class="s9">3/3: Emojis can be submitted in Notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Items Table Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Check search functionality for Item Name.<br>2. Verify listed items match selected PO.<br>3. Check columns for Item, Account Code, Delivered Qty, Amount.</td><td class="s4">1. Able to use search bar<br>2. Correct columns are displayed<br>3. Items should be listed correctly.<br>4. Amount should reflect the canvassed price.<br><br>Note: Canvassed Price is the Price of the Item which the Discounts has been deducted from the Original Price of the Item</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Items Table Section - Clear Button</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Input in search bar<br>2. Click clear button</td><td class="s4">1. Able to clear search</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Items Table Section - Pagination</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click page numbers</td><td class="s4">1. Max number of items display per page is 10<br>2. Able to click page numbers<br>3. Displays different item per different page numbers</td><td class="s19">Blocked</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Total Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Check displayed Sub-Total, Discounts, and Total Amount.</td><td class="s4">1. Totals should be calculated accurately.</td><td class="s7">Failed</td><td class="s9">3/3: Price has more than 2 decimals</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td></tr><tr style="height: 19px"><th id="135784448R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Cancel Button Functionality</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click &#39;Cancel&#39;.<br>2. Confirm cancellation in the modal.</td><td class="s4">1. Pop up modal for confirmation of cancellation should display<br>2. Able to cancel PR</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Save Draft Button Functionality</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click &#39;Save Draft&#39;.<br>2. Confirm draft save in the modal.</td><td class="s4">1. A draft VR Number should be assigned.<br>2. Payment Request should be saved as a draft.<br><br>Sample: VR-&lt;company_code&gt;&lt;number&gt;</td><td class="s7">Failed</td><td class="s9">3/11: Incorrect pop-up for saving Payment Request draft</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1125">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1125</a></td></tr><tr style="height: 19px"><th id="135784448R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s12"></td><td class="s15">Entering of Payment Request per Purchase Order</td><td class="s4">Verify Submit Button Functionality</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Status per Delivery Receipt as Fully Delivered or Fully Delivered w/ Return<br>5. Must be the assigned Purchasing Staff</td><td class="s4">1. Click &#39;Submit&#39;.<br>2. Confirm submission in the modal.</td><td class="s4">1. Able to click submit<br>2. Confirmation modal should display<br>3. Payment Request should be submitted for approval.<br>4. Status should update to &#39;For PR Approval&#39;.<br>5. Save draft is not a pre-requisite to submit</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Redirection to Payment Request Details</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click on an RS Number from the list.<br>2. Click on Related Documents<br>3. Click Payments Tab<br>4. Click a PR Number</td><td class="s4">1. Should display the RS Details.<br>2. Should display related documents of the RS<br>3. Should display a table of Payment Requests<br>4. Should display PR details including PR Number and Request Number</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4"> Verify that clicking &quot;Visit Main RS&quot; redirects back to the RS Main Page.</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click &quot;Visit Main RS&quot;</td><td class="s4">1. Should navigate back to the RS Main Page.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify request details have non editable fields</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click on an RS Number from the list.<br>2. Click on Related Documents<br>3. Click Payments Tab<br>4. Click a PR Number<br>5 . View the Payment Request page.</td><td class="s4">1. Non-editable fields should be displayed for:<br>- Payable Date<br>- Payable To<br>- Charge To<br>- Discount</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that clicking the &quot;Check Attachments&quot; button displays a list of uploaded files.</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click the &quot;Check Attachments&quot; button</td><td class="s4">1. A modal should open showing a list of uploaded files.</td><td class="s7">Failed</td><td class="s9">3/11: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that users can search for file names in the Attachments modal.</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Enter a file name in the search bar inside the Attachments modal.</td><td class="s4">1. Should filter and display matching files.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Clear Button in Attachments modal</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Enter a file name<br>2. Click search<br>3. Click Clear</td><td class="s4">1. Should clear the search bar and reset searched to default view</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that clicking a file name opens the attachment in a new tab.</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click on a file name in the Attachments modal</td><td class="s4">1. The file should open in a new tab</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that users can remove an attachment with confirmation</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click the &quot;X&quot; icon next to an attachment.<br>2. Confirm the deletion in the confirmation modal.</td><td class="s4">1. The attachment should be removed from the Payment Request.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that clicking &quot;Check Notes&quot; displays all notes and reasons for request approval.</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click the &quot;Check Notes&quot; button</td><td class="s4">1 . Should display all Notes and the Reason added during the Request Approval</td><td class="s7">Failed</td><td class="s9">3/11: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that users can upload attachments within size and format limits.</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click the &quot;Upload Attachment&quot; button.<br>2. Select a file <br>3. Click &quot;Submit&quot;.</td><td class="s4">1. Able to upload file with the file types allowed and 25mb limit file size<br><br>Allowed formats: PDF, DOC, JPG, JPEG, PNG, CSV, Excel</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s11">Verify that users can upload attachments within size and format limits. - negative scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click the &quot;Upload Attachment&quot; button.<br>2. Select a file that is not an allowed format<br>3. Select a file that has more than 25mb file size <br>4. Click &quot;Submit&quot;.</td><td class="s4">1. Not able to upload file that is different from the allowed formats<br>2. Not able to upload more than 25mb file size</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that users can add a note with valid characters.</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Enter an alphanumeric text in note <br>2. Click &quot;Submit&quot;.</td><td class="s4">1. Able to input alphanumeric and special characters, No Emojis allowed<br>2. Max 100 characters<br>3. Note should be saved successfully.</td><td class="s7">Failed</td><td class="s9">3/11: Character count remains at 0/60. Maximum of only 60 characters</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014</a></td></tr><tr style="height: 19px"><th id="135784448R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s12"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that users can filter date in Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click on Select date in From<br>2. Click on Select date in To</td><td class="s4">1. Filtering Date should work<br>2. Displays the correct notes on the date filtered</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s11">Verify that users can add a note with valid characters. - Negative scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Enter an emoji in note <br>2. Enter more than 100 characters<br>3. Click &quot;Submit&quot;.</td><td class="s4">1. Emoji input should not be saved<br>2. Not able to input more than 100 characters</td><td class="s7">Failed</td><td class="s9">3/11: Emojis are accepted in notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify that Purchase Order details are displayed correctly.</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. View the Purchase Order section.</td><td class="s4">1. Should display the following:<br><br>- PO Number<br>- Supplier Details (clicking should open a modal)<br>- Delivery Address<br>- Terms<br>- Deposit<br>- Warranty<br>- Invoice Details (clicking should open a modal with invoice info)</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Supplier Details Button</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click Supplier Details button<br>2. Click Close window button</td><td class="s4">1. Able to open supplier details modal, displaying the following:<br>- Supplier<br>- Address<br>- Citizenship Code<br>- Nature of Income<br>- TIN<br>- Individual/Corporation<br>- Contact Person<br>- Contact Number<br>- Lines of Business<br>- Status<br>- Attachments<br>- Notes<br><br>2. Able to close modal</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Invoice Details button</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click Invoice Details button<br>2. Click Close window button</td><td class="s4">1. Able to open Invoice Details modals, displaying the following:<br>- Invoice File<br>- Invoice No.<br>- Invoice Date<br>- Total Sales<br>- VAT Amount<br>- VAT exempt sales amount<br>- Zero Rated Sales Amount<br><br>2. Able to close modal</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Invoice File opening in different tab on Invoice Details</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click Invoice Details button<br>2. Click Invoice file</td><td class="s4">1. File opening in another tab</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Items Table Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Check search functionality for Item Name.<br>2. Verify listed items match selected PO.<br>3. Check columns for Item, Account Code, Delivered Qty, Amount.</td><td class="s4">1. Able to use search bar<br>2. Correct columns are displayed<br>3. Items should be listed correctly.<br>4. Amount should reflect the canvassed price.<br><br>Note: Canvassed Price is the Price of the Item which the Discounts has been deducted from the Original Price of the Item</td><td class="s7">Failed</td><td class="s9">3/3: Price has more than 2 decimals</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td></tr><tr style="height: 19px"><th id="135784448R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Items Table Section - Clear Button</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Input in search bar<br>2. Click clear button</td><td class="s4">1. Able to clear search</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Items Table Section - Pagination</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. Click page numbers</td><td class="s4">1. Max number of items display per page is 10<br>2. Able to click page numbers<br>3. Displays different item per different page numbers</td><td class="s19">Blocked</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">Related bug: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1055">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1055</a></td></tr><tr style="height: 19px"><th id="135784448R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s4"></td><td class="s20">Viewing of Payment Request</td><td class="s4">Verify Sub-total, Discounts, and Total are being correctly calculated</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval</td><td class="s4">1. View the Total Section</td><td class="s4">1. Calculations should be correct<br>    a. Sub-total should be computed by<br>        i. Items Original Price + Additional Fees indicated<br>    b. Discount should be computed by<br>        i. Items Original Price - indicated Discounts per Item<br>    c. Total should be computed by<br>        i. Items Discounted Price + Additional Fees indicated<br>           i) Terms should be based for this<br><br>Note: Discounts can be found from Canvassing and PR,<br>Additional Fees can be found in PR</td><td class="s7">Failed</td><td class="s9">3/3: Price has more than 2 decimals</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td></tr><tr style="height: 19px"><th id="135784448R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Access of Approvers in Payment Request</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Log in as an Approver.<br>2. Click &quot;For My Approval/Assigned&quot; tab. <br>3. Verify the Payment Request is listed.<br>4. Open the Payment Request by clicking on the Ref No.<br><br><br>Other Path is:<br>1. Log in as an Approver<br>2. Click the RS user gonna use for testing<br>3. Click on the Related Document<br>4. Click on Payment Tab<br>5. Click on the Payment No.</td><td class="s4">1. Approver should be able to access the request.</td><td class="s7">Failed</td><td class="s9">3/3: RS is not in For My Approval/Assigned</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018</a></td></tr><tr style="height: 19px"><th id="135784448R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Payment Request Approval Confirmation</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click on the Payment Request Number from either entry point.<br>2 .Verify that a confirmation appears with Approve and Reject buttons.<br>3. Check that PR Number and Request Number are displayed.</td><td class="s4">1. Confirmation is sticky</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Navigation to Main Requisition Slip Page</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click on &quot;Visit Main RS&quot; button.</td><td class="s4">1. Should redirect to the RS Main Page.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s16"></td><td class="s17">Payment Request Approval - OFM Request</td><td class="s17">Verify Request Details Section is Non-Editable</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check fields: Payable Date, Payable To, Charge To, Discount.</td><td class="s17">1. Fields should be visible but non-editable.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Attachment Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Check Attachments&quot; button.<br>2. Search for a file by name.<br>3. Click on a file name to open it in a new tab.<br>4. Click the &quot;X&quot; icon to remove a file.<br>5. Confirm removal in the modal.</td><td class="s4">1. Attachments should be searchable, viewable, and removable with confirmation.</td><td class="s7">Failed</td><td class="s9">Can&#39;t remove attachments unless it&#39;s added by user</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1019">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1019</a></td></tr><tr style="height: 19px"><th id="135784448R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Adding Attachments</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Upload a file (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. File should upload successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s11">Verify Adding Attachments - Negative Scenarios</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Upload a file that is not a valid type and size is greater than 25MB (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br></td><td class="s4">1. Should not be able to upload file</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Notes Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Check Notes&quot; button.<br>2. Verify that all notes and reasons are displayed.<br></td><td class="s4">1. Notes should be visible.</td><td class="s7">Failed</td><td class="s9">Approvers are tagged as Requestor in Notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1022">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1022</a></td></tr><tr style="height: 19px"><th id="135784448R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Adding Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Enter a note (Alphanumeric, special characters allowed, max 100 characters).<br>2. Click &quot;Submit&quot;.</td><td class="s23">1. Note should be added successfully.</td><td class="s7">Failed</td><td class="s9">3/11: Character count remains at 0/60. Maximum of only 60 characters</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014</a></td></tr><tr style="height: 19px"><th id="135784448R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s11">Verify Adding Notes - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Enter a note that has emoji and try inputting more than 100 characters<br></td><td class="s4">1. Should not be able to input more than 100 characters<br>2. Emoji shouldn&#39;t be accepted as input</td><td class="s7">Failed</td><td class="s9">3/11: Emojis are accepted in notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Adding and Managing Approvers</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Add&quot; to add an Approver.<br>2. Select an available user (excluding IT Admin and Root User).<br>3. Click &quot;Submit&quot;.<br>4. Verify the new approver appears below the current approver.<br>5. Click the three-dot menu next to the added approver and select &quot;Edit&quot;.<br>6. Modify the approver and save.<br>7. Click the three-dot menu again and select &quot;Delete&quot;.<br>8. Confirm deletion in the modal.</td><td class="s4">1. Approver should be added, edited, and removed successfully.</td><td class="s7">Failed</td><td class="s9">3/11: No add button in approvers</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020</a></td></tr><tr style="height: 19px"><th id="135784448R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s16"></td><td class="s17">Payment Request Approval - OFM Request</td><td class="s17">Verify Purchase Order Details Section</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check PO Number, Supplier, Delivery Address, Terms, Deposit, Warranty, Invoice Details.<br>3. Click &quot;Supplier Details&quot; and &quot;Invoice Details&quot; buttons.</td><td class="s17">1. Details should be visible and modal should open correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Item Table</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Use the search function to find an item.<br>2. Verify columns (Item, Account Code, Delivered Quantity, Amount).<br>3. Check pagination (10 rows per page).<br>4. Verify sorting by Latest Updated Date.<br>5. Check Sorting per column<br>6. Use the clear button</td><td class="s4">1. Table should function correctly.<br>2. Check Sorting is working properly<br>3. Search function is working<br>4. Should clear the search bar and the searched item</td><td class="s7">Failed</td><td class="s9" rowspan="2">3/3: Price has more than 2 decimals</td><td class="s10" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td></tr><tr style="height: 19px"><th id="135784448R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Total Calculation Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Check that Sub-Total, Discounts, and Total Amount are displayed.</td><td class="s4">1. Correct totals should be displayed.</td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s16"></td><td class="s17">Payment Request Approval - OFM Request</td><td class="s17">Verify Approval Process (With Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Add an additional approver.<br>3. Click &quot;Submit&quot;.<br>4. Verify status updates to Approved.<br>5. Check Request History, Payments Tab for a record.</td><td class="s17">1. Approval should proceed correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s16"></td><td class="s17">Payment Request Approval - OFM Request</td><td class="s17">Verify Approval Process (Without Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Choose &quot;Confirm&quot; without adding an approver.<br>3. Verify status updates to Approved.<br>4. Check Request History, Payments Tab.</td><td class="s17">1. Approval should complete successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s4">Verify Rejection Process</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter a reason (max 100 characters, no emojis).<br>3. Click &quot;Confirm&quot;.<br>4. Verify status updates to &quot;Rejected&quot;.<br>5. Check Request History, Payments Tab.<br></td><td class="s4">1. Request should be rejected<br>2. Once rejected request should be returned to the assigned purchasing staff<br>3. Other approvers cannot proceed to approving once the higher level approver has a rejected status<br>4. Recorded in Payments Tab<br>5. Should require the Current Approver to Approve the Payment Request to proceed</td><td class="s13">Passed</td><td class="s9"></td><td class="s10"></td></tr><tr style="height: 19px"><th id="135784448R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s12"></td><td class="s22">Payment Request Approval - OFM Request</td><td class="s11">Verify Rejection Process - Negative Scenario</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter more than 100 characters in reason with emoji<br>3. Click &quot;Confirm&quot;.</td><td class="s4">1. Should not allow to enter more than 100 characters<br>2. Should not allow emoji on reason</td><td class="s7">Failed</td><td class="s9">3/10: Can submit emojis into reason for rejecting Payment Request</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021</a></td></tr><tr style="height: 19px"><th id="135784448R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Access of Approvers in Payment Request</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Log in as an Approver.<br>2. Click &quot;For My Approval/Assigned&quot; tab. <br>3. Verify the Payment Request is listed.<br>4. Open the Payment Request by clicking on the Ref No.<br><br><br>Other Path is:<br>1. Log in as an Approver<br>2. Click the RS user gonna use for testing<br>3. Click on the Related Document<br>4. Click on Payment Tab<br>5. Click on the Payment No.</td><td class="s4">1. Approver should be able to access the request.</td><td class="s7">Failed</td><td class="s9">3/3: RS is not in For My Approval/Assigned</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018</a></td></tr><tr style="height: 19px"><th id="135784448R71" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">72</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Payment Request Approval Confirmation</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click on the Payment Request Number from either entry point.<br>2 .Verify that a confirmation appears with Approve and Reject buttons.<br>3. Check that PR Number and Request Number are displayed.</td><td class="s4">1. Confirmation is sticky</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R72" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">73</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Navigation to Main Requisition Slip Page</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click on &quot;Visit Main RS&quot; button.</td><td class="s4">1. Should redirect to the RS Main Page.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R73" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">74</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Non-OFM Request </td><td class="s17">Verify Request Details Section is Non-Editable</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check fields: Payable Date, Payable To, Charge To, Discount.</td><td class="s17">1. Fields should be visible but non-editable.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R74" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">75</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Attachment Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Check Attachments&quot; button.<br>2. Search for a file by name.<br>3. Click on a file name to open it in a new tab.<br>4. Click the &quot;X&quot; icon to remove a file.<br>5. Confirm removal in the modal.</td><td class="s4">1. Attachments should be searchable, viewable, and removable with confirmation.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R75" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">76</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Adding Attachments</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Upload a file (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. File should upload successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R76" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">77</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s11">Verify Adding Attachments - Negative Scenarios</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Upload a file that is not a valid type and size is greater than 25MB (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br></td><td class="s4">1. Should not be able to upload file</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R77" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">78</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Notes Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Check Notes&quot; button.<br>2. Verify that all notes and reasons are displayed.<br></td><td class="s4">1. Notes should be visible.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R78" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">79</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Adding Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Enter a note (Alphanumeric, special characters allowed, max 100 characters).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. Note should be added successfully.</td><td class="s7">Failed</td><td class="s9">3/11: Character count remains at 0/60. Maximum of only 60 characters</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014</a></td></tr><tr style="height: 19px"><th id="135784448R79" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">80</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s11">Verify Adding Notes - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Enter a note that has emoji and try inputting more than 100 characters<br></td><td class="s4">1. Should not be able to input more than 100 characters<br>2. Emoji shouldn&#39;t be accepted as input</td><td class="s7">Failed</td><td class="s9">3/3: Emojis can be submitted in Notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R80" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">81</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Adding and Managing Approvers</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Add&quot; to add an Approver.<br>2. Select an available user (excluding IT Admin and Root User).<br>3. Click &quot;Submit&quot;.<br>4. Verify the new approver appears below the current approver.<br>5. Click the three-dot menu next to the added approver and select &quot;Edit&quot;.<br>6. Modify the approver and save.<br>7. Click the three-dot menu again and select &quot;Delete&quot;.<br>8. Confirm deletion in the modal.</td><td class="s4">1. Approver should be added, edited, and removed successfully.</td><td class="s7">Failed</td><td class="s9">3/11: No add button in approvers</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020</a></td></tr><tr style="height: 19px"><th id="135784448R81" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">82</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Non-OFM Request </td><td class="s17">Verify Purchase Order Details Section</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check PO Number, Supplier, Delivery Address, Terms, Deposit, Warranty, Invoice Details.<br>3. Click &quot;Supplier Details&quot; and &quot;Invoice Details&quot; buttons.</td><td class="s17">1. Details should be visible and modal should open correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R82" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">83</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Item Table</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Use the search function to find an item.<br>2. Verify columns (Item, Account Code, Delivered Quantity, Amount).<br>3. Check pagination (10 rows per page).<br>4. Verify sorting by Latest Updated Date.<br>5. Check Sorting per column<br>6. Use the clear button</td><td class="s4">1. Table should function correctly.<br>2. Check Sorting is working properly<br>3. Search function is working<br>4. Should clear the search bar and the searched item</td><td class="s7">Failed</td><td class="s9" rowspan="2">3/3: Price has more than 2 decimals</td><td class="s10" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td></tr><tr style="height: 19px"><th id="135784448R83" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">84</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Total Calculation Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Check that Sub-Total, Discounts, and Total Amount are displayed.</td><td class="s4">1. Correct totals should be displayed.</td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R84" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">85</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Non-OFM Request </td><td class="s17">Verify Approval Process (With Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Add an additional approver.<br>3. Click &quot;Submit&quot;.<br>4. Verify status updates to Approved.<br>5. Check Request History, Payments Tab for a record.</td><td class="s17">1. Approval should proceed correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R85" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">86</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Non-OFM Request </td><td class="s17">Verify Approval Process (Without Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Choose &quot;Confirm&quot; without adding an approver.<br>3. Verify status updates to Approved.<br>4. Check Request History, Payments Tab.</td><td class="s17">1. Approval should complete successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R86" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">87</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s4">Verify Rejection Process</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter a reason (max 100 characters, no emojis).<br>3. Click &quot;Confirm&quot;.<br>4. Verify status updates to &quot;Rejected&quot;.<br>5. Check Request History, Payments Tab.<br></td><td class="s4">1. Request should be rejected<br>2. Once rejected request should be returned to the assigned purchasing staff<br>3. Other approvers cannot proceed to approving once the higher level approver has a rejected status<br>4. Recorded in Payments Tab<br>5. Should require the Current Approver to Approve the Payment Request to proceed</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R87" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">88</div></th><td class="s12"></td><td class="s5">Payment Request Approval - Non-OFM Request </td><td class="s11">Verify Rejection Process - Negative Scenario</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter more than 100 characters in reason with emoji<br>3. Click &quot;Confirm&quot;.</td><td class="s4">1. Should not allow to enter more than 100 characters<br>2. Should not allow emoji on reason</td><td class="s7">Failed</td><td class="s9">3/10: Can submit emojis into reason for rejecting Payment Request</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021</a></td></tr><tr style="height: 19px"><th id="135784448R88" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">89</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Access of Approvers in Payment Request</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Log in as an Approver.<br>2. Click &quot;For My Approval/Assigned&quot; tab. <br>3. Verify the Payment Request is listed.<br>4. Open the Payment Request by clicking on the Ref No.<br><br><br>Other Path is:<br>1. Log in as an Approver<br>2. Click the RS user gonna use for testing<br>3. Click on the Related Document<br>4. Click on Payment Tab<br>5. Click on the Payment No.</td><td class="s4">1. Approver should be able to access the request.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R89" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">90</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Payment Request Approval Confirmation</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click on the Payment Request Number from either entry point.<br>2 .Verify that a confirmation appears with Approve and Reject buttons.<br>3. Check that PR Number and Request Number are displayed.</td><td class="s4">1. Confirmation is sticky</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R90" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">91</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Navigation to Main Requisition Slip Page</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click on &quot;Visit Main RS&quot; button.</td><td class="s4">1. Should redirect to the RS Main Page.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R91" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">92</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s17">Verify Request Details Section is Non-Editable</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check fields: Payable Date, Payable To, Charge To, Discount.</td><td class="s17">1. Fields should be visible but non-editable.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R92" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">93</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Attachment Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Check Attachments&quot; button.<br>2. Search for a file by name.<br>3. Click on a file name to open it in a new tab.<br>4. Click the &quot;X&quot; icon to remove a file.<br>5. Confirm removal in the modal.</td><td class="s4">1. Attachments should be searchable, viewable, and removable with confirmation.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R93" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">94</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Adding Attachments</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Upload a file (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. File should upload successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R94" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">95</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s11">Verify Adding Attachments - Negative Scenarios</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Upload a file that is not a valid type and size is greater than 25MB (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br></td><td class="s4">1. Should not be able to upload file</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R95" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">96</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Notes Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Check Notes&quot; button.<br>2. Verify that all notes and reasons are displayed.<br></td><td class="s4">1. Notes should be visible.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R96" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">97</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Adding Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Enter a note (Alphanumeric, special characters allowed, max 100 characters).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. Note should be added successfully.</td><td class="s7">Failed</td><td class="s9">3/11: Character count remains at 0/60. Maximum of only 60 characters</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014</a></td></tr><tr style="height: 19px"><th id="135784448R97" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">98</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s11">Verify Adding Notes - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Enter a note that has emoji and try inputting more than 100 characters<br></td><td class="s4">1. Should not be able to input more than 100 characters<br>2. Emoji shouldn&#39;t be accepted as input</td><td class="s7">Failed</td><td class="s9">3/3: Emojis can be submitted in Notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R98" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">99</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Adding and Managing Approvers</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Add&quot; to add an Approver.<br>2. Select an available user (excluding IT Admin and Root User).<br>3. Click &quot;Submit&quot;.<br>4. Verify the new approver appears below the current approver.<br>5. Click the three-dot menu next to the added approver and select &quot;Edit&quot;.<br>6. Modify the approver and save.<br>7. Click the three-dot menu again and select &quot;Delete&quot;.<br>8. Confirm deletion in the modal.</td><td class="s4">1. Approver should be added, edited, and removed successfully.</td><td class="s7">Failed</td><td class="s9">3/11: No add button in approvers</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020</a></td></tr><tr style="height: 19px"><th id="135784448R99" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">100</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s17">Verify Purchase Order Details Section</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check PO Number, Supplier, Delivery Address, Terms, Deposit, Warranty, Invoice Details.<br>3. Click &quot;Supplier Details&quot; and &quot;Invoice Details&quot; buttons.</td><td class="s17">1. Details should be visible and modal should open correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R100" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">101</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Item Table</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Use the search function to find an item.<br>2. Verify columns (Item, Account Code, Delivered Quantity, Amount).<br>3. Check pagination (10 rows per page).<br>4. Verify sorting by Latest Updated Date.<br>5. Check Sorting per column<br>6. Use the clear button</td><td class="s4">1. Table should function correctly.<br>2. Check Sorting is working properly<br>3. Search function is working<br>4. Should clear the search bar and the searched item</td><td class="s7">Failed</td><td class="s9" rowspan="2">3/3: Price has more than 2 decimals</td><td class="s10" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1050">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1050</a></td></tr><tr style="height: 19px"><th id="135784448R101" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">102</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Total Calculation Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Check that Sub-Total, Discounts, and Total Amount are displayed.</td><td class="s4">1. Correct totals should be displayed.</td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R102" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">103</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s17">Verify Approval Process (With Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Add an additional approver.<br>3. Click &quot;Submit&quot;.<br>4. Verify status updates to Approved.<br>5. Check Request History, Payments Tab for a record.</td><td class="s17">1. Approval should proceed correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R103" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">104</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s17">Verify Approval Process (Without Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Choose &quot;Confirm&quot; without adding an approver.<br>3. Verify status updates to Approved.<br>4. Check Request History, Payments Tab.</td><td class="s17">1. Approval should complete successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R104" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">105</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s4">Verify Rejection Process</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter a reason (max 100 characters, no emojis).<br>3. Click &quot;Confirm&quot;.<br>4. Verify status updates to &quot;Rejected&quot;.<br>5. Check Request History, Payments Tab.<br></td><td class="s4">1. Request should be rejected<br>2. Once rejected request should be returned to the assigned purchasing staff<br>3. Other approvers cannot proceed to approving once the higher level approver has a rejected status<br>4. Recorded in Payments Tab<br>5. Should require the Current Approver to Approve the Payment Request to proceed</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R105" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">106</div></th><td class="s12"></td><td class="s24">Payment Request Approval - Transfer of Materials - OFM Items</td><td class="s11">Verify Rejection Process - Negative Scenario</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>    -  i. Level 1 - Purchasing Staff&#39;s Supervisor<br>    -  ii. Level 2 - Requester&#39;s Supervisor<br>    -  iv. Level 3 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter more than 100 characters in reason with emoji<br>3. Click &quot;Confirm&quot;.</td><td class="s4">1. Should not allow to enter more than 100 characters<br>2. Should not allow emoji on reason</td><td class="s7">Failed</td><td class="s9">3/10: Can submit emojis into reason for rejecting Payment Request</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021</a></td></tr><tr style="height: 19px"><th id="135784448R106" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">107</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Access of Approvers in Payment Request</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Log in as an Approver.<br>2. Click &quot;For My Approval/Assigned&quot; tab. <br>3. Verify the Payment Request is listed.<br>4. Open the Payment Request by clicking on the Ref No.<br><br><br>Other Path is:<br>1. Log in as an Approver<br>2. Click the RS user gonna use for testing<br>3. Click on the Related Document<br>4. Click on Payment Tab<br>5. Click on the Payment No.</td><td class="s4">1. Approver should be able to access the request.</td><td class="s7">Failed</td><td class="s9">3/3: RS is not in For My Approval/Assigned</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018</a></td></tr><tr style="height: 19px"><th id="135784448R107" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">108</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Payment Request Approval Confirmation</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click on the Payment Request Number from either entry point.<br>2 .Verify that a confirmation appears with Approve and Reject buttons.<br>3. Check that PR Number and Request Number are displayed.</td><td class="s4">1. Confirmation is sticky</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R108" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">109</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Navigation to Main Requisition Slip Page</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click on &quot;Visit Main RS&quot; button.</td><td class="s4">1. Should redirect to the RS Main Page.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R109" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">110</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s17">Verify Request Details Section is Non-Editable</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Open the Payment Request.<br>2. Check fields: Payable Date, Payable To, Charge To, Discount.</td><td class="s17">1. Fields should be visible but non-editable.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R110" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">111</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Attachment Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Check Attachments&quot; button.<br>2. Search for a file by name.<br>3. Click on a file name to open it in a new tab.<br>4. Click the &quot;X&quot; icon to remove a file.<br>5. Confirm removal in the modal.</td><td class="s4">1. Attachments should be searchable, viewable, and removable with confirmation.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R111" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">112</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Adding Attachments</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Upload a file (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. File should upload successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R112" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">113</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s11">Verify Adding Attachments - Negative Scenarios</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Upload a file that is not a valid type and size is greater than 25MB (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br></td><td class="s4">1. Should not be able to upload file</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R113" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">114</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Notes Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Check Notes&quot; button.<br>2. Verify that all notes and reasons are displayed.<br></td><td class="s4">1. Notes should be visible.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1026</a></td></tr><tr style="height: 19px"><th id="135784448R114" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">115</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Adding Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Enter a note (Alphanumeric, special characters allowed, max 100 characters).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. Note should be added successfully.</td><td class="s7">Failed</td><td class="s9">3/11: Character count remains at 0/60. Maximum of only 60 characters</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1014</a></td></tr><tr style="height: 19px"><th id="135784448R115" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">116</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s11">Verify Adding Notes - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Enter a note that has emoji and try inputting more than 100 characters<br></td><td class="s4">1. Should not be able to input more than 100 characters<br>2. Emoji shouldn&#39;t be accepted as input</td><td class="s7">Failed</td><td class="s9">3/3: Emojis can be submitted in Notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R116" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">117</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Adding and Managing Approvers</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Add&quot; to add an Approver.<br>2. Select an available user (excluding IT Admin and Root User).<br>3. Click &quot;Submit&quot;.<br>4. Verify the new approver appears below the current approver.<br>5. Click the three-dot menu next to the added approver and select &quot;Edit&quot;.<br>6. Modify the approver and save.<br>7. Click the three-dot menu again and select &quot;Delete&quot;.<br>8. Confirm deletion in the modal.</td><td class="s4">1. Approver should be added, edited, and removed successfully.</td><td class="s7">Failed</td><td class="s9">3/11: No add button in approvers</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020</a></td></tr><tr style="height: 19px"><th id="135784448R117" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">118</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Purchase Order Details Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Open the Payment Request.<br>2. Check PO Number, Supplier, Delivery Address, Terms, Deposit, Warranty, Invoice Details.<br>3. Click &quot;Supplier Details&quot; and &quot;Invoice Details&quot; buttons.</td><td class="s4">1. Details should be visible and modal should open correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R118" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">119</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Item Table</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Use the search function to find an item.<br>2. Verify columns (Item, Account Code, Delivered Quantity, Amount).<br>3. Check pagination (10 rows per page).<br>4. Verify sorting by Latest Updated Date.<br>5. Check Sorting per column<br>6. Use the clear button</td><td class="s4">1. Table should function correctly.<br>2. Check Sorting is working properly<br>3. Search function is working<br>4. Should clear the search bar and the searched item</td><td class="s7">Failed</td><td class="s9" rowspan="2">3/3: Price has more than 2 decimals</td><td class="s10" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1082">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1082</a></td></tr><tr style="height: 19px"><th id="135784448R119" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">120</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Total Calculation Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Check that Sub-Total, Discounts, and Total Amount are displayed.</td><td class="s4">1. Correct totals should be displayed.</td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R120" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">121</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s17">Verify Approval Process (With Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Add an additional approver.<br>3. Click &quot;Submit&quot;.<br>4. Verify status updates to Approved.<br>5. Check Request History, Payments Tab for a record.</td><td class="s17">1. Approval should proceed correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R121" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">122</div></th><td class="s16"></td><td class="s17">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s17">Verify Approval Process (Without Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Choose &quot;Confirm&quot; without adding an approver.<br>3. Verify status updates to Approved.<br>4. Check Request History, Payments Tab.</td><td class="s17">1. Approval should complete successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R122" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">123</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s4">Verify Rejection Process</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter a reason (max 100 characters, no emojis).<br>3. Click &quot;Confirm&quot;.<br>4. Verify status updates to &quot;Rejected&quot;.<br>5. Check Request History, Payments Tab.<br></td><td class="s4">1. Request should be rejected<br>2. Once rejected request should be returned to the assigned purchasing staff<br>3. Other approvers cannot proceed to approving once the higher level approver has a rejected status<br>4. Recorded in Payments Tab<br>5. Should require the Current Approver to Approve the Payment Request to proceed</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R123" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">124</div></th><td class="s12"></td><td class="s25">Payment Request Approval - Transfer of Materials - Non-OFM Items</td><td class="s11">Verify Rejection Process - Negative Scenario</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip for Non-OFM Request is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Payment Request Status is For PR Approval<br>6. User Must Use Approver Account which are the following:<br>      - i. Level 1 - Purchasing Staff&#39;s Supervisor<br>      - ii. Level 2 - Purchasing Head</td><td class="s4">1. Click &quot;Reject&quot;.<br>2. Enter more than 100 characters in reason with emoji<br>3. Click &quot;Confirm&quot;.</td><td class="s4">1. Should not allow to enter more than 100 characters<br>2. Should not allow emoji on reason</td><td class="s7">Failed</td><td class="s9">3/10: Can submit emojis into reason for rejecting Payment Request</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1021</a></td></tr><tr style="height: 19px"><th id="135784448R124" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">125</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Access of Approvers in Payment Request</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Log in as an Approver.<br>2. Click &quot;For My Approval/Assigned&quot; tab. <br>3. Verify the Payment Request is listed.<br>4. Open the Payment Request by clicking on the Ref No.<br><br><br>Other Path is:<br>1. Log in as an Approver<br>2. Click the RS user gonna use for testing<br>3. Click on the Related Document<br>4. Click on Payment Tab<br>5. Click on the Payment No.</td><td class="s4">1. Approver should be able to access the request.</td><td class="s7">Failed</td><td class="s9">3/3: RS is not in For My Approval/Assigned</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1018</a></td></tr><tr style="height: 19px"><th id="135784448R125" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">126</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Payment Request Approval Confirmation</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Click on the Payment Request Number from either entry point.<br>2 .Verify that a confirmation appears with Approve and Reject buttons.<br>3. Check that PR Number and Request Number are displayed.</td><td class="s4">1. Confirmation is sticky</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R126" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">127</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Navigation to Main Requisition Slip Page</td><td class="s6">Minor</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Click on &quot;Visit Main RS&quot; button.</td><td class="s4">1. Should redirect to the RS Main Page.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R127" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">128</div></th><td class="s16"></td><td class="s17">Payment Submission to Accounting</td><td class="s17">Verify Request Details Section is Non-Editable</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s17">1. Open the Payment Request.<br>2. Check fields: Payable Date, Payable To, Charge To, Discount.</td><td class="s17">1. Fields should be visible but non-editable.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R128" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">129</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Attachment Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Click &quot;Check Attachments&quot; button.<br>2. Search for a file by name.<br>3. Click on a file name to open it in a new tab.<br>4. Click the &quot;X&quot; icon to remove a file.<br>5. Confirm removal in the modal.</td><td class="s4">1. Attachments should be searchable, viewable, and removable with confirmation.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1019">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1019</a></td></tr><tr style="height: 19px"><th id="135784448R129" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">130</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Adding Attachments</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Upload a file (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. File should upload successfully.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R130" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">131</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s11">Verify Adding Attachments - Negative Scenarios</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Upload a file that is not a valid type and size is greater than 25MB (valid types: PDF, Doc, JPG, PNG, CSV, Excel; max size: 25MB).<br></td><td class="s4">1. Should not be able to upload file</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R131" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">132</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Notes Features</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Click &quot;Check Notes&quot; button.<br>2. Verify that all notes and reasons are displayed.<br></td><td class="s4">1. Notes should be visible.</td><td class="s7">Failed</td><td class="s9">3/4: Uploaded attachments and notes don&#39;t carry over when payment request is submitted</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1022">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1022</a></td></tr><tr style="height: 19px"><th id="135784448R132" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">133</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Adding Notes</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Enter a note (Alphanumeric, special characters allowed, max 100 characters).<br>2. Click &quot;Submit&quot;.</td><td class="s4">1. Note should be added successfully.</td><td class="s7">Failed</td><td class="s9">3/11: Character count remains at 0/60. Maximum of only 60 characters</td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R133" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">134</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s11">Verify Adding Notes - Negative Scenario</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Enter a note that has emoji and try inputting more than 100 characters<br></td><td class="s4">1. Should not be able to input more than 100 characters<br>2. Emoji shouldn&#39;t be accepted as input</td><td class="s7">Failed</td><td class="s9">3/3: Emojis can be submitted in Notes</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1016</a></td></tr><tr style="height: 19px"><th id="135784448R134" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">135</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Adding and Managing Approvers</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Click &quot;Add&quot; to add an Approver.<br>2. Select an available user (excluding IT Admin and Root User).<br>3. Click &quot;Submit&quot;.<br>4. Verify the new approver appears below the current approver.<br>5. Click the three-dot menu next to the added approver and select &quot;Edit&quot;.<br>6. Modify the approver and save.<br>7. Click the three-dot menu again and select &quot;Delete&quot;.<br>8. Confirm deletion in the modal.</td><td class="s4">1. Approver should be added, edited, and removed successfully.</td><td class="s7">Failed</td><td class="s9">3/11: No add button in approvers</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1020</a></td></tr><tr style="height: 19px"><th id="135784448R135" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">136</div></th><td class="s16"></td><td class="s17">Payment Submission to Accounting</td><td class="s17">Verify Purchase Order Details Section</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s17">1. Open the Payment Request.<br>2. Check PO Number, Supplier, Delivery Address, Terms, Deposit, Warranty, Invoice Details.<br>3. Click &quot;Supplier Details&quot; and &quot;Invoice Details&quot; buttons.</td><td class="s17">1. Details should be visible and modal should open correctly.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R136" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">137</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Item Table</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Use the search function to find an item.<br>2. Verify columns (Item, Account Code, Delivered Quantity, Amount).<br>3. Check pagination (10 rows per page).<br>4. Verify sorting by Latest Updated Date.<br>5. Check Sorting per column<br>6. Use the clear button</td><td class="s4">1. Table should function correctly.<br>2. Check Sorting is working properly<br>3. Search function is working<br>4. Should clear the search bar and the searched item</td><td class="s7">Failed</td><td class="s9" rowspan="2">3/3: Price has more than 2 decimals</td><td class="s10" rowspan="2"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1017</a></td></tr><tr style="height: 19px"><th id="135784448R137" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">138</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Total Calculation Section</td><td class="s6">Critical</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Check that Sub-Total, Discounts, and Total Amount are displayed.</td><td class="s4">1. Correct totals should be displayed.</td><td class="s7">Failed</td></tr><tr style="height: 19px"><th id="135784448R138" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">139</div></th><td class="s16"></td><td class="s17">Payment Submission to Accounting</td><td class="s17">Verify Approval Process (With Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Add an additional approver.<br>3. Click &quot;Submit&quot;.<br>4. Verify status updates to Approved.<br>5. Check Request History, Payments Tab for a record.</td><td class="s17">1. Approval should proceed correctly.<br>2. Should update the Approver&#39;s Status to Approve<br>3. Should record the Action to Request History, Payments Tab<br>4. Should display the Added Approver&#39;s Name below the Current Approver, if applicable<br></td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R139" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">140</div></th><td class="s16"></td><td class="s17">Payment Submission to Accounting</td><td class="s17">Verify Approval Process (Without Additional Approver)</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s17">1. Click &quot;Approve&quot;.<br>2. Choose &quot;Confirm&quot; without adding an approver.<br>3. Verify status updates to Approved.<br>4. Check Request History, Payments Tab.</td><td class="s17">1. Approval should proceed correctly.<br>2. Should update the Approver&#39;s Status to Approve<br>3. Should record the Action to Request History, Payments Tab<br>4. Should display the Added Approver&#39;s Name below the Current Approver, if applicable<br></td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R140" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">141</div></th><td class="s12"></td><td class="s22">Payment Submission to Accounting</td><td class="s4">Verify Approval Process - Cancellation</td><td class="s6">High</td><td class="s4">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s4">1. Click &quot;Approve&quot;.<br>2. Click Cancel<br>3. Verify Modal closed</td><td class="s4">1. Modal should close<br>2. Return to Payment Request Page</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R141" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">142</div></th><td class="s16"></td><td class="s17">Payment Submission to Accounting</td><td class="s17">Verify Payment Request Submission to Accounting</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s17">1. Approve the Payment Request as the last Approver.<br>2. Verify the Payment Request is sent to the Accounting of Cityland.<br>3. Check if Payment Request status is updated to &quot;Closed.&quot;<br>4. Verify RS Status updates to &quot;Partial Payment.&quot;</td><td class="s17">1. Payment Request is sent to Accounting successfully.<br>2. Payment Request status is updated to &quot;Closed.&quot;<br>3. RS Status is updated to &quot;Partial Payment.&quot;</td><td class="s7">Failed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R142" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">143</div></th><td class="s16"></td><td class="s17">Payment Submission to Accounting</td><td class="s17">Verify Payment Request Closure</td><td class="s18">Critical</td><td class="s17">1. Requisition Slip is created<br>2. Purchase Order has been made<br>3. Delivery of the items in the Purchase Order has been fully Delivered<br>4. Payment Request has been created<br>5. Status is For PR Approval<br>6. Current Approver is the last Approver</td><td class="s17">1. Approve all Payment Requests for a Requisition Slip.</td><td class="s17">1. RS Status updates to &quot;Fully Closed.&quot;</td><td class="s7">Failed</td><td class="s9">3/11: RS status only closes for OFM Type of Request and does not change to closed after approval of all PR for other types</td><td class="s10"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-960">https://youtrack.stratpoint.com/issue/CITYLANDPRS-960</a></td></tr><tr style="height: 19px"><th id="135784448R143" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">144</div></th><td class="s12"></td><td class="s26">Assigning of Payment Request Approvers with an existing Payment Request</td><td class="s4">Ensure Approvers are Not Assigned in the Payment Request</td><td class="s4">High</td><td class="s4">1. Requestor should have no supervisor<br>2. A Payment Request has been created</td><td class="s4">1. Open the Payment Request details.<br>2. Verify that no approvers are automatically assigned on level for requester&#39;s supervisor</td><td class="s4">1. The system should indicate that no Approvers are assigned.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R144" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">145</div></th><td class="s12"></td><td class="s26">Assigning of Payment Request Approvers with an existing Payment Request</td><td class="s4">Ensure Next Approver can not Approve if there is an Empty Approver a level before</td><td class="s4">High</td><td class="s4">1. A Payment Request has been created with missing approver<br>2. Should be the next approver after empty approver level</td><td class="s4">1. Verify if next approver can approve Payment Request</td><td class="s4">1. should prevent approval until approver is assigned</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R145" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">146</div></th><td class="s12"></td><td class="s26">Assigning of Payment Request Approvers with an existing Payment Request</td><td class="s4">Assign an Approver After Payment Request Creation</td><td class="s4">Critical</td><td class="s4">1. A Payment Request has been created with missing approver</td><td class="s4">1. Assign an Approver to a specific User Type.<br>2. Verify if the Approver is applied to all Open, Not Approved, and New Payment Requests.</td><td class="s4">1. should automatically assign the newly assigned Approver to all relevant Payment Requests.</td><td class="s13">Passed</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R146" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">147</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify the generation of the Gate Pass</td><td class="s4">Critical</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers</td><td class="s4">1. Complete approval process for payment request<br>2. Navigate to Requisition Slip &gt; Attachments<br>3. Validate if Gate Pass was generated<br>4. Click Gate Pass from Attachments<br>5. Try to delete Gate Pass from Attachments</td><td class="s4">1. Payment Request status should change to &quot;Approved.&quot;<br>2. Should display a Badge for New Attachment to the New Attachments Button and Modal<br>3. Should add the Gate Pass as an Attachment (PDF file) to the Requisition Slip<br>4. Should be able to view to a New Tab once clicked<br>5. Should not be able to delete as an Attachment</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R147" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">148</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify the Header of Generated Gate Pass</td><td class="s4">High</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate header of Gate Pass</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should have a Header displaying:<br>    a. Cityland Group of Companies<br>    b. Gate Pass<br>    c. (Internal Use Only)</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R148" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">149</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify Incremental Numbering of Gate Pass</td><td class="s4">Medium</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate header of Gate Pass</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should have a Number that will increment depending on the Released Gate Pass per Payment Request for Transfer of Materials Request Type</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R149" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">150</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify Gate Pass Information</td><td class="s4">High</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate information displayed</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should have the following Data in the Gate Pass:<br>    a. Date - Date that the gate pass was generated<br>         i. Format - MONTH DD YYYY [ex. June 20, 2024]<br>     b. Stock From - selected Supplier Company or Project during Canvassing<br>     c. To - the Company selected in the Requisition Slip</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R150" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">151</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify Gate Pass Items Table List</td><td class="s4">High</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate Items Table List</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should have Items Table displaying:<br>    a. Item # - Incremental Numbering Count of Items<br>    b. Quantity - Quantity Purchased in the Purchase Order<br>    c. Description - Item Description/Name<br>    d. Remarks - Latest Notes to the Item in the Requisition Slip</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R151" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">152</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify Assignatory section</td><td class="s4">High</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate assignatory section</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should have Empty Section for Assignatories<br>    a. Requested By - Engineer In-Charge<br>    b. Noted By - Supervisor/Dept Head<br>    c. Approval By - Purchasing Head<br>    d. Clearance Approved By</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R152" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">153</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify Guard section</td><td class="s4">High</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate Guard section</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should have Section for the Guard&#39;s Remarks<br>    a. Date<br>    b. Time<br>    c. Staff<br>    d. Driver<br>    e. Vehicle Type<br>    f. Plate #<br>    g. Guard&#39;s Remarks<br>    h. Materials Received/Released By:<br>        i. Print Name and Sign<br>        ii. Date and Time</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="135784448R153" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">154</div></th><td class="s12"></td><td class="s5">Gate Pass for Transfer of Material Request</td><td class="s4">Verify Continuation of Item Table Data Across Multiple Pages in Gate Pass</td><td class="s4">High</td><td class="s4">1. Should have an Requisition Slip with a Request Type of<br>    a. OFM Transfer of Materials<br>    b. Non-OFM Transfer of Materials<br>2. Should have the at least one Payment Request fully Approved by all of the Approvers<br>3. Should have Gate Pass generated and available as an attachment in the Requisition Slip<br>4. Should have more than 5 items in RS</td><td class="s4">1. Click on Gate Pass attachment to open it in a new tab<br>2. Validate Gate Pass Details<br>3. Validate if Item Table List is dynamic</td><td class="s4">1. Gate Pass should open in a new tab as a PDF file without errors<br>2. Should retain all of the Details of the Gate Pass, Assignatories, and Guard&#39;s Remarks per Page<br>3. Should have the Items Table Dynamic<br>    a. Should continue the Numbering of the Items per Page</td><td class="s13">Not Run</td><td class="s9"></td><td class="s14"></td></tr></tbody></table></div>