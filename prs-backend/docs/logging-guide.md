# PRS Backend Logging Guide

This document provides an overview of the logging system implemented in the PRS Backend application, with a focus on production environments.

## Overview

The PRS Backend uses a structured logging system based on Pino, with the following features:

- **Structured JSON Logs**: All logs are in JSON format for easy parsing and analysis
- **Multiple Log Destinations**: Logs are sent to both console (for Docker) and files
- **Log Levels**: Different severity levels (info, warn, error) for appropriate filtering
- **Log Rotation**: Automatic rotation of log files to manage disk space
- **Sensitive Data Protection**: Redaction of sensitive information in logs

## Log Locations

In production, logs can be found in the following locations:

1. **Docker Container Logs**:
   ```bash
   docker logs deployment-backend-1
   ```

2. **Log Files**:
   - Main application logs: `/home/<USER>/prs-prod/prs-backend/logs/app.log`
   - Error logs: `/home/<USER>/prs-prod/prs-backend/logs/error.log`
   - Rotated logs: `/home/<USER>/prs-prod/prs-backend/logs/app.log.YYYYMMDD-HHMMSS.gz`

## Log Format

Logs are in structured JSON format with the following fields:

```json
{
  "level": 30,                           // Log level (30=info, 40=warn, 50=error)
  "time": 1746852924104,                 // Timestamp in milliseconds
  "pid": 717498,                         // Process ID
  "hostname": "ip-172-31-56-40",         // Server hostname
  "service": "prs-backend",              // Service name
  "environment": "production",           // Environment
  "msg": "User logged in",               // Log message
  "userId": 123,                         // Additional context fields
  "category": "security"                 // Log category
}
```

## Log Management

### Log Rotation

Log files are automatically rotated:
- When they exceed 10MB in size
- Daily at 1:00 AM (via cron job)
- Rotated logs are compressed with gzip
- App logs are kept for 14 days
- Error logs are kept for 30 days

To set up the log rotation cron job:

```bash
cd /home/<USER>/prs-prod/prs-backend
./scripts/setup-log-rotation.sh
```

### Manual Log Management

A log management utility script is provided:

```bash
cd /home/<USER>/prs-prod/prs-backend
node scripts/manage-logs.js
```

This utility allows you to:
- List all log files with sizes
- Clean up old logs
- Force log rotation
- View log statistics

### Viewing Logs

To view logs in real-time:

```bash
# Docker logs
docker logs -f deployment-backend-1

# File logs
tail -f /home/<USER>/prs-prod/prs-backend/logs/app.log
```

To search logs:

```bash
# Search for errors
grep "error" /home/<USER>/prs-prod/prs-backend/logs/app.log

# Search for a specific user
grep "userId\":123" /home/<USER>/prs-prod/prs-backend/logs/app.log
```

To parse and analyze JSON logs:

```bash
# Using jq (may need to be installed)
cat /home/<USER>/prs-prod/prs-backend/logs/app.log | jq

# Filter for errors
cat /home/<USER>/prs-prod/prs-backend/logs/app.log | jq 'select(.level == 50)'

# Filter by category
cat /home/<USER>/prs-prod/prs-backend/logs/app.log | jq 'select(.category == "security")'
```

## Best Practices for Logging

When adding new logs to the application, follow these best practices:

1. **Use Appropriate Log Levels**:
   - `logger.error()`: For errors that affect functionality
   - `logger.warn()`: For potential issues that don't break functionality
   - `logger.info()`: For significant application events
   - `logger.debug()`: For detailed troubleshooting (disabled in production)

2. **Include Context**:
   ```javascript
   logger.info('User created order', { 
     userId: user.id, 
     orderId: order.id,
     amount: order.total
   });
   ```

3. **Categorize Logs**:
   ```javascript
   logger.info('User logged in', { 
     category: 'security',
     userId: user.id
   });
   ```

4. **Never Log Sensitive Data**:
   - Passwords
   - Authentication tokens
   - Credit card numbers
   - Personal identifiable information

5. **Log Business Events**:
   ```javascript
   logger.info('Order status changed', {
     category: 'business',
     orderId: order.id,
     oldStatus: oldStatus,
     newStatus: newStatus,
     changedBy: user.id
   });
   ```

## Troubleshooting

If logs are not being written to files:

1. Check if the logs directory exists and has proper permissions:
   ```bash
   ls -la /home/<USER>/prs-prod/prs-backend/logs/
   ```

2. Ensure the application has write permissions:
   ```bash
   chmod 755 /home/<USER>/prs-prod/prs-backend/logs/
   ```

3. Check disk space:
   ```bash
   df -h
   ```

4. Verify the log configuration in `src/infra/logs/index.js`
