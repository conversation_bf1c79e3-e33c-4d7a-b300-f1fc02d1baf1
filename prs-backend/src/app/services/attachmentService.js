const { transact } = require('@fastify/postgres');

class AttachmentService {
  constructor({
    db,
    fastify,
    attachmentRepository,
    attachmentBadgeRepository,
    clientErrors,
  }) {
    this.db = db;
    this.fastify = fastify;
    this.attachmentRepository = attachmentRepository;
    this.attachmentBadgeRepository = attachmentBadgeRepository;
    this.clientErrors = clientErrors;
  }

  async createAttachments({
    model,
    userId,
    modelId,
    parentPath,
    attachments,
    transaction,
  }) {
    let results;
    const options = transaction ? { transaction } : {};
    const formattedAttachments = attachments.map((file) => ({
      model,
      modelId,
      userId,
      fileName: file.originalname,
      path: `/${parentPath}/${file.filePath}`,
    }));

    try {
      this.fastify.log.info(`Creating attachments - ${model}`);
      results = await Promise.all(
        formattedAttachments.map((attachment) =>
          this.attachmentRepository.create(attachment, options),
        ),
      );

      this.fastify.log.info(
        `Successfully created ${results.length} attachments`,
      );
    } catch (error) {
      this.fastify.log.error('[ERROR] Saving attachments');
      this.fastify.log.error('error');

      throw error;
    }

    return results;
  }

  async getAttachments(payload = {}) {
    const { search, model, modelId } = payload;

    const whereClause = {
      model,
      modelId,
    };

    if (search) {
      whereClause.fileName = {
        [this.db.Sequelize.Op.iLike]: `%${search}%`,
      };
    }

    const attachments = await this.attachmentRepository.getAllAttachments({
      whereClause,
    });

    // if an attachment has empty badge data array it means that it has not been seen by the user
    const hasNewNotifications = attachments.data.some((attachment) =>
      attachment.badges.length === 0 ? true : false,
    );

    const result = {
      hasNewNotifications,
      data: attachments.data,
      total: attachments.total,
    };

    return result;
  }

  async syncAttachmentsByIds(
    currentAttachmentIds,
    updatedAttachmentIds,
    model,
    modelId,
    userId,
    transaction = null,
  ) {
    if (!updatedAttachmentIds) {
      updatedAttachmentIds = [];
    }

    const newAttachmentIdsForAssignment = updatedAttachmentIds
      ? updatedAttachmentIds
          .filter((id) => !currentAttachmentIds.includes(Number(id)))
          .map(Number)
      : [];

    if (newAttachmentIdsForAssignment.length) {
      await this.assignAttachmentsToModelId(
        newAttachmentIdsForAssignment,
        model,
        modelId,
        userId,
        transaction,
      );
    }

    const attachmentIdsForRemoval = currentAttachmentIds
      .filter((id) => !updatedAttachmentIds.includes(id))
      .map(Number);

    if (attachmentIdsForRemoval.length) {
      await this.removeAttachments(
        attachmentIdsForRemoval,
        model,
        modelId,
        userId,
        transaction,
      );
    }
  }

  async assignAttachmentsToModelId(
    attachmentIds,
    model,
    modelId,
    userId,
    transaction = null,
  ) {
    await this._validateForAssignOrRemove(
      attachmentIds,
      model,
      modelId,
      userId,
    );
    const results = await this.attachmentRepository.assignAttachmentsToModelId({
      attachmentIds,
      model,
      modelId,
      userId,
      transaction,
    });

    return results;
  }

  async removeAttachments(
    attachmentIds,
    model,
    modelId,
    userId,
    transaction = null,
  ) {
    await this._validateForAssignOrRemove(
      attachmentIds,
      model,
      modelId,
      userId,
      transaction,
    );
    const results = await this.attachmentRepository.removeAttachments(
      attachmentIds,
      model,
      modelId,
      userId,
      transaction,
    );

    return results;
  }

  async _validateForAssignOrRemove(attachmentIds, model, modelId, userId) {
    const attachments = await this.attachmentRepository.findAll({
      where: {
        id: {
          [this.db.Sequelize.Op.in]: attachmentIds.map(Number),
        },
        model,
        modelId: {
          [this.db.Sequelize.Op.or]: [modelId, 0],
        },
        userId,
      },
    });

    const attachmentData = attachments.data;
    if (attachmentData) {
      const missingAttachmentIds = attachmentIds.filter(
        (id) =>
          !attachmentData.some((attachment) => attachment.id === Number(id)),
      );

      if (missingAttachmentIds.length > 0) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Attachments not found with ID(s) ${missingAttachmentIds.join(', ').trim()}.`,
        });
      }
    } else {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Attachments missing.',
      });
    }
  }

  async deleteSingleAttachment(attachmentId) {
    const existingAttachment = await this.attachmentRepository.getById(
      parseInt(attachmentId),
    );

    if (!existingAttachment) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Attachment not found',
      });
    }

    await this.attachmentRepository.destroy({
      id: existingAttachment.id,
    });
  }

  async markAttachmentAsSeen(model, modelId, userFromToken) {
    const { data: existingAttachmentsByModule } =
      await this.attachmentRepository.findAll({
        where: {
          model,
          modelId,
        },
        include: [
          {
            association: 'badges',
            required: false,
          },
        ],
      });

    if (existingAttachmentsByModule.length === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Attachments related to  "${model}" with an id ${modelId} was not found.`,
      });
    }

    const unseenAttachments = existingAttachmentsByModule.filter(
      (attachment) => {
        return attachment.badges.length === 0;
      },
    );

    try {
      await this.attachmentBadgeRepository.bulkCreate(
        unseenAttachments.map((unseenAttachment) => ({
          userId: parseInt(userFromToken.id),
          attachmentId: unseenAttachment.id,
        })),
      );

      return {
        message: `Attachments related to ${model} with id of ${modelId} was marked as seen`,
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = AttachmentService;
