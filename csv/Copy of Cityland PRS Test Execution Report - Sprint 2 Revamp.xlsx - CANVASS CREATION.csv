Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,STATUS,Remarks,Assign,Defects
Multiple Canvass Sheet,,,,,,,,,,,,
PRS-1300-001,Multiple Canvass Sheet,Critical,Verify creation and view of multiple Canvass Sheets under one Requisiiton Slip.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.","1. Navigate to an approved RS.
2. Create multiple canvass sheet.",,2. Each canvass sheet is created and listed under Related Document > Canvasses Tab,Me-Ann_Sprint1_Test Result,Passed,,Me-Ann,
PRS-1300-002,Multiple Canvass Sheet,High,Verify that the Canvass Sheet Number auto-increments correctly and follows the proper format upon Submission.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. Ensure that at least one Canvass Sheet has already been created.","1. Create a new canvass sheet.
2. Click ""Submit"" button.
3. Observe the generated canvass sheet number.",,3. The generated Canvass Sheet Number should follow the format: CS-[Company Code][AA-ZZ][8-digit number]. ,,Passed,,Me-Ann,
PRS-1300-003,Multiple Canvass Sheet,High,Verify that the Canvass Sheet Number auto-increments correctly and follows the proper format upon Saving as Draft.,,"1. Create a new canvass sheet.
2. Click ""Save as Draft"" button.
3. Observe the generated canvass sheet number.",,3. The generated Canvass Sheet Number should follow the format: CS-TMP-[Company Code][AA-ZZ][8-digit number]. ,,Passed,,Me-Ann,
PRS-1300-004,Multiple Canvass Sheet,Critical,Verify another creation of Canvass Sheet if total supplier quantity is less than requested quantity.,"1. Type of Request: OFM, OFM TOM, Non-OFM, and Non-OFM TOM
2. Requisition Slip is approved and assigned to a Purchasing Staff.
3. Log in as the assigned Purchasing Staff.
4. Ensure that at least one Canvass Sheet has already been created.
5. Ensure that the total supplier quantity in the Canvass Sheet is less than the requested quantity.","1. Create a new canvass sheet.
2. Click ""Submit"" button or ""Save as Draft"".",,2. User should be required to create another Canvass Sheet to fulfill the whole request.,,Passed,"OFM : Passed / http://**************/app/requisition-slip/692

Non-OFM:  Passed / http://**************/app/requisition-slip/704

OFM Transfer: Passed / http://**************/app/requisition-slip/705

Non-OFM Transfer: Passed / http://**************/app/requisition-slip/707",Me-Ann,
PRS-1300-005,Multiple Canvass Sheet,Critical,Verify fail further submission if total supplier quantity is equal to requested quantity.,"1. Type of Request: OFM, OFM TOM, Non-OFM, and Non-OFM TOM
2. Requisition Slip is approved and assigned to a Purchasing Staff.
3. Log in as the assigned Purchasing Staff.
4. Ensure that a Canvass Sheet has already been created.
5. Ensure that the total supplier quantity in the Canvass Sheet matches the requested quantity.","1. Create a new canvass sheet.
2. Click ""Submit"" button or ""Save as Draft"".",,2. User should not be required to create another Canvass Sheet.,,Passed,"OFM : Passed / http://**************/app/requisition-slip/692

Non-OFM:  Passed / http://**************/app/requisition-slip/704

OFM Transfer: Passed / http://**************/app/requisition-slip/705

Non-OFM Transfer: Passed / http://**************/app/requisition-slip/707",Me-Ann,
PRS-1300-006,Multiple Canvass Sheet,Critical,Verify allowed submission if total quantity exceeds requested quantity.,"1. Type of Request: Non-OFM & Non-OFM TOM
2. Requisition Slip is approved and assigned to a Purchasing Staff.
3. Log in as the assigned Purchasing Staff.
4. Ensure that the total supplier quantity in the Canvass Sheet matches the requested quantity.
","1. Create a new canvass sheet.
2. Click ""Submit"" button Click ""Submit"" button or ""Save as Draft"".
3. Create another canvass sheet and click submit or save draft.",,"2. User should be able to create the Canvass Sheet
3. User should not be able to create another canvass sheet.",,Not Started,"Non-OFM:  Failed / http://**************/app/requisition-slip/704

Non-OFM Transfer: Failed / http://**************/app/requisition-slip/707 


Bug Ticket:
https://youtrack.stratpoint.com/issue/CITYLANDPRS-1552",Me-Ann,
PRS-1300-007,Multiple Canvass Sheet,High,Verify that clicking the Canvass No. hyperlink redirect to Canvass Details page.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. Navigate to Canvasses Tab.
4. Ensure that at least one Canvass Sheet has already been created.","1. Click the Canvass No.
2. Verify system behavior.",,2. User should redirect to that Canvass Sheet detail page.,,Passed,,Me-Ann,
PRS-1300-008,Multiple Canvass Sheet,Critical,Verify changes in status through the proper workflow.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. Navigate to Canvasses Tab.","1. Create a new Canvass Sheet.
2. Verify the Canvass Status after saving it as a draft.
3. Verify the Canvass Status after submitting the sheet.
4. Verify the Canvass Status after it is rejected.
5. Verify the Canvass Status after it is fully approved.",,"Status updates correctly with the following visual indicators:

2. CS Draft
 • Background Color: #5F636833
 • Text Color: #5F6368

3. For CS Approval
 • Background Color: #F0963D33
 • Text Color: #F0963D

4. CS Rejected
 • Background Color: #DC433B33
 • Text Color: #DC433B

5. CS Approved
 • Background Color: #1EA52B33
 • Text Color: #1EA52B",,Not Started,Not yet scope for sprint 1,,
PRS-1300-009,Multiple Canvass Sheet,Minor,Verify that pagination in Canvasses Tab  work properly.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents > Canvasses tab.
4. Ensure that at least 11 Canvass Sheets are displayed.","1. Click the next (“>”) or previous (“<”) page buttons, or select a specific page number.
2. Verify that the system correctly loads and displays the corresponding set of Canvass Sheets.",,2. Canvass Sheet list/data should update correctly based on the selected page.,,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1550,,
PRS-1300-010,Multiple Canvass Sheet,Minor,Verify Column sorting functions works properly.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents > Canvasses tab.
4. Ensure that there are different update dates, approver, and status.","1. Click on a column header in the Canvass Sheet list.
2. Verify that the sorting functionality works correctly.",,3. The list should sort accordingly based on the selected column.,,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1551,,
PRS-1300-011,Multiple Canvass Sheet,Minor,Verify Tab navigation work correctly.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents.","1. Click each tab.
2. Verify system respond for each tab.",,"2. The system should display the correct data for each tab.

",,Passed,,,
PRS-1300-012,Multiple Canvass Sheet,Minor,Verify Canvass Sheet Number is displayed on top of RS Number.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents.","1. Click Canvass No.
2. Verify Canvass Sheet Number is displayed on canvass details page.",,"2. The Canvass Sheet Number should be clearly displayed at the top of the page, above the RS Number.",,Passed,,,
PRS-1300-013,Multiple Canvass Sheet,Minor,Verify RS Number in Canvass Tab matches the RS Number rin Canvass Sheet details page.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents.","1. Click Canvass No.
2. Verify Canvass Sheet Number on canvass details page.",,2. The Canvass Sheet number on the Canvasses Tab should match the number shown on the Canvass Details page.,,Passed,,,
PRS-1300-014,Multiple Canvass Sheet,Minor,Verify that amount field accepts only 2 decimal places.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents Canvasses tab > Canvass sheet detail page.","1. Fill in the amount field.
2. Verify that the amount field accepts only two decimal places.",,2. The field should accept and display only up to two decimal places.,,Passed,,,
PRS-1300-015,Multiple Canvass Sheet,Minor,Verify Column displayed and sorting function in Canvass Sheet details page.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents Canvasses tab > Canvass sheet detail page.","1. Verify the columns displayed on the Item table.
2. Verify that the sorting functionality works correctly.",,"1. The Item table should display the correct columns as follows:
     - Item
     - Unit
     - Requested Qty
     - Approved Qty
     - Remaining GFQ
     - Actions

2. The list should sort accordingly based on the selected column.",,Passed,,,
PRS-1300-016,Multiple Canvass Sheet,Minor,"Verify the ""Go Back"" button redirects to Canvass Tab in Related Documents.","1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Related Documents Canvasses tab > Canvass sheet detail page.","1. Click the ""Go Back"" button.",,1. User should be able to redirect back at the Canvasses Tab.,,Passed,,,
PRS-1300-017,Multiple Canvass Sheet,Critical,Verify quantity validation for Selected Supplier quantity less than Requested Quantity.,"1. Type of Request: OFM, OFM TOM, Non-OFM, and Non-OFM TOM
2. Requisition Slip is approved and assigned to a Purchasing Staff.
3. Log in as the assigned Purchasing Staff.
4. Ensure that the selected supplier quantity is less than Requested quantity.","1. Create a new Canvass Sheet.
2. Verify the system's recomputation.
3. Create another Canvass Sheet.
",,"2. The system should recompute or add the remaining Quantity to be fulfilled.
3. The system should require the User to create another Canvass Sheet to fulfill the entire Request.","Aira_Results,
Verna_Sprint1_Test Result",Passed,"OFM : Passed
http://**************/app/requisition-slip/729
Non-OFM:  Passed
http://**************/app/requisition-slip/728
OFM Transfer: Passed
http://**************/app/requisition-slip/731
Non-OFM Transfer: Passed
http://**************/app/requisition-slip/725","Aira, Verna",
PRS-1300-018,Multiple Canvass Sheet,Critical,Verify creation of canvass sheet if the selected Supplier Quantity is equal to the Requested Quantity.,"1. Type of Request: OFM, OFM TOM, Non-OFM, and Non-OFM TOM
2. Requisition Slip is approved and assigned to a Purchasing Staff.
3. Log in as the assigned Purchasing Staff.
4. Ensure that the selected supplier quantity is equal to Requested quantity.","1. Create another canvass sheet.
2. Click ""Submit"" button.",,2. The system should not require the User to create another Canvass Sheet.,,Passed,"OFM : Passed
http://**************/app/requisition-slip/729
Non-OFM:  Passed
http://**************/app/requisition-slip/728
OFM Transfer: Passed
http://**************/app/requisition-slip/731
Non-OFM Transfer: Passed
http://**************/app/requisition-slip/725","Aira, Verna",
PRS-1300-019,Multiple Canvass Sheet,Critical,Verify creation of canvass sheet if the selected Supplier Quantity exceeds the Requested Quantity.,"1. Type of Request: Non-OFM, and Non-OFM TOM
2. Requisition Slip is approved and assigned to a Purchasing Staff.
3. Log in as the assigned Purchasing Staff.
4. Ensure that the selected supplier quantity is greater than the Requested quantity.","1. Create another canvass sheet.
2. Click ""Submit"" button.",,2. The system should not require the User to create another Canvass Sheet.,,Passed,"OFM : Passed
http://**************/app/requisition-slip/729
Non-OFM:  Passed
http://**************/app/requisition-slip/728
OFM Transfer: Passed
http://**************/app/requisition-slip/731
Non-OFM Transfer: Passed
http://**************/app/requisition-slip/725","Aira, Verna",
PRS-1300-020,Multiple Canvass Sheet,Critical,Verify supplier quantity tracking across multiple Canvass Sheet.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.","1. Create the first Canvass Sheet with a selected Supplier Quantity.
2. Create a second Canvass Sheet with a different Supplier Quantity.
3. Verify that the system correctly tracks the Supplier Quantity across multiple canvass sheets.",,"3. The system should properly aggregate or display the Supplier Quantity across Canvass Sheets, with accurate tracking of total quantities fulfilled and remaining quantities.",,Not Started,,"Aira, Verna",
PRS-1300-021,Multiple Canvass Sheet,Critical,Verify 'Next Approver' column displays correct user.,"1. Requisition Slip is approved and assigned to a Purchasing Staff.
2. Log in as the assigned Purchasing Staff.
3. User is on Canvass sheet detail page.
4. One approver has already approved.","1. Observe ""Next Approver"" on Canvassed tab.",,"1. The ""Next Approver"" column should display the user correctly.",,Passed,"OFM : Passed
http://**************/app/requisition-slip/729
Non-OFM:  Passed
http://**************/app/requisition-slip/728
OFM Transfer: Passed
http://**************/app/requisition-slip/731
Non-OFM Transfer: Passed
http://**************/app/requisition-slip/725","Aira, Verna",
Entering of OFM Quantity per Supplier,,,,,,,,,,,,
PRS-1300-023,Entering of OFM Quantity per Supplier,High,Verify if User can click Enter Canvass option in Select Actions,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button",,3. Enter Canvas button is verified in Select Actions,,Not Started,,,
PRS-1300-024,Entering of OFM Quantity per Supplier,High,Verify the Selection of items for Canvassing,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s",,4. Items are visible and available for the User,,Not Started,,,
PRS-1300-025,Entering of OFM Quantity per Supplier,Critical,Verifiy if User is able to select Supplier per item in Canvassing,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Select Item/s in the list
8. Click Enter Canvass
9. Select Supplier",,9. User is able to select Supplier per item,,Not Started,,,
PRS-1300-026,Entering of OFM Quantity per Supplier,Critical,"Verify if the Quantity entered per Supplier is greater than to the Quantity Requested or Remaining Quantity for Canvassing
[Negative Scenario]","1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Submit Canvass",,8 Canvass should not be able to save draft and submit canvass,,Not Started,,,
PRS-1300-027,Entering of OFM Quantity per Supplier,Critical,Verify if the Quantity entered per Supplier is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when save as canvass draft,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Save as draft",,8. User can enter quantity per Supplier more than or equal to the Quantity Requested,,Not Started,,,
PRS-1300-028,Entering of OFM Quantity per Supplier,Critical,Verify if the Quantity entered per Supplier is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when submitting canvass,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Submit Canvass",,8. Canvass creation should proceed,,Not Started,,,
PRS-1300-029,Entering of OFM Quantity per Supplier,Critical,"Verify if Quantity is more than 3 Decimal Places in Canvass Creation
[Negative Scenario]","1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Input more than 3 decimal places quantity",,9. Canvass creation should not proceed if quantity is more than 3 decimal places,,Not Started,,,
PRS-1300-030,Entering of OFM Quantity per Supplier,High,Verify if Quantity is only at 3 Decimal Places in Canvass Creation,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. User can input maximum of 3 Decimal Places for Quantity,,Not Started,,,
PRS-1300-031,Entering of OFM Quantity per Supplier,High,"Verify if Quantity is more than 3 Decimal Places Steelbars item
[Negative Scenario]","1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Input more than 3 decimal places quantity",,9. Canvass creation should not proceed if quantity is more than 3 decimal places,,Not Started,,,
PRS-1300-032,Entering of OFM Quantity per Supplier,High,Verifiy if decimal is allowed and up to 3 decimal in the quantity per Supplier is available in SteelBars,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. Decimal is allowed in steelbars and up to 3 decimal,,Not Started,,,
PRS-1300-033,Entering of OFM Quantity per Supplier,High,Verify if the Quantity entered per Supplier in Steel bars is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when save as canvass draft,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Save as draft",,10. User can enter quantity per Supplier more than or equal to the Quantity Requested,,Not Started,,,
PRS-1300-034,Entering of OFM Quantity per Supplier,High,Verify if the Quantity entered per Supplier in Steel bars is less than or equal to the Quantity Requested or Remaining Quantity for Canvassing when submit,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submit",,10. Canvass creation should proceed,,Not Started,,,
PRS-1300-035,Entering of OFM Quantity per Supplier,High,"Verify if the Maximum Discounts and Prices are more than 2 Decimal Places 
[Negative Scenario]","1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. Discounts and Prices should not be more than 2 decimal places.,,Not Started,,,
PRS-1300-036,Entering of OFM Quantity per Supplier,Minor,Verify if the Maximum Discounts and Prices are in 2 Decimal Places ,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. Maximum 2 Decimal in Discounts and Prices should not be more than 2 decimal places,,Not Started,,,
PRS-1300-037,Entering of OFM Quantity per Supplier,High,Verify if Canvassing is allowed if the Supplier Quantity is less or equal to the Requested Quantity,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Enter Quantity less than requested quantity",,9. Canvassing should only allow if the Supplier Quantity is less or equal to the Requested Quantity,,Not Started,,,
PRS-1300-038,Entering of OFM Quantity per Supplier,Critical,Verify Canvass draft open when Item quantity that was approved and has been completed but there is still a draft cs with the same item,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Save as draft
11. Open Canvass draft",,11. User should be able to open the drafted Canvass Sheet,,Not Started,,,
PRS-1300-039,Entering of OFM Quantity per Supplier,High,Verify the retain same Item that was completed on other Canvass Sheets,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Save as draft
11. Open the other Canvass draft",,11. Should retain the same Item that was completed on other Canvass Sheets,,Not Started,,,
PRS-1300-040,Entering of OFM Quantity per Supplier,Minor,Verify if a display Warning Modal when the Canvass Sheet will be Submitted,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,"10. A display warning modal is expected to show when submitting Canvass sheet
a. This warning will tell the User that the Item will be removed as this has been completed to a different Canvass Sheet",,Not Started,,,
PRS-1300-041,Entering of OFM Quantity per Supplier,Minor,Verify display of a Toast Message that it was successfully Submitted and the Item is removed,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,10. A message is expected to display when an item is successfully submited and removed,,Not Started,,,
PRS-1300-042,Entering of OFM Quantity per Supplier,Critical,Verify if User is allowed to do CS Approve when item quantity that was approved has been completed but there is still an ongoing Canvass sheet with the same item ,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,10. User is expected to be able to do CS Approval ,,Not Started,,,
PRS-1300-043,Entering of OFM Quantity per Supplier,Minor,Verify the display Error Message that the Item has been completed and the Approval will not be allowed to proceed,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,"10. Should display an Error Message that the Item has been completed and the Approval will not be allowed to proceed.
a. Approver's Action is to only Reject the Canvass Sheet",,Not Started,,,
PRS-1300-044,Entering of OFM Quantity per Supplier,High,Verify the required Assigned Purchasing Staff to edit the Canvass Sheet,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,"10. Should require the Assigned Purchasing Staff to edit the Canvass Sheet
a. Remove Option will be displayed under Actions Button only for the Item/s that was Completed.
b. Require removal of the Item",,Not Started,,,
PRS-1300-045,Entering of OFM Quantity per Supplier,High,Verify resubmitting and repeating the Approval Process for the Approver that has rejected the Canvass Sheet,"1. Requisition Slip has been fully Approved
    a. Type of Request must be OFM or OFM Transfer of Materials
2. Canvass Sheet is created but rejected","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,10. User should be able to resubmit and repeat the Approval process,,Not Started,,,
Entering of Non OFM Quantity per Supplier,,,,,,,,,,,,
PRS-1300-035,Entering of Non OFM Quantity per Supplier,High,Verify if User can click Enter Canvass option in Select Actions,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button",,3. Enter Canvas button is verified in Select Actions,,Not Started,"NON-OFM
NON-OFM TRANSFER


RS-01AA00000271",,
PRS-1300-036,Entering of Non OFM Quantity per Supplier,HIgh,Verify the Selection of items for Canvassing,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s",,4. Items are visible and available for the User,,Not Started,,,
PRS-1300-037,Entering of Non OFM Quantity per Supplier,Critical,Verifiy if User is able to select Supplier per item in Canvassing,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Select Item/s in the list
8. Click Enter Canvass
9. Select Supplier",,9. User is able to select Supplier per item,,Not Started,,,
PRS-1300-038,Entering of Non OFM Quantity per Supplier,Critical,Verify if the Quantity entered per Supplier is less than or more than to the Quantity Requested or Remaining Quantity for Canvassing when save as draft,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Verify the Quantity",,8. User can enter quantity per Supplier less than or more than the Quantity Requested,,Not Started,,,
PRS-1300-039,Entering of Non OFM Quantity per Supplier,Critical,Verify if the Quantity entered per Supplier is less than or more than to the Quantity Requested or Remaining Quantity for Canvassing when submitting canvas,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Input Quantity
9. Submit Canvass",,9. Canvass should proceed when quantity per Supplier less than or more than the Quantity Requested,,Not Started,,,
PRS-1300-040,Entering of Non OFM Quantity per Supplier,Critical,"Verify if Quantity is more than 3 Decimal Places in Canvass Creation
[Negative Scenario]","1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Input more than 3 decimal places quantity",,9. Canvass creation should not proceed if quantity is more than 3 decimal places,,Not Started,,,
PRS-1300-041,Entering of Non OFM Quantity per Supplier,HIgh,Verify if Quantity is only at 3 Decimal Places in Canvass Creation,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. User can input maximum of 3 Decimal Places for Quantity,,Not Started,,,
PRS-1300-042,Entering of Non OFM Quantity per Supplier,High,Verify if Canvassing is allowed even if the Supplier Quantity is equal or greater than to the Requested Quantity,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Enter Quantity equal or more than requested quantity",,7. User can enter quantity per Supplier more than or equal to the Quantity Requested,,Not Started,,,
PRS-1300-043,Entering of Non OFM Quantity per Supplier,High,"Verify if the Maximum Discounts and Prices are more than 2 Decimal Places 
[Negative Scenario]","1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. Discounts and Prices should not be more than 2 decimal places.,,Not Started,,,
PRS-1300-044,Entering of Non OFM Quantity per Supplier,Minor,Verify if the Maximum Discounts and Prices are in 2 Decimal Places ,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity",,9. Maximum 2 Decimal in Discounts and Prices should not be more than 2 decimal places,,Not Started,,,
PRS-1300-045,Entering of Non OFM Quantity per Supplier,High,Verify if highlight in Requested Quantity is visible if one of the Suppliers has a Higher Quantity than Requested,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Enter Quantity equal or more than requested quantity",,9. Highlight in Requested Quantity is not verified,,Not Started,,,
PRS-1300-046,Entering of Non OFM Quantity per Supplier,Critical,Verify Canvass draft open when Item quantity that was approved and has been completed but there is still a draft cs with the same item,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Save as draft
11. Open Canvass draft",,11. User should be able to open the drafted Canvass Sheet,,Not Started,,,
PRS-1300-047,Entering of Non OFM Quantity per Supplier,High,Verify the retain same Item that was completed on other Canvass Sheets,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Save as draft
11. Open the other Canvass draft",,11. Should retain the same Item that was completed on other Canvass Sheets,,Not Started,,,
PRS-1300-048,Entering of Non OFM Quantity per Supplier,Minor,Verify if a display Warning Modal when the Canvass Sheet will be Submitted,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,"10. A display warning modal is expected to show when submitting Canvass sheet
a. This warning will tell the User that the Item will be removed as this has been completed to a different Canvass Sheet",,Not Started,spillover sprint 2,,
PRS-1300-049,Entering of Non OFM Quantity per Supplier,Minor,Verify display of a Toast Message that it was successfully Submitted and the Item is removed,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,10. A message is expected to display when an item is successfully submited and removed,,Not Started,,,
PRS-1300-050,Entering of Non OFM Quantity per Supplier,Critical,Verify if User is allowed to do CS Approve when item quantity that was approved has been completed but there is still an ongoing Canvass sheet with the same item ,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,10. User is expected to be able to do CS Approval ,,Not Started,,,
PRS-1300-051,Entering of Non OFM Quantity per Supplier,Minor,Verify the display Error Message that the Item has been completed and the Approval will not be allowed to proceed,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,"10. Should display an Error Message that the Item has been completed and the Approval will not be allowed to proceed.
a. Approver's Action is to only Reject the Canvass Sheet",,Not Started,spillover sprint 2,,
PRS-1300-052,Entering of Non OFM Quantity per Supplier,High,Verify the required Assigned Purchasing Staff to edit the Canvass Sheet,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is to be created","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,"10. Should require the Assigned Purchasing Staff to edit the Canvass Sheet
a. Remove Option will be displayed under Actions Button only for the Item/s that was Completed.
b. Require removal of the Item",,Not Started,,,
PRS-1300-053,Entering of Non OFM Quantity per Supplier,High,Verify resubmitting and repeating the Approval Process for the Approver that has rejected the Canvass Sheet,"1. Requisition Slip has been fully Approved
    a. Type of Request must be Non-OFM or Non-OFM Transfer of Materials
2. Canvass Sheet is created but rejected","1. Select an RS
2. Navigate to Select Actions Modal
3. Verify the Enter Canvass button
4. Click Add item/s
5. Select item/s in the list
6. Click Enter Canvass
7. Create Canvass
8. Populate the requirements
9. Verify the Quantity
10. Submitt canvass",,10. User should be able to resubmit and repeat the Approval process,,Not Started,CITYLANDPRS-1536,,
[CANVASS CREATION] Update Table Behavior for Canvass Sheet[for Create and Viewing],,,,,,,,,,,,
PRS-1315-001,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify that Account Code, Supplier, and Canvass Status columns are removed from the Items Table","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click Canvass Sheet Number
2. Observe the Items Table",,"1. Should update the Items Table Column
2. Should remove the 
    a. Account Code Column
    b. Supplier Column
    c. Canvass Status Column
        i. Should be implemented in Canvass Creation, Canvass Viewing, Approver View of Canvass",Me-Ann_Sprint1_Test Result,Passed,,,
PRS-1315-002,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify that Canvass Status column removed from Items Table in Canvass Creation, Viewing, and Approver View","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Go to Canvass Creation, Canvass Viewing, Approver View of Canvass
2. Check Canvass Status Column",,"2. Canvass Status Column
        i. Should be implemented in Canvass Creation, Canvass Viewing, Approver View of Canvass",,Passed,,,
PRS-1315-003,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify that full item names are displayed in the Item column,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Check the Item Name displayed in the Item Column",,2. Should display the Full Item name in the Item Column,,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1494,,
PRS-1315-004,Update Table Behavior for Canvass Sheet[for Create and Viewing],Critical,"Verify the presence of Item Name, Unit, Requested Qty, Approved Qty, Remaining GFQ, and Actions","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Observe all the columns in Items Table",,"2 Should have the following Columns:
    a. Item Name
    b. Unit
    c. Requested Qty
    d. Approved Quantity
    e. Remaining GFQ
    f. Actions
      ",,Passed,,,
PRS-1315-005,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Item Name column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2 Observe sorting of Item Name
       a. Sort by 0-9, A-Z || 9-0, Z-A || Default
",,"2. Item Name sorts accordingly
       a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default",,Passed,,,
PRS-1315-006,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Unit column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2 Observe sorting of Unit
    ii. Unit
       a. Sort by A-Z || Z-A || Default
",,"2. Unit sorts accordingly:
    ii. Unit
       a.Should be sorted by A-Z || Z-A || Default",,Passed,,,
PRS-1315-007,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Requested Qty column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2  Observe sorting of Requested Qty
    iii. Requested Qty
       a.Sort by 0-9, 9-0 || Default
",,"2. Requested Qty sorts accordingly:
    iii. Requested Qty
       a.Should be sorted by 0-9, 9-0 || Default
",,Passed,,,
PRS-1315-008,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Approved Quantity of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2 Observe sorting of Approved Quantity
    iv. Approved Quantity
       a.Sort by 0-9, 9-0 || Default
",,"2. Approved Quantity sorts accordingly:
    iv. Approved Quantity
       a.Should be sorted by 0-9, 9-0 || Default",,Passed,,,
PRS-1315-009,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Remaining GFQ column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe sorting of Approved Quantity
    v. Remaining GFQ
        a.Sort by 0-9, 9-0 || Default
",,"2. Remaining GFQ sorts accordingly:
    v. Remaining GFQ
        a.Should be sorted by 0-9, 9-0 || Default",,Passed,,,
PRS-1315-010,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that Approved Quantity shows ""---"" when not yet approved","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Check Approved Quantity column",,"2. Should display as ""---"" if not yet Approved by any of the Approvers",,Passed,,,
PRS-1315-011,Update Table Behavior for Canvass Sheet[for Create and Viewing],Mnor,"Verify Remaining GFQ column shows ""---"" for Non-OFM and Non-OFM Transfer of Materials request types","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Check Remaining GFQ Column",,"2.  For OFM and OFM Transfer of Materials Type of Request
      ii. Should display as ""---"" fo Non-OFM and Non-OFM Transfer of Materials",,Passed,,,
PRS-1315-012,Update Table Behavior for Canvass Sheet[for Create and Viewing],Citical,Verify Enter Canvass button displays Enter Canvass Modal if there are no suppliers,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click ""Enter Canvass"" Button under Actions for Item without Suppliers yet
2. Validate display of Enter Canvass Modal",,"2. If Enter Canvass Button is clicked
    a) If without Suppliers yet, should display Enter Canvass Modal",,Passed,https://youtrack.stratpoint.com/issue/CITYLANDPRS-1524,,
PRS-1315-013,Update Table Behavior for Canvass Sheet[for Create and Viewing],Critical,Verify Enter Canvass button displays View Modal if suppliers already exist,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click ""Enter Canvass"" Button under Actions for Item with Suppliers
2. Validate display View Modal that will allow Editing of the Suppliers",,"2. If Enter Canvass Button is clicked
    b) If with Suppliers, should display View Modal that will allow Editing of the Suppliers",,Passed,,,
PRS-1315-014,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify that clicking ""View Purchase History"" opens a modal with correct data","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click ""View Purchase History""
2. Check modal column fields",," 2. If View Purchase History is clicked
         a) Should display a Modal that contains the following
                   1) RS Number
                        a. RS Numbers that has used the Item in their Requests
                   2) Supplier
                        a. Supplier of the Item per Requisition Slip
                   3) Price Per Unit
                        a. Price declared for the Item
                   4) Qty
                        a. Requested Quantity for the Item
                   5) Date Purchased
                        a1. Date that the Pruchase Order for the Item has been Approved
",,Passed,,,
PRS-1315-015,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify RS Number column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if RS Number column sorts accordingly:
    i) RS Number
        a. Sort by 0-9, 9-0 || Default ",,"2. RS Number column sorts accordingly:
    i) RS Number
        a. Should be sorted by 0-9, 9-0 || Default",,Not Started,"https://youtrack.stratpoint.com/issue/CITYLANDPRS-1534
 
04/24 : Devs : currently , mock data and for now is the sorting is not working",,
PRS-1315-016,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Supplier column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Supplier sorts accordingly:
    ii) Supplier
        a. Sort by 0-9, A-Z || 9-0, Z-A || Default ",,"2. Supplier column sorts accordingly:
    ii) Supplier
        a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default
",,Not Started,,,
PRS-1315-017,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Price Per Unit column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Price Per Unit column sorts accordingly:
    iii) Price Per Unit
        a. Sort by 0-9, 9-0 || Default",,"2. Price Per Unit column sorts accordingly:
    iii) Price Per Unit
        a. Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1315-018,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Qty column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Qty column sorts accordingly:
    iv) Qty
        a. Sort by 0-9, 9-0 || Default",,"2. Qty column sorts accordingly:
    iv) Qty
        a. Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1315-019,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Date Purchased column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Date Purchased column sorts accordingly:
    v) Date Purchased
       a. Sort by Oldest Date-Latest Date, Latest Date-Oldest Date || Default",,"2. Date Purchased column sorts accordingly:
    v) Date Purchased
       a. Should be sorted by Oldest Date-Latest Date, Latest Date-Oldest Date || Default",,Not Started,,,
PRS-1315-020,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that ""---"" is displayed for no Supplier","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Observe no values for Supplier
2. Validate values can be displayed as ""---""",,"2. Values can be displayed as
    ii) Supplier
        a. Can be displayed as ""---""
",,Not Started,,,
PRS-1315-021,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that ""---"" is displayed for no Price Per Unit","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Observe no values for Price per Unit
2. Validate values can be displayed as ""---""",,"2. Values can be displayed as
    iii) Price Per Unit
        a. Can be displayed as ""---""
",,Not Started,,,
PRS-1315-022,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that ""---"" is displayed for no Date Purchased values","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Observe no values for Date Purchased
2. Validate values can be displayed as ""---""",,"2. Values can be displayed as
    v) Date Purchased
       a. Can be displayed as ""---""
",,Not Started,,,
PRS-1315-023,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,Verify only 10 rows of data are shown per page in Purchase History modal,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open Purchase History modal
2. Validate pagination",,2. Should display 10 Rows of Data per Page,,Passed,,,
PRS-1315-024,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify that changes to the Items Table are also applied to Steelbars,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open Canvass with Steelbar items
2. Verify columns and behavior",,"2. Should implement the changes to Steelbars
",,Passed,,,
PRS-1315-025,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify clicking the whole row expands Supplier Summary View,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click any item row
2. Check if it expands and shows Suppliers Summary View",,2. Should click the whole row for the Item to expand the Suppliers Summary View,,Passed,,,
PRS-1315-026,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify that supplier names are shown instead of generic labels in Supplier Summary View,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click any item row
2. Check if Supplier's Names are displayed instead of Supplier 1, etc.",,"2. Should display the Supplier's Names instead of Supplier 1, etc.",,Passed,,,
PRS-1315-027,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,Verify that the Items Table adjusts its spacing for single item,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open RS or Enter Canvass Sheet with one item
2. Observe table spacing",,"2. Should view the Requisition Slip, and Enter a Canvass Sheet
     a. Observe the current Items Table, even if the Requested Item is only one Item, the Spacing on the Items Table is using too much space",,Passed,,,
PRS-1315-028,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,Verify that the Items Table adjusts properly for multiple items,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open RS or Enter Canvass Sheet with multiple items
2. Observe table spacing",,2. Should adjust the Items Table sizing depending on the selected Number of Items,,Passed,,,
[CANVASS - CREATION] Canvass Status updates,,,,,,,,,,,,
PRS-1495-001,Canvass Status Updates,High,"Verify Canvass Status updates to ""CS Draft"" upon drafting a sheet.","1. Logged as any type of user.
2. Canvass Sheet created.","1. Draft a Canvass Sheet.
2. Save the draft.",,"2. Status through Related Document, Canvass Sheet and Request History should update to:

a. CS Draft
BG - #5F636833
Text - #5F6368",Verna Sprint 2 Test Results,Passed,,Verna,
PRS-1495-002,Canvass Status Updates,High,"Verify Canvass Status updates to ""For CS Approval"" upon submission.","1. Logged as any type of user.
2. Canvass Sheet drafted.",1. Submit the drafted Canvass Sheet.,,"1. Status through Related Document, Canvass Sheet and Request History should update to:

b. For CS Approval
BG - #F0963D33
Text - #F0963D",,Failed,Correct canvass status are only updated on the Canvass Tab.,Verna,CITYLANDPRS-1687
PRS-1495-003,Canvass Status Updates,High,"Verify Canvass Status updates to ""CS Rejected"" if any approver rejects.","1. Logged in as the current approver.
2. Canvass Sheet submitted for approval.",1. Approver rejects the Canvass Sheet.,,"1. Status through Related Document, Canvass Sheet and Request History should update to:

c. CS Rejected
BG - #DC433B33
Text - #DC433B",,Failed,,Verna,
PRS-1495-004,Canvass Status Updates,High,"Verify Canvass Status updates to ""CS Approved"" when all approvers approve.","1. Logged in as the current approver.
2. Canvass Sheet submitted for approval.",1. All approvers approve the Canvass Sheet.,,"1. Status through Related Document, Canvass Sheet and Request History should update to:

d. CS Approved
BG - #1EA52B33
Text - #1EA52B",,Failed,,Verna,
PRS-1495-005,Canvass Status Updates,Critical,"Verify Canvass Sheet returns to ""For CS Approval"" after resubmission from rejection.","1. Logged in as the current approver.
2. Canvass Sheet rejected previously.",1. Resubmit the rejected Canvass Sheet.,,"1. Status through Related Document, Canvass Sheet and Request History should update to:

For CS Approval
BG - #F0963D33
Text - #F0963D",,Failed,,Verna,
PRS-1495-006,Canvass Status Updates,High,"Verify re-submission flow updates canvass approver status to ""Pending"".","1. Logged in as the current approver.
2. Canvass Sheet resubmitted after rejection.",1. Resubmit the rejected Canvass Sheet.,,"1. Approver Status updates to ""Pending"".",,Passed,,Verna,
[CANVASS - CREATION] Item Group Canvassing,,,,,,,,,,,,
PRS-1646-001,Item Group Canvassing,Critical,Verify checkbox column appears as the first column in the Table,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Go to Canvass Sheet and click the Steel Bars Tab
2. Observe the checkbox column in the Table",,2. Should add a Checkbox Column per Item as the first Column in the Table,,Not Started,,,
PRS-1646-002,Item Group Canvassing,Critical,Verify checkbox column appears only for Steelbar items,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Go to Canvass Sheet and click the Items Tab
2. Check if there is no column for checkbox",,2. Should only display the Checkbox Column for Steelbar Items,,Not Started,,,
PRS-1646-003,Item Group Canvassing,Critical,Verify checkbox is enabled if item has no entered Canvass,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Check Steelbar items with no Canvass
2. Observe Checkbox",,2. Should enable the Checkbox for the Item if it has no any Canvass Entered,,Not Started,,,
PRS-1646-004,Item Group Canvassing,Critical,Verify if user can select two or more items in the Table,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click two or more checkboxes
2. Validate that it allows selection of two or more Steelbar Items",,2. Should allow selection of two or more Items in the Table,,Not Started,,,
PRS-1646-005,Item Group Canvassing,Critical,"Verify selection of two or more items enables ""Item Group Canvass"" button","1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click two or more checkboxes
2. Check if the Item Group Canvass button enables",,"2. Should display a Button at the Top Left of the Table with a Label of Item Group Canvass
        i. Should only be enabled if the selected Checkbox is two or more",,Not Started,,,
PRS-1646-006,Item Group Canvassing,Critical,"Verify If  ""Item Group Canvass"" button is initially disabled when there is no or there is only one item selected","1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click only one checkbox 
2. Check if the Item Group Canvass button is initially disabled when there is no checkbox clicked and if there is only one selected",,2. Should be intially disabled when there is no checkbox selected and if only one checkbox is selected,,Not Started,,,
PRS-1646-007,Item Group Canvassing,Critical,"Verify ""Item Group Canvass"" page opens after clicking button","1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click the Item Group Canvass Button
2. Validate display of Item Group Canvass Page details",,2. Should display a Page for entering the Details of the Item Group,,Not Started,,,
PRS-1646-008,Item Group Canvassing,High,Verify fields displayed in Item Group Canvass page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Observe the Item Group Canvass Page
2. Check if it has the all the necessary fields for details",,"2.  Should have the following Details
        i. Supplier
        ii. Terms
        iii. Discount
        iv. Items Table",,Not Started,,,
PRS-1646-009,Item Group Canvassing,High,Verify Discount field allows negative sign,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input negative sign or value to the Discount field (e.g., -10)
2. Validate if it allows Negative sign input",,"2.   Discount
             i) Should allow entering of Negative Sign or - once Discount Percentage is clicked",,Not Started,,,
PRS-1646-010,Item Group Canvassing,High,Verify invalid placement of negative sign is not allowed,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input negative sign between numbers or at the end (e.g.,  10-500 or 100-)
2. Validate if it does not allow the input",,2.  Should not allow invalid placement of negative sign,,Not Started,,,
PRS-1646-011,Item Group Canvassing,High,Verify negative discount in Percentage increase the amount of the items included in the Item Group,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1.  Populate all the fields and input negative discount in Percentage
2. Validate if it will increase the Amounts of the Items included in the Item Group based on the negative Percentage entered",,"2.  Negative discount will increase the Amounts of the Items included in the Item Group
     Example: Discount to be entered: -10%
        - Item 1 Price: ₱100.00 -> ₱110.00
        - Item 2 Price: ₱200.00 -> ₱210.00
  ",,Not Started,,,
PRS-1646-012,Item Group Canvassing,High,Verify negative discount in Fixed Amount increase the amount of the items included in the Item Group,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1.  Populate all the fields and input negative discount in Fixed Amount
2. Validate if it will increase the Amounts of the Items included in the Item Group based on the negative Fixed Amount entered",,"2.  Negative discount will increase the Amounts of the Items included in the Item Group
     Example: Discount to be entered: -50
        - Item 1 Price: ₱100.00 -> ₱150.00
        - Item 2 Price: ₱200.00 -> ₱250.00
  ",,Not Started,,,
PRS-1646-013,Item Group Canvassing,Medium,Verify sorting of Item Name Column,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Observe the Items Table in the Item Group Canvass Page
2. Validate if the table is sorted properly",,"2.  Should be sorted Alphabetically by Item Name - Special Charactes + 0-9 + A-Z
  ",,Not Started,,,
PRS-1646-014,Item Group Canvassing,Critical,Verify Items Table displays selected items,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Check items in the Items Table in the Item Group Canvass Page
2. Validate if the items displayed are the selected items",,"2.  Should display the selected Items for the Item Group
  ",,Not Started,,,
PRS-1646-015,Item Group Canvassing,Medium,Verify if maximum of 10 items per Page is displayed,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Select more than 10 items and go to the Item Group Canvass Page
2. Check if the Items Table display 10 items per Page",,"2.  Should display 10 Items per Page
  ",,Not Started,,,
PRS-1646-016,Item Group Canvassing,Medium,Verify sorting of Item Column	,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Item Column
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                 a) Item - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default",,Not Started,,,
PRS-1646-017,Item Group Canvassing,Medium,Verify sorting of Unit Column,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Unit Column
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                  b) Unit - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default",,Not Started,,,
PRS-1646-018,Item Group Canvassing,Medium,Verify sorting of Quantity Column,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Quantity Column
2. Validate if sorting works properly and allow 3-Decimal Places",,"2.   Should have the following Columns and sorting per Column
                 c) Quantity - 0-9, 9-0 || Default
                     1) Should allow 3-Decimal Places",,Not Started,,,
PRS-1646-019,Item Group Canvassing,Medium,Verify sorting of Weight Column,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Weight Column
2. Validate if sorting works properly and allow 3-Decimal Places",,"2.   Should have the following Columns and sorting per Column
                 d) Weight - 0-9, 9-0 || Default
                     1) Should allow 3-Decimal Places",,Not Started,,,
PRS-1646-020,Item Group Canvassing,Medium,Verify sorting of Unit Price Column,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Unit Price Column
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                  e) Unit Price - 0-9, 9-0 || Default",,Not Started,,,
PRS-1646-021,Item Group Canvassing,Critical,Verify Unit Price allows only valid input,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input Numbers and Dot in the Unit Price fields
2. Validate if it allows Numbers and Dot as inputs",,"2.   Should allow Numbers and Dot Only
",,Not Started,,,
PRS-1646-022,Item Group Canvassing,Critical,Verify Unit Price does not allow invalid input,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input any characters except for Numbers and Dot in the Unit Price fields
2. Validate if it does not allow any characters except for Numbers and Dot as inputs",,"2.   Should not allow other characters except for Numbers and Dot 
",,Not Started,,,
PRS-1646-023,Item Group Canvassing,High,Verify Unit Price allows only 2-Decimal Places,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input amount with 2-Decimal Places in the Unit Price fields
2.Validate if it allows 2-Decimal Places inputs",,"2.   Should allow 2-Decimal Places
",,Not Started,,,
PRS-1646-024,Item Group Canvassing,High,Verify Unit Price does not allow more than 2-Decimal Places,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input amount withmore than  2-Decimal Places in the Unit Price fields
2.Validate if it does not allow more than 2-Decimal Places inputs",,"2.   Should not allow 2-Decimal Places
",,Not Started,,,
PRS-1646-025,Item Group Canvassing,Medium,Verify Peso sign placeholder in Unit Price,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. View Unit Price fields
2. Check if they have a Peso Sign Placeholder",,"2.   Should have a Peso Sign Placeholder
",,Not Started,,,
PRS-1646-026,Item Group Canvassing,Medium,Verify Maximum of 10 Character Input in Unit Price,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input 10 Characters or less in the Unit Price field
2. Validate if it accepts 10 characters or less",,"2.   Should have a Maximum of  10 Characters
",,Not Started,,,
PRS-1646-027,Item Group Canvassing,Medium,Verify Behavior when more than 10 Characters are entered in Unit Price,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Input more than 10 Characters or less in the Unit Price field
2. Validate if it does not accept more than 10 characters",,"2.   Should only accept maximum of  10 Characters
",,Not Started,,,
PRS-1646-028,Item Group Canvassing,Critical,Verify if Unit Price is a required Field,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Leave the Unit Price fields blank and Confirm
2. Validate if it requires input for the Unit Price fields",,"2.   Should require the Field
",,Not Started,,,
PRS-1646-029,Item Group Canvassing,High,Verify Cancel Button behavior in Item Group Canvass Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click the Cancel button in the Item Group Canvass Page
2. Click Continue upon display of Cancel Canvas Modal and validate if it returns to Canvass Form",,"2.   If Cancel Button is clicked, should display a Confirmation Modal
           i) Once Confirmed, should return to Canvass Form",,Not Started,,,
PRS-1646-030,Item Group Canvassing,High,Verify Confirm Button behavior in Item Group Canvass Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click the Confirm button in the Item Group Canvass Page
2. Click Submit upon display of Confirm Canvas Modal and validate if it applies changes to all the selected items",,"2.   If Confirm Button is clicked, should display a Confirmation Modal
           i) Once Confirmed, should apply the changes to all of the selected Items
              a. Selected Supplier
              b. Supplier Terms
              c. Discount
              d. Item Quantity 
              e. Item Unit Price",,Not Started,,,
PRS-1646-031,Item Group Canvassing,High,Verify Confirm Button behavior in Item Group Canvass Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click the Confirm button in the Item Group Canvass Page
2. Click Submit upon display of Confirm Canvas Modal and validate if it applies changes to all the selected items",,"2.   If Confirm Button is clicked, should display a Confirmation Modal
           i) Once Confirmed, should apply the changes to all of the selected Items
              a. Selected Supplier
              b. Supplier Terms
              c. Discount
              d. Item Quantity 
              e. Item Unit Price",,Not Started,,,
PRS-1646-032,Item Group Canvassing,High,Verify Negative discount is also applicable when creating a Canvass per Steelbar item,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click 3 dotted icon in any Steelbar Item and enter Canvass
2. Validate if it allows Negative sign input in Discount",,"2.  Should also include the Negative Discount when creating a Canvass per Item
    a. Should only be implemented for Steelbar Items
        i. Should allow entering of Negative Sign or - once Discount Percentage is clicked",,Not Started,,,
PRS-1646-033,Item Group Canvassing,High,Verify negative discount in Percentage increase the amount of the specific Steelbar item canvassed,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1.  Populate all the fields and input negative discount in Percentage
2. Validate if it will increase the Amount of the Item based on the negative Percentage entered",,"2.  Negative discount will increase the Amount of the Item
     Example: Item Price: ₱100.00 -> ₱110.00
  ",,Not Started,,,
PRS-1646-034,Item Group Canvassing,High,Verify if maximum of 10 items per Page is displayed in Canvass Sheet Items Table,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. More than 10 items was requested and go to the Canvass Sheet Page
2. Check if the Items Table in Canvass Sheet display 10 items per Page",,"2.  Should display 10 Items per Page
  ",,Not Started,,,
PRS-1646-035,Item Group Canvassing,Medium,Verify sorting of Item Column in Canvass Sheet Items Table     ,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Item Column
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                 i) Item - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default",,Not Started,,,
PRS-1646-036,Item Group Canvassing,Medium,Verify sorting of Unit Column in Canvass Sheet Items Table     ,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Unit Column in Canvass Sheet Items Table
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                 ii) Unit - Special Charactes + 0-9 + A-Z || Z-A + 9-0 + Special Characters || Default",,Not Started,,,
PRS-1646-037,Item Group Canvassing,Medium,Verify sorting of Requested Quantity Column in Canvass Sheet Items Table     ,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Requested Quantity Column  in Canvass Sheet Items Table
2. Validate if sorting works properly and allow 3-Decimal Places",,"2.   Should have the following Columns and sorting per Column
                 iii) Requested Quantity - 0-9, 9-0 || Default
                     a) Should allow 3-Decimal Places",,Not Started,,,
PRS-1646-038,Item Group Canvassing,Medium,Verify sorting of Weight Column in Canvass Sheet Items Table     ,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Weight Column  in Canvass Sheet Items Table
2. Validate if sorting works properly and allow 3-Decimal Places",,"2.   Should have the following Columns and sorting per Column
                 iv) Weight - 0-9, 9-0 || Default
                     a) Should allow 3-Decimal Places",,Not Started,,,
PRS-1646-039,Item Group Canvassing,Medium,Verify sorting of Approved Quantity Column in Canvass Sheet Items Table     ,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Approved Quantity Column in Canvass Sheet Items Table
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                 v) Approved Quantity - 0-9, 9-0 || Default
                     a) Should allow 3-Decimal Places",,Not Started,,,
PRS-1646-040,Item Group Canvassing,Medium,Verify sorting of Remaining GFQ Column in Canvass Sheet Items Table     ,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click sorting buttons for Remaining GFQ Column in Canvass Sheet Items Table
2. Validate if sorting works properly",,"2.   Should have the following Columns and sorting per Column
                 vi) Remaining GFQ - 0-9, 9-0 || Default
                     a) Should allow 3-Decimal Places",,Not Started,,,
PRS-1646-041,Item Group Canvassing,Critical,Verify changes not saved until Save Draft in Canvass Sheet Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click Save Draft in the Canvass Sheet after Item Group Canvassing
2. Validate if the changes has been saved",,2.   Should Save the changes when Save Draft Button in the Canvass Form is clicked,,Not Started,,,
PRS-1646-042,Item Group Canvassing,Critical,Verify changes not saved until Submit in Canvass Sheet Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click Save Draft in the Canvass Sheet after Item Group Canvassing
2. Validate if the changes has been saved",,2.   Should Save the changes when Submit Button in the Canvass Form is clicked,,Not Started,,,
PRS-1646-043,Item Group Canvassing,Critical,Verify changes not saved when Cancel button is clicked in Canvass Sheet Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click Cancel button in the Canvass Sheet after Item Group Canvassing
2. Validate if the changes has not been saved",,2.   Should not save the changes when Cancel Button in the Canvass Form is clicked,,Not Started,,,
PRS-1646-044,Item Group Canvassing,Medium,Verify pagination works in Canvass Sheet Items Table,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click numbered pagination buttons and arrows left and right below the Items Table in Canvass Sheet
2. Validate if the buttons work and successfully navigate to other pages of the Items Table",,2.   Pagination should function as intended,,Not Started,,,
PRS-1646-045,Item Group Canvassing,Medium,Verify pagination works in Item Group Canvass Items Table,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click numbered pagination buttons and arrows left and right below the Items Table in Item Group Canvass Page
2. Validate if the buttons work and successfully navigate to other pages of the Items Table",,2.   Pagination should function as intended,,Not Started,,,
PRS-1646-046,Item Group Canvassing,Medium,Verify Go back Button works as intended in Item Group Canvass Page,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click Go back button in Item Group Canvass Page
2. Validate if it returns to the Canvass Sheet",,2.   Should return to the Canvass Sheet,,Not Started,,,
PRS-1646-047,Item Group Canvassing,Medium,Verify X Button works as intended in Confirm Canvas Modal,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click X button in Confirm Canvas Modal
2. Validate if it closes the modal",,2.   Should close the Confirm Canvas Modal,,Not Started,,,
PRS-1646-048,Item Group Canvassing,Medium,Verify X Button works as intended in Cancel Canvas Modal,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click X button in Cancel Canvas Modal
2. Validate if it closes the modal",,2.   Should close the Cancel Canvas Modal,,Not Started,,,
PRS-1646-049,Item Group Canvassing,Medium,Verify Cancel Action Button works as intended in Confirm Canvas Modal,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click Cancel Action button in Confirm Canvas Modal
2. Validate if it closes the modal and does not apply any changes",,2.   Should close the Confirm Canvas Modal and should not apply any changes and return to Item Group Canvas Page,,Not Started,,,
PRS-1646-050,Item Group Canvassing,Medium,Verify Cancel Button works as intended in Cancel Canvas Modal,"1. Logged in as Assigned Purchasing Staff
2. Created Requisition Slip must have a Steelbar as an Item","1. Click X button in Cancel Canvas Modal
2. Validate if it closes the modal",,2.   Should close the Cancel Canvas Modal and return to Item Group Canvas Page,,Not Started,,,
[CANVASS CREATION] Update Table Behavior for Canvass Sheet[for Create and Viewing],,,,,,,,,,,,
PRS-1390-001,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify that Account Code, Supplier, and Canvass Status columns are removed from the Items Table","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click Canvass Sheet Number
2. Observe the Items Table",,"1. Should update the Items Table Column
2. Should remove the 
    a. Account Code Column
    b. Supplier Column
    c. Canvass Status Column
        i. Should be implemented in Canvass Creation, Canvass Viewing, Approver View of Canvass",,Not Started,,,
PRS-1390-002,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify that Canvass Status column removed from Items Table in Canvass Creation, Viewing, and Approver View","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Go to Canvass Creation, Canvass Viewing, Approver View of Canvass
2. Check Canvass Status Column",,"2. Canvass Status Column
        i. Should be implemented in Canvass Creation, Canvass Viewing, Approver View of Canvass",,Not Started,,,
PRS-1390-003,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify that full item names are displayed in the Item column,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Check the Item Name displayed in the Item Column",,2. Should display the Full Item name in the Item Column,,Not Started,,,
PRS-1390-004,Update Table Behavior for Canvass Sheet[for Create and Viewing],Critical,"Verify the presence of Item Name, Unit, Requested Qty, Approved Qty, Remaining GFQ, and Actions","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Observe all the columns in Items Table",,"2 Should have the following Columns:
    a. Item Name
    b. Unit
    c. Requested Qty
    d. Approved Quantity
    e. Remaining GFQ
    f. Actions
      ",,Not Started,,,
PRS-1390-005,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Item Name column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2 Observe sorting of Item Name
       a. Sort by 0-9, A-Z || 9-0, Z-A || Default
",,"2. Item Name sorts accordingly
       a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default",,Not Started,,,
PRS-1390-006,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Unit column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2 Observe sorting of Unit
    ii. Unit
       a. Sort by A-Z || Z-A || Default
",,"2. Unit sorts accordingly:
    ii. Unit
       a.Should be sorted by A-Z || Z-A || Default",,Not Started,,,
PRS-1390-007,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Requested Qty column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2  Observe sorting of Requested Qty
    iii. Requested Qty
       a.Sort by 0-9, 9-0 || Default
",,"2. Requested Qty sorts accordingly:
    iii. Requested Qty
       a.Should be sorted by 0-9, 9-0 || Default
",,Not Started,,,
PRS-1390-008,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Approved Quantity of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2 Observe sorting of Approved Quantity
    iv. Approved Quantity
       a.Sort by 0-9, 9-0 || Default
",,"2. Approved Quantity sorts accordingly:
    iv. Approved Quantity
       a.Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1390-009,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Remaining GFQ column of the table is sortable (default, ascending, & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe sorting of Approved Quantity
    v. Remaining GFQ
        a.Sort by 0-9, 9-0 || Default
",,"2. Remaining GFQ sorts accordingly:
    v. Remaining GFQ
        a.Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1390-010,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that Approved Quantity shows ""---"" when not yet approved","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Check Approved Quantity column",,"2. Should display as ""---"" if not yet Approved by any of the Approvers",,Not Started,,,
PRS-1390-011,Update Table Behavior for Canvass Sheet[for Create and Viewing],Mnor,"Verify Remaining GFQ column shows ""---"" for Non-OFM and Non-OFM Transfer of Materials request types","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Navigate to Items Table in Canvass Sheet
2. Check Remaining GFQ Column",,"2.  For OFM and OFM Transfer of Materials Type of Request
      ii. Should display as ""---"" fo Non-OFM and Non-OFM Transfer of Materials",,Not Started,,,
PRS-1390-012,Update Table Behavior for Canvass Sheet[for Create and Viewing],Citical,Verify Enter Canvass button displays Enter Canvass Modal if there are no suppliers,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click ""Enter Canvass"" Button under Actions for Item without Suppliers yet
2. Validate display of Enter Canvass Modal",,"2. If Enter Canvass Button is clicked
    a) If without Suppliers yet, should display Enter Canvass Modal",,Not Started,,,
PRS-1390-013,Update Table Behavior for Canvass Sheet[for Create and Viewing],Critical,Verify Enter Canvass button displays View Modal if suppliers already exist,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click ""Enter Canvass"" Button under Actions for Item with Suppliers
2. Validate display View Modal that will allow Editing of the Suppliers",,"2. If Enter Canvass Button is clicked
    b) If with Suppliers, should display View Modal that will allow Editing of the Suppliers",,Not Started,,,
PRS-1390-014,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify that clicking ""View Purchase History"" opens a modal with correct data","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click ""View Purchase History""
2. Check modal column fields",," 2. If View Purchase History is clicked
         a) Should display a Modal that contains the following
                   1) RS Number
                        a. RS Numbers that has used the Item in their Requests
                   2) Supplier
                        a. Supplier of the Item per Requisition Slip
                   3) Price Per Unit
                        a. Price declared for the Item
                   4) Qty
                        a. Requested Quantity for the Item
                   5) Date Purchased
                        a1. Date that the Pruchase Order for the Item has been Approved
",,Not Started,,,
PRS-1390-015,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify RS Number column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if RS Number column sorts accordingly:
    i) RS Number
        a. Sort by 0-9, 9-0 || Default ",,"2. RS Number column sorts accordingly:
    i) RS Number
        a. Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1390-016,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Supplier column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Supplier sorts accordingly:
    ii) Supplier
        a. Sort by 0-9, A-Z || 9-0, Z-A || Default ",,"2. Supplier column sorts accordingly:
    ii) Supplier
        a. Should be sorted by 0-9, A-Z || 9-0, Z-A || Default
",,Not Started,,,
PRS-1390-017,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Price Per Unit column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Price Per Unit column sorts accordingly:
    iii) Price Per Unit
        a. Sort by 0-9, 9-0 || Default",,"2. Price Per Unit column sorts accordingly:
    iii) Price Per Unit
        a. Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1390-018,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Qty column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Qty column sorts accordingly:
    iv) Qty
        a. Sort by 0-9, 9-0 || Default",,"2. Qty column sorts accordingly:
    iv) Qty
        a. Should be sorted by 0-9, 9-0 || Default",,Not Started,,,
PRS-1390-019,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,"Verify Date Purchased column inside Purchase History modal is sortable (default, ascending & descending)","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click the following column headers arrow buttons
2. Observe if Date Purchased column sorts accordingly:
    v) Date Purchased
       a. Sort by Oldest Date-Latest Date, Latest Date-Oldest Date || Default",,"2. Date Purchased column sorts accordingly:
    v) Date Purchased
       a. Should be sorted by Oldest Date-Latest Date, Latest Date-Oldest Date || Default",,Not Started,,,
PRS-1390-020,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that ""---"" is displayed for no Supplier","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Observe no values for Supplier
2. Validate values can be displayed as ""---""",,"2. Values can be displayed as
    ii) Supplier
        a. Can be displayed as ""---""
",,Not Started,,,
PRS-1390-021,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that ""---"" is displayed for no Price Per Unit","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Observe no values for Price per Unit
2. Validate values can be displayed as ""---""",,"2. Values can be displayed as
    iii) Price Per Unit
        a. Can be displayed as ""---""
",,Not Started,,,
PRS-1390-022,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,"Verify that ""---"" is displayed for no Date Purchased values","1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Observe no values for Date Purchased
2. Validate values can be displayed as ""---""",,"2. Values can be displayed as
    v) Date Purchased
       a. Can be displayed as ""---""
",,Not Started,,,
PRS-1390-023,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,Verify only 10 rows of data are shown per page in Purchase History modal,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open Purchase History modal
2. Validate pagination",,2. Should display 10 Rows of Data per Page,,Not Started,,,
PRS-1390-024,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify that changes to the Items Table are also applied to Steelbars,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open Canvass with Steelbar items
2. Verify columns and behavior",,"2. Should implement the changes to Steelbars
",,Not Started,,,
PRS-1390-025,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify clicking the whole row expands Supplier Summary View,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click any item row
2. Check if it expands and shows Suppliers Summary View",,2. Should click the whole row for the Item to expand the Suppliers Summary View,,Not Started,,,
PRS-1390-026,Update Table Behavior for Canvass Sheet[for Create and Viewing],High,Verify that supplier names are shown instead of generic labels in Supplier Summary View,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Click any item row
2. Check if Supplier's Names are displayed instead of Supplier 1, etc.",,"2. Should display the Supplier's Names instead of Supplier 1, etc.",,Not Started,,,
PRS-1390-027,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,Verify that the Items Table adjusts its spacing for single item,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open RS or Enter Canvass Sheet with one item
2. Observe table spacing",,"2. Should view the Requisition Slip, and Enter a Canvass Sheet
     a. Observe the current Items Table, even if the Requested Item is only one Item, the Spacing on the Items Table is using too much space",,Not Started,,,
PRS-1390-028,Update Table Behavior for Canvass Sheet[for Create and Viewing],Minor,Verify that the Items Table adjusts properly for multiple items,"1. Logged in as Assigned Purchasing Staff
2. Requisition Slip is approved and Assigned","1. Open RS or Enter Canvass Sheet with multiple items
2. Observe table spacing",,2. Should adjust the Items Table sizing depending on the selected Number of Items,,Not Started,,,