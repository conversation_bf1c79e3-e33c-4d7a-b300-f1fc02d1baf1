# Delivery Report Workflow

This document outlines the complete workflow for the delivery report in the PRS system.

## Workflow Diagram

```mermaid
flowchart TD
  Start([Create Delivery Report]) -->|isDraft| S1[Draft]
  S1 -->|Edit DR| Start
  Start -->|Submitted| S2[Delivered]
```

## Status Definitions

| Status | Description |
|--------|-------------|
| DRAFT | Initial status when a delivery report is created but not submitted |
| DELIVERED | Delivery report has been submitted |

## Detailed Workflow Steps

### 1. Delivery Report Creation

**Actor**: Purchasing Staff or Requester

**Actions**:

- Create a new delivery report for a purchase order
- Record delivered items and quantities
- Add delivery date and notes
- Include returns in notes
- Upload delivery receipt from supplier
- Save as draft or submit immediately

**Status Transitions**:

- New → DRAFT (if saved as draft)
- New → DELIVERED (if submitted immediately)

**Business Rules**:

- Only the creator or assignee of the requisition can create a delivery report
- Deliveries can only be created for Purchase Orders with status FOR_DELIVERY
- DR Number is auto-generated
- DR Letter is auto-generated
- Can create multiple delivery reports for the same PO
- Must include delivery date and quantities
- Can handle partial deliveries (not all items delivered)
- The sum of all delivery quantities of different DRs for an item cannot exceed the ordered quantity
- Returns are handled in notes section
- Delivery items must reference the Purchase Order items they correspond to
- Delivery Receipt No. from supplier must be inputted

### 2. Delivery Report Submission

**Actor**: Purchasing Staff or Requester

**Actions**:

- Update a draft delivery report
- Add or update delivered items
- Submit the delivery report

**Status Transitions**:

- DRAFT → DELIVERED

**Business Rules**:

- Same as above
- All required fields must be filled
- Delivered quantities must be valid
- Delivery history is recorded for each item

## Example Scenarios

### Scenario 1: Standard Delivery Flow

1. Purchasing Staff creates a delivery report for a purchase order
2. Purchasing Staff records all delivered items and quantities
3. Purchasing Staff submits the delivery report

### Scenario 2: Partial Delivery

1. Purchasing Staff creates a delivery report for some items in a purchase order
2. Purchasing Staff submits the delivery report
3. Later, Purchasing Staff creates another delivery report for remaining items
4. Purchasing Staff submits the second delivery report
5. System updates the delivery status of all items
6. Continue with standard flow

### Scenario 3: Delivery with Returns

1. Purchasing Staff creates a delivery report for a purchase order
2. Some items are found to be defective
3. Purchasing Staff records all valid delivered items and quantities only
4. Purchasing Staff indicates return quantity in notes section
5. Continue with standard flow for non-returned items

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/deliveryReceiptController.js`
- **Service**: `src/app/services/deliveryReceiptService.js`
- **Repository**: `src/infra/repositories/deliveryReceiptRepository.js`
- **Entity**: `src/domain/entities/deliveryReceiptEntity.js`
- **Constants**: `src/domain/constants/deliveryConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic
async function updateDeliveryReceiptStatus(deliveryReceiptId, newStatus, transaction) {
  const deliveryReceipt = await deliveryReceiptRepository.findOne({
    where: { id: deliveryReceiptId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'DRAFT': ['SUBMITTED'],
    'SUBMITTED': ['FOR_INVOICE_RECEIVING'],
  };
  
  if (!validTransitions[deliveryReceipt.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${deliveryReceipt.status} to ${newStatus}`);
  }
  
  // Update status
  await deliveryReceipt.update({ status: newStatus }, { transaction });
  
  return deliveryReceipt;
}
```

### Delivery Item Tracking

```javascript
// Example delivery item tracking logic
async function trackDeliveryItems(deliveryReceiptId, items, transaction) {
  for (const item of items) {
    // Create delivery history record
    await deliveryHistoryRepository.create({
      deliveryReceiptId,
      purchaseOrderItemId: item.purchaseOrderItemId,
      quantity: item.quantity,
      notes: item.notes,
    }, { transaction });
    
    // Update purchase order item delivered quantity
    const purchaseOrderItem = await purchaseOrderItemRepository.findOne({
      where: { id: item.purchaseOrderItemId },
      transaction,
    });
    
    const currentDelivered = purchaseOrderItem.deliveredQuantity || 0;
    const newDelivered = currentDelivered + item.quantity;
    
    await purchaseOrderItem.update({
      deliveredQuantity: newDelivered,
      isFullyDelivered: newDelivered >= purchaseOrderItem.quantity,
    }, { transaction });
    
    // Check if all items in purchase order are fully delivered
    const allItems = await purchaseOrderItemRepository.findAll({
      where: { purchaseOrderId: purchaseOrderItem.purchaseOrderId },
      transaction,
    });
    
    const allDelivered = allItems.every(item => item.isFullyDelivered);
    
    if (allDelivered) {
      // Update purchase order status
      await purchaseOrderRepository.update(
        { id: purchaseOrderItem.purchaseOrderId },
        { isFullyDelivered: true },
        { transaction }
      );
      
      // Check if all purchase orders for requisition are delivered
      const purchaseOrder = await purchaseOrderRepository.findOne({
        where: { id: purchaseOrderItem.purchaseOrderId },
        transaction,
      });
      
      const allPOs = await purchaseOrderRepository.findAll({
        where: { requisitionId: purchaseOrder.requisitionId },
        transaction,
      });
      
      const allPOsDelivered = allPOs.every(po => po.isFullyDelivered);
      
      if (allPOsDelivered) {
        // Update requisition status to CLOSED
        await requisitionRepository.update(
          { id: purchaseOrder.requisitionId },
          { status: 'CLOSED' },
          { transaction }
        );
      }
    }
  }
}
```

## Common Issues and Solutions

### Issue 1: Cannot Create Delivery Report

**Cause**: User is not authorized or purchase order is not in FOR_DELIVERY status.

**Solution**:

- Check if the user is the creator or assignee of the requisition
- Ensure the purchase order is in FOR_DELIVERY status

### Issue 2: Partial Delivery Tracking

**Cause**: Confusion about how to handle partial deliveries.

**Solution**:

- Create a delivery report with only the items that were delivered
- System will track which items are fully delivered
- Create additional delivery report for subsequent deliveries
