<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s0{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:bottom;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-origin-ltr"></th><th id="1955009037C0" style="width:687px;" class="column-headers-background">A</th><th id="1955009037C1" style="width:100px;" class="column-headers-background">B</th><th id="1955009037C2" style="width:100px;" class="column-headers-background">C</th><th id="1955009037C3" style="width:100px;" class="column-headers-background">D</th><th id="1955009037C4" style="width:100px;" class="column-headers-background">E</th><th id="1955009037C5" style="width:100px;" class="column-headers-background">F</th><th id="1955009037C6" style="width:100px;" class="column-headers-background">G</th><th id="1955009037C7" style="width:113px;" class="column-headers-background">H</th><th id="1955009037C8" style="width:89px;" class="column-headers-background">I</th><th id="1955009037C9" style="width:177px;" class="column-headers-background">J</th><th id="1955009037C10" style="width:177px;" class="column-headers-background">K</th><th id="1955009037C11" style="width:129px;" class="column-headers-background">L</th><th id="1955009037C12" style="width:100px;" class="column-headers-background">M</th><th id="1955009037C13" style="width:132px;" class="column-headers-background">N</th></tr></thead><tbody><tr style="height: 20px"><th id="1955009037R0" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">1</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R1" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">2</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R2" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">3</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R3" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">4</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R4" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">5</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R5" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">6</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R6" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">7</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr><tr style="height: 20px"><th id="1955009037R7" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">8</div></th><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="s0"></td></tr></tbody></table></div><div id='embed_1142155694' class='waffle-embedded-object-overlay' style='width: 2157px; height: 149px; display: block;'><img src='https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FLHmjDKt5kl3HTlZRMoXdTvsk6f-Mkg-5-IOwf4f0yX8wrN-Van_JFvInfDdnT1u8DfrS2xTjVT07urTkv-zKeZgIsKfnfM9mW3RJGTyc0mpJqJ751VpMmTLeWZZzosivqFFKrR0gTqvAH2gqxalQ?key=NN19urqwamtM8J1xTnhjCeBh' style='display: block;' height='149' width='2157'></div><script>
  function posObj(sheet, id, row, col, x, y) {
      var rtl = false;
      var sheetElement = document.getElementById(sheet);
      if (!sheetElement) {
        sheetElement = document.getElementById(sheet + '-grid-container');
      }
      if (sheetElement) {
        rtl = sheetElement.getAttribute('dir') == 'rtl';
      }
      var r = document.getElementById(sheet+'R'+row);
      var c = document.getElementById(sheet+'C'+col);
      if (r && c) {
        var objElement = document.getElementById(id);
        var s = objElement.style;
        var t = y;
        while (r && r != sheetElement) {
          t += r.offsetTop;
          r = r.offsetParent;
      }
      var offsetX = x;
      while (c && c != sheetElement) {
        offsetX += c.offsetLeft;
        c = c.offsetParent;
      }
      if (rtl) {
        offsetX -= objElement.offsetWidth;
      }
      s.left = offsetX + 'px';
      s.top = t + 'px';
      s.display = 'block';
      s.border = '1px solid #000000';
    }
  }

  function posObjs() {
  posObj('1955009037', 'embed_1142155694', 0, 0, 0, 0);}posObjs();</script>