'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('note_badges', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        field: 'user_id',
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      noteId: {
        type: Sequelize.INTEGER,
        field: 'note_id',
        allowNull: false,
        references: {
          model: 'notes',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'created_at',
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        field: 'updated_at',
      },
    });

    await queryInterface.addConstraint('note_badges', {
      type: 'unique',
      fields: ['user_id', 'note_id'],
      name: 'unique_user_note_constraint',
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'note_badges',
      'unique_user_note_constraint',
    );

    await queryInterface.dropTable('note_badges');
  },
};
