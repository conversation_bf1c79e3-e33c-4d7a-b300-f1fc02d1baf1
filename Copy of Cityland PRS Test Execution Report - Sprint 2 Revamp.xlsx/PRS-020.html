<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s33{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s26{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s35{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#c9daf8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s31{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s28{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s30{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s37{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#6d9eeb;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s34{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#cc0000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s32{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9ead3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9d2e9;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s29{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#cfe2f3;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s36{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-right:none;border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#d9d2e9;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fce5cd;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s27{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffff00;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="1478547496C0" style="width:103px;" class="column-headers-background">A</th><th id="1478547496C1" style="width:167px;" class="column-headers-background">B</th><th id="1478547496C2" style="width:252px;" class="column-headers-background">C</th><th id="1478547496C3" style="width:98px;" class="column-headers-background">D</th><th id="1478547496C4" style="width:233px;" class="column-headers-background">E</th><th id="1478547496C5" style="width:330px;" class="column-headers-background">F</th><th id="1478547496C6" style="width:90px;" class="column-headers-background">G</th><th id="1478547496C7" style="width:502px;" class="column-headers-background">H</th><th id="1478547496C8" style="width:100px;" class="column-headers-background">I</th><th id="1478547496C9" style="width:114px;" class="column-headers-background">J</th><th id="1478547496C10" style="width:185px;" class="column-headers-background">K</th><th id="1478547496C11" style="width:185px;" class="column-headers-background">L</th><th id="1478547496C12" style="width:185px;" class="column-headers-background">M</th><th id="1478547496C13" style="width:185px;" class="column-headers-background">N</th><th id="1478547496C14" style="width:185px;" class="column-headers-background">O</th><th id="1478547496C15" style="width:125px;" class="column-headers-background">P</th></tr></thead><tbody><tr style="height: 42px"><th id="1478547496R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Priority</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s1">Actual Results</td><td class="s1">STG_wk2</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="1478547496R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s3" colspan="16">PRS-019 - [Syncing] - OFM Item Sync Scenario, Assigning Alt Approver, Viewing of Leave, Editing of Leave, Cancelling of Leave</td></tr><tr style="height: 19px"><th id="1478547496R2" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">3</div></th><td class="s4">PRS-020-001</td><td class="s5"><a target="_blank" href="https://drive.google.com/file/d/1BCBFlkITW3PDEJhj39iUx-FIxvFVv7vc/view?usp=drive_link">OFM Item Sync Scenario<br><br>Meeting recording: https://drive.google.com/file/d/1BCBFlkITW3PDEJhj39iUx-FIxvFVv7vc/view?usp=drive_link </a></td><td class="s6">Scenario #1<br>Verify existing Requisition Slip is already Closed </td><td class="s7"></td><td class="s6">1. A Requisition Slip is created</td><td class="s6">1. Create an RS with added items and mark it as “Closed”.<br>2. Go to Items &gt; Edit items description of the added item from #1<br>3. Click Sync</td><td class="s4"></td><td class="s6">1. The RS remains unaffected. No notifications are sent, and no changes occur.</td><td class="s4" rowspan="4"></td><td class="s8">Blocked</td><td class="s9">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s12">3/10: No RS with closed status yet</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-841">https://youtrack.stratpoint.com/issue/CITYLANDPRS-841</a></td></tr><tr style="height: 19px"><th id="1478547496R3" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">4</div></th><td class="s4">PRS-020-002</td><td class="s14">OFM Item Sync Scenario</td><td class="s6">Scenario #2<br>Verfiy open RS with a status of Draft and For Approval</td><td class="s7"></td><td class="s6">1. A Requisition Slip with Draft or For approval status<br></td><td class="s12">1. Create an RS with an added items and status is Draft and For Approval<br>2. Update on Items &gt; Go to Items<br>3. Go to OFM &gt; Update Item unit of the added item from #1<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s4"></td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=0#gid=0">1.  Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Approvers<br><br>notification sheet Others tab ROW 10 https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=0#gid=0 <br><br>Title: <br>An OFM Item Details for your Requisition Slip has been updated<br><br>Content:<br>An OFM Item that was included for your Requisition Slip has some updates. Click here if you wish to update the OFM Item.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</a></td><td class="s16">Failed</td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s12"></td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1141">3/12: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1141<br><br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-841</a></td></tr><tr style="height: 19px"><th id="1478547496R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s4">PRS-020-003</td><td class="s18">OFM Item Sync Scenario</td><td class="s6">Scenario 2: <br>Verify that the RS can be canceled, returning quantities to Remaining GFQ.</td><td class="s7"></td><td class="s6">1. A Requisition Slip with Draft or For approval status</td><td class="s6">1. Create an RS with  status “Draft” or “For Approval”.<br>2. Update the OFM<br>3. Cancel the RS.<br>4. Check Remaining GFQ and RS status.</td><td class="s4"></td><td class="s6">1. The RS is canceled, and the requested quantity is returned to the Remaining GFQ.</td><td class="s8">Blocked</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-841">https://youtrack.stratpoint.com/issue/CITYLANDPRS-841</a></td></tr><tr style="height: 19px"><th id="1478547496R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s4">PRS-020-004</td><td class="s14">OFM Item Sync Scenario</td><td class="s6">Scenario 3:<br>Verify Open RS with a Status of Canvassing or Partially Canvassed</td><td class="s7"></td><td class="s6">1. A Requisition Slip with Canvassing or Partially Canvassed status</td><td class="s6">1. Create an RS and status is Canvassing or Partially Canvassed<br>2. Update on Items<br>3. Go to Items<br>4. Go to OFM<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s4"></td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=0#gid=0">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br><br>notification sheet Others tab ROW 10 https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=0#gid=0 <br><br>Title: <br>An OFM Item Details for your Requisition Slip has been updated<br><br>Content:<br>An OFM Item that was included for your Requisition Slip has some updates. Click here if you wish to update the OFM Item.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</a></td><td class="s8">Blocked</td><td class="s9">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s12">3/10: Blocked by bug on C4</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-841">https://youtrack.stratpoint.com/issue/CITYLANDPRS-841</a></td></tr><tr style="height: 19px"><th id="1478547496R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s4">PRS-020-008</td><td class="s20">Assigning Alt Approver<br><br>https://www.figma.com/design/HJlEuwYiUHhWKazqTKmqP0/Cityland---PRS-(Desktop)?node-id=4441-106451&amp;t=bNMXb0PwcgtGFemW-0</td><td class="s6">Verify that the User Profile page displays the required sections.</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page.<br>2. Check for sections:<br> a. User Details<br> b. Leave Setup <br>c. Approvers Setup.</td><td class="s4"></td><td class="s6">1. All sections are displayed correctly.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6">- These are User Profile, Leave setup and Approver Status according to wireframe</td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s4">PRS-020-009</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that clicking Add Leave Button opens the Leave Setup Modal.</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page. Click Add Leave Button.<br>2. Check if the modal displays fields for Start Date and End Date.</td><td class="s4"></td><td class="s6">1. Leave Setup Modal is displayed with Start Date and End Date fields.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 113px"><th id="1478547496R8" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">9</div></th><td class="s4">PRS-020-010</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate whole day or 1 day leave is successful</td><td class="s7"></td><td class="s4"></td><td class="s6">1. Login as one of approvers<br>2. Click User Profile under your Name<br>3. Click Add leave on leave setup table<br>4. Select 1 day leave date or date for tomorrow</td><td class="s4"></td><td class="s6">Validate leave is successfully added. If user wants 1 day leave only, it should accept same dates as whole day leave</td><td class="s4"></td><td class="s16">Failed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-860">https://youtrack.stratpoint.com/issue/CITYLANDPRS-860</a></td></tr><tr style="height: 113px"><th id="1478547496R9" style="height: 113px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 113px">10</div></th><td class="s4">PRS-020-011</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate past date error handling on Add Leave</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page. Click Add Leave Button.<br>2. Check if the modal displays fields for Start Date and End Date.&quot;<br>3. Select past dates as Start Date and End date<br>4. Click Add leave &gt; continue<br>5. Validate error handling message returns &quot;Start Date must be in Future&quot;</td><td class="s4"></td><td class="s6">Validate error message returns &quot;Start Date must be in Future&quot;</td><td class="s4"></td><td class="s21">Passed</td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s12"></td><td class="s23 softmerge"><div class="softmerge-inner" style="width:177px;left:-1px">3/12: CITYLANDPRS-1142</div></td></tr><tr style="height: 19px"><th id="1478547496R10" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">11</div></th><td class="s4">PRS-020-012</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate Invalid date</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s12">1. Go to Settings &gt; User Profile page. Click Add Leave Button.<br>2. Check if the modal displays fields for Start Date and End Date.&quot;<br>3. Select Start Date as current date<br>4. Select End date as future date<br>5. Click End date &gt; Clear Link<br>6. Validate End date is empty<br>7. Validate &#39;Invalid date&#39; is displayed highlighting the end date</td><td class="s4"></td><td class="s6">Validate &#39;Invalid date&#39; is displayed highlighting the end date</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R11" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">12</div></th><td class="s4">PRS-020-013</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify error message when Start date and End date are current date</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page. Click Add Leave Button. Enter a Start Date and End date as current date<br>2. Click Add Leave Button &gt; Continue</td><td class="s4"></td><td class="s12">1. An error message is displayed indicating  &quot;Start Date must be in the future</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R12" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">13</div></th><td class="s4">PRS-020-014</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify error message when End date is past date and Start date is future date</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page. Click Add Leave Button.Enter a valid Start Date.<br>2. Enter past date as  End Date <br>3. Enter Start Date as Future Date<br>4. Click Add Leave Button.</td><td class="s4"></td><td class="s6">1. An error message is displayed indicating the &quot;End date must be greater than start date&quot;</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R13" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">14</div></th><td class="s4">PRS-020-015</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify error message on past Start Date and Future Start Date</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page. Click Add Leave Button. Enter a valid Start Date.<br>2. Enter past date as  Start Date<br>3. Enter End Date as Future Date<br>4. Click Add Leave Button.</td><td class="s4"></td><td class="s6">Validate error message returns &quot;Start Date must be in Future&quot;</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R14" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">15</div></th><td class="s4">PRS-020-016</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that clicking Cancel Button in Leave Setup displays a confirmation modal.</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Settings &gt; User Profile page. Click Add Leave Button.<br>2. Enter Start and End Dates.<br>3. Click Cancel Button.<br>4. Confirm the cancellation.</td><td class="s4"></td><td class="s12">1. Pop up msg &quot;You are about to cancel. All changes will not be saved. Press continue if you want to proceed with this action.&quot;&quot; is displayed<br>2. Leave should not be added.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R15" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">16</div></th><td class="s4">PRS-020-017</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that clicking Check Affected Workflows Button displays the Affected Workflow page and approvers without duplicates</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Set up leave dates.<br>2. Click Add Leave and select dates within date required of RS<br>3. Click Check Affected Workflows Button.</td><td class="s4"></td><td class="s12">1. User should be rredicred to the Affected Workflow page with Leave Date details, Requsition slip details<br>2. Click requisition slip arrow on right pane<br>3. Should display Accordion of all Workflows that the Current User is assigned as an Approver</td><td class="s4"></td><td class="s16">Failed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:339px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-851">https://youtrack.stratpoint.com/issue/CITYLANDPRS-851</a></div></td></tr><tr style="height: 19px"><th id="1478547496R16" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">17</div></th><td class="s4">PRS-020-018</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate add leave is successful and Add Alternative Button  is displayed</td><td class="s7">Critical</td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. File/Add a leave. Go to Settings &gt; User Profile page. Click Add Leave Button. <br>2. Select and choose start date and end date within the Date Required for submitted RS from pre-requisite. See example.<br>3. Click Add Leave<br>4. Validate leave is displayed on Workflow/s affected by leave table<br>5. Click leave date<br>6. Click RS arrow on right pane<br>7. Validate that Add Alternative button is present inline with your name / approver name who is on leave within those dates<br><br>Ex: RS created date required is April 14, 2025. Approver files/add a leave from April 13 to April 16 which is within the date required </td><td class="s4"></td><td class="s6">1. It will display Leave Date as link, Days of Leave, Leave Until and Actions ( Edit , Delete) buttons<br>2. Add Alternative button is present inline with your name / approver name who is on leave within those dates</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R17" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">18</div></th><td class="s4">PRS-020-019</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate Affected Workflow page</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Click leave date<br>2. Validate that Requisition Slip will be displayed if user is assigned as approver in requsition slip<br>3. Click the arrow from right pane to display the approvers</td><td class="s4"></td><td class="s6"> User should be rredicred to the Affected Workflow page displaying the level of approvers and the level with your assignment</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R18" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">19</div></th><td class="s4">PRS-020-020</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that clicking Add Alternative Button displays the Add Alternative Approver Modal.</td><td class="s7">Critical</td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Go to Affected Workflow page. Click the arrow at the right pane &gt; Click the arrow at the right pane per approver<br>2. Validate that Add alternative button will be displayed on the logged in approver<br>3. Click Add Alternative Button for a workflow</td><td class="s4"></td><td class="s6">1. Add Alternative Modal is displayed with Go Back and Add approver buttons</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R19" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">20</div></th><td class="s4">PRS-020-021</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate that Go back button is disabled when there is no affected workflow</td><td class="s7">Minor</td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6"><br>1. Login as RS approver<br>2. Go to user profile<br>3. Add leave with dates that doesn&#39;t impact any RS required date<br>4. Click Check Affected Workflows Button<br>5. Click Leave date on leave setup table<br>6. Validate that there is no available workflow affected<br>7. Validate that Go back button is disabled</td><td class="s4"></td><td class="s6">5. Should click Check Affected Workflows Button to display Affected Workflow Page <br>    a. Should have Go Back Button<br>        i. This will only be enabled if all of the Workflows have an Assigned Alt Approver</td><td class="s4"></td><td class="s16">Failed</td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:338px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-852">https://youtrack.stratpoint.com/issue/CITYLANDPRS-852</a></div></td></tr><tr style="height: 19px"><th id="1478547496R20" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">21</div></th><td class="s4">PRS-020-022</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate that Go back button is enabled when ALL of the Workflows have an Assigned Alt Approver</td><td class="s7">Minor</td><td class="s6"><br>1. Submit an RS under that project and take note of selected Date Required<br>2. Take note of the assigned approvers of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6"><br>1. Login as RS approver<br>2. Go to user profile<br>3. Add leave with dates that impact any RS required date<br>4. Click Check Affected Workflows Button<br>5. Click Leave date on leave setup table<br>6. Validate that there is available workflow affected<br>7. Click Add Alternative Button for a workflow<br>8. Select Approver &gt; Add approver<br>9. Validate Alternative added text is displayed within blue box<br>10. Validate there are no other alternative button present to aother approvers<br>11. Validate Go back button is enabled</td><td class="s4"></td><td class="s6">Validate Go back button is enabled when ALL workflows have assigned Alt approver<br><br>5. Should click Check Affected Workflows Button to display Affected Workflow Page <br>    a. Should have Go Back Button<br>        i. This will only be enabled if all of the Workflows have an Assigned Alt Approver</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R21" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">22</div></th><td class="s4">PRS-020-023</td><td class="s22">Assigning Alt Approver</td><td class="s12">Validate that if both approvers on leave on same dates, each approver should see Add Alternative button beside their names ONLY on Affected workflow</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the 2 assigned approver of RS<br>3. Login as both of the assigned approver of RS</td><td class="s6">1.  Login as level 1 approver &gt; Go to User Profile<br>2.. Add Leave with dates that impacts created RS required date<br>3. Click Check Affected Workflows Button<br>4. Click Leave date on leave setup table<br>5 Validate that there is available workflow affected<br>6. Validate that the  Add Alternative Button is displayed only on right pane of logged in approver / approver 1.<br>7. Click Add Alternative Button for a workflow<br>8. Select Alternate approver and validate selection is successful<br>9. Login as level 2 approver or another approver &gt; Go to User Profile<br>10. Add Leave with similar date as level 1 approver or date that impacts the created RS required date<br>11. Repeat steps 3 - 8</td><td class="s4"></td><td class="s6">Add Alternative Button is displayed only on right pane of logged in approver when both approvers on leave on same dates and impacts created RS date required</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R22" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">23</div></th><td class="s4">PRS-020-024</td><td class="s22"></td><td class="s6">Validate that Draft RS should not display the Add Alternative button on Approvers</td><td class="s7"></td><td class="s4"></td><td class="s6">1. Draft an RS with date required on June 3, 2025. Ex. project: ALABANG HEIGHTS<br>2. Take note the approver of the RS project from Manage &gt; Project. On Alabang heights approver - g_stg_supervisor, nina_stg_supervisor<br>3. Login as one of the project approver<br>4. Go to User Profile<br>5. Click Add Leave button<br>6. Select start date as June 2, 2025 and End date as June 3, 2025<br>7. Add Leave<br>8. Click affected workflow button<br>9. Validate affected Requsition Slip &gt; Click Arrows on right pane<br>10. Find the logged in approver</td><td class="s4"></td><td class="s6">Add Alternative button should not display on right pane of the  logged in approver because its a DRAFT RS and not yet submitted</td><td class="s4"></td><td class="s16">Failed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-855">https://youtrack.stratpoint.com/issue/CITYLANDPRS-855</a></td></tr><tr style="height: 19px"><th id="1478547496R23" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">24</div></th><td class="s4">PRS-020-025</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that adding an alternative approver reflects in relevant sections and notifies the Alt Approver.</td><td class="s7"></td><td class="s6">1. Submit an RS and take note of selected Date Required<br>2. Take note of the assigned approver of RS<br>3. Login as one of the assigned approver of RS</td><td class="s6">1. Select a user as Alt Approver.<br>2. Click Add Approver Button. <br>3. Check notifications and approver sections for updates.</td><td class="s4"></td><td class="s6">1. Alt Approver is reflected in relevant sections with Altenative added blue text and notifications are sent.</td><td class="s4"></td><td class="s21">Passed</td><td class="s9">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s25 softmerge"><div class="softmerge-inner" style="width:540px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-856">3/10: Blocked by bug  https://youtrack.stratpoint.com/issue/CITYLANDPRS-856</a></div></td></tr><tr style="height: 19px"><th id="1478547496R24" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">25</div></th><td class="s4">PRS-020-026</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that users with IT Admin or Root User type are excluded from Add Alternative Modal.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s6">1. Go to Add Alternative Modal.<br>2. Verify the user list does not include IT Admin and Root User types.<br>3. Press Ctrl+F on key board and type &quot;admin&quot; only applicable if user contains admin in username<br>4. Press Ctrl+F on key board and type &quot;root&quot;</td><td class="s4"></td><td class="s6">1. IT Admin and Root User types are excluded from the user list.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R25" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">26</div></th><td class="s4">PRS-020-027</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify NO error message when no alternative approver is assigned to affected workflows. Go back button is disabled only.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s12">1. Go to Affected Workflow page.<br>2. Attempt to proceed without assigning an Alt Approver.<br>3. Verify go back button is disabled</td><td class="s4"></td><td class="s12">No error is displayed. Per requirement, go back button will be enabled when all of workflow have assigned approver. Means, if no approver assigned, go back button is disabled<br><br>5. Should click Check Affected Workflows Button to display Affected Workflow Page <br>    a. Should have Go Back Button<br>        i. This will only be enabled if all of the Workflows have an Assigned Alt Approver</td><td class="s4"></td><td class="s21">Passed</td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:466px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1118">3/10: Bug https://youtrack.stratpoint.com/issue/CITYLANDPRS-1118</a></div></td></tr><tr style="height: 19px"><th id="1478547496R26" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">27</div></th><td class="s4">PRS-020-028</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that clicking Edit Icon opens the Edit Alternative Modal.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s6">1.Assign an Alt Approver.<br>2. Click the Edit Icon.<br>3. Verify the Edit Alternative Modal.</td><td class="s4"></td><td class="s6">1. Edit Alternative Modal is displayed.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R27" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">28</div></th><td class="s4">PRS-020-029</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that editing the Alt Approver reflects in relevant sections and notifications with correct leave dates on notification message</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s6">1.  Take note the leave dates of the approver. Assign an Alt Approver<br>2. Edit the Alt Approver.<br>3. Verify updates in approver sections and notifications.<br>4. Login as alternate approver<br>5. Validate notification message recived<br>6. Validate notification message contains correct leave dates. Compare start date and end date of the approver on leave</td><td class="s4"></td><td class="s6">1. The new Alt Approver is reflected correctly, and notifications are sent.<br>2. Notification message contains the correct leave dates start date and end date of the approver<br><br>Sample Notification message: <br>Alt Approver Assignment<br>g_stg_supervisor has assigned you as their Alt Approver during their Leave Dates, Start Date: 2025-02-08 End Date: 2025-02-09. You will be Approving on their behalf by the given Dates.<br>Today</td><td class="s4"></td><td class="s16">Failed</td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:645px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-856">3/10: Same issue on staging but for QA in dev. To be tested on next RT or during defect retest<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-856</a></div></td></tr><tr style="height: 19px"><th id="1478547496R28" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">29</div></th><td class="s4">PRS-020-030</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that clicking Delete Icon removes the Alt Approver.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s6">1. Assign an Alt Approver.<br>2. Click Delete Icon.<br>3. Confirm deletion.</td><td class="s4"></td><td class="s6">1. Alt Approver is removed, and Add Alternative Button is displayed again.</td><td class="s4"></td><td class="s21">Passed</td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:435px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1113">3/10: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1113</a></div></td></tr><tr style="height: 19px"><th id="1478547496R29" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">30</div></th><td class="s4">PRS-020-031</td><td class="s22">Assigning Alt Approver</td><td class="s12">Verify that Alt Approver only has access during the specified leave dates.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s12">1. Assign an Alt Approver.<br>2. Verify approval access on RS  before, during, and after the leave dates.</td><td class="s4"></td><td class="s12">1. Alt Approver has access on RS only from 12:00:00 AM of Start Date to 11:59:59 PM of End Date.</td><td class="s4"></td><td class="s8">Blocked</td><td class="s9">Blocked</td><td class="s26"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s27">3/12: Blocked by bug  CITYLANDPRS-1140</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-860">https://youtrack.stratpoint.com/issue/CITYLANDPRS-860</a></td></tr><tr style="height: 19px"><th id="1478547496R30" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">31</div></th><td class="s4">PRS-020-032</td><td class="s22">Assigning Alt Approver</td><td class="s6">Verify that Alt Approver retains access beyond leave dates if the system fails to revoke permissions.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver</td><td class="s6">1. Assign an Alt Approver.<br>2. Verify behavior after leave dates.3<br>. Check if Alt Approver retains permissions incorrectly.</td><td class="s4"></td><td class="s6">1. Should return the Approver Access to the Default Approver after the End Date</td><td class="s4"></td><td class="s8">Blocked</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-860">https://youtrack.stratpoint.com/issue/CITYLANDPRS-860</a></td></tr><tr style="height: 19px"><th id="1478547496R31" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">32</div></th><td class="s4">PRS-020-033</td><td class="s22">Assigning Alt Approver</td><td class="s6">Validate that after assigning alt approver to 1 leave date/s do not reflect to other leave dates affected workflow</td><td class="s7">Critical</td><td class="s4"></td><td class="s6">1. Submit 2 RS and take note the date required and approver<br>2. Login as approver<br>3. Add 2 Leaves that affect each RS submitted date required<br>4. Add alternate approver on the 1st leave date. Take note the approvers added<br>5. Click 2nd leave date &gt; Add an alternate approver. Take note the approvers added<br>6. Once successful, go back to leaves setup<br>7. Click the 1st leave date and verify the approver</td><td class="s4"></td><td class="s6"> Should display correct approver when user navigates on other leave dates after adding an alternate approver</td><td class="s4"></td><td class="s16">Failed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s28"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-859">CITYLANDPRS-859</a></td></tr><tr style="height: 19px"><th id="1478547496R32" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">33</div></th><td class="s4">PRS-020-034</td><td class="s29">Viewing of Leave</td><td class="s6">Validate viewing other leave dates displays the correct approver</td><td class="s7">Critical</td><td class="s4"></td><td class="s6">1. Submit 2 RS and take note the date required and approver<br>2. Login as approver<br>3. Add 2 Leaves that affect each RS submitted<br>4. Add alternate approver for each leave<br>5. Once alternative approver has been added, view the 2 added leaves<br>6. Click the 1 st leave date and view the approver of the affected workflow. Take note the approver names and alt approver names for each level<br>7. Click back button on browser<br>8. Click the 2nd leave date and view the approver of the affected workflow</td><td class="s4"></td><td class="s6">Should display the correct approver on affected workflow when user view other leave dates affected workflows</td><td class="s4"></td><td class="s16">Failed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s28"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-858">CITYLANDPRS-858</a></td></tr><tr style="height: 19px"><th id="1478547496R33" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">34</div></th><td class="s4">PRS-020-035</td><td class="s30">Viewing of Leave<br><br>https://www.figma.com/design/HJlEuwYiUHhWKazqTKmqP0/Cityland---PRS-(Desktop)?node-id=4921-52975&amp;t=bNMXb0PwcgtGFemW-0</td><td class="s6">Verify that clicking the Leave Date Text Link redirects to the Affected Workflow Page.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Log in as an Assigned Approver.<br>2. Click the Leave Date Text Link.</td><td class="s4"></td><td class="s6">1. Affected Workflow Page is displayed.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R34" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">35</div></th><td class="s4">PRS-020-036</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that the Leave Date is displayed on the Affected Workflow Page.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Navigate to the Affected Workflow Page.<br>2. Check for the display of the Leave Date.</td><td class="s4"></td><td class="s6">1. Leave Date is displayed correctly.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R35" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">36</div></th><td class="s4">PRS-020-037</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that the Edit Leave Date Button is enabled when the current date is less than the Start Date.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Navigate to the Affected Workflow Page.<br>2. Check the status of the Edit Leave Date Button.<br>3. Verify its state before the Start Date.</td><td class="s4"></td><td class="s6">1. Edit Leave Date Button is enabled if the current date is less than the Start Date.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Deprecated</td><td class="s26">3/12: This is part of client issue raise that past date should not be allowed on RS. Probably, similar logic applies to leave dates. There is a bug raise that past date should not be available bug 1142</td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R36" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">37</div></th><td class="s4">PRS-020-038</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that the Edit Leave Date Button is disabled when the current date is on or after the Start Date.</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s12">1. Navigate to the Affected Workflow Page.<br>2. On leave date table, validate edit button is disable<br>3. Click the leave date on workflow for the past leave date<br>4. Check the edit leave button is disabled</td><td class="s4"></td><td class="s12">1. Edit Leave Date Button on action column  is disabled.<br>2. Edit leave button is disabled inside the workflow for past leave date</td><td class="s4"></td><td class="s16">Failed</td><td class="s17">Failed</td><td class="s26">3/12: Same issue on bug reported even the edit button is already disabled on action column</td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:338px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-862">https://youtrack.stratpoint.com/issue/CITYLANDPRS-862</a></div></td></tr><tr style="height: 19px"><th id="1478547496R37" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">38</div></th><td class="s4">PRS-020-039</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that clicking the Edit Leave Date is successful when new date DOES NOT impact any Requistion Slip</td><td class="s7">Critical</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date<br>3. Should have alternate approver added<br>4. On User Profile &gt; Click Leave Date &gt; Take note the approvers on leave date prior to editing</td><td class="s6"></td><td class="s4"></td><td class="s6"> Validate that no workflow affected and no approvers displayed</td><td class="s4"></td><td class="s21">Passed</td><td class="s21">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R38" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">39</div></th><td class="s4">PRS-020-040</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that clicking the Edit Leave Date is successful when new date IMPACT any Requistion Slip</td><td class="s7">Critical</td><td class="s6">1. Create an RS and take note of date required. Ex. June 16, 2025<br>2. Take note approver of RS</td><td class="s6">Check Pre-req<br><br>1. Login as approver. Navigate to Leave Setup table<br>2. Open 1 leave date and take note the approvers<br>3. Click edit leave<br>4. Update the leave date, make it same date as the RS submitted required date from pre-req<br>5. Validate that new dates reflected on affected workflow<br>6. Validate that there are no alternate approver retained<br>7. Validate that Add Alternative button is visible and enabled<br></td><td class="s4"></td><td class="s6"></td><td class="s4"></td><td class="s21">Passed</td><td class="s21">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R39" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">40</div></th><td class="s4">PRS-020-041</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that the Accordion displays workflows where the user is an approver.</td><td class="s7">Critical</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Navigate to the Affected Workflow Page.<br>2. Expand the Accordion.<br>3. Verify the displayed workflows.</td><td class="s4"></td><td class="s6">1. Workflows where the user is assigned as an approver are displayed.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R40" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">41</div></th><td class="s4">PRS-020-042</td><td class="s29">Viewing of Leave</td><td class="s12">Verify that the Accordion does not display workflows for non-approver roles.</td><td class="s7">Minor</td><td class="s6">1. User is an Assigned Approver with added leave dates and impacted a Submitted RS required date<br>2. Add alternate approver that is not part of RS Approver</td><td class="s6">1. Login as the alternate approver or approver that is not part of RS approver<br>2. Navigate to the Affected Workflow Page.<br>3. Expand the Accordion.</td><td class="s4"></td><td class="s6">1. No workflows are displayed in the Accordion for non-approver roles.<br>2. Login as non approver and validate that user still receives notification for alt approver</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s31"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R41" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">42</div></th><td class="s4">PRS-020-043</td><td class="s29">Viewing of Leave</td><td class="s12">Verify that expanding a workflow displays the User’s Name and Alt Approver’s Name with the “Alternative Added” blue tag.</td><td class="s7">Critical</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Navigate to the Affected Workflow Page.<br>2. Expand a workflow in the Accordion.<br>3. Verify the displayed User’s Name and Alt Approver’s Name.<br><br>Sample how name is displayed:<br>G Agregado | g_stg_supervisor - Supervisor</td><td class="s4"></td><td class="s6">1. The User’s Name and Alt Approver’s Name are displayed, with the Alt Approver tagged as “Alternative Added”</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R42" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">43</div></th><td class="s4">PRS-020-044</td><td class="s29">Viewing of Leave</td><td class="s6">Verify that no Alt Approver is displayed if none has been set.</td><td class="s7">Minor</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Navigate to the Affected Workflow Page.<br>2. Expand a workflow in the Accordion.<br>3. Verify the absence of an Alt Approver.</td><td class="s4"></td><td class="s6">1. No Alt Approver is displayed, and the system prompts to assign one.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R43" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">44</div></th><td class="s4">PRS-020-045</td><td class="s29">Viewing of Leave</td><td class="s12">Verify that dismissing the confirmation modal retains the Leave Date and Alt Approvers.</td><td class="s7">Critical</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Click Cancel Leave Button on Actions column Leave setup table. Validate cancellation message<br>2. Dismiss the confirmation modal by clicking cancel and &#39;x&#39;&#39; button<br>3. Verify that the Leave Date and Alt Approvers remain.</td><td class="s4"></td><td class="s6">1. Leave Date and Alt Approvers are not removed.</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R44" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">45</div></th><td class="s4">PRS-020-046</td><td class="s32">Cancelling of Leave</td><td class="s6">Validate cancelling leave is successful</td><td class="s7"></td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Login as one of approver &gt; Click Profile<br>2. On Leave setup table , click the Red Cancel button on Actions column<br>3. Validate confirmation msg &#39;You are about to cancel this leave. Press continue if you want to proceed with this action.&#39;<br>4. Click Continue<br>5. Validate confirmation msg &#39;Leave cancelled successfully &#39;<br>6. Verify that the Leave Date and Alt Approvers are removed.</td><td class="s4"></td><td class="s6">Validate leave has been successfully cancelled and do not reflect on the leave setup</td><td class="s4"></td><td class="s21">Passed</td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R45" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">46</div></th><td class="s4">PRS-020-047</td><td class="s33">Editing of Leave</td><td class="s6">Edit Leave Date via Edit Button</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button under the Actions Column.<br>2. Update the Leave Date.<br>3. Save the changes.        </td><td class="s4"></td><td class="s6">1. Leave Date is updated successfully.</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R46" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">47</div></th><td class="s4">PRS-020-048</td><td class="s33">Editing of Leave</td><td class="s6">Edit Leave Date via Viewing Leave	</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. View the Leave details<br>2. Edit the Date or Alt Approver.<br>3. Save the changes.	</td><td class="s4"></td><td class="s6">1. The Leave Date or Alt Approver is updated successfully.	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R47" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">48</div></th><td class="s4">PRS-020-049</td><td class="s33">Editing of Leave</td><td class="s34">Edit Leave Date with invalid Start Date	</td><td class="s4">High</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Leave Date Button.<br>2. Enter a Start Date less than the Current Date.<br>3. Attempt to save.	</td><td class="s4"></td><td class="s6">1. An error message is displayed: &quot;Start Date must be greater than the Current Date.&quot;	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R48" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">49</div></th><td class="s4">PRS-020-050</td><td class="s33">Editing of Leave</td><td class="s34">Edit Leave Date with invalid End Date	</td><td class="s4">High</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Leave Date Button.<br>2. Enter an End Date less than the Start Date.<br>3. Attempt to save.	</td><td class="s4"></td><td class="s6">1. An error message is displayed: &quot;End Date must be greater than the Start Date.&quot;	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R49" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">50</div></th><td class="s4">PRS-020-051</td><td class="s33">Editing of Leave</td><td class="s6">View Workflows as an Assigned Approver	</td><td class="s4">High</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Accordion of Workflows.<br>2. Verify the displayed User&#39;s Name and Alt Approvers.	</td><td class="s4"></td><td class="s6">1. User&#39;s Name and Alt Approvers (tagged as Alternative) are displayed correctly.        </td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R50" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">51</div></th><td class="s4">PRS-020-052</td><td class="s33">Editing of Leave</td><td class="s6">Verify Alternative approver list</td><td class="s4">High</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button.<br>2. Select a valid Alt Approver (not IT Admin or Root User).    </td><td class="s4"></td><td class="s6">1.  User Type of IT Admin and Root User should not be visible on the list<br></td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R51" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">52</div></th><td class="s4">PRS-020-053</td><td class="s33">Editing of Leave</td><td class="s12">Edit Alt Approver via Edit Button        </td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button.<br>2. Select a valid Alt Approver (not IT Admin or Root User).<br>3. Click the Update Approver button</td><td class="s4"></td><td class="s6">1. The Alt Approver is updated successfully and displayed in the Approver Setup and Request sections.	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R52" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">53</div></th><td class="s4">PRS-020-054</td><td class="s33">Editing of Leave</td><td class="s6">Verify Alternative Approver on different workflows</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button.<br>2. Select a valid Alt Approver<br>3. Click the Update Approver button</td><td class="s4"></td><td class="s6">1. The same User can be assigned as the Alt Approver for all Affected Workflows</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R53" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">54</div></th><td class="s4">PRS-020-055</td><td class="s33">Editing of Leave</td><td class="s6">Go Back from Edit Alt Approver Modal	</td><td class="s4">Minor</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button.<br>2. Make changes to the Alt Approver.<br>3. Click the Go Back Button.	</td><td class="s4"></td><td class="s6">1. Modal closes without updating the Alt Approver, and the user returns to the Affected Workflows Page.	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R54" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">55</div></th><td class="s4">PRS-020-056</td><td class="s33">Editing of Leave</td><td class="s12" rowspan="2">Verfiy leave setup after updating the alternative approver</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6" rowspan="2">1. Click the Edit Button.<br>2. Make changes to the Alt Approver.<br>3. Click the Update Approver button</td><td class="s4"></td><td class="s6">1. It should update the Alt Approver</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R55" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">56</div></th><td class="s4">PRS-020-057</td><td class="s33">Editing of Leave</td><td class="s4">High</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s4"></td><td class="s6">2. Leave Setup should be:<br>                    1) Alt Approver should be displayed to the<br>                        a.1) Approver Setup<br>                            a-1) Department Approvers  <br>                            a-2) Project Approvers</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R56" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">57</div></th><td class="s4">PRS-020-058</td><td class="s33">Editing of Leave</td><td class="s6">Verfiy notification bell for New alternative approver</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button.<br>2. Make changes to the Alt Approver.<br>3. Click the Update Approver button<br>4. Logged in as the new alternative approver<br>5. validate the notification bell</td><td class="s4"></td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217">1. Should notify the Alt Approver through the Notification Bell<br>https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217 <br><br>Row 11 [New Alt Approver]<br>&quot;Title: <br>Alt Approver Assignment<br><br>Content:<br>[DEFAULT APPROVER NAME] has assigned you as their Alt Approver during their Leave Dates, [LEAVE START AND END DATE]. You will be Approving on their behalf by the given Dates.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024&quot;</a></td><td class="s4"></td><td class="s4"></td><td class="s17">Failed</td><td class="s26">3/12:  Notification sent but incorrect date. There is a known  bug for that. Need to find bug details</td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R57" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">58</div></th><td class="s4">PRS-020-059</td><td class="s33">Editing of Leave</td><td class="s6">Verfiy notification bell for Previous approver</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the Edit Button.<br>2. Make changes to the Alt Approver.<br>3. Click the Update Approver button<br>4. Logged in as the previous approver<br>5. validate the notification bell</td><td class="s4"></td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217">1. Should notify the previous Approver through the Notification Bell<br>https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217 <br><br>Row 12<br>&quot;Title: <br>Updated Alt Approver Assignment<br><br>Content:<br>[DEFAULT APPROVER NAME] has updated the Alt Approver for their Leave Dates.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024&quot;</a></td><td class="s4"></td><td class="s4"></td><td class="s17">Failed</td><td class="s26">3/12:  Notification sent but incorrect date. There is a known  bug for that. Need to find bug details</td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R58" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">59</div></th><td class="s4">PRS-020-060</td><td class="s33">Editing of Leave</td><td class="s6">Verfiy notification bell for updated leave date</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s12">1. Click the Edit Button under the Actions Column.<br>2. Update the Leave Date.<br>3. Save the changes.        <br>4. Logged in as the alternative approver<br>5. validate the notification bell</td><td class="s4"></td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217">1.  All of updates and Notifications by the Leave Date shall be received by the Alt Approver<br>https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217 <br><br>Row 13<br>Title: <br>Updated Leave Dates<br><br>Content:<br>[DEFAULT APPROVER NAME] has updated their Leave Dates to [LEAVE START AND END DATES]. You will be Approving on their behalf by the given Dates.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</a></td><td class="s4"></td><td class="s4"></td><td class="s17">Failed</td><td class="s26"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:438px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1146">3/12: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1146</a></div></td></tr><tr style="height: 19px"><th id="1478547496R59" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">60</div></th><td class="s4">PRS-020-061</td><td class="s33">Editing of Leave</td><td class="s6">Delete Alt Approver	</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Click the X Icon to delete an Alt Approver.<br>2. Confirm the deletion in the Delete Alternative Approver Modal.	</td><td class="s4"></td><td class="s6">1. The Alt Approver is removed, and the Add Alternative Button is displayed again.	</td><td class="s4"></td><td class="s4"></td><td class="s17">Failed</td><td class="s26">3/12 With known bug</td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R60" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">61</div></th><td class="s4">PRS-020-062</td><td class="s33">Editing of Leave</td><td class="s6">Verfiy remaining not approved request of the original approver before leave dates</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. Original approver has request  that are not approved before the leave date</div></td><td class="s6">1. Logged in as alt approver<br>2. go to dashboard<br>3. click the R.S created by the original approver</td><td class="s4"></td><td class="s6">1.  All pending requests are approved by the Alt Approver.	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R61" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">62</div></th><td class="s4">PRS-020-063</td><td class="s33">Editing of Leave</td><td class="s6">Verfiy remaining request of the original approver during leave date</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. Original approver has request  that are not approved during the leave date</div></td><td class="s6">1. Logged in as alt approver<br>2. go to dashboard<br>3. click the R.S created by the original approver</td><td class="s4"></td><td class="s6">1. All of the New Requests done during the Leave Date will be Approved by the Alt Approver<br></td><td class="s4"></td><td class="s4"></td><td class="s9">Blocked</td><td class="s9">3/12: Blocked by bug that alternate approve do not appear on RS approver</td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R62" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">63</div></th><td class="s4">PRS-020-064</td><td class="s33">Editing of Leave</td><td class="s6">Alt Approver Access During Leave Period	</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Set a Leave Date<br>2. Verify Alt Approver access between 12:00:00 AM of the Start Date and 11:59:59 PM of the End Date.	</td><td class="s4"></td><td class="s6">1. The Alt Approver has access to approve requests during the Leave Period.	</td><td class="s4"></td><td class="s4"></td><td class="s9">Blocked</td><td class="s9">3/12: Blocked by bug that alternate approve do not appear on RS approver</td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R63" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">64</div></th><td class="s4">PRS-020-065</td><td class="s33">Editing of Leave</td><td class="s6">Alt Approver Access After Leave Period	</td><td class="s4">Critical</td><td class="s4 softmerge"><div class="softmerge-inner" style="width:230px;left:-1px">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers<br>3. User should be on Users page</div></td><td class="s6">1. Set a Leave Date.<br>2. Verify Alt Approver access after the End Date.        <br>3. Click an asingee ticket</td><td class="s4"></td><td class="s6">1. The Alt Approver access is cleared, and the Default Approver regains access.	</td><td class="s4"></td><td class="s4"></td><td class="s9">Blocked</td><td class="s9">3/12: Blocked by bug that alternate approve do not appear on RS approver</td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R64" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">65</div></th><td class="s4">PRS-020-066</td><td class="s35">Cancelling of Leave</td><td class="s6">Cancel Leave via Edit Button under the Actions Column</td><td class="s4">Critical</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Go to Users tab<br>2. Go to Leaves section<br>3. click Edit button<br>4. cancel leave</td><td class="s4"></td><td class="s6">1. The Leave Date is removed from the Leave Setup, Alt Approver is removed from Approvers Setup<br></td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R65" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">66</div></th><td class="s4">PRS-020-067</td><td class="s35">Cancelling of Leave</td><td class="s6">Cancel Leave via Viewing of Leave	</td><td class="s4">Critical</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Go to Users tab<br>2. Go to Leaves section<br>3. click the leave date<br>4. cancel leave</td><td class="s4"></td><td class="s6">1. The Leave Date is removed from the Leave Setup, Alt Approver is removed from Approvers Setup<br></td><td class="s4"></td><td class="s4"></td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s24 softmerge"><div class="softmerge-inner" style="width:346px;left:-1px"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1126">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1126</a></div></td></tr><tr style="height: 19px"><th id="1478547496R66" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">67</div></th><td class="s4">PRS-020-068</td><td class="s35">Cancelling of Leave</td><td class="s6">Cancel Leave with Current Date greater than Start Date	</td><td class="s4">Critical</td><td class="s4"></td><td class="s6">1. Set a Leave Date with Current Date greater than Start Date<br>2. Attempt to cancel the Leave through Edit Button or Viewing of Leave.	</td><td class="s4"></td><td class="s6">1. cancel button should be disabled</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R67" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">68</div></th><td class="s4">PRS-020-069</td><td class="s35">Cancelling of Leave</td><td class="s6">Confirming Cancel leave removes the Leave Date	</td><td class="s4">Critical</td><td class="s4"></td><td class="s6">1. Go to Users tab<br>2. Go to Leaves section<br>3. click Cancel button<br>4. Click Confirm in Confirmation Modal</td><td class="s4"></td><td class="s6">1. Leave Date is removed from Leave Setup	</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R68" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">69</div></th><td class="s4">PRS-020-070</td><td class="s35">Cancelling of Leave</td><td class="s6">Confirming Cancel removes the Alternate Approver	</td><td class="s4">Critical</td><td class="s4"></td><td class="s6">1. Go to Users tab<br>2. Go to Leaves section<br>3. click Cancel button<br>4. Click Confirm in Confirmation Modal</td><td class="s4"></td><td class="s6">1. Alt Approver is removed from Approvers Setup</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s4"></td></tr><tr style="height: 19px"><th id="1478547496R69" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">70</div></th><td class="s4">PRS-020-071</td><td class="s35">Cancelling of Leave</td><td class="s6">Confirming Cancel removes the Alternate Approver in all approved request</td><td class="s4">High</td><td class="s6">1. User is an Assigned Approver<br>2. Should have setup the Leave Date and the Alt Approvers</td><td class="s6">1. Go to Users tab<br>2. Go to Leaves section<br>3. click Cancel button<br>4. Click Confirm in Confirmation Modal<br>5. logged in as Alt approver<br>6. click an R.S</td><td class="s4"></td><td class="s6">1. Should remove the target Access of the Alt Approver by the said Dates and go back to the original approver</td><td class="s4"></td><td class="s4"></td><td class="s19">Passed</td><td class="s36"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=717227574#gid=717227574">https://docs.google.com/spreadsheets/d/1-ovTrHaAmRF_uq-ehNcex-nPnAoKFYFCQQRi_Nhe4SY/edit?gid=717227574#gid=717227574</a></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6">3/12: bug raised for alt approver not reflected in RS approver</td><td class="s13"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1140">3/12: https://youtrack.stratpoint.com/issue/CITYLANDPRS-1140</a></td></tr><tr style="height: 19px"><th id="1478547496R70" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">71</div></th><td class="s4">PRS-020-072</td><td class="s35">Cancelling of Leave</td><td class="s6">Notify Assigned Alt Approver after cancellation	</td><td class="s4">Critical</td><td class="s4"></td><td class="s6">1. Go to Users tab<br>2. Go to Leaves section<br>3. click Cancel button<br>4. Click Confirm in Confirmation Modal</td><td class="s4"></td><td class="s15"><a target="_blank" href="https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217">1. Notification bell shows notification to Assigned Alt Approver        <br>row 14: <br>https://docs.google.com/spreadsheets/d/1pm55V85-JDh862_DdUuvO5Wew6LoPeC5lABV1-QFoEY/edit?gid=1469532217#gid=1469532217 <br><br>&quot;Title: <br>Cancelled Alt Approver Access<br><br>Content:<br>[DEFAULT APPROVER NAME] has cancelled their Leave Dates to [LEAVE START AND END DATES]. You will no longer be Approving on their behalf by the given Dates.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024&quot;</a></td><td class="s4"></td><td class="s4"></td><td class="s17">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s6"></td><td class="s6"></td><td class="s37"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1127">CITYLANDPRS-1127</a></td></tr></tbody></table></div>