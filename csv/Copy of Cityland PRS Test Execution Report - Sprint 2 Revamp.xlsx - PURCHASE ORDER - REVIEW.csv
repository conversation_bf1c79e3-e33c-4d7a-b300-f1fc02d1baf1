 Case Number,Feature Name,Priority,Test Case/Scenario,Pre-requisite,Test Steps,Parameters,Expected Results,Actual Results,Status,Remarks,Defects
[PURCHASE ORDER - REVIEW] Add Discounts Field and Computation if the Purchase Order,,,,,,,,,,,
PRS-1497-001,Purchase Order - Discount Field,Critical,Verify that Discount Field is displayed beside the Warranty Field.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Verify Discount field beside Warranty field.",,2. Discount field should be displayed beside Warranty field.,,Not Started,,
PRS-1497-002,Purchase Order - Discount Field,Critical,Verify that Discount Field provides two options: Fixed Amount and Percentage,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Verify Discount field two options:
     - Fixed Amount
     - Percentage",,2. Fixed Amount and Percentage options should be visible.,,Not Started,,
PRS-1497-003,Purchase Order - Discount Field,High,Verify that Discount Field accepts only Numbers.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Attempt to input non-numeric characters and special characters.",,2. Discount fields should not accept non-numeric characters and special characters.,,Not Started,,
PRS-1497-004,Purchase Order - Discount Field,High,Verify that Discount Field accepts numbers with up to 2 decimal places.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Attempt to input number with 2 decimal places.",,2. Discount field accepts input correctly.,,Not Started,,
PRS-1497-005,Purchase Order - Discount Field,Minor,Verify that Discount Field allows maximum of 20 characters.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Attempt to input 20 digits.",,2. Discount field should accept the input.,,Not Started,,
PRS-1497-006,Purchase Order - Discount Field,Minor,Verify that Discount Field does not allow more than 20 characters,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Attempt to input 21 digits.",,2. Field blocks or ignores excess input after 20 digits.,,Not Started,,
PRS-1497-007,Purchase Order - Discount Field,Minor,Verify that Discount Field is optional.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Open Purchase Order detail page.
2. Leave Discount field empty/blank.",,2. There should be no error and PO can still be saved without discount.,,Not Started,,
PRS-1497-008,Purchase Order - Discount Field,Critical,Verify that selecting Fixed Amount applies fixed discount correctly in PO total amount section.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Select ""Fixed Amount"".
2. Input discount.
3. Check total computation.",,3. Should compute the difference with the Canvass-PO Amount with the Fixed Amount to have it displayed as the deducted Discounts in the Purchase Order Total Section.,,Not Started,,
PRS-1497-009,Purchase Order - Discount Field,High,Verify that deducted Discount appears correctly when Fixed Amount is used,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Apply Fixed Amount discount.
2. Verify Discount breakdown in totals section.",,2. Discount value displayed includes Canvass Discounts + Fixed Amount.,,Not Started,,
PRS-1497-010,Purchase Order - Discount Field,High,Verify that entering a negative Fixed Amount shows an error.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Apply negative Fixed Amount discount 
2. Verify system behavior.",,2. The system should not accept negative value.,,Not Started,,
PRS-1497-011,Purchase Order - Discount Field,Critical,Verify that selecting Percentage applies discount correctly in PO total amount section.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Select ""Percentage"".
2. Input discount percent.
3. Verify total computation.",,3. Should add the deducted Amount as the Discount in the Purchase Order Total Section.,,Not Started,,
PRS-1497-012,Purchase Order - Discount Field,High,Verify that deducted Discount appears correctly when Percentage is used.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Apply Percentage discount.
2. Verify Discount breakdown in totals section.",,2. Discount value displayed includes Canvass Discounts + PO Percentage.,,Not Started,,
PRS-1497-013,Purchase Order - Discount Field,High,Verify that entering Percentage greater than 100% shows an error.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.","1. Select ""Percentage"".
2. Input invalid discount percent.",,2. The system should have an error for invalid discount percentage.,,Not Started,,
PRS-1497-014,Purchase Order - Discount Field,Critical,Verify computation of Purchase Order Total Amount when no Fixed Discount is applied.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.
4. No Fixed Discount entered",1. Review total computation.,,"1. The computation should be accurate and follows the correct formula:

Amount + Other Charges = Canvassed Amount of Items (discounted)
Discount = Total Discounts of the Items from Canvass
Total Amount = Amount - Discounts

Sample:
Amount + Other Charges = 1000
Discount (From CS) = 100
Total Amount = 900",,Not Started,,
PRS-1497-015,Purchase Order - Discount Field,Critical,Verify computation of Purchase Order Total Amount when Fixed Discount is applied.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.
4.  Fixed Amount entered.","1. Select Fixed Amount.
2. Enter Fixed Discount.
3. Review total computation.",,"3. The computation should be accurate and follows the correct formula:

Amount + Other Charges = Canvassed Amount of the included Items
Discount = Total Discounts of the Items from Canvass + Fixed Amount Discount from PO
Total Amount = Amount - Total Discounts

 SAMPLE:
  Amount + Other Charges = 1000

  Total Discount = Sum of Discounts
  100 (Total of Discounts from Canvass)
  100 (Fixed Amount)

  Total Amount(Fixed Amount) = Canvass Amount - Total Discounts",,Not Started,,
PRS-1497-016,Purchase Order - Discount Field,Critical,Verify computation of Purchase Order Total Amount when no Percentage Discount is applied.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.
4.  No Percentage Discount entered.",1.  Review total computation.,,"1. The computation should be accurate and follows the correct formula:

Amount + Other Charges = Canvassed Amount of Items (discounted)
Discount = Total Discounts of the Items from Canvass
Total Amount = Amount - Discounts

Sample:
Amount + Other Charges = 1000
Discount (From CS) = 100
Total Amount = 900",,Not Started,,
PRS-1497-017,Purchase Order - Discount Field,Critical,Verify computation of Purchase Order Total Amount when Percentage Discount is applied.,"1. Logged in as Assigned Purchasing Staff
2. Canvass Sheet has been fully Approved.
3. PO has been generated.
4. Percentage Discount entered.","1. Select Percentage.
2. Enter discount percentage.
3. Review total computation.",,"3. The computation should be accurate and follows the correct formula:

Amount + Other Charges = Canvassed Amount of the included Items
Discount = Total Discounts of the Items from Canvass + Percentage of the Amount Deducted to Total Amount
Total Amount = Amount - (Canvass Discounts + PO Percentage Discount)

 SAMPLE:
  Amount + Other Charges = 1000

  Total Discount = Sum of all Discounts
  100 (Total of Discounts from Canvass) 
  10% of 900

  Total Amount = 800",,Not Started,,