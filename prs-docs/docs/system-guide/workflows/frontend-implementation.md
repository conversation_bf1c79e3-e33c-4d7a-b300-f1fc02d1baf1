# Frontend Implementation of Workflows

This document describes how the PRS Frontend implements the various business workflows defined in the system, based on the actual codebase implementation.

## General Approach

The PRS Frontend implements workflows using a combination of:

1. **Page Components**: React components that represent entire pages in the application, organized in `src/app/routes/app/`
2. **Form Components**: Specialized components for data entry and validation with React Hook Form
3. **API Integration**: React Query hooks for data fetching and mutations, organized by feature
4. **State Management**: Zustand stores for workflow state with persistence
5. **Routing**: React Router for navigation between workflow steps with permission-based access control
6. **Permission Checks**: Fine-grained permission controls for UI elements and actions

## Common Workflow Patterns

Based on the actual codebase implementation, here are the common workflow patterns used in the PRS Frontend:

### Multi-Step Forms with Tabs

Many workflows in the PRS system involve multi-step forms with tab navigation. These are implemented using a common pattern with Zustand for state management:

```jsx
// Based on the actual implementation pattern
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCreateRequisition } from '@features/dashboard/api/create-requisition';
import { useRequisitionItemsStore } from '@store/requisitionItemsStore';
import { Tabs, Button, FormInput } from '@components/ui';
import { toast } from 'react-toastify';
import { format } from 'date-fns';

export const CreateRequisitionForm = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const { items: requisitionItems, clearItems } = useRequisitionItemsStore();

  const { mutate: createRequisition, isLoading: isSubmitting } = useCreateRequisition();

  const tabs = [
    { label: 'Basic Information', content: <BasicInfoTab /> },
    { label: 'Items', content: <ItemsTab /> },
    { label: 'Review', content: <ReviewTab onSubmit={handleSubmit} /> },
  ];

  const handleSubmit = (data) => {
    // Validate items
    if (requisitionItems.length === 0) {
      toast.error('Please add at least one item');
      setActiveTab(1); // Switch to items tab
      return;
    }

    const payload = {
      ...data,
      requestDate: format(new Date(data.requestDate), 'yyyy-MM-dd'),
      items: requisitionItems,
    };

    createRequisition(payload, {
      onSuccess: (data) => {
        toast.success('Requisition created successfully');
        clearItems(); // Clear items from store
        navigate(`/app/requisition-slip/${data.id}`);
      },
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <h1 className="text-2xl font-bold">Create Requisition</h1>

      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onChange={setActiveTab}
      />

      <div className="mt-4 flex justify-end gap-2">
        {activeTab > 0 && (
          <Button
            type="button"
            variant="secondary"
            onClick={() => setActiveTab(activeTab - 1)}
          >
            Previous
          </Button>
        )}

        {activeTab < tabs.length - 1 && (
          <Button
            type="button"
            variant="primary"
            onClick={() => setActiveTab(activeTab + 1)}
          >
            Next
          </Button>
        )}

        {activeTab === tabs.length - 1 && (
          <Button
            type="submit"
            variant="submit"
            disabled={isSubmitting}
            onClick={handleSubmit}
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </Button>
        )}
      </div>
    </div>
  );
};

// Tab components
const BasicInfoTab = () => {
  const { register, formState: { errors } } = useFormContext();

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      <FormInput
        label="Request Type"
        name="requestType"
        register={register}
        errors={errors}
        type="select"
        options={[
          { value: 'GOODS', label: 'Goods' },
          { value: 'SERVICES', label: 'Services' },
        ]}
        required
      />

      {/* More form fields... */}
    </div>
  );
};

const ItemsTab = () => {
  const { items, addItem, updateItem, removeItem } = useRequisitionItemsStore();

  return (
    <div>
      <div className="mb-4 flex justify-end">
        <Button
          variant="primary"
          onClick={() => /* Open add item modal */}
        >
          Add Item
        </Button>
      </div>

      <Table
        headers={[
          { key: 'name', label: 'Item Name' },
          { key: 'quantity', label: 'Quantity' },
          { key: 'unit', label: 'Unit' },
          { key: 'actions', label: 'Actions' },
        ]}
        data={items}
        tdDesign={{
          actions: {
            render: (_, index) => (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => removeItem(index)}
              >
                Remove
              </Button>
            ),
          },
        }}
      />
    </div>
  );
};

const ReviewTab = ({ onSubmit }) => {
  const { getValues } = useFormContext();
  const { items } = useRequisitionItemsStore();

  return (
    <div>
      <h2 className="text-xl font-semibold">Basic Information</h2>
      <div className="grid grid-cols-2 gap-4">
        {/* Display form values */}
      </div>

      <h2 className="mt-4 text-xl font-semibold">Items</h2>
      <Table
        headers={[
          { key: 'name', label: 'Item Name' },
          { key: 'quantity', label: 'Quantity' },
          { key: 'unit', label: 'Unit' },
        ]}
        data={items}
      />

      <div className="mt-4 flex justify-end">
        <Button
          variant="submit"
          onClick={() => onSubmit(getValues())}
        >
          Submit Requisition
        </Button>
      </div>
    </div>
  );
};
```

### Approval Workflows

Based on the actual codebase implementation, approval workflows involve displaying the current status, approval history, and providing actions based on user permissions:

```jsx
// Based on the actual implementation pattern
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useGetRequisitionById } from '@features/dashboard/api/get-requisition-by-id';
import { useApproveRequisition } from '@features/dashboard/api/approve-requisition';
import { useRejectRequisition } from '@features/dashboard/api/reject-requisition';
import { useUserStore } from '@store/userStore';
import { usePermission } from '@hooks/usePermission';
import { requisitionApprove, requisitionReject } from '@utils/permissions';
import { Modal, Button, Spinner, TextArea } from '@components/ui';
import { toast } from 'react-toastify';

export const RequisitionApproval = () => {
  const { id } = useParams();
  const { user } = useUserStore();
  const [rejectReason, setRejectReason] = useState('');
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);

  const { data: requisition, isLoading } = useGetRequisitionById(id);
  const { mutate: approveRequisition, isLoading: isApproving } = useApproveRequisition();
  const { mutate: rejectRequisition, isLoading: isRejecting } = useRejectRequisition();

  const canApprove = usePermission(requisitionApprove);
  const canReject = usePermission(requisitionReject);

  if (isLoading) {
    return (
      <div className="flex h-60 items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  // Check if the current user is a pending approver
  const isPendingApprover = requisition.approvers.some(
    (approver) =>
      approver.user.id === user.id &&
      approver.status === 'PENDING' &&
      requisition.status !== 'REJECTED' &&
      requisition.status !== 'CANCELLED'
  );

  const handleApprove = () => {
    approveRequisition(
      { id, remarks: 'Approved' },
      {
        onSuccess: () => {
          toast.success('Requisition approved successfully');
        },
      }
    );
  };

  const handleReject = () => {
    if (!rejectReason.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    rejectRequisition(
      { id, remarks: rejectReason },
      {
        onSuccess: () => {
          toast.success('Requisition rejected');
          setIsRejectModalOpen(false);
          setRejectReason('');
        },
      }
    );
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">
          Requisition #{requisition.combine_rs_number}
        </h1>

        <div className="flex gap-2">
          {isPendingApprover && canApprove && (
            <Button
              variant="submit"
              onClick={handleApprove}
              disabled={isApproving}
            >
              {isApproving ? 'Approving...' : 'Approve'}
            </Button>
          )}

          {isPendingApprover && canReject && (
            <Button
              variant="destructive"
              onClick={() => setIsRejectModalOpen(true)}
              disabled={isRejecting}
            >
              Reject
            </Button>
          )}
        </div>
      </div>

      <div className="rounded-md border border-gray-200 bg-white p-4">
        <h2 className="mb-4 text-xl font-semibold">Approval Status</h2>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse text-sm">
            <thead>
              <tr className="border-b bg-gray-50">
                <th className="px-4 py-2 text-left">Approver</th>
                <th className="px-4 py-2 text-left">Role</th>
                <th className="px-4 py-2 text-left">Status</th>
                <th className="px-4 py-2 text-left">Date</th>
                <th className="px-4 py-2 text-left">Remarks</th>
              </tr>
            </thead>
            <tbody>
              {requisition.approvers.map((approver) => (
                <tr key={approver.id} className="border-b">
                  <td className="px-4 py-2">
                    {approver.user.firstName} {approver.user.lastName}
                  </td>
                  <td className="px-4 py-2">{approver.role}</td>
                  <td className="px-4 py-2">
                    <span
                      className={`inline-block rounded-full px-2 py-1 text-xs font-semibold ${
                        approver.status === 'APPROVED'
                          ? 'bg-green-100 text-green-800'
                          : approver.status === 'REJECTED'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {approver.status}
                    </span>
                  </td>
                  <td className="px-4 py-2">
                    {approver.updatedAt
                      ? new Date(approver.updatedAt).toLocaleDateString()
                      : '-'}
                  </td>
                  <td className="px-4 py-2">{approver.remarks || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Reject Modal */}
      <Modal
        isOpen={isRejectModalOpen}
        onClose={() => setIsRejectModalOpen(false)}
        title="Reject Requisition"
      >
        <div className="flex flex-col gap-4">
          <TextArea
            label="Reason for Rejection"
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            required
          />

          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setIsRejectModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isRejecting}
            >
              {isRejecting ? 'Rejecting...' : 'Reject'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
```

## Specific Workflow Implementations

Based on the actual codebase implementation, here are the key workflow implementations:

### Requisition Workflow

The requisition workflow in the frontend involves:

1. **Creating a Requisition**:
   - Multi-step form with tabs for basic info, items, and review
   - Items stored in Zustand store for state management
   - Validation of required fields and business rules
   - Option to save as draft or submit for approval

2. **Viewing Requisitions**:
   - Dashboard with advanced filters and sorting
   - Status indicators with color coding
   - Quick actions based on user permissions
   - Pagination and search functionality

3. **Approving/Rejecting Requisitions**:
   - Approval buttons for authorized users based on permissions
   - Rejection with required reason via modal
   - Detailed approval history with timestamps
   - Email notifications for approvers

4. **Editing Requisitions**:
   - Edit form for draft requisitions
   - Restricted editing for submitted requisitions
   - Audit trail for changes

The routing implementation in the actual codebase:

```jsx
// Based on the actual implementation pattern in router.jsx
hasPermission(
  {
    path: 'dashboard',
    children: [
      {
        path: '',
        lazy: async () => {
          const { DashboardRoute } = await import('./routes/app/dashboard');
          return { Component: DashboardRoute };
        },
      },
      {
        path: 'create',
        lazy: async () => {
          const { CreateRequisitionRoute } = await import(
            './routes/app/dashboard'
          );
          return { Component: CreateRequisitionRoute };
        },
      },
      {
        path: 'create/:id',
        lazy: async () => {
          const { EditRequisitionRoute } = await import(
            './routes/app/dashboard'
          );
          return { Component: EditRequisitionRoute };
        },
      },
    ],
  },
  dashboardView, // Permission check
),

hasPermission(
  {
    path: 'requisition-slip/:id',
    lazy: async () => {
      const { RequisitionSlipRoute } = await import(
        './routes/app/requisition-slip'
      );
      return { Component: RequisitionSlipRoute };
    },
  },
  requisitionView, // Permission check
),
```

### Purchase Order Workflow

Based on the actual codebase implementation, the purchase order workflow in the frontend involves:

1. **Creating a Purchase Order**:
   - Selection of approved requisition from a list
   - Adding supplier details with search functionality
   - Setting delivery details and payment terms
   - Adding line items with quantity and pricing
   - Attaching supporting documents

2. **Managing Purchase Orders**:
   - List view with advanced filters and sorting
   - Status tracking with color-coded indicators
   - Detailed view with line items and history
   - Document generation (PDF) with customizable templates
   - Email notifications to suppliers

3. **Approving Purchase Orders**:
   - Multi-level approval workflow
   - Role-based approval routing
   - Budget validation and authority checks
   - Approval history with audit trail

The actual implementation includes PDF generation and email functionality:

```jsx
// Based on the actual implementation pattern
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useGetPurchaseOrderById } from '@features/purchase-order/api/get-purchase-order-by-id';
import { useGeneratePdf } from '@features/purchase-order/api/generate-pdf';
import { useSendEmail } from '@features/purchase-order/api/send-email';
import { usePermission } from '@hooks/usePermission';
import { poView, poGeneratePdf, poSendEmail } from '@utils/permissions';
import { Button, Spinner, Modal, FormInput } from '@components/ui';
import { toast } from 'react-toastify';

export const PurchaseOrderDetails = () => {
  const { id } = useParams();
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [emailData, setEmailData] = useState({
    to: '',
    cc: '',
    subject: '',
    message: '',
  });

  const { data: po, isLoading } = useGetPurchaseOrderById(id);
  const { mutate: generatePdf, isLoading: isGeneratingPdf } = useGeneratePdf();
  const { mutate: sendEmail, isLoading: isSendingEmail } = useSendEmail();

  const canGeneratePdf = usePermission(poGeneratePdf);
  const canSendEmail = usePermission(poSendEmail);

  if (isLoading) {
    return (
      <div className="flex h-60 items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  const handleGeneratePdf = () => {
    generatePdf(
      { id },
      {
        onSuccess: (data) => {
          // Create a blob from the PDF data
          const blob = new Blob([data], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);

          // Open PDF in new tab
          window.open(url, '_blank');

          toast.success('PDF generated successfully');
        },
      }
    );
  };

  const handleSendEmail = () => {
    // Validate email data
    if (!emailData.to) {
      toast.error('Recipient email is required');
      return;
    }

    sendEmail(
      {
        id,
        ...emailData,
      },
      {
        onSuccess: () => {
          toast.success('Email sent successfully');
          setEmailModalOpen(false);
        },
      }
    );
  };

  const handleOpenEmailModal = () => {
    // Pre-fill email data
    setEmailData({
      to: po.supplier?.email || '',
      cc: '',
      subject: `Purchase Order #${po.po_number}`,
      message: `Dear ${po.supplier?.name},\n\nPlease find attached the purchase order #${po.po_number}.\n\nRegards,\n${po.createdBy?.firstName} ${po.createdBy?.lastName}`,
    });

    setEmailModalOpen(true);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">
          Purchase Order #{po.po_number}
        </h1>

        <div className="flex gap-2">
          {canGeneratePdf && (
            <Button
              variant="primary"
              onClick={handleGeneratePdf}
              disabled={isGeneratingPdf}
            >
              {isGeneratingPdf ? 'Generating...' : 'Generate PDF'}
            </Button>
          )}

          {canSendEmail && (
            <Button
              variant="secondary"
              onClick={handleOpenEmailModal}
            >
              Send Email
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="rounded-md border border-gray-200 bg-white p-4">
          <h2 className="mb-4 text-xl font-semibold">Purchase Order Details</h2>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">PO Number</p>
              <p>{po.po_number}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <p>
                <span
                  className={`inline-block rounded-full px-2 py-1 text-xs font-semibold ${
                    po.status === 'APPROVED'
                      ? 'bg-green-100 text-green-800'
                      : po.status === 'REJECTED'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}
                >
                  {po.status}
                </span>
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date</p>
              <p>{new Date(po.createdAt).toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Supplier</p>
              <p>{po.supplier?.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Amount</p>
              <p>₱{po.totalAmount.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Payment Terms</p>
              <p>{po.paymentTerms}</p>
            </div>
          </div>
        </div>

        <div className="rounded-md border border-gray-200 bg-white p-4">
          <h2 className="mb-4 text-xl font-semibold">Requisition Details</h2>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">RS Number</p>
              <p>{po.requisition?.combine_rs_number}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Requested By</p>
              <p>
                {po.requisition?.requestedBy?.firstName}{' '}
                {po.requisition?.requestedBy?.lastName}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Department</p>
              <p>{po.requisition?.department?.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Company</p>
              <p>{po.requisition?.company?.name}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-md border border-gray-200 bg-white p-4">
        <h2 className="mb-4 text-xl font-semibold">Items</h2>

        <table className="w-full border-collapse text-sm">
          <thead>
            <tr className="border-b bg-gray-50">
              <th className="px-4 py-2 text-left">Item</th>
              <th className="px-4 py-2 text-left">Description</th>
              <th className="px-4 py-2 text-right">Quantity</th>
              <th className="px-4 py-2 text-left">Unit</th>
              <th className="px-4 py-2 text-right">Unit Price</th>
              <th className="px-4 py-2 text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            {po.items.map((item) => (
              <tr key={item.id} className="border-b">
                <td className="px-4 py-2">{item.name}</td>
                <td className="px-4 py-2">{item.description}</td>
                <td className="px-4 py-2 text-right">{item.quantity}</td>
                <td className="px-4 py-2">{item.unit}</td>
                <td className="px-4 py-2 text-right">
                  ₱{item.unitPrice.toLocaleString()}
                </td>
                <td className="px-4 py-2 text-right">
                  ₱{(item.quantity * item.unitPrice).toLocaleString()}
                </td>
              </tr>
            ))}
            <tr className="font-bold">
              <td colSpan={5} className="px-4 py-2 text-right">
                Total:
              </td>
              <td className="px-4 py-2 text-right">
                ₱{po.totalAmount.toLocaleString()}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Email Modal */}
      <Modal
        isOpen={emailModalOpen}
        onClose={() => setEmailModalOpen(false)}
        title="Send Purchase Order Email"
      >
        <div className="flex flex-col gap-4">
          <FormInput
            label="To"
            value={emailData.to}
            onChange={(e) => setEmailData({ ...emailData, to: e.target.value })}
            required
          />

          <FormInput
            label="CC"
            value={emailData.cc}
            onChange={(e) => setEmailData({ ...emailData, cc: e.target.value })}
          />

          <FormInput
            label="Subject"
            value={emailData.subject}
            onChange={(e) => setEmailData({ ...emailData, subject: e.target.value })}
            required
          />

          <FormInput
            label="Message"
            type="textarea"
            value={emailData.message}
            onChange={(e) => setEmailData({ ...emailData, message: e.target.value })}
            required
          />

          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setEmailModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="submit"
              onClick={handleSendEmail}
              disabled={isSendingEmail}
            >
              {isSendingEmail ? 'Sending...' : 'Send Email'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
```

### Canvass Workflow

Based on the actual codebase implementation, the canvass workflow in the frontend involves:

1. **Creating a Canvass**:
   - Selection of approved requisition items
   - Adding potential suppliers from the supplier directory
   - Setting canvass criteria and requirements
   - Defining evaluation parameters

2. **Recording Supplier Quotes**:
   - Form for entering quote details with validation
   - Item-by-item price comparison
   - Delivery date and payment terms tracking
   - Supporting document attachments

3. **Comparing and Selecting Suppliers**:
   - Side-by-side comparison table for all quotes
   - Lowest price highlighting
   - Selection of winning quote with justification
   - Approval workflow for selected supplier

The actual implementation includes a comprehensive quote comparison system:

```jsx
// Based on the actual implementation pattern
import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, useFieldArray } from 'react-hook-form';
import { useGetCanvassById } from '@features/canvass/api/get-canvass-by-id';
import { useAddSupplierQuote } from '@features/canvass/api/add-supplier-quote';
import { useGetSuppliers } from '@features/supplier/api/get-suppliers';
import { useCanvassItemsStore } from '@store/canvassItemsStore';
import { Button, FormInput, Modal, Spinner, Table } from '@components/ui';
import { toast } from 'react-toastify';
import { format } from 'date-fns';

export const CanvassQuoteForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [supplierModalOpen, setSupplierModalOpen] = useState(false);
  const { items, setItems } = useCanvassItemsStore();

  const { data: canvass, isLoading: isLoadingCanvass } = useGetCanvassById(id);
  const { data: suppliers } = useGetSuppliers({
    page: 1,
    paginate: false,
    filterBy: { status: 'ACTIVE' },
  });

  const { mutate: addQuote, isLoading: isAddingQuote } = useAddSupplierQuote();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      supplierId: '',
      deliveryDate: format(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
      paymentTerms: '30 days',
      items: canvass?.items.map(item => ({
        itemId: item.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: 0,
      })) || [],
    },
  });

  const { fields } = useFieldArray({
    control,
    name: 'items',
  });

  if (isLoadingCanvass) {
    return (
      <div className="flex h-60 items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  const handleAddQuote = data => {
    // Validate items
    const hasZeroPrices = data.items.some(item => !item.unitPrice);
    if (hasZeroPrices) {
      toast.error('All items must have a unit price');
      return;
    }

    // Calculate total amount
    const totalAmount = data.items.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0
    );

    addQuote(
      {
        canvassId: id,
        supplierId: data.supplierId,
        deliveryDate: data.deliveryDate,
        paymentTerms: data.paymentTerms,
        totalAmount,
        items: data.items.map(item => ({
          itemId: item.itemId,
          unitPrice: item.unitPrice,
          quantity: item.quantity,
        })),
      },
      {
        onSuccess: () => {
          toast.success('Supplier quote added successfully');
          navigate(`/app/canvass/${id}`);
        },
      }
    );
  };

  const supplierOptions = suppliers?.data.map(supplier => ({
    value: supplier.id,
    label: supplier.name,
  })) || [];

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Add Supplier Quote</h1>
      </div>

      <form onSubmit={handleSubmit(handleAddQuote)} className="flex flex-col gap-4">
        <div className="rounded-md border border-gray-200 bg-white p-4">
          <h2 className="mb-4 text-xl font-semibold">Quote Details</h2>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <FormInput
              label="Supplier"
              name="supplierId"
              register={register}
              errors={errors}
              type="select"
              options={supplierOptions}
              required
            />

            <FormInput
              label="Delivery Date"
              name="deliveryDate"
              register={register}
              errors={errors}
              type="date"
              required
            />

            <FormInput
              label="Payment Terms"
              name="paymentTerms"
              register={register}
              errors={errors}
              required
            />
          </div>
        </div>

        <div className="rounded-md border border-gray-200 bg-white p-4">
          <h2 className="mb-4 text-xl font-semibold">Items</h2>

          <Table
            headers={[
              { key: 'name', label: 'Item' },
              { key: 'description', label: 'Description' },
              { key: 'quantity', label: 'Quantity' },
              { key: 'unit', label: 'Unit' },
              { key: 'unitPrice', label: 'Unit Price' },
              { key: 'total', label: 'Total' },
            ]}
            data={fields.map((field, index) => {
              const unitPrice = watch(`items.${index}.unitPrice`) || 0;
              const quantity = watch(`items.${index}.quantity`) || 0;
              const total = unitPrice * quantity;

              return {
                ...field,
                unitPrice: (
                  <FormInput
                    name={`items.${index}.unitPrice`}
                    register={register}
                    errors={errors}
                    type="number"
                    step="0.01"
                    required
                  />
                ),
                total: `₱${total.toLocaleString()}`,
              };
            })}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="secondary"
            onClick={() => navigate(`/app/canvass/${id}`)}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="submit"
            disabled={isAddingQuote}
          >
            {isAddingQuote ? 'Adding Quote...' : 'Add Quote'}
          </Button>
        </div>
      </form>
    </div>
  );
};

// Canvass comparison component
export const CanvassComparison = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [selectionReason, setSelectionReason] = useState('');
  const [selectionModalOpen, setSelectionModalOpen] = useState(false);

  const { data: canvass, isLoading } = useGetCanvassById(id);
  const { mutate: selectSupplier, isLoading: isSelectingSupplier } = useSelectSupplier();

  if (isLoading) {
    return (
      <div className="flex h-60 items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  const handleSelectSupplier = () => {
    if (!selectionReason.trim()) {
      toast.error('Please provide a reason for selection');
      return;
    }

    selectSupplier(
      {
        canvassId: id,
        supplierId: selectedSupplier.id,
        reason: selectionReason,
      },
      {
        onSuccess: () => {
          toast.success('Supplier selected successfully');
          setSelectionModalOpen(false);
          navigate(`/app/canvass/${id}`);
        },
      }
    );
  };

  // Group items by name for comparison
  const itemsMap = {};
  canvass.quotes.forEach(quote => {
    quote.items.forEach(item => {
      if (!itemsMap[item.name]) {
        itemsMap[item.name] = {
          name: item.name,
          description: item.description,
          unit: item.unit,
          suppliers: {},
        };
      }

      itemsMap[item.name].suppliers[quote.supplier.id] = {
        unitPrice: item.unitPrice,
        quantity: item.quantity,
        total: item.unitPrice * item.quantity,
      };
    });
  });

  // Find lowest price for each item
  Object.values(itemsMap).forEach(item => {
    let lowestPrice = Infinity;
    let lowestSupplierId = null;

    Object.entries(item.suppliers).forEach(([supplierId, data]) => {
      if (data.unitPrice < lowestPrice) {
        lowestPrice = data.unitPrice;
        lowestSupplierId = supplierId;
      }
    });

    item.lowestSupplierId = lowestSupplierId;
  });

  // Calculate total for each supplier
  const supplierTotals = {};
  canvass.quotes.forEach(quote => {
    supplierTotals[quote.supplier.id] = quote.items.reduce(
      (sum, item) => sum + item.unitPrice * item.quantity,
      0
    );
  });

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">
          Canvass Comparison - {canvass.requisition.combine_rs_number}
        </h1>

        <div className="flex gap-2">
          <Button
            variant="primary"
            onClick={() => navigate(`/app/canvass/${id}/add-quote`)}
          >
            Add Quote
          </Button>

          {canvass.status === 'PENDING' && (
            <Button
              variant="submit"
              onClick={() => setSelectionModalOpen(true)}
              disabled={canvass.quotes.length === 0}
            >
              Select Supplier
            </Button>
          )}
        </div>
      </div>

      <div className="rounded-md border border-gray-200 bg-white p-4">
        <h2 className="mb-4 text-xl font-semibold">Supplier Quotes</h2>

        {canvass.quotes.length === 0 ? (
          <div className="flex h-20 items-center justify-center">
            <p className="text-gray-500">No quotes added yet</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse text-sm">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="px-4 py-2 text-left">Item</th>
                  <th className="px-4 py-2 text-left">Description</th>
                  <th className="px-4 py-2 text-left">Unit</th>
                  {canvass.quotes.map(quote => (
                    <th key={quote.id} className="px-4 py-2 text-center">
                      {quote.supplier.name}
                      <div className="text-xs font-normal">
                        Delivery: {new Date(quote.deliveryDate).toLocaleDateString()}
                      </div>
                      <div className="text-xs font-normal">
                        Terms: {quote.paymentTerms}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {Object.values(itemsMap).map((item, index) => (
                  <tr key={index} className="border-b">
                    <td className="px-4 py-2">{item.name}</td>
                    <td className="px-4 py-2">{item.description}</td>
                    <td className="px-4 py-2">{item.unit}</td>
                    {canvass.quotes.map(quote => {
                      const supplierData = item.suppliers[quote.supplier.id];
                      const isLowest = item.lowestSupplierId === quote.supplier.id;

                      return (
                        <td
                          key={quote.id}
                          className={`px-4 py-2 text-center ${
                            isLowest ? 'bg-green-100' : ''
                          }`}
                        >
                          {supplierData ? (
                            <div>
                              <div>₱{supplierData.unitPrice.toLocaleString()}</div>
                              <div className="text-xs text-gray-500">
                                Qty: {supplierData.quantity}
                              </div>
                              <div className="font-semibold">
                                ₱{supplierData.total.toLocaleString()}
                              </div>
                            </div>
                          ) : (
                            '-'
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))}
                <tr className="border-b font-bold">
                  <td colSpan={3} className="px-4 py-2 text-right">
                    Total:
                  </td>
                  {canvass.quotes.map(quote => (
                    <td key={quote.id} className="px-4 py-2 text-center">
                      ₱{supplierTotals[quote.supplier.id].toLocaleString()}
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Supplier Selection Modal */}
      <Modal
        isOpen={selectionModalOpen}
        onClose={() => setSelectionModalOpen(false)}
        title="Select Supplier"
      >
        <div className="flex flex-col gap-4">
          <FormInput
            label="Supplier"
            type="select"
            value={selectedSupplier?.id || ''}
            onChange={e => {
              const supplierId = e.target.value;
              const supplier = canvass.quotes.find(
                quote => quote.supplier.id === supplierId
              )?.supplier;
              setSelectedSupplier(supplier);
            }}
            options={canvass.quotes.map(quote => ({
              value: quote.supplier.id,
              label: `${quote.supplier.name} - ₱${supplierTotals[quote.supplier.id].toLocaleString()}`,
            }))}
            required
          />

          <FormInput
            label="Reason for Selection"
            type="textarea"
            value={selectionReason}
            onChange={e => setSelectionReason(e.target.value)}
            required
          />

          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setSelectionModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="submit"
              onClick={handleSelectSupplier}
              disabled={!selectedSupplier || isSelectingSupplier}
            >
              {isSelectingSupplier ? 'Selecting...' : 'Select Supplier'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
```

## Additional Workflow Features

Based on the actual codebase implementation, the PRS Frontend includes several additional features to support the core workflows:

### Notifications

The frontend implements a comprehensive notification system to alert users about workflow events, with real-time updates and badge indicators:

```jsx
// Based on the actual implementation pattern
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGetNotifications } from '@features/notification/api/get-notifications';
import { useMarkNotificationAsRead } from '@features/notification/api/mark-notification-as-read';
import { useMarkAllNotificationsAsRead } from '@features/notification/api/mark-all-notifications-as-read';
import { useNotificationStore } from '@store/notificationStore';
import { Button, Spinner, Dropdown } from '@components/ui';
import { BellIcon } from '@assets/icons';
import { formatDistanceToNow } from 'date-fns';

export const NotificationCenter = () => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);

  const { data: notifications, isLoading, refetch } = useGetNotifications();
  const { mutate: markAsRead } = useMarkNotificationAsRead();
  const { mutate: markAllAsRead } = useMarkAllNotificationsAsRead();
  const { unreadCount, setUnreadCount } = useNotificationStore();

  useEffect(() => {
    if (notifications) {
      const count = notifications.filter(notification => !notification.read).length;
      setUnreadCount(count);
    }
  }, [notifications, setUnreadCount]);

  // Set up polling for notifications
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 60000); // Poll every minute

    return () => clearInterval(interval);
  }, [refetch]);

  const handleMarkAsRead = (id) => {
    markAsRead(id, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead(null, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleNotificationClick = (notification) => {
    // Mark as read
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }

    // Navigate to the relevant page based on notification type
    if (notification.type === 'REQUISITION_APPROVAL') {
      navigate(`/app/requisition-slip/${notification.entityId}`);
    } else if (notification.type === 'PURCHASE_ORDER_APPROVAL') {
      navigate(`/app/purchase-order/${notification.entityId}`);
    } else if (notification.type === 'DELIVERY_RECEIPT') {
      navigate(`/app/delivery-receipt/${notification.entityId}`);
    }

    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        className="relative rounded-full p-2 hover:bg-gray-100"
        onClick={() => setIsOpen(!isOpen)}
      >
        <BellIcon className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs text-white">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 rounded-md border border-gray-200 bg-white shadow-lg">
          <div className="flex items-center justify-between border-b p-3">
            <h3 className="font-semibold">Notifications</h3>
            {unreadCount > 0 && (
              <Button
                variant="link"
                size="sm"
                onClick={handleMarkAllAsRead}
              >
                Mark all as read
              </Button>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex h-20 items-center justify-center">
                <Spinner size="sm" />
              </div>
            ) : notifications?.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No notifications
              </div>
            ) : (
              <ul>
                {notifications.map(notification => (
                  <li
                    key={notification.id}
                    className={`cursor-pointer border-b p-3 hover:bg-gray-50 ${
                      !notification.read ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-sm font-medium">
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(notification.createdAt), {
                            addSuffix: true,
                          })}
                        </p>
                      </div>
                      {!notification.read && (
                        <span className="ml-2 h-2 w-2 rounded-full bg-blue-500"></span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <div className="border-t p-2 text-center">
            <Button
              variant="link"
              size="sm"
              onClick={() => {
                navigate('/app/notifications');
                setIsOpen(false);
              }}
            >
              View all notifications
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
```

### Document Attachments

Based on the actual codebase implementation, the frontend implements a comprehensive file upload and management system for workflow documents, with support for multiple file types, previews, and download functionality:

```jsx
// Based on the actual implementation pattern
import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUploadAttachment } from '@features/attachments/api/upload-attachment';
import { useDeleteAttachment } from '@features/attachments/api/delete-attachment';
import { useGetAttachments } from '@features/attachments/api/get-attachments';
import { usePermission } from '@hooks/usePermission';
import { attachmentUpload, attachmentDelete } from '@utils/permissions';
import { Button, Spinner, Modal } from '@components/ui';
import { UploadIcon, TrashIcon, DownloadIcon, EyeIcon } from '@assets/icons';
import { toast } from 'react-toastify';

export const AttachmentManager = ({ entityId, entityType }) => {
  const [previewFile, setPreviewFile] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const { data: attachments, isLoading, refetch } = useGetAttachments({
    entityId,
    entityType,
  });

  const { mutate: uploadAttachment, isLoading: isUploading } = useUploadAttachment();
  const { mutate: deleteAttachment, isLoading: isDeleting } = useDeleteAttachment();

  const canUpload = usePermission(attachmentUpload);
  const canDelete = usePermission(attachmentDelete);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: acceptedFiles => {
      if (acceptedFiles.length === 0) return;

      const formData = new FormData();
      formData.append('entityId', entityId);
      formData.append('entityType', entityType);

      acceptedFiles.forEach(file => {
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
          return;
        }

        formData.append('files', file);
      });

      uploadAttachment(formData, {
        onSuccess: () => {
          toast.success('Files uploaded successfully');
          refetch();
        },
        onError: error => {
          toast.error(error.message || 'Failed to upload files');
        },
      });
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.gif'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
        '.docx',
      ],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
    },
  });

  const handleDelete = attachmentId => {
    if (window.confirm('Are you sure you want to delete this attachment?')) {
      deleteAttachment(
        { id: attachmentId },
        {
          onSuccess: () => {
            toast.success('Attachment deleted successfully');
            refetch();
          },
          onError: error => {
            toast.error(error.message || 'Failed to delete attachment');
          },
        }
      );
    }
  };

  const handleDownload = attachment => {
    // Create a link and trigger download
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePreview = attachment => {
    setPreviewFile(attachment);
    setIsPreviewOpen(true);
  };

  const isImage = file => {
    return file?.mimeType?.startsWith('image/');
  };

  const isPdf = file => {
    return file?.mimeType === 'application/pdf';
  };

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-xl font-semibold">Attachments</h2>

      {canUpload && (
        <div
          {...getRootProps()}
          className={`flex cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed p-6 transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:bg-gray-50'
          }`}
        >
          <input {...getInputProps()} />
          <UploadIcon className="mb-2 h-8 w-8 text-gray-400" />
          <p className="text-sm text-gray-500">
            Drag & drop files here, or click to select files
          </p>
          <p className="text-xs text-gray-400">
            Supported formats: Images, PDF, Word, Excel (Max 10MB)
          </p>
        </div>
      )}

      {isUploading && (
        <div className="flex items-center justify-center p-4">
          <Spinner size="md" />
          <span className="ml-2">Uploading files...</span>
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center p-4">
          <Spinner size="md" />
        </div>
      ) : attachments?.length === 0 ? (
        <div className="rounded-md border border-gray-200 bg-gray-50 p-4 text-center text-gray-500">
          No attachments found
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {attachments.map(attachment => (
            <div
              key={attachment.id}
              className="flex flex-col rounded-md border border-gray-200 bg-white"
            >
              <div className="flex items-center justify-between border-b p-3">
                <div className="truncate font-medium">{attachment.originalName}</div>
                <div className="flex gap-1">
                  {(isImage(attachment) || isPdf(attachment)) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePreview(attachment)}
                      title="Preview"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDownload(attachment)}
                    title="Download"
                  >
                    <DownloadIcon className="h-4 w-4" />
                  </Button>
                  {canDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(attachment.id)}
                      title="Delete"
                      disabled={isDeleting}
                    >
                      <TrashIcon className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              </div>
              <div className="p-3 text-sm">
                <div className="flex justify-between text-gray-500">
                  <span>
                    {new Date(attachment.createdAt).toLocaleDateString()}
                  </span>
                  <span>
                    {(attachment.size / 1024).toFixed(1)} KB
                  </span>
                </div>
                <div className="mt-1 text-xs text-gray-400">
                  Uploaded by: {attachment.createdBy?.firstName} {attachment.createdBy?.lastName}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* File Preview Modal */}
      <Modal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        title={previewFile?.originalName || 'File Preview'}
        size="lg"
      >
        <div className="flex flex-col items-center justify-center">
          {isImage(previewFile) ? (
            <img
              src={previewFile.url}
              alt={previewFile.originalName}
              className="max-h-[70vh] max-w-full object-contain"
            />
          ) : isPdf(previewFile) ? (
            <iframe
              src={`${previewFile.url}#view=FitH`}
              title={previewFile.originalName}
              className="h-[70vh] w-full"
            />
          ) : (
            <div className="p-4 text-center">
              <p>Preview not available for this file type.</p>
              <Button
                variant="primary"
                className="mt-4"
                onClick={() => handleDownload(previewFile)}
              >
                Download File
              </Button>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};
```

## Workflow Integration

Based on the actual codebase implementation, the frontend integrates these workflows to provide a seamless user experience:

1. **Dashboard**:
   - Shows pending tasks and items requiring attention
   - Displays recent activity and status updates
   - Provides quick access to common actions
   - Includes summary statistics and metrics

2. **Navigation**:
   - Hierarchical menu structure based on user permissions
   - Quick access to frequently used workflows
   - Breadcrumb navigation for context awareness
   - Responsive design for mobile and desktop

3. **Search**:
   - Global search functionality across all entities
   - Advanced filtering with multiple criteria
   - Saved searches for frequent queries
   - Export functionality for search results

4. **Reports**:
   - Generates reports based on workflow data
   - Customizable report parameters
   - Multiple export formats (PDF, Excel)
   - Scheduled report generation

The actual implementation of the Dashboard component:

```jsx
// Based on the actual implementation pattern
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGetRequisitions } from '@features/dashboard/api/get-requisitions';
import { useGetUsers } from '@features/user/api/get-users';
import { useGetCompanies } from '@features/company/api/get-companies';
import { useGetDepartments } from '@features/department/api/get-departments';
import { useGetProjects } from '@features/project/api/get-projects';
import { usePermission } from '@hooks/usePermission';
import { useRequisitionItemsStore } from '@store/requisitionItemsStore';
import { dashboardView, requisitionCreate } from '@utils/permissions';
import { SearchBar, Table, Tabs, Pagination, Button, Card, Spinner } from '@components/ui';
import { NewDocument, ChartIcon, ClockIcon, CheckIcon } from '@assets/icons';
import { Status } from '@components/Status';
import { cn } from '@utils/cn';
import { Link } from 'react-router-dom';

export const Dashboard = () => {
  const navigate = useNavigate();
  const [currentPage, setPage] = useState(1);
  const [currentLimit, setLimit] = useState(10);
  const [sortBy, setSort] = useState({ createdAt: 'DESC' });
  const [searchQuery, setSearchQuery] = useState({});
  const [activeTab, setActiveTab] = useState(0);
  const [requestType, setRequestType] = useState(null);

  const { clearItems: clearRequisitionItemsStore } = useRequisitionItemsStore();

  const createPermission = usePermission(requisitionCreate);
  const getPermission = usePermission(dashboardView);

  // Fetch requisitions with filters
  const { data: requisitions, isLoading } = useGetRequisitions(
    {
      page: currentPage,
      limit: currentLimit,
      sortBy,
      filterBy: searchQuery,
      requestType: requestType,
    },
    { enabled: getPermission },
  );

  // Fetch users for filters
  const { data: users } = useGetUsers({
    page: 1,
    paginate: false,
    includeCurrentUser: true,
    sortBy: { firstName: 'ASC' },
  });

  // Fetch companies for filters
  const { data: companies } = useGetCompanies({
    page: 1,
    paginate: false,
    sortBy: { name: 'ASC' },
  });

  // Fetch departments for filters
  const { data: departments } = useGetDepartments({
    page: 1,
    paginate: false,
    sortBy: { name: 'ASC' },
  });

  // Fetch projects for filters
  const { data: projects } = useGetProjects({
    page: 1,
    paginate: false,
    sortBy: { name: 'ASC' },
  });

  // Update request type based on active tab
  useEffect(() => {
    const types = [null, 'GOODS', 'SERVICES'];
    setRequestType(types[activeTab]);
    setPage(1);
  }, [activeTab]);

  // Define tabs
  const tabs = [
    { label: 'All', icon: ChartIcon },
    { label: 'Goods', icon: CheckIcon },
    { label: 'Services', icon: ClockIcon },
  ];

  // Define table headers
  const headers = [
    { key: 'rsNumber', label: 'RS Number', sortable: true },
    { key: 'requestType', label: 'Type', sortable: true },
    { key: 'requestDate', label: 'Date', sortable: true },
    { key: 'requestedBy', label: 'Requested By', sortable: true },
    { key: 'company', label: 'Company', sortable: true },
    { key: 'department', label: 'Department', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
  ];

  // Define table cell rendering
  const tdDesign = {
    rsNumber: {
      render: ({ combine_rs_number, id, status }) =>
        status === 'draft' ? (
          <Link
            to={`/app/dashboard/create/${id}`}
            className="text-blue-500 underline underline-offset-4"
          >
            {combine_rs_number}
          </Link>
        ) : (
          <Link
            to={`/app/requisition-slip/${id}`}
            className="text-blue-500 underline underline-offset-4"
          >
            {combine_rs_number}
          </Link>
        ),
    },
    requestType: {
      render: ({ requestType }) => requestType,
    },
    requestDate: {
      render: ({ requestDate }) => new Date(requestDate).toLocaleDateString(),
    },
    requestedBy: {
      render: ({ requestedBy }) =>
        requestedBy
          ? `${requestedBy.firstName} ${requestedBy.lastName}`
          : '-',
    },
    company: {
      render: ({ company }) => company?.name || '-',
    },
    department: {
      render: ({ department }) => department?.name || '-',
    },
    status: {
      css: 'min-w-48',
      render: Status, // Custom Status component
    },
  };

  // Define search filters
  const searchItems = [
    {
      key: 'combine_rs_number',
      label: 'RS Number',
      type: 'text',
    },
    {
      key: 'requestedBy',
      label: 'Requested By',
      type: 'select',
      options: users?.data?.map(user => ({
        value: user.id,
        label: `${user.firstName} ${user.lastName}`,
      })) || [],
    },
    {
      key: 'company',
      label: 'Company',
      type: 'select',
      options: companies?.data?.map(company => ({
        value: company.id,
        label: company.name,
      })) || [],
    },
    {
      key: 'department',
      label: 'Department',
      type: 'select',
      options: departments?.data?.map(department => ({
        value: department.id,
        label: department.name,
      })) || [],
    },
    {
      key: 'project',
      label: 'Project',
      type: 'select',
      options: projects?.data?.map(project => ({
        value: project.id,
        label: project.name,
      })) || [],
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'draft', label: 'Draft' },
        { value: 'pending', label: 'Pending' },
        { value: 'approved', label: 'Approved' },
        { value: 'rejected', label: 'Rejected' },
        { value: 'cancelled', label: 'Cancelled' },
      ],
    },
  ];

  // Define action buttons
  const ActionButtons = () => {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="secondary"
          onClick={() => {
            // Export to Excel functionality
          }}
        >
          Export to Excel
        </Button>
      </div>
    );
  };

  // Render dashboard
  return (
    <div className="flex flex-col gap-4">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <div className="flex items-center p-4">
            <div className="mr-4 rounded-full bg-blue-100 p-3">
              <ChartIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Requisitions</p>
              <p className="text-2xl font-bold">
                {requisitions?.total || 0}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center p-4">
            <div className="mr-4 rounded-full bg-green-100 p-3">
              <CheckIcon className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Approved</p>
              <p className="text-2xl font-bold">
                {requisitions?.data?.filter(r => r.status === 'approved').length || 0}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center p-4">
            <div className="mr-4 rounded-full bg-yellow-100 p-3">
              <ClockIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Pending</p>
              <p className="text-2xl font-bold">
                {requisitions?.data?.filter(r => r.status === 'pending').length || 0}
              </p>
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center p-4">
            <div className="mr-4 rounded-full bg-red-100 p-3">
              <ClockIcon className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Rejected</p>
              <p className="text-2xl font-bold">
                {requisitions?.data?.filter(r => r.status === 'rejected').length || 0}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <SearchBar
        searchItems={searchItems}
        onSearch={val => setSearchQuery(val)}
        subClassName="lg:grid-cols-6"
        setPage={setPage}
      />

      {/* Tabs */}
      <Tabs tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />

      {/* Action Buttons */}
      <div
        className={cn('block sm:flex items-center justify-end', {
          'justify-between': createPermission,
        })}
      >
        {createPermission && (
          <Button
            onClick={() => {
              clearRequisitionItemsStore();
              navigate('create');
            }}
            variant="submit"
            className="w-auto px-8"
            icon={NewDocument}
          >
            New Request
          </Button>
        )}

        <ActionButtons />
      </div>

      {/* Data Table with Pagination */}
      <Pagination
        total={requisitions?.total}
        setPage={setPage}
        setLimit={setLimit}
        hasLimit={true}
      >
        <Table
          headers={headers}
          data={requisitions?.data}
          tdDesign={tdDesign}
          isLoading={isLoading}
          onSort={setSort}
          currentSort={sortBy}
          infoDescription="Create a new request to add content"
        />
      </Pagination>
    </div>
  );
};
```

## Additional Resources

- [Frontend Architecture Overview](../architecture/frontend-overview.md): Detailed information about the frontend architecture, including components, technologies, and design principles
- [Backend Workflow Implementation](./index.md): Information about how workflows are implemented in the backend
- [Frontend Getting Started Guide](../development-guide/frontend-getting-started.md): Guide for setting up the frontend development environment and understanding the codebase
- [Coding Standards](../development-guide/coding-standards.md): Coding standards and best practices for frontend development

## Key Takeaways

Based on the actual codebase implementation, the PRS Frontend provides a comprehensive user interface for the Procurement Request System with the following key features:

1. **Feature-Based Architecture**: The codebase is organized by business features rather than technical concerns, making it easier to understand and maintain.

2. **Component Composition**: UI components are built using composition, with smaller components combined to create more complex interfaces.

3. **Permission-Based Access Control**: Fine-grained permission controls ensure that users can only access and perform actions they are authorized for.

4. **Comprehensive Workflow Support**: The frontend implements all the key procurement workflows, including requisition management, canvassing, purchase orders, delivery receipts, and payment processing.

5. **Responsive Design**: The UI is designed to work well on both desktop and mobile devices, with responsive layouts and components.

6. **Modern Technology Stack**: The application uses modern frontend technologies like React, React Query, Zustand, and Tailwind CSS to provide a fast and responsive user experience.

7. **Error Handling**: Comprehensive error handling ensures that users receive clear feedback when errors occur, with appropriate recovery mechanisms.

8. **Document Management**: The application includes robust document management capabilities, including file upload, preview, and download functionality.

9. **Notification System**: A real-time notification system keeps users informed about important events and actions that require their attention.

10. **Search and Filtering**: Advanced search and filtering capabilities make it easy for users to find the information they need across all entities in the system.
