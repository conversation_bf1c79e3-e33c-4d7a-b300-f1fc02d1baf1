#!/bin/bash

# Fix Frontend Deployment Script
# This script rebuilds the frontend container and restarts the services

set -e

echo "🔧 Fixing frontend deployment issue..."

# Navigate to deployment directory
cd "$(dirname "$0")"

echo "📦 Stopping current containers..."
docker compose down

echo "🏗️  Rebuilding frontend container..."
docker compose build frontend --no-cache

echo "🚀 Starting all containers..."
docker compose up -d

echo "⏳ Waiting for containers to be ready..."
sleep 15

echo "⏳ Waiting for services to start..."
sleep 10

echo "🔍 Checking container status..."
docker compose ps

echo "📋 Checking frontend logs..."
docker compose logs frontend --tail=20

echo "🌐 Testing frontend health..."
if docker compose exec frontend curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ Frontend health check passed"
else
    echo "❌ Frontend health check failed"
fi

echo "🔗 Testing nginx proxy..."
if docker compose exec nginx curl -f http://frontend:80/health > /dev/null 2>&1; then
    echo "✅ Nginx proxy to frontend working"
else
    echo "❌ Nginx proxy to frontend failed"
fi

echo "📊 Container resource usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo ""
echo "🔍 Checking what files are being served..."
echo "Main entry file in index.html:"
docker compose exec frontend grep -o 'src="/scripts/[^"]*"' /usr/share/nginx/html/index.html || echo "No script src found"

echo ""
echo "🎉 Frontend deployment fix completed!"
echo ""
echo "📝 Next steps:"
echo "1. Clear your browser cache completely (Ctrl+Shift+Delete or Cmd+Shift+Delete)"
echo "2. Try opening the site in an incognito/private window"
echo "3. Test the application at https://prs.stratpoint.io"
echo "4. If issues persist, check logs with: docker compose logs frontend"
echo ""
echo "⚠️  IMPORTANT: The error you saw was due to browser caching an old version."
echo "   The server is now serving the correct files, but your browser needs to"
echo "   fetch the new version. A hard refresh (Ctrl+F5) might also work."
echo ""
