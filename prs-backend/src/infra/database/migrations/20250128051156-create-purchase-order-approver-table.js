'use strict';

const {
  PO_APPROVER_STATUS,
} = require('../../../domain/constants/purchaseOrderConstants');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('purchase_order_approvers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
        field: 'purchase_order_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      status: {
        type: Sequelize.STRING(255),
        allowNull: false,
        defaultValue: PO_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('purchase_order_approvers', [
      'purchase_order_id',
    ]);
    await queryInterface.addIndex('purchase_order_approvers', ['user_id']);
    await queryInterface.addIndex('purchase_order_approvers', ['role_id']);
    await queryInterface.addIndex('purchase_order_approvers', ['level']);
    await queryInterface.addIndex('purchase_order_approvers', ['status']);
    await queryInterface.addIndex('purchase_order_approvers', [
      'alt_approver_id',
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('purchase_order_approvers', [
      'purchase_order_id',
    ]);
    await queryInterface.removeIndex('purchase_order_approvers', ['user_id']);
    await queryInterface.removeIndex('purchase_order_approvers', ['role_id']);
    await queryInterface.removeIndex('purchase_order_approvers', ['level']);
    await queryInterface.removeIndex('purchase_order_approvers', ['status']);
    await queryInterface.removeIndex('purchase_order_approvers', [
      'alt_approver_id',
    ]);

    await queryInterface.dropTable('purchase_order_approvers');
  },
};
