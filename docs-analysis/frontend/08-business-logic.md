# Business Logic Implementation

## Overview

The PRS-Frontend implements complex business logic for managing the purchase requisition process, from requisition creation to payment processing. This document provides a detailed analysis of the business logic implementation, including core workflows, data transformations, validation rules, and business rules.

## Core Business Workflows

The application implements several core business workflows:

### 1. Requisition Slip (RS) Workflow

The requisition slip workflow is the primary workflow in the application, allowing users to create and manage purchase requisitions:

```
Create RS → Submit for Approval → Approval Process → Canvassing → Purchase Order → Delivery → Payment
```

This workflow is implemented across multiple features:

```javascript
// From src/features/dashboard/api/index.js
export * from './get-requisitions';
export * from './get-requisition-slip';
export * from './get-charge-to-client-list';
export * from './create-requisition';
export * from './get-delivery-address';
export * from './upload-requisition-attachments';
export * from './approve-requisition';
export * from './reject-requisition';
export * from './get-rs-approvers';
export * from './add-approver';
export * from './submit-requisition';
export * from './add-purchasing-staff';
export * from './update-requisition';
export * from './update-requisition-item-list';
export * from './get-rs-items';
export * from './get-rs-canvass-details';
export * from './cancel-requisition';
export * from './get-request-history';
export * from './get-rs-canvass-document';
```

The main component for managing requisition slips is `RequisitionSlip.jsx`:

```jsx
// From src/features/dashboard/components/RequisitionSlip.jsx
export const RequisitionSlip = ({
  currentPage,
  currentLimit,
  setPage,
  setLimit,
  setSort,
  currentSort,
  sortBy,
}) => {
  const { user } = useUserStore();
  const {
    requisitionItems,
    addRequisitionItem,
    clearRequisitionItemsStore,
    setAddItemMode,
  } = useRequisitionItemsStore();

  const { id } = useParams();

  const { data: requisition, isLoading } = useGetRequisitionSlip(id);

  // Component implementation
};
```

### 2. Non-Requisition Slip (Non-RS) Workflow

The non-requisition slip workflow allows users to create and manage purchase requisitions that don't follow the standard process:

```
Create Non-RS → Submit for Approval → Approval Process → Payment
```

This workflow is implemented in the `non-rs` feature:

```javascript
// From src/features/non-rs/api/index.js
export { useGetNonRSHistory } from './get-non-rs-history';
export { useGetNonRSDetails } from './get-non-rs-details';
export { useGetNonRSItemList } from './get-non-rs-item-list';
export { useGetNonRSApprovers } from './get-non-rs-approvers';
export { useSubmitNonRequisitionSlip } from './create-non-rs-request';
export { useGetNonRequisitionSlips } from './get-non-requisition-slips';
export { useCancelNonRS } from './cancel-non-rs';
export { useRejectNonRS } from './reject-non-rs';
export { useApproveNonRS } from './approve-non-rs';
export { useRemoveNonRSAdhocApprover } from './remove-non-rs-adhoc-approver';
export { useGetDownloadNonRSDashboard } from './download-non-rs-dashboard';
export { useGetFormOptions } from './get-form-options';
```

The main component for managing non-requisition slips is `NonRSManagement.jsx`:

```jsx
// From src/features/non-rs/components/NonRSManagement.jsx
const NonRSManagement = () => {
  // Component implementation with business logic for Non-RS management
};
```

### 3. Purchase Order (PO) Workflow

The purchase order workflow allows users to create and manage purchase orders based on approved requisition slips:

```
Create PO → Submit for Approval → Approval Process → Delivery
```

This workflow is implemented in the `purchase-order` feature:

```javascript
// From src/features/purchase-order/api/index.js
export * from './get-purchase-order';
export * from './get-warranties';
export * from './add-warranty';
export * from './get-items';
export * from './submit-purchased-item';
export * from './submit-po';
export * from './get-approvers';
export * from './get-assignee';
export * from './create-attachment';
export * from './create-notes';
export * from './get-notes';
export * from './get-attachments';
export * from './approve-purchase-order';
export * from './get-users-for-adhoc';
export * from './add-adhoc-user';
export * from './reject-purchase-order';
export * from './resubmit-purchase-order';
export * from './mark-seen-attachments';
export * from './mark-seen-notes';
export * from './cancel-po';
export * from './get-requestor';
```

The main component for managing purchase orders is `PurchaseOrderReview.jsx`:

```jsx
// From src/features/purchase-order/components/PurchaseOrderReview.jsx
const PurchaseOrderReview = () => {
  // Component implementation with business logic for PO review and submission
  
  const handleSubmitPO = async () => {
    const formattedQuantities = quantities.map(item => ({
      purchaseOrderItemId: String(item.purchaseOrderItemId), // Convert to string
      quantityPurchased: item.quantityPurchased,
    }));

    const hasEmptyQty = formattedQuantities.find(
      item => item?.quantityPurchased === 0,
    );

    if (hasEmptyQty) {
      showNotification({
        type: 'error',
        message: 'Quantity Purchased must be greater than 0',
      });
    } else if (deliveryAddressSelection === 'new' && !newDeliveryAddress) {
      showNotification({
        type: 'error',
        message: 'New Delivery Address is required',
      });
    } else {
      // Submit PO logic
    }
  };
};
```

### 4. Payment Request Workflow

The payment request workflow allows users to create and manage payment requests based on delivered purchase orders:

```
Create Payment Request → Submit for Approval → Approval Process → Payment
```

This workflow is implemented in the `payment-request` feature:

```javascript
// From src/features/payment-request/api/index.js
export * from './get-payment-request-details'
export * from './get-payment-request-items'
export * from './get-payment-request-approvers'
export * from './get-payment-request-attachments'
export * from './get-payment-request-comments'
export * from './create-payment-request'
export * from './create-payment-request-comment'
export * from './approve-payment-request'
export * from './get-purchase-list'
export * from './get-po-details'
export * from './reject-payment-request'
export * from './add-adhoc-approver'
export * from './mark-seen-notes'
export * from './mark-seen-attachments'
export * from './get-supplier-details'
```

The main component for creating payment requests is `PaymentRequestCreate.jsx`:

```jsx
// From src/features/payment-request/components/PaymentRequestCreate.jsx
const PaymentRequestCreate = () => {
  // Component implementation with business logic for payment request creation
  
  const handleSubmit = async (values) => {
    try {
      // Form data preparation
      const formData = new FormData();
      formData.append('poId', values.poId);
      formData.append('payableDate', values.payableDate);
      formData.append('comment', values.comment || '');
      formData.append('terms', values.terms || '');
      
      if (values.employee) {
        formData.append('employeeId', values.employee.id);
      }
      
      // File attachments handling
      if (values.attachments && values.attachments.length > 0) {
        values.attachments.forEach(file => {
          formData.append('attachments', file);
        });
      }
      
      // Submit payment request
      await createPaymentRequest(formData, {
        onSuccess: ({ result }) => {
          setGeneratedId(result?.paymentRequest?.id);
          
          closeModal();
          showNotification({
            type: 'success',
            message: 'Purchase Request submitted successfully',
          });
        },
      });
    } catch (error) {
      // Error handling
    }
  };
};
```

### 5. Delivery Receipt Workflow

The delivery receipt workflow allows users to create and manage delivery receipts for received items:

```
Create Delivery Receipt → Submit → Update Inventory
```

This workflow is implemented in the `delivery-receipt` feature:

```javascript
// From src/features/delivery-receipt/components/DeliveryReceipt.jsx
const DeliveryReceipt = () => {
  // Component implementation with business logic for delivery receipt management
  
  // API hooks
  const {
    useCreateDeliveryReceipt,
    useCreateDeliveryReceiptAttachment,
    useGetDeliveryReceipt,
    useUpdateDeliveryReceipt,
    useUpdateDeliveryReceiptItem,
    useCancelReturns,
    useGetPOsForDelivery,
    useGetPODetailsForDelivery,
    useGetDeliveryReceiptNotes,
    useCreateDeliveryReceiptNote,
    useMarkAsSeen,
    useGetDrAttachmentNotifications,
  } = require('../api');
};
```

## Data Transformations

The application implements several data transformations to convert between API data formats and UI-friendly formats:

### 1. Item List Transformations

```javascript
// From src/utils/itemListSelector.js
export const transformRequisitionArray = data => {
  return data?.map(item => {
    const transformedItem = {
      id: item.itemId,
      itemCd: item?.item?.itemCd || '---',
      itmDes: item?.item?.itmDes || '---',
      itemId: item?.id || '---',
      itemName: item?.item?.itemName || '---',
      unit: item?.item?.unit || '---',
      acctCd: item?.item?.acctCd || '---',
      gfq: item?.item?.gfq,
      tradeCode: item?.item?.tradeCode,
      remainingGfq: item?.item?.remainingGfq,
      quantity: item?.quantity,
      notes: item?.notes,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      trade_code: item?.item?.trade_code,
    };

    if (item?.itemType === 'ofm' || item?.itemType === 'ofm-tom') {
      transformedItem.ofmListId = item?.ofmListId;
      transformedItem.isSteelbars = item?.item?.isSteelbars;
    }

    return transformedItem;
  });
};
```

### 2. Query Parameter Transformations

```javascript
// From src/utils/query.js
export const formatSortParams = sortBy => {
  if (!sortBy || !sortBy.length) return {};

  const sortParams = {};
  sortBy.forEach(sort => {
    sortParams[`sortBy[${sort.id}]`] = sort.desc ? 'desc' : 'asc';
  });

  return sortParams;
};
```

### 3. String Cleanup Transformations

```javascript
// From src/utils/stringCleanup.js
export const removeEmptyStringFromObject = obj => {
  if (!obj) return {};

  const newObj = { ...obj };
  Object.keys(newObj).forEach(key => {
    if (newObj[key] === '') {
      delete newObj[key];
    }
  });

  return newObj;
};

export const convertObjectToString = obj => {
  if (!obj) return '';
  return JSON.stringify(obj);
};
```

## Validation Rules

The application uses Zod for form validation, providing a type-safe way to validate form inputs:

### 1. Payment Request Validation

```javascript
// From src/schema/payment-request-schema.js
const prFormSchema = z
  .object({
    poId: z
      .number({
        invalid_type_error: 'Purchase order ID is required',
      })
      .min(1, 'Purchase order ID is required'),

    payableDate: dateSchema.min(1, 'Payable Date is required.'),
    comment: z.string().optional().nullable(),
    terms: z.string().optional().nullable(),
    attachments: z.any().optional(),
    employee: z.any().optional().nullable(),
  })
  .superRefine((data, ctx) => {
    const terms = data?.terms;
    const employee = data?.employee;

    if (terms === 'Cash in Advance (CIA)' && !employee) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Employee is required.',
        path: ['employee'],
      });
    }
  });
```

### 2. Non-RS Validation

```javascript
// From src/schema/non-rs.schema.js
const discountSchema = z.object(discountFields).superRefine((data, ctx) => {
  if (data.discountType === 'percent' && data.discountValue > 100) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Percentage discount cannot exceed 100%',
      path: ['discountValue'],
    });
  }
});

const nonRSItemSchema = z
  .object({
    ...baseItemSchema.shape,
    ...discountFields,
  })
  .superRefine((data, ctx) => {
    if (data.discountType === 'percent' && data.discountValue > 100) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Percentage discount cannot exceed 100%',
        path: ['discountValue'],
      });
    }
  });
```

### 3. Company Validation

```javascript
// From src/schema/company.schema.js
const companyAddressSchema = z
  .string('Invalid Company Address')
  .max(50, 'Company Address must be at most 50 characters')
  .regex(
    /^[a-zA-Z0-9#&*()\-[\]:;',.?\s]*$/,
    "Company Address can only contain alphanumeric characters and specific special characters (#&*()-[]:;',.?)",
  )
  .trim();

const contactNumberSchema = z
  .string('Invalid Contact Number')
  .regex(/^\+639\d{9}$/, 'Must be a valid mobile number')
  .trim();

const updateCompanySchema = z
  .object({
    address: companyAddressSchema.optional(),
    contactNumber: contactNumberSchema.optional(),
  })
  .strip()
  .strict()
  .transform(obj => {
    return Object.fromEntries(
      Object.entries(obj).filter(([_, value]) => value !== undefined),
    );
  });
```

## Business Rules

The application implements several business rules to enforce business logic:

### 1. Approval Workflow Rules

The approval workflow follows a specific sequence of approvers based on the requisition type and amount:

```jsx
// From src/features/non-rs/api/approve-non-rs.js
export const approveNonRS = ({ id, data }) => {
  return api.post(`/v1/non-requisitions/${id}/approve`, data);
};

export const useApproveNonRS = (config = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: approveNonRS,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['non_rs_history'] });
      queryClient.invalidateQueries({ queryKey: ['non_rs_details'] });
      queryClient.invalidateQueries({ queryKey: ['non_rs_item_list'] });
      queryClient.invalidateQueries({ queryKey: ['non_rs_approvers'] });
      queryClient.invalidateQueries({ queryKey: ['non_requisition_slips'] });
    },
    ...config,
  });
};
```

### 2. Discount Rules

Discounts have specific rules for percentage-based discounts:

```javascript
// From src/schema/non-rs.schema.js
const discountSchema = z.object(discountFields).superRefine((data, ctx) => {
  if (data.discountType === 'percent' && data.discountValue > 100) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Percentage discount cannot exceed 100%',
      path: ['discountValue'],
    });
  }
});
```

### 3. Purchase Order Submission Rules

Purchase orders have specific rules for submission:

```jsx
// From src/features/purchase-order/components/PurchaseOrderReview.jsx
const handleSubmitPO = async () => {
  const formattedQuantities = quantities.map(item => ({
    purchaseOrderItemId: String(item.purchaseOrderItemId), // Convert to string
    quantityPurchased: item.quantityPurchased,
  }));

  const hasEmptyQty = formattedQuantities.find(
    item => item?.quantityPurchased === 0,
  );

  if (hasEmptyQty) {
    showNotification({
      type: 'error',
      message: 'Quantity Purchased must be greater than 0',
    });
  } else if (deliveryAddressSelection === 'new' && !newDeliveryAddress) {
    showNotification({
      type: 'error',
      message: 'New Delivery Address is required',
    });
  } else {
    // Submit PO logic
  }
};
```

### 4. Payment Request Rules

Payment requests have specific rules for terms and employee selection:

```javascript
// From src/schema/payment-request-schema.js
const prFormSchema = z
  .object({
    // Schema fields
  })
  .superRefine((data, ctx) => {
    const terms = data?.terms;
    const employee = data?.employee;

    if (terms === 'Cash in Advance (CIA)' && !employee) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Employee is required.',
        path: ['employee'],
      });
    }
  });
```

## API Integration

The business logic is tightly integrated with the API through React Query:

### 1. Query Hooks

```javascript
// From src/features/dashboard/api/get-requisitions.js
export const getRequisitions = ({ page = 1, limit = 10, sortBy, filterBy, requestType }) => {
  return api.get('/v2/requisitions', {
    params: {
      page,
      limit,
      filterBy: convertObjectToString(removeEmptyStringFromObject(filterBy)),
      ...formatSortParams(sortBy),
      requestType,
    },
  });
};

export const useGetRequisitions = (
  { page, limit, sortBy, filterBy, requestType },
  config = {},
) => {
  return useQuery({
    queryKey: ['requisitions', { page, limit, sortBy, filterBy, requestType }],
    queryFn: () => getRequisitions({ page, limit, sortBy, filterBy, requestType }),
    keepPreviousData: true,
    ...config,
  });
};
```

### 2. Mutation Hooks

```javascript
// From src/features/non-rs/api/create-non-rs-request.js
export const submitNonRequisitionSlip = data => {
  return api.post('/v1/non-requisitions', data);
};

export const useSubmitNonRequisitionSlip = (config = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: submitNonRequisitionSlip,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['non_rs_details'] });
      queryClient.invalidateQueries({ queryKey: ['non_rs_item_list'] });
      queryClient.invalidateQueries({ queryKey: ['non_rs_approvers'] });
      queryClient.invalidateQueries({ queryKey: ['non_requisition_slips'] });
    },
    ...config,
  });
};
```

## Business Logic Issues

### 1. Inconsistent Error Handling

Error handling is inconsistent across different parts of the application:

```jsx
// Example of inconsistent error handling
try {
  await submitNonRSSlip(payload, {
    onSuccess: async response => {
      showNotification({
        type: 'success',
        message: 'Request submitted successfully',
      });
      // Success handling
    },
  });
} catch (error) {
  let errorMessage =
    error?.response?.data?.message || 'Failed to submit request';

  if (error instanceof z.ZodError) {
    const parsedError = JSON.parse(JSON.stringify(error));
    errorMessage = parsedError.issues?.[0].message;
  }

  showNotification({
    type: 'error',
    message: errorMessage,
  });
}
```

Some components use try/catch blocks, while others rely on the onError callback in React Query mutations.

### 2. Duplicated Business Logic

Business logic is sometimes duplicated across different components:

```jsx
// Duplicated validation logic in multiple components
if (data.discountType === 'percent' && data.discountValue > 100) {
  // Show error
}
```

### 3. Limited Client-Side Validation

Some validation is only performed on the server, leading to unnecessary API calls:

```jsx
// Missing client-side validation
await submitPO(
  {
    id: id,
    data: {
      warrantyId: String(selectedWarrantyId),
      isNewDeliveryAddress:
        deliveryAddressSelection === 'new' ? true : false,
      newDeliveryAddress:
        deliveryAddressSelection === 'new' ? newDeliveryAddress : null,
    },
  },
  {
    onSuccess: () => {
      // Success handling
    },
    onError: error => {
      // Error handling
    },
  },
);
```

### 4. Complex Component Logic

Some components contain too much business logic, making them difficult to understand and maintain:

```jsx
// Complex component with too much business logic
const RSMainTab = () => {
  // Hundreds of lines of business logic
};
```

## Recommendations

### 1. Implement a Consistent Error Handling Strategy

Create a consistent error handling utility:

```javascript
// Error handling utility
export const handleApiError = (error, options = {}) => {
  const { showNotification, defaultMessage = 'An error occurred' } = options;
  
  let errorMessage = defaultMessage;
  
  if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  } else if (error instanceof z.ZodError) {
    const parsedError = JSON.parse(JSON.stringify(error));
    errorMessage = parsedError.issues?.[0].message;
  } else if (error?.message) {
    errorMessage = error.message;
  }
  
  if (showNotification) {
    showNotification({
      type: 'error',
      message: errorMessage,
    });
  }
  
  return errorMessage;
};

// Usage
try {
  // API call
} catch (error) {
  handleApiError(error, { showNotification });
}
```

### 2. Extract Business Logic to Custom Hooks

Extract business logic from components to custom hooks:

```javascript
// Custom hook for non-RS submission
export const useNonRSSubmission = () => {
  const { showNotification } = useNotification();
  const submitNonRS = useSubmitNonRequisitionSlip();
  
  const submitNonRSWithValidation = async (data, options = {}) => {
    try {
      // Validate data
      nonRSCreationSchema.parse(data);
      
      // Submit data
      const response = await submitNonRS.mutateAsync(data);
      
      showNotification({
        type: 'success',
        message: options.isDraft
          ? 'Draft saved successfully'
          : 'Request submitted successfully',
      });
      
      return response;
    } catch (error) {
      handleApiError(error, { showNotification });
      throw error;
    }
  };
  
  return {
    submitNonRSWithValidation,
    isSubmitting: submitNonRS.isLoading,
  };
};

// Usage in component
const { submitNonRSWithValidation, isSubmitting } = useNonRSSubmission();

const handleSubmit = async (data) => {
  try {
    await submitNonRSWithValidation(data, { isDraft: false });
    // Handle success
  } catch (error) {
    // Error already handled by the hook
  }
};
```

### 3. Implement Client-Side Validation

Implement comprehensive client-side validation using Zod:

```javascript
// Validation utility
export const validateForm = (schema, data) => {
  try {
    return { data: schema.parse(data), success: true, error: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const parsedError = JSON.parse(JSON.stringify(error));
      const errorMessage = parsedError.issues?.[0].message;
      return { data: null, success: false, error: errorMessage };
    }
    return { data: null, success: false, error: error.message };
  }
};

// Usage
const handleSubmit = async (data) => {
  const validation = validateForm(poSubmissionSchema, data);
  
  if (!validation.success) {
    showNotification({
      type: 'error',
      message: validation.error,
    });
    return;
  }
  
  // Proceed with API call
};
```

### 4. Create Business Logic Services

Create dedicated services for complex business logic:

```javascript
// Discount service
export const DiscountService = {
  calculateDiscount: (amount, discount) => {
    if (!discount) return amount;
    
    const { discountType, discountValue } = discount;
    
    if (discountType === 'percent') {
      if (discountValue > 100) return 0;
      return amount * (1 - discountValue / 100);
    }
    
    if (discountType === 'amount') {
      return Math.max(0, amount - discountValue);
    }
    
    return amount;
  },
  
  validateDiscount: (discount) => {
    if (!discount) return true;
    
    const { discountType, discountValue } = discount;
    
    if (discountType === 'percent' && discountValue > 100) {
      return false;
    }
    
    if (discountType === 'amount' && discountValue < 0) {
      return false;
    }
    
    return true;
  },
};

// Usage
const finalAmount = DiscountService.calculateDiscount(itemPrice, discount);
const isValidDiscount = DiscountService.validateDiscount(discount);
```

### 5. Implement a State Machine for Workflows

Implement state machines for complex workflows:

```javascript
// Requisition state machine
const requisitionStateMachine = {
  initial: 'draft',
  states: {
    draft: {
      on: {
        SUBMIT: 'pending_approval',
        CANCEL: 'cancelled',
      },
    },
    pending_approval: {
      on: {
        APPROVE: 'approved',
        REJECT: 'rejected',
        CANCEL: 'cancelled',
      },
    },
    approved: {
      on: {
        CREATE_PO: 'po_created',
        CANCEL: 'cancelled',
      },
    },
    rejected: {
      on: {
        RESUBMIT: 'pending_approval',
        CANCEL: 'cancelled',
      },
    },
    po_created: {
      on: {
        DELIVER: 'delivered',
        CANCEL_PO: 'approved',
      },
    },
    delivered: {
      on: {
        CREATE_PAYMENT: 'payment_created',
      },
    },
    payment_created: {
      on: {
        APPROVE_PAYMENT: 'payment_approved',
        REJECT_PAYMENT: 'delivered',
      },
    },
    payment_approved: {
      on: {
        COMPLETE: 'completed',
      },
    },
    completed: {
      type: 'final',
    },
    cancelled: {
      type: 'final',
    },
  },
};

// Usage
const { state, send } = useRequisitionStateMachine(requisitionStateMachine, requisition.status);

// Transition to next state
send('APPROVE');
```

These recommendations are explored in more detail in the [Recommendations and Improvements](./09-recommendations.md) document.
