<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s4{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#000000;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s11{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#999999;text-align:left;font-weight:bold;color:#000000;font-family:docs-<PERSON><PERSON><PERSON>,<PERSON><PERSON>;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s15{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#b6d7a8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s25{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Calibri,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s24{background-color:#9fc5e8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s3{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s2{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:center;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s13{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#000000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s17{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s1{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s10{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s9{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ff0000;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s7{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s14{background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#f4cccc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s19{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s6{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#fff2cc;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s18{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#00b800;text-align:left;font-weight:bold;color:#ffffff;font-family:docs-Poppins,Arial;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s20{border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s22{border-bottom:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s23{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#9fc5e8;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s8{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#ffffff;text-align:left;color:#000000;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:nowrap;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s0{border-bottom:1px SOLID #000000;border-right:1px SOLID #000000;background-color:#1c4587;text-align:left;font-weight:bold;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#ffffff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}.ritz .waffle .s21{background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#0000ff;font-family:docs-Poppins,Arial;font-size:10pt;vertical-align:top;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:0px 3px 0px 3px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="2100941411C0" style="width:103px;" class="column-headers-background">A</th><th id="2100941411C1" style="width:98px;" class="column-headers-background">B</th><th id="2100941411C3" style="width:252px;" class="column-headers-background">D</th><th id="2100941411C4" style="width:233px;" class="column-headers-background">E</th><th id="2100941411C5" style="width:392px;" class="column-headers-background">F</th><th id="2100941411C6" style="width:86px;" class="column-headers-background">G</th><th id="2100941411C7" style="width:286px;" class="column-headers-background">H</th><th id="2100941411C10" style="width:144px;" class="column-headers-background">K</th><th id="2100941411C11" style="width:128px;" class="column-headers-background">L</th><th id="2100941411C12" style="width:183px;" class="column-headers-background">M</th><th id="2100941411C13" style="width:293px;" class="column-headers-background">N</th><th id="2100941411C14" style="width:293px;" class="column-headers-background">O</th><th id="2100941411C15" style="width:367px;" class="column-headers-background">P</th><th id="2100941411C16" style="width:100px;" class="column-headers-background">Q</th><th id="2100941411C17" style="width:100px;" class="column-headers-background">R</th><th id="2100941411C18" style="width:100px;" class="column-headers-background">S</th><th id="2100941411C19" style="width:100px;" class="column-headers-background">T</th><th id="2100941411C20" style="width:100px;" class="column-headers-background">U</th><th id="2100941411C21" style="width:100px;" class="column-headers-background">V</th><th id="2100941411C22" style="width:100px;" class="column-headers-background">W</th><th id="2100941411C23" style="width:100px;" class="column-headers-background">X</th><th id="2100941411C24" style="width:100px;" class="column-headers-background">Y</th><th id="2100941411C25" style="width:100px;" class="column-headers-background">Z</th><th id="2100941411C26" style="width:100px;" class="column-headers-background">AA</th><th id="2100941411C27" style="width:100px;" class="column-headers-background">AB</th><th id="2100941411C28" style="width:100px;" class="column-headers-background">AC</th></tr></thead><tbody><tr style="height: 42px"><th id="2100941411R0" style="height: 42px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 42px">1</div></th><td class="s0"><a target="_blank" href="#gid=null">Case Number</a></td><td class="s1">Feature Name</td><td class="s1">Test Case/Scenario</td><td class="s1">Pre-requisite</td><td class="s1">Test Steps</td><td class="s1">Parameters</td><td class="s1">Expected Results</td><td class="s2">Status<br>(E2E_Run1)</td><td class="s2">Actual Results<br>(E2E_Run1)</td><td class="s2">Status<br>(E2E_Run2)</td><td class="s2">Actual Result<br>(E2E_Run2)</td><td class="s1">Remarks</td><td class="s1">Defects</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 19px"><th id="2100941411R1" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">2</div></th><td class="s4" colspan="13">PRS-019 - [Syncing] - Supplier Sync Scenario, Suspending a Supplier, Company Sync Scenario</td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 211px"><th id="2100941411R2" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">3</div></th><td class="s5"><a target="_blank" href="https://docs.google.com/spreadsheets/d/13vZeyEomzPRHw_qhZeWUunD6-wyQX2Z-eQm1Z91eryw/edit?pli=1&amp;gid=*********#gid=*********&amp;range=351:351">CITYLANDPRS-698<br><br><br><br>PRS-019-001</a></td><td class="s6">Supplier Sync Scenario</td><td class="s7">SCNEARIO #1<br>1. Existing Requisition Slip is already Closed, should not be affected by the changes</td><td class="s7">1. A Requisition Slip has been created<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver<br></td><td class="s7">1. Create an Requisition Slip and mark it as “Closed”.<br>2. Click Manage &gt;&gt; Go to Supplier<br>3. . Go to Supplier &gt;&gt; Update supplier Details <br>3. Click Sync<br><br></td><td class="s8"></td><td class="s7">1. The Requisition Slip remains unaffected. No notifications are sent, and no changes occur.</td><td class="s9">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s12"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1068">03/06  : <br>[QA BUGS] [Supplier Sync Scenario] Upon Clicking Sync button the updated detail of Supplier not reflect<br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1068<br><br><br><br><br>2/12/25 <br>As per Miss Irene:<br><br>need maantay ung buong E2E ng Development para magawa ung scenario #1</a></td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1068">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1068</a></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 211px"><th id="2100941411R3" style="height: 211px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 211px">4</div></th><td class="s7">PRS-019-002</td><td class="s6">Supplier Sync Scenario</td><td class="s7">Scenario #2<br>Verfiy Open Requisition Slip with a Status of Partially Canvassed or Canvass Approval</td><td class="s7">1. A Requisition Slip with a Status of Partially Canvassed or Canvass Approval</td><td class="s7">1. Create an Requisition Slip and Status of Partially Canvassed or Canvass Approval<br>2. Update on supplier &gt;&gt; Go to manage<br>3. Go to Supplier &gt;&gt; Update supplier details of the supplier from #1<br>4. Click Sync<br>5. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7"> 1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br><br> NOTIFICATION TEMPLATE:<br>   <br>Title: <br>A Supplier for your Requisition Slip has been updated<br><br>Content:<br>A Supplier that was added for your Requisition Slip has some updates. Click here if you wish to update the Supplier.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s9">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7">[QA BUGS] [Supplier Sync Scenario] When Edit the supplier name in Canvass sheet the updated supplier name is not reflect <br><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1072">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1072                <br><br>https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="2100941411R4" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">5</div></th><td class="s7">PRS-019-003</td><td class="s6">Supplier Sync Scenario</td><td class="s7">Scenario #2<br>Verify update or replaced items in Requisition Slip status of canvassing or partially canvassed</td><td class="s7">1. already updated Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the Supplier<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should repeat the Approval Process from Requisition Slip to Canvassing</td><td class="s9">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7">[QA BUGS] [Supplier Sync Scenario] Not Repeat the approval process from Requisition Slip to Canvassing when update or added a supplier</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1090">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1090</a></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="2100941411R5" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">6</div></th><td class="s7">PRS-019-004</td><td class="s6">Supplier Sync Scenario</td><td class="s7">SCENARIO #3<br>Verify Open Requistion Slip with a Status of For PO Review</td><td class="s7">1. A Requisition Slip with a Status For PO Review</td><td class="s7">1. Create an Requisition Slip and with a Status of For PO Review<br>3. Go to manage<br>4. Go to Supplier &gt;&gt; Update supplier details of the supplier from #1<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    <br>NOTIFICATION TEMPLATE:<br>   <br>Title: <br>A Supplier for your Requisition Slip has been updated<br><br>Content:<br>A Supplier that was added for your Requisition Slip has some updates. Click here if you wish to update the Supplier.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="2100941411R6" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">7</div></th><td class="s7">PRS-019-005</td><td class="s6">Supplier Sync Scenario</td><td class="s7">Scenario #3<br>Verify update or replaced supplier in Requisition Slip status of canvassing </td><td class="s7">1. already updated Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should repeat the Approval Process from Requisition Slip to Canvassing</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 19px"><th id="2100941411R7" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">8</div></th><td class="s7">PRS-019-006</td><td class="s6">Supplier Sync Scenario</td><td class="s7">Scenario #3<br>Verify if the Supplier is replaced</td><td class="s7">1. already updated/replaced Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced  the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should cancel the Purchase Order if the Supplier is replaced<br></td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="2100941411R8" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">9</div></th><td class="s7">PRS-019-007</td><td class="s6">Supplier Sync Scenario</td><td class="s7">SCENARIO #4<br>Verify Open RS with a Status of For Delivery</td><td class="s7">1. A Requisition Slip with a Status of For Delivery</td><td class="s7">1. Create an Requisition Slip with a Status of For Delivery<br>2. Update on supplier<br>3. Go to manage<br>4. Go to Supplier<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    2. Should highlight the Row of Request in the Dashboard with a Badge<br><br>NOTIFICATION TEMPLATE:<br>   <br>Title: <br>A Supplier for your Requisition Slip has been updated<br><br>Content:<br>A Supplier that was added for your Requisition Slip has some updates. Click here if you wish to update the Supplier.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7">&quot;<br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head&quot;</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 19px"><th id="2100941411R9" style="height: 19px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 19px">10</div></th><td class="s7">PRS-019-008</td><td class="s6">Supplier Sync Scenario</td><td class="s7">SCENARIO #4<br>Verify if the Supplier is updated</td><td class="s7">1. already updated/replaced Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced  the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">    1. Should not allow updating of Supplier</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 372px"><th id="2100941411R10" style="height: 372px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 372px">11</div></th><td class="s7">PRS-019-009</td><td class="s6">Supplier Sync Scenario</td><td class="s7">SCENARIO #5<br>Verify Open RS with a Status of For Payment Request and PR Approval</td><td class="s7">1. A Requisition Slip with a Status of For Payment Request and PR Approval</td><td class="s7">1. Create an Requisition Slip with a Status of For Payment Request and PR Approval<br>2. Update on supplier<br>3. Go to manage<br>4. Go to Supplier<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s7"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    2. Should highlight the Row of Request in the Dashboard with a Badge<br><br>NOTIFICATION TEMPLATE:<br>   <br>Title: <br>A Supplier for your Requisition Slip has been updated<br><br>Content:<br>A Supplier that was added for your Requisition Slip has some updates. Click here if you wish to update the Supplier.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s7"></td><td class="s7">&quot;<br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head&quot;</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 167px"><th id="2100941411R11" style="height: 167px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 167px">12</div></th><td class="s7">PRS-019-010</td><td class="s6">Supplier Sync Scenario</td><td class="s7">SCENARIO #5<br>Verify if the Supplier is updated</td><td class="s7">1. already updated/replaced Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced  the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">    1. Should not allow updating of Supplier</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s10"></td><td class="s7"></td><td class="s7"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 30px"><th id="2100941411R12" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">13</div></th><td class="s15" colspan="13"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 110px"><th id="2100941411R13" style="height: 110px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 110px">14</div></th><td class="s7">PRS-019-011</td><td class="s15">Suspending a Supplier</td><td class="s16">SCNEARIO #1<br>1. Existing Requisition Slip is already Closed, should not be affected by the changes</td><td class="s16">1. A Requisition Slip has been created<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver<br></td><td class="s16">1. Create an Requisition Slip and mark it as “Closed”.<br>2. Click Manage &gt;&gt; Go to Supplier &gt;&gt;&gt;&gt;Update supplier status of the supplier from #1<br>3. Click Sync<br><br></td><td class="s17"></td><td class="s16">1. The Requisition Slip remains unaffected. No notifications are sent, and no changes occur.</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s16"></td><td class="s16">2/12/25 <br>As per Miss Irene:<br><br>need maantay ung buong E2E ng Development para magawa ung scenario #1</td><td class="s7"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 498px"><th id="2100941411R14" style="height: 498px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 498px">15</div></th><td class="s7">PRS-019-012</td><td class="s15">Suspending a Supplier</td><td class="s7">Scenario #2<br>Verfiy Open Requisition Slip with a Status of Partially Canvassed or Canvass Approval</td><td class="s7">1. A Requisition Slip with a Status of Partially Canvassed or Canvass Approval</td><td class="s7">1. Create an Requisition Slip and Status of Partially Canvassed or Canvass Approval<br>2. Update on supplier status&gt;&gt; Go to manage<br>3. Go to Supplier &gt;&gt; Update supplier status of the supplier from #1<br>4. Click Sync<br>5. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br><br>NOTIFICATION TEMPLATE:<br><br> Title: <br>Attached Supplier for your Request has been tagged as Suspended<br><br>Content:<br>A Supplier that was added for your Requisition Slip has been tagged as Suspended. Click here to check if your Request has been affected<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s19"></td><td class="s19"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 136px"><th id="2100941411R15" style="height: 136px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 136px">16</div></th><td class="s7">PRS-019-013</td><td class="s15">Suspending a Supplier</td><td class="s7">Scenario #2<br>Verify update or replaced Supplier in Requisition Slip status of canvassing or partially canvassed</td><td class="s7">1. already updated Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Required to Update/replaced the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should require updating of Supplier<br>        a. If Supplier replaced, should repeat Approval Process</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"></td><td class="s7"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 370px"><th id="2100941411R16" style="height: 370px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 370px">17</div></th><td class="s7">PRS-019-014</td><td class="s15">Suspending a Supplier</td><td class="s7">SCENARIO #3<br>Verify Open Requistion Slip with a Status of For PO Review</td><td class="s7">1. A Requisition Slip with a Status For PO Review</td><td class="s7">1. Create an Requisition Slip and with a Status of For PO Review<br>3. Go to manage<br>4. Go to Supplier &gt;&gt;&gt;Update supplier status of the supplier from #1<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    <br>NOTIFICATION TEMPLATE:<br><br> Title: <br>Attached Supplier for your Request has been tagged as Suspended<br><br>Content:<br>A Supplier that was added for your Requisition Slip has been tagged as Suspended. Click here to check if your Request has been affected<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 118px"><th id="2100941411R17" style="height: 118px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 118px">18</div></th><td class="s7">PRS-019-015</td><td class="s15">Suspending a Supplier</td><td class="s7">Scenario #3<br>Verify update or replaced Supplier in Requisition Slip status of For PO Review</td><td class="s7">1. already updated Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Required to Update/replaced the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should repeat the Approval Process from Requisition Slip to Canvassing</td><td class="s9">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s3">[QA BUGS] [Suspending a Supplier] Unable to update or replaced the Supplier in Requisition Slip with the status of For PO Review when the supplier is change to susupended</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1103">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1103</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 88px"><th id="2100941411R18" style="height: 88px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 88px">19</div></th><td class="s7">PRS-019-016</td><td class="s15">Suspending a Supplier</td><td class="s7">Scenario #3<br>Verify if the Supplier is replaced</td><td class="s7">1. already updated/replaced Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Required to Update/replaced the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should cancel the Purchase Order if the Supplier is replaced<br></td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s3">[QA BUGS] [Suspending a Supplier] Unable to update or replaced the Supplier in Requisition Slip with the status of For PO Review when the supplier is change to susupended</td><td class="s21"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1103">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1103</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 326px"><th id="2100941411R19" style="height: 326px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 326px">20</div></th><td class="s7">PRS-019-017</td><td class="s15">Suspending a Supplier</td><td class="s7">SCENARIO #4<br>Verify Open RS with a Status of For Delivery</td><td class="s7">1. A Requisition Slip with a Status of For Delivery</td><td class="s7">1. Create an Requisition Slip with a Status of For Delivery<br>2. Update on supplier<br>3. Go to manage<br>4.  Go to Supplier &gt;&gt;&gt;Update supplier status of the supplier from #1<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    2. Should highlight the Row of Request in the Dashboard with a Badge<br><br>NOTIFICATION TEMPLATE:<br><br> Title: <br>Attached Supplier for your Request has been tagged as Suspended<br><br>Content:<br>A Supplier that was added for your Requisition Slip has been tagged as Suspended. Click here to check if your Request has been affected<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 83px"><th id="2100941411R20" style="height: 83px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 83px">21</div></th><td class="s7">PRS-019-018</td><td class="s15">Suspending a Supplier</td><td class="s7">SCENARIO #4<br>Verify if the Supplier is updated</td><td class="s7">1. already updated/replaced Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">    1. Should not allow updating of Supplier</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 427px"><th id="2100941411R21" style="height: 427px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 427px">22</div></th><td class="s7">PRS-019-019</td><td class="s15">Suspending a Supplier</td><td class="s7">SCENARIO #5<br>Verify Open RS with a Status of For Payment Request and PR Approval</td><td class="s7">1. A Requisition Slip with a Status of For Payment Request and PR Approval</td><td class="s7">1. Create an Requisition Slip with a Status of For Payment Request and PR Approval<br>2. Update on supplier<br>3. Go to manage<br>4. Go to Supplier<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s7"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>   <br>NOTIFICATION TEMPLATE:<br><br> Title: <br>Attached Supplier for your Request has been tagged as Suspended<br><br>Content:<br>A Supplier that was added for your Requisition Slip has been tagged as Suspended. Click here to check if your Request has been affected<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 109px"><th id="2100941411R22" style="height: 109px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 109px">23</div></th><td class="s7">PRS-019-020</td><td class="s15">Suspending a Supplier</td><td class="s7">SCENARIO #5<br>Verify if the Supplier is updated</td><td class="s7">1. already updated/replaced Supplier </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the supplier<br>3. click submit</td><td class="s8"></td><td class="s7">    1. Should not allow updating of Supplier</td><td class="s18">Passed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s22"></td><td class="s19"></td><td class="s19"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 30px"><th id="2100941411R23" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">24</div></th><td class="s23" colspan="13"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td><td class="s24"></td></tr><tr style="height: 110px"><th id="2100941411R24" style="height: 110px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 110px">25</div></th><td class="s7">PRS-019-021</td><td class="s23">Company Sync Scenario</td><td class="s16">SCNEARIO #1<br>1. Existing Requisition Slip is already Closed, should not be affected by the changes</td><td class="s16">1. A Requisition Slip has been created<br>2. Suppliers has been synced<br>3. Items has been added to the Canvass Sheet<br>4. A Supplier has been added to the Item and Canvass was submitted<br>5. User must be Canvass Sheet Approver<br></td><td class="s16">1. Create an Requisition Slip and mark it as “Closed”.<br>2. Click Manage &gt;&gt; Go to Company<br>3. Click Sync<br><br></td><td class="s17"></td><td class="s16">1. The Requisition Slip remains unaffected. No notifications are sent, and no changes occur.</td><td class="s13">Blocked</td><td class="s10">Need help sa dev to update the company name</td><td class="s11">Not Started</td><td class="s16"></td><td class="s16">2/12/25 <br>As per Miss Irene:<br><br>need maantay ung buong E2E ng Development para magawa ung scenario #1</td><td class="s7"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td><td class="s3"></td></tr><tr style="height: 536px"><th id="2100941411R25" style="height: 536px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 536px">26</div></th><td class="s7">PRS-019-022</td><td class="s23">Company Sync Scenario</td><td class="s7">Scenario #2<br>Verfiy Open Requisition Slip w with a Status of Draft or For Approval</td><td class="s7">1. A Requisition Slip with Draft or For approval status<br></td><td class="s7">1. Create an Requisition Slip with an added items and status is Draft and For Approval<br>2. Update on Company &gt; Go to Manage<br>3. Go to Company &gt; Update Company description of the company from #1<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Approvers<br><br><br>NOTIFICATION TEMPLATE:<br><br> Title: <br>A Company Details for your Requisition Slip has been updated<br><br>Content:<br>The Company that was tagged for your Requisition Slip has some updates. Click here if you wish to update the Company.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s9">Failed</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 138px"><th id="2100941411R26" style="height: 138px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 138px">27</div></th><td class="s7">PRS-019-023</td><td class="s23">Company Sync Scenario</td><td class="s7">Scenario #2<br>Verify that the Requisition Slip can be canceled, returning quantities to Remaining GFQ.</td><td class="s7">1. A Requisition Slip with Draft or For approval status</td><td class="s7">1. Create an Requisition Slip with  status “Draft” or “For Approval”.<br>2. Update the Company<br>3. Cancel the RS.<br>4. Check Remaining GFQ and Requisition Slip status.</td><td class="s25"></td><td class="s7">1. The Requisition Slip is canceled, and the requested quantity is returned to the Remaining GFQ.</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 418px"><th id="2100941411R27" style="height: 418px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 418px">28</div></th><td class="s7">PRS-019-024</td><td class="s23">Company Sync Scenario</td><td class="s7">Scenario #3<br>Verfiy Open Requisition Slip with a Status of Partially Canvassed or Canvass Approval</td><td class="s7">1. A Requisition Slip with a Status of Partially Canvassed or Canvass Approval</td><td class="s7">1. Create an Requisition Slip and Status of Partially Canvassed or Canvass Approval<br>2. Update on Company&gt;&gt; Go to manage<br>3. Go to Company &gt;&gt; Update company of the company from #1<br>4. Click Sync<br>5. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>       i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br><br>NOTIFICATION TEMPLATE:<br><br> Title: <br>A Company Details for your Requisition Slip has been updated<br><br>Content:<br>The Company that was tagged for your Requisition Slip has some updates. Click here if you wish to update the Company.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 138px"><th id="2100941411R28" style="height: 138px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 138px">29</div></th><td class="s7">PRS-019-025</td><td class="s23">Company Sync Scenario</td><td class="s7">Scenario #3<br>Verify updating of company or Cancelling the  Requisition Slip</td><td class="s7">1. already updated Company </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the company<br>3. click submit</td><td class="s8"></td><td class="s7">1. Should allow updating of Company or Cancelling the Requisition Slip<br>        i. If Company replaced, should allow the Next Approver to Review<br>        ii. If Cancelled, should add the Requested Item Quantity back to the Remaining GFQ</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 495px"><th id="2100941411R29" style="height: 495px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 495px">30</div></th><td class="s7">PRS-019-026</td><td class="s23">Company Sync Scenario</td><td class="s7">SCENARIO #4<br>Verify Open Requisition Slip with a Status of PO Created and PO Approval</td><td class="s7">1. A Requisition Slip with a Status of PO Created and PO Approval</td><td class="s7">1. Create an Requisition Slip with a Status of PO Created and PO Approval<br>2. Update the Company<br>3. Go to manage<br>4. Go to company<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    <br>NOTIFICATION TEMPLATE:<br><br> Title: <br>A Company Details for your Requisition Slip has been updated<br><br>Content:<br>The Company that was tagged for your Requisition Slip has some updates. Click here if you wish to update the Company.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 127px"><th id="2100941411R30" style="height: 127px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 127px">31</div></th><td class="s7">PRS-019-027</td><td class="s23">Company Sync Scenario</td><td class="s7">Scenario #4<br>Verify updating of Company in the Requisition Slip</td><td class="s7">1. already updated Company </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the company<br>3. click submit</td><td class="s8"></td><td class="s7">Should allow updating of Company in the Requisition Slip<br>        i. If Company replaced, should allow the Next Approver to Review<br></td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 374px"><th id="2100941411R31" style="height: 374px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 374px">32</div></th><td class="s7">PRS-019-028</td><td class="s23">Company Sync Scenario</td><td class="s7">SCENARIO #5<br>Verify Open Requisition Slip with a Status of For Delivery</td><td class="s7">1. A Requisition Slip with a Status of For Delivery</td><td class="s7">1. Create an Requisition Slip with a Status of For Delivery<br>2. Update the Company<br>3. Go to manage<br>4. Go to company<br>5. Click Sync<br>6. Check the Notification Bell for Requester and Approvers.</td><td class="s8"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>   <br><br>NOTIFICATION TEMPLATE:<br><br> Title: <br>A Company Details for your Requisition Slip has been updated<br><br>Content:<br>The Company that was tagged for your Requisition Slip has some updates. Click here if you wish to update the Company.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 158px"><th id="2100941411R32" style="height: 158px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 158px">33</div></th><td class="s7">PRS-019-029</td><td class="s23">Company Sync Scenario</td><td class="s7">SCENARIO #5<br>Verify updating of Company or Cancelling the Requisition Slip</td><td class="s7">1. already updated/replaced company </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the company<br>3. click submit</td><td class="s8"></td><td class="s7">    1. Should not allow updating of Company or Cancelling the Requisition Slip</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 424px"><th id="2100941411R33" style="height: 424px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 424px">34</div></th><td class="s7">PRS-019-030</td><td class="s23">Company Sync Scenario</td><td class="s7">SCENARIO #6<br>Verify Open RS with a Status of For Payment Request and PR Approval</td><td class="s7">1. A Requisition Slip with a Status of For Payment Request and PR Approval</td><td class="s7">1. Create an Requisition Slip with a Status of For Payment Request and PR Approval<br>2. Update the Company<br>3. Go to manage<br>4. Go to company<br>5. Click Sync<br>6. Check the Notification Bell for Requester and </td><td class="s7"></td><td class="s7">1. Should Notify through the Notification Bell that an update has been made<br>        i. Requester<br>        ii. Assigned Purchasing Staff<br>        iii. Purchasing Head<br>    <br>NOTIFICATION TEMPLATE:<br><br> Title: <br>A Company Details for your Requisition Slip has been updated<br><br>Content:<br>The Company that was tagged for your Requisition Slip has some updates. Click here if you wish to update the Company.<br><br>Date of the Notification: [MMM-DD-YYY]<br>Nov 08 2024</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 154px"><th id="2100941411R34" style="height: 154px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 154px">35</div></th><td class="s7">PRS-019-031</td><td class="s23">Company Sync Scenario</td><td class="s7">SCENARIO #6<br>Verify updating of Company or Cancelling the Requisition Slip</td><td class="s7">1. already updated/replaced company </td><td class="s7">1. Click Requisition Slip<br>2. Update/replaced the company<br>3. click submit</td><td class="s8"></td><td class="s7">    1. Should not allow updating of Company or Cancelling the Requisition Slip</td><td class="s13">Blocked</td><td class="s10"></td><td class="s11">Not Started</td><td class="s20"></td><td class="s7"><br>[QA BUGS] [Supplier Sync Scenario] No Notification Receive when i update the Supplier Details for Requester, Assigned Purchasing, Purchasing Head</td><td class="s5"><a target="_blank" href="https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085">https://youtrack.stratpoint.com/issue/CITYLANDPRS-1085</a></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 20px"><th id="2100941411R35" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">36</div></th><td class="s7"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 20px"><th id="2100941411R36" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">37</div></th><td class="s7"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr><tr style="height: 20px"><th id="2100941411R37" style="height: 20px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 20px">38</div></th><td class="s7"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s3"></td><td class="s3"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td><td class="s14"></td></tr></tbody></table></div>