# Canvass Workflow

This document outlines the complete workflow for the canvassing process in the PRS system, from creation to approval.

## Workflow Diagram

```mermaid
  flowchart TD
  Start([Create or Edit
  Canvass]) -->|isDraft| S1[Draft]
  S1 -->|RS Cancelled| S5[CS Cancelled]
  S1 -->|Edit| Start
  Start -->|Submitted| S2[For CS Approval]
  S2 -->|Fully Approved| S3[CS Approved]
  S2 -->|Rejected| S4[CS Rejected]
  S4 -->|Return| Start
  S2 -->|RS Cancelled| S5
  S4 -->|RS Cancelled| S6[CS Cancelled]
```

## Status Definitions

| Status | Description |
|--------|-------------|
| DRAFT | Initial status when a canvass is created but not submitted |
| FOR_CS_APPROVAL | Canvass has been submitted for approval |
| APPROVED | All approvers have approved the canvass |
| CS_REJECTED | Canvass has been rejected by an approver |
| CS_CANCELLED | Parent RS of Canvass Sheet has been cancelled |

## Detailed Workflow Steps

### 1. Canvass Creation

**Actor**: Assigned Purchasing Staff

**Actions**:

- Create a canvass for a requisition
- Add item/s from the requisition slip
- Add suppliers and prices for each item
- Save as draft or submit immediately

**Status Transitions**:

- New → DRAFT (if saved as draft)
- New → FOR_CS_APPROVAL (if submitted for approval)

**Business Rules**:

- Only the user assigned to the requisition can create a canvass
- Assigned purchasing staff can create multiple canvass sheet
- Canvass Sheet Number (CS Number) is auto-generated when submitted
- Items must be valid and belong to the requisition
- Items can be added from the requisition as long as the requested quantity is not yet fully canvassed
- For OFM, canvass quantity should not exceed requested quantity
- For Non-OFM, allowed but will highlight exceeded quantity
- For Non-TOM Request:
    - Each canvass item can have 1 up to 4 suppliers
    - Suppliers must have valid information (name, contact, etc.)
    - Suppliers must provide a price for the item
- For TOM Request:
    - Each canvass item can have 1 supplier only, cannot add more supplier
    - Supplier drop-down would only contain list of COMPANIES and PROJECTS
    - Suppliers must provide a price for the item 
- Suppliers can offer discounts (percentage or fixed amount)


### 2. Canvass Submission

**Actor**: Purchasing Staff

**Actions**:

- Update a draft or rejected canvass
- Add more items and suppliers
- Submit the canvass for approval

**Status Transitions**:

- DRAFT → FOR_CS_APPROVAL
- REJECTED_CS → FOR_CS_APPROVAL (if resubmitted after rejection)

- FOR RS STATUS: ASSIGNED → RS_IN_PROGRESS

**Business Rules**:

- Each item must have at least one supplier
- Each supplier must have a price
- Approvers are assigned based on the assigned user

### 3. Canvass Approval

**Actor**: Approvers

**Actions**:

- Review the canvass
- Purchasing Head to select suppliers for items
- Approve or reject the canvass
- Add note of approval (optional) or rejection (required)
- Add additional approvers (optional)

**Status Transitions**:

- FOR_CS_APPROVAL → CS_APPROVED (if all approvers approve)
- FOR_CS_APPROVAL → CS_REJECTED (if any approver rejects)

**Business Rules**:

- Only FOR_CS_APPROVAL or REJECTED canvasses can be approved
- User must be an assigned approver for the canvass
- Approvers are processed in order by level
- Approvers can edit item quantity or delete an item from the table
- During Purchasing Head approval, atleast 1 (one) or more supplier per item should be chosen
- If multiple suppliers are chosen, total canvassed quantity should not exceed the requested quantity
- Final Management approvals has 2 fixed approvers that can approve interchangeably
- If all approvers approve, status changes to CS_APPROVED
- Upon final approval, Purchase Orders are automatically created, grouped by supplier and terms

### 4. Canvass Rejection

**Actor**: Approvers

**Actions**:

- Review the canvass
- Reject the canvass
- Provide rejection reason

**Status Transitions**:

- FOR_CS_APPROVAL → CS_REJECTED

**Business Rules**:

- Only FOR_CS_APPROVAL canvasses can be rejected
- User must be an assigned approver for the canvass
- Rejection requires a reason/comment
- Upon rejection, status changes to CS_REJECTED

### 5. Canvass Behavior after RS Cancellation

**Business Rules**:

- Once the parent RS of the canvass has been cancelled:
    - No new canvasses can be created
    - Existing canvasses cannot be edited
    - Canvass statuses will be CS Cancelled, except CS with CS_Approved status

### 5. Canvass Behavior after PO Cancellation

**Business Rules**:

- Once a Purchase Order generated from a canvass has been cancelled:
    - System to add cancelled PO quantity to remaining quantity
    - Assigned user is allowed to create canvass for the cancelled items again

## Example Scenarios

### Scenario 1: Standard Canvass Flow

1. Purchasing Staff creates a canvass for all requisition items
2. Purchasing Staff adds suppliers and prices for each item
3. Purchasing Staff submits the canvass for approval
4. Approvers review and approve the canvass
5. System creates purchase orders for selected suppliers

### Scenario 2: Canvass with Rejection

1. Purchasing Staff creates and submits a canvass
2. An approver rejects the canvass with a reason
3. Purchasing Staff updates the canvass and resubmits it
4. Approvers approve the canvass
5. System creates purchase orders for selected suppliers

## Implementation Details

### Key Files

- **Controller**: `src/app/handlers/controllers/canvassController.js`
- **Service**: `src/app/services/canvassService.js`
- **Repository**: `src/infra/repositories/canvassRepository.js`
- **Entity**: `src/domain/entities/canvassEntity.js`
- **Constants**: `src/domain/constants/canvassConstants.js`

### Status Transition Implementation

```javascript
// Example status transition logic
async function updateCanvassStatus(canvassId, newStatus, transaction) {
  const canvass = await canvassRepository.findOne({
    where: { id: canvassId },
    transaction,
  });
  
  // Validate status transition
  const validTransitions = {
    'DRAFT': ['PARTIALLY_CANVASSED', 'FOR_APPROVAL'],
    'PARTIALLY_CANVASSED': ['FOR_APPROVAL'],
    'FOR_APPROVAL': ['APPROVED', 'REJECTED'],
    'REJECTED': ['FOR_APPROVAL'],
  };
  
  if (!validTransitions[canvass.status]?.includes(newStatus)) {
    throw new Error(`Invalid status transition from ${canvass.status} to ${newStatus}`);
  }
  
  // Update status
  await canvass.update({ status: newStatus }, { transaction });
  
  return canvass;
}
```

## Common Issues and Solutions

### Issue 1: Cannot Create Canvass

**Cause**: User is not assigned to the requisition or requisition is not in ASSIGNED status.

**Solution**:

- Check if the requisition is assigned to the current user
- Ensure the requisition is in ASSIGNED status

### Issue 2: Cannot Submit Canvass

**Cause**: Missing suppliers or prices for items.

**Solution**:

- Ensure each item has at least one supplier
- Ensure each supplier has a price

### Issue 3: Canvass Approval Issues

**Cause**: Approvers not assigned correctly or missing supplier selections.

**Solution**:

- Check if approvers are assigned correctly
- Ensure suppliers are selected for each item
- Verify approver permissions
